"""
异步处理模块

提供 Mini-Boot 框架的异步任务执行、线程池管理和异步上下文功能.

主要功能:
- 异步执行器 (AsyncExecutor) - 异步任务执行和调度
- 线程池管理 (ThreadPool) - 线程池的创建和管理
- 异步上下文 (AsyncContext) - 异步环境下的 Bean 管理
- 异步装饰器 (@Async 等异步相关注解)
- 异步任务结果处理
- 异步回调机制
"""

# 注解(引用现有注解模块)
from ..annotations.schedule import Async, EnableAsync
# 动态线程池调整
from .adjuster import (AdjustmentPolicy, DynamicPoolAdjuster, LoadMetrics,
                       LoadMonitor)
# 基础组件
from .base import (AsyncComponentBase, AsyncSingletonComponentBase,
                   AsyncSingletonMeta, AsyncSingletonMixin,
                   async_singleton_manager)
# 自动配置已迁移到 processor 模块
# AsyncApplicationContextIntegration 已迁移到 processor 模块
# endpoints.py 已删除，功能整合到 Actuator 系统中
# 异步执行器
from .executor import AsyncExecutor, ThreadPoolAsyncExecutor
# 异步拦截器和代理功能已整合到 processor 模块
# 异步监控和管理
from .metrics import (AsyncExecutorMetrics, AsyncTaskMetric,
                      AsyncTaskMetricsCollector,
                      ConfigurablePerformanceMonitor, LightweightMetrics,
                      PerformanceConfig)
# 线程池管理系统
from .pool import (ManagedThreadPool, PoolState, PoolStrategy, TaskWrapper,
                   ThreadPoolManager, ThreadPoolMetrics)
# Bean生命周期集成已迁移到 processor 模块
# Spring Boot风格异步配置(新增)
from .properties import (AdaptiveLearningConfig, AsyncExecutorProperties,
                         AsyncProperties, CustomExecutorProperties,
                         DynamicAdjustmentConfig, IntelligentSchedulingConfig,
                         LoadBalancingConfig, MonitoringConfig,
                         OptimizationConfig, PerformanceThresholdsConfig,
                         ThreadPoolAdvancedConfig, ThreadPoolConfig)
# 异步工具函数
from .tools import (AsyncBatch, async_exception_handler, filter, gather, map,
                    retry, run_sync, timeout, timer)

__all__ = [
    # 基础组件
    "AsyncComponentBase",
    "AsyncSingletonComponentBase",
    "AsyncSingletonMeta",
    "AsyncSingletonMixin",
    "async_exception_handler",
    "async_singleton_manager",
    # 线程池管理
    "PoolStrategy",
    "PoolState",
    "ThreadPoolMetrics",
    "ManagedThreadPool",
    "ThreadPoolManager",
    "TaskWrapper",
    # 动态调整
    "AdjustmentPolicy",
    "LoadMetrics",
    "LoadMonitor",
    "DynamicPoolAdjuster",

    # 异步工具函数
    "run_sync",
    "gather",
    "timeout",
    "retry",
    "AsyncBatch",
    "timer",
    "map",
    "filter",
    # Spring Boot风格异步配置(重构后)
    "AsyncProperties",
    "AsyncExecutorProperties",
    "CustomExecutorProperties",
    # 配置分组类
    "ThreadPoolConfig",
    "DynamicAdjustmentConfig",
    "LoadBalancingConfig",
    "IntelligentSchedulingConfig",
    "OptimizationConfig",
    "MonitoringConfig",
    "AdaptiveLearningConfig",
    "PerformanceThresholdsConfig",
    "ThreadPoolAdvancedConfig",
    # 异步拦截器和代理功能已整合到 processor 模块
    # 异步执行器(新增)
    "AsyncExecutor",
    "ThreadPoolAsyncExecutor",
    # Bean生命周期集成已迁移到 processor 模块
    # 应用上下文集成已迁移到 processor 模块
    # 异步监控和管理(新增)
    "AsyncTaskMetric",
    "AsyncExecutorMetrics",
    "AsyncTaskMetricsCollector",
    "ConfigurablePerformanceMonitor",
    "LightweightMetrics",
    "PerformanceConfig",
    # endpoints.py 已删除，功能整合到 Actuator 系统中
    # 异步注解(引用现有注解模块)
    "Async",
    "EnableAsync",
]
