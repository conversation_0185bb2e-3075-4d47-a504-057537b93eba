"""
Task Scheduler - 智能任务调度器

独立的任务调度 Starter 组件，提供智能任务调度和管理功能。
重构自原 miniboot.web.scheduling.scheduler，采用 @PostConstruct 生命周期管理。
"""

import asyncio
import time
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass, field
from enum import Enum
from queue import PriorityQueue
from threading import Lock
from typing import Any, Callable, Dict, List, Optional, Union

from loguru import logger

from miniboot.annotations import Component, PostConstruct, PreDestroy
from .properties import TaskSchedulerProperties


class SchedulerState(Enum):
    """调度器状态"""
    
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class TaskStatus(Enum):
    """任务状态"""
    
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


@dataclass
class TaskRequest:
    """任务请求"""
    
    task_id: str
    function: Callable
    args: tuple = ()
    kwargs: dict = field(default_factory=dict)
    priority: int = 5  # 1-10，数字越大优先级越高
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)
    
    def __lt__(self, other):
        """优先级比较（用于优先队列）"""
        # 优先级高的任务排在前面，如果优先级相同则按创建时间排序
        if self.priority != other.priority:
            return self.priority > other.priority
        return self.created_at < other.created_at


@dataclass
class TaskResult:
    """任务结果"""
    
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    execution_time: Optional[float] = None
    retry_count: int = 0
    
    @property
    def duration(self) -> Optional[float]:
        """任务执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


@dataclass
class SchedulerMetrics:
    """调度器性能指标"""
    
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    cancelled_tasks: int = 0
    timeout_tasks: int = 0
    active_tasks: int = 0
    avg_execution_time: float = 0.0
    success_rate: float = 0.0
    throughput: float = 0.0  # 任务/秒
    last_updated: float = field(default_factory=time.time)


@Component
class TaskScheduler:
    """智能任务调度器 - 独立 Starter 版本
    
    提供智能任务调度和管理功能，支持：
    - 智能任务调度：基于负载和优先级的调度策略
    - 并发控制：可配置的并发任务数量限制
    - 任务队列管理：支持优先级队列和延迟执行
    - 性能监控：任务执行时间和成功率统计
    - 超时控制：可配置的任务执行超时机制
    """
    
    def __init__(self, properties: TaskSchedulerProperties):
        """初始化任务调度器
        
        Args:
            properties: 任务调度配置属性
        """
        self.properties = properties
        
        # 验证配置
        self.properties.validate()
        
        # 调度器状态
        self._state = SchedulerState.STOPPED
        self._scheduler_task: Optional[asyncio.Task] = None
        
        # 任务队列和存储
        self._task_queue = PriorityQueue(maxsize=self.properties.queue_size)
        self._active_tasks: Dict[str, TaskRequest] = {}
        self._task_results: Dict[str, TaskResult] = {}
        
        # 线程池（用于执行阻塞任务）
        self._thread_pool: Optional[ThreadPoolExecutor] = None
        
        # 性能指标
        self._metrics = SchedulerMetrics()
        self._execution_times: List[float] = []
        
        # 线程安全
        self._lock = Lock()
        
        logger.debug(f"TaskScheduler initialized with properties: {self.properties}")
    
    @PostConstruct
    async def initialize(self) -> None:
        """自动初始化任务调度器（框架调用）"""
        if not self.properties.enabled:
            logger.info("TaskScheduler disabled by configuration")
            return
            
        if self._state != SchedulerState.STOPPED:
            logger.warning("TaskScheduler is already initialized")
            return
        
        logger.info(f"🚀 Initializing TaskScheduler (max_concurrent: {self.properties.max_concurrent_tasks})")
        
        self._state = SchedulerState.STARTING
        
        try:
            # 创建线程池
            self._thread_pool = ThreadPoolExecutor(
                max_workers=self.properties.worker_threads,
                thread_name_prefix="TaskScheduler"
            )
            
            # 启动调度器主循环
            self._scheduler_task = asyncio.create_task(self._scheduler_loop())
            
            self._state = SchedulerState.RUNNING
            logger.info("✅ TaskScheduler initialized successfully")
            
        except Exception as e:
            self._state = SchedulerState.ERROR
            logger.error(f"Failed to initialize TaskScheduler: {e}")
            raise
    
    @PreDestroy
    async def cleanup(self) -> None:
        """自动清理任务调度器（框架调用）"""
        if self._state == SchedulerState.STOPPED:
            logger.debug("TaskScheduler is already stopped")
            return
            
        logger.info("🛑 Stopping TaskScheduler")
        
        self._state = SchedulerState.STOPPING
        
        try:
            # 取消调度器任务
            if self._scheduler_task:
                self._scheduler_task.cancel()
                try:
                    await self._scheduler_task
                except asyncio.CancelledError:
                    logger.debug("TaskScheduler loop cancelled successfully")
            
            # 等待活跃任务完成（最多等待30秒）
            if self._active_tasks:
                logger.info(f"Waiting for {len(self._active_tasks)} active tasks to complete...")
                await self._wait_for_active_tasks(timeout=30.0)
            
            # 关闭线程池
            if self._thread_pool:
                self._thread_pool.shutdown(wait=True)
                logger.debug("Thread pool shutdown completed")
            
            self._state = SchedulerState.STOPPED
            logger.info("✅ TaskScheduler stopped successfully")
            
        except Exception as e:
            self._state = SchedulerState.ERROR
            logger.error(f"Error during TaskScheduler cleanup: {e}")
    
    async def _scheduler_loop(self) -> None:
        """调度器主循环"""
        logger.debug("TaskScheduler main loop started")
        
        while self._state == SchedulerState.RUNNING:
            try:
                # 处理待执行任务
                await self._process_pending_tasks()
                
                # 更新性能指标
                if self.properties.enable_metrics:
                    await self._update_metrics()
                
                # 清理过期任务结果
                await self._cleanup_expired_results()
                
                # 等待下一次调度
                await asyncio.sleep(self.properties.scheduling_interval)
                
            except asyncio.CancelledError:
                logger.debug("TaskScheduler loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                await asyncio.sleep(1.0)  # 错误时短暂等待
    
    async def _process_pending_tasks(self) -> None:
        """处理待执行任务"""
        try:
            # 获取当前活跃任务数量
            active_count = len(self._active_tasks)
            
            # 计算可以启动的新任务数量
            max_concurrent = self.properties.max_concurrent_tasks
            if self.properties.adaptive_concurrency:
                # 这里可以根据系统负载动态调整
                max_concurrent = self.properties.get_effective_concurrency()
            
            available_slots = max_concurrent - active_count
            
            # 启动新任务
            started_count = 0
            while available_slots > 0 and not self._task_queue.empty() and started_count < available_slots:
                try:
                    # 从队列中获取任务（非阻塞）
                    task_request = self._task_queue.get_nowait()
                    
                    # 启动任务
                    await self._start_task(task_request)
                    started_count += 1
                    available_slots -= 1
                    
                except Exception as e:
                    logger.error(f"Error starting task: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"Error processing pending tasks: {e}")
    
    async def _start_task(self, task_request: TaskRequest) -> None:
        """启动单个任务"""
        try:
            # 添加到活跃任务列表
            with self._lock:
                self._active_tasks[task_request.task_id] = task_request
            
            # 创建任务结果
            task_result = TaskResult(
                task_id=task_request.task_id,
                status=TaskStatus.RUNNING,
                start_time=time.time(),
                retry_count=task_request.retry_count
            )
            
            with self._lock:
                self._task_results[task_request.task_id] = task_result
            
            # 异步执行任务
            asyncio.create_task(self._execute_task(task_request, task_result))
            
        except Exception as e:
            logger.error(f"Error starting task {task_request.task_id}: {e}")
            await self._handle_task_error(task_request, e)
    
    async def _execute_task(self, task_request: TaskRequest, task_result: TaskResult) -> None:
        """执行任务"""
        try:
            # 确定执行超时时间
            timeout = task_request.timeout or self.properties.task_timeout
            
            # 执行任务
            if asyncio.iscoroutinefunction(task_request.function):
                # 异步任务
                result = await asyncio.wait_for(
                    task_request.function(*task_request.args, **task_request.kwargs),
                    timeout=timeout
                )
            else:
                # 同步任务，在线程池中执行
                loop = asyncio.get_event_loop()
                result = await asyncio.wait_for(
                    loop.run_in_executor(
                        self._thread_pool,
                        lambda: task_request.function(*task_request.args, **task_request.kwargs)
                    ),
                    timeout=timeout
                )
            
            # 任务成功完成
            await self._handle_task_success(task_request, task_result, result)
            
        except asyncio.TimeoutError:
            # 任务超时
            await self._handle_task_timeout(task_request, task_result)
        except Exception as e:
            # 任务执行错误
            await self._handle_task_error(task_request, task_result, e)
    
    async def _handle_task_success(self, task_request: TaskRequest, task_result: TaskResult, result: Any) -> None:
        """处理任务成功完成"""
        try:
            # 更新任务结果
            task_result.status = TaskStatus.COMPLETED
            task_result.result = result
            task_result.end_time = time.time()
            task_result.execution_time = task_result.end_time - task_result.start_time
            
            # 从活跃任务列表中移除
            with self._lock:
                self._active_tasks.pop(task_request.task_id, None)
                self._metrics.completed_tasks += 1
                if task_result.execution_time:
                    self._execution_times.append(task_result.execution_time)
            
            logger.debug(f"Task {task_request.task_id} completed successfully in {task_result.execution_time:.3f}s")
            
        except Exception as e:
            logger.error(f"Error handling task success for {task_request.task_id}: {e}")
    
    async def _handle_task_timeout(self, task_request: TaskRequest, task_result: TaskResult) -> None:
        """处理任务超时"""
        try:
            task_result.status = TaskStatus.TIMEOUT
            task_result.end_time = time.time()
            task_result.execution_time = task_result.end_time - task_result.start_time
            
            # 从活跃任务列表中移除
            with self._lock:
                self._active_tasks.pop(task_request.task_id, None)
                self._metrics.timeout_tasks += 1
            
            logger.warning(f"Task {task_request.task_id} timed out after {task_result.execution_time:.3f}s")
            
            # 尝试重试
            if self.properties.enable_retry and task_request.retry_count < task_request.max_retries:
                await self._retry_task(task_request)
            
        except Exception as e:
            logger.error(f"Error handling task timeout for {task_request.task_id}: {e}")
    
    async def _handle_task_error(self, task_request: TaskRequest, task_result: Optional[TaskResult] = None, error: Optional[Exception] = None) -> None:
        """处理任务执行错误"""
        try:
            if task_result:
                task_result.status = TaskStatus.FAILED
                task_result.error = error
                task_result.end_time = time.time()
                if task_result.start_time:
                    task_result.execution_time = task_result.end_time - task_result.start_time
            
            # 从活跃任务列表中移除
            with self._lock:
                self._active_tasks.pop(task_request.task_id, None)
                self._metrics.failed_tasks += 1
            
            logger.error(f"Task {task_request.task_id} failed: {error}")
            
            # 尝试重试
            if self.properties.enable_retry and task_request.retry_count < task_request.max_retries:
                await self._retry_task(task_request)
            
        except Exception as e:
            logger.error(f"Error handling task error for {task_request.task_id}: {e}")
    
    async def _retry_task(self, task_request: TaskRequest) -> None:
        """重试任务"""
        try:
            task_request.retry_count += 1
            
            # 计算重试延迟
            delay = self.properties.get_retry_delay(task_request.retry_count)
            
            logger.info(f"Retrying task {task_request.task_id} (attempt {task_request.retry_count}/{task_request.max_retries}) after {delay}s")
            
            # 延迟后重新提交任务
            await asyncio.sleep(delay)
            await self.submit_task(
                task_request.function,
                *task_request.args,
                task_id=task_request.task_id,
                priority=task_request.priority,
                timeout=task_request.timeout,
                **task_request.kwargs
            )
            
        except Exception as e:
            logger.error(f"Error retrying task {task_request.task_id}: {e}")
    
    async def _wait_for_active_tasks(self, timeout: float = 30.0) -> None:
        """等待活跃任务完成"""
        start_time = time.time()
        
        while self._active_tasks and (time.time() - start_time) < timeout:
            await asyncio.sleep(0.1)
        
        if self._active_tasks:
            logger.warning(f"Timeout waiting for {len(self._active_tasks)} active tasks to complete")
    
    async def _update_metrics(self) -> None:
        """更新性能指标"""
        try:
            with self._lock:
                self._metrics.total_tasks = (
                    self._metrics.completed_tasks + 
                    self._metrics.failed_tasks + 
                    self._metrics.cancelled_tasks + 
                    self._metrics.timeout_tasks
                )
                
                self._metrics.active_tasks = len(self._active_tasks)
                
                # 计算成功率
                if self._metrics.total_tasks > 0:
                    self._metrics.success_rate = self._metrics.completed_tasks / self._metrics.total_tasks * 100
                
                # 计算平均执行时间
                if self._execution_times:
                    self._metrics.avg_execution_time = sum(self._execution_times) / len(self._execution_times)
                    
                    # 保持最近1000个执行时间记录
                    if len(self._execution_times) > 1000:
                        self._execution_times = self._execution_times[-1000:]
                
                # 计算吞吐量（任务/秒）
                current_time = time.time()
                time_diff = current_time - self._metrics.last_updated
                if time_diff > 0:
                    completed_since_last = self._metrics.completed_tasks
                    self._metrics.throughput = completed_since_last / time_diff if time_diff > 0 else 0.0
                
                self._metrics.last_updated = current_time
                
        except Exception as e:
            logger.error(f"Error updating metrics: {e}")
    
    async def _cleanup_expired_results(self) -> None:
        """清理过期的任务结果"""
        try:
            current_time = time.time()
            expired_tasks = []
            
            with self._lock:
                for task_id, result in self._task_results.items():
                    if result.end_time and (current_time - result.end_time) > self.properties.task_result_ttl:
                        expired_tasks.append(task_id)
                
                # 移除过期结果
                for task_id in expired_tasks:
                    self._task_results.pop(task_id, None)
            
            if expired_tasks:
                logger.debug(f"Cleaned up {len(expired_tasks)} expired task results")
                
        except Exception as e:
            logger.error(f"Error cleaning up expired results: {e}")
    
    # ==================== 公共接口 ====================
    
    async def submit_task(
        self,
        function: Callable,
        *args,
        task_id: Optional[str] = None,
        priority: Optional[int] = None,
        timeout: Optional[float] = None,
        max_retries: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """提交任务到调度器
        
        Args:
            function: 要执行的函数
            *args: 函数位置参数
            task_id: 任务ID，如果不提供则自动生成
            priority: 任务优先级 (1-10)
            timeout: 任务超时时间(秒)
            max_retries: 最大重试次数
            metadata: 任务元数据
            **kwargs: 函数关键字参数
            
        Returns:
            任务ID
            
        Raises:
            RuntimeError: 如果调度器未运行
            ValueError: 如果队列已满
        """
        if self._state != SchedulerState.RUNNING:
            raise RuntimeError("TaskScheduler is not running")
        
        # 生成任务ID
        if task_id is None:
            task_id = str(uuid.uuid4())
        
        # 创建任务请求
        task_request = TaskRequest(
            task_id=task_id,
            function=function,
            args=args,
            kwargs=kwargs,
            priority=priority or self.properties.default_priority,
            timeout=timeout,
            max_retries=max_retries or self.properties.max_retry_attempts,
            metadata=metadata or {}
        )
        
        try:
            # 添加到队列
            self._task_queue.put_nowait(task_request)
            
            with self._lock:
                self._metrics.total_tasks += 1
            
            logger.debug(f"Task {task_id} submitted with priority {task_request.priority}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to submit task {task_id}: {e}")
            raise ValueError(f"Failed to submit task: {e}")
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务结果，如果任务不存在则返回 None
        """
        with self._lock:
            return self._task_results.get(task_id)
    
    def get_scheduler_metrics(self) -> SchedulerMetrics:
        """获取调度器性能指标
        
        Returns:
            调度器性能指标
        """
        with self._lock:
            return SchedulerMetrics(
                total_tasks=self._metrics.total_tasks,
                completed_tasks=self._metrics.completed_tasks,
                failed_tasks=self._metrics.failed_tasks,
                cancelled_tasks=self._metrics.cancelled_tasks,
                timeout_tasks=self._metrics.timeout_tasks,
                active_tasks=self._metrics.active_tasks,
                avg_execution_time=self._metrics.avg_execution_time,
                success_rate=self._metrics.success_rate,
                throughput=self._metrics.throughput,
                last_updated=self._metrics.last_updated
            )
    
    def get_state(self) -> SchedulerState:
        """获取调度器状态
        
        Returns:
            调度器状态
        """
        return self._state
    
    def get_queue_size(self) -> int:
        """获取当前队列大小
        
        Returns:
            队列中待处理任务数量
        """
        return self._task_queue.qsize()
    
    def get_active_task_count(self) -> int:
        """获取活跃任务数量
        
        Returns:
            当前正在执行的任务数量
        """
        with self._lock:
            return len(self._active_tasks)
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        try:
            # 检查是否在活跃任务中
            with self._lock:
                if task_id in self._active_tasks:
                    # 标记为取消（实际取消需要任务配合）
                    if task_id in self._task_results:
                        self._task_results[task_id].status = TaskStatus.CANCELLED
                    self._active_tasks.pop(task_id, None)
                    self._metrics.cancelled_tasks += 1
                    logger.info(f"Task {task_id} cancelled")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error cancelling task {task_id}: {e}")
            return False