#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: AutoConfigure相关异常类

自动配置系统相关的异常。
"""

from ..base import ApplicationError, BusinessError, ValidationError


# 自动配置相关异常 (ApplicationError)
class AutoConfigurationError(ApplicationError):
    """自动配置错误 - 自动配置过程中的错误"""
    max_attempts = 2
    base_delay = 2.0


class AutoConfigurationFailureError(ApplicationError):
    """自动配置失败错误 - 自动配置失败"""
    max_attempts = 2
    base_delay = 2.0


class AutoConfigurationTimeoutError(ApplicationError):
    """自动配置超时错误 - 自动配置超时"""
    max_attempts = 1
    base_delay = 3.0


# 配置类相关异常 (ValidationError)
class ConfigurationClassError(ValidationError):
    """配置类错误 - 配置类相关的错误"""
    # 继承ValidationError的属性：retryable = False


class InvalidConfigurationClassError(ValidationError):
    """无效配置类错误 - 配置类无效"""
    # 继承ValidationError的属性：retryable = False


class ConfigurationClassNotFoundError(ValidationError):
    """配置类未找到错误 - 找不到配置类"""
    # 继承ValidationError的属性：retryable = False


# 条件配置相关异常 (BusinessError)
class ConditionalConfigurationError(BusinessError):
    """条件配置错误 - 条件配置相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ConditionEvaluationError(BusinessError):
    """条件评估错误 - 条件评估失败"""
    max_attempts = 2
    base_delay = 1.0


class ConditionalBeanError(BusinessError):
    """条件Bean错误 - 条件Bean相关的错误"""
    max_attempts = 2
    base_delay = 1.0


# 配置属性相关异常 (ApplicationError)
class ConfigurationPropertiesError(ApplicationError):
    """配置属性错误 - 配置属性相关的错误"""
    retryable = False


class ConfigurationPropertiesBindingError(ApplicationError):
    """配置属性绑定错误 - 配置属性绑定失败"""
    retryable = False


class ConfigurationPropertiesValidationError(ValidationError):
    """配置属性验证错误 - 配置属性验证失败"""
    # 继承ValidationError的属性：retryable = False


# 配置导入相关异常 (ApplicationError)
class ConfigurationImportError(ApplicationError):
    """配置导入错误 - 配置导入过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


class ImportSelectorError(ApplicationError):
    """导入选择器错误 - 导入选择器相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ImportRegistrarError(ApplicationError):
    """导入注册器错误 - 导入注册器相关的错误"""
    max_attempts = 2
    base_delay = 1.0


# 配置扫描相关异常 (ApplicationError)
class ComponentScanError(ApplicationError):
    """组件扫描错误 - 组件扫描过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


class ClassPathScanningError(ApplicationError):
    """类路径扫描错误 - 类路径扫描失败"""
    max_attempts = 2
    base_delay = 1.0


class AnnotationScanningError(ApplicationError):
    """注解扫描错误 - 注解扫描失败"""
    max_attempts = 2
    base_delay = 1.0
