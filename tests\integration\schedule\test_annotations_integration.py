#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务模块与annotations模块集成测试
"""

import asyncio
import threading
import time
import unittest

from miniboot.schedule import EnableScheduling, MiniBootScheduler, ScheduleConfigurationError, Scheduled, TaskType


@EnableScheduling
class AnnotationIntegrationService:
    """注解集成测试服务"""

    def __init__(self):
        self.execution_results = []
        self.execution_count = 0
        self.lock = threading.Lock()

    @Scheduled(fixed_rate="1s")
    def fixed_rate_task(self):
        """固定频率任务"""
        with self.lock:
            self.execution_count += 1
            result = f"fixed_rate_{self.execution_count}"
            self.execution_results.append(result)
            return result

    @Scheduled(fixed_delay="2s", initial_delay="0.5s")
    def fixed_delay_task(self):
        """固定延迟任务"""
        with self.lock:
            self.execution_count += 1
            result = f"fixed_delay_{self.execution_count}"
            self.execution_results.append(result)
            time.sleep(0.1)  # 模拟处理时间
            return result

    @Scheduled(cron="*/3 * * * * *")  # 每3秒执行（如果支持秒级cron）
    def cron_task(self):
        """Cron表达式任务"""
        with self.lock:
            self.execution_count += 1
            result = f"cron_{self.execution_count}"
            self.execution_results.append(result)
            return result

    @Scheduled(fixed_rate="1.5s")
    async def async_scheduled_task(self):
        """异步调度任务"""
        with self.lock:
            self.execution_count += 1
            result = f"async_{self.execution_count}"
            self.execution_results.append(result)

        await asyncio.sleep(0.05)
        return result

    def normal_method(self):
        """普通方法（不应被调度）"""
        with self.lock:
            self.execution_count += 1
            return f"normal_{self.execution_count}"

    def get_results(self):
        """获取执行结果"""
        with self.lock:
            return self.execution_results.copy()

    def get_execution_count(self):
        """获取执行次数"""
        with self.lock:
            return self.execution_count

    def reset(self):
        """重置状态"""
        with self.lock:
            self.execution_results.clear()
            self.execution_count = 0


class NonScheduledService:
    """未启用调度的服务"""

    def __init__(self):
        self.call_count = 0

    @Scheduled(fixed_rate="1s")
    def should_not_be_scheduled(self):
        """这个方法不应该被调度（因为类没有@EnableScheduling）"""
        self.call_count += 1
        return f"should_not_run_{self.call_count}"


class TestAnnotationsIntegration(unittest.TestCase):
    """测试定时任务与注解模块的集成"""

    def setUp(self):
        """设置测试环境"""
        self.service = AnnotationIntegrationService()
        self.scheduler = MiniBootScheduler(max_workers=3, use_asyncio=False)

        # 强制初始化TaskManager
        if self.scheduler.task_manager is None:
            try:
                from miniboot.schedule.task_manager import TaskManager

                self.scheduler.task_manager = TaskManager(self.scheduler)
            except Exception as e:
                print(f"警告: 无法初始化TaskManager: {e}")

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

    def test_enable_scheduling_annotation(self):
        """测试@EnableScheduling注解功能"""
        # 验证类被正确标记
        self.assertTrue(hasattr(AnnotationIntegrationService, "_scheduling_enabled"))
        self.assertTrue(AnnotationIntegrationService._scheduling_enabled)

        # 验证方法有调度配置
        self.assertTrue(hasattr(self.service.fixed_rate_task, "_scheduled_config"))
        self.assertTrue(hasattr(self.service.fixed_delay_task, "_scheduled_config"))
        self.assertTrue(hasattr(self.service.cron_task, "_scheduled_config"))
        self.assertTrue(hasattr(self.service.async_scheduled_task, "_scheduled_config"))

        # 验证普通方法没有调度配置
        self.assertFalse(hasattr(self.service.normal_method, "_scheduled_config"))

    def test_scheduled_annotation_metadata(self):
        """测试@Scheduled注解元数据"""
        # 测试固定频率任务配置
        fixed_rate_config = self.service.fixed_rate_task._scheduled_config
        self.assertEqual(fixed_rate_config.fixed_rate, "1s")
        self.assertEqual(fixed_rate_config.get_task_type(), TaskType.FIXED_RATE)

        # 测试固定延迟任务配置
        fixed_delay_config = self.service.fixed_delay_task._scheduled_config
        self.assertEqual(fixed_delay_config.fixed_delay, "2s")
        self.assertEqual(fixed_delay_config.initial_delay, "0.5s")
        self.assertEqual(fixed_delay_config.get_task_type(), TaskType.FIXED_DELAY)

        # 测试Cron任务配置
        cron_config = self.service.cron_task._scheduled_config
        self.assertEqual(cron_config.cron, "*/3 * * * * *")
        self.assertEqual(cron_config.get_task_type(), TaskType.CRON)

        # 测试异步任务配置
        async_config = self.service.async_scheduled_task._scheduled_config
        self.assertEqual(async_config.fixed_rate, "1.5s")
        self.assertEqual(async_config.get_task_type(), TaskType.FIXED_RATE)

    def test_scheduled_method_discovery(self):
        """测试调度方法发现"""
        if not self.scheduler.task_manager:
            self.skipTest("TaskManager not available")

        # 从@Scheduled方法创建任务
        task_ids = []

        # 添加固定频率任务
        task_id1 = self.scheduler.task_manager.create_from_scheduled_method(self.service.fixed_rate_task, self.service)
        task_ids.append(task_id1)

        # 添加固定延迟任务
        task_id2 = self.scheduler.task_manager.create_from_scheduled_method(self.service.fixed_delay_task, self.service)
        task_ids.append(task_id2)

        # 添加异步任务
        task_id3 = self.scheduler.task_manager.create_from_scheduled_method(self.service.async_scheduled_task, self.service)
        task_ids.append(task_id3)

        # 验证所有任务都被创建
        for task_id in task_ids:
            self.assertIsNotNone(task_id)

        # 验证任务在调度器中
        for task_id in task_ids:
            task = self.scheduler.get_task(task_id)
            self.assertIsNotNone(task)

    def test_annotation_based_scheduling_execution(self):
        """测试基于注解的调度执行"""
        if not self.scheduler.task_manager:
            self.skipTest("TaskManager not available")

        # 注册所有@Scheduled方法
        self.scheduler.task_manager.create_from_scheduled_method(self.service.fixed_rate_task, self.service)

        self.scheduler.task_manager.create_from_scheduled_method(self.service.async_scheduled_task, self.service)

        # 启动调度器并运行
        self.scheduler.start()
        time.sleep(3)  # 运行3秒
        self.scheduler.shutdown()

        # 验证任务执行
        results = self.service.get_results()
        self.assertGreater(len(results), 0)

        # 验证不同类型的任务都执行了
        fixed_rate_results = [r for r in results if r.startswith("fixed_rate_")]
        async_results = [r for r in results if r.startswith("async_")]

        self.assertGreater(len(fixed_rate_results), 0)
        self.assertGreater(len(async_results), 0)

    def test_non_scheduled_service(self):
        """测试未启用调度的服务"""
        non_scheduled_service = NonScheduledService()

        # 验证类没有调度标记
        self.assertFalse(hasattr(NonScheduledService, "_scheduling_enabled"))

        # 验证方法有@Scheduled装饰但不应被调度
        self.assertTrue(hasattr(non_scheduled_service.should_not_be_scheduled, "_scheduled_config"))

        # 尝试从非调度服务创建任务应该失败或被忽略
        if self.scheduler.task_manager:
            try:
                task_id = self.scheduler.task_manager.create_from_scheduled_method(
                    non_scheduled_service.should_not_be_scheduled, non_scheduled_service
                )
                # 如果创建成功，验证任务不会实际执行
                if task_id:
                    self.scheduler.start()
                    time.sleep(2)
                    self.scheduler.shutdown()

                    # 验证方法没有被调用
                    self.assertEqual(non_scheduled_service.call_count, 0)
            except Exception:
                # 预期可能会抛出异常，这是正常的
                pass

    def test_annotation_inheritance(self):
        """测试注解继承"""

        @EnableScheduling
        class BaseScheduledService:
            def __init__(self):
                self.base_count = 0

            @Scheduled(fixed_rate="2s")
            def base_task(self):
                self.base_count += 1
                return f"base_{self.base_count}"

        class DerivedScheduledService(BaseScheduledService):
            def __init__(self):
                super().__init__()
                self.derived_count = 0

            @Scheduled(fixed_rate="3s")
            def derived_task(self):
                self.derived_count += 1
                return f"derived_{self.derived_count}"

        # 验证继承的调度功能
        derived_service = DerivedScheduledService()

        # 基类方法应该有调度配置
        self.assertTrue(hasattr(derived_service.base_task, "_scheduled_config"))

        # 派生类方法也应该有调度配置
        self.assertTrue(hasattr(derived_service.derived_task, "_scheduled_config"))

        # 验证配置正确
        base_config = derived_service.base_task._scheduled_config
        derived_config = derived_service.derived_task._scheduled_config

        self.assertEqual(base_config.fixed_rate, "2s")
        self.assertEqual(derived_config.fixed_rate, "3s")

    def test_annotation_error_handling(self):
        """测试注解错误处理"""
        # 测试无效的@Scheduled配置
        try:

            @EnableScheduling
            class InvalidScheduledService:
                @Scheduled()  # 没有提供调度配置
                def invalid_task(self):
                    pass

            # 如果没有抛出异常，验证配置验证
            service = InvalidScheduledService()
            if hasattr(service.invalid_task, "_scheduled_config"):
                config = service.invalid_task._scheduled_config
                # 应该在验证时失败
                with self.assertRaises((ValueError, ScheduleConfigurationError)):
                    config.validate()

        except Exception:
            # 预期在装饰器阶段就会失败
            pass

    def test_multiple_scheduled_annotations(self):
        """测试多个@Scheduled注解的处理"""

        @EnableScheduling
        class MultiScheduledService:
            def __init__(self):
                self.execution_counts = {}

            @Scheduled(fixed_rate="1s")
            def task1(self):
                self.execution_counts["task1"] = self.execution_counts.get("task1", 0) + 1
                return f"task1_{self.execution_counts['task1']}"

            @Scheduled(fixed_delay="1.5s")
            def task2(self):
                self.execution_counts["task2"] = self.execution_counts.get("task2", 0) + 1
                return f"task2_{self.execution_counts['task2']}"

            @Scheduled(cron="*/2 * * * * *")
            def task3(self):
                self.execution_counts["task3"] = self.execution_counts.get("task3", 0) + 1
                return f"task3_{self.execution_counts['task3']}"

        service = MultiScheduledService()

        # 验证所有方法都有调度配置
        self.assertTrue(hasattr(service.task1, "_scheduled_config"))
        self.assertTrue(hasattr(service.task2, "_scheduled_config"))
        self.assertTrue(hasattr(service.task3, "_scheduled_config"))

        # 验证配置类型正确
        self.assertEqual(service.task1._scheduled_config.get_task_type(), TaskType.FIXED_RATE)
        self.assertEqual(service.task2._scheduled_config.get_task_type(), TaskType.FIXED_DELAY)
        self.assertEqual(service.task3._scheduled_config.get_task_type(), TaskType.CRON)


if __name__ == "__main__":
    unittest.main()
