#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置加载器模块

包含：
- AutoConfigurationDiscovery: 自动配置发现器
- AutoConfigurationLoader: 自动配置加载器
- AutoConfigurationRegistry: 自动配置注册表
"""

import importlib
import inspect
import pkgutil
from collections import defaultdict, deque
from pathlib import Path
from typing import Optional, Union

from loguru import logger

from miniboot.context import ApplicationContext


def _safe_import_module(module_name: str) -> Optional[object]:
    """安全导入模块，统一异常处理

    Args:
        module_name: 模块名称

    Returns:
        导入的模块对象，失败时返回None
    """
    try:
        return importlib.import_module(module_name)
    except ImportError as e:
        logger.warning(f"Failed to import module {module_name}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error importing module {module_name}: {e}")
        return None

from ..errors.domains.autoconfigure import \
    AutoConfigurationError as AutoConfigurationException
from ..errors.domains.autoconfigure import \
    ConditionalConfigurationError as CircularDependencyException
from ..errors.domains.autoconfigure import \
    ConfigurationClassError as ConfigurationConflictException
from .base import AutoConfiguration, StarterAutoConfiguration
from .conditions import ConditionEvaluator
from .metadata import AutoConfigurationMetadata


class AutoConfigurationDiscovery:
    """自动配置发现器

    负责自动发现和加载自动配置类:
    1. 扫描指定包路径
    2. 发现AutoConfiguration子类
    3. 自动注册到注册表
    4. 支持配置文件指定
    """

    def __init__(self, registry: "AutoConfigurationRegistry"):
        """初始化发现器

        Args:
            registry: 自动配置注册表
        """
        self.registry = registry
        self._discovered_classes: set[type[AutoConfiguration]] = set()

    def discover_package(self, package_name: str, recursive: bool = True) -> list[type[AutoConfiguration]]:
        """从包中发现自动配置类

        Args:
            package_name: 包名
            recursive: 是否递归扫描子包

        Returns:
            发现的自动配置类列表
        """
        discovered = []

        # 导入包
        package = _safe_import_module(package_name)
        if package is None:
            return discovered

        if recursive:
            # 递归扫描子包
            discovered.extend(self._scan_package_recursive(package))
        else:
            # 只扫描当前包
            discovered.extend(self._scan_module(package))

        return discovered

    def discover_modules(self, module_names: list[str]) -> list[type[AutoConfiguration]]:
        """从指定模块列表中发现自动配置类

        Args:
            module_names: 模块名列表

        Returns:
            发现的自动配置类列表
        """
        discovered = []

        for module_name in module_names:
            module = _safe_import_module(module_name)
            if module is not None:
                discovered.extend(self._scan_module(module))

        return discovered

    def discover_from_factories(self, factories_files: list[str]) -> list[type[AutoConfiguration]]:
        """从 META-INF/mini.factories 文件中发现自动配置类

        Args:
            factories_files: mini.factories 文件路径列表

        Returns:
            发现的自动配置类列表
        """
        discovered = []
        logger.info(f"🔧 discover_from_factories 被调用，文件列表: {factories_files}")

        for factories_file in factories_files:
            try:
                logger.info(f"🔧 正在解析 factories 文件: {factories_file}")
                file_discovered = self._parse_factories_file(factories_file)
                discovered.extend(file_discovered)
                logger.info(f"🔧 从 {factories_file} 发现了 {len(file_discovered)} 个配置类")
                for config_class in file_discovered:
                    logger.info(f"🔧   - {config_class.__name__}")
            except Exception as e:
                logger.error(f"Failed to parse factories file {factories_file}: {e}")

        logger.info(f"🔧 discover_from_factories 完成，总共发现 {len(discovered)} 个配置类")
        return discovered

    def _parse_factories_file(self, factories_file: str) -> list[type[AutoConfiguration]]:
        """解析单个 mini.factories 文件

        Args:
            factories_file: factories 文件路径

        Returns:
            发现的自动配置类列表
        """
        discovered = []

        try:
            logger.debug(f"🔧 开始解析 factories 文件: {factories_file}")
            with open(factories_file, 'r', encoding='utf-8') as f:
                content = f.read()

            logger.debug(f"🔧 文件内容长度: {len(content)} 字符")

            # 解析配置类名列表
            class_names = self._extract_class_names_from_factories(content)
            logger.debug(f"🔧 从 {factories_file} 提取到 {len(class_names)} 个类名: {class_names}")

            # 导入并验证配置类
            for class_name in class_names:
                logger.debug(f"🔧 尝试加载配置类: {class_name}")
                config_class = self._load_configuration_class(class_name)
                if config_class:
                    discovered.append(config_class)
                    logger.debug(f"✅ 成功加载配置类: {class_name}")
                else:
                    logger.warning(f"❌ 加载配置类失败: {class_name}")

            logger.debug(f"🔧 从 {factories_file} 成功发现 {len(discovered)} 个配置类")

        except Exception as e:
            logger.error(f"Error parsing factories file {factories_file}: {e}")

        return discovered

    def _extract_class_names_from_factories(self, content: str) -> list[str]:
        """从 factories 文件内容中提取配置类名

        支持两种格式：
        1. 键值对格式：miniboot.autoconfigure.EnableAutoConfiguration=类名1,类名2
        2. 直接列出格式：类名1\n类名2

        Args:
            content: factories 文件内容

        Returns:
            配置类名列表
        """
        class_names = []
        lines = content.splitlines()

        # 处理续行符和合并行
        processed_lines = self._process_continuation_lines(lines)

        for line in processed_lines:
            line = line.strip()

            # 跳过空行和注释行
            if not line or line.startswith('#'):
                continue

            # 检查是否是键值对格式
            if '=' in line:
                # 键值对格式：key=value
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()

                # 检查是否是自动配置相关的键
                if self._is_auto_configuration_key(key):
                    # 解析值中的类名（可能有多个，逗号分隔）
                    names = self._parse_class_names_from_value(value)
                    class_names.extend(names)
            else:
                # 直接列出格式：假设每行是一个类名
                if self._is_valid_class_name(line):
                    class_names.append(line)

        return class_names

    def _process_continuation_lines(self, lines: list[str]) -> list[str]:
        """处理续行符，合并被 \ 分割的行

        Args:
            lines: 原始行列表

        Returns:
            处理后的行列表
        """
        processed = []
        current_line = ""

        for line in lines:
            line = line.rstrip()

            if line.endswith('\\'):
                # 续行：移除末尾的 \ 并继续累积
                current_line += line[:-1].rstrip()
            else:
                # 非续行：完成当前行
                current_line += line
                if current_line.strip():
                    processed.append(current_line)
                current_line = ""

        # 处理最后一行（如果有未完成的）
        if current_line.strip():
            processed.append(current_line)

        return processed

    def _is_auto_configuration_key(self, key: str) -> bool:
        """检查键是否是自动配置相关的键

        Args:
            key: 配置键

        Returns:
            是否是自动配置键
        """
        auto_config_keys = [
            'miniboot.autoconfigure.EnableAutoConfiguration',
            'miniboot.autoconfigure.WebSocketAutoConfiguration',
            'miniboot.autoconfigure.AutoConfiguration',
            'mini.ioc.AutoConfiguration',  # 兼容旧格式
        ]
        return key in auto_config_keys

    def _parse_class_names_from_value(self, value: str) -> list[str]:
        """从配置值中解析类名列表

        Args:
            value: 配置值，可能包含多个类名（逗号分隔）

        Returns:
            类名列表
        """
        class_names = []

        # 按逗号分割
        parts = value.split(',')

        for part in parts:
            class_name = part.strip()
            if class_name and self._is_valid_class_name(class_name):
                class_names.append(class_name)

        return class_names

    def _is_valid_class_name(self, class_name: str) -> bool:
        """检查是否是有效的类名

        Args:
            class_name: 类名

        Returns:
            是否是有效的类名
        """
        # 基本格式检查：应该包含包名和类名
        return (
            class_name and
            '.' in class_name and
            not class_name.startswith('.') and
            not class_name.endswith('.') and
            all(part.isidentifier() for part in class_name.split('.'))
        )

    def _load_configuration_class(self, class_name: str) -> Union[type[AutoConfiguration], None]:
        """加载并验证配置类

        Args:
            class_name: 完整的类名

        Returns:
            配置类或None（如果加载失败）
        """
        try:
            # 分离模块名和类名
            module_name, class_name_only = class_name.rsplit('.', 1)

            # 导入模块
            module = _safe_import_module(module_name)
            if module is None:
                logger.warning(f"Failed to import module: {module_name}")
                return None

            # 获取类
            if not hasattr(module, class_name_only):
                logger.warning(f"Class {class_name_only} not found in module {module_name}")
                return None

            config_class = getattr(module, class_name_only)

            # 验证是否是 AutoConfiguration 子类
            if not (inspect.isclass(config_class) and
                   issubclass(config_class, AutoConfiguration) and
                   config_class != AutoConfiguration):
                logger.warning(f"Class {class_name} is not a valid AutoConfiguration subclass")
                return None

            logger.debug(f"Successfully loaded configuration class: {class_name}")
            return config_class

        except Exception as e:
            logger.error(f"Failed to load configuration class {class_name}: {e}")
            return None



    def load_and_register(
        self,
        packages: list[str] = None,
        modules: list[str] = None,
        classes: list[type[AutoConfiguration]] = None,
        factories_files: list[str] = None,
    ) -> int:
        """发现并注册自动配置类

        Args:
            packages: 要扫描的包名列表
            modules: 要扫描的模块名列表
            classes: 直接指定的配置类列表
            factories_files: META-INF/mini.factories 文件路径列表

        Returns:
            成功注册的配置类数量
        """
        all_discovered = []

        # 从包中发现
        if packages:
            for package in packages:
                all_discovered.extend(self.discover_package(package))

        # 从模块中发现
        if modules:
            all_discovered.extend(self.discover_modules(modules))

        # 直接指定的类
        if classes:
            all_discovered.extend(classes)

        # 从 factories 文件中发现
        if factories_files:
            logger.info(f"🔧 load_and_register: 开始从 factories 文件发现配置类")
            factories_discovered = self.discover_from_factories(factories_files)
            all_discovered.extend(factories_discovered)
            logger.info(f"🔧 load_and_register: 从 factories 文件发现了 {len(factories_discovered)} 个配置类")

        # 去重
        unique_classes = list(set(all_discovered))

        # 注册到注册表
        registered_count = 0
        for config_class in unique_classes:
            try:
                self.registry.register(config_class)
                self._discovered_classes.add(config_class)
                registered_count += 1
            except Exception as e:
                logger.error(f"Failed to register {config_class.__name__}: {e}")

        logger.info(f"Discovered and registered {registered_count} auto configurations")
        return registered_count

    def _scan_package_recursive(self, package) -> list[type[AutoConfiguration]]:
        """递归扫描包"""
        discovered = []

        # 扫描当前包
        discovered.extend(self._scan_module(package))

        # 递归扫描子包
        if hasattr(package, "__path__"):
            for _importer, modname, ispkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
                submodule = _safe_import_module(modname)
                if submodule is not None:
                    if ispkg:
                        discovered.extend(self._scan_package_recursive(submodule))
                    else:
                        discovered.extend(self._scan_module(submodule))

        return discovered

    def _scan_module(self, module) -> list[type[AutoConfiguration]]:
        """扫描模块中的自动配置类"""
        discovered = []

        for _name, obj in inspect.getmembers(module):
            if inspect.isclass(obj) and issubclass(obj, AutoConfiguration) and obj != AutoConfiguration and obj != StarterAutoConfiguration:
                # 确保类是在当前模块中定义的
                if obj.__module__ == module.__name__:
                    discovered.append(obj)
                    logger.debug(f"Discovered auto configuration: {obj.__name__}")

        return discovered




class AutoConfigurationLoader:
    """自动配置加载器

    提供便捷的自动配置加载和管理功能.
    """

    def __init__(self):
        """初始化加载器"""
        self.registry = AutoConfigurationRegistry()
        self.discovery = AutoConfigurationDiscovery(self.registry)

    def load_defaults(self) -> int:
        """加载默认的自动配置

        扫描miniboot.autoconfigure包下的所有配置类.

        Returns:
            加载的配置数量
        """
        return self.discovery.load_and_register(packages=["miniboot.autoconfigure"])

    def load_packages(self, packages: list[str]) -> int:
        """从指定包加载配置

        Args:
            packages: 包名列表

        Returns:
            加载的配置数量
        """
        return self.discovery.load_and_register(packages=packages)

    def load_config(self, config_file: str) -> int:
        """从配置文件加载

        Args:
            config_file: 配置文件路径

        Returns:
            加载的配置数量
        """
        return self.discovery.load_and_register(config_files=[config_file])

    def get_registry(self) -> "AutoConfigurationRegistry":
        """获取注册表实例

        Returns:
            自动配置注册表
        """
        return self.registry

    def get_discovery(self) -> AutoConfigurationDiscovery:
        """获取发现器实例

        Returns:
            自动配置发现器
        """
        return self.discovery


class AutoConfigurationRegistry:
    """自动配置注册表

    统一管理所有自动配置类,负责:
    1. 注册和发现自动配置类
    2. 依赖关系解析和排序
    3. 条件评估和过滤
    4. 配置执行顺序管理
    """

    def __init__(self):
        """初始化注册表"""
        self._configurations: dict[str, type[AutoConfiguration]] = {}
        self._metadata_cache: dict[str, AutoConfigurationMetadata] = {}
        self._condition_evaluator = ConditionEvaluator()
        self._execution_order: list[str] = []
        self._enabled_configurations: set[str] = set()

    def register(self, config_class: type[AutoConfiguration]) -> None:
        """注册自动配置类

        Args:
            config_class: 自动配置类

        Raises:
            AutoConfigurationException: 注册失败
        """
        try:
            # 创建临时实例获取元数据
            temp_instance = config_class()
            metadata = temp_instance.get_metadata()

            # 验证元数据
            metadata.validate()

            # 检查名称冲突
            if metadata.name in self._configurations:
                existing_class = self._configurations[metadata.name]
                if existing_class != config_class:
                    raise ConfigurationConflictException(f"Configuration name '{metadata.name}' already registered by {existing_class.__name__}")
                return  # 已经注册过相同的类

            # 注册配置类
            self._configurations[metadata.name] = config_class
            self._metadata_cache[metadata.name] = metadata

            logger.debug(f"Registered auto configuration: {metadata.name} ({config_class.__name__})")

        except Exception as e:
            raise AutoConfigurationException(f"Failed to register configuration {config_class.__name__}: {e}")

    def register_multiple(self, config_classes: list[type[AutoConfiguration]]) -> None:
        """批量注册自动配置类

        Args:
            config_classes: 自动配置类列表
        """
        for config_class in config_classes:
            try:
                self.register(config_class)
            except Exception as e:
                logger.error(f"Failed to register {config_class.__name__}: {e}")
                # 继续注册其他配置类

    def get_configuration(self, name: str) -> Optional[type[AutoConfiguration]]:
        """获取指定名称的配置类

        Args:
            name: 配置名称

        Returns:
            配置类或None
        """
        return self._configurations.get(name)

    def get_all_configurations(self) -> dict[str, type[AutoConfiguration]]:
        """获取所有已注册的配置类

        Returns:
            配置名称到配置类的映射
        """
        return self._configurations.copy()

    def get_metadata(self, name: str) -> Optional[AutoConfigurationMetadata]:
        """获取指定配置的元数据

        Args:
            name: 配置名称

        Returns:
            元数据或None
        """
        return self._metadata_cache.get(name)

    def evaluate_conditions(self, context: ApplicationContext) -> set[str]:
        """评估所有配置的条件

        Args:
            context: 应用上下文

        Returns:
            满足条件的配置名称集合
        """
        enabled_configs = set()

        for name, config_class in self._configurations.items():
            try:
                # 创建配置实例
                config_instance = config_class()

                # 评估条件
                if config_instance.should_configure(context):
                    enabled_configs.add(name)
                    logger.debug(f"Configuration {name} conditions satisfied")
                else:
                    logger.debug(f"Configuration {name} conditions not satisfied")

            except Exception as e:
                logger.error(f"Error evaluating conditions for {name}: {e}")
                # 条件评估失败时不启用该配置

        self._enabled_configurations = enabled_configs
        return enabled_configs

    def resolve_execution_order(self, enabled_configs: set[str] = None) -> list[str]:
        """解析配置执行顺序

        Args:
            enabled_configs: 启用的配置集合,None表示使用上次评估结果

        Returns:
            按执行顺序排列的配置名称列表

        Raises:
            CircularDependencyException: 存在循环依赖
        """
        if enabled_configs is None:
            enabled_configs = self._enabled_configurations

        # 构建依赖图
        dependencies = defaultdict(set)
        reverse_dependencies = defaultdict(set)

        for config_name in enabled_configs:
            metadata = self._metadata_cache[config_name]

            # 处理depends_on依赖
            for dep in metadata.depends_on:
                if dep in enabled_configs:
                    dependencies[config_name].add(dep)
                    reverse_dependencies[dep].add(config_name)

            # 处理auto_configure_after依赖
            for after in metadata.auto_configure_after:
                if after in enabled_configs:
                    dependencies[config_name].add(after)
                    reverse_dependencies[after].add(config_name)

            # 处理auto_configure_before依赖
            for before in metadata.auto_configure_before:
                if before in enabled_configs:
                    dependencies[before].add(config_name)
                    reverse_dependencies[config_name].add(before)

        # 使用拓扑排序解析执行顺序
        execution_order = self._topological_sort(enabled_configs, dependencies)

        # 按优先级进行二次排序
        execution_order.sort(key=lambda name: self._metadata_cache[name].priority)

        self._execution_order = execution_order
        return execution_order

    def _topological_sort(self, configs: set[str], dependencies: dict[str, set[str]]) -> list[str]:
        """拓扑排序算法

        Args:
            configs: 配置集合
            dependencies: 依赖关系图

        Returns:
            排序后的配置列表

        Raises:
            CircularDependencyException: 存在循环依赖
        """
        # 计算入度
        in_degree = {config: 0 for config in configs}
        for config in configs:
            for dep in dependencies.get(config, set()):
                if dep in in_degree:
                    in_degree[config] += 1

        # 使用队列进行拓扑排序
        queue = deque([config for config, degree in in_degree.items() if degree == 0])
        result = []

        while queue:
            current = queue.popleft()
            result.append(current)

            # 更新依赖当前配置的其他配置的入度
            for config in configs:
                if current in dependencies.get(config, set()):
                    in_degree[config] -= 1
                    if in_degree[config] == 0:
                        queue.append(config)

        # 检查是否存在循环依赖
        if len(result) != len(configs):
            remaining = configs - set(result)
            raise CircularDependencyException(f"Circular dependency detected among configurations: {remaining}")

        return result

    def get_execution_order(self) -> list[str]:
        """获取当前的执行顺序

        Returns:
            配置执行顺序列表
        """
        return self._execution_order.copy()

    def get_enabled_configurations(self) -> set[str]:
        """获取启用的配置集合

        Returns:
            启用的配置名称集合
        """
        return self._enabled_configurations.copy()

    def configure_all(self, context: ApplicationContext) -> dict[str, bool]:
        """执行所有启用的配置

        Args:
            context: 应用上下文

        Returns:
            配置名称到执行结果的映射
        """
        # 评估条件
        enabled_configs = self.evaluate_conditions(context)

        if not enabled_configs:
            logger.info("No auto configurations enabled")
            return {}

        # 解析执行顺序
        execution_order = self.resolve_execution_order(enabled_configs)

        logger.info(f"Executing {len(execution_order)} auto configurations in order: {execution_order}")

        # 执行配置
        results = {}
        for config_name in execution_order:
            try:
                config_class = self._configurations[config_name]

                # 通过 Bean 工厂创建配置实例，以支持 ApplicationContextAware 接口
                bean_factory = context.get_bean_factory()
                config_instance = config_class()

                # 手动调用感知接口，确保自动配置类能获取应用上下文
                if hasattr(bean_factory, '_invoke_aware_interfaces'):
                    bean_factory._invoke_aware_interfaces(config_instance, config_name)

                # 手动调用生命周期处理器，确保 @PostConstruct 方法被执行
                logger.debug(f"Checking for post processors for {config_name}")
                if hasattr(bean_factory, '_post_processors'):
                    logger.debug(f"Found {len(bean_factory._post_processors)} post processors")
                    for processor in bean_factory._post_processors:
                        if hasattr(processor, 'post_process_after_initialization') and hasattr(processor, 'supports'):
                            try:
                                # 检查处理器是否支持此实例
                                if processor.supports(config_instance, config_name):
                                    logger.debug(f"Applying post processor {processor.__class__.__name__} to {config_name}")
                                    processor.post_process_after_initialization(config_instance, config_name)
                                else:
                                    logger.debug(f"Post processor {processor.__class__.__name__} does not support {config_name}")
                            except Exception as e:
                                logger.debug(f"Post processor {processor.__class__.__name__} failed for {config_name}: {e}")
                else:
                    logger.debug(f"No _post_processors attribute found on bean factory")

                logger.debug(f"Configuring: {config_name}")
                config_instance.configure(context)

                results[config_name] = True
                logger.debug(f"Successfully configured: {config_name}")

            except Exception as e:
                logger.error(f"Failed to configure {config_name}: {e}")
                results[config_name] = False
                # 继续执行其他配置

        return results

    def clear(self) -> None:
        """清空注册表"""
        self._configurations.clear()
        self._metadata_cache.clear()
        self._condition_evaluator.clear_cache()
        self._execution_order.clear()
        self._enabled_configurations.clear()

        logger.debug("Auto configuration registry cleared")

    def get_registry_info(self) -> dict:
        """获取注册表信息

        Returns:
            注册表统计信息
        """
        return {
            "total_configurations": len(self._configurations),
            "enabled_configurations": len(self._enabled_configurations),
            "execution_order": self._execution_order.copy(),
            "configuration_names": list(self._configurations.keys()),
        }
