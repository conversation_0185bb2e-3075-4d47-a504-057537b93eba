#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 组合式组件并发测试 - 验证组合模式组件的线程安全性

测试重构后的组合式组件在并发环境下的线程安全性和性能表现。
"""

import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock

from miniboot.bean.factory import DefaultBeanFactory
# Bean代理
from miniboot.bean.proxy import BeanProxy
# 统一Bean工厂
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.bean.utils import DependencyGraph
# 组合式事件
from miniboot.events.event_types import (ApplicationStartedEvent,
                                         BeanCreatedEvent)
# 生命周期处理器
from miniboot.processor.base import LifecycleProcessor, SmartLifecycle


class ConcurrentTestBean:
    """并发测试用的Bean"""

    def __init__(self, name: str):
        self.name = name
        self.call_count = 0
        self.access_times = []
        self._lock = threading.Lock()

    def thread_safe_method(self) -> str:
        """线程安全的方法"""
        with self._lock:
            self.call_count += 1
            self.access_times.append(time.time())
            return f"result_{self.call_count}"

    def get_stats(self) -> dict:
        """获取统计信息"""
        with self._lock:
            return {"call_count": self.call_count, "access_count": len(self.access_times)}


class ConcurrentLifecycleBean(SmartLifecycle):
    """并发测试用的生命周期Bean"""

    def __init__(self, phase: int = 0):
        self._phase = phase
        self._running = False
        self._start_count = 0
        self._stop_count = 0
        self._lock = threading.Lock()

    def start(self) -> None:
        """启动Bean"""
        with self._lock:
            if not self._running:
                self._running = True
                self._start_count += 1

    def stop(self, callback=None) -> None:
        """停止Bean"""
        with self._lock:
            if self._running:
                self._running = False
                self._stop_count += 1
        if callback:
            callback()

    def is_running(self) -> bool:
        """检查是否运行"""
        with self._lock:
            return self._running

    def get_phase(self) -> int:
        """获取阶段"""
        return self._phase

    def is_auto_startup(self) -> bool:
        """是否自动启动"""
        return True

    def get_stats(self) -> dict:
        """获取统计信息"""
        with self._lock:
            return {"start_count": self._start_count, "stop_count": self._stop_count, "running": self._running}


class CompositeComponentsConcurrencyTestCase(unittest.TestCase):
    """组合式组件并发测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.test_bean = ConcurrentTestBean("concurrentBean")
        self.lifecycle_bean = ConcurrentLifecycleBean(100)

    def test_composite_bean_factory_concurrency(self):
        """测试组合式Bean工厂并发访问"""
        # 创建统一Bean工厂
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = DefaultBeanFactory()

        # 注册Bean定义
        from miniboot.bean.definition import BeanDefinition, BeanScope

        bean_definition = BeanDefinition(bean_name="concurrentBean", bean_class=type(self.test_bean), scope=BeanScope.SINGLETON)
        registry.register_bean_definition("concurrentBean", bean_definition)

        # 直接注册Bean实例到工厂
        factory.register_singleton("concurrentBean", self.test_bean)

        results = []
        errors = []

        def concurrent_access(worker_id: int):
            """并发访问函数"""
            try:
                for i in range(50):
                    bean = factory.get_bean("concurrentBean")
                    result = bean.thread_safe_method()
                    results.append((worker_id, i, result))
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(concurrent_access, i) for i in range(10)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"并发访问出现错误: {errors}")
        self.assertEqual(len(results), 500, "应该有500个访问结果")

        # 验证Bean状态
        stats = self.test_bean.get_stats()
        self.assertEqual(stats["call_count"], 500, "Bean方法应该被调用500次")

        # 验证性能
        self.assertLess(end_time - start_time, 5.0, "并发访问应该在5秒内完成")

        # 清理
        factory.shutdown()

        print(f"✅ Bean工厂并发测试通过 - 耗时: {end_time - start_time:.3f}秒")

    def test_composite_proxy_concurrency(self):
        """测试组合式代理并发访问"""
        # 创建组合式代理
        proxy = CompositeProxy(sync_bean=self.test_bean, bean_name="concurrentProxy")

        results = []
        errors = []

        def concurrent_proxy_access(worker_id: int):
            """并发代理访问函数"""
            try:
                for i in range(30):
                    result = proxy.thread_safe_method()
                    results.append((worker_id, i, result))
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(concurrent_proxy_access, i) for i in range(8)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"代理并发访问出现错误: {errors}")
        self.assertEqual(len(results), 240, "应该有240个代理访问结果")

        # 验证代理统计
        proxy_stats = proxy.get_stats()
        self.assertGreater(proxy_stats["access_count"], 0, "代理访问计数应该大于0")

        # 验证底层Bean状态
        bean_stats = self.test_bean.get_stats()
        self.assertEqual(bean_stats["call_count"], 240, "底层Bean方法应该被调用240次")

        # 验证性能
        self.assertLess(end_time - start_time, 3.0, "代理并发访问应该在3秒内完成")

        # 清理
        proxy.shutdown()

        print(f"✅ 代理并发测试通过 - 耗时: {end_time - start_time:.3f}秒")

    def test_composite_event_concurrency(self):
        """测试组合式事件并发创建和处理"""
        events = []
        errors = []

        def concurrent_event_creation(worker_id: int):
            """并发事件创建函数"""
            try:
                worker_events = []
                for i in range(20):
                    # 创建应用启动事件
                    app_event = ApplicationStartedEvent(application=Mock(), startup_time=1.5)
                    worker_events.append(app_event)

                    # 创建Bean创建事件
                    bean_event = BeanCreatedEvent(bean_name=f"bean_{worker_id}_{i}", bean_class="TestBean", source=Mock())
                    worker_events.append(bean_event)

                events.extend(worker_events)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=12) as executor:
            futures = [executor.submit(concurrent_event_creation, i) for i in range(12)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"事件并发创建出现错误: {errors}")
        self.assertEqual(len(events), 480, "应该创建480个事件")

        # 验证事件唯一性
        event_ids = [event.event_id for event in events]
        unique_ids = set(event_ids)
        self.assertEqual(len(unique_ids), len(event_ids), "所有事件ID应该唯一")

        # 验证事件功能
        for event in events[:10]:  # 验证前10个事件
            self.assertIsNotNone(event.event_id)
            self.assertIsNotNone(event.timestamp)
            self.assertFalse(event.is_processed())

        # 验证性能
        self.assertLess(end_time - start_time, 2.0, "事件并发创建应该在2秒内完成")

        # 清理事件
        for event in events:
            event.cleanup()

        print(f"✅ 事件并发测试通过 - 耗时: {end_time - start_time:.3f}秒")

    def test_lifecycle_processor_concurrency(self):
        """测试生命周期处理器并发操作"""
        # 创建生命周期处理器
        processor = LifecycleProcessor()

        # 添加多个生命周期Bean
        lifecycle_beans = []
        for i in range(20):
            bean = ConcurrentLifecycleBean(i * 10)
            lifecycle_beans.append(bean)
            processor.add_lifecycle_bean(f"lifecycleBean_{i}", bean)

        results = []
        errors = []

        def concurrent_lifecycle_operations(worker_id: int):
            """并发生命周期操作函数"""
            try:
                for i in range(10):
                    # 启动处理器
                    processor.start()

                    # 检查状态
                    is_running = processor.is_running()
                    results.append((worker_id, i, "start", is_running))

                    # 停止处理器
                    processor.stop()

                    # 检查状态
                    is_running = processor.is_running()
                    results.append((worker_id, i, "stop", is_running))

                    # 短暂休眠
                    time.sleep(0.001)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(concurrent_lifecycle_operations, i) for i in range(5)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"生命周期并发操作出现错误: {errors}")
        self.assertEqual(len(results), 100, "应该有100个操作结果")

        # 验证Bean状态一致性
        for bean in lifecycle_beans:
            stats = bean.get_stats()
            # 由于并发操作，start_count和stop_count可能不完全相等，但应该接近
            self.assertGreaterEqual(stats["start_count"], 0)
            self.assertGreaterEqual(stats["stop_count"], 0)

        # 验证性能
        self.assertLess(end_time - start_time, 3.0, "生命周期并发操作应该在3秒内完成")

        print(f"✅ 生命周期处理器并发测试通过 - 耗时: {end_time - start_time:.3f}秒")


if __name__ == "__main__":
    unittest.main(verbosity=2)
