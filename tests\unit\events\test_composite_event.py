#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 组合式事件系统测试 - 验证组合模式替代继承层次的效果
"""

import unittest
from datetime import datetime
from unittest.mock import Mock

from miniboot.events.components import Async<PERSON><PERSON><PERSON>omponent, BeanEventComponent, EventDataComponent, EventIdComponent
from miniboot.events.composite_event import CompositeEvent
from miniboot.events.event_types import (
    ApplicationFailedEvent,
    ApplicationReadyEvent,
    ApplicationStartedEvent,
    ApplicationStoppedEvent,
    BeanCreatedEvent,
    BeanDestroyedEvent,
    BeanInitializedEvent,
    EventTypes,
    create_async_bean_creation_completed_event,
    create_async_bean_creation_failed_event,
    create_async_bean_creation_started_event,
)


class CompositeEventTestCase(unittest.TestCase):
    """组合式事件测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.mock_source = Mock()
        self.test_data = {"key1": "value1", "key2": 42}

    def test_composite_event_creation(self):
        """测试组合式事件创建"""
        event = CompositeEvent(event_type="TestEvent", source=self.mock_source, data=self.test_data)

        # 验证基本属性
        self.assertEqual(event.event_type, "TestEvent")
        self.assertIs(event.source, self.mock_source)
        self.assertIsInstance(event.timestamp, datetime)
        self.assertFalse(event.processed)
        self.assertTrue(event.event_id.startswith("evt_"))

        # 验证数据访问
        self.assertEqual(event.get_data("key1"), "value1")
        self.assertEqual(event.get_data("key2"), 42)
        self.assertIsNone(event.get_data("nonexistent"))
        self.assertEqual(event.get_data("nonexistent", "default"), "default")

    def test_event_components(self):
        """测试事件组件"""
        event = CompositeEvent("TestEvent", self.mock_source)

        # 验证组件存在
        self.assertIsNotNone(event.get_component(EventIdComponent))
        self.assertIsNotNone(event.get_component(EventDataComponent))
        self.assertIsNotNone(event.get_component(AsyncEventComponent))
        self.assertIsNotNone(event.get_component(BeanEventComponent))

    def test_event_data_management(self):
        """测试事件数据管理"""
        event = CompositeEvent("TestEvent", self.mock_source)

        # 设置数据
        event.set_data("test_key", "test_value")
        self.assertEqual(event.get_data("test_key"), "test_value")

        # 获取所有数据
        all_data = event.get_all_data()
        self.assertIn("test_key", all_data)
        self.assertEqual(all_data["test_key"], "test_value")

    def test_event_processing_state(self):
        """测试事件处理状态"""
        event = CompositeEvent("TestEvent", self.mock_source)

        # 初始状态
        self.assertFalse(event.is_processed())
        self.assertFalse(event.processed)

        # 标记为已处理
        event.mark_processed()
        self.assertTrue(event.is_processed())
        self.assertTrue(event.processed)

    def test_async_event_functionality(self):
        """测试异步事件功能"""
        event = CompositeEvent("AsyncTestEvent", self.mock_source)

        # 注册为异步事件
        async_data = {"operation": "test"}
        event.register_as_async(async_data)

        # 开始异步处理
        event.start_async_processing()
        self.assertTrue(event.is_async_processing())

        # 完成异步处理
        event.finish_async_processing()
        self.assertFalse(event.is_async_processing())

    def test_bean_event_functionality(self):
        """测试Bean事件功能"""
        event = CompositeEvent("BeanTestEvent", self.mock_source)

        # 注册为Bean事件
        bean_name = "testBean"
        event.register_bean_event(bean_name)

        # 验证Bean关联
        self.assertEqual(event.get_bean_name(), bean_name)

    def test_event_cleanup(self):
        """测试事件清理"""
        event = CompositeEvent("TestEvent", self.mock_source, self.test_data)

        # 设置一些状态
        event.register_as_async({"test": "data"})
        event.start_async_processing()

        # 清理事件
        event.cleanup()

        # 验证清理效果
        self.assertFalse(event.is_async_processing())


class ApplicationEventTestCase(unittest.TestCase):
    """应用事件测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.mock_app = Mock()

    def test_application_started_event(self):
        """测试应用启动事件"""
        startup_time = 2.5
        event = ApplicationStartedEvent(self.mock_app, startup_time)

        self.assertEqual(event.event_type, EventTypes.APPLICATION_STARTED)
        self.assertIs(event.source, self.mock_app)
        self.assertEqual(event.get_startup_time(), startup_time)

    def test_application_stopped_event(self):
        """测试应用停止事件"""
        shutdown_time = 1.2
        event = ApplicationStoppedEvent(self.mock_app, shutdown_time)

        self.assertEqual(event.event_type, EventTypes.APPLICATION_STOPPED)
        self.assertIs(event.source, self.mock_app)
        self.assertEqual(event.get_shutdown_time(), shutdown_time)

    def test_application_ready_event(self):
        """测试应用就绪事件"""
        event = ApplicationReadyEvent(self.mock_app)

        self.assertEqual(event.event_type, EventTypes.APPLICATION_READY)
        self.assertIs(event.source, self.mock_app)

    def test_application_failed_event(self):
        """测试应用失败事件"""
        exception = ValueError("Test error")
        event = ApplicationFailedEvent(self.mock_app, exception)

        self.assertEqual(event.event_type, EventTypes.APPLICATION_FAILED)
        self.assertIs(event.source, self.mock_app)
        self.assertIs(event.get_exception(), exception)
        self.assertEqual(event.get_exception_type(), "ValueError")
        self.assertEqual(event.get_exception_message(), "Test error")


class BeanEventTestCase(unittest.TestCase):
    """Bean事件测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.bean_name = "testBean"
        self.bean_class = "TestBeanClass"
        self.mock_source = Mock()

    def test_bean_created_event(self):
        """测试Bean创建事件"""
        event = BeanCreatedEvent(self.bean_name, self.bean_class, self.mock_source)

        self.assertEqual(event.event_type, EventTypes.BEAN_CREATED)
        self.assertIs(event.source, self.mock_source)
        self.assertEqual(event.get_bean_name(), self.bean_name)
        self.assertEqual(event.get_bean_class(), self.bean_class)

        # 验证Bean事件注册
        self.assertEqual(event.get_bean_name(), self.bean_name)

    def test_bean_destroyed_event(self):
        """测试Bean销毁事件"""
        event = BeanDestroyedEvent(self.bean_name, self.bean_class, self.mock_source)

        self.assertEqual(event.event_type, EventTypes.BEAN_DESTROYED)
        self.assertEqual(event.get_bean_name(), self.bean_name)
        self.assertEqual(event.get_bean_class(), self.bean_class)

    def test_bean_initialized_event(self):
        """测试Bean初始化事件"""
        event = BeanInitializedEvent(self.bean_name, self.bean_class, self.mock_source)

        self.assertEqual(event.event_type, EventTypes.BEAN_INITIALIZED)
        self.assertEqual(event.get_bean_name(), self.bean_name)
        self.assertEqual(event.get_bean_class(), self.bean_class)


class AsyncBeanEventTestCase(unittest.TestCase):
    """异步Bean事件测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.bean_name = "asyncTestBean"
        self.bean_class = "AsyncTestBeanClass"
        self.mock_source = Mock()

    def test_async_bean_creation_started_event(self):
        """测试异步Bean创建开始事件"""
        event = create_async_bean_creation_started_event(self.bean_name, self.bean_class, self.mock_source)

        self.assertEqual(event.event_type, EventTypes.ASYNC_BEAN_CREATION_STARTED)
        self.assertEqual(event.get_bean_name(), self.bean_name)
        self.assertEqual(event.get_bean_class(), self.bean_class)
        self.assertIsNone(event.get_duration())
        self.assertIsNone(event.is_success())

        # 验证异步事件注册
        self.assertTrue(event.is_async_processing() or not event.is_async_processing())  # 状态可能变化

    def test_async_bean_creation_completed_event(self):
        """测试异步Bean创建完成事件"""
        duration = 1.5
        event = create_async_bean_creation_completed_event(self.bean_name, self.bean_class, duration, self.mock_source)

        self.assertEqual(event.event_type, EventTypes.ASYNC_BEAN_CREATION_COMPLETED)
        self.assertEqual(event.get_bean_name(), self.bean_name)
        self.assertEqual(event.get_bean_class(), self.bean_class)
        self.assertEqual(event.get_duration(), duration)
        self.assertTrue(event.is_success())
        self.assertIsNone(event.get_error())

    def test_async_bean_creation_failed_event(self):
        """测试异步Bean创建失败事件"""
        error = RuntimeError("Creation failed")
        event = create_async_bean_creation_failed_event(self.bean_name, self.bean_class, error, self.mock_source)

        self.assertEqual(event.event_type, EventTypes.ASYNC_BEAN_CREATION_FAILED)
        self.assertEqual(event.get_bean_name(), self.bean_name)
        self.assertEqual(event.get_bean_class(), self.bean_class)
        self.assertFalse(event.is_success())
        self.assertIs(event.get_error(), error)


class EventInheritanceComparisonTestCase(unittest.TestCase):
    """事件继承对比测试用例"""

    def test_composition_vs_inheritance_complexity(self):
        """测试组合模式vs继承的复杂性对比"""
        # 创建组合式事件
        composite_event = CompositeEvent("TestEvent", Mock())

        # 验证组合式事件的优势
        # 1. 可以动态添加组件
        custom_component = Mock()
        custom_component.component_name = "CustomComponent"
        custom_component.initialize = Mock()
        custom_component.shutdown = Mock()

        composite_event.add_component(custom_component)
        custom_component.initialize.assert_called_once()

        # 2. 可以独立测试组件
        data_component = composite_event.get_component(EventDataComponent)
        self.assertIsNotNone(data_component)

        # 3. 职责分离清晰
        async_component = composite_event.get_component(AsyncEventComponent)
        bean_component = composite_event.get_component(BeanEventComponent)
        self.assertIsNotNone(async_component)
        self.assertIsNotNone(bean_component)

        # 清理
        composite_event.shutdown()
        custom_component.shutdown.assert_called_once()

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 新的组合式事件应该提供与旧事件相同的接口
        event = ApplicationStartedEvent(Mock(), 2.0)

        # 基本事件接口
        self.assertTrue(hasattr(event, "event_id"))
        self.assertTrue(hasattr(event, "event_type"))
        self.assertTrue(hasattr(event, "source"))
        self.assertTrue(hasattr(event, "timestamp"))
        self.assertTrue(hasattr(event, "processed"))

        # 数据访问接口
        self.assertTrue(hasattr(event, "get_data"))
        self.assertTrue(hasattr(event, "set_data"))

        # 处理状态接口
        self.assertTrue(hasattr(event, "mark_processed"))
        self.assertTrue(hasattr(event, "is_processed"))


if __name__ == "__main__":
    unittest.main()
