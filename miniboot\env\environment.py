#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
* @author: cz
* @description: Environment management module - provides environment configuration management
"""

from abc import ABC, abstractmethod
from typing import Any

from .config import ConfigurationLoader
from .resolver import PropertyResolver
from .sources import MutablePropertySources, SystemEnvironmentPropertySource


class Environment(PropertyResolver, ABC):
    """环境接口

    扩展 PropertyResolver,添加 Profile 管理功能.
    """

    @abstractmethod
    def get_active_profiles(self) -> list[str]:
        """获取激活的 Profile 列表

        Returns:
            激活的 Profile 名称列表
        """
        raise NotImplementedError

    @abstractmethod
    def get_default_profiles(self) -> list[str]:
        """获取默认的 Profile 列表

        Returns:
            默认的 Profile 名称列表
        """
        raise NotImplementedError

    @abstractmethod
    def accepts_profiles(self, *profiles: str) -> bool:
        """检查是否接受指定的 Profile

        Args:
            profiles: Profile 名称列表

        Returns:
            如果当前环境接受任一指定 Profile 返回 True
        """
        raise NotImplementedError


class ConfigurableEnvironment(Environment, ABC):
    """可配置环境接口

    扩展 Environment,添加配置修改功能.
    """

    @abstractmethod
    def set_active_profiles(self, *profiles: str) -> None:
        """Set active profiles

        Args:
            profiles: Profile name list
        """
        raise NotImplementedError

    @abstractmethod
    def add_active_profile(self, profile: str) -> None:
        """添加激活的 Profile

        Args:
            profile: Profile 名称
        """
        raise NotImplementedError

    @abstractmethod
    def set_default_profiles(self, *profiles: str) -> None:
        """设置默认的 Profile

        Args:
            profiles: Profile 名称列表
        """
        raise NotImplementedError

    @abstractmethod
    def get_property_sources(self) -> MutablePropertySources:
        """获取属性源管理器

        Returns:
            属性源管理器实例
        """
        raise NotImplementedError

    @abstractmethod
    def set_property(self, key: str, value: Any) -> None:
        """设置属性值

        Args:
            key: 属性键
            value: 属性值
        """
        raise NotImplementedError

    @abstractmethod
    def remove_property(self, key: str) -> bool:
        """移除属性

        Args:
            key: 属性键

        Returns:
            bool: 如果成功移除返回True,否则返回False
        """
        raise NotImplementedError

    @abstractmethod
    def get_property_source_names(self) -> list[str]:
        """获取所有属性源名称

        Returns:
            list[str]: 属性源名称列表
        """
        raise NotImplementedError

    @abstractmethod
    def add_property_source(self, name: str, source: dict[str, Any], first: bool = False) -> None:
        """添加属性源

        Args:
            name: 属性源名称
            source: 属性源数据
            first: 是否添加到最前面(优先级最高)
        """
        raise NotImplementedError

    @abstractmethod
    def remove_property_source(self, name: str) -> bool:
        """移除属性源

        Args:
            name: 属性源名称

        Returns:
            bool: 如果成功移除返回True,否则返回False
        """
        raise NotImplementedError


class StandardEnvironment(ConfigurableEnvironment):
    """标准环境实现

    提供基于属性源的环境管理功能.
    """

    # 默认的 Profile 相关属性键
    ACTIVE_PROFILES_PROPERTY_NAME = "miniboot.profiles.active"
    DEFAULT_PROFILES_PROPERTY_NAME = "miniboot.profiles.default"

    def __init__(self):
        """初始化标准环境"""
        self._property_sources = MutablePropertySources()
        self._active_profiles: set[str] = set()
        self._default_profiles: set[str] = {"default"}

        # 先添加系统环境变量以便读取 Profile 配置
        self._property_sources.add_last(SystemEnvironmentPropertySource())

        # 从环境变量中读取 Profile 配置
        self._setup_profiles()

        # 两阶段配置加载
        self._load_with_profiles()

    def _setup_sources(self, property_sources: MutablePropertySources) -> None:
        """自定义属性源

        子类可以重写此方法来添加自定义属性源.
        默认会自动加载 resources 目录下的配置文件.

        Args:
            property_sources: 属性源管理器
        """
        # 自动加载配置文件 (类似 Spring Boot)
        configuration_loader = ConfigurationLoader()
        configuration_loader.load_configuration(property_sources, self._active_profiles)

        # 系统环境变量已经在初始化时添加了, 不需要重复添加

    def _load_with_profiles(self) -> None:
        """两阶段配置加载: 先加载默认配置检测 Profile, 再重新加载所有配置"""

        # 第一阶段: 加载默认配置文件以检测 Profile 配置
        configuration_loader = ConfigurationLoader()
        temp_property_sources = MutablePropertySources()

        # 复制系统环境变量到临时属性源
        temp_property_sources.add_last(SystemEnvironmentPropertySource())

        # 只加载默认配置文件(不包含 profile 特定的)
        # 使用新的优化加载机制
        configuration_loader.load_configuration(temp_property_sources, set())

        # 从默认配置文件中读取 Profile 配置
        original_property_sources = self._property_sources
        self._property_sources = temp_property_sources
        self._setup_profiles()
        self._property_sources = original_property_sources

        # 第二阶段: 基于检测到的 Profile 重新加载所有配置
        self._setup_sources(self._property_sources)

    def _setup_profiles(self) -> None:
        """从属性配置 Profile"""
        # 配置激活的 Profile
        active_profiles_value = self.get_property(self.ACTIVE_PROFILES_PROPERTY_NAME)
        if active_profiles_value and isinstance(active_profiles_value, str):
            profiles = [p.strip() for p in active_profiles_value.split(",") if p.strip()]
            self._active_profiles.update(profiles)

        # 配置默认的 Profile
        default_profiles_value = self.get_property(self.DEFAULT_PROFILES_PROPERTY_NAME)
        if default_profiles_value and isinstance(default_profiles_value, str):
            profiles = [p.strip() for p in default_profiles_value.split(",") if p.strip()]
            self._default_profiles = set(profiles)

    def get_property(self, key: str, default: Any = None) -> Any:
        """获取属性值"""
        value = self._property_sources.get_property(key, default)

        # 如果值是字符串, 尝试解析占位符
        if isinstance(value, str):
            return self.resolve(value)

        return value

    def contains_property(self, key: str) -> bool:
        """检查是否包含指定属性"""
        return self._property_sources.contains_property(key)

    def get_properties_with_prefix(self, prefix: str) -> dict[str, Any]:
        """获取指定前缀的所有属性

        Args:
            prefix: 属性前缀

        Returns:
            dict[str, Any]: 匹配前缀的属性字典，键为去掉前缀后的部分
        """
        result = {}
        prefix_with_dot = f"{prefix}."

        # 遍历所有属性源查找匹配的属性
        for property_source in self._property_sources:
            if hasattr(property_source, "_properties"):
                for prop_key, prop_value in property_source._properties.items():
                    if prop_key.startswith(prefix_with_dot):
                        # 提取去掉前缀后的键名
                        key_without_prefix = prop_key[len(prefix_with_dot):]
                        result[key_without_prefix] = prop_value

        return result

    def get_active_profiles(self) -> list[str]:
        """获取激活的 Profile 列表"""
        return list(self._active_profiles)

    def get_default_profiles(self) -> list[str]:
        """获取默认的 Profile 列表"""
        return list(self._default_profiles)

    def accepts_profiles(self, *profiles: str) -> bool:
        """检查是否接受指定的 Profile"""
        if not profiles:
            return True

        # 获取当前有效的 Profile(激活的或默认的)
        effective_profiles = self._active_profiles if self._active_profiles else self._default_profiles

        # 检查是否有任何指定的 Profile 在有效 Profile 中
        return any(profile in effective_profiles for profile in profiles)

    def set_active_profiles(self, *profiles: str) -> None:
        """设置激活的 Profile"""
        self._active_profiles = set(profiles)

    def add_active_profile(self, profile: str) -> None:
        """添加激活的 Profile"""
        self._active_profiles.add(profile)

    def set_default_profiles(self, *profiles: str) -> None:
        """设置默认的 Profile"""
        self._default_profiles = set(profiles)

    def get_property_sources(self) -> MutablePropertySources:
        """获取属性源管理器"""
        return self._property_sources

    def set_property(self, key: str, value: Any) -> None:
        """设置属性值"""
        # 创建或更新运行时属性源
        runtime_source = self._property_sources.get("runtime")
        if runtime_source is None:
            from miniboot.env.sources import MapPropertySource

            runtime_source = MapPropertySource("runtime", {}, priority=2000)
            self._property_sources.add_first(runtime_source)

        # 设置属性值
        runtime_source._properties[key] = value

    def remove_property(self, key: str) -> bool:
        """移除属性"""
        # 从运行时属性源中移除
        runtime_source = self._property_sources.get("runtime")
        if runtime_source and hasattr(runtime_source, "_properties") and key in runtime_source._properties:
            del runtime_source._properties[key]
            return True
        return False

    def get_property_source_names(self) -> list[str]:
        """获取所有属性源名称"""
        return [ps.name for ps in self._property_sources._property_sources]

    def add_property_source(self, name: str, source: dict[str, Any], first: bool = False) -> None:
        """添加属性源"""
        from miniboot.env.sources import MapPropertySource

        priority = 1500 if first else 500
        property_source = MapPropertySource(name, source, priority)

        if first:
            self._property_sources.add_first(property_source)
        else:
            self._property_sources.add_last(property_source)

    def remove_property_source(self, name: str) -> bool:
        """移除属性源"""
        removed = self._property_sources.remove(name)
        return removed is not None

    def refresh(self) -> None:
        """刷新环境配置"""
        # 重新加载配置
        self._load_with_profiles()
