#!/usr/bin/env python
"""
* @author: cz
* @description: PropertySource类单元测试
"""

import os
import unittest
from unittest.mock import patch

from miniboot.env.sources import MapPropertySource, MutablePropertySources, PropertySource, SystemEnvironmentPropertySource


class PropertySourceTestCase(unittest.TestCase):
    """PropertySource基类测试用例"""

    def test_abstract_methods(self):
        """测试抽象方法"""
        # PropertySource是抽象类，不能直接实例化
        with self.assertRaises(TypeError):
            PropertySource("test")


class MapPropertySourceTestCase(unittest.TestCase):
    """MapPropertySource测试用例"""

    def setUp(self):
        """测试前准备"""
        self.properties = {"app.name": "test-app", "app.version": "1.0.0", "app.port": 8080, "app.enabled": True}
        self.source = MapPropertySource("test", self.properties)

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.source.name, "test")
        self.assertEqual(self.source.properties, self.properties)

    def test_get_property(self):
        """测试获取属性"""
        self.assertEqual(self.source.get_property("app.name"), "test-app")
        self.assertEqual(self.source.get_property("app.version"), "1.0.0")
        self.assertEqual(self.source.get_property("app.port"), 8080)
        self.assertTrue(self.source.get_property("app.enabled"))

    def test_get_property_not_found(self):
        """测试获取不存在的属性"""
        self.assertIsNone(self.source.get_property("non.existent"))

    def test_contains_property(self):
        """测试检查属性是否存在"""
        self.assertTrue(self.source.contains_property("app.name"))
        self.assertFalse(self.source.contains_property("non.existent"))

    def test_properties_access(self):
        """测试属性字典访问"""
        # 验证可以访问底层属性字典
        self.assertEqual(len(self.source.properties), 4)
        self.assertIn("app.name", self.source.properties)
        self.assertIn("app.version", self.source.properties)

    def test_string_representation(self):
        """测试字符串表示"""
        repr_str = repr(self.source)
        self.assertIn("MapPropertySource", repr_str)
        # 基本的对象表示应该包含类名

    def test_identity_comparison(self):
        """测试对象身份比较"""
        # 由于没有自定义__eq__方法，对象比较基于身份
        same_source = self.source
        different_source = MapPropertySource("other", self.properties)

        self.assertEqual(self.source, same_source)
        self.assertNotEqual(self.source, different_source)

    def test_priority_default(self):
        """测试默认优先级"""
        self.assertEqual(self.source.priority, 0)

    def test_priority_custom(self):
        """测试自定义优先级"""
        high_priority_source = MapPropertySource("high", {}, priority=100)
        self.assertEqual(high_priority_source.priority, 100)


class SystemEnvironmentPropertySourceTestCase(unittest.TestCase):
    """SystemEnvironmentPropertySource测试用例"""

    def setUp(self):
        """测试前准备"""
        self.source = SystemEnvironmentPropertySource()

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.source.name, "systemEnvironment")

    @patch.dict(os.environ, {"TEST_VAR": "test_value", "ANOTHER_VAR": "another_value"})
    def test_get_property_from_environment(self):
        """测试从环境变量获取属性"""
        self.assertEqual(self.source.get_property("TEST_VAR"), "test_value")
        self.assertEqual(self.source.get_property("ANOTHER_VAR"), "another_value")

    def test_get_property_not_found(self):
        """测试获取不存在的环境变量"""
        self.assertIsNone(self.source.get_property("NON_EXISTENT_VAR"))

    @patch.dict(os.environ, {"TEST_VAR": "test_value"})
    def test_contains_property(self):
        """测试检查环境变量是否存在"""
        self.assertTrue(self.source.contains_property("TEST_VAR"))
        self.assertFalse(self.source.contains_property("NON_EXISTENT_VAR"))

    @patch.dict(os.environ, {"VAR1": "value1", "VAR2": "value2"})
    def test_environment_access(self):
        """测试环境变量访问"""
        # 验证可以访问环境变量
        self.assertEqual(self.source.get_property("VAR1"), "value1")
        self.assertEqual(self.source.get_property("VAR2"), "value2")

    def test_priority(self):
        """测试优先级"""
        # 系统环境变量的默认优先级
        from miniboot.env.priority import ConfigurationPriority

        self.assertEqual(self.source.priority, ConfigurationPriority.SYSTEM_ENVIRONMENT)


class MutablePropertySourcesTestCase(unittest.TestCase):
    """MutablePropertySources测试用例"""

    def setUp(self):
        """测试前准备"""
        self.sources = MutablePropertySources()
        self.source1 = MapPropertySource("source1", {"key1": "value1"})
        self.source2 = MapPropertySource("source2", {"key2": "value2"})
        self.source3 = MapPropertySource("source3", {"key3": "value3"})

    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(len(self.sources), 0)
        self.assertEqual(list(self.sources), [])

    def test_add_last(self):
        """测试添加到末尾"""
        self.sources.add_last(self.source1)
        self.sources.add_last(self.source2)

        self.assertEqual(len(self.sources), 2)
        self.assertEqual(list(self.sources), [self.source1, self.source2])

    def test_add_first(self):
        """测试添加到开头"""
        self.sources.add_last(self.source1)
        self.sources.add_first(self.source2)

        self.assertEqual(len(self.sources), 2)
        self.assertEqual(list(self.sources), [self.source2, self.source1])

    def test_add_before(self):
        """测试在指定源之前添加"""
        self.sources.add_last(self.source1)
        self.sources.add_last(self.source3)
        self.sources.add_before("source3", self.source2)

        expected = [self.source1, self.source2, self.source3]
        self.assertEqual(list(self.sources), expected)

    def test_add_after(self):
        """测试在指定源之后添加"""
        self.sources.add_last(self.source1)
        self.sources.add_last(self.source3)
        self.sources.add_after("source1", self.source2)

        expected = [self.source1, self.source2, self.source3]
        self.assertEqual(list(self.sources), expected)

    def test_remove(self):
        """测试移除属性源"""
        self.sources.add_last(self.source1)
        self.sources.add_last(self.source2)

        removed = self.sources.remove("source1")
        self.assertEqual(removed, self.source1)
        self.assertEqual(list(self.sources), [self.source2])

    def test_remove_not_found(self):
        """测试移除不存在的属性源"""
        self.sources.add_last(self.source1)

        removed = self.sources.remove("non_existent")
        self.assertIsNone(removed)
        self.assertEqual(list(self.sources), [self.source1])

    def test_replace(self):
        """测试替换属性源"""
        self.sources.add_last(self.source1)
        self.sources.add_last(self.source2)

        self.sources.replace("source1", self.source3)
        expected = [self.source3, self.source2]
        self.assertEqual(list(self.sources), expected)

    def test_contains(self):
        """测试检查是否包含属性源"""
        self.sources.add_last(self.source1)

        self.assertTrue(self.sources.contains("source1"))
        self.assertFalse(self.sources.contains("non_existent"))

    def test_get(self):
        """测试获取属性源"""
        self.sources.add_last(self.source1)

        retrieved = self.sources.get("source1")
        self.assertEqual(retrieved, self.source1)

        not_found = self.sources.get("non_existent")
        self.assertIsNone(not_found)

    def test_priority_ordering(self):
        """测试优先级排序"""
        high_priority = MapPropertySource("high", {}, priority=100)
        medium_priority = MapPropertySource("medium", {}, priority=50)
        low_priority = MapPropertySource("low", {}, priority=10)

        # 添加顺序与优先级不同
        self.sources.add_last(low_priority)
        self.sources.add_last(high_priority)
        self.sources.add_last(medium_priority)

        # 应该按优先级排序
        expected = [high_priority, medium_priority, low_priority]
        self.assertEqual(list(self.sources), expected)

    def test_iterator(self):
        """测试迭代器"""
        self.sources.add_last(self.source1)
        self.sources.add_last(self.source2)

        sources_list = []
        for source in self.sources:
            sources_list.append(source)

        self.assertEqual(sources_list, [self.source1, self.source2])

    def test_string_representation(self):
        """测试字符串表示"""
        self.sources.add_last(self.source1)
        self.sources.add_last(self.source2)

        repr_str = repr(self.sources)
        self.assertIn("MutablePropertySources", repr_str)
        # 基本的对象表示应该包含类名


if __name__ == "__main__":
    unittest.main()
