"""
Task Scheduling Starter

任务调度 Starter 组件，提供智能任务调度和管理功能。

主要功能:
- 智能任务调度：基于负载和优先级的调度策略
- 并发控制：可配置的并发任务数量限制
- 任务队列管理：支持优先级队列和延迟执行
- 性能监控：任务执行时间和成功率统计
- 超时控制：可配置的任务执行超时机制

使用方式:
```yaml
miniboot:
  starters:
    web:
      task-scheduler:
        enabled: true
        intelligent-scheduling: true
        max-concurrent-tasks: 100
        task-timeout: 30.0
```
"""

from .scheduler import TaskScheduler
from .properties import TaskSchedulerProperties

__all__ = [
    "TaskScheduler",
    "TaskSchedulerProperties",
]