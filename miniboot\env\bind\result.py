#!/usr/bin/env python
"""
* @author: cz
* @description: 属性绑定结果
"""

from dataclasses import dataclass
from typing import Any, Optional


@dataclass
class BindingError:
    """绑定错误信息"""

    property_name: str
    message: str
    cause: Optional[Exception] = None


class BindingResult:
    """属性绑定结果

    包含绑定的对象实例和可能的错误信息.
    """

    def __init__(self, target: Any = None):
        self._target = target
        self._errors: list[BindingError] = []

    @property
    def target(self) -> Any:
        """获取绑定的目标对象"""
        return self._target

    @property
    def errors(self) -> list[BindingError]:
        """获取绑定错误列表"""
        return self._errors.copy()

    @property
    def has_errors(self) -> bool:
        """检查是否有绑定错误"""
        return len(self._errors) > 0

    def add_error(self, property_name: str, message: str, cause: Exception = None) -> None:
        """添加绑定错误

        Args:
            property_name: 属性名称
            message: 错误消息
            cause: 原始异常
        """
        error = BindingError(property_name, message, cause)
        self._errors.append(error)

    def get_errors_for_property(self, property_name: str) -> list[BindingError]:
        """获取指定属性的错误列表

        Args:
            property_name: 属性名称

        Returns:
            该属性的错误列表
        """
        return [error for error in self._errors if error.property_name == property_name]

    def __str__(self) -> str:
        if not self.has_errors:
            return f"BindingResult(target={self._target}, errors=0)"

        error_messages = [f"{error.property_name}: {error.message}" for error in self._errors]
        return f"BindingResult(target={self._target}, errors={len(self._errors)}: {', '.join(error_messages)})"
