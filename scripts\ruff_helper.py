#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Ruff代码质量检查助手 - 为AI生成的代码提供实时的ruff检查和修复建议
"""

import json
import subprocess
import sys
from pathlib import Path
from typing import Dict, List, Optional


class RuffHelper:
    """Ruff代码质量检查助手"""

    def __init__(self, project_root: Optional[Path] = None):
        """初始化Ruff助手
        
        Args:
            project_root: 项目根目录，默认为当前脚本的父目录
        """
        self.project_root = project_root or Path(__file__).parent.parent
        self.ruff_config = self.project_root / "ruff.toml"
        
    def check_file(self, file_path: str) -> Dict:
        """检查单个文件的代码质量
        
        Args:
            file_path: 要检查的文件路径
            
        Returns:
            检查结果字典，包含错误列表和建议
        """
        try:
            # 运行ruff检查
            result = subprocess.run(
                ["uv", "run", "ruff", "check", file_path, "--output-format=json"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                return {"status": "success", "issues": [], "message": "代码质量检查通过"}
            
            # 解析JSON输出
            try:
                issues = json.loads(result.stdout) if result.stdout else []
            except json.JSONDecodeError:
                issues = []
                
            return {
                "status": "error",
                "issues": issues,
                "message": f"发现 {len(issues)} 个代码质量问题"
            }
            
        except subprocess.TimeoutExpired:
            return {"status": "timeout", "issues": [], "message": "检查超时"}
        except Exception as e:
            return {"status": "error", "issues": [], "message": f"检查失败: {e}"}
    
    def format_file(self, file_path: str) -> Dict:
        """格式化单个文件
        
        Args:
            file_path: 要格式化的文件路径
            
        Returns:
            格式化结果
        """
        try:
            result = subprocess.run(
                ["uv", "run", "ruff", "format", file_path],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                return {"status": "success", "message": "代码格式化完成"}
            else:
                return {"status": "error", "message": f"格式化失败: {result.stderr}"}
                
        except Exception as e:
            return {"status": "error", "message": f"格式化失败: {e}"}
    
    def fix_file(self, file_path: str) -> Dict:
        """自动修复文件中可修复的问题
        
        Args:
            file_path: 要修复的文件路径
            
        Returns:
            修复结果
        """
        try:
            result = subprocess.run(
                ["uv", "run", "ruff", "check", "--fix", file_path],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return {"status": "success", "message": "自动修复完成"}
                
        except Exception as e:
            return {"status": "error", "message": f"自动修复失败: {e}"}
    
    def get_ruff_rules(self) -> Dict:
        """获取项目的ruff规则配置
        
        Returns:
            ruff规则配置字典
        """
        rules = {
            "line_length": 150,
            "target_version": "py39",
            "enabled_rules": [
                "F",    # Pyflakes
                "E", "W",  # pycodestyle
                "I",    # isort
                "N",    # pep8-naming
                "UP",   # pyupgrade
                "B",    # flake8-bugbear
                "SIM",  # flake8-simplify
                "C4",   # flake8-comprehensions
                "PIE",  # flake8-pie
                "ARG",  # flake8-unused-arguments
                "PTH",  # flake8-use-pathlib
            ],
            "ignored_rules": [
                "E501",   # 行长度由格式化工具处理
                "F403", "F405",  # 允许在 __init__.py 中使用 import *
                "PIE790", # 允许抽象方法中的 pass 语句
                "E402",   # 允许函数内部导入
                "UP009",  # 允许UTF-8编码声明
                "I001",   # 忽略导入排序问题
                "N802",   # 允许注解函数使用大写开头
            ],
            "format_rules": {
                "quote_style": "double",
                "indent_style": "space",
                "line_ending": "auto"
            }
        }
        return rules


def main():
    """主函数 - 命令行工具入口"""
    if len(sys.argv) < 2:
        print("用法: uv run python scripts/ruff_helper.py <file_path>")
        print("注意: 必须使用 'uv run python' 来执行此脚本")
        sys.exit(1)
        
    file_path = sys.argv[1]
    helper = RuffHelper()
    
    print(f"🔍 检查文件: {file_path}")
    
    # 检查代码质量
    check_result = helper.check_file(file_path)
    print(f"检查结果: {check_result['message']}")
    
    if check_result["issues"]:
        print("\n发现的问题:")
        for issue in check_result["issues"]:
            print(f"  - 行 {issue.get('location', {}).get('row', '?')}: {issue.get('message', '未知问题')}")
    
    # 自动修复
    print("\n🔧 尝试自动修复...")
    fix_result = helper.fix_file(file_path)
    print(f"修复结果: {fix_result['message']}")
    
    # 格式化代码
    print("\n📝 格式化代码...")
    format_result = helper.format_file(file_path)
    print(f"格式化结果: {format_result['message']}")
    
    # 再次检查
    print("\n🔍 再次检查...")
    final_check = helper.check_file(file_path)
    print(f"最终结果: {final_check['message']}")
    
    print("\n💡 提示: 请始终使用 'uv run python' 来运行Python脚本")


if __name__ == "__main__":
    main()
