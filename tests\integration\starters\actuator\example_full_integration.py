#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Mini-Boot Actuator 模块完整端到端集成示例

作者: Python 系统架构师
描述: 展示 Mini-Boot 应用从启动到 Actuator 端点暴露的完整流程
     参考 Spring Boot 的启动模式，实现企业级监控和管理功能

功能特性:
1. 应用上下文初始化和启动流程
2. Actuator 模块自动配置机制
3. Web 框架集成（FastAPI）
4. 端点功能验证（health、info、metrics、beans）
5. 安全和监控模块集成
6. 配置验证和条件化装配

使用方式:
    python example_full_integration.py
"""

import asyncio
import json
import os
import signal
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import FastAPI
from loguru import logger

# Mini-Boot 核心组件导入
from miniboot.context.application import DefaultApplicationContext
from miniboot.context.auto_context import create_application
# 端点组件导入（仅用于类型提示，实际使用时从容器获取）
from miniboot.starters.actuator.endpoints.beans import BeansEndpoint
from miniboot.starters.actuator.endpoints.health import HealthEndpoint
from miniboot.starters.actuator.endpoints.info import InfoEndpoint
from miniboot.starters.actuator.endpoints.metrics import MetricsEndpoint


class MiniBootApplication:
    """Mini-Boot 应用启动类

    模拟 Spring Boot 的 @SpringBootApplication 启动模式，
    提供完整的应用生命周期管理和 Actuator 集成功能。
    """

    def __init__(self, app_name: str = "Mini-Boot Demo Application"):
        """初始化 Mini-Boot 应用

        Args:
            app_name: 应用名称
        """
        self.app_name = app_name
        self.application_context: Optional[DefaultApplicationContext] = None

        # 启动统计信息
        self.startup_stats = {
            "start_time": None,
            "context_init_time": None,
            "total_startup_time": None
        }

        logger.info(f"Initializing {self.app_name}")

    async def run(self) -> None:
        """运行应用 - 主入口方法

        执行完整的应用启动流程：
        1. 初始化应用上下文
        2. 验证端点功能
        3. 清理资源
        """
        start_time = time.time()
        self.startup_stats["start_time"] = start_time

        try:
            logger.info("=" * 80)
            logger.info(f"🌟 启动 {self.app_name}")
            logger.info("=" * 80)

            # 第一阶段：初始化应用上下文
            await self._initialize_application_context()

            # 等待性能监控启动完成
            await asyncio.sleep(1.0)

            # 第二阶段：验证自动配置
            await self._verify_auto_configuration()

            # 第三阶段：验证依赖注入
            await self._verify_dependency_injection()

            # 第四阶段：验证端点功能
            await self._verify_endpoints()

            # 计算总启动时间
            total_time = time.time() - start_time
            self.startup_stats["total_startup_time"] = total_time

            logger.info("=" * 80)
            logger.info(f"[SUCCESS] {self.app_name} 启动完成！")
            logger.info(f"[TIME]  总启动时间: {total_time:.2f}s")
            logger.info("=" * 80)

            # 显示启动摘要
            await self._display_startup_summary()

            # 等待性能监控运行
            logger.info("")
            logger.info("⏳ 等待性能监控运行...")
            await asyncio.sleep(2.0)  # 简单等待2秒让监控运行

            # 测试完成，框架会自动关闭
            logger.info("")
            logger.info("🎯 集成测试完成")
            logger.info("✅ 框架将自动关闭所有组件")

        except Exception as e:
            logger.error(f"[ERROR] 应用启动失败: {e}")
            raise
        finally:
            # 确保清理资源，特别是停止web服务器
            await self._cleanup_resources()


    async def _wait_for_shutdown_old(self) -> None:
        """等待用户手动关闭信号

        使用信号处理器等待 CTRL+C 信号
        """
        logger.info("🔄 开始等待用户关闭信号...")
        logger.info("💡 提示: 按 CTRL+C 退出应用")

        # 创建一个 Future 来等待关闭信号
        shutdown_event = asyncio.Event()

        def signal_handler(signum, frame):
            logger.info("")
            logger.info("� 收到用户中断信号 (CTRL+C)")
            logger.info("[STOP] 开始优雅关闭应用...")
            shutdown_event.set()

        # 注册信号处理器 (在 uvicorn 启动后重新注册)
        logger.info("[CONFIG] 注册信号处理器...")

        # 保存原来的处理器
        original_sigint_handler = signal.signal(signal.SIGINT, signal_handler)
        original_sigterm_handler = signal.signal(signal.SIGTERM, signal_handler)

        logger.info("[OK] 信号处理器注册完成")
        logger.info(f"[INFO] 原 SIGINT 处理器: {original_sigint_handler}")
        logger.info(f"[INFO] 原 SIGTERM 处理器: {original_sigterm_handler}")

        try:
            logger.info("[WAIT] 等待关闭信号中...")
            # 等待关闭信号
            await shutdown_event.wait()
            logger.info("[OK] 收到关闭信号，准备退出")
        except Exception as e:
            logger.error(f"[ERROR] 等待关闭信号时发生错误: {e}")
            raise

    async def _initialize_application_context(self) -> None:
        """第一阶段：初始化应用上下文

        创建和启动 Mini-Boot 应用上下文，包括：
        - Bean 工厂初始化
        - 环境配置加载
        - 组件扫描和注册
        - 依赖注入容器启动
        """
        phase_start = time.time()
        logger.info("[INIT] 第一阶段：初始化应用上下文")

        # 创建应用上下文（只创建一次）
        # 框架会自动加载配置文件，无需手动创建配置
        logger.info("  🔧 创建 DefaultApplicationContext...")
        self.application_context = create_application(
            packages_to_scan=["miniboot.starters.actuator", "miniboot.starters.web"],
            auto_detect=True
        )

        # 启动应用上下文（只启动一次）
        logger.info("  🚀 启动应用上下文...")
        await self.application_context.start()

        # 验证上下文状态
        if self.application_context.is_running():
            logger.info("  ✅ 应用上下文启动成功")
        else:
            raise RuntimeError("应用上下文启动失败")

        phase_time = time.time() - phase_start
        self.startup_stats["context_init_time"] = phase_time
        logger.info(f"  [TIME] 上下文初始化耗时: {phase_time:.2f}s")

    async def _verify_auto_configuration(self) -> None:
        """验证自动配置是否正确加载"""
        logger.info("  [AUTO-CONFIG] 验证自动配置...")

        # 验证关键Bean是否被自动配置创建
        required_beans = [
            "health_endpoint_provider",
            "info_endpoint_provider",
            # "bean_metrics_collector",  # 暂时注释掉，因为BeanMetricsAutoConfiguration没有被加载
            "performance_monitor",
            "actuator_context",
            "endpoint_registry",
            "metrics_registry"
        ]

        for bean_name in required_beans:
            try:
                bean = self.application_context.get_bean(bean_name)
                logger.info(f"    ✅ Auto-configured bean found: {bean_name} ({type(bean).__name__})")
            except Exception as e:
                logger.error(f"    ❌ Auto-configured bean missing: {bean_name}")
                raise RuntimeError(f"Required auto-configured bean '{bean_name}' not found") from e

        logger.info("  [AUTO-CONFIG] 自动配置验证完成")

    async def _verify_dependency_injection(self) -> None:
        """验证Bean依赖注入是否正确"""
        logger.info("  [DI] 验证依赖注入...")

        try:
            # 验证HealthEndpoint的依赖注入
            health_endpoint = self.application_context.get_bean("health_endpoint_provider")
            logger.info(f"    ✅ HealthEndpoint type: {type(health_endpoint).__name__}")

            # 验证InfoEndpoint的依赖注入
            info_endpoint = self.application_context.get_bean("info_endpoint_provider")
            logger.info(f"    ✅ InfoEndpoint type: {type(info_endpoint).__name__}")

            # 验证BeanMetricsCollector的依赖注入
            bean_collector = self.application_context.get_bean("bean_metrics_collector")
            if hasattr(bean_collector, '_monitored_factories') and bean_collector._monitored_factories:
                logger.info("    ✅ BeanMetricsCollector has registered bean factories")
            else:
                logger.warning("    ⚠️ BeanMetricsCollector missing bean factory registrations")

            logger.info("  [DI] 依赖注入验证完成")

        except Exception as e:
            logger.error(f"  [DI] 依赖注入验证失败: {e}")
            raise

    async def _verify_endpoints(self) -> None:
        """第二阶段：验证端点功能

        基于Mini-Boot自动配置机制的端点验证：
        1. 检查Web服务器是否已自动启动（通过WebStarter）
        2. 检查Actuator路由是否已自动注册（通过ActuatorRouteRegistrar）
        3. 发送HTTP请求验证端点响应
        4. 如果Web集成未启用，降级到直接调用验证
        """
        logger.info("🔍 第二阶段：端点功能验证")

        # 检查是否启用了Web集成
        if await self._is_web_integration_enabled():
            logger.info("  [WEB] Web集成已启用，进行HTTP端点验证")
            await self._verify_http_endpoints()
        else:
            logger.info("  [DIRECT] Web集成未启用，进行直接调用验证")
            await self._verify_endpoints_direct()

        logger.info("  [OK] 所有端点功能验证完成")


    async def _is_web_integration_enabled(self) -> bool:
        """检查Web集成是否已启用

        通过检查应用上下文中是否存在ActuatorWebConditions和ActuatorRouteRegistrar Bean
        来判断Web集成是否已自动配置启用
        """
        try:
            # 检查是否存在Web集成条件Bean (注意Bean名称是下划线格式)
            web_conditions = self.application_context.get_bean("actuator_web_conditions")
            if web_conditions and hasattr(web_conditions, 'should_integrate'):
                web_enabled = web_conditions.should_integrate()
                logger.info(f"    [CHECK] Web集成状态: {web_enabled}")
                return web_enabled

            # 如果没有Web集成条件，检查是否有路由注册器
            route_registrar = self.application_context.get_bean("actuator_route_registrar")
            if route_registrar:
                logger.info("    [CHECK] 发现ActuatorRouteRegistrar Bean，Web集成已启用")
                return True

            logger.info("    [CHECK] 未发现Web集成相关Bean，Web集成未启用")
            return False

        except Exception as e:
            logger.debug(f"    [CHECK] Web集成检查异常: {e}")
            return False

    async def _verify_http_endpoints(self) -> None:
        """通过HTTP请求验证端点（假设Web服务器已自动启动）"""
        logger.info("  [HTTP] 开始HTTP端点验证...")

        # 从配置中获取Web服务器地址
        base_url = await self._get_web_server_base_url()

        if not base_url:
            logger.warning("  [HTTP] 无法获取Web服务器地址，降级到直接调用验证")
            await self._verify_endpoints_direct()
            return

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                # 验证健康检查端点
                await self._verify_http_health_endpoint(session, base_url)

                # 验证应用信息端点
                await self._verify_http_info_endpoint(session, base_url)

                # 验证指标收集端点
                await self._verify_http_metrics_endpoint(session, base_url)

                # 验证Bean信息端点
                await self._verify_http_beans_endpoint(session, base_url)

                # 验证线程转储端点
                await self._verify_http_threaddump_endpoint(session, base_url)

                # 验证环境信息端点
                await self._verify_http_env_endpoint(session, base_url)

                # 验证性能监控端点
                await self._verify_http_performance_endpoint(session, base_url)

            logger.info("  [HTTP] HTTP端点验证完成")

        except ImportError:
            logger.warning("  [HTTP] aiohttp不可用，降级到直接调用验证")
            await self._verify_endpoints_direct()
        except Exception as e:
            logger.error(f"  [HTTP] HTTP端点验证失败: {e}")
            raise

    async def _get_web_server_base_url(self) -> Optional[str]:
        """获取Web服务器的基础URL"""
        try:
            # 尝试从应用上下文获取Web应用Bean
            web_app = self.application_context.get_bean("webApplication")
            if web_app and hasattr(web_app, 'properties'):
                host = web_app.properties.host
                port = web_app.properties.port
                # 将0.0.0.0转换为127.0.0.1，因为客户端无法连接到0.0.0.0
                if host == "0.0.0.0":
                    host = "127.0.0.1"
                return f"http://{host}:{port}/actuator"

            # 如果没有Web应用Bean，尝试从配置获取
            from miniboot.env.environment import StandardEnvironment
            env = StandardEnvironment()

            host = env.get_property("miniboot.starters.web.host", "localhost")
            port = env.get_property("miniboot.starters.web.port", 8080)

            return f"http://{host}:{port}/actuator"

        except Exception as e:
            logger.debug(f"    [URL] 获取Web服务器URL失败: {e}")
            return None

    async def _verify_http_health_endpoint(self, session, base_url: str) -> None:
        """验证健康检查端点HTTP响应"""
        logger.info("    [HEALTH] 验证健康检查端点HTTP响应...")

        try:
            url = f"{base_url}/health"
            async with session.get(url, timeout=5) as response:
                assert response.status == 200, f"健康检查端点返回状态码: {response.status}"

                data = await response.json()
                assert "status" in data, "健康检查响应缺少 status 字段"
                assert "timestamp" in data, "健康检查响应缺少 timestamp 字段"

                logger.info(f"      [OK] HTTP状态码: {response.status}")
                logger.info(f"      [OK] 健康状态: {data['status']}")

        except Exception as e:
            logger.error(f"      [ERROR] 健康检查端点HTTP验证失败: {e}")
            raise

    async def _verify_http_info_endpoint(self, session, base_url: str) -> None:
        """验证应用信息端点HTTP响应"""
        logger.info("    [INFO] 验证应用信息端点HTTP响应...")

        try:
            url = f"{base_url}/info"
            async with session.get(url, timeout=5) as response:
                assert response.status == 200, f"应用信息端点返回状态码: {response.status}"

                data = await response.json()
                assert "app" in data, "应用信息响应缺少 app 字段"
                assert "timestamp" in data, "应用信息响应缺少 timestamp 字段"

                logger.info(f"      [OK] HTTP状态码: {response.status}")
                logger.info(f"      [OK] 应用名称: {data['app'].get('name', 'Unknown')}")

        except Exception as e:
            logger.error(f"      [ERROR] 应用信息端点HTTP验证失败: {e}")
            raise

    async def _verify_http_metrics_endpoint(self, session, base_url: str) -> None:
        """验证指标收集端点HTTP响应"""
        logger.info("    [METRICS] 验证指标收集端点HTTP响应...")

        try:
            url = f"{base_url}/metrics"
            async with session.get(url, timeout=5) as response:
                assert response.status == 200, f"指标端点返回状态码: {response.status}"

                data = await response.json()
                assert "timestamp" in data, "指标响应缺少 timestamp 字段"
                assert "metrics" in data, "指标响应缺少 metrics 字段"

                metrics = data["metrics"]
                logger.info(f"      [OK] HTTP状态码: {response.status}")
                logger.info(f"      [OK] 系统指标: {list(metrics.get('system', {}).keys())}")
                logger.info(f"      [OK] 应用指标: {list(metrics.get('application', {}).keys())}")

        except Exception as e:
            logger.error(f"      [ERROR] 指标端点HTTP验证失败: {e}")
            raise

    async def _verify_http_beans_endpoint(self, session, base_url: str) -> None:
        """验证Bean信息端点HTTP响应"""
        logger.info("    [BEANS] 验证Bean信息端点HTTP响应...")

        try:
            url = f"{base_url}/beans"
            async with session.get(url, timeout=5) as response:
                assert response.status == 200, f"Bean信息端点返回状态码: {response.status}"

                data = await response.json()
                assert "timestamp" in data, "Bean信息响应缺少 timestamp 字段"

                logger.info(f"      [OK] HTTP状态码: {response.status}")

                if "contexts" in data:
                    contexts = data["contexts"]
                    if "application" in contexts:
                        app_context = contexts["application"]
                        beans_count = len(app_context.get("beans", {}))
                        logger.info(f"      [OK] Bean 总数: {beans_count}")
                    else:
                        logger.info("      [OK] Bean 信息结构正常（无应用上下文）")
                else:
                    logger.info("      [OK] Bean 信息结构正常（简化模式）")

        except Exception as e:
            logger.error(f"      [ERROR] Bean信息端点HTTP验证失败: {e}")
            raise

    async def _verify_http_threaddump_endpoint(self, session, base_url: str) -> None:
        """验证线程转储端点HTTP响应"""
        logger.info("    [THREADDUMP] 验证线程转储端点HTTP响应...")

        try:
            url = f"{base_url}/threaddump"
            async with session.get(url, timeout=10) as response:
                if response.status != 200:
                    raise RuntimeError(f"线程转储端点返回状态 {response.status}")

                data = await response.json()
                logger.info(f"      [OK] 线程转储端点响应正常，状态码: {response.status}")

                # 验证响应结构
                if isinstance(data, dict):
                    if "threads" in data:
                        threads = data["threads"]
                        logger.info(f"      [OK] 线程信息: {len(threads)} 个线程")
                    else:
                        logger.info("      [OK] 线程转储信息结构正常")
                else:
                    logger.info("      [OK] 线程转储信息结构正常（简化模式）")

        except Exception as e:
            logger.error(f"      [ERROR] 线程转储端点HTTP验证失败: {e}")
            raise

    async def _verify_http_env_endpoint(self, session, base_url: str) -> None:
        """验证环境信息端点HTTP响应"""
        logger.info("    [ENV] 验证环境信息端点HTTP响应...")

        try:
            url = f"{base_url}/env"
            async with session.get(url, timeout=10) as response:
                if response.status != 200:
                    raise RuntimeError(f"环境信息端点返回状态 {response.status}")

                data = await response.json()
                logger.info(f"      [OK] 环境信息端点响应正常，状态码: {response.status}")

                # 验证响应结构
                if isinstance(data, dict):
                    if "environment_variables" in data:
                        env_vars = data["environment_variables"]
                        if isinstance(env_vars, dict) and "variables" in env_vars:
                            variables = env_vars["variables"]
                            logger.info(f"      [OK] 环境变量: {len(variables)} 个变量")
                        else:
                            logger.info("      [OK] 环境变量信息结构正常")
                    else:
                        logger.info("      [OK] 环境信息结构正常")
                else:
                    logger.info("      [OK] 环境信息结构正常（简化模式）")

        except Exception as e:
            logger.error(f"      [ERROR] 环境信息端点HTTP验证失败: {e}")
            raise

    async def _verify_http_performance_endpoint(self, session, base_url: str) -> None:
        """验证性能监控端点HTTP响应"""
        logger.info("    [PERFORMANCE] 验证性能监控端点HTTP响应...")

        # 调试：检查路由注册情况
        logger.info("    [DEBUG] 检查性能端点路由注册情况...")
        try:
            route_registrar = self.application_context.get_bean('actuator_route_registrar')
            if hasattr(route_registrar, '_app') and route_registrar._app:
                app = route_registrar._app
                performance_routes = []
                for route in app.routes:
                    if hasattr(route, 'path') and 'performance' in route.path:
                        performance_routes.append(f"{route.methods} {route.path}")
                logger.info(f"    [DEBUG] 发现性能相关路由: {performance_routes}")

            # 检查端点操作定义
            performance_endpoint = self.application_context.get_bean('performance_endpoint_provider')
            operations = performance_endpoint.operations()
            logger.info(f"    [DEBUG] 性能端点操作数量: {len(operations)}")
            for i, op in enumerate(operations):
                logger.info(f"    [DEBUG] 操作 {i+1}: {op.method} {op.path} -> {op.operation_type}")
        except Exception as e:
            logger.warning(f"    [DEBUG] 调试信息获取失败: {e}")

        try:
            # 验证性能状态概览端点
            url = f"{base_url}/performance"
            async with session.get(url, timeout=10) as response:
                if response.status != 200:
                    raise RuntimeError(f"性能监控端点返回状态 {response.status}")

                data = await response.json()
                logger.info(f"      [OK] 性能监控端点响应正常，状态码: {response.status}")

                # 验证响应结构
                if "status" in data:
                    logger.info(f"      [OK] 性能监控状态: {data.get('status', 'unknown')}")
                if "metrics" in data:
                    logger.info(f"      [OK] 性能指标数量: {len(data.get('metrics', {}))}")
                if "performance_score" in data:
                    logger.info(f"      [OK] 性能分数: {data.get('performance_score', 'N/A')}")

            # 验证性能指标详情端点
            url = f"{base_url}/performance/metrics"
            async with session.get(url, timeout=10) as response:
                if response.status != 200:
                    logger.warning(f"      [WARN] 性能指标详情端点返回状态 {response.status}")
                else:
                    data = await response.json()
                    logger.info(f"      [OK] 性能指标详情端点响应正常")

            # 验证性能快照端点
            url = f"{base_url}/performance/snapshots"
            async with session.get(url, timeout=10) as response:
                if response.status != 200:
                    logger.warning(f"      [WARN] 性能快照端点返回状态 {response.status}")
                else:
                    data = await response.json()
                    logger.info(f"      [OK] 性能快照端点响应正常")

        except Exception as e:
            logger.error(f"      [ERROR] 性能监控端点HTTP验证失败: {e}")
            raise

    async def _verify_endpoints_direct(self) -> None:
        """通过HTTP请求验证端点（真正的集成测试）"""
        logger.info("  [HTTP] 开始HTTP端点验证...")

        # 验证健康检查端点
        await self._verify_health_endpoint_http()

        # 验证应用信息端点
        await self._verify_info_endpoint_http()

        # 验证指标收集端点
        await self._verify_metrics_endpoint_http()

        # 验证Bean信息端点
        await self._verify_beans_endpoint_http()

        # 验证线程转储端点
        await self._verify_threaddump_endpoint_http()

        # 验证环境信息端点
        await self._verify_env_endpoint_http()

        logger.info("  [HTTP] HTTP端点验证完成")



    async def _wait_for_server_ready(self, max_attempts: int = 10) -> bool:
        """等待服务器准备就绪"""
        import aiohttp

        for attempt in range(max_attempts):
            try:
                async with aiohttp.ClientSession() as session:
                    # 尝试连接到服务器
                    async with session.get("http://127.0.0.1:8080/actuator/health",
                                         timeout=aiohttp.ClientTimeout(total=2)) as response:
                        if response.status in [200, 404]:  # 404也表示服务器在运行
                            logger.info(f"    [READY] 服务器已准备就绪 (尝试 {attempt + 1}/{max_attempts})")
                            return True
            except Exception as e:
                logger.debug(f"    [WAIT] 等待服务器启动... (尝试 {attempt + 1}/{max_attempts}): {e}")
                await asyncio.sleep(1.0)

        logger.warning(f"    [TIMEOUT] 服务器在 {max_attempts} 次尝试后仍未准备就绪")
        return False

    async def _verify_health_endpoint_http(self) -> None:
        """通过HTTP请求验证健康检查端点"""
        logger.info("    [HEALTH] HTTP验证健康检查端点...")

        try:
            import aiohttp

            # 等待Web服务器完全启动
            if not await self._wait_for_server_ready():
                raise RuntimeError("Web服务器未能在预期时间内启动")

            async with aiohttp.ClientSession() as session:
                url = "http://127.0.0.1:8080/actuator/health"
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        health_data = await response.json()
                        logger.info(f"      📋 健康检查响应内容: {health_data}")

                        # 验证响应结构
                        assert "status" in health_data, "健康检查响应缺少 status 字段"
                        assert "timestamp" in health_data, "健康检查响应缺少 timestamp 字段"

                        logger.info(f"      ✅ 健康状态: {health_data['status']}")
                        logger.info(f"      ✅ 检查时间: {health_data['timestamp']}")
                    else:
                        raise RuntimeError(f"Health endpoint returned status {response.status}")

        except Exception as e:
            logger.error(f"      ❌ 健康检查端点HTTP验证失败: {e}")
            raise

    async def _verify_info_endpoint_http(self) -> None:
        """通过HTTP请求验证应用信息端点"""
        logger.info("    [INFO] HTTP验证应用信息端点...")

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                url = "http://127.0.0.1:8080/actuator/info"
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        info_data = await response.json()

                        # 验证响应结构
                        assert "app" in info_data, "应用信息响应缺少 app 字段"
                        assert "timestamp" in info_data, "应用信息响应缺少 timestamp 字段"

                        logger.info(f"      ✅ 应用名称: {info_data['app'].get('name', 'Unknown')}")
                        logger.info(f"      ✅ 应用版本: {info_data['app'].get('version', 'Unknown')}")
                    else:
                        raise RuntimeError(f"Info endpoint returned status {response.status}")

        except Exception as e:
            logger.error(f"      ❌ 应用信息端点HTTP验证失败: {e}")
            raise

    async def _verify_metrics_endpoint_http(self) -> None:
        """通过HTTP请求验证指标收集端点"""
        logger.info("    [METRICS] HTTP验证指标收集端点...")

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                url = "http://127.0.0.1:8080/actuator/metrics"
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        metrics_data = await response.json()

                        # 验证响应结构
                        assert "timestamp" in metrics_data, "指标响应缺少 timestamp 字段"
                        assert "metrics" in metrics_data, "指标响应缺少 metrics 字段"

                        metrics = metrics_data["metrics"]
                        logger.info(f"      ✅ 系统指标: {list(metrics.get('system', {}).keys())}")
                        logger.info(f"      ✅ 应用指标: {list(metrics.get('application', {}).keys())}")
                    else:
                        raise RuntimeError(f"Metrics endpoint returned status {response.status}")

        except Exception as e:
            logger.error(f"      ❌ 指标收集端点HTTP验证失败: {e}")
            raise

    async def _verify_beans_endpoint_http(self) -> None:
        """通过HTTP请求验证Bean信息端点"""
        logger.info("    [BEANS] HTTP验证Bean信息端点...")

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                url = "http://127.0.0.1:8080/actuator/beans"
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        beans_data = await response.json()

                        # 验证响应结构
                        assert "timestamp" in beans_data, "Bean 信息响应缺少 timestamp 字段"

                        if "contexts" in beans_data:
                            contexts = beans_data["contexts"]
                            if "application" in contexts:
                                app_context = contexts["application"]
                                beans_count = len(app_context.get("beans", {}))
                                logger.info(f"      ✅ Bean 总数: {beans_count}")
                            else:
                                logger.info("      ✅ Bean 信息结构正常（无应用上下文）")
                        else:
                            logger.info("      ✅ Bean 信息结构正常（简化模式）")
                    else:
                        raise RuntimeError(f"Beans endpoint returned status {response.status}")

        except Exception as e:
            logger.error(f"      ❌ Bean信息端点HTTP验证失败: {e}")
            raise

    async def _verify_threaddump_endpoint_http(self) -> None:
        """通过HTTP请求验证线程转储端点"""
        logger.info("    [THREADDUMP] HTTP验证线程转储端点...")

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.get("http://127.0.0.1:8080/actuator/threaddump", timeout=10) as response:
                    if response.status == 200:
                        threaddump_data = await response.json()
                        logger.info(f"      ✅ 线程转储端点响应正常，状态码: {response.status}")

                        # 验证响应结构
                        if isinstance(threaddump_data, dict):
                            if "threads" in threaddump_data:
                                threads = threaddump_data["threads"]
                                logger.info(f"      ✅ 线程信息: {len(threads)} 个线程")
                            else:
                                logger.info("      ✅ 线程转储信息结构正常")
                        else:
                            logger.info("      ✅ 线程转储信息结构正常（简化模式）")
                    else:
                        raise RuntimeError(f"ThreadDump endpoint returned status {response.status}")

        except Exception as e:
            logger.error(f"      ❌ 线程转储端点HTTP验证失败: {e}")
            raise

    async def _verify_env_endpoint_http(self) -> None:
        """通过HTTP请求验证环境信息端点"""
        logger.info("    [ENV] HTTP验证环境信息端点...")

        try:
            import aiohttp

            async with aiohttp.ClientSession() as session:
                async with session.get("http://127.0.0.1:8080/actuator/env", timeout=10) as response:
                    if response.status == 200:
                        env_data = await response.json()
                        logger.info(f"      ✅ 环境信息端点响应正常，状态码: {response.status}")

                        # 验证响应结构
                        if isinstance(env_data, dict):
                            if "environment_variables" in env_data:
                                env_vars = env_data["environment_variables"]
                                if isinstance(env_vars, dict) and "variables" in env_vars:
                                    variables = env_vars["variables"]
                                    logger.info(f"      ✅ 环境变量: {len(variables)} 个变量")
                                else:
                                    logger.info("      ✅ 环境变量信息结构正常")
                            else:
                                logger.info("      ✅ 环境信息结构正常")
                        else:
                            logger.info("      ✅ 环境信息结构正常（简化模式）")
                    else:
                        raise RuntimeError(f"Env endpoint returned status {response.status}")

        except Exception as e:
            logger.error(f"      ❌ 环境信息端点HTTP验证失败: {e}")
            raise

    async def _display_startup_summary(self) -> None:
        """显示启动摘要信息"""
        logger.info("")
        logger.info("[INFO] 启动摘要信息")
        logger.info("-" * 60)

        # 应用信息
        logger.info(f"🏷️  应用名称: {self.app_name}")
        logger.info(f"🔧 应用上下文: {type(self.application_context).__name__}")
        # 检查Web框架可用性
        try:
            import fastapi
            web_framework = "FastAPI"
        except ImportError:
            web_framework = "未集成"
        logger.info(f"🌐 Web 框架: {web_framework}")

        # 性能统计
        logger.info("[TIME]  性能统计:")
        stats = self.startup_stats
        if stats["context_init_time"]:
            logger.info(f"   [PACKAGE] 上下文初始化: {stats['context_init_time']:.2f}s")
        if stats["total_startup_time"]:
            logger.info(f"   [START] 总启动时间: {stats['total_startup_time']:.2f}s")

        logger.info("-" * 60)
        logger.info("[SUCCESS] Mini-Boot 应用启动完成，Actuator 监控功能已就绪！")

    async def _cleanup_resources(self) -> None:
        """清理应用资源

        只需要停止应用上下文，框架会自动管理所有组件：
        - 性能监控器会自动停止
        - Web服务器会自动关闭
        - 所有Bean的@PreDestroy方法会自动调用
        - 事件系统会自动清理
        """
        try:
            logger.info("🧹 正在清理应用资源...")

            if hasattr(self, 'application_context') and self.application_context:
                logger.info("[STOP] 停止应用上下文...")
                try:
                    # ✅ 只需要这一行，框架会自动处理一切
                    await self.application_context.stop()
                    logger.info("✅ 应用上下文已停止")
                except Exception as e:
                    logger.error(f"❌ 停止应用上下文失败: {e}")
            else:
                logger.info("⚠️ 应用上下文不存在，跳过停止操作")

            logger.info("✅ 资源清理完成")

        except Exception as e:
            logger.warning(f"⚠️ 资源清理时出现问题: {e}")
            # 不抛出异常，避免影响主流程

    async def _wait_for_shutdown(self) -> None:
        """等待用户手动关闭信号

        简单地等待，让 KeyboardInterrupt 异常来处理关闭
        """
        logger.info("🔄 开始等待用户关闭信号...")
        logger.info("💡 提示: 按 CTRL+C 退出应用")

        try:
            logger.info("[WAIT] 等待关闭信号中...")
            # 简单的无限等待，让 KeyboardInterrupt 异常来处理关闭
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("")
            logger.info("[INTERRUPT] 收到用户中断信号 (CTRL+C)")
            logger.info("[STOP] 开始优雅关闭应用...")
        except Exception as e:
            logger.error(f"[ERROR] 等待关闭信号时发生错误: {e}")
            raise


async def main():
    """主函数 - 演示完整的 Mini-Boot 应用启动流程"""
    try:
        # 创建并运行 Mini-Boot 应用
        app = MiniBootApplication("Mini-Boot Actuator 集成演示")
        await app.run()

    except asyncio.CancelledError:
        # 优雅处理应用关闭时的取消错误
        logger.debug("🔄 应用关闭过程中的异步任务被取消（正常现象）")
        # 不重新抛出，避免传播到 asyncio.run()

    except Exception as e:
        logger.error(f"[ERROR] 应用运行失败: {e}")
        raise


if __name__ == "__main__":
    """程序入口点"""
    print("开始启动 Mini-Boot 应用...")
    print("[CONFIG] 配置文件: resources/application.yml")
    print("[SCHEDULE] 预期收集间隔: 2秒")
    logger.info("[START] 启动 Mini-Boot Actuator 完整集成演示")
    asyncio.run(main())
