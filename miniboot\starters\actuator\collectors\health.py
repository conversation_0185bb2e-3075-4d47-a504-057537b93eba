#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 异步健康指标收集器 - 高性能异步实现

提供高性能的异步健康指标收集,支持并发执行和智能缓存.

核心特性:
- 异步健康检查
- 多维度健康指标
- 自定义健康检查器
- 健康状态聚合
- 智能缓存和性能优化
"""

import asyncio
import time
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from loguru import logger

from miniboot.utils import timeout


class HealthStatus(Enum):
    """健康状态枚举"""

    UP = "UP"
    DOWN = "DOWN"
    OUT_OF_SERVICE = "OUT_OF_SERVICE"
    UNKNOWN = "UNKNOWN"


class AsyncHealthIndicator:
    """异步健康指标基类"""

    def __init__(self, name: str, timeout: float = 3.0):
        """初始化异步健康指标

        Args:
            name: 健康指标名称
            timeout: 检查超时时间(秒)
        """
        self.name = name
        self.timeout = timeout

    async def check_health(self) -> Dict[str, Any]:
        """健康检查 - 子类需要实现"""
        raise NotImplementedError("Subclasses must implement check_health")


class AsyncDiskSpaceHealthIndicator(AsyncHealthIndicator):
    """异步磁盘空间健康指标"""

    def __init__(self, name: str = "diskSpace", threshold: float = 0.9, timeout: float = 3.0):
        """初始化磁盘空间健康指标

        Args:
            name: 指标名称
            threshold: 磁盘使用率阈值(0-1)
            timeout: 检查超时时间
        """
        super().__init__(name, timeout)
        self.threshold = threshold

    @timeout(3.0)
    async def check_health(self) -> Dict[str, Any]:
        """检查磁盘空间健康"""
        try:
            import psutil

            loop = asyncio.get_event_loop()
            disk_usage = await loop.run_in_executor(None, self._get_disk_usage)

            # 检查磁盘使用率
            max_usage = max(partition["usage_percent"] for partition in disk_usage) / 100
            status = HealthStatus.UP if max_usage < self.threshold else HealthStatus.DOWN

            return {"status": status.value, "details": {"threshold": self.threshold, "max_usage": max_usage, "partitions": disk_usage}}

        except Exception as e:
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e)}}

    def _get_disk_usage(self) -> List[Dict[str, Any]]:
        """获取磁盘使用情况"""
        import psutil

        partitions = psutil.disk_partitions()
        disk_usage = []

        for partition in partitions:
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage.append(
                    {
                        "device": partition.device,
                        "mountpoint": partition.mountpoint,
                        "total": usage.total,
                        "used": usage.used,
                        "free": usage.free,
                        "usage_percent": (usage.used / usage.total * 100) if usage.total > 0 else 0,
                    }
                )
            except PermissionError:
                continue

        return disk_usage


class AsyncMemoryHealthIndicator(AsyncHealthIndicator):
    """异步内存健康指标"""

    def __init__(self, name: str = "memory", threshold: float = 0.9, timeout: float = 3.0):
        """初始化内存健康指标

        Args:
            name: 指标名称
            threshold: 内存使用率阈值(0-1)
            timeout: 检查超时时间
        """
        super().__init__(name, timeout)
        self.threshold = threshold

    @timeout(3.0)
    async def check_health(self) -> Dict[str, Any]:
        """检查内存健康"""
        try:
            import psutil

            loop = asyncio.get_event_loop()
            memory_info = await loop.run_in_executor(None, psutil.virtual_memory)

            usage_percent = memory_info.percent / 100
            status = HealthStatus.UP if usage_percent < self.threshold else HealthStatus.DOWN

            return {
                "status": status.value,
                "details": {
                    "threshold": self.threshold,
                    "usage_percent": usage_percent,
                    "total": memory_info.total,
                    "used": memory_info.used,
                    "available": memory_info.available,
                },
            }

        except Exception as e:
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e)}}


class AsyncDatabaseHealthIndicator(AsyncHealthIndicator):
    """异步数据库健康指标"""

    def __init__(self, name: str = "database", connection_check: Optional[Callable] = None, timeout: float = 5.0):
        """初始化数据库健康指标

        Args:
            name: 指标名称
            connection_check: 数据库连接检查函数
            timeout: 检查超时时间
        """
        super().__init__(name, timeout)
        self.connection_check = connection_check

    @timeout(5.0)
    async def check_health(self) -> Dict[str, Any]:
        """检查数据库健康"""
        try:
            if self.connection_check:
                # 使用自定义连接检查
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, self.connection_check)

                if result:
                    return {"status": HealthStatus.UP.value, "details": {"connection": "successful", "check_time": datetime.now().isoformat()}}
                else:
                    return {"status": HealthStatus.DOWN.value, "details": {"connection": "failed", "check_time": datetime.now().isoformat()}}
            else:
                # 默认返回未知状态
                return {"status": HealthStatus.UNKNOWN.value, "details": {"message": "No connection check configured"}}

        except Exception as e:
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e), "check_time": datetime.now().isoformat()}}


class AsyncCustomHealthIndicator(AsyncHealthIndicator):
    """异步自定义健康指标"""

    def __init__(self, name: str, check_function: Callable, timeout: float = 3.0):
        """初始化自定义健康指标

        Args:
            name: 指标名称
            check_function: 自定义检查函数
            timeout: 检查超时时间
        """
        super().__init__(name, timeout)
        self.check_function = check_function

    async def check_health(self) -> Dict[str, Any]:
        """执行自定义健康检查"""
        try:
            loop = asyncio.get_event_loop()

            # 在线程池中执行自定义检查函数
            result = await asyncio.wait_for(loop.run_in_executor(None, self.check_function), timeout=self.timeout)

            # 如果返回字典,直接使用;否则根据布尔值构造结果
            if isinstance(result, dict):
                return result
            else:
                status = HealthStatus.UP if result else HealthStatus.DOWN
                return {"status": status.value, "details": {"check_result": result, "check_time": datetime.now().isoformat()}}

        except asyncio.TimeoutError:
            return {
                "status": HealthStatus.DOWN.value,
                "details": {"error": f"Health check timeout after {self.timeout}s", "check_time": datetime.now().isoformat()},
            }
        except Exception as e:
            return {"status": HealthStatus.DOWN.value, "details": {"error": str(e), "check_time": datetime.now().isoformat()}}


class AsyncHealthCollector:
    """异步健康收集器

    管理多个健康指标并提供聚合的健康状态.
    """

    def __init__(self, timeout: float = 10.0):
        """初始化异步健康收集器

        Args:
            timeout: 总体检查超时时间(秒)
        """
        self.timeout = timeout
        self.indicators: Dict[str, AsyncHealthIndicator] = {}
        self._start_time = time.time()

    def register_indicator(self, indicator: AsyncHealthIndicator) -> None:
        """注册健康指标"""
        self.indicators[indicator.name] = indicator
        logger.info(f"Registered health indicator: {indicator.name}")

    def unregister_indicator(self, name: str) -> bool:
        """注销健康指标"""
        if name in self.indicators:
            del self.indicators[name]
            logger.info(f"Unregistered health indicator: {name}")
            return True
        return False

    @timeout(10.0)
    async def collect_health(self, detailed: bool = False) -> Dict[str, Any]:
        """收集所有健康指标

        Args:
            detailed: 是否返回详细信息,包含各个指标的详细状态
        """
        try:
            if not self.indicators:
                result = {
                    "status": HealthStatus.UNKNOWN.value,
                    "timestamp": datetime.now().isoformat(),
                    "details": {"message": "No health indicators registered"},
                }
                # 如果是详细模式,添加 checks 键
                if detailed:
                    result["checks"] = {}
                return result

            # 并发执行所有健康检查
            tasks = []
            indicator_names = []

            for name, indicator in self.indicators.items():
                task = indicator.check_health()
                tasks.append(task)
                indicator_names.append(name)

            # 等待所有检查完成
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            health_details = {}
            overall_status = HealthStatus.UP

            for i, result in enumerate(results):
                name = indicator_names[i]

                if isinstance(result, Exception):
                    health_details[name] = {"status": HealthStatus.DOWN.value, "details": {"error": str(result)}}
                    overall_status = HealthStatus.DOWN
                else:
                    health_details[name] = result

                    # 更新总体状态
                    if result.get("status") == HealthStatus.DOWN.value:
                        overall_status = HealthStatus.DOWN
                    elif result.get("status") == HealthStatus.OUT_OF_SERVICE.value:
                        if overall_status != HealthStatus.DOWN:
                            overall_status = HealthStatus.OUT_OF_SERVICE

            result = {"status": overall_status.value, "timestamp": datetime.now().isoformat()}

            # 根据 detailed 参数决定是否包含详细信息
            if detailed:
                result["details"] = health_details
                result["checks"] = health_details  # 添加 checks 键以兼容测试
                result["summary"] = {
                    "total_indicators": len(self.indicators),
                    "up_count": len([d for d in health_details.values() if d.get("status") == HealthStatus.UP.value]),
                    "down_count": len([d for d in health_details.values() if d.get("status") == HealthStatus.DOWN.value]),
                    "collection_time_ms": round((time.time() - time.time()) * 1000, 2),
                }

            return result

        except Exception as e:
            logger.error(f"Health collection failed: {e}")
            return {"status": HealthStatus.DOWN.value, "timestamp": datetime.now().isoformat(), "details": {"error": str(e)}}

    def get_indicator_names(self) -> List[str]:
        """获取所有注册的健康指标名称"""
        return list(self.indicators.keys())

    def get_indicator(self, name: str) -> Optional[AsyncHealthIndicator]:
        """获取指定的健康指标"""
        return self.indicators.get(name)

    def create_default_indicators(self) -> None:
        """创建默认的健康指标"""
        # 磁盘空间检查
        disk_indicator = AsyncDiskSpaceHealthIndicator()
        self.register_indicator(disk_indicator)

        # 内存检查
        memory_indicator = AsyncMemoryHealthIndicator()
        self.register_indicator(memory_indicator)

        logger.info("Created default health indicators: diskSpace, memory")
