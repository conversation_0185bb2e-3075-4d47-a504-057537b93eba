#!/usr/bin/env python
# encoding: utf-8
"""
异步环境检测策略模块

使用策略模式优化复杂的异步环境检测逻辑.
"""

from abc import ABC, abstractmethod
from typing import Optional
from loguru import logger


class AsyncDetectionStrategy(ABC):
    """异步检测策略接口"""

    @abstractmethod
    def detect(self) -> Optional[bool]:
        """检测异步环境

        Returns:
            Optional[bool]: True表示异步环境,False表示同步环境,None表示无法确定
        """
        pass

    @abstractmethod
    def get_priority(self) -> int:
        """获取检测优先级

        Returns:
            优先级数字,越小优先级越高
        """
        pass

    @abstractmethod
    def get_name(self) -> str:
        """获取策略名称

        Returns:
            策略名称
        """
        pass


class EventLoopDetectionStrategy(AsyncDetectionStrategy):
    """事件循环检测策略"""

    def detect(self) -> Optional[bool]:
        """通过检测事件循环来判断异步环境"""
        try:
            import asyncio

            # 检查是否有运行中的事件循环
            try:
                loop = asyncio.get_running_loop()
                if loop and loop.is_running():
                    logger.debug("Detected running event loop")
                    return True
            except RuntimeError:
                # 没有运行中的事件循环
                pass

            # 检查是否有默认事件循环
            try:
                loop = asyncio.get_event_loop()
                if loop and not loop.is_closed():
                    logger.debug("Detected event loop (not running)")
                    return False  # 有循环但未运行,可能是同步环境
            except RuntimeError:
                pass

            return None  # 无法确定

        except ImportError:
            logger.debug("asyncio not available")
            return False
        except Exception as e:
            logger.warning(f"Error in event loop detection: {e}")
            return None

    def get_priority(self) -> int:
        return 100  # 高优先级

    def get_name(self) -> str:
        return "EventLoop"


class FrameworkDetectionStrategy(AsyncDetectionStrategy):
    """框架检测策略"""

    def detect(self) -> Optional[bool]:
        """通过检测异步框架来判断环境"""
        try:
            # 检测 FastAPI
            try:
                import fastapi

                logger.debug("Detected FastAPI framework")
                return True
            except ImportError:
                pass

            # 检测 Starlette
            try:
                import starlette

                logger.debug("Detected Starlette framework")
                return True
            except ImportError:
                pass

            # 检测 aiohttp
            try:
                import aiohttp

                logger.debug("Detected aiohttp framework")
                return True
            except ImportError:
                pass

            # 检测 Tornado
            try:
                import tornado

                logger.debug("Detected Tornado framework")
                return True
            except ImportError:
                pass

            # 检测同步框架
            try:
                import flask

                logger.debug("Detected Flask framework (sync)")
                return False
            except ImportError:
                pass

            try:
                import django

                logger.debug("Detected Django framework (sync)")
                return False
            except ImportError:
                pass

            return None  # 无法确定

        except Exception as e:
            logger.warning(f"Error in framework detection: {e}")
            return None

    def get_priority(self) -> int:
        return 200  # 中等优先级

    def get_name(self) -> str:
        return "Framework"


class EnvironmentDetectionStrategy(AsyncDetectionStrategy):
    """环境变量检测策略"""

    def detect(self) -> Optional[bool]:
        """通过环境变量来判断异步环境"""
        try:
            import os

            # 检查明确的异步环境变量
            async_env = os.getenv("MINIBOOT_ASYNC_MODE")
            if async_env is not None:
                result = async_env.lower() in ("true", "1", "yes", "on")
                logger.debug(f"Environment variable MINIBOOT_ASYNC_MODE: {result}")
                return result

            # 检查其他相关环境变量
            if os.getenv("UVICORN_HOST") or os.getenv("UVICORN_PORT"):
                logger.debug("Detected Uvicorn environment variables")
                return True

            if os.getenv("GUNICORN_CMD_ARGS"):
                logger.debug("Detected Gunicorn environment variables")
                return False

            return None  # 无法确定

        except Exception as e:
            logger.warning(f"Error in environment detection: {e}")
            return None

    def get_priority(self) -> int:
        return 50  # 最高优先级(明确的用户配置)

    def get_name(self) -> str:
        return "Environment"


class ThreadDetectionStrategy(AsyncDetectionStrategy):
    """线程检测策略"""

    def detect(self) -> Optional[bool]:
        """通过线程信息来判断异步环境"""
        try:
            import threading

            # 检查线程名称
            current_thread = threading.current_thread()
            thread_name = current_thread.name.lower()

            if "asyncio" in thread_name or "event" in thread_name:
                logger.debug(f"Detected async thread name: {thread_name}")
                return True

            if "main" in thread_name and threading.active_count() == 1:
                logger.debug("Single main thread detected")
                return None  # 可能是同步环境,但不确定

            return None  # 无法确定

        except Exception as e:
            logger.warning(f"Error in thread detection: {e}")
            return None

    def get_priority(self) -> int:
        return 300  # 低优先级

    def get_name(self) -> str:
        return "Thread"


class DefaultDetectionStrategy(AsyncDetectionStrategy):
    """默认检测策略"""

    def detect(self) -> Optional[bool]:
        """默认返回同步环境"""
        logger.debug("Using default detection strategy")
        return False  # 默认为同步环境

    def get_priority(self) -> int:
        return 1000  # 最低优先级

    def get_name(self) -> str:
        return "Default"


class AsyncDetectionChain:
    """异步检测责任链"""

    def __init__(self):
        """初始化检测链"""
        self._strategies = []
        self._register_default_strategies()

    def _register_default_strategies(self) -> None:
        """注册默认策略"""
        self.register_strategy(EnvironmentDetectionStrategy())
        self.register_strategy(EventLoopDetectionStrategy())
        self.register_strategy(FrameworkDetectionStrategy())
        self.register_strategy(ThreadDetectionStrategy())
        self.register_strategy(DefaultDetectionStrategy())

    def register_strategy(self, strategy: AsyncDetectionStrategy) -> None:
        """注册策略

        Args:
            strategy: 异步检测策略
        """
        self._strategies.append(strategy)
        # 按优先级排序
        self._strategies.sort(key=lambda s: s.get_priority())
        logger.debug(f"Registered async detection strategy: {strategy.get_name()}")

    def detect_async_environment(self) -> bool:
        """检测异步环境

        Returns:
            是否为异步环境
        """
        logger.debug("Starting async environment detection")

        # 按优先级顺序尝试各种检测策略
        for strategy in self._strategies:
            try:
                result = strategy.detect()
                if result is not None:
                    logger.info(f"Async environment detected by {strategy.get_name()}: {result}")
                    return result
                else:
                    logger.debug(f"Strategy {strategy.get_name()} returned inconclusive result")
            except Exception as e:
                logger.warning(f"Strategy {strategy.get_name()} failed: {e}")
                continue

        # 如果所有策略都无法确定,默认返回False
        logger.info("No conclusive async detection, defaulting to sync environment")
        return False
