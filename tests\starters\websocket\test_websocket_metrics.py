#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 指标收集器测试
"""

import json
import unittest

from miniboot.starters.websocket.metrics import ConnectionMetrics, MessageMetrics, WebSocketMetrics


class WebSocketMetricsTestCase(unittest.TestCase):
    """WebSocket 指标收集器测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.metrics = WebSocketMetrics()

    def test_connection_metrics_initialization(self):
        """测试连接指标初始化"""
        conn_metrics = self.metrics.get_connection_metrics()

        self.assertEqual(conn_metrics["total_connections"], 0)
        self.assertEqual(conn_metrics["active_connections"], 0)
        self.assertEqual(conn_metrics["failed_connections"], 0)
        self.assertEqual(conn_metrics["peak_connections"], 0)
        self.assertEqual(conn_metrics["connection_attempts"], 0)
        self.assertEqual(conn_metrics["successful_connections"], 0)
        self.assertEqual(conn_metrics["disconnections"], 0)
        self.assertEqual(conn_metrics["success_rate"], 0.0)
        self.assertEqual(conn_metrics["average_duration_seconds"], 0.0)
        self.assertEqual(conn_metrics["unique_users"], 0)

    def test_connection_attempt_recording(self):
        """测试连接尝试记录"""
        session_id = "test_session_1"
        user_id = "test_user_1"

        self.metrics.record_connection_attempt(session_id, user_id)

        conn_metrics = self.metrics.get_connection_metrics()
        self.assertEqual(conn_metrics["connection_attempts"], 1)
        self.assertEqual(conn_metrics["unique_users"], 1)

    def test_connection_success_recording(self):
        """测试连接成功记录"""
        session_id = "test_session_1"

        self.metrics.record_connection_attempt(session_id)
        self.metrics.record_connection_success(session_id)

        conn_metrics = self.metrics.get_connection_metrics()
        self.assertEqual(conn_metrics["successful_connections"], 1)
        self.assertEqual(conn_metrics["active_connections"], 1)
        self.assertEqual(conn_metrics["total_connections"], 1)
        self.assertEqual(conn_metrics["peak_connections"], 1)
        self.assertEqual(conn_metrics["success_rate"], 1.0)

    def test_connection_failure_recording(self):
        """测试连接失败记录"""
        session_id = "test_session_1"
        error = Exception("Connection failed")

        self.metrics.record_connection_attempt(session_id)
        self.metrics.record_connection_failure(session_id, error)

        conn_metrics = self.metrics.get_connection_metrics()
        self.assertEqual(conn_metrics["failed_connections"], 1)
        self.assertEqual(conn_metrics["success_rate"], 0.0)

        # 检查错误记录
        recent_errors = self.metrics.get_recent_errors(limit=1)
        self.assertEqual(len(recent_errors), 1)
        self.assertEqual(recent_errors[0]["type"], "connection_failure")

    def test_disconnection_recording(self):
        """测试断开连接记录"""
        import time

        session_id = "test_session_1"
        user_id = "test_user_1"

        # 先建立连接
        self.metrics.record_connection_attempt(session_id, user_id)
        self.metrics.record_connection_success(session_id)

        # 等待一小段时间确保持续时间大于0
        time.sleep(0.001)

        # 然后断开连接
        self.metrics.record_disconnection(session_id, user_id)

        conn_metrics = self.metrics.get_connection_metrics()
        self.assertEqual(conn_metrics["active_connections"], 0)
        self.assertEqual(conn_metrics["disconnections"], 1)
        self.assertEqual(conn_metrics["unique_users"], 0)

        # 检查连接持续时间记录
        self.assertEqual(len(self.metrics.connection_metrics.connection_durations), 1)
        self.assertGreaterEqual(self.metrics.connection_metrics.connection_durations[0], 0)

    def test_message_metrics_initialization(self):
        """测试消息指标初始化"""
        msg_metrics = self.metrics.get_message_metrics()

        self.assertEqual(msg_metrics["total_messages_sent"], 0)
        self.assertEqual(msg_metrics["total_messages_received"], 0)
        self.assertEqual(msg_metrics["total_bytes_sent"], 0)
        self.assertEqual(msg_metrics["total_bytes_received"], 0)
        self.assertEqual(msg_metrics["failed_messages"], 0)
        self.assertEqual(msg_metrics["message_failure_rate"], 0.0)
        self.assertEqual(msg_metrics["average_processing_time_ms"], 0.0)
        self.assertEqual(len(msg_metrics["messages_by_type"]), 0)

    def test_message_sent_recording(self):
        """测试发送消息记录"""
        session_id = "test_session_1"
        message_type = "text"
        size_bytes = 100
        processing_time_ms = 5.5

        self.metrics.record_message_sent(session_id, message_type, size_bytes, processing_time_ms)

        msg_metrics = self.metrics.get_message_metrics()
        self.assertEqual(msg_metrics["total_messages_sent"], 1)
        self.assertEqual(msg_metrics["total_bytes_sent"], size_bytes)
        self.assertEqual(msg_metrics["messages_by_type"][message_type], 1)
        self.assertEqual(msg_metrics["average_processing_time_ms"], processing_time_ms)

    def test_message_received_recording(self):
        """测试接收消息记录"""
        session_id = "test_session_1"
        message_type = "json"
        size_bytes = 200
        processing_time_ms = 3.2

        self.metrics.record_message_received(session_id, message_type, size_bytes, processing_time_ms)

        msg_metrics = self.metrics.get_message_metrics()
        self.assertEqual(msg_metrics["total_messages_received"], 1)
        self.assertEqual(msg_metrics["total_bytes_received"], size_bytes)
        self.assertEqual(msg_metrics["messages_by_type"][f"received_{message_type}"], 1)
        self.assertEqual(msg_metrics["average_processing_time_ms"], processing_time_ms)

    def test_message_failure_recording(self):
        """测试消息失败记录"""
        session_id = "test_session_1"
        message_type = "binary"
        error = Exception("Message processing failed")

        self.metrics.record_message_failure(session_id, message_type, error)

        msg_metrics = self.metrics.get_message_metrics()
        self.assertEqual(msg_metrics["failed_messages"], 1)

        # 检查错误记录
        recent_errors = self.metrics.get_recent_errors(limit=1)
        self.assertEqual(len(recent_errors), 1)
        self.assertEqual(recent_errors[0]["type"], f"message_failure_{message_type}")

    def test_performance_metrics(self):
        """测试性能指标"""
        # 记录响应时间
        response_times = [10.5, 15.2, 8.7, 12.1, 20.3]
        for rt in response_times:
            self.metrics.record_response_time(rt)

        perf_metrics = self.metrics.get_performance_metrics()
        self.assertEqual(perf_metrics["average_response_time_ms"], sum(response_times) / len(response_times))

        # 检查 P95 响应时间
        sorted_times = sorted(response_times)
        expected_p95 = sorted_times[int(len(sorted_times) * 0.95)]
        self.assertEqual(perf_metrics["p95_response_time_ms"], expected_p95)

    def test_error_recording(self):
        """测试错误记录"""
        error_type = "test_error"
        error_message = "This is a test error"

        self.metrics.record_error(error_type, error_message)

        perf_metrics = self.metrics.get_performance_metrics()
        self.assertEqual(perf_metrics["error_counts"][error_type], 1)
        self.assertEqual(perf_metrics["total_errors"], 1)

        # 检查错误历史
        recent_errors = self.metrics.get_recent_errors(limit=1)
        self.assertEqual(len(recent_errors), 1)
        self.assertEqual(recent_errors[0]["type"], error_type)
        self.assertEqual(recent_errors[0]["message"], error_message)

    def test_all_metrics_aggregation(self):
        """测试所有指标聚合"""
        # 模拟一些活动
        session_id = "test_session_1"
        user_id = "test_user_1"

        # 连接活动
        self.metrics.record_connection_attempt(session_id, user_id)
        self.metrics.record_connection_success(session_id)

        # 消息活动
        self.metrics.record_message_sent(session_id, "text", 100, 5.0)
        self.metrics.record_message_received(session_id, "json", 150, 3.0)

        # 性能活动
        self.metrics.record_response_time(12.5)

        # 获取所有指标
        all_metrics = self.metrics.get_all_metrics()

        # 验证结构
        self.assertIn("timestamp", all_metrics)
        self.assertIn("uptime_seconds", all_metrics)
        self.assertIn("connection_metrics", all_metrics)
        self.assertIn("message_metrics", all_metrics)
        self.assertIn("performance_metrics", all_metrics)
        self.assertIn("session_metrics", all_metrics)

        # 验证数据
        self.assertEqual(all_metrics["connection_metrics"]["active_connections"], 1)
        self.assertEqual(all_metrics["message_metrics"]["total_messages_sent"], 1)
        self.assertEqual(all_metrics["message_metrics"]["total_messages_received"], 1)
        self.assertEqual(all_metrics["performance_metrics"]["average_response_time_ms"], 12.5)
        self.assertEqual(all_metrics["session_metrics"]["active_sessions"], 1)

    def test_metrics_export_json(self):
        """测试 JSON 格式导出"""
        # 添加一些数据
        self.metrics.record_connection_attempt("session_1")
        self.metrics.record_connection_success("session_1")

        # 导出 JSON
        json_export = self.metrics.export_metrics("json")

        # 验证是有效的 JSON
        parsed_data = json.loads(json_export)
        self.assertIn("connection_metrics", parsed_data)
        self.assertEqual(parsed_data["connection_metrics"]["active_connections"], 1)

    def test_metrics_export_prometheus(self):
        """测试 Prometheus 格式导出"""
        # 添加一些数据
        self.metrics.record_connection_attempt("session_1")
        self.metrics.record_connection_success("session_1")
        self.metrics.record_message_sent("session_1", "text", 100)

        # 导出 Prometheus 格式
        prometheus_export = self.metrics.export_metrics("prometheus")

        # 验证包含预期的指标
        self.assertIn("websocket_connections_total 1", prometheus_export)
        self.assertIn("websocket_connections_active 1", prometheus_export)
        self.assertIn("websocket_messages_sent_total 1", prometheus_export)
        self.assertIn("# HELP", prometheus_export)
        self.assertIn("# TYPE", prometheus_export)

    def test_metrics_reset(self):
        """测试指标重置"""
        # 添加一些数据
        self.metrics.record_connection_attempt("session_1")
        self.metrics.record_connection_success("session_1")
        self.metrics.record_message_sent("session_1", "text", 100)
        self.metrics.record_error("test_error", "test message")

        # 验证数据存在
        all_metrics = self.metrics.get_all_metrics()
        self.assertEqual(all_metrics["connection_metrics"]["active_connections"], 1)
        self.assertEqual(all_metrics["message_metrics"]["total_messages_sent"], 1)

        # 重置指标
        self.metrics.reset_metrics()

        # 验证数据已清空
        all_metrics = self.metrics.get_all_metrics()
        self.assertEqual(all_metrics["connection_metrics"]["active_connections"], 0)
        self.assertEqual(all_metrics["message_metrics"]["total_messages_sent"], 0)
        self.assertEqual(len(self.metrics.get_recent_errors()), 0)

    def test_connection_metrics_properties(self):
        """测试连接指标属性计算"""
        conn_metrics = ConnectionMetrics()

        # 测试成功率计算
        self.assertEqual(conn_metrics.success_rate, 0.0)

        conn_metrics.connection_attempts = 10
        conn_metrics.successful_connections = 8
        self.assertEqual(conn_metrics.success_rate, 0.8)

        # 测试平均持续时间计算
        self.assertEqual(conn_metrics.average_duration, 0.0)

        conn_metrics.connection_durations = [10.5, 15.2, 8.7]
        expected_avg = sum(conn_metrics.connection_durations) / len(conn_metrics.connection_durations)
        self.assertEqual(conn_metrics.average_duration, expected_avg)

    def test_message_metrics_properties(self):
        """测试消息指标属性计算"""
        msg_metrics = MessageMetrics()

        # 测试失败率计算
        self.assertEqual(msg_metrics.message_failure_rate, 0.0)

        msg_metrics.total_messages_sent = 50
        msg_metrics.total_messages_received = 30
        msg_metrics.failed_messages = 8
        expected_failure_rate = 8 / (50 + 30)
        self.assertEqual(msg_metrics.message_failure_rate, expected_failure_rate)

        # 测试平均处理时间计算
        self.assertEqual(msg_metrics.average_processing_time, 0.0)

        msg_metrics.message_processing_times = [5.5, 3.2, 7.8, 4.1]
        expected_avg = sum(msg_metrics.message_processing_times) / len(msg_metrics.message_processing_times)
        self.assertEqual(msg_metrics.average_processing_time, expected_avg)


if __name__ == "__main__":
    unittest.main()
