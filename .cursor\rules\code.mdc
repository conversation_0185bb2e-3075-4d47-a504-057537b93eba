---
description:
globs: .py
alwaysApply: false
---
# Mini-Boot Python 编码规范

## 概述

为确保 Mini-Boot 项目代码质量和一致性，制定以下编码规范。所有代码必须严格遵循这些规范，确保无编译告警。

## 代码格式规范

### 1. 文件头格式

所有 Python 文件必须使用统一的头文件格式：

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块功能描述
"""
```

**格式说明**：

-   **第一行**：`#!/usr/bin/env python` - Python 解释器声明
-   **第二行**：`# encoding: utf-8` - 文件编码声明
-   **第三行**：空行
-   **文档字符串**：使用三重双引号，包含作者和描述信息
    -   `@author`: 作者信息，统一使用 "cz"
    -   `@description`: 模块功能的简要描述

**示例**：

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 环境配置模块 - 提供环境变量管理和配置文件加载功能
"""

import os
from typing import Optional

class Environment:
    """环境管理类"""
    pass
```

### 2. 缩进和空白

-   **缩进**：使用 4 个空格，禁用制表符
-   **行尾空白**：严禁任何尾随空白字符
-   **行长度**：最大 100 字符（比 PEP8 的 79 字符稍宽松）
-   **空行**：
    -   类定义前后 2 个空行
    -   函数定义前后 1 个空行
    -   方法定义前后 1 个空行

### 3. 导入规范

```python
# 标准库导入
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

# 第三方库导入
import yaml
import requests

# 本地导入
from miniboot.core import Container
from miniboot.annotations import Component
```

### 4. 命名规范

-   **类名**：PascalCase (`BannerManager`)
-   **函数/方法名**：snake_case (`get_current_version`)
-   **变量名**：snake_case (`config_file`)
-   **常量名**：UPPER_SNAKE_CASE (`DEFAULT_PORT`)
-   **私有成员**：前缀下划线 (`_private_method`)

### 5. 字符串规范

-   **优先使用双引号**：`"hello world"`
-   **文档字符串使用三重双引号**：`"""Documentation string"""`
-   **格式化字符串使用 f-string**：`f"Version: {version}"`

### 6. 语言规范

-   **注释和文档字符串可以使用中文**：为提高代码可读性，允许使用中文注释
-   **中文注释标点符号规范**：
    -   **必须使用英文标点符号**：逗号(,)、句号(.)、分号(;)、冒号(:)、感叹号(!)、问号(?)等
    -   **禁止使用中文标点符号**：避免使用中文逗号(，)、中文句号(。)、中文冒号(：)、中文分号(；)等
    -   **括号统一使用英文括号**：()、[]、{}，禁止使用（）、【】、《》
    -   **引号统一使用英文引号**：""、''，禁止使用""、''
-   **变量命名使用英文**：避免使用中文拼音或中文字符
-   **异常消息可以使用中文**：用户面向的错误消息可以使用中文以提高可读性，但标点符号仍需使用英文

**正确示例**：

```python
class Environment(PropertyResolver, ABC):
    """环境接口

    扩展 PropertyResolver, 添加 Profile 管理功能.
    """

    def get_active_profiles(self) -> list[str]:
        """获取激活的 Profile 列表

        Returns:
            激活的 Profile 名称列表
        """
        # 从配置中获取激活的 profiles, 如果没有则返回默认值
        profiles = self.get_property('miniboot.profiles.active', 'default')
        return profiles.split(',') if isinstance(profiles, str) else []
```

**错误示例**：

```python
class Environment(PropertyResolver, ABC):
    """环境接口

    扩展 PropertyResolver，添加 Profile 管理功能。  # ❌ 中文逗号和句号
    """

    def get_active_profiles(self) -> list[str]:
        """获取激活的 Profile 列表。  # ❌ 中文句号

        Returns：  # ❌ 中文冒号
            激活的 Profile 名称列表
        """
        # 从配置中获取激活的 profiles，如果没有则返回默认值  # ❌ 中文逗号
        profiles = self.get_property（'miniboot.profiles.active'，'default'）  # ❌ 中文括号和逗号
        return profiles.split（'，'） if isinstance（profiles，str） else []  # ❌ 多处中文标点
```

## 代码质量规范

### 1. 异常处理

```python
# ✅ 正确：具体异常类型
try:
    result = some_operation()
except (ValueError, TypeError) as e:
    logger.error(f"操作失败: {e}")

# ❌ 错误：过于宽泛
try:
    result = some_operation()
except Exception as e:  # 避免使用
    logger.error(f"操作失败: {e}")
```

### 2. 函数参数

```python
# ✅ 正确：所有参数都被使用
def process_data(data: dict, config: dict) -> bool:
    result = validate_data(data)
    settings = load_settings(config)
    return result and settings

# ❌ 错误：未使用的参数
def process_data(data: dict, unused_param: dict) -> bool:
    return validate_data(data)
```

### 3. 导入管理

```python
# ✅ 正确：只导入使用的模块
from pathlib import Path
from typing import Optional

def get_file_size(file_path: Optional[Path]) -> int:
    if file_path and file_path.exists():
        return file_path.stat().st_size
    return 0

# ❌ 错误：导入但未使用
import os  # 如果未使用则删除
from typing import Dict, List  # 如果未使用则删除
```

### 4. 类型注解

```python
# ✅ 正确：完整的类型注解
def update_version(self, version_type: str) -> str:
    """更新版本号"""
    pass

def load_config(self, config_file: Optional[str] = None) -> Dict[str, Any]:
    """加载配置文件"""
    pass
```

## 设计模式规范

### 1. 单例模式

对于需要全局唯一实例的类（如配置管理器、缓存管理器等），必须使用标准的单例模式实现。

#### 实现规范

**✅ 正确：使用 SingletonMeta 元类**

```python
from miniboot.utils.singleton import SingletonMeta

class ConfigurationMerger(metaclass=SingletonMeta):
    """配置合并器 - 全局单例"""

    def __init__(self):
        """初始化配置合并器"""
        if not hasattr(self, '_initialized'):
            self._priority_manager = get_priority()
            self._initialized = True

    def merge_configs(self, high_priority: dict, low_priority: dict) -> dict:
        """合并配置"""
        # 实现逻辑
        pass
```

**❌ 错误：使用全局变量和便捷函数**

```python
# 不要这样做
class ConfigurationMerger:
    def __init__(self):
        self._priority_manager = get_priority()

# 全局实例
_configuration_merger = ConfigurationMerger()

def get_merger() -> ConfigurationMerger:
    """获取全局配置合并器实例"""
    return _configuration_merger

def merge_configs(high_priority: dict, low_priority: dict) -> dict:
    """便捷函数"""
    return _configuration_merger.merge_configs(high_priority, low_priority)
```

#### 使用规范

**调用方负责初始化**

```python
# ✅ 正确：调用方控制初始化时机
from miniboot.env.merger import ConfigurationMerger

class SomeService:
    def __init__(self):
        # 第一次调用时自动初始化，后续调用返回同一实例
        self.merger = ConfigurationMerger()

    def process_configs(self, config1: dict, config2: dict) -> dict:
        return self.merger.merge_configs(config1, config2)

# 验证单例行为
merger1 = ConfigurationMerger()
merger2 = ConfigurationMerger()
assert merger1 is merger2  # True
```

**❌ 错误：使用便捷函数**

```python
# 不要这样做
from miniboot.env.merger import get_merger, merge_configs

# 隐式依赖全局状态
merger = get_merger()
result = merge_configs(config1, config2)
```

#### 测试支持

**单例重置（仅测试时使用）**

```python
import unittest
from miniboot.utils.singleton import SingletonMeta
from miniboot.env.merger import ConfigurationMerger

class TestConfigurationMerger(unittest.TestCase):
    def setUp(self):
        # 测试前重置单例状态
        SingletonMeta.reset_instance(ConfigurationMerger)

    def test_singleton_behavior(self):
        merger1 = ConfigurationMerger()
        merger2 = ConfigurationMerger()
        self.assertIs(merger1, merger2)
```

#### 适用场景

**应该使用单例模式的类**：

-   配置管理器（ConfigurationMerger、ConfigurationPriorityManager）
-   缓存管理器（CacheManager）
-   日志管理器（LogManager）
-   性能指标收集器（MetricsCollector）
-   事件总线（EventBus）

**不应该使用单例模式的类**：

-   数据传输对象（DTO）
-   业务实体类（Entity）
-   服务类（Service）- 除非确实需要全局唯一
-   工具类（Utility）- 通常使用静态方法

#### 优势

1. **线程安全**：SingletonMeta 使用双重检查锁定模式
2. **延迟初始化**：首次使用时才创建实例
3. **测试友好**：支持单例重置，便于单元测试
4. **内存效率**：全局唯一实例，避免重复创建
5. **职责清晰**：调用方控制初始化时机

## 文档规范

### 1. 文档字符串

```python
def publish_to_repository(self, url: str, username: str, password: str) -> bool:
    """
    发布包到私有仓库

    Args:
        url: 仓库地址
        username: 用户名
        password: 密码

    Returns:
        发布是否成功

    Raises:
        ConnectionError: 网络连接失败
        AuthenticationError: 认证失败
    """
    pass
```

### 2. 注释规范

#### 基本格式

```python
# 单行注释使用 # 开头，后跟一个空格
result = calculate_value()  # 行尾注释

# 多行注释每行都使用 #
# 这是一个复杂的算法
# 需要多行解释
complex_algorithm()
```

#### 中文注释标点符号规范

**标点符号对照表**：

| 中文标点 | 英文标点 | 说明   |
| -------- | -------- | ------ |
| ，       | ,        | 逗号   |
| 。       | .        | 句号   |
| ：       | :        | 冒号   |
| ；       | ;        | 分号   |
| ！       | !        | 感叹号 |
| ？       | ?        | 问号   |
| （）     | ()       | 圆括号 |
| 【】     | []       | 方括号 |
| 《》     | <>       | 尖括号 |
| ""       | ""       | 双引号 |
| ''       | ''       | 单引号 |

**✅ 正确示例**：

```python
class Environment:
    """环境接口

    扩展 PropertyResolver, 添加 Profile 管理功能.
    """

    def get_profiles(self) -> list[str]:
        """获取 Profile 列表

        Args:
            profiles: Profile 名称列表

        Returns:
            激活的 Profile 名称列表
        """
        # 从配置文件中读取 profiles, 支持逗号分隔的多个值
        return self._load_profiles_from_config()
```

**❌ 错误示例**：

```python
class Environment:
    """环境接口

    扩展 PropertyResolver，添加 Profile 管理功能。  # ❌ 中文逗号和句号
    """

    def get_profiles(self) -> list[str]:
        """获取 Profile 列表。  # ❌ 中文句号

        Args：  # ❌ 中文冒号
            profiles： Profile 名称列表  # ❌ 中文冒号
        """
        # 从配置文件中读取 profiles，支持逗号分隔的多个值  # ❌ 中文逗号
        return self._load_profiles_from_config（）  # ❌ 中文括号
```

## 工具配置

### 1. Ruff 配置 (.ruff.toml)

```toml
[tool.ruff]
line-length = 100
target-version = "py39"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "N",  # pep8-naming
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "PIE790",  # unnecessary pass statement (allow in abstract methods)
    "E402",  # module level import not at top of file (allow function-level imports)
    "F401",  # imported but unused (handled by per-file-ignores)
    "F811",  # redefined while unused
    "PLC0414",  # import alias does not rename original package
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
```

### 2. VS Code 配置 (.vscode/settings.json)

```json
{
    "python.formatting.provider": "ruff",
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.analysis.diagnosticSeverityOverrides": {
        "reportUnnecessaryTypeIgnoreComment": "none",
        "reportUnnecessaryComparison": "none",
        "reportUnnecessaryContains": "none",
        "reportUnnecessaryIsInstance": "none"
    },
    "editor.formatOnSave": true,
    "editor.trimAutoWhitespace": true,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true
}
```

## 代码检查清单

### 提交前检查

-   [ ] 文件头格式正确（包含 shebang、encoding 和文档字符串）
-   [ ] 中文注释使用英文标点符号（,.:;!?()[]{}""''）
-   [ ] 无中文标点符号（，。：；！？（）【】《》""''）
-   [ ] 无语法错误
-   [ ] 无尾随空白
-   [ ] 无未使用的导入
-   [ ] 无未使用的参数
-   [ ] 无过于宽泛的异常捕获
-   [ ] 所有函数都有类型注解
-   [ ] 所有公共函数都有文档字符串
-   [ ] 代码格式符合规范
-   [ ] 全局变量使用单例模式（使用 SingletonMeta 元类）
-   [ ] 无全局实例变量和便捷函数（调用方负责初始化）
-   [ ] 单例类正确实现 `_initialized` 检查

### 自动化检查

```bash
# 运行完整的代码质量检查
uv run python tests/test_runner.py

# 单独检查中文标点符号
uv run python -m pytest tests/test_code_quality.py::CodeQualityTestCase::test_no_chinese_punctuation -v

# 格式化代码
uv run ruff format .

# 检查代码风格
uv run ruff check .

# 修复可自动修复的问题
uv run ruff check --fix .
```

## 强制执行

### 1. Git 钩子

创建 `.git/hooks/pre-commit` 脚本自动检查代码质量

### 2. CI/CD 集成

在发布流程中强制执行代码质量检查

### 3. IDE 配置

配置 VS Code 自动格式化和检查

## 违规处理

-   **轻微违规**：自动修复（格式化工具）
-   **中等违规**：警告并要求修复
-   **严重违规**：阻止提交/发布

## 快速参考

### 常见中文标点符号替换

在编写代码时，请注意以下常见的中文标点符号替换：

```
❌ 中文标点    ✅ 英文标点    说明
，           ,            逗号
。           .            句号
：           :            冒号
；           ;            分号
！           !            感叹号
？           ?            问号
（           (            左圆括号
）           )            右圆括号
【           [            左方括号
】           ]            右方括号
《           <            左尖括号
》           >            右尖括号
"            "            左双引号
"            "            右双引号
'            '            左单引号
'            '            右单引号
```

### 记忆技巧

1. **输入法设置**：将输入法设置为英文标点符号模式
2. **IDE 配置**：配置 IDE 自动检查和提示中文标点符号
3. **代码审查**：在代码审查时重点检查标点符号
4. **自动化工具**：使用 `test_runner.py` 自动检查

### 常见错误示例

```python
# ❌ 错误：混用中文标点
def load_config(self，config_file: str）-> dict：
    """加载配置文件。

    Args：
        config_file： 配置文件路径
    """
    pass

# ✅ 正确：统一使用英文标点
def load_config(self, config_file: str) -> dict:
    """加载配置文件.

    Args:
        config_file: 配置文件路径
    """
    pass
```

遵循这些规范可以确保 Mini-Boot 项目的代码质量始终保持在高水平！
