#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Monitor Starter自动配置
"""

from miniboot.annotations import Bean, ConditionalOnProperty
from miniboot.autoconfigure.base import StarterAutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata

from .properties import MonitorProperties
from .service import MonitorService


@ConditionalOnProperty(prefix="miniboot.starters.monitor", name="enabled", having_value="true", match_if_missing=True)
class MonitorAutoConfiguration(StarterAutoConfiguration):
    """Monitor Starter自动配置类

    当monitor.enabled=true时自动配置监控功能.
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="monitor-auto-configuration",
            description="监控功能自动配置",
            priority=100,  # 高优先级,监控应该早启动
            auto_configure_after=[],  # 不依赖其他配置
        )

    def get_starter_name(self) -> str:
        """获取Starter名称"""
        return "miniboot-starter-monitor"

    def get_starter_version(self) -> str:
        """获取Starter版本"""
        return "1.0.0"

    def get_starter_description(self) -> str:
        """获取Starter描述"""
        return "Mini-Boot 监控功能Starter,提供应用性能监控和健康检查"

    def get_configuration_properties_classes(self) -> list[type]:
        """获取配置属性类列表"""
        return [MonitorProperties]

    @Bean
    def monitor_properties(self) -> MonitorProperties:
        """创建Monitor配置属性Bean

        Returns:
            MonitorProperties: Monitor配置属性实例
        """
        return MonitorProperties()

    @Bean
    def monitor_service(self, monitor_properties: MonitorProperties) -> MonitorService:
        """创建监控服务Bean

        Args:
            monitor_properties: 监控配置属性

        Returns:
            MonitorService: 监控服务实例
        """
        return MonitorService(monitor_properties)
