#!/usr/bin/env python
"""
* @author: cz
* @description: 属性映射功能测试
"""

import unittest
from dataclasses import dataclass

from miniboot.env.bind import Binder, configuration_properties
from miniboot.env.resolver import PropertyResolver


@configuration_properties(prefix="db", property_mapping={"host": "hostname", "port": "port_number", "username": "user", "password": "pass"})
@dataclass
class DatabaseConfig:
    """数据库配置,使用属性映射"""

    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = ""
    database: str = "test"  # 没有映射,使用原始名称


@configuration_properties(prefix="server", property_mapping={"name": "server_name", "port": "listen_port"})
@dataclass
class ServerConfig:
    """服务器配置,使用属性映射"""

    name: str = "default-server"
    port: int = 8080
    enabled: bool = True  # 没有映射,使用原始名称


@configuration_properties(prefix="app", property_mapping={"database": "db_config", "server": "web_server"})
@dataclass
class ApplicationConfig:
    """应用配置,嵌套对象也使用属性映射"""

    name: str = "test-app"
    version: str = "1.0.0"
    database: DatabaseConfig = None
    server: ServerConfig = None


class MockPropertyResolver(PropertyResolver):
    """模拟属性解析器"""

    def __init__(self, properties: dict):
        self._properties = properties

    def get_property(self, key: str, default=None):
        return self._properties.get(key, default)

    def contains_property(self, key: str) -> bool:
        return key in self._properties

    def get_property_as(self, key: str, target_type, default=None):
        value = self.get_property(key, default)
        if value is None:
            return default
        return target_type(value)

    def resolve_placeholders(self, text: str) -> str:
        return text

    def resolve_required_placeholders(self, text: str) -> str:
        return text


class PropertyMappingTestCase(unittest.TestCase):
    """属性映射功能测试"""

    def test_simple_property_mapping(self):
        """测试简单属性映射"""
        properties = {
            "db.hostname": "db.example.com",  # 映射到 host 字段
            "db.port_number": "5432",  # 映射到 port 字段
            "db.user": "admin",  # 映射到 username 字段
            "db.pass": "secret",  # 映射到 password 字段
            "db.database": "myapp",  # 没有映射,使用原始名称
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("db", DatabaseConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        db = result.target
        self.assertEqual("db.example.com", db.host)
        self.assertEqual(5432, db.port)
        self.assertEqual("admin", db.username)
        self.assertEqual("secret", db.password)
        self.assertEqual("myapp", db.database)

    def test_partial_property_mapping(self):
        """测试部分属性映射"""
        properties = {
            "server.server_name": "web-server",  # 映射到 name 字段
            "server.enabled": "false",  # 没有映射,使用原始名称
            # port 使用默认值
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("server", ServerConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        server = result.target
        self.assertEqual("web-server", server.name)
        self.assertEqual(8080, server.port)  # 默认值
        self.assertFalse(server.enabled)

    def test_nested_property_mapping(self):
        """测试嵌套对象的属性映射"""
        properties = {
            "app.name": "my-application",
            "app.version": "2.0.0",
            "app.db_config.hostname": "nested-db.example.com",  # 嵌套映射
            "app.db_config.port_number": "5432",
            "app.db_config.user": "nested-admin",
            "app.web_server.server_name": "nested-web-server",  # 嵌套映射
            "app.web_server.listen_port": "9090",
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("app", ApplicationConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        app = result.target
        self.assertEqual("my-application", app.name)
        self.assertEqual("2.0.0", app.version)

        # 检查嵌套数据库配置
        self.assertIsNotNone(app.database)
        self.assertEqual("nested-db.example.com", app.database.host)
        self.assertEqual(5432, app.database.port)
        self.assertEqual("nested-admin", app.database.username)

        # 检查嵌套服务器配置
        self.assertIsNotNone(app.server)
        self.assertEqual("nested-web-server", app.server.name)
        self.assertEqual(9090, app.server.port)

    def test_property_mapping_with_type_conversion(self):
        """测试属性映射与类型转换"""
        properties = {
            "db.hostname": "converted-db.example.com",
            "db.port_number": "3307",  # 字符串转整数
            "db.user": "converted-user",
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("db", DatabaseConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        db = result.target
        self.assertEqual("converted-db.example.com", db.host)
        self.assertEqual(3307, db.port)
        self.assertIsInstance(db.port, int)
        self.assertEqual("converted-user", db.username)

    def test_property_mapping_without_prefix(self):
        """测试无前缀的属性映射"""

        @configuration_properties(property_mapping={"host": "server_host", "port": "server_port"})
        @dataclass
        class SimpleConfig:
            host: str = "localhost"
            port: int = 8080

        properties = {"server_host": "no-prefix.example.com", "server_port": "9999"}

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("", SimpleConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        config = result.target
        self.assertEqual("no-prefix.example.com", config.host)
        self.assertEqual(9999, config.port)

    def test_property_mapping_missing_properties(self):
        """测试映射属性不存在的情况"""
        properties = {
            "db.database": "only-database"  # 只有一个非映射属性
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("db", DatabaseConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        db = result.target
        # 映射的属性使用默认值
        self.assertEqual("localhost", db.host)
        self.assertEqual(3306, db.port)
        self.assertEqual("root", db.username)
        self.assertEqual("", db.password)
        # 非映射属性使用配置值
        self.assertEqual("only-database", db.database)

    def test_property_mapping_empty_mapping(self):
        """测试空映射的情况"""

        @configuration_properties(
            prefix="test",
            property_mapping={},  # 空映射
        )
        @dataclass
        class EmptyMappingConfig:
            name: str = "default"
            value: int = 100

        properties = {"test.name": "empty-mapping-test", "test.value": "200"}

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("test", EmptyMappingConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        config = result.target
        self.assertEqual("empty-mapping-test", config.name)
        self.assertEqual(200, config.value)

    def test_property_mapping_no_decorator(self):
        """测试没有装饰器的类(向后兼容性)"""

        @dataclass
        class NoDecoratorConfig:
            name: str = "default"
            value: int = 100

        properties = {"test.name": "no-decorator-test", "test.value": "300"}

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("test", NoDecoratorConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        config = result.target
        self.assertEqual("no-decorator-test", config.name)
        self.assertEqual(300, config.value)


if __name__ == "__main__":
    unittest.main()
