{"recommendations": ["ms-python.python", "ms-python.vscode-pylance", "charliermarsh.ruff", "ms-python.mypy-type-checker", "formulahendry.code-runner", "ms-vscode.test-adapter-converter", "littlefoxteam.vscode-python-test-adapter", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-markdown", "ms-vscode.vscode-todo-highlight", "gruntfuggly.todo-tree", "eamodio.gitlens", "ms-vscode.vscode-git-graph"], "unwantedRecommendations": ["ms-python.flake8", "ms-python.pylint", "ms-python.black-formatter"]}