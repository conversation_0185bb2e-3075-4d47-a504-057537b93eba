#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: PerformanceMonitor 测试运行脚本

提供便捷的测试运行和报告功能。

使用方法:
    python run_tests.py                    # 运行所有测试
    python run_tests.py --verbose          # 详细输出
    python run_tests.py --coverage         # 生成覆盖率报告
    python run_tests.py --specific TestPerformanceMonitor  # 运行特定测试类
"""

import argparse
import subprocess
import sys
from pathlib import Path


def run_tests(verbose=False, coverage=False, specific_test=None):
    """运行测试"""
    
    # 基础命令
    cmd = ["python", "-m", "pytest"]
    
    # 测试文件路径
    test_file = "tests/unit/starters/actuator/performance/test_performance_monitor.py"
    
    if specific_test:
        test_file += f"::{specific_test}"
    
    cmd.append(test_file)
    
    # 添加选项
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend([
            "--cov=miniboot.starters.actuator.performance.monitor",
            "--cov-report=html",
            "--cov-report=term-missing"
        ])
    
    # 添加其他有用的选项
    cmd.extend([
        "--tb=short",  # 简短的错误回溯
        "-x",          # 遇到第一个失败就停止
    ])
    
    print(f"运行命令: {' '.join(cmd)}")
    print("-" * 80)
    
    try:
        result = subprocess.run(cmd, check=True)
        print("-" * 80)
        print("✅ 所有测试通过!")
        
        if coverage:
            print("📊 覆盖率报告已生成到 htmlcov/ 目录")
            
        return True
        
    except subprocess.CalledProcessError as e:
        print("-" * 80)
        print(f"❌ 测试失败，退出码: {e.returncode}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="PerformanceMonitor 测试运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python run_tests.py                                    # 运行所有测试
  python run_tests.py -v                                 # 详细输出
  python run_tests.py --coverage                         # 生成覆盖率报告
  python run_tests.py --specific TestPerformanceMonitor  # 运行特定测试类
  python run_tests.py -v --coverage                      # 详细输出 + 覆盖率
        """
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="详细输出测试结果"
    )
    
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="生成代码覆盖率报告"
    )
    
    parser.add_argument(
        "--specific",
        type=str,
        help="运行特定的测试类或方法"
    )
    
    args = parser.parse_args()
    
    # 检查当前目录
    if not Path("miniboot").exists():
        print("❌ 请在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 运行测试
    success = run_tests(
        verbose=args.verbose,
        coverage=args.coverage,
        specific_test=args.specific
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
