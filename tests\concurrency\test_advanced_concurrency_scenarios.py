#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 高级并发场景测试 - 复杂并发场景和边界条件测试

测试复杂的并发场景，包括竞态条件、死锁检测、资源竞争等。
"""

import asyncio
import random
import threading
import time
import unittest
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from unittest.mock import Mock

# 组合式组件
from miniboot.bean.bean_registry import DefaultBeanDefinitionRegistry
from miniboot.bean.composite_factory import AsyncCompositeBeanFactory, CompositeBeanFactory
from miniboot.bean.composite_proxy import CompositeProxy
from miniboot.bean.deps_graph import DependencyGraph
from miniboot.events.composite_event import CompositeEvent


class AdvancedConcurrencyTestCase(unittest.TestCase):
    """高级并发场景测试用例"""

    def test_race_condition_detection(self):
        """测试竞态条件检测"""
        # 创建共享资源
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = CompositeBeanFactory(registry, dependency_graph)

        # 共享计数器
        shared_counter = {"value": 0}
        counter_lock = threading.Lock()

        class RaceConditionBean:
            def __init__(self):
                self.local_counter = 0

            def increment_shared(self):
                """可能产生竞态条件的操作"""
                # 故意不加锁，测试竞态条件
                current = shared_counter["value"]
                time.sleep(0.001)  # 增加竞态条件发生概率
                shared_counter["value"] = current + 1
                self.local_counter += 1

            def safe_increment_shared(self):
                """线程安全的操作"""
                with counter_lock:
                    current = shared_counter["value"]
                    time.sleep(0.001)
                    shared_counter["value"] = current + 1
                    self.local_counter += 1

        race_bean = RaceConditionBean()
        factory.register_singleton("raceBean", race_bean)

        # 测试竞态条件
        def unsafe_worker():
            bean = factory.get_bean("raceBean")
            for _ in range(10):
                bean.increment_shared()

        def safe_worker():
            bean = factory.get_bean("raceBean")
            for _ in range(10):
                bean.safe_increment_shared()

        # 重置计数器
        shared_counter["value"] = 0
        race_bean.local_counter = 0

        # 运行不安全的并发操作
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(unsafe_worker) for _ in range(5)]
            for future in as_completed(futures):
                future.result()

        unsafe_result = shared_counter["value"]
        unsafe_local = race_bean.local_counter

        # 重置计数器
        shared_counter["value"] = 0
        race_bean.local_counter = 0

        # 运行安全的并发操作
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(safe_worker) for _ in range(5)]
            for future in as_completed(futures):
                future.result()

        safe_result = shared_counter["value"]
        safe_local = race_bean.local_counter

        # 验证结果
        self.assertEqual(safe_result, 50, "安全操作应该得到正确结果")
        self.assertEqual(safe_local, 50, "本地计数器应该正确")

        # 不安全操作可能产生竞态条件（结果可能小于期望值）
        print(f"不安全操作结果: {unsafe_result}/50, 本地计数: {unsafe_local}")
        print(f"安全操作结果: {safe_result}/50, 本地计数: {safe_local}")

        # 清理
        factory.shutdown()

        print("✅ 竞态条件检测测试完成")

    def test_deadlock_prevention(self):
        """测试死锁预防"""
        # 创建可能导致死锁的资源
        lock1 = threading.Lock()
        lock2 = threading.Lock()

        results = []
        errors = []

        def worker1():
            """工作线程1 - 按顺序获取锁"""
            try:
                with lock1:
                    time.sleep(0.1)
                    with lock2:
                        results.append("worker1_success")
            except Exception as e:
                errors.append(("worker1", e))

        def worker2():
            """工作线程2 - 按相同顺序获取锁（避免死锁）"""
            try:
                with lock1:
                    time.sleep(0.1)
                    with lock2:
                        results.append("worker2_success")
            except Exception as e:
                errors.append(("worker2", e))

        def worker3_deadlock_prone():
            """工作线程3 - 可能导致死锁的顺序"""
            try:
                with lock2:
                    time.sleep(0.1)
                    # 尝试获取lock1，但设置超时
                    if lock1.acquire(timeout=0.5):
                        try:
                            results.append("worker3_success")
                        finally:
                            lock1.release()
                    else:
                        results.append("worker3_timeout")
            except Exception as e:
                errors.append(("worker3", e))

        # 运行死锁测试
        start_time = time.time()
        threads = [threading.Thread(target=worker1), threading.Thread(target=worker2), threading.Thread(target=worker3_deadlock_prone)]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join(timeout=2.0)  # 设置超时防止测试卡死

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"死锁测试出现错误: {errors}")
        self.assertGreater(len(results), 0, "应该有成功的操作")
        self.assertLess(end_time - start_time, 2.0, "测试不应该因死锁而超时")

        print(f"✅ 死锁预防测试通过 - 结果: {results}")

    def test_resource_contention(self):
        """测试资源竞争"""
        # 创建有限资源池
        resource_pool = []
        pool_lock = threading.Lock()
        max_resources = 5

        # 初始化资源池
        for i in range(max_resources):
            resource_pool.append(f"resource_{i}")

        results = []
        errors = []

        def resource_worker(worker_id: int):
            """资源竞争工作函数"""
            try:
                acquired_resources = []
                for i in range(10):
                    # 尝试获取资源
                    resource = None
                    with pool_lock:
                        if resource_pool:
                            resource = resource_pool.pop()

                    if resource:
                        acquired_resources.append(resource)
                        # 模拟使用资源
                        time.sleep(random.uniform(0.01, 0.05))

                        # 释放资源
                        with pool_lock:
                            resource_pool.append(resource)

                        results.append((worker_id, i, "acquired", resource))
                    else:
                        results.append((worker_id, i, "failed", None))
                        time.sleep(0.01)  # 等待资源释放

            except Exception as e:
                errors.append((worker_id, e))

        # 运行资源竞争测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(resource_worker, i) for i in range(10)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"资源竞争测试出现错误: {errors}")

        # 统计成功和失败的操作
        successful_ops = [r for r in results if r[2] == "acquired"]
        [r for r in results if r[2] == "failed"]

        self.assertGreater(len(successful_ops), 0, "应该有成功的资源获取")

        # 验证资源池完整性
        with pool_lock:
            self.assertEqual(len(resource_pool), max_resources, "资源池应该恢复完整")

        success_rate = len(successful_ops) / len(results)
        print(f"✅ 资源竞争测试通过 - 成功率: {success_rate:.2%}, 耗时: {end_time - start_time:.3f}秒")

    def test_async_sync_interaction(self):
        """测试异步同步交互"""

        async def async_test():
            # 创建异步和同步工厂
            registry = DefaultBeanDefinitionRegistry()
            dependency_graph = DependencyGraph()
            sync_factory = CompositeBeanFactory(registry, dependency_graph)
            async_factory = AsyncCompositeBeanFactory(registry, dependency_graph)

            # 创建测试Bean
            class AsyncSyncBean:
                def __init__(self):
                    self.sync_calls = 0
                    self.async_calls = 0
                    self._lock = threading.Lock()

                def sync_method(self):
                    with self._lock:
                        self.sync_calls += 1
                        return f"sync_result_{self.sync_calls}"

                async def async_method(self):
                    await asyncio.sleep(0.01)
                    with self._lock:
                        self.async_calls += 1
                        return f"async_result_{self.async_calls}"

            test_bean = AsyncSyncBean()
            sync_factory.register_singleton("testBean", test_bean)
            await async_factory.register_singleton("testBean", test_bean)

            # 并发执行同步和异步操作
            async def async_worker(worker_id: int):
                results = []
                for _i in range(5):
                    bean = await async_factory.get_bean("testBean")
                    result = await bean.async_method()
                    results.append((worker_id, "async", result))
                return results

            def sync_worker(worker_id: int):
                results = []
                for _i in range(5):
                    bean = sync_factory.get_bean("testBean")
                    result = bean.sync_method()
                    results.append((worker_id, "sync", result))
                return results

            # 创建混合任务
            async_tasks = [async_worker(i) for i in range(3)]

            # 在线程池中运行同步任务
            loop = asyncio.get_event_loop()
            sync_tasks = [loop.run_in_executor(None, sync_worker, i) for i in range(3)]

            # 等待所有任务完成
            async_results = await asyncio.gather(*async_tasks)
            sync_results = await asyncio.gather(*sync_tasks)

            # 验证结果
            total_async_calls = sum(len(result) for result in async_results)
            total_sync_calls = sum(len(result) for result in sync_results)

            self.assertEqual(total_async_calls, 15, "应该有15个异步调用")
            self.assertEqual(total_sync_calls, 15, "应该有15个同步调用")
            self.assertEqual(test_bean.async_calls, 15, "Bean应该记录15个异步调用")
            self.assertEqual(test_bean.sync_calls, 15, "Bean应该记录15个同步调用")

            # 清理
            sync_factory.shutdown()
            await async_factory.shutdown()

            print("✅ 异步同步交互测试通过")

        # 运行异步测试
        asyncio.run(async_test())

    def test_memory_pressure_under_concurrency(self):
        """测试并发下的内存压力"""
        import gc
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 创建大量对象进行并发操作
        def memory_pressure_worker(worker_id: int):
            objects = []
            for i in range(100):
                # 创建事件对象
                event = CompositeEvent(
                    event_type=f"MemoryTest_{worker_id}_{i}", source=Mock(), data={"worker": worker_id, "iteration": i, "data": "x" * 1000}
                )
                objects.append(event)

                # 创建代理对象
                proxy = CompositeProxy(sync_bean=Mock(), bean_name=f"memoryProxy_{worker_id}_{i}")
                objects.append(proxy)

                # 定期清理
                if i % 20 == 0:
                    for obj in objects[:10]:
                        if hasattr(obj, "cleanup"):
                            obj.cleanup()
                        if hasattr(obj, "shutdown"):
                            obj.shutdown()
                    objects = objects[10:]

            # 清理剩余对象
            for obj in objects:
                if hasattr(obj, "cleanup"):
                    obj.cleanup()
                if hasattr(obj, "shutdown"):
                    obj.shutdown()

        # 运行内存压力测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(memory_pressure_worker, i) for i in range(5)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 强制垃圾回收
        gc.collect()
        time.sleep(0.1)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 验证内存使用
        self.assertLess(memory_increase, 100, f"内存增长应该小于100MB，实际增长: {memory_increase:.2f}MB")
        self.assertLess(end_time - start_time, 10.0, "内存压力测试应该在10秒内完成")

        print(f"✅ 内存压力测试通过 - 内存增长: {memory_increase:.2f}MB, 耗时: {end_time - start_time:.3f}秒")


if __name__ == "__main__":
    unittest.main(verbosity=2)
