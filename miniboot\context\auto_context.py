#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 自动上下文装饰器模块

提供@auto_context装饰器,实现函数级别的智能上下文管理.
自动检测函数执行环境,提供透明的同步/异步上下文支持.
"""

import asyncio
import functools
import inspect
from typing import Any, Callable, Optional, TypeVar

from loguru import logger

from miniboot.utils import SingletonMeta

from ..errors import ContextInitializationError

# Smart context functionality is now integrated into DefaultApplicationContext

F = TypeVar("F", bound=Callable[..., Any])


def auto_context(
    config_path: Optional[str] = None,
    packages_to_scan: Optional[list[str]] = None,
    auto_detect: bool = True,
    context_param: str = "context",
    auto_start: bool = True,
) -> Callable[[F], F]:
    """自动上下文装饰器

    为函数自动提供Mini-Boot应用上下文,支持同步/异步透明切换.

    Args:
        config_path: 配置文件路径
        packages_to_scan: 要扫描的包列表
        auto_detect: 是否自动检测异步环境
        context_param: 上下文参数名称
        auto_start: 是否自动启动上下文

    Returns:
        装饰器函数

    Example:
        @auto_context(config_path="app.yml")
        def my_function(context):
            user_service = context.get_bean("userService")
            return user_service.get_users()

        @auto_context(packages_to_scan=["com.example"])
        async def my_async_function(context):
            user_service = await context.get_bean("userService")
            return await user_service.get_users_async()
    """

    def decorator(func: F) -> F:
        # 检查函数签名
        sig = inspect.signature(func)
        has_context_param = context_param in sig.parameters

        if not has_context_param:
            logger.warning(f"Function {func.__name__} does not have '{context_param}' parameter")

        if asyncio.iscoroutinefunction(func):
            # 异步函数装饰器
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await _execute_with_context_async(
                    func, args, kwargs, config_path, packages_to_scan, auto_detect, context_param, auto_start, has_context_param
                )

            return async_wrapper
        else:
            # 同步函数装饰器
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                return _execute_with_context_sync(
                    func, args, kwargs, config_path, packages_to_scan, auto_detect, context_param, auto_start, has_context_param
                )

            return sync_wrapper

    return decorator


def create_application(
    config_path: Optional[str] = None, packages_to_scan: Optional[list[str]] = None, auto_detect: bool = True
) -> "DefaultApplicationContext":
    """创建Mini-Boot应用上下文

    Args:
        config_path: 配置文件路径
        packages_to_scan: 要扫描的包列表
        auto_detect: 是否自动检测异步环境

    Returns:
        DefaultApplicationContext: 智能应用上下文实例(现在默认包含智能功能)
    """
    from .application import DefaultApplicationContext

    context = DefaultApplicationContext(config_path=config_path, packages_to_scan=packages_to_scan, auto_detect=auto_detect)
    return context


async def _execute_with_context_async(
    func: Callable,
    args: tuple,
    kwargs: dict,
    config_path: Optional[str],
    packages_to_scan: Optional[list[str]],
    auto_detect: bool,
    context_param: str,
    auto_start: bool,
    has_context_param: bool,
) -> Any:
    """异步执行函数并提供上下文"""
    context = None

    try:
        # 创建上下文
        context = create_application(config_path=config_path, packages_to_scan=packages_to_scan, auto_detect=auto_detect)

        # 启动上下文
        if auto_start:
            if context.is_async_mode():
                await context._start_async()
            else:
                context._start_sync()

        # 注入上下文参数
        if has_context_param:
            kwargs[context_param] = context

        # 执行函数
        result = await func(*args, **kwargs)

        return result

    except Exception as e:
        logger.error(f"Error executing async function with auto context: {e}")
        raise
    finally:
        # 清理上下文
        if context and auto_start and context.is_running():
            try:
                if context.is_async_mode():
                    await context._stop_async()
                else:
                    context._stop_sync()
            except Exception as e:
                logger.error(f"Error stopping context: {e}")


def _execute_with_context_sync(
    func: Callable,
    args: tuple,
    kwargs: dict,
    config_path: Optional[str],
    packages_to_scan: Optional[list[str]],
    auto_detect: bool,
    context_param: str,
    auto_start: bool,
    has_context_param: bool,
) -> Any:
    """同步执行函数并提供上下文"""
    context = None

    try:
        # 创建上下文
        context = create_application(config_path=config_path, packages_to_scan=packages_to_scan, auto_detect=auto_detect)

        # 启动上下文
        if auto_start:
            context._start_sync()

        # 注入上下文参数
        if has_context_param:
            kwargs[context_param] = context

        # 执行函数
        result = func(*args, **kwargs)

        return result

    except Exception as e:
        logger.error(f"Error executing sync function with auto context: {e}")
        raise
    finally:
        # 清理上下文
        if context and auto_start and context.is_running():
            try:
                context._stop_sync()
            except Exception as e:
                logger.error(f"Error stopping context: {e}")


class ContextManager(metaclass=SingletonMeta):
    """上下文管理器类

    提供更高级的上下文管理功能,支持上下文复用、缓存等特性.
    使用单例模式确保全局唯一性.
    """

    def __init__(self):
        """初始化上下文管理器"""
        # 防止重复初始化
        if not hasattr(self, "_initialized"):
            self._contexts = {}
            self._default_context = None
            self._initialized = True
            logger.debug("ContextManager initialized")

    def get_or_create_context(
        self,
        config_path: Optional[str] = None,
        packages_to_scan: Optional[list[str]] = None,
        auto_detect: bool = True,
        cache_key: Optional[str] = None,
    ) -> "DefaultApplicationContext":
        """获取或创建上下文

        Args:
            config_path: 配置文件路径
            packages_to_scan: 要扫描的包列表
            auto_detect: 是否自动检测异步环境
            cache_key: 缓存键,用于上下文复用

        Returns:
            DefaultApplicationContext: 应用上下文实例(现在默认包含智能功能)
        """
        if cache_key and cache_key in self._contexts:
            logger.debug(f"Reusing cached context: {cache_key}")
            return self._contexts[cache_key]

        # 创建新上下文
        context = create_application(config_path=config_path, packages_to_scan=packages_to_scan, auto_detect=auto_detect)

        # 缓存上下文
        if cache_key:
            self._contexts[cache_key] = context
            logger.debug(f"Cached context with key: {cache_key}")

        return context

    def set_default_context(self, context: "DefaultApplicationContext") -> None:
        """设置默认上下文"""
        self._default_context = context
        logger.debug("Default context set")

    def get_default_context(self) -> Optional["DefaultApplicationContext"]:
        """获取默认上下文"""
        return self._default_context

    def clear_cache(self) -> None:
        """清除上下文缓存"""
        self._contexts.clear()
        logger.debug("Context cache cleared")

    def shutdown_all(self) -> None:
        """关闭所有上下文"""
        for cache_key, context in self._contexts.items():
            try:
                if context.is_running():
                    context.stop()
                    logger.debug(f"Shutdown context: {cache_key}")
            except Exception as e:
                logger.error(f"Error shutting down context {cache_key}: {e}")

        if self._default_context and self._default_context.is_running():
            try:
                self._default_context.stop()
                logger.debug("Shutdown default context")
            except Exception as e:
                logger.error(f"Error shutting down default context: {e}")

    def cleanup(self) -> None:
        """清理资源(单例重置时调用)"""
        try:
            self.shutdown_all()
            self.clear_cache()
            self._default_context = None
            logger.debug("ContextManager cleanup completed")
        except Exception as e:
            logger.error(f"Error during ContextManager cleanup: {e}")


def with_context(
    cache_key: Optional[str] = None, config_path: Optional[str] = None, packages_to_scan: Optional[list[str]] = None, auto_detect: bool = True
) -> Callable[[F], F]:
    """使用上下文装饰器(支持缓存)"""

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 按需创建单例实例
            context_manager = ContextManager()
            context = context_manager.get_or_create_context(
                config_path=config_path, packages_to_scan=packages_to_scan, auto_detect=auto_detect, cache_key=cache_key
            )

            # 将上下文作为第一个参数传递
            return func(context, *args, **kwargs)

        return wrapper

    return decorator


def get_global_context_manager() -> ContextManager:
    """获取全局上下文管理器(单例)"""
    return ContextManager()


def set_default_context(context: "DefaultApplicationContext") -> None:
    """设置全局默认上下文"""
    context_manager = ContextManager()
    context_manager.set_default_context(context)


def get_default_context() -> Optional["DefaultApplicationContext"]:
    """获取全局默认上下文"""
    context_manager = ContextManager()
    return context_manager.get_default_context()
