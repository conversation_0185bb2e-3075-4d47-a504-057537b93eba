#!/usr/bin/env python
"""
* @author: cz
* @description: 启动横幅核心类
"""

from typing import Optional

from .constants import BannerConstants
from .printer import BannerPrinter, ConsoleBannerPrinter, LogBannerPrinter
from .properties import BannerConfig, BannerMode
from .resource import BannerResource, BannerResourceLoader


class Banner:
    """启动横幅类

    负责管理和显示应用启动时的横幅信息
    """

    def __init__(
        self,
        application_name: str = BannerConstants.DEFAULT_APPLICATION_NAME,
        application_version: str = BannerConstants.DEFAULT_APPLICATION_VERSION,
        resource_loader: Optional[BannerResourceLoader] = None,
    ):
        """初始化横幅

        Args:
            application_name: 应用名称
            application_version: 应用版本
            resource_loader: 资源加载器
        """
        self.application_name = application_name or BannerConstants.DEFAULT_APPLICATION_NAME
        self.application_version = application_version or BannerConstants.DEFAULT_APPLICATION_VERSION
        self.resource_loader = resource_loader or BannerResourceLoader()
        self._config: Optional[BannerConfig] = None
        self._resource: Optional[BannerResource] = None

    def configure(self, config: BannerConfig) -> "Banner":
        """配置横幅

        Args:
            config: 横幅配置

        Returns:
            Banner: 返回自身支持链式调用
        """
        self._config = config
        return self

    def from_env(self, env) -> "Banner":
        """从环境配置横幅

        Args:
            env: 环境配置对象

        Returns:
            Banner: 返回自身支持链式调用
        """
        self._config = BannerConfig.from_env(env)
        return self

    def resource(self, resource: BannerResource) -> "Banner":
        """设置横幅资源

        Args:
            resource: 横幅资源

        Returns:
            Banner: 返回自身支持链式调用
        """
        self._resource = resource
        return self

    def load(self, location: str, charset: str = BannerConstants.DEFAULT_CHARSET) -> "Banner":
        """从位置加载横幅资源

        Args:
            location: 资源位置
            charset: 字符编码

        Returns:
            Banner: 返回自身支持链式调用
        """
        self._resource = self.resource_loader.load(location, charset, self.application_name, self.application_version)
        return self

    def show(self, printer: Optional[BannerPrinter] = None) -> None:
        """显示横幅

        Args:
            printer: 横幅打印器,如果不提供则根据配置自动选择
        """
        config = self._get_config()

        if not config.is_enabled():
            return

        resource = self._get_resource(config)

        if printer is None:
            printer = self._create_printer(config)

        printer.print(resource, config)

    def _get_config(self) -> BannerConfig:
        """获取配置"""
        if self._config is None:
            self._config = BannerConfig()
        return self._config

    def _get_resource(self, config: BannerConfig) -> BannerResource:
        """获取资源"""
        if self._resource is None:
            if config.location:
                self._resource = self.resource_loader.load(
                    config.location, config.charset, self.application_name, self.application_version
                )
            else:
                self._resource = self.resource_loader.find(self.application_name, self.application_version)
        return self._resource

    def _create_printer(self, config: BannerConfig) -> BannerPrinter:
        """创建默认打印器"""
        if config.mode == BannerMode.LOG:
            return LogBannerPrinter()
        else:
            return ConsoleBannerPrinter()




    @staticmethod
    def show_from_env(env, application_name: str = BannerConstants.DEFAULT_APPLICATION_NAME, application_version: str = BannerConstants.DEFAULT_APPLICATION_VERSION) -> None:
        """从环境配置显示横幅

        Args:
            env: 环境配置对象
            application_name: 应用名称
            application_version: 应用版本
        """
        banner = Banner(application_name, application_version)
        banner.from_env(env).show()
