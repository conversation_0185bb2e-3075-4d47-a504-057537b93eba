#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 组合式事件类 - 使用组合模式替代继承层次

通过组合多个功能组件来实现复杂的事件功能,
避免深层继承带来的维护困难.
"""

from datetime import datetime
from typing import Any, Optional

from loguru import logger

from .components import (AsyncEventComponent, BeanEventComponent,
                         EventComponent, EventDataComponent, EventIdComponent,
                         EventMetadata)


class CompositeEvent:
    """组合式事件类

    使用组合模式将事件的各种功能分解为独立的组件,
    通过组合这些组件来实现完整的事件功能.

    优势:
    - 职责分离: 每个组件负责特定功能
    - 易于测试: 组件可以独立测试
    - 易于扩展: 可以轻松添加新组件
    - 易于维护: 避免了深层继承的复杂性
    """

    def __init__(self, event_type: str, source: Any = None, data: Optional[dict[str, Any]] = None, components: Optional[list[EventComponent]] = None):
        """初始化组合式事件

        Args:
            event_type: 事件类型
            source: 事件源
            data: 事件数据
            components: 自定义组件列表(可选)
        """
        # 初始化组件
        self._components: dict[str, EventComponent] = {}
        self._initialize_components(components)

        # 生成事件ID
        id_component = self.get_component(EventIdComponent)
        event_id = id_component.generate_id() if id_component else f"evt_{id(self)}"

        # 创建事件元数据
        self._metadata = EventMetadata(event_id=event_id, event_type=event_type, source=source, timestamp=datetime.now(), data=data or {})

        # 存储事件数据
        data_component = self.get_component(EventDataComponent)
        if data_component and data:
            data_component.store_event_data(event_id, data)

        logger.debug(f"CompositeEvent created: {event_id} ({event_type})")

    def _initialize_components(self, custom_components: Optional[list[EventComponent]] = None) -> None:
        """初始化事件组件

        Args:
            custom_components: 自定义组件列表
        """
        # 默认组件
        default_components = [EventIdComponent(), EventDataComponent(), AsyncEventComponent(), BeanEventComponent()]

        # 添加自定义组件
        if custom_components:
            default_components.extend(custom_components)

        # 注册组件
        for component in default_components:
            self._components[component.component_name] = component
            component.initialize()
            logger.debug(f"Initialized component: {component.component_name}")

    def get_component(self, component_type: type[EventComponent]) -> Optional[EventComponent]:
        """获取指定类型的组件

        Args:
            component_type: 组件类型

        Returns:
            Optional[EventComponent]: 组件实例,如果不存在返回None
        """
        for component in self._components.values():
            if isinstance(component, component_type):
                return component
        return None

    def add_component(self, component: EventComponent) -> None:
        """添加新组件

        Args:
            component: 要添加的组件
        """
        self._components[component.component_name] = component
        component.initialize()
        logger.debug(f"Added component: {component.component_name}")

    def remove_component(self, component_name: str) -> None:
        """移除组件

        Args:
            component_name: 组件名称
        """
        if component_name in self._components:
            component = self._components.pop(component_name)
            component.shutdown()
            logger.debug(f"Removed component: {component_name}")

    # === 事件基本属性 ===

    @property
    def event_id(self) -> str:
        """获取事件ID"""
        return self._metadata.event_id

    @property
    def event_type(self) -> str:
        """获取事件类型"""
        return self._metadata.event_type

    @property
    def source(self) -> Any:
        """获取事件源"""
        return self._metadata.source

    @property
    def timestamp(self) -> datetime:
        """获取事件时间戳"""
        return self._metadata.timestamp

    @property
    def processed(self) -> bool:
        """获取事件处理状态"""
        return self._metadata.processed

    def mark_processed(self) -> None:
        """标记事件已处理"""
        self._metadata.mark_processed()

    def is_processed(self) -> bool:
        """检查事件是否已处理"""
        return self._metadata.is_processed()

    # === 事件数据管理 ===

    def get_data(self, key: str, default: Any = None) -> Any:
        """获取事件数据

        Args:
            key: 数据键
            default: 默认值

        Returns:
            Any: 数据值或默认值
        """
        data_component = self.get_component(EventDataComponent)
        if data_component:
            event_data = data_component.get_event_data(self.event_id)
            return event_data.get(key, default)
        return self._metadata.get_data(key, default)

    def set_data(self, key: str, value: Any) -> None:
        """设置事件数据

        Args:
            key: 数据键
            value: 数据值
        """
        data_component = self.get_component(EventDataComponent)
        if data_component:
            event_data = data_component.get_event_data(self.event_id)
            event_data[key] = value
            data_component.store_event_data(self.event_id, event_data)
        else:
            self._metadata.set_data(key, value)

    def get_all_data(self) -> dict[str, Any]:
        """获取所有事件数据

        Returns:
            Dict[str, Any]: 所有事件数据
        """
        data_component = self.get_component(EventDataComponent)
        if data_component:
            return data_component.get_event_data(self.event_id)
        return self._metadata.data.copy()

    # === 异步事件支持 ===

    def register_as_async(self, async_data: Any = None) -> None:
        """注册为异步事件

        Args:
            async_data: 异步事件数据
        """
        async_component = self.get_component(AsyncEventComponent)
        if async_component:
            async_component.register_async_event(self.event_id, async_data)

    def start_async_processing(self) -> None:
        """开始异步处理"""
        async_component = self.get_component(AsyncEventComponent)
        if async_component:
            async_component.start_processing(self.event_id)

    def finish_async_processing(self) -> None:
        """完成异步处理"""
        async_component = self.get_component(AsyncEventComponent)
        if async_component:
            async_component.finish_processing(self.event_id)

    def is_async_processing(self) -> bool:
        """检查是否正在异步处理

        Returns:
            bool: 如果正在异步处理返回True
        """
        async_component = self.get_component(AsyncEventComponent)
        if async_component:
            return async_component.is_processing(self.event_id)
        return False

    # === Bean事件支持 ===

    def register_bean_event(self, bean_name: str) -> None:
        """注册为Bean事件

        Args:
            bean_name: Bean名称
        """
        bean_component = self.get_component(BeanEventComponent)
        if bean_component:
            bean_component.register_bean_event(self.event_id, bean_name)

    def get_bean_name(self) -> Optional[str]:
        """获取关联的Bean名称

        Returns:
            Optional[str]: Bean名称,如果不是Bean事件返回None
        """
        bean_component = self.get_component(BeanEventComponent)
        if bean_component:
            return bean_component.get_event_bean(self.event_id)
        return None

    # === 清理和销毁 ===

    def cleanup(self) -> None:
        """清理事件资源"""
        # 清理数据组件中的事件数据
        data_component = self.get_component(EventDataComponent)
        if data_component:
            data_component.remove_event_data(self.event_id)

        # 完成异步处理
        async_component = self.get_component(AsyncEventComponent)
        if async_component:
            async_component.finish_processing(self.event_id)

        logger.debug(f"Event cleaned up: {self.event_id}")

    def shutdown(self) -> None:
        """关闭事件和所有组件"""
        self.cleanup()

        # 关闭所有组件
        for component_name, component in self._components.items():
            try:
                component.shutdown()
                logger.debug(f"Shutdown component: {component_name}")
            except Exception as e:
                logger.error(f"Failed to shutdown component '{component_name}': {e}")

        self._components.clear()
        logger.debug(f"Event shutdown: {self.event_id}")

    # === 字符串表示 ===

    def __str__(self) -> str:
        """事件的字符串表示"""
        return (
            f"{self.event_type}(id={self.event_id[:8]}..., source={self.source}, timestamp={self.timestamp.isoformat()}, processed={self.processed})"
        )

    def __repr__(self) -> str:
        """事件的详细字符串表示"""
        components = list(self._components.keys())
        return f"CompositeEvent(id={self.event_id}, type={self.event_type}, source={self.source}, components={components})"
