#!/usr/bin/env python
"""
* @author: cz
* @description: 配置加载器测试
"""

import tempfile
import unittest
from pathlib import Path

from miniboot.env.config_loader import ConfigurationLoader
from miniboot.env.sources import MutablePropertySources


class ConfigurationLoaderTestCase(unittest.TestCase):
    """配置加载器测试"""

    def setUp(self):
        self.temp_dir = Path(tempfile.mkdtemp())
        self.loader = ConfigurationLoader(search_locations=[str(self.temp_dir)])

    def tearDown(self):
        # 清理临时文件
        for file in self.temp_dir.rglob("*"):
            if file.is_file():
                file.unlink()
        self.temp_dir.rmdir()

    def test_discover_config_files_yaml(self):
        """测试发现 YAML 配置文件"""
        # 创建测试配置文件
        (self.temp_dir / "application.yml").write_text(
            """
app:
  name: test-app
  version: 1.0.0
""",
            encoding="utf-8",
        )

        # 发现配置文件
        config_files = self.loader.discover_config_files()

        self.assertEqual(1, len(config_files))
        self.assertEqual("application.yml", config_files[0].name)

    def test_discover_config_files_json(self):
        """测试发现 JSON 配置文件"""
        # 创建测试配置文件
        (self.temp_dir / "application.json").write_text(
            """
{
  "app": {
    "name": "test-app",
    "version": "1.0.0"
  }
}
""",
            encoding="utf-8",
        )

        # 发现配置文件
        config_files = self.loader.discover_config_files()

        self.assertEqual(1, len(config_files))
        self.assertEqual("application.json", config_files[0].name)

    def test_discover_config_files_properties(self):
        """测试发现 Properties 配置文件"""
        # 创建测试配置文件
        (self.temp_dir / "application.properties").write_text(
            """
app.name=test-app
app.version=1.0.0
""",
            encoding="utf-8",
        )

        # 发现配置文件
        config_files = self.loader.discover_config_files()

        self.assertEqual(1, len(config_files))
        self.assertEqual("application.properties", config_files[0].name)

    def test_discover_profile_specific_configs(self):
        """测试发现 Profile 特定配置文件"""
        # 创建默认和 Profile 特定配置文件
        (self.temp_dir / "application.yml").write_text(
            """
app:
  name: default-app
""",
            encoding="utf-8",
        )

        (self.temp_dir / "application-dev.yml").write_text(
            """
app:
  name: dev-app
  debug: true
""",
            encoding="utf-8",
        )

        (self.temp_dir / "application-prod.yml").write_text(
            """
app:
  name: prod-app
  debug: false
""",
            encoding="utf-8",
        )

        # 发现配置文件(包含 Profile)
        config_files = self.loader.discover_config_files({"dev", "prod"})

        # 应该发现 3 个文件
        self.assertEqual(3, len(config_files))

        file_names = [f.name for f in config_files]
        self.assertIn("application.yml", file_names)
        self.assertIn("application-dev.yml", file_names)
        self.assertIn("application-prod.yml", file_names)

    def test_load_configuration_with_profiles(self):
        """测试加载配置(包含 Profile)"""
        # 创建配置文件
        (self.temp_dir / "application.yml").write_text(
            """
app:
  name: default-app
  version: 1.0.0
""",
            encoding="utf-8",
        )

        (self.temp_dir / "application-dev.yml").write_text(
            """
app:
  name: dev-app
  debug: true
""",
            encoding="utf-8",
        )

        # 加载配置
        property_sources = MutablePropertySources()
        self.loader.load_configuration(property_sources, {"dev"})

        # 验证属性源数量(命令行 + dev配置 + 默认配置)
        self.assertGreaterEqual(len(property_sources), 2)

    def test_resolve_path_normal(self):
        """测试解析普通文件路径"""
        path = self.loader._resolve_path("config/", "application.yml")
        expected = Path("config/application.yml")
        self.assertEqual(expected, path)

    def test_resolve_path_classpath(self):
        """测试解析 classpath 路径"""
        path = self.loader._resolve_path("classpath:/config/", "application.yml")
        expected = Path("config/application.yml")
        self.assertEqual(expected, path)

    def test_resolve_path_classpath_root(self):
        """测试解析 classpath 根路径"""
        path = self.loader._resolve_path("classpath:/", "application.yml")
        expected = Path("application.yml")
        self.assertEqual(expected, path)

    def test_multiple_config_names(self):
        """测试多个配置文件名"""
        # 创建不同名称的配置文件
        (self.temp_dir / "application.yml").write_text("app.name: app-config", encoding="utf-8")
        (self.temp_dir / "config.yml").write_text("app.name: config-config", encoding="utf-8")

        # 发现配置文件
        config_files = self.loader.discover_config_files()

        self.assertEqual(2, len(config_files))
        file_names = [f.name for f in config_files]
        self.assertIn("application.yml", file_names)
        self.assertIn("config.yml", file_names)

    def test_multiple_extensions(self):
        """测试多种文件扩展名"""
        # 创建不同扩展名的配置文件
        (self.temp_dir / "application.yml").write_text("app.name: yml-config", encoding="utf-8")
        (self.temp_dir / "application.yaml").write_text("app.name: yaml-config", encoding="utf-8")
        (self.temp_dir / "application.json").write_text('{"app": {"name": "json-config"}}', encoding="utf-8")
        (self.temp_dir / "application.properties").write_text("app.name=props-config", encoding="utf-8")

        # 发现配置文件
        config_files = self.loader.discover_config_files()

        self.assertEqual(4, len(config_files))
        extensions = [f.suffix for f in config_files]
        self.assertIn(".yml", extensions)
        self.assertIn(".yaml", extensions)
        self.assertIn(".json", extensions)
        self.assertIn(".properties", extensions)

    def test_load_configuration_empty_directory(self):
        """测试在空目录中加载配置"""
        property_sources = MutablePropertySources()
        self.loader.load_configuration(property_sources)

        # 应该至少有命令行属性源
        self.assertGreaterEqual(len(property_sources), 1)

    def test_default_search_locations(self):
        """测试默认搜索位置"""
        default_loader = ConfigurationLoader()
        # 验证搜索位置包含关键路径
        search_locations = default_loader._search_locations
        self.assertIn(".", search_locations)
        self.assertIn("./config", search_locations)
        self.assertIn("./resources", search_locations)
        # 验证至少包含用户和框架路径
        self.assertGreaterEqual(len(search_locations), 8)


if __name__ == "__main__":
    unittest.main()
