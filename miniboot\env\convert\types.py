#!/usr/bin/env python
"""
* @author: cz
* @description: 类型描述符实现
"""

import inspect
from dataclasses import dataclass
from typing import Any, Optional, get_args, get_origin


@dataclass(frozen=True)
class TypeDescriptor:
    """类型描述符

    封装类型信息,包括原始类型、泛型参数、注解等,
    为类型转换系统提供详细的类型元数据.
    """

    type: type
    """原始类型"""

    origin: Optional[type] = None
    """泛型的原始类型,如 List[str] 的原始类型是 list"""

    args: tuple = ()
    """泛型参数,如 List[str] 的参数是 (str,)"""

    annotations: dict = None
    """类型注解信息"""

    def __post_init__(self):
        """初始化后处理"""
        if self.annotations is None:
            object.__setattr__(self, "annotations", {})

        # 如果没有显式设置 origin 和 args,尝试从 type 中提取
        if self.origin is None:
            origin = get_origin(self.type)
            if origin is not None:
                object.__setattr__(self, "origin", origin)
                object.__setattr__(self, "args", get_args(self.type))

    @classmethod
    def of(cls, type_hint: type) -> "TypeDescriptor":
        """从类型提示创建类型描述符

        Args:
            type_hint: 类型提示

        Returns:
            类型描述符实例
        """
        return cls(type=type_hint)

    @classmethod
    def of_value(cls, value: Any) -> "TypeDescriptor":
        """从值创建类型描述符

        Args:
            value: 值对象

        Returns:
            类型描述符实例
        """
        return cls(type=type(value))

    @classmethod
    def of_field(cls, obj: Any, field_name: str) -> "TypeDescriptor":
        """从对象字段创建类型描述符

        Args:
            obj: 对象实例或类
            field_name: 字段名称

        Returns:
            类型描述符实例

        Raises:
            AttributeError: 字段不存在时抛出
        """
        if inspect.isclass(obj):
            # 如果是类,尝试获取类型注解
            annotations = getattr(obj, "__annotations__", {})
            if field_name in annotations:
                return cls(type=annotations[field_name])
            else:
                # 尝试获取字段的默认值类型
                if hasattr(obj, field_name):
                    field_value = getattr(obj, field_name)
                    return cls.of_value(field_value)
                else:
                    raise AttributeError(f"Field '{field_name}' not found in {obj}")
        else:
            # 如果是实例,获取字段值的类型
            if hasattr(obj, field_name):
                field_value = getattr(obj, field_name)
                return cls.of_value(field_value)
            else:
                raise AttributeError(f"Field '{field_name}' not found in {obj}")

    def is_generic(self) -> bool:
        """检查是否为泛型类型

        Returns:
            如果是泛型类型返回 True,否则返回 False
        """
        return self.origin is not None

    def is_collection(self) -> bool:
        """检查是否为集合类型

        Returns:
            如果是集合类型返回 True,否则返回 False
        """
        if self.is_generic():
            return self.origin in (list, tuple, set, frozenset)
        else:
            return self.type in (list, tuple, set, frozenset)

    def is_mapping(self) -> bool:
        """检查是否为映射类型

        Returns:
            如果是映射类型返回 True,否则返回 False
        """
        if self.is_generic():
            return self.origin is dict
        else:
            return self.type is dict

    def get_element_type(self) -> Optional["TypeDescriptor"]:
        """获取集合元素类型

        Returns:
            集合元素的类型描述符,如果不是集合类型返回 None
        """
        if self.is_collection() and self.args:
            return TypeDescriptor.of(self.args[0])
        return None

    def get_key_type(self) -> Optional["TypeDescriptor"]:
        """获取映射键类型

        Returns:
            映射键的类型描述符,如果不是映射类型返回 None
        """
        if self.is_mapping() and len(self.args) >= 1:
            return TypeDescriptor.of(self.args[0])
        return None

    def get_value_type(self) -> Optional["TypeDescriptor"]:
        """获取映射值类型

        Returns:
            映射值的类型描述符,如果不是映射类型返回 None
        """
        if self.is_mapping() and len(self.args) >= 2:
            return TypeDescriptor.of(self.args[1])
        return None

    def get_raw_type(self) -> type:
        """获取原始类型

        Returns:
            原始类型,对于泛型返回其原始类型,否则返回类型本身
        """
        return self.origin if self.origin is not None else self.type

    def is_assignable_from(self, other: "TypeDescriptor") -> bool:
        """检查是否可以从另一个类型赋值

        Args:
            other: 另一个类型描述符

        Returns:
            如果可以赋值返回 True,否则返回 False
        """
        # 相同类型
        if self.type == other.type:
            return True

        # 检查原始类型
        self_raw = self.get_raw_type()
        other_raw = other.get_raw_type()

        # 原始类型相同
        if self_raw == other_raw:
            return True

        # 检查继承关系
        try:
            return issubclass(other_raw, self_raw)
        except TypeError:
            # 如果不是类类型,返回 False
            return False

    def __str__(self) -> str:
        """字符串表示"""
        if self.is_generic():
            args_str = ", ".join(str(arg.__name__ if hasattr(arg, "__name__") else arg) for arg in self.args)
            return f"{self.origin.__name__}[{args_str}]"
        else:
            return self.type.__name__ if hasattr(self.type, "__name__") else str(self.type)

    def __repr__(self) -> str:
        """详细字符串表示"""
        return f"TypeDescriptor(type={self.type}, origin={self.origin}, args={self.args})"
