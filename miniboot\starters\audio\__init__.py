#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频播放 Starter 模块导出
"""

from .configuration import AudioAutoConfiguration
from .exceptions import AudioException, AudioPlayerException, TTSException, AudioFileException, AudioConfigurationException
from .models import AudioItem, AudioItemType
from .players import MP3Player, ChineseTTSPlayer
from .properties import AudioProperties, AudioPlayerConfig, TTSConfig
from .service import AudioService

__all__ = [
    # 核心服务
    "AudioService",
    # 配置类
    "AudioProperties",
    "AudioPlayerConfig",
    "TTSConfig",
    # 播放器
    "MP3Player",
    "ChineseTTSPlayer",
    # 数据模型
    "AudioItem",
    "AudioItemType",
    # 异常类
    "AudioException",
    "AudioPlayerException",
    "TTSException",
    "AudioFileException",
    "AudioConfigurationException",
    # 自动配置
    "AudioAutoConfiguration",
]
