#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Cache module unit tests - basic testing for cache functionality
"""

import unittest

from miniboot.starters.actuator.cache import (AdvancedEndpointCache, CacheManager,
                                               CacheStrategy)


class CacheManagerTestCase(unittest.TestCase):
    """Cache manager unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.cache_manager = CacheManager()

    def test_cache_manager_initialization(self) -> None:
        """Test cache manager initialization"""
        self.assertIsInstance(self.cache_manager, CacheManager)

    def test_cache_manager_singleton(self) -> None:
        """Test cache manager singleton pattern"""
        another_manager = CacheManager()
        self.assertIs(self.cache_manager, another_manager)

    def test_cache_manager_methods_exist(self) -> None:
        """Test cache manager has required methods"""
        self.assertTrue(hasattr(self.cache_manager, 'get'))
        self.assertTrue(hasattr(self.cache_manager, 'remove'))
        self.assertTrue(hasattr(self.cache_manager, 'clear'))
        self.assertTrue(hasattr(self.cache_manager, 'names'))

    def test_cache_manager_get_cache(self) -> None:
        """Test getting cache from manager"""
        cache = self.cache_manager.get("test_cache", max_size=50)
        self.assertIsInstance(cache, AdvancedEndpointCache)
        self.assertEqual(cache.max_size, 50)

    def test_cache_manager_remove_cache(self) -> None:
        """Test removing cache from manager"""
        # Create cache
        self.cache_manager.get("test_cache")
        self.assertIn("test_cache", self.cache_manager.names())
        
        # Remove cache
        self.cache_manager.remove("test_cache")
        self.assertNotIn("test_cache", self.cache_manager.names())

    def test_cache_manager_clear_all(self) -> None:
        """Test clearing all caches"""
        # Create multiple caches
        self.cache_manager.get("cache1")
        self.cache_manager.get("cache2")
        self.assertEqual(len(self.cache_manager.names()), 2)
        
        # Clear all
        self.cache_manager.clear()
        self.assertEqual(len(self.cache_manager.names()), 0)


class AdvancedEndpointCacheTestCase(unittest.TestCase):
    """Advanced endpoint cache unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.cache = AdvancedEndpointCache(
            max_size=100,
            strategy=CacheStrategy.LRU
        )

    def test_cache_initialization(self) -> None:
        """Test cache initialization"""
        self.assertIsInstance(self.cache, AdvancedEndpointCache)
        self.assertEqual(self.cache.max_size, 100)
        self.assertEqual(self.cache.strategy, CacheStrategy.LRU)

    def test_cache_has_required_methods(self) -> None:
        """Test cache has required async methods"""
        self.assertTrue(hasattr(self.cache, 'get'))
        self.assertTrue(hasattr(self.cache, 'put'))
        self.assertTrue(hasattr(self.cache, 'remove'))
        self.assertTrue(hasattr(self.cache, 'clear'))
        self.assertTrue(hasattr(self.cache, 'get_stats'))

    def test_cache_stats_initialization(self) -> None:
        """Test cache statistics initialization"""
        stats = self.cache.get_stats()
        self.assertIsNotNone(stats)
        self.assertEqual(stats.max_size, 100)

    def test_cache_info(self) -> None:
        """Test cache info retrieval"""
        info = self.cache.get_cache_info()
        self.assertIsInstance(info, dict)
        self.assertIn('max_size', info)
        self.assertIn('strategy', info)
        self.assertIn('stats', info)
        self.assertEqual(info['max_size'], 100)
        self.assertEqual(info['strategy'], CacheStrategy.LRU.value)


class CacheStrategyTestCase(unittest.TestCase):
    """Cache strategy unit test suite"""

    def test_cache_strategy_enum(self) -> None:
        """Test cache strategy enum values"""
        self.assertEqual(CacheStrategy.LRU.value, "lru")
        self.assertEqual(CacheStrategy.LFU.value, "lfu")
        self.assertEqual(CacheStrategy.FIFO.value, "fifo")
        self.assertEqual(CacheStrategy.TTL.value, "ttl")
        self.assertEqual(CacheStrategy.ADAPTIVE.value, "adaptive")

    def test_cache_with_different_strategies(self) -> None:
        """Test cache creation with different strategies"""
        strategies = [
            CacheStrategy.LRU,
            CacheStrategy.LFU,
            CacheStrategy.FIFO,
            CacheStrategy.TTL,
            CacheStrategy.ADAPTIVE
        ]
        
        for strategy in strategies:
            with self.subTest(strategy=strategy):
                cache = AdvancedEndpointCache(
                    max_size=50,
                    strategy=strategy
                )
                self.assertEqual(cache.strategy, strategy)

    def test_cache_default_parameters(self) -> None:
        """Test cache creation with default parameters"""
        cache = AdvancedEndpointCache()
        self.assertEqual(cache.max_size, 1000)
        self.assertEqual(cache.strategy, CacheStrategy.LRU)
        self.assertIsNone(cache.default_ttl)
        self.assertTrue(cache.enable_stats)


if __name__ == "__main__":
    unittest.main()
