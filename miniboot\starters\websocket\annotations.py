#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket Starter 注解系统
"""

from typing import Any, Callable, Optional


def WebSocketController(path: str = ""):  # noqa: N802
    """WebSocket 控制器装饰器

    标记一个类为 WebSocket 控制器,用于处理 WebSocket 连接和消息.

    Args:
        path: WebSocket 路径,默认为空字符串

    Example:
        @WebSocketController("/chat")
        class ChatController:
            pass
    """

    def decorator(cls: type) -> type:
        cls.__websocket_controller__ = True
        cls.__websocket_path__ = path
        return cls

    return decorator


def WebSocketOnConnect():  # noqa: N802
    """WebSocket 连接建立事件装饰器

    标记方法处理 WebSocket 连接建立事件.被装饰的方法将在客户端
    成功连接到 WebSocket 服务器时被调用.

    Example:
        @WebSocketOnConnect()
        async def on_connect(self, session: WebSocketSession):
            print(f"Client {session.get_id()} connected")
    """

    def decorator(func: Callable) -> Callable:
        func.__websocket_on_connect__ = True
        return func

    return decorator


def WebSocketOnDisconnect():  # noqa: N802
    """WebSocket 连接断开事件装饰器

    标记方法处理 WebSocket 连接断开事件.被装饰的方法将在客户端
    断开 WebSocket 连接时被调用.

    Example:
        @WebSocketOnDisconnect()
        async def on_disconnect(self, session: WebSocketSession):
            print(f"Client {session.get_id()} disconnected")
    """

    def decorator(func: Callable) -> Callable:
        func.__websocket_on_disconnect__ = True
        return func

    return decorator


def WebSocketOnMessage(message_type: str = "any"):  # noqa: N802
    """WebSocket 消息处理装饰器

    标记方法处理 WebSocket 消息.被装饰的方法将在接收到指定类型的
    消息时被调用.

    Args:
        message_type: 消息类型,支持 "text", "binary", "json", "any"

    Example:
        @WebSocketOnMessage("text")
        async def on_text_message(self, session: WebSocketSession, message: str):
            await session.send_text(f"Echo: {message}")

        @WebSocketOnMessage("json")
        async def on_json_message(self, session: WebSocketSession, message: dict):
            await session.send_json({"echo": message})
    """

    def decorator(func: Callable) -> Callable:
        func.__websocket_on_message__ = True
        func.__websocket_message_type__ = message_type
        return func

    return decorator


def WebSocketOnError():  # noqa: N802
    """WebSocket 错误处理装饰器

    标记方法处理 WebSocket 错误.被装饰的方法将在 WebSocket 连接
    或消息处理过程中发生错误时被调用.

    Example:
        @WebSocketOnError()
        async def on_error(self, session: WebSocketSession, error: Exception):
            print(f"Error in session {session.get_id()}: {error}")
    """

    def decorator(func: Callable) -> Callable:
        func.__websocket_on_error__ = True
        return func

    return decorator


def WebSocketSendTo(target: str):  # noqa: N802
    """WebSocket 消息发送装饰器

    标记方法的返回值发送给指定目标.被装饰的方法的返回值将自动
    发送给指定的目标.

    Args:
        target: 发送目标,支持 "all", "sender", "user", "room"

    Example:
        @WebSocketSendTo("all")
        async def broadcast_message(self, message: str) -> str:
            return f"Broadcast: {message}"

        @WebSocketSendTo("sender")
        async def echo_message(self, message: str) -> str:
            return f"Echo: {message}"
    """

    def decorator(func: Callable) -> Callable:
        func.__websocket_send_to__ = True
        func.__websocket_send_target__ = target
        return func

    return decorator


def WebSocketMessageMapping(path: str = "", message_type: str = "any"):  # noqa: N802
    """WebSocket 消息映射装饰器

    组合装饰器,同时指定消息路径和类型.用于更精确的消息路由.

    Args:
        path: 消息路径
        message_type: 消息类型

    Example:
        @WebSocketMessageMapping("/chat/send", "json")
        async def handle_chat_message(self, session: WebSocketSession, message: dict):
            pass
    """

    def decorator(func: Callable) -> Callable:
        func.__websocket_message_mapping__ = True
        func.__websocket_message_path__ = path
        func.__websocket_message_type__ = message_type
        return func

    return decorator


def WebSocketEventListener(event_type: str):  # noqa: N802
    """WebSocket 事件监听装饰器

    标记方法监听特定的 WebSocket 事件.

    Args:
        event_type: 事件类型

    Example:
        @WebSocketEventListener("user_join")
        async def on_user_join(self, session: WebSocketSession, event_data: dict):
            pass
    """

    def decorator(func: Callable) -> Callable:
        func.__websocket_event_listener__ = True
        func.__websocket_event_type__ = event_type
        return func

    return decorator


# 辅助函数
def is_websocket_controller(cls: type) -> bool:
    """检查类是否为 WebSocket 控制器

    Args:
        cls: 要检查的类

    Returns:
        bool: 如果是 WebSocket 控制器返回 True
    """
    return hasattr(cls, "__websocket_controller__") and cls.__websocket_controller__


def get_websocket_path(cls: type) -> Optional[str]:
    """获取 WebSocket 控制器的路径

    Args:
        cls: WebSocket 控制器类

    Returns:
        Optional[str]: WebSocket 路径,如果不是控制器返回 None
    """
    if is_websocket_controller(cls):
        return getattr(cls, "__websocket_path__", "")
    return None


def get_websocket_handlers(controller_instance: Any) -> dict:
    """获取 WebSocket 控制器的所有处理器方法

    Args:
        controller_instance: 控制器实例

    Returns:
        dict: 处理器方法字典,键为处理器类型,值为方法列表
    """
    handlers = {"connect": [], "disconnect": [], "message": [], "error": [], "send_to": []}

    for method_name in dir(controller_instance):
        method = getattr(controller_instance, method_name)

        if hasattr(method, "__websocket_on_connect__"):
            handlers["connect"].append(method)

        if hasattr(method, "__websocket_on_disconnect__"):
            handlers["disconnect"].append(method)

        if hasattr(method, "__websocket_on_message__"):
            handlers["message"].append(method)

        if hasattr(method, "__websocket_on_error__"):
            handlers["error"].append(method)

        if hasattr(method, "__websocket_send_to__"):
            handlers["send_to"].append(method)

    return handlers
