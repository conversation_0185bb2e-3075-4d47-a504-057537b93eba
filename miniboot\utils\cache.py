#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 通用缓存基础设施 - 为整个框架提供统一的缓存接口

提供统一的缓存基类和实现，可以在整个框架中复用，
减少重复的缓存实现代码，统一缓存行为和性能监控。
"""

import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Generic, Optional, TypeVar
from weakref import WeakKeyDictionary

K = TypeVar("K")  # Key type
V = TypeVar("V")  # Value type


@dataclass
class CacheStats:
    """缓存统计信息"""

    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size: int = 0
    max_size: int = 0
    hit_rate: float = 0.0
    created_at: float = field(default_factory=time.time)

    def update_hit_rate(self) -> None:
        """更新命中率"""
        total = self.hits + self.misses
        self.hit_rate = self.hits / total if total > 0 else 0.0

    def reset(self) -> None:
        """重置统计信息"""
        self.hits = 0
        self.misses = 0
        self.evictions = 0
        self.hit_rate = 0.0
        self.created_at = time.time()


class BaseCache(Generic[K, V], ABC):
    """
    统一缓存基类

    提供所有缓存实现的通用接口和基础功能：
    - 线程安全
    - 统计信息
    - 基础的缓存操作

    子类需要实现具体的缓存策略和存储逻辑。
    """

    def __init__(self, max_size: int = 1000, enable_stats: bool = True):
        """
        初始化缓存基类

        Args:
            max_size: 最大缓存大小
            enable_stats: 是否启用统计
        """
        self.max_size = max_size
        self.enable_stats = enable_stats
        self._lock = threading.RLock()
        self._stats = CacheStats(max_size=max_size)

    @abstractmethod
    def _do_get(self, key: K) -> Optional[V]:
        """
        实际的获取操作（子类实现）

        Args:
            key: 缓存键

        Returns:
            缓存值，如果不存在返回None
        """
        pass

    @abstractmethod
    def _do_put(self, key: K, value: V) -> None:
        """
        实际的存储操作（子类实现）

        Args:
            key: 缓存键
            value: 缓存值
        """
        pass

    @abstractmethod
    def _do_remove(self, key: K) -> bool:
        """
        实际的删除操作（子类实现）

        Args:
            key: 缓存键

        Returns:
            是否成功删除
        """
        pass

    @abstractmethod
    def _do_clear(self) -> None:
        """实际的清空操作（子类实现）"""
        pass

    @abstractmethod
    def _do_size(self) -> int:
        """获取实际缓存大小（子类实现）"""
        pass

    def get(self, key: K) -> Optional[V]:
        """
        获取缓存值

        Args:
            key: 缓存键

        Returns:
            缓存值，如果不存在返回None
        """
        with self._lock:
            value = self._do_get(key)

            if self.enable_stats:
                if value is not None:
                    self._stats.hits += 1
                else:
                    self._stats.misses += 1
                self._stats.update_hit_rate()

            return value

    def put(self, key: K, value: V) -> None:
        """
        存储缓存值

        Args:
            key: 缓存键
            value: 缓存值
        """
        with self._lock:
            self._do_put(key, value)

            if self.enable_stats:
                self._stats.size = self._do_size()

    def remove(self, key: K) -> bool:
        """
        删除缓存项

        Args:
            key: 缓存键

        Returns:
            是否成功删除
        """
        with self._lock:
            removed = self._do_remove(key)

            if self.enable_stats and removed:
                self._stats.size = self._do_size()

            return removed

    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._do_clear()

            if self.enable_stats:
                self._stats.size = 0
                self._stats.evictions += 1

    def size(self) -> int:
        """获取缓存大小"""
        with self._lock:
            return self._do_size()

    def is_empty(self) -> bool:
        """检查缓存是否为空"""
        return self.size() == 0

    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._lock:
            # 更新当前大小
            self._stats.size = self._do_size()
            return self._stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._lock:
            self._stats.reset()


class WeakReferenceCache(BaseCache[type, Any]):
    """
    弱引用缓存实现

    使用弱引用作为键，当类被垃圾回收时自动清理缓存。
    适用于以类型为键的缓存场景。
    """

    def __init__(self, max_size: int = 1000, enable_stats: bool = True):
        super().__init__(max_size, enable_stats)
        self._cache: WeakKeyDictionary[type, Any] = WeakKeyDictionary()

    def _do_get(self, key: type) -> Optional[Any]:
        return self._cache.get(key)

    def _do_put(self, key: type, value: Any) -> None:
        self._cache[key] = value

    def _do_remove(self, key: type) -> bool:
        if key in self._cache:
            del self._cache[key]
            return True
        return False

    def _do_clear(self) -> None:
        self._cache.clear()

    def _do_size(self) -> int:
        return len(self._cache)


class LRUCache(BaseCache[Any, Any]):
    """
    LRU缓存实现

    使用最近最少使用策略进行缓存淘汰。
    """

    def __init__(self, max_size: int = 1000, enable_stats: bool = True):
        super().__init__(max_size, enable_stats)
        self._cache: dict[Any, Any] = {}
        self._access_order: dict[Any, float] = {}

    def _do_get(self, key: Any) -> Optional[Any]:
        if key in self._cache:
            # 更新访问时间
            self._access_order[key] = time.time()
            return self._cache[key]
        return None

    def _do_put(self, key: Any, value: Any) -> None:
        # 如果已存在，直接更新
        if key in self._cache:
            self._cache[key] = value
            self._access_order[key] = time.time()
            return

        # 检查是否需要淘汰
        if len(self._cache) >= self.max_size:
            self._evict_lru()

        # 添加新项
        self._cache[key] = value
        self._access_order[key] = time.time()

    def _do_remove(self, key: Any) -> bool:
        if key in self._cache:
            del self._cache[key]
            del self._access_order[key]
            return True
        return False

    def _do_clear(self) -> None:
        self._cache.clear()
        self._access_order.clear()

    def _do_size(self) -> int:
        return len(self._cache)

    def _evict_lru(self) -> None:
        """淘汰最近最少使用的项"""
        if not self._access_order:
            return

        # 找到最旧的项
        lru_key = min(self._access_order.keys(), key=lambda k: self._access_order[k])

        # 删除最旧的项
        del self._cache[lru_key]
        del self._access_order[lru_key]

        if self.enable_stats:
            self._stats.evictions += 1


class TTLCache(BaseCache[Any, Any]):
    """
    TTL缓存实现

    支持过期时间的缓存，自动清理过期项。
    """

    def __init__(self, max_size: int = 1000, default_ttl: float = 300.0, enable_stats: bool = True):
        super().__init__(max_size, enable_stats)
        self.default_ttl = default_ttl
        self._cache: dict[Any, Any] = {}
        self._expire_times: dict[Any, float] = {}

    def _do_get(self, key: Any) -> Optional[Any]:
        if key in self._cache:
            # 检查是否过期
            if time.time() > self._expire_times[key]:
                # 过期，删除
                del self._cache[key]
                del self._expire_times[key]
                return None
            return self._cache[key]
        return None

    def _do_put(self, key: Any, value: Any, ttl: Optional[float] = None) -> None:
        # 使用指定TTL或默认TTL
        actual_ttl = ttl if ttl is not None else self.default_ttl
        expire_time = time.time() + actual_ttl

        # 如果缓存已满，清理过期项或淘汰最旧的项
        if len(self._cache) >= self.max_size and key not in self._cache:
            self._cleanup_expired()
            if len(self._cache) >= self.max_size:
                self._evict_oldest()

        self._cache[key] = value
        self._expire_times[key] = expire_time

    def _do_remove(self, key: Any) -> bool:
        if key in self._cache:
            del self._cache[key]
            del self._expire_times[key]
            return True
        return False

    def _do_clear(self) -> None:
        self._cache.clear()
        self._expire_times.clear()

    def _do_size(self) -> int:
        # 清理过期项后返回大小
        self._cleanup_expired()
        return len(self._cache)

    def _cleanup_expired(self) -> None:
        """清理过期项"""
        current_time = time.time()
        expired_keys = [key for key, expire_time in self._expire_times.items() if current_time > expire_time]

        for key in expired_keys:
            del self._cache[key]
            del self._expire_times[key]
            if self.enable_stats:
                self._stats.evictions += 1

    def _evict_oldest(self) -> None:
        """淘汰最旧的项"""
        if not self._expire_times:
            return

        # 找到最早过期的项（即最旧的项）
        oldest_key = min(self._expire_times.keys(), key=lambda k: self._expire_times[k])

        del self._cache[oldest_key]
        del self._expire_times[oldest_key]

        if self.enable_stats:
            self._stats.evictions += 1
