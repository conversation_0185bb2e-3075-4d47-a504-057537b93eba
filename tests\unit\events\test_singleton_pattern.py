#!/usr/bin/env python
"""
* @author: cz
* @description: 单例模式测试

测试EventIdGenerator的单例实现，验证线程安全性和性能。
"""

import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor

from miniboot.events.base import EventIdGenerator
from miniboot.utils import SingletonMeta


class TestSingletonMeta(unittest.TestCase):
    """单例元类测试"""

    def setUp(self):
        """设置测试"""
        # 重置单例状态
        SingletonMeta.reset_instance(EventIdGenerator)

    def test_singleton_basic(self):
        """测试基本单例功能"""
        # 创建多个实例
        gen1 = EventIdGenerator()
        gen2 = EventIdGenerator()
        gen3 = EventIdGenerator()

        # 应该是同一个实例
        self.assertIs(gen1, gen2)
        self.assertIs(gen2, gen3)
        self.assertIs(gen1, gen3)

    def test_singleton_thread_safety(self):
        """测试单例的线程安全性"""
        instances = []
        lock = threading.Lock()

        def create_instance():
            """创建实例"""
            instance = EventIdGenerator()
            with lock:
                instances.append(instance)

        # 创建多个线程同时创建实例
        threads = []
        for _ in range(20):
            thread = threading.Thread(target=create_instance)
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证所有实例都是同一个
        self.assertEqual(len(instances), 20)
        first_instance = instances[0]
        for instance in instances:
            self.assertIs(instance, first_instance)

    def test_singleton_with_thread_pool(self):
        """测试线程池环境下的单例"""
        instances = []

        def get_instance():
            """获取实例"""
            return EventIdGenerator()

        # 使用线程池执行
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(get_instance) for _ in range(50)]

            for future in futures:
                instances.append(future.result())

        # 验证所有实例都是同一个
        first_instance = instances[0]
        for instance in instances:
            self.assertIs(instance, first_instance)

    def test_singleton_reset(self):
        """测试单例重置功能"""
        # 创建第一个实例并生成多个ID
        gen1 = EventIdGenerator()
        gen1.generate_id()
        id2 = gen1.generate_id()  # 生成第二个ID确保序列号递增

        # 重置单例
        SingletonMeta.reset_instance(EventIdGenerator)

        # 稍微等待确保时间戳可能不同
        time.sleep(0.001)

        # 创建新实例
        gen2 = EventIdGenerator()
        id3 = gen2.generate_id()

        # 应该是不同的实例
        self.assertIsNot(gen1, gen2)

        # 新实例的第一个ID序列号应该是000001
        self.assertTrue(id3.endswith("000001"))

        # 验证ID确实不同
        self.assertNotEqual(id2, id3)

    def test_singleton_consistency(self):
        """测试单例一致性"""
        # 通过不同方式获取实例
        gen1 = EventIdGenerator()
        gen2 = EventIdGenerator()

        # 应该是同一个实例
        self.assertIs(gen1, gen2)

        # 重置后应该获得新实例
        SingletonMeta.reset_instance(EventIdGenerator)
        gen3 = EventIdGenerator()

        self.assertIsNot(gen1, gen3)

    def test_singleton_performance(self):
        """测试单例性能"""
        num_calls = 10000

        # 测试直接构造函数调用
        start_time = time.time()
        for _ in range(num_calls):
            EventIdGenerator()
        direct_time = time.time() - start_time

        # 重置以确保公平比较
        SingletonMeta.reset_instance(EventIdGenerator)

        # 测试通过重复构造函数调用
        start_time = time.time()
        for _ in range(num_calls):
            EventIdGenerator()
        repeat_time = time.time() - start_time

        print(f"直接构造函数调用 {num_calls} 次耗时: {direct_time:.3f}秒")
        print(f"重复构造函数调用 {num_calls} 次耗时: {repeat_time:.3f}秒")

        # 性能差异应该不大
        self.assertLess(abs(direct_time - repeat_time), 0.1)

    def test_singleton_id_generation_consistency(self):
        """测试单例ID生成的一致性"""
        # 通过不同方式获取生成器
        gen1 = EventIdGenerator()
        gen2 = EventIdGenerator()

        # 生成ID应该是连续的
        id1 = gen1.generate_id()
        id2 = gen2.generate_id()

        # 验证ID是连续的（如果在同一秒内）
        if id1[:14] == id2[:14]:  # 同一秒
            seq1 = int(id1[14:])
            seq2 = int(id2[14:])
            self.assertEqual(seq2, seq1 + 1)

    def test_singleton_state_persistence(self):
        """测试单例状态持久性"""
        # 获取生成器并生成一些ID
        gen1 = EventIdGenerator()
        ids_before = [gen1.generate_id() for _ in range(5)]

        # 通过不同方式获取生成器
        gen2 = EventIdGenerator()
        ids_after = [gen2.generate_id() for _ in range(5)]

        # 验证是同一个实例
        self.assertIs(gen1, gen2)

        # 验证状态持续性（ID应该继续递增）
        all_ids = ids_before + ids_after
        self.assertEqual(len(set(all_ids)), 10)  # 所有ID都应该唯一


class TestSingletonInheritance(unittest.TestCase):
    """测试单例继承"""

    def setUp(self):
        """设置测试"""
        SingletonMeta.reset_instance(EventIdGenerator)

    class CustomEventIdGenerator(EventIdGenerator):
        """自定义事件ID生成器"""

        def __init__(self):
            super().__init__()
            self.custom_prefix = "CUSTOM_"

        def generate_id(self) -> str:
            """生成带自定义前缀的ID"""
            base_id = super().generate_id()
            return f"{self.custom_prefix}{base_id}"

    def test_inheritance_singleton(self):
        """测试继承类的单例行为"""
        # 基类单例
        base1 = EventIdGenerator()
        base2 = EventIdGenerator()
        self.assertIs(base1, base2)

        # 子类单例（应该是独立的）
        custom1 = self.CustomEventIdGenerator()
        custom2 = self.CustomEventIdGenerator()
        self.assertIs(custom1, custom2)

        # 基类和子类应该是不同的实例
        self.assertIsNot(base1, custom1)

    def test_inheritance_functionality(self):
        """测试继承类的功能"""
        custom_gen = self.CustomEventIdGenerator()
        custom_id = custom_gen.generate_id()

        # 应该有自定义前缀
        self.assertTrue(custom_id.startswith("CUSTOM_"))

        # 移除前缀后应该是有效的ID格式
        base_id = custom_id[7:]  # 移除 "CUSTOM_" 前缀
        self.assertTrue(base_id.isdigit())
        self.assertEqual(len(base_id), 20)


class TestSingletonEdgeCases(unittest.TestCase):
    """测试单例边界情况"""

    def setUp(self):
        """设置测试"""
        SingletonMeta.reset_instance(EventIdGenerator)

    def test_rapid_creation_and_reset(self):
        """测试快速创建和重置"""
        for i in range(100):
            gen = EventIdGenerator()
            event_id = gen.generate_id()
            self.assertIsNotNone(event_id)

            if i % 10 == 0:  # 每10次重置一次
                SingletonMeta.reset_instance(EventIdGenerator)

    def test_concurrent_reset(self):
        """测试并发重置"""
        results = []
        lock = threading.Lock()

        def create_and_reset():
            """创建实例并重置"""
            for _ in range(10):
                gen = EventIdGenerator()
                event_id = gen.generate_id()

                with lock:
                    results.append(event_id)

                # 随机重置
                if len(results) % 5 == 0:
                    SingletonMeta.reset_instance(EventIdGenerator)

        # 启动多个线程
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=create_and_reset)
            threads.append(thread)
            thread.start()

        # 等待完成
        for thread in threads:
            thread.join()

        # 验证所有ID都是有效的
        for event_id in results:
            self.assertTrue(event_id.isdigit())
            self.assertEqual(len(event_id), 20)


if __name__ == "__main__":
    unittest.main(verbosity=2)
