"""
Mini-Boot Actuator 非阻塞架构性能基准测试

验证非阻塞架构相对于传统架构的性能提升：
- 启动时间: < 50ms (目标: 99.9% 提升)
- 内存占用: 44% 减少
- 响应延迟: 80% 改善
- 并发处理: 4x 性能提升
- 稳定性: 高并发场景验证
"""

import asyncio
import gc
import statistics
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Tuple
from unittest.mock import MagicMock, patch

import psutil

from miniboot.starters.actuator.collectors.health import AsyncHealthCollector
from miniboot.starters.actuator.collectors.metrics import AppMetricsCollector
from miniboot.starters.actuator.collectors.system import \
    AsyncSystemMetricsCollector
from miniboot.starters.actuator.context import ActuatorContext
from miniboot.starters.actuator.endpoints.health import AsyncHealthEndpoint
from miniboot.starters.actuator.endpoints.info import AsyncInfoEndpoint
from miniboot.starters.actuator.endpoints.metrics import AsyncMetricsEndpoint

# DEPRECATED: from miniboot.actuator.integration import ActuatorIntegration


class ActuatorBenchmarkTestCase(unittest.TestCase):
    """Actuator 性能基准测试基类"""

    def setUp(self):
        """测试前准备"""
        # 强制垃圾回收，确保内存测试准确性
        gc.collect()

        # 记录基准内存使用
        self.process = psutil.Process()
        self.baseline_memory = self.process.memory_info().rss

        # 性能指标收集
        self.metrics = {"startup_times": [], "memory_usage": [], "response_times": [], "throughput": [], "concurrent_performance": []}

    def tearDown(self):
        """测试后清理"""
        gc.collect()

    def measure_time(self, func, *args, **kwargs) -> Tuple[Any, float]:
        """测量函数执行时间"""
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = (end_time - start_time) * 1000  # 转换为毫秒
        return result, execution_time

    def measure_memory(self) -> int:
        """测量当前内存使用量（字节）"""
        return self.process.memory_info().rss

    def calculate_memory_increase(self, before: int, after: int) -> int:
        """计算内存增长量"""
        return after - before


class StartupBenchmarkTestCase(ActuatorBenchmarkTestCase):
    """启动时间基准测试 - 验证 < 50ms 非阻塞启动目标"""

    def test_actuator_context_startup_time(self):
        """测试 ActuatorContext 启动时间"""
        startup_times = []

        # 执行多次启动测试，获取平均值
        for i in range(10):
            with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
                context = ActuatorContext()

                # 测量启动时间
                _, startup_time = self.measure_time(lambda: asyncio.run(context.start()))
                startup_times.append(startup_time)

                # 清理
                asyncio.run(context.shutdown_async())

        # 计算统计数据
        avg_startup_time = statistics.mean(startup_times)
        min_startup_time = min(startup_times)
        max_startup_time = max(startup_times)

        print(f"\n🚀 ActuatorContext 启动时间基准测试:")
        print(f"   平均启动时间: {avg_startup_time:.2f}ms")
        print(f"   最快启动时间: {min_startup_time:.2f}ms")
        print(f"   最慢启动时间: {max_startup_time:.2f}ms")

        # 验证目标: < 50ms
        self.assertLess(avg_startup_time, 50.0, f"平均启动时间 {avg_startup_time:.2f}ms 超过目标 50ms")
        self.assertLess(max_startup_time, 100.0, f"最大启动时间 {max_startup_time:.2f}ms 过长")

    def test_actuator_integration_startup_time(self):
        """测试 ActuatorIntegration 集成启动时间"""
        startup_times = []

        for i in range(10):
            # 模拟 FastAPI 应用
            mock_app = MagicMock()
            mock_app.mount = MagicMock()

            integration = ActuatorIntegration()

            # 测量集成启动时间
            _, startup_time = self.measure_time(lambda: asyncio.run(integration.integrate_async(mock_app)))
            startup_times.append(startup_time)

        avg_startup_time = statistics.mean(startup_times)

        print(f"\n⚡ ActuatorIntegration 集成启动时间:")
        print(f"   平均集成时间: {avg_startup_time:.2f}ms")

        # 验证目标: < 10ms (集成应该非常快)
        self.assertLess(avg_startup_time, 10.0, f"平均集成时间 {avg_startup_time:.2f}ms 超过目标 10ms")

    def test_endpoint_registration_startup_time(self):
        """测试端点注册启动时间"""
        context = ActuatorContext()

        # 准备多个端点
        endpoints = [AsyncHealthEndpoint(), AsyncMetricsEndpoint(), AsyncInfoEndpoint()]

        registration_times = []

        for endpoint in endpoints:
            # 测量单个端点注册时间
            _, reg_time = self.measure_time(lambda ep=endpoint: context.register_endpoint(ep))
            registration_times.append(reg_time)

        total_registration_time = sum(registration_times)
        avg_registration_time = statistics.mean(registration_times)

        print(f"\n📝 端点注册启动时间:")
        print(f"   总注册时间: {total_registration_time:.2f}ms")
        print(f"   平均单端点注册时间: {avg_registration_time:.2f}ms")

        # 验证目标: 总注册时间 < 20ms
        self.assertLess(total_registration_time, 20.0, f"总端点注册时间 {total_registration_time:.2f}ms 超过目标 20ms")


class MemoryBenchmarkTestCase(ActuatorBenchmarkTestCase):
    """内存占用基准测试 - 验证 44% 内存减少目标"""

    def test_actuator_context_memory_usage(self):
        """测试 ActuatorContext 内存使用"""
        initial_memory = self.measure_memory()

        # 创建 ActuatorContext
        with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
            context = ActuatorContext()
            asyncio.run(context.start())

            after_init_memory = self.measure_memory()
            memory_increase = self.calculate_memory_increase(initial_memory, after_init_memory)

            print(f"\n💾 ActuatorContext 内存使用:")
            print(f"   初始内存: {initial_memory / 1024 / 1024:.2f}MB")
            print(f"   初始化后内存: {after_init_memory / 1024 / 1024:.2f}MB")
            print(f"   内存增长: {memory_increase / 1024 / 1024:.2f}MB")

            # 验证目标: 内存增长 < 10MB
            self.assertLess(memory_increase / 1024 / 1024, 10.0, f"内存增长 {memory_increase / 1024 / 1024:.2f}MB 超过目标 10MB")

            # 清理
            asyncio.run(context.shutdown_async())

    def test_endpoint_memory_footprint(self):
        """测试端点内存占用"""
        initial_memory = self.measure_memory()

        # 创建多个端点实例
        endpoints = []
        for i in range(10):
            endpoints.extend([AsyncHealthEndpoint(), AsyncMetricsEndpoint(), AsyncInfoEndpoint()])

        after_creation_memory = self.measure_memory()
        memory_per_endpoint = self.calculate_memory_increase(initial_memory, after_creation_memory) / len(endpoints)

        print(f"\n🎯 端点内存占用:")
        print(f"   创建 {len(endpoints)} 个端点")
        print(f"   总内存增长: {(after_creation_memory - initial_memory) / 1024 / 1024:.2f}MB")
        print(f"   平均每端点内存: {memory_per_endpoint / 1024:.2f}KB")

        # 验证目标: 每端点内存 < 100KB
        self.assertLess(memory_per_endpoint / 1024, 100.0, f"每端点内存 {memory_per_endpoint / 1024:.2f}KB 超过目标 100KB")


class ResponseBenchmarkTestCase(ActuatorBenchmarkTestCase):
    """响应延迟基准测试 - 验证 80% 响应时间改善目标"""

    def setUp(self):
        super().setUp()
        # 创建测试用的 ActuatorContext
        with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
            self.context = ActuatorContext()
            asyncio.run(self.context.initialize_async())

    def tearDown(self):
        asyncio.run(self.context.shutdown_async())
        super().tearDown()

    def test_health_endpoint_response_time(self):
        """测试健康检查端点响应时间"""
        response_times = []

        for i in range(50):
            # 测量健康检查响应时间
            _, response_time = self.measure_time(lambda: asyncio.run(self.context.execute_endpoint_async("health")))
            response_times.append(response_time)

        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile

        print(f"\n🏥 健康检查端点响应时间:")
        print(f"   平均响应时间: {avg_response_time:.2f}ms")
        print(f"   95%响应时间: {p95_response_time:.2f}ms")

        # 验证目标: 平均响应时间 < 10ms, 95% < 20ms
        self.assertLess(avg_response_time, 10.0, f"平均响应时间 {avg_response_time:.2f}ms 超过目标 10ms")
        self.assertLess(p95_response_time, 20.0, f"95%响应时间 {p95_response_time:.2f}ms 超过目标 20ms")

    def test_metrics_endpoint_response_time(self):
        """测试指标端点响应时间"""
        response_times = []

        for i in range(30):
            _, response_time = self.measure_time(lambda: asyncio.run(self.context.execute_endpoint_async("metrics")))
            response_times.append(response_time)

        avg_response_time = statistics.mean(response_times)

        print(f"\n📊 指标端点响应时间:")
        print(f"   平均响应时间: {avg_response_time:.2f}ms")

        # 验证目标: 平均响应时间 < 50ms (指标收集较复杂)
        self.assertLess(avg_response_time, 50.0, f"平均响应时间 {avg_response_time:.2f}ms 超过目标 50ms")


class ConcurrencyBenchmarkTestCase(ActuatorBenchmarkTestCase):
    """并发处理基准测试 - 验证 4x 并发性能提升目标"""

    def setUp(self):
        super().setUp()
        with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
            self.context = ActuatorContext()
            asyncio.run(self.context.initialize_async())

    def tearDown(self):
        asyncio.run(self.context.shutdown_async())
        super().tearDown()

    def test_concurrent_health_checks(self):
        """测试并发健康检查性能"""
        concurrent_levels = [1, 5, 10, 20, 50]
        results = {}

        for concurrency in concurrent_levels:
            start_time = time.perf_counter()

            # 使用 asyncio.gather 进行并发测试
            async def run_concurrent_health_checks():
                tasks = []
                for _ in range(concurrency):
                    task = self.context.execute_endpoint_async("health")
                    tasks.append(task)
                return await asyncio.gather(*tasks, return_exceptions=True)

            responses = asyncio.run(run_concurrent_health_checks())
            end_time = time.perf_counter()

            total_time = (end_time - start_time) * 1000  # 毫秒
            throughput = concurrency / (total_time / 1000)  # 请求/秒

            results[concurrency] = {
                "total_time": total_time,
                "throughput": throughput,
                "success_count": sum(1 for r in responses if not isinstance(r, Exception)),
            }

            print(f"\n🔄 并发健康检查 (并发数: {concurrency}):")
            print(f"   总耗时: {total_time:.2f}ms")
            print(f"   吞吐量: {throughput:.2f} 请求/秒")
            print(f"   成功率: {results[concurrency]['success_count']}/{concurrency}")

        # 验证并发性能提升
        single_throughput = results[1]["throughput"]
        high_concurrency_throughput = results[50]["throughput"]
        performance_ratio = high_concurrency_throughput / single_throughput

        print(f"\n📈 并发性能提升:")
        print(f"   单请求吞吐量: {single_throughput:.2f} 请求/秒")
        print(f"   高并发吞吐量: {high_concurrency_throughput:.2f} 请求/秒")
        print(f"   性能提升倍数: {performance_ratio:.2f}x")

        # 验证目标: 至少 2x 性能提升 (考虑到测试环境限制)
        self.assertGreater(performance_ratio, 2.0, f"并发性能提升 {performance_ratio:.2f}x 未达到目标 2x")

    def test_mixed_endpoint_concurrency(self):
        """测试混合端点并发访问"""
        endpoints = ["health", "metrics", "info"]
        concurrent_requests = 30

        start_time = time.perf_counter()

        async def run_mixed_concurrent_requests():
            tasks = []
            for i in range(concurrent_requests):
                endpoint = endpoints[i % len(endpoints)]
                task = self.context.execute_endpoint_async(endpoint)
                tasks.append(task)
            return await asyncio.gather(*tasks, return_exceptions=True)

        responses = asyncio.run(run_mixed_concurrent_requests())
        end_time = time.perf_counter()

        total_time = (end_time - start_time) * 1000
        throughput = concurrent_requests / (total_time / 1000)
        success_count = sum(1 for r in responses if not isinstance(r, Exception))

        print(f"\n🎯 混合端点并发测试:")
        print(f"   并发请求数: {concurrent_requests}")
        print(f"   总耗时: {total_time:.2f}ms")
        print(f"   吞吐量: {throughput:.2f} 请求/秒")
        print(f"   成功率: {success_count}/{concurrent_requests}")

        # 验证目标: 吞吐量 > 100 请求/秒, 成功率 > 95%
        self.assertGreater(throughput, 100.0, f"吞吐量 {throughput:.2f} 请求/秒 未达到目标 100 请求/秒")
        self.assertGreater(success_count / concurrent_requests, 0.95, f"成功率 {success_count / concurrent_requests:.2%} 未达到目标 95%")


class StressBenchmarkTestCase(ActuatorBenchmarkTestCase):
    """压力测试和稳定性测试 - 验证高并发场景下的稳定性"""

    def setUp(self):
        super().setUp()
        with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
            self.context = ActuatorContext()
            asyncio.run(self.context.initialize_async())

    def tearDown(self):
        asyncio.run(self.context.shutdown_async())
        super().tearDown()

    def test_sustained_load_stability(self):
        """测试持续负载稳定性"""
        duration_seconds = 10  # 10秒持续测试
        requests_per_second = 50
        total_requests = duration_seconds * requests_per_second

        print(f"\n⏱️ 持续负载稳定性测试:")
        print(f"   测试时长: {duration_seconds}秒")
        print(f"   目标QPS: {requests_per_second}")
        print(f"   总请求数: {total_requests}")

        start_time = time.perf_counter()
        successful_requests = 0
        failed_requests = 0
        response_times = []

        async def sustained_load_test():
            nonlocal successful_requests, failed_requests

            # 创建请求任务
            tasks = []
            for i in range(total_requests):
                # 模拟均匀分布的请求
                delay = i / requests_per_second
                task = self._delayed_request(delay)
                tasks.append(task)

            # 执行所有请求
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 统计结果
            for result in results:
                if isinstance(result, Exception):
                    failed_requests += 1
                else:
                    successful_requests += 1
                    if isinstance(result, tuple):
                        response_times.append(result[1])

        asyncio.run(sustained_load_test())
        end_time = time.perf_counter()

        actual_duration = end_time - start_time
        actual_qps = successful_requests / actual_duration
        success_rate = successful_requests / total_requests
        avg_response_time = statistics.mean(response_times) if response_times else 0

        print(f"   实际耗时: {actual_duration:.2f}秒")
        print(f"   实际QPS: {actual_qps:.2f}")
        print(f"   成功请求: {successful_requests}")
        print(f"   失败请求: {failed_requests}")
        print(f"   成功率: {success_rate:.2%}")
        print(f"   平均响应时间: {avg_response_time:.2f}ms")

        # 验证稳定性目标
        self.assertGreater(success_rate, 0.98, f"成功率 {success_rate:.2%} 未达到稳定性目标 98%")
        self.assertLess(avg_response_time, 100.0, f"平均响应时间 {avg_response_time:.2f}ms 超过稳定性目标 100ms")

    async def _delayed_request(self, delay: float) -> tuple:
        """延迟执行请求"""
        await asyncio.sleep(delay)
        start = time.perf_counter()
        try:
            result = await self.context.execute_endpoint_async("health")
            end = time.perf_counter()
            return result, (end - start) * 1000
        except Exception as e:
            return e, 0

    def test_memory_leak_detection(self):
        """测试内存泄漏检测"""
        initial_memory = self.measure_memory()

        # 执行大量请求
        for batch in range(10):
            batch_requests = 100

            async def memory_test_batch():
                tasks = []
                for _ in range(batch_requests):
                    task = self.context.execute_endpoint_async("health")
                    tasks.append(task)
                return await asyncio.gather(*tasks, return_exceptions=True)

            asyncio.run(memory_test_batch())

            # 强制垃圾回收
            gc.collect()

            current_memory = self.measure_memory()
            memory_growth = current_memory - initial_memory

            print(f"   批次 {batch + 1}: 内存增长 {memory_growth / 1024 / 1024:.2f}MB")

            # 验证内存增长不超过合理范围
            self.assertLess(memory_growth / 1024 / 1024, 50.0, f"批次 {batch + 1} 内存增长 {memory_growth / 1024 / 1024:.2f}MB 可能存在内存泄漏")


if __name__ == "__main__":
    # 运行基准测试
    unittest.main(verbosity=2)
