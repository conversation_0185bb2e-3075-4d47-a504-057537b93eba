#!/usr/bin/env python
"""
* @author: cz
* @description: YAML 属性源加载器
"""

from pathlib import Path
from typing import Any, Union

import yaml

from ..sources import PropertySource
from .base import AbstractPropertySourceLoader


class YamlPropertySourceLoader(AbstractPropertySourceLoader):
    """YAML 属性源加载器

    支持加载 YAML 和 YML 格式的配置文件.
    """

    def get_file_extensions(self) -> list[str]:
        """获取支持的文件扩展名"""
        return [".yaml", ".yml"]

    def _parse_content(self, content: str, location: Union[str, Path]) -> Any:
        """解析YAML内容

        Args:
            content: YAML文件内容
            location: 文件位置(用于错误信息)

        Returns:
            解析后的数据对象

        Raises:
            yaml.YAMLError: YAML解析失败时抛出
        """
        try:
            return yaml.safe_load(content)
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Failed to parse YAML content from {location}: {e}") from e
