#!/usr/bin/env python
"""
* @author: cz
* @description: 事件异常体系单元测试

测试事件模块的异常类，验证异常的创建、信息获取和异常链等功能。
"""

import unittest
from datetime import datetime

from miniboot.errors.events import AsyncEventExecutionError, ConditionParseError, EventAnnotationError
from miniboot.errors.events import EventError as EventException
from miniboot.errors.events import EventHandlerError, EventListenerRegistrationError, EventPublishError, EventPublisherShutdownError, EventTypeError


class TestEventException(unittest.TestCase):
    """测试EventException基础异常类"""

    def test_basic_exception_creation(self):
        """测试基础异常创建"""
        message = "Test error message"
        exception = EventException(message)

        self.assertEqual(str(exception), message)  # __str__返回用户消息
        self.assertEqual(exception.message, message)
        self.assertEqual(exception.error_code, "EVENT")  # EventError -> EVENT
        # details包含调试信息，不为空
        self.assertIn("stack_trace", exception.details)
        self.assertIsNone(exception.cause)
        self.assertIsInstance(exception.timestamp, datetime)

    def test_exception_with_details(self):
        """测试带详细信息的异常"""
        message = "Test error"
        error_code = "TEST_ERROR"
        details = {"key1": "value1", "key2": 123}

        exception = EventException(message=message, error_code=error_code, details=details)

        self.assertEqual(exception.error_code, error_code)
        self.assertEqual(exception.details, details)
        # __str__返回用户消息，不包含详细信息
        self.assertEqual(str(exception), message)

    def test_exception_with_cause(self):
        """测试带原始异常的异常"""
        original_error = ValueError("Original error")
        exception = EventException("Wrapper error", cause=original_error)

        self.assertEqual(exception.cause, original_error)
        self.assertEqual(exception.__cause__, original_error)
        # __str__返回用户消息，不包含cause信息
        self.assertEqual(str(exception), "Wrapper error")

    def test_get_error_info(self):
        """测试获取错误信息"""
        details = {"test": "value"}
        cause = RuntimeError("Test cause")
        exception = EventException(message="Test message", error_code="TEST_CODE", details=details, cause=cause)

        error_info = exception.get_error_info()

        self.assertEqual(error_info["error_type"], "EventError")  # 实际类名
        self.assertEqual(error_info["error_code"], "TEST_CODE")
        self.assertEqual(error_info["message"], "Test message")
        # details包含了传入的details和调试信息
        self.assertEqual(error_info["details"]["test"], "value")
        # cause现在是字典格式
        self.assertEqual(error_info["cause"]["type"], "RuntimeError")
        self.assertEqual(error_info["cause"]["message"], "Test cause")
        self.assertIn("timestamp", error_info)


class TestEventHandlerError(unittest.TestCase):
    """测试EventHandlerError异常类"""

    def test_handler_error_creation(self):
        """测试事件处理器错误创建"""
        handler_name = "test_handler"
        event_type = "TestEvent"
        message = "Handler failed"

        error = EventHandlerError(message, event_type=event_type, handler_name=handler_name)

        self.assertEqual(error.get_handler_name(), handler_name)
        self.assertEqual(error.get_event_type(), event_type)
        self.assertEqual(error.error_code, "EVENTHANDLER")
        # 字符串表示包含事件类型和消息
        self.assertIn(event_type, str(error))
        self.assertIn(message, str(error))

    def test_handler_error_with_cause(self):
        """测试带原始异常的处理器错误"""
        cause = ValueError("Invalid value")
        error = EventHandlerError("Failed", event_type="Event", handler_name="handler", cause=cause)

        self.assertEqual(error.cause, cause)
        self.assertEqual(error.__cause__, cause)


class TestEventPublishError(unittest.TestCase):
    """测试EventPublishError异常类"""

    def test_publish_error_creation(self):
        """测试事件发布错误创建"""
        event_type = "TestEvent"
        message = "Publish failed"
        failed_handlers = ["handler1", "handler2"]

        error = EventPublishError(event_type, message, failed_handlers)

        self.assertEqual(error.get_event_type(), event_type)
        self.assertEqual(error.failed_handlers, failed_handlers)
        self.assertEqual(error.error_code, "EVENTPUBLISH")
        self.assertIn(event_type, str(error))
        self.assertIn(message, str(error))

    def test_publish_error_without_failed_handlers(self):
        """测试没有失败处理器列表的发布错误"""
        error = EventPublishError("TestEvent", "Failed")

        self.assertEqual(error.failed_handlers, [])


class TestConditionParseError(unittest.TestCase):
    """测试ConditionParseError异常类"""

    def test_condition_parse_error(self):
        """测试条件解析错误"""
        condition = "invalid_condition"
        message = "Syntax error"

        error = ConditionParseError(condition, message)

        self.assertEqual(error.condition, condition)
        self.assertEqual(error.error_code, "CONDITIONPARSE")
        # condition不会出现在字符串表示中，但可以通过属性获取
        self.assertEqual(error.condition, condition)
        self.assertIn(message, str(error))


class TestEventListenerRegistrationError(unittest.TestCase):
    """测试EventListenerRegistrationError异常类"""

    def test_registration_error(self):
        """测试监听器注册错误"""
        listener_name = "test_listener"
        event_type = "TestEvent"
        message = "Registration failed"

        error = EventListenerRegistrationError(listener_name, event_type, message)

        self.assertEqual(error.listener_name, listener_name)
        self.assertEqual(error.get_event_type(), event_type)
        self.assertEqual(error.error_code, "EVENTLISTENERREGISTRATION")
        # listener_name不会出现在字符串表示中，但可以通过属性获取
        self.assertEqual(error.listener_name, listener_name)
        self.assertIn(event_type, str(error))


class TestEventTypeError(unittest.TestCase):
    """测试EventTypeError异常类"""

    def test_type_error_with_expected_type(self):
        """测试带期望类型的事件类型错误"""
        event_type = "WrongEvent"
        expected_type = "CorrectEvent"

        error = EventTypeError(event_type, expected_type)

        self.assertEqual(error.get_event_type(), event_type)
        self.assertEqual(error.get_expected_type(), expected_type)
        self.assertEqual(error.error_code, "EVENTTYPE")
        self.assertIn(event_type, str(error))
        self.assertIn(expected_type, str(error))

    def test_type_error_without_expected_type(self):
        """测试不带期望类型的事件类型错误"""
        event_type = "InvalidEvent"

        error = EventTypeError(event_type)

        self.assertEqual(error.get_event_type(), event_type)
        self.assertIsNone(error.get_expected_type())
        self.assertIn("Invalid or unsupported", str(error))

    def test_type_error_with_custom_message(self):
        """测试自定义消息的事件类型错误"""
        custom_message = "Custom error message"

        error = EventTypeError("TestEvent", message=custom_message)

        self.assertEqual(error.message, f"Event 'TestEvent': {custom_message}")


class TestEventPublisherShutdownError(unittest.TestCase):
    """测试EventPublisherShutdownError异常类"""

    def test_shutdown_error(self):
        """测试发布器关闭错误"""
        operation = "publish_event"

        error = EventPublisherShutdownError(operation)

        self.assertEqual(error.operation, operation)
        self.assertEqual(error.error_code, "EVENTPUBLISHERSHUTDOWN")
        self.assertIn(operation, str(error))
        self.assertIn("shutdown", str(error))


class TestAsyncEventExecutionError(unittest.TestCase):
    """测试AsyncEventExecutionError异常类"""

    def test_async_execution_error(self):
        """测试异步执行错误"""
        event_type = "TestEvent"
        handler_name = "async_handler"
        message = "Async execution failed"
        execution_time = 5.5

        error = AsyncEventExecutionError(event_type, handler_name, message, execution_time)

        self.assertEqual(error.get_event_type(), event_type)
        self.assertEqual(error.get_handler_name(), handler_name)
        self.assertEqual(error.execution_time, execution_time)
        self.assertEqual(error.error_code, "ASYNCEVENTEXECUTION")
        self.assertIn(event_type, str(error))
        # handler_name不会出现在字符串表示中，但可以通过getter方法获取
        self.assertEqual(error.get_handler_name(), handler_name)

    def test_async_execution_error_without_time(self):
        """测试不带执行时间的异步执行错误"""
        error = AsyncEventExecutionError("Event", "handler", "Failed")

        self.assertIsNone(error.execution_time)


class TestEventAnnotationError(unittest.TestCase):
    """测试EventAnnotationError异常类"""

    def test_annotation_error(self):
        """测试事件注解错误"""
        annotation_type = "@EventListener"
        target = "MyClass.my_method"
        message = "Annotation processing failed"

        error = EventAnnotationError(annotation_type, target, message)

        self.assertEqual(error.annotation_type, annotation_type)
        self.assertEqual(error.target, target)
        self.assertEqual(error.error_code, "EVENTANNOTATION")
        # annotation_type和target不会出现在字符串表示中，但可以通过属性获取
        self.assertEqual(error.annotation_type, annotation_type)
        self.assertEqual(error.target, target)


if __name__ == "__main__":
    unittest.main()
