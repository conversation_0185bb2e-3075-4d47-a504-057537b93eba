"""
Bean 管理模块

提供 Mini-Boot 框架的 Bean 定义、创建、注册和依赖解析功能。


"""

# 导入核心异常类
from miniboot.errors import BeanCreationError
from miniboot.errors import BeanNotFoundError as NoSuchBeanDefinitionError
from miniboot.errors import MultipleBeanFoundError as NoUniqueBeanDefinitionError

# 导入核心接口和基类
from .base import (
    BeanDefinitionRegistry,
    BeanFactory,
    BeanFactoryAware,
    ConfigurableBeanFactory,
    HierarchicalBeanFactory,
    Lifecycle,
    ListableBeanFactory,
)

# 导入缓存管理器
from .cache import CacheManager

# 导入核心实现类
from .definition import BeanDefinition, BeanScope, BeanStatus, ConstructorArgument, PropertyValue

# 导入分层工厂
from .factory import DefaultBeanFactory

# 导入依赖图
from .graph import DependencyGraph

# Bean创建优化器已删除 - 功能未被使用
#
# 重构说明：
# - 删除了未使用的Bean创建优化相关类
# - 简化了bean模块的API表面
# - 保持了核心Bean创建功能在DefaultBeanFactory中
from .registry import DefaultBeanDefinitionRegistry

# 导入核心作用域管理
from .scopes import BeanScopeContext, BeanScopeManager, BeanScopeRegistry, PrototypeScopeManager, ScopeManager, SingletonScopeManager

# 监控诊断功能已删除 - 功能未被使用
#
# 重构说明：
# - 删除了未使用的监控诊断相关类
# - 简化了bean模块的API表面
# - 保持了核心性能统计功能在factory.py中


# 内存优化功能已删除 - 功能未被使用
#
# 重构说明：
# - 删除了未使用的内存管理相关类
# - 简化了bean模块的API表面
# - 保持了核心缓存功能在CacheManager中


# 生命周期管理已迁移到processor模块
#
# 重构说明：
# - LifecycleManager功能已由processor.lifecycle.LifecycleAnnotationProcessor替代
# - 生命周期管理现在通过Bean后置处理器实现


# 高性能依赖注入器已删除 - 功能未被使用
#
# 重构说明：
# - 删除了未使用的高性能依赖注入器相关类
# - 简化了bean模块的API表面
# - 保持了核心依赖注入功能在factory.py中


__all__ = [
    # === 核心Bean定义 ===
    "BeanDefinition",  # Bean元数据定义
    "BeanScope",  # Bean作用域枚举
    "BeanStatus",  # Bean状态枚举
    "PropertyValue",  # Bean属性值
    "ConstructorArgument",  # 构造函数参数
    # === 核心接口 ===
    "BeanFactory",  # Bean工厂基础接口
    "HierarchicalBeanFactory",  # 分层Bean工厂接口
    "ConfigurableBeanFactory",  # 可配置Bean工厂接口
    "ListableBeanFactory",  # 可列举Bean工厂接口
    "BeanDefinitionRegistry",  # Bean定义注册表接口
    # === 生命周期接口 ===
    "InitializingBean",  # 初始化Bean接口
    "DisposableBean",  # 可销毁Bean接口
    "BeanNameAware",  # Bean名称感知接口
    "BeanFactoryAware",  # Bean工厂感知接口
    "ApplicationContextAware",  # 应用上下文感知接口
    "Lifecycle",  # 生命周期接口
    "SmartLifecycle",  # 智能生命周期接口
    # === 核心实现类 ===
    "DefaultBeanFactory",  # 默认Bean工厂实现（推荐）
    "DefaultBeanDefinitionRegistry",  # 默认Bean定义注册表实现
    "CacheManager",  # 多级缓存管理器
    # "LifecycleManager" - 已删除，功能迁移到processor.lifecycle模块
    # === 核心作用域管理 ===
    "BeanScopeManager",  # 核心作用域管理器接口
    "BeanScopeRegistry",  # 核心作用域注册表
    "BeanScopeContext",  # 核心作用域上下文
    "ScopeManager",  # 作用域管理器
    "SingletonScopeManager",  # 单例作用域管理器
    "PrototypeScopeManager",  # 原型作用域管理器
    # === 工具类 ===
    "DependencyGraph",  # 依赖关系图
    # === 核心异常类 ===
    "BeanCreationError",  # Bean创建异常(最常见)
    "NoSuchBeanDefinitionError",  # Bean不存在异常(最常见)
    "NoUniqueBeanDefinitionError",  # Bean不唯一异常
    "CircularDependencyError",  # 循环依赖异常(重要)
]
