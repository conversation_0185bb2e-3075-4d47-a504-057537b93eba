#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务模块与events模块集成测试
"""

import unittest
import time
import threading
from dataclasses import dataclass
from typing import Any

from miniboot.schedule import MiniBootScheduler, Scheduled, EnableScheduling


@dataclass
class TaskExecutionEvent:
    """任务执行事件"""

    task_name: str
    execution_time: str
    result: Any
    success: bool
    error: str = None


@dataclass
class SchedulerStateEvent:
    """调度器状态事件"""

    state: str
    timestamp: str
    message: str = None


class MockEventPublisher:
    """模拟事件发布器"""

    def __init__(self):
        self.published_events = []
        self.lock = threading.Lock()

    def publish(self, event: Any):
        """发布事件"""
        with self.lock:
            self.published_events.append(event)

    def get_events(self, event_type: type = None):
        """获取事件"""
        with self.lock:
            if event_type:
                return [e for e in self.published_events if isinstance(e, event_type)]
            return self.published_events.copy()

    def clear_events(self):
        """清空事件"""
        with self.lock:
            self.published_events.clear()


class MockEventListener:
    """模拟事件监听器"""

    def __init__(self):
        self.received_events = []
        self.lock = threading.Lock()

    def handle_task_execution_event(self, event: TaskExecutionEvent):
        """处理任务执行事件"""
        with self.lock:
            self.received_events.append(event)

    def handle_scheduler_state_event(self, event: SchedulerStateEvent):
        """处理调度器状态事件"""
        with self.lock:
            self.received_events.append(event)

    def get_received_events(self, event_type: type = None):
        """获取接收到的事件"""
        with self.lock:
            if event_type:
                return [e for e in self.received_events if isinstance(e, event_type)]
            return self.received_events.copy()

    def clear_events(self):
        """清空事件"""
        with self.lock:
            self.received_events.clear()


@EnableScheduling
class EventAwareScheduledService:
    """事件感知的调度服务"""

    def __init__(self, name: str, event_publisher: MockEventPublisher):
        self.name = name
        self.event_publisher = event_publisher
        self.execution_count = 0
        self.lock = threading.Lock()

    @Scheduled(fixed_rate="1s")
    def event_publishing_task(self):
        """发布事件的任务"""
        with self.lock:
            self.execution_count += 1
            result = f"{self.name}_execution_{self.execution_count}"

        try:
            # 模拟任务执行
            time.sleep(0.01)

            # 发布成功事件
            event = TaskExecutionEvent(
                task_name=f"{self.name}.event_publishing_task", execution_time=time.strftime("%Y-%m-%d %H:%M:%S"), result=result, success=True
            )
            self.event_publisher.publish(event)

            return result

        except Exception as e:
            # 发布失败事件
            event = TaskExecutionEvent(
                task_name=f"{self.name}.event_publishing_task",
                execution_time=time.strftime("%Y-%m-%d %H:%M:%S"),
                result=None,
                success=False,
                error=str(e),
            )
            self.event_publisher.publish(event)
            raise

    @Scheduled(fixed_delay="2s")
    def error_prone_task(self):
        """容易出错的任务"""
        with self.lock:
            self.execution_count += 1

        # 模拟随机错误
        if self.execution_count % 3 == 0:
            error_event = TaskExecutionEvent(
                task_name=f"{self.name}.error_prone_task",
                execution_time=time.strftime("%Y-%m-%d %H:%M:%S"),
                result=None,
                success=False,
                error="Simulated error",
            )
            self.event_publisher.publish(error_event)
            raise RuntimeError("Simulated error")

        result = f"{self.name}_error_prone_{self.execution_count}"
        success_event = TaskExecutionEvent(
            task_name=f"{self.name}.error_prone_task", execution_time=time.strftime("%Y-%m-%d %H:%M:%S"), result=result, success=True
        )
        self.event_publisher.publish(success_event)
        return result


class EventDrivenScheduledService:
    """事件驱动的调度服务"""

    def __init__(self, name: str, event_listener: MockEventListener):
        self.name = name
        self.event_listener = event_listener
        self.triggered_executions = []
        self.lock = threading.Lock()

    def handle_external_event(self, event: Any):
        """处理外部事件"""
        with self.lock:
            self.triggered_executions.append(
                {"event_type": type(event).__name__, "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"), "event_data": event}
            )

    def get_triggered_executions(self):
        """获取触发的执行"""
        with self.lock:
            return self.triggered_executions.copy()


class TestEventsIntegration(unittest.TestCase):
    """测试定时任务与事件模块的集成"""

    def setUp(self):
        """设置测试环境"""
        self.event_publisher = MockEventPublisher()
        self.event_listener = MockEventListener()
        self.scheduler = MiniBootScheduler(max_workers=3, use_asyncio=False)

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

    def test_task_execution_event_publishing(self):
        """测试任务执行事件发布"""
        service = EventAwareScheduledService("testService", self.event_publisher)

        # 注册调度任务
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(service.event_publishing_task, service)

            # 启动调度器
            self.scheduler.start()
            time.sleep(2.5)  # 运行2.5秒
            self.scheduler.shutdown()

            # 验证事件发布
            events = self.event_publisher.get_events(TaskExecutionEvent)
            self.assertGreater(len(events), 0)

            # 验证事件内容
            for event in events:
                self.assertEqual(event.task_name, "testService.event_publishing_task")
                self.assertTrue(event.success)
                self.assertIsNotNone(event.result)
                self.assertIsNone(event.error)

    def test_task_error_event_publishing(self):
        """测试任务错误事件发布"""
        service = EventAwareScheduledService("errorService", self.event_publisher)

        # 注册容易出错的任务
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(service.error_prone_task, service)

            # 启动调度器
            self.scheduler.start()
            time.sleep(4)  # 运行4秒，确保有错误发生
            self.scheduler.shutdown()

            # 验证事件发布
            events = self.event_publisher.get_events(TaskExecutionEvent)
            self.assertGreater(len(events), 0)

            # 应该有成功和失败的事件
            success_events = [e for e in events if e.success]
            error_events = [e for e in events if not e.success]

            self.assertGreater(len(success_events), 0)
            self.assertGreater(len(error_events), 0)

            # 验证错误事件内容
            for error_event in error_events:
                self.assertEqual(error_event.task_name, "errorService.error_prone_task")
                self.assertFalse(error_event.success)
                self.assertIsNone(error_event.result)
                self.assertIsNotNone(error_event.error)

    def test_scheduler_lifecycle_events(self):
        """测试调度器生命周期事件"""

        # 模拟调度器状态事件发布
        def publish_scheduler_event(state: str, message: str = None):
            event = SchedulerStateEvent(state=state, timestamp=time.strftime("%Y-%m-%d %H:%M:%S"), message=message)
            self.event_publisher.publish(event)

        # 发布启动事件
        publish_scheduler_event("STARTING", "Scheduler is starting")

        # 启动调度器
        self.scheduler.start()
        publish_scheduler_event("RUNNING", "Scheduler is now running")

        time.sleep(1)

        # 发布暂停事件
        self.scheduler.pause()
        publish_scheduler_event("PAUSED", "Scheduler is paused")

        time.sleep(0.5)

        # 发布恢复事件
        self.scheduler.resume()
        publish_scheduler_event("RESUMED", "Scheduler is resumed")

        time.sleep(0.5)

        # 发布停止事件
        self.scheduler.shutdown()
        publish_scheduler_event("STOPPED", "Scheduler is stopped")

        # 验证生命周期事件
        events = self.event_publisher.get_events(SchedulerStateEvent)
        self.assertEqual(len(events), 5)

        expected_states = ["STARTING", "RUNNING", "PAUSED", "RESUMED", "STOPPED"]
        actual_states = [e.state for e in events]
        self.assertEqual(actual_states, expected_states)

    def test_event_driven_task_execution(self):
        """测试事件驱动的任务执行"""
        event_driven_service = EventDrivenScheduledService("eventDriven", self.event_listener)

        # 模拟外部事件触发任务执行
        external_events = [
            TaskExecutionEvent("external_task_1", time.strftime("%Y-%m-%d %H:%M:%S"), "result1", True),
            TaskExecutionEvent("external_task_2", time.strftime("%Y-%m-%d %H:%M:%S"), "result2", True),
            SchedulerStateEvent("EXTERNAL_TRIGGER", time.strftime("%Y-%m-%d %H:%M:%S"), "External trigger"),
        ]

        # 处理外部事件
        for event in external_events:
            event_driven_service.handle_external_event(event)

        # 验证事件处理
        triggered_executions = event_driven_service.get_triggered_executions()
        self.assertEqual(len(triggered_executions), 3)

        # 验证事件类型
        event_types = [exec["event_type"] for exec in triggered_executions]
        self.assertIn("TaskExecutionEvent", event_types)
        self.assertIn("SchedulerStateEvent", event_types)

    def test_event_listener_integration(self):
        """测试事件监听器集成"""
        service = EventAwareScheduledService("listenerTest", self.event_publisher)

        # 模拟事件监听器订阅
        def simulate_event_subscription():
            """模拟事件订阅"""
            events = self.event_publisher.get_events()
            for event in events:
                if isinstance(event, TaskExecutionEvent):
                    self.event_listener.handle_task_execution_event(event)
                elif isinstance(event, SchedulerStateEvent):
                    self.event_listener.handle_scheduler_state_event(event)

        # 注册任务
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(service.event_publishing_task, service)

            # 启动调度器
            self.scheduler.start()
            time.sleep(2)

            # 模拟事件处理
            simulate_event_subscription()

            self.scheduler.shutdown()

            # 验证监听器接收到事件
            received_events = self.event_listener.get_received_events()
            self.assertGreater(len(received_events), 0)

            # 验证接收到的事件类型
            task_events = self.event_listener.get_received_events(TaskExecutionEvent)
            self.assertGreater(len(task_events), 0)

    def test_multiple_services_event_coordination(self):
        """测试多个服务的事件协调"""
        # 创建多个事件感知服务
        services = [EventAwareScheduledService(f"service_{i}", self.event_publisher) for i in range(3)]

        # 注册所有服务的任务
        task_ids = []
        if self.scheduler.task_manager:
            for service in services:
                task_id = self.scheduler.task_manager.create_from_scheduled_method(service.event_publishing_task, service)
                task_ids.append(task_id)

            # 启动调度器
            self.scheduler.start()
            time.sleep(2)
            self.scheduler.shutdown()

            # 验证所有服务都发布了事件
            events = self.event_publisher.get_events(TaskExecutionEvent)
            self.assertGreater(len(events), 0)

            # 验证每个服务都有事件
            service_names = set()
            for event in events:
                service_name = event.task_name.split(".")[0]
                service_names.add(service_name)

            expected_service_names = {f"service_{i}" for i in range(3)}
            self.assertEqual(service_names, expected_service_names)

    def test_event_ordering_and_timing(self):
        """测试事件顺序和时间"""
        service = EventAwareScheduledService("timingTest", self.event_publisher)

        # 记录开始时间
        start_time = time.time()

        # 注册任务
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(service.event_publishing_task, service)

            # 启动调度器
            self.scheduler.start()
            time.sleep(3)  # 运行3秒
            self.scheduler.shutdown()

            # 验证事件时间顺序
            events = self.event_publisher.get_events(TaskExecutionEvent)
            self.assertGreater(len(events), 1)  # 应该有多个事件

            # 验证事件时间递增
            event_times = []
            for event in events:
                event_time = time.mktime(time.strptime(event.execution_time, "%Y-%m-%d %H:%M:%S"))
                event_times.append(event_time)

            # 事件时间应该是递增的
            for i in range(1, len(event_times)):
                self.assertGreaterEqual(event_times[i], event_times[i - 1])

            # 所有事件时间都应该在测试时间范围内
            end_time = time.time()
            for event_time in event_times:
                self.assertGreaterEqual(event_time, start_time)
                self.assertLessEqual(event_time, end_time)

    def test_event_filtering_and_routing(self):
        """测试事件过滤和路由"""
        service = EventAwareScheduledService("filterTest", self.event_publisher)

        # 创建专门的事件过滤器
        class EventFilter:
            def __init__(self):
                self.success_events = []
                self.error_events = []

            def filter_events(self, events):
                for event in events:
                    if isinstance(event, TaskExecutionEvent):
                        if event.success:
                            self.success_events.append(event)
                        else:
                            self.error_events.append(event)

        event_filter = EventFilter()

        # 注册容易出错的任务
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(service.error_prone_task, service)

            # 启动调度器
            self.scheduler.start()
            time.sleep(4)  # 运行足够长时间以产生成功和错误事件
            self.scheduler.shutdown()

            # 过滤事件
            all_events = self.event_publisher.get_events()
            event_filter.filter_events(all_events)

            # 验证事件过滤
            self.assertGreater(len(event_filter.success_events), 0)
            self.assertGreater(len(event_filter.error_events), 0)

            # 验证过滤正确性
            for event in event_filter.success_events:
                self.assertTrue(event.success)
                self.assertIsNone(event.error)

            for event in event_filter.error_events:
                self.assertFalse(event.success)
                self.assertIsNotNone(event.error)


if __name__ == "__main__":
    unittest.main()
