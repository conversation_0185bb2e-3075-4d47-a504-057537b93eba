# Mini-Boot 开发环境初始化指南

## 1. 概述

本文档指导开发者如何下载Mini-Boot框架源码并使用uv工具初始化本地开发环境。通过本指南，开发者可以快速搭建完整的开发环境，进行框架的开发、测试和调试工作。

## 2. 环境要求

### 2.1 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Python版本**: Python 3.8 或更高版本
- **Git**: 用于代码版本控制

### 2.2 工具安装

#### 安装uv工具

```bash
# Linux/macOS
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 验证安装
uv --version
```

#### 安装Git

```bash
# Ubuntu/Debian
sudo apt-get install git

# CentOS/RHEL
sudo yum install git

# macOS (使用Homebrew)
brew install git

# Windows
# 下载并安装 https://git-scm.com/download/win
```

## 3. 获取源码

### 3.1 克隆仓库

```bash
# 克隆Mini-Boot仓库
git clone https://github.com/miniboot/mini-boot.git
cd mini-boot

# 或者如果是私有仓库
git clone https://your-git-server.com/miniboot/mini-boot.git
cd mini-boot
```

### 3.2 查看项目结构

```bash
# 查看项目结构
tree -L 2
# 或者
ls -la
```

预期的项目结构：
```
mini-boot/
├── README.md                        # 项目说明
├── LICENSE                          # 许可证
├── docs/                            # 文档目录
├── examples/                        # 示例项目
├── tests/                           # 测试目录
└── scripts/                         # 构建脚本
```

## 4. 初始化开发环境

### 4.1 使用uv初始化项目

```bash
# 在mini-boot目录下初始化项目
# 指定包名为miniboot（不带连字符）
uv init miniboot

# 查看生成的文件
ls -la
```

这会创建：
- `pyproject.toml` - 项目配置文件
- `miniboot/` - Python包目录
- `miniboot/__init__.py` - 包初始化文件

### 4.2 添加项目依赖

```bash
# 添加核心依赖
uv add pydantic pyyaml loguru typing-extensions

# 添加可选依赖
uv add --optional web fastapi "uvicorn[standard]"
uv add --optional redis aioredis
uv add --optional database "sqlalchemy[asyncio]" asyncpg

# 添加开发依赖
uv add --dev pytest pytest-asyncio pytest-cov ruff mypy
```

### 4.3 同步开发环境

```bash
# 创建虚拟环境并安装所有依赖
uv sync

# 安装开发模式（可编辑安装）
uv pip install -e ".[all,dev]"
```

## 5. 开发环境配置

### 5.1 IDE配置

#### VS Code配置

创建 `.vscode/settings.json`:
```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.formatting.provider": "ruff",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".venv": false
    }
}
```

#### PyCharm配置

1. 打开项目目录
2. 设置Python解释器为 `.venv/bin/python`
3. 配置代码风格为ruff
4. 设置测试框架为pytest

### 5.2 Git配置

```bash
# 配置Git用户信息
git config user.name "Your Name"
git config user.email "<EMAIL>"

# 配置Git忽略文件
cat > .gitignore << EOF
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.venv/
venv/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Testing
.coverage
.pytest_cache/
htmlcov/

# OS
.DS_Store
Thumbs.db
EOF
```

## 6. 验证开发环境

### 6.1 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest tests/test_context.py

# 运行测试并生成覆盖率报告
uv run pytest --cov=miniboot --cov-report=html
```

### 6.2 代码质量检查

```bash
# 代码格式检查
uv run ruff check

# 自动格式化代码
uv run ruff format

# 类型检查
uv run mypy miniboot
```

### 6.3 运行示例

```bash
# 运行基础示例
cd examples/basic
python main.py

# 运行Web示例
cd examples/web
python main.py
```

## 7. 开发工作流

### 7.1 日常开发流程

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 同步依赖
uv sync

# 3. 创建功能分支
git checkout -b feature/new-feature

# 4. 进行开发工作
# ... 编写代码 ...

# 5. 运行测试
uv run pytest

# 6. 代码检查
uv run ruff check
uv run ruff format

# 7. 提交代码
git add .
git commit -m "feat: add new feature"

# 8. 推送分支
git push origin feature/new-feature
```

### 7.2 添加新功能

```bash
# 1. 在miniboot/目录下创建新模块
mkdir miniboot/newmodule
touch miniboot/newmodule/__init__.py
touch miniboot/newmodule/service.py

# 2. 编写测试
touch tests/test_newmodule.py

# 3. 运行测试确保功能正常
uv run pytest tests/test_newmodule.py
```

### 7.3 调试技巧

```bash
# 使用pdb调试
python -m pdb your_script.py

# 使用pytest调试
uv run pytest --pdb tests/test_your_module.py

# 查看详细输出
uv run pytest -v -s tests/
```

## 8. 常见问题

### 8.1 依赖问题

```bash
# 清理并重新安装依赖
rm -rf .venv
uv sync

# 更新依赖
uv add package@latest
```

### 8.2 测试问题

```bash
# 清理测试缓存
rm -rf .pytest_cache
rm -rf htmlcov

# 重新运行测试
uv run pytest --cache-clear
```

### 8.3 代码格式问题

```bash
# 自动修复格式问题
uv run ruff format .
uv run ruff check --fix .
```

---

*本文档指导开发者初始化Mini-Boot框架的本地开发环境。*
