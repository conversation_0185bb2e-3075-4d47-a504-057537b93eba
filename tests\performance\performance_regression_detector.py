#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 性能回归检测器 - 检测性能回归和生成对比报告

通过对比历史性能数据，自动检测性能回归问题并生成详细的分析报告。
"""

import json
import os
import statistics
import sys
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from test_comprehensive_performance_suite import PerformanceBenchmark, PerformanceReport


@dataclass
class RegressionAnalysis:
    """回归分析结果"""

    test_name: str
    baseline_avg_time: float
    current_avg_time: float
    performance_change_percent: float
    is_regression: bool
    severity: str  # 'low', 'medium', 'high', 'critical'
    recommendation: str


@dataclass
class RegressionReport:
    """回归检测报告"""

    current_report_timestamp: str
    baseline_report_timestamp: str
    total_tests: int
    regression_count: int
    improvement_count: int
    stable_count: int
    regressions: list[RegressionAnalysis]
    improvements: list[RegressionAnalysis]
    summary: dict[str, float]


class PerformanceRegressionDetector:
    """性能回归检测器"""

    def __init__(self, reports_dir: str = "tests/performance/reports"):
        """初始化回归检测器

        Args:
            reports_dir: 性能报告目录
        """
        self.reports_dir = reports_dir
        self.regression_threshold = 0.10  # 10%性能下降视为回归
        self.improvement_threshold = 0.05  # 5%性能提升视为改进

    def load_performance_reports(self) -> list[PerformanceReport]:
        """加载所有性能报告"""
        reports = []

        if not os.path.exists(self.reports_dir):
            return reports

        for filename in os.listdir(self.reports_dir):
            if filename.endswith(".json"):
                filepath = os.path.join(self.reports_dir, filename)
                try:
                    with open(filepath, encoding="utf-8") as f:
                        data = json.load(f)

                    # 重构基准数据
                    benchmarks = []
                    for b_data in data.get("benchmarks", []):
                        benchmark = PerformanceBenchmark(**b_data)
                        benchmarks.append(benchmark)

                    report = PerformanceReport(
                        test_suite=data["test_suite"],
                        total_tests=data["total_tests"],
                        passed_tests=data["passed_tests"],
                        failed_tests=data["failed_tests"],
                        total_duration=data["total_duration"],
                        benchmarks=benchmarks,
                        system_info=data["system_info"],
                        timestamp=data["timestamp"],
                    )
                    reports.append(report)

                except Exception as e:
                    print(f"警告: 无法加载报告 {filename}: {e}")

        # 按时间戳排序
        reports.sort(key=lambda r: r.timestamp)
        return reports

    def get_baseline_report(self, reports: list[PerformanceReport]) -> Optional[PerformanceReport]:
        """获取基准报告（最新的稳定版本）"""
        if len(reports) < 2:
            return None

        # 使用倒数第二个报告作为基准
        return reports[-2]

    def analyze_performance_change(self, baseline_benchmark: PerformanceBenchmark, current_benchmark: PerformanceBenchmark) -> RegressionAnalysis:
        """分析性能变化"""
        baseline_time = baseline_benchmark.avg_time_per_operation
        current_time = current_benchmark.avg_time_per_operation

        # 计算性能变化百分比
        change_percent = (current_time - baseline_time) / baseline_time * 100 if baseline_time > 0 else 0

        # 判断是否为回归
        is_regression = change_percent > (self.regression_threshold * 100)

        # 确定严重程度
        if change_percent > 50:
            severity = "critical"
            recommendation = "立即调查性能问题，可能存在严重的性能回归"
        elif change_percent > 25:
            severity = "high"
            recommendation = "高优先级调查，性能显著下降"
        elif change_percent > 10:
            severity = "medium"
            recommendation = "中等优先级调查，性能有所下降"
        elif change_percent > 5:
            severity = "low"
            recommendation = "低优先级关注，轻微性能下降"
        elif change_percent < -10:
            severity = "improvement"
            recommendation = "性能显著改善，值得记录和保持"
        else:
            severity = "stable"
            recommendation = "性能稳定，无需特别关注"

        return RegressionAnalysis(
            test_name=current_benchmark.test_name,
            baseline_avg_time=baseline_time,
            current_avg_time=current_time,
            performance_change_percent=change_percent,
            is_regression=is_regression,
            severity=severity,
            recommendation=recommendation,
        )

    def detect_regressions(self, baseline_report: PerformanceReport, current_report: PerformanceReport) -> RegressionReport:
        """检测性能回归"""
        # 创建基准数据映射
        baseline_benchmarks = {b.test_name: b for b in baseline_report.benchmarks}

        regressions = []
        improvements = []
        stable = []

        # 分析每个测试的性能变化
        for current_benchmark in current_report.benchmarks:
            test_name = current_benchmark.test_name

            if test_name in baseline_benchmarks:
                baseline_benchmark = baseline_benchmarks[test_name]
                analysis = self.analyze_performance_change(baseline_benchmark, current_benchmark)

                if analysis.is_regression:
                    regressions.append(analysis)
                elif analysis.performance_change_percent < -(self.improvement_threshold * 100):
                    improvements.append(analysis)
                else:
                    stable.append(analysis)

        # 按严重程度排序回归
        severity_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        regressions.sort(key=lambda r: severity_order.get(r.severity, 0), reverse=True)

        # 计算总结统计
        total_tests = len(current_report.benchmarks)
        regression_count = len(regressions)
        improvement_count = len(improvements)
        stable_count = len(stable)

        # 计算平均性能变化
        all_changes = [r.performance_change_percent for r in regressions + improvements + stable]
        avg_change = statistics.mean(all_changes) if all_changes else 0

        summary = {
            "total_tests": total_tests,
            "regression_rate": (regression_count / total_tests) * 100 if total_tests > 0 else 0,
            "improvement_rate": (improvement_count / total_tests) * 100 if total_tests > 0 else 0,
            "stable_rate": (stable_count / total_tests) * 100 if total_tests > 0 else 0,
            "avg_performance_change": avg_change,
        }

        return RegressionReport(
            current_report_timestamp=current_report.timestamp,
            baseline_report_timestamp=baseline_report.timestamp,
            total_tests=total_tests,
            regression_count=regression_count,
            improvement_count=improvement_count,
            stable_count=stable_count,
            regressions=regressions,
            improvements=improvements,
            summary=summary,
        )

    def generate_regression_report_text(self, regression_report: RegressionReport) -> str:
        """生成文本格式的回归报告"""
        report_lines = []

        # 报告头部
        report_lines.append("# 性能回归检测报告")
        report_lines.append("")
        report_lines.append(f"**检测时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append(f"**当前报告**: {regression_report.current_report_timestamp}")
        report_lines.append(f"**基准报告**: {regression_report.baseline_report_timestamp}")
        report_lines.append("")

        # 总结
        report_lines.append("## 📊 检测总结")
        report_lines.append("")
        report_lines.append(f"- **总测试数**: {regression_report.total_tests}")
        report_lines.append(f"- **性能回归**: {regression_report.regression_count} ({regression_report.summary['regression_rate']:.1f}%)")
        report_lines.append(f"- **性能改进**: {regression_report.improvement_count} ({regression_report.summary['improvement_rate']:.1f}%)")
        report_lines.append(f"- **性能稳定**: {regression_report.stable_count} ({regression_report.summary['stable_rate']:.1f}%)")
        report_lines.append(f"- **平均性能变化**: {regression_report.summary['avg_performance_change']:+.2f}%")
        report_lines.append("")

        # 性能回归详情
        if regression_report.regressions:
            report_lines.append("## 🚨 性能回归详情")
            report_lines.append("")

            for regression in regression_report.regressions:
                severity_emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}.get(regression.severity, "⚪")

                report_lines.append(f"### {severity_emoji} {regression.test_name}")
                report_lines.append("")
                report_lines.append(f"- **严重程度**: {regression.severity.upper()}")
                report_lines.append(f"- **基准耗时**: {regression.baseline_avg_time * 1000:.3f}ms")
                report_lines.append(f"- **当前耗时**: {regression.current_avg_time * 1000:.3f}ms")
                report_lines.append(f"- **性能变化**: {regression.performance_change_percent:+.2f}%")
                report_lines.append(f"- **建议**: {regression.recommendation}")
                report_lines.append("")

        # 性能改进详情
        if regression_report.improvements:
            report_lines.append("## ✅ 性能改进详情")
            report_lines.append("")

            for improvement in regression_report.improvements[:5]:  # 只显示前5个改进
                report_lines.append(f"### 🚀 {improvement.test_name}")
                report_lines.append("")
                report_lines.append(f"- **基准耗时**: {improvement.baseline_avg_time * 1000:.3f}ms")
                report_lines.append(f"- **当前耗时**: {improvement.current_avg_time * 1000:.3f}ms")
                report_lines.append(f"- **性能提升**: {abs(improvement.performance_change_percent):.2f}%")
                report_lines.append("")

        return "\n".join(report_lines)

    def save_regression_report(self, regression_report: RegressionReport, filename: str = None) -> str:
        """保存回归检测报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"regression_report_{timestamp}.md"

        report_path = os.path.join(self.reports_dir, filename)
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        report_text = self.generate_regression_report_text(regression_report)

        with open(report_path, "w", encoding="utf-8") as f:
            f.write(report_text)

        return report_path

    def run_regression_detection(self) -> Optional[RegressionReport]:
        """运行回归检测"""
        print("🔍 开始性能回归检测...")

        # 加载性能报告
        reports = self.load_performance_reports()

        if len(reports) < 2:
            print("❌ 需要至少2个性能报告才能进行回归检测")
            return None

        # 获取基准和当前报告
        baseline_report = self.get_baseline_report(reports)
        current_report = reports[-1]

        if not baseline_report:
            print("❌ 无法获取基准报告")
            return None

        print(f"📊 基准报告: {baseline_report.timestamp}")
        print(f"📊 当前报告: {current_report.timestamp}")

        # 执行回归检测
        regression_report = self.detect_regressions(baseline_report, current_report)

        # 保存报告
        report_path = self.save_regression_report(regression_report)
        print(f"📄 回归报告已保存: {report_path}")

        # 输出简要结果
        print("\n🎯 回归检测结果:")
        print(f"   总测试数: {regression_report.total_tests}")
        print(f"   性能回归: {regression_report.regression_count}")
        print(f"   性能改进: {regression_report.improvement_count}")
        print(f"   性能稳定: {regression_report.stable_count}")

        if regression_report.regressions:
            print(f"\n⚠️ 发现 {len(regression_report.regressions)} 个性能回归:")
            for regression in regression_report.regressions[:3]:  # 显示前3个
                print(f"   - {regression.test_name}: {regression.performance_change_percent:+.1f}% ({regression.severity})")

        return regression_report


if __name__ == "__main__":
    detector = PerformanceRegressionDetector()
    detector.run_regression_detection()
