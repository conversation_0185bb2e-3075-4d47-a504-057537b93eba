# Mini-Boot 框架架构设计

## 1. 框架概述

Mini-Boot 是一个轻量级的 Python Web 框架，参考 Spring Boot 的设计理念。它提供了 IoC 容器、依赖注入、自动配置、Web 集成、事件系统、任务调度等企业级功能，旨在为 Python 开发者提供类似 Spring Boot 的开发体验。

## 2. 核心架构

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "Mini-Boot 核心架构"
        A[MiniBootApplication] --> B[ApplicationContext]
        B --> C[BeanFactory]
        B --> D[ComponentScanner]
        B --> E[ConfigurationManager]

        C --> F[BeanRegistry]
        C --> G[BeanDefinition]
        C --> H[BeanPostProcessor]

        D --> I[AnnotationProcessor]
        E --> J[PropertyLoader]
        E --> K[ConditionalEvaluator]

        subgraph "扩展组件"
            L[EventPublisher]
            M[SchedulerManager]
            N[WebIntegration]
        end

        B --> L
        B --> M
        B --> N
    end
```

### 2.2 分层架构

```mermaid
graph TB
    subgraph "应用层"
        A1[用户应用代码]
        A2[业务逻辑]
        A3[控制器层]
    end

    subgraph "框架层"
        F1[Web集成层]
        F2[注解处理层]
        F3[事件系统层]
        F4[调度系统层]
    end

    subgraph "容器核心层"
        C1[ApplicationContext]
        C2[BeanFactory]
        C3[ComponentScanner]
        C4[ConfigurationManager]
    end

    subgraph "基础设施层"
        I1[PropertyLoader]
        I2[AnnotationProcessor]
        I3[ConditionalEvaluator]
        I4[BeanPostProcessor]
    end

    A1 --> F1
    A2 --> F2
    A3 --> F3
    F1 --> C1
    F2 --> C2
    F3 --> C3
    F4 --> C4
    C1 --> I1
    C2 --> I2
    C3 --> I3
    C4 --> I4
```

## 3. 框架启动流程

```mermaid
sequenceDiagram
    participant App as MiniBootApplication
    participant Context as ApplicationContext
    participant Scanner as ComponentScanner
    participant Factory as BeanFactory
    participant Config as ConfigurationManager

    App->>Context: 1. 创建应用上下文
    Context->>Config: 2. 加载配置文件
    Config-->>Context: 配置属性
    Context->>Scanner: 3. 扫描组件
    Scanner-->>Context: 组件定义列表
    Context->>Factory: 4. 注册Bean定义
    Factory->>Factory: 5. 创建Bean实例
    Factory->>Factory: 6. 依赖注入
    Factory->>Factory: 7. 初始化Bean
    Context->>App: 8. 应用就绪
```

### 3.1 启动阶段说明

1. **应用初始化**: MiniBootApplication 创建 ApplicationContext
2. **配置加载**: 加载 application.yml 等配置文件
3. **组件扫描**: 扫描指定包路径下的组件类
4. **Bean 注册**: 将扫描到的组件注册为 Bean 定义
5. **自动配置**: 处理自动配置类和条件装配
6. **Bean 创建**: 根据依赖关系创建 Bean 实例
7. **依赖注入**: 注入 Bean 之间的依赖关系
8. **初始化完成**: 调用初始化方法，应用就绪

## 4. 核心组件

### 4.1 应用层组件

```mermaid
graph LR
    A[MiniBootApplication] --> B[应用启动入口]
    A --> C[配置扫描路径]
    A --> D[启动Web服务器]
    A --> E[生命周期管理]
```

**MiniBootApplication 职责**：

-   应用程序启动入口
-   初始化 ApplicationContext
-   配置组件扫描路径
-   启动内嵌 Web 服务器
-   管理应用生命周期

### 4.2 容器核心组件

```mermaid
graph TD
    subgraph "ApplicationContext - 应用上下文"
        A1[Bean生命周期管理]
        A2[配置加载协调]
        A3[组件扫描协调]
        A4[扩展功能集成]
    end

    subgraph "BeanFactory - Bean工厂"
        B1[Bean实例创建]
        B2[依赖注入处理]
        B3[作用域管理]
        B4[循环依赖解决]
    end

    subgraph "ComponentScanner - 组件扫描器"
        C1[包路径扫描]
        C2[注解解析]
        C3[Bean定义生成]
        C4[条件判断]
    end
```

**组件职责说明**：

-   **ApplicationContext**: 容器的核心，统一管理所有组件的生命周期
-   **BeanFactory**: 负责 Bean 的创建、依赖注入和作用域管理
-   **ComponentScanner**: 扫描指定包路径，解析注解，生成 Bean 定义

### 4.3 配置管理组件

```mermaid
graph LR
    subgraph "ConfigurationManager"
        A[PropertyLoader] --> B[YAML/JSON解析]
        A --> C[环境变量读取]
        A --> D[命令行参数]

        E[ConditionalEvaluator] --> F[条件注解评估]
        E --> G[自动配置选择]

        H[AutoConfiguration] --> I[自动配置类加载]
        H --> J[配置属性绑定]
    end
```

**配置管理职责**：

-   **PropertyLoader**: 加载各种配置源（YAML、环境变量、命令行参数）
-   **ConditionalEvaluator**: 评估条件注解，决定是否启用某个配置
-   **AutoConfiguration**: 管理自动配置类的加载和属性绑定

## 5. Bean 生命周期

```mermaid
flowchart TD
    A[Bean定义注册] --> B[实例化]
    B --> C[属性注入]
    C --> D[BeanPostProcessor前置处理]
    D --> E[初始化方法调用]
    E --> F[BeanPostProcessor后置处理]
    F --> G[Bean就绪]
    G --> H[使用阶段]
    H --> I[销毁方法调用]
    I --> J[Bean销毁]

    style A fill:#e1f5fe
    style G fill:#e8f5e9
    style J fill:#ffebee
```

### 5.1 生命周期阶段

1. **Bean 定义注册**: 将组件类注册为 Bean 定义
2. **实例化**: 创建 Bean 实例对象
3. **属性注入**: 注入依赖的其他 Bean
4. **前置处理**: BeanPostProcessor 前置处理
5. **初始化**: 调用 Bean 的初始化方法
6. **后置处理**: BeanPostProcessor 后置处理
7. **Bean 就绪**: Bean 可以正常使用
8. **使用阶段**: Bean 的正常使用期
9. **销毁方法**: 容器关闭时调用销毁方法
10. **Bean 销毁**: Bean 被垃圾回收

## 6. 注解处理流程

```mermaid
graph TD
    A[类扫描] --> B{发现注解?}
    B -->|是| C[解析注解属性]
    B -->|否| D[跳过]

    C --> E{注解类型}
    E -->|@Component| F[注册为组件]
    E -->|@Configuration| G[处理配置类]
    E -->|@Bean| H[注册Bean方法]
    E -->|@Autowired| I[标记依赖注入点]

    F --> J[创建BeanDefinition]
    G --> K[扫描@Bean方法]
    H --> J
    I --> L[记录依赖关系]

    J --> M[注册到BeanRegistry]
    K --> J
    L --> M
```

### 6.1 支持的注解

-   **@Component**: 标记通用组件
-   **@Service**: 标记服务层组件
-   **@Repository**: 标记数据访问层组件
-   **@Controller**: 标记控制器组件
-   **@Configuration**: 标记配置类
-   **@Bean**: 定义 Bean 方法
-   **@Autowired**: 标记依赖注入点
-   **@Value**: 注入配置属性值

## 7. 自动配置机制

```mermaid
flowchart LR
    A[启动应用] --> B[加载META-INF/mini.factories]
    B --> C[获取自动配置类列表]
    C --> D[条件评估]

    D --> E{条件满足?}
    E -->|是| F[加载配置类]
    E -->|否| G[跳过配置]

    F --> H[处理@Bean方法]
    H --> I[注册Bean定义]
    I --> J[配置生效]

    G --> K[继续下一个配置]
    J --> K
    K --> L{还有配置?}
    L -->|是| D
    L -->|否| M[自动配置完成]
```

### 7.1 条件装配

-   **@ConditionalOnClass**: 当类路径存在指定类时生效
-   **@ConditionalOnBean**: 当容器中存在指定 Bean 时生效
-   **@ConditionalOnMissingBean**: 当容器中不存在指定 Bean 时生效
-   **@ConditionalOnProperty**: 当配置属性满足条件时生效

## 8. Web 集成架构

```mermaid
graph TB
    subgraph "Web集成层"
        A[WebAutoConfiguration] --> B[FastAPI实例创建]
        A --> C[中间件注册]
        A --> D[异常处理器配置]

        E[WebAnnotationProcessor] --> F[扫描@RestController]
        E --> G[解析@RequestMapping]
        E --> H[注册路由]

        I[WebProperties] --> J[服务器配置]
        I --> K[CORS配置]
        I --> L[中间件配置]
    end

    B --> M[启动Web服务器]
    H --> M
    J --> M
```

### 8.1 Web 功能特性

-   **REST API 支持**: 基于 FastAPI 的 REST 服务
-   **路由自动注册**: 基于注解的路由配置
-   **中间件支持**: CORS、Gzip、日志等中间件
-   **异常处理**: 全局异常处理机制
-   **文档生成**: 自动生成 Swagger 文档

## 9. 事件系统架构

```mermaid
sequenceDiagram
    participant Publisher as EventPublisher
    participant Bus as EventBus
    participant Listener as EventListener
    participant Async as AsyncProcessor

    Publisher->>Bus: 发布事件
    Bus->>Bus: 查找监听器

    alt 同步事件
        Bus->>Listener: 直接调用
        Listener-->>Bus: 处理完成
    else 异步事件
        Bus->>Async: 提交到异步处理器
        Async->>Listener: 异步调用
        Listener-->>Async: 处理完成
    end

    Bus-->>Publisher: 发布完成
```

### 9.1 事件系统特性

-   **同步事件**: 立即处理，阻塞发布者
-   **异步事件**: 异步处理，不阻塞发布者
-   **事件监听器**: 基于注解的事件监听
-   **条件过滤**: 支持条件判断的事件处理
-   **优先级排序**: 监听器执行优先级控制

## 10. 调度系统架构

```mermaid
graph LR
    A[SchedulerAutoConfiguration] --> B[AsyncScheduler创建]
    A --> C[SchedulerManager创建]

    D[ScheduledAnnotationProcessor] --> E[扫描@Scheduled方法]
    E --> F[创建任务定义]
    F --> G[注册到调度器]

    B --> H[APScheduler集成]
    C --> I[任务生命周期管理]
    G --> I

    H --> J[任务执行]
    I --> J
```

### 10.1 调度功能特性

-   **定时任务**: 支持 Cron 表达式和固定频率
-   **异步执行**: 基于异步的任务执行
-   **任务管理**: 任务的启动、停止、监控
-   **错过策略**: 任务错过时的处理策略
-   **并发控制**: 任务并发执行的控制

## 11. 整体数据流

```mermaid
flowchart TD
    A[应用启动] --> B[配置加载]
    B --> C[组件扫描]
    C --> D[Bean定义注册]
    D --> E[自动配置处理]
    E --> F[Bean实例创建]
    F --> G[依赖注入]
    G --> H[初始化完成]

    H --> I[Web服务启动]
    H --> J[事件系统就绪]
    H --> K[调度器启动]

    I --> L[应用就绪]
    J --> L
    K --> L

    L --> M[处理请求]
    L --> N[执行定时任务]
    L --> O[处理事件]
```

## 12. 核心设计原则

### 12.1 分层清晰

-   **应用层**: MiniBootApplication - 应用启动和生命周期管理
-   **框架层**: Web 集成、注解处理、事件系统、调度系统
-   **容器核心层**: ApplicationContext、BeanFactory、ComponentScanner
-   **基础设施层**: PropertyLoader、AnnotationProcessor、ConditionalEvaluator

### 12.2 职责单一

-   每个组件只负责一个核心功能
-   通过接口和抽象类定义清晰的边界
-   避免组件间的直接耦合

### 12.3 扩展性强

-   通过 BeanPostProcessor 提供扩展点
-   支持自定义自动配置
-   事件驱动的松耦合架构
-   插件化的功能模块

### 12.4 配置驱动

-   约定优于配置的设计理念
-   支持外部化配置管理
-   条件装配机制
-   自动配置和属性绑定

## 13. 技术特色

### 13.1 Python 原生特性

-   充分利用 Python 装饰器实现注解系统
-   基于 Python 元类的框架级抽象
-   利用 Python 动态特性实现灵活配置
-   支持 Python 异步编程模型

### 13.2 Spring Boot 对标

-   相同的注解体系和使用方式
-   类似的自动配置机制
-   一致的应用启动流程
-   对等的企业级功能特性

### 13.3 现代化设计

-   支持类型注解和静态类型检查
-   基于 FastAPI 的现代 Web 集成
-   异步优先的架构设计
-   容器化友好的部署方式
