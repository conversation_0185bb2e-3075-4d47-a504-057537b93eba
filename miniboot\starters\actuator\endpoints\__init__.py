"""
内置端点实现

提供Actuator模块的内置端点实现,包括健康检查、指标、信息等.
"""

# 基础类和接口
from .base import (BaseEndpoint, CacheEntry, CacheStrategy, Endpoint,
                   EndpointOperation, OperationType, PerformanceStats,
                   SmartCache)
# 具体端点实现
from .beans import BeansEndpoint, create_beans_endpoint
from .custom import (ConfigEndpointHandler, CustomEndpoint,
                     CustomEndpointBuilder, CustomEndpointHandler)
from .custom import EndpointRegistry as CustomEndpointRegistry
from .custom import (FunctionEndpointHandler, LambdaEndpointHandler,
                     StatusEndpointHandler, VersionEndpointHandler, delete,
                     endpoint, read, write)
from .health import HealthEndpoint, HealthIndicator, HealthStatus
from .info import InfoContributor, InfoEndpoint
from .metrics import (BaseMetrics, Counter, Gauge, MetricsCollector,
                      MetricsConfig, MetricsEndpoint, MetricsRegistry,
                      TaskMetrics, Timer)
from .registry import EndpointRegistry

__all__ = [
    # 基础类和接口
    "Endpoint",
    "BaseEndpoint",
    "EndpointOperation",
    "OperationType",
    "EndpointRegistry",
    "CacheStrategy",
    "CacheEntry",
    "PerformanceStats",
    "SmartCache",

    # Bean 信息端点
    "BeansEndpoint",
    "create_beans_endpoint",
    # 健康检查
    "HealthEndpoint",
    "HealthIndicator",
    "HealthStatus",

    # 信息端点
    "InfoEndpoint",
    "InfoContributor",

    # 指标端点
    "BaseMetrics",
    "MetricsEndpoint",
    "MetricsRegistry",
    "MetricsCollector",
    "MetricsConfig",
    "TaskMetrics",
    "Counter",
    "Gauge",
    "Timer",

    # 自定义端点
    "CustomEndpoint",
    "CustomEndpointBuilder",
    "CustomEndpointHandler",
    "FunctionEndpointHandler",
    "LambdaEndpointHandler",
    "CustomEndpointRegistry",
    "endpoint",
    "read",
    "write",
    "delete",
    "StatusEndpointHandler",
    "ConfigEndpointHandler",
    "VersionEndpointHandler",
]
