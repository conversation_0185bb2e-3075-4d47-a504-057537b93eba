# Mini-Boot 生产环境配置

miniboot:
    # 生产环境应用配置
    application:
        name: mini-boot-app-prod

    # 生产服务器配置
    server:
        port: 80
        debug: false

    # 生产数据库配置
    datasource:
        url: postgresql://localhost:5432/miniboot_prod
        driver: postgresql

    # 生产日志配置
    logging:
        level: WARNING # 生产环境使用WARNING级别

        # 控制台输出（生产环境关闭）
        console:
            enabled: false # 生产环境关闭控制台输出

        # 文件输出（生产环境主要输出方式）
        file:
            enabled: true
            path: /var/log/miniboot/app.log # 生产环境标准日志路径
            level: INFO # 生产环境文件日志级别
            rotation: "1 GB" # 生产环境较大的轮转大小
            retention: "30 days" # 生产环境较长的保留时间
            compression: "zip" # 生产环境启用压缩节省空间
