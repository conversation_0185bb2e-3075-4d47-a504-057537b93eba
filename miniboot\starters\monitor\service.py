#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Monitor服务实现
"""

import threading
import time
from dataclasses import dataclass, field
from typing import Any, Optional

from loguru import logger

from .properties import MonitorProperties


@dataclass
class HealthCheckResult:
    """健康检查结果"""

    name: str
    status: str
    details: Optional[dict[str, Any]] = None
    timestamp: float = field(default_factory=time.time)


class MonitorService:
    """Monitor 演示实现"""

    def __init__(self, properties: MonitorProperties):
        """初始化 Monitor 服务

        Args:
            properties: Monitor配置属性
        """
        self.properties = properties
        self._running = False
        self._monitor_thread = None
        self._metrics = {}

    def is_enabled(self) -> bool:
        """检查服务是否启用

        Returns:
            是否启用
        """
        return self.properties.enabled

    def start(self) -> None:
        """启动监控服务"""
        if not self.is_enabled() or self._running:
            return

        self._running = True

        if self.properties.auto_start:
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            logger.info("Monitor service started with auto collection")
        else:
            logger.info("Monitor service started")

    def stop(self) -> None:
        """停止监控服务"""
        self._running = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=2.0)
        logger.info("Monitor service stopped")

    def is_running(self) -> bool:
        """检查服务是否运行中

        Returns:
            是否运行中
        """
        return self._running

    def _monitor_loop(self) -> None:
        """监控循环 - 定时采集指标"""
        logger.debug("Monitor loop started")

        while self._running:
            try:
                # 采集指标
                metrics = self.collect_metrics()
                if metrics:
                    self._metrics.update(metrics)

                # 等待下一次采集
                time.sleep(self.properties.collect_interval)

            except Exception as e:
                logger.error(f"Monitor loop error: {e}")
                time.sleep(1)  # 短暂等待后继续

    def collect_metrics(self) -> dict:
        """采集系统指标

        Returns:
            采集到的指标数据
        """
        if not self.is_enabled() or not self.properties.metrics_enabled:
            return {}

        metrics = {"timestamp": time.time(), "cpu_usage": 0.0, "memory_usage": 0.0, "disk_usage": 0.0}

        try:
            # 尝试使用psutil获取真实指标
            import psutil

            metrics["cpu_usage"] = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            metrics["memory_usage"] = memory.percent
            disk = psutil.disk_usage("/")
            metrics["disk_usage"] = (disk.used / disk.total) * 100
        except ImportError:
            # 如果没有psutil,使用模拟数据
            import random

            metrics["cpu_usage"] = random.uniform(10, 80)
            metrics["memory_usage"] = random.uniform(20, 70)
            metrics["disk_usage"] = random.uniform(30, 60)
        except Exception as e:
            logger.debug(f"Failed to collect real metrics: {e}")

        logger.debug(f"Collected metrics: {metrics}")
        return metrics

    def get_monitor_data(self) -> dict:
        """获取监控数据

        Returns:
            监控数据字典
        """
        if not self.is_enabled():
            return {}

        return {
            "service": "MonitorService",
            "enabled": self.properties.enabled,
            "auto_start": self.properties.auto_start,
            "collect_interval": self.properties.collect_interval,
            "metrics_enabled": self.properties.metrics_enabled,
            "health_enabled": self.properties.health_enabled,
            "running": self._running,
            "latest_metrics": self._metrics,
            "message": "This is monitor data from MonitorService",
        }
