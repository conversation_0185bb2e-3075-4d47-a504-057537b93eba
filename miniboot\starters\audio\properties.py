#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频播放 Starter 配置属性
"""

from dataclasses import dataclass, field

from miniboot.annotations import ConfigurationProperties
from miniboot.autoconfigure.properties import StarterProperties


@dataclass
class AudioPlayerConfig:
    """音频播放器配置"""

    enabled: bool = True
    volume: float = 1.0
    timeout_seconds: int = 30
    supported_formats: list[str] = field(default_factory=lambda: [".mp3", ".wav", ".m4a", ".ogg"])


@dataclass
class TTSConfig:
    """TTS配置"""

    enabled: bool = True
    engine: str = "pyttsx3"
    language: str = "zh-CN"
    rate: int = 150
    volume: float = 1.0


@ConfigurationProperties(prefix="miniboot.audio")
@dataclass
class AudioProperties(StarterProperties):
    """音频播放配置属性

    用于配置音频播放和TTS功能的各种参数.
    """

    # 基础配置
    enabled: bool = True

    # 音频播放配置
    player: AudioPlayerConfig = field(default_factory=AudioPlayerConfig)

    # TTS配置
    tts: TTSConfig = field(default_factory=TTSConfig)

    def validate(self) -> None:
        """验证配置参数"""
        if self.player.volume < 0.0 or self.player.volume > 1.0:
            raise ValueError("音频播放音量必须在 0.0 到 1.0 之间")

        if self.tts.volume < 0.0 or self.tts.volume > 1.0:
            raise ValueError("TTS音量必须在 0.0 到 1.0 之间")

        if self.tts.rate < 50 or self.tts.rate > 300:
            raise ValueError("TTS语速必须在 50 到 300 之间")

        if self.player.timeout_seconds <= 0:
            raise ValueError("音频播放超时时间必须大于 0")
