#!/usr/bin/env python
"""
* @author: cz
* @description: 事件发布器单元测试
"""

import asyncio
import unittest

from miniboot.events import ApplicationEvent, ApplicationEventPublisher, Event, EventHandlerInfo, EventPublisher


class TestEvent(Event):
    """测试事件类"""

    def __init__(self, data: str = "test"):
        super().__init__()
        self.data = data

    def get_event_name(self) -> str:
        """获取事件名称"""
        return "TestEvent"


class TestApplicationEvent(ApplicationEvent):
    """测试应用事件类"""

    def __init__(self, action: str, user_id: str = "test_user"):
        super().__init__(data={"action": action, "user_id": user_id})
        self.action = action
        self.user_id = user_id


class TestEventHandlerInfo(unittest.TestCase):
    """事件处理器信息测试"""

    def test_handler_info_creation(self):
        """测试处理器信息创建"""

        def test_handler(event):
            return "handled"

        handler_info = EventHandlerInfo(handler=test_handler, event_type=TestEvent, order=1, async_exec=False, condition="event.data == 'test'")

        self.assertEqual(handler_info.handler, test_handler)
        self.assertEqual(handler_info.event_type, TestEvent)
        self.assertEqual(handler_info.order, 1)
        self.assertFalse(handler_info.async_exec)
        self.assertEqual(handler_info.condition, "event.data == 'test'")
        self.assertEqual(handler_info.handler_name, "test_handler")
        self.assertTrue(handler_info.handler_id.startswith("TestEvent_"))

    def test_can_handle_event_type(self):
        """测试事件类型匹配"""

        def test_handler(event):
            pass

        handler_info = EventHandlerInfo(handler=test_handler, event_type=TestEvent)

        # 正确的事件类型
        test_event = TestEvent("test")
        self.assertTrue(handler_info.can_handle(test_event))

        # 错误的事件类型
        app_event = TestApplicationEvent("login")
        self.assertFalse(handler_info.can_handle(app_event))

    def test_condition_evaluation(self):
        """测试条件评估"""

        def test_handler(event):
            pass

        handler_info = EventHandlerInfo(handler=test_handler, event_type=TestEvent, condition="event.data == 'valid'")

        # 满足条件的事件
        valid_event = TestEvent("valid")
        self.assertTrue(handler_info.can_handle(valid_event))

        # 不满足条件的事件
        invalid_event = TestEvent("invalid")
        self.assertFalse(handler_info.can_handle(invalid_event))

    def test_execute_sync(self):
        """测试同步执行"""

        def test_handler(event):
            return f"handled: {event.data}"

        handler_info = EventHandlerInfo(handler=test_handler, event_type=TestEvent)

        event = TestEvent("test_data")
        result = handler_info.execute_sync(event)

        self.assertEqual(result, "handled: test_data")

    def test_execute_with_instance(self):
        """测试实例方法执行"""

        class TestHandler:
            def handle_event(self, event):
                return f"instance handled: {event.data}"

        handler_instance = TestHandler()
        handler_info = EventHandlerInfo(
            handler=handler_instance.handle_event,  # 使用绑定的方法
            event_type=TestEvent,
            instance=handler_instance,
        )

        event = TestEvent("test_data")
        result = handler_info.execute_sync(event)

        self.assertEqual(result, "instance handled: test_data")


class TestEventPublisher(unittest.TestCase):
    """事件发布器测试"""

    def setUp(self):
        """设置测试"""
        self.publisher = EventPublisher()
        self.handled_events = []

    def tearDown(self):
        """清理测试"""
        self.publisher.shutdown()

    def test_publisher_initialization(self):
        """测试发布器初始化"""
        self.assertEqual(self.publisher.get_handler_count(), 0)
        self.assertEqual(len(self.publisher.get_registered_event_types()), 0)

        stats = self.publisher.get_stats()
        self.assertEqual(stats["published_events"], 0)
        self.assertEqual(stats["handled_events"], 0)
        self.assertEqual(stats["failed_events"], 0)
        self.assertEqual(stats["registered_handlers"], 0)

    def test_subscribe_and_unsubscribe(self):
        """测试订阅和取消订阅"""

        def test_handler(event):
            self.handled_events.append(event)

        # 订阅事件
        handler_id = self.publisher.subscribe(TestEvent, test_handler)

        self.assertEqual(self.publisher.get_handler_count(), 1)
        self.assertEqual(self.publisher.get_handler_count(TestEvent), 1)
        self.assertIn(TestEvent, self.publisher.get_registered_event_types())

        # 取消订阅
        success = self.publisher.unsubscribe(handler_id)

        self.assertTrue(success)
        self.assertEqual(self.publisher.get_handler_count(), 0)
        self.assertEqual(len(self.publisher.get_registered_event_types()), 0)

    def test_publish_sync(self):
        """测试同步发布"""

        def test_handler(event):
            self.handled_events.append(event)

        # 订阅事件
        self.publisher.subscribe(TestEvent, test_handler)

        # 发布事件
        event = TestEvent("sync_test")
        self.publisher.publish(event)

        # 验证处理结果
        self.assertEqual(len(self.handled_events), 1)
        self.assertEqual(self.handled_events[0].data, "sync_test")
        self.assertTrue(event.is_processed())

        # 验证统计信息
        stats = self.publisher.get_stats()
        self.assertEqual(stats["published_events"], 1)
        self.assertEqual(stats["handled_events"], 1)

    def test_publish_async(self):
        """测试异步发布"""

        async def async_test():
            async def async_handler(event):
                await asyncio.sleep(0.01)  # 模拟异步操作
                self.handled_events.append(event)

            # 订阅异步事件
            self.publisher.subscribe(TestEvent, async_handler, async_exec=True)

            # 异步发布事件
            event = TestEvent("async_test")
            await self.publisher.publish_async(event)

            # 验证处理结果
            self.assertEqual(len(self.handled_events), 1)
            self.assertEqual(self.handled_events[0].data, "async_test")
            self.assertTrue(event.is_processed())

        # 运行异步测试
        asyncio.run(async_test())

    def test_multiple_handlers(self):
        """测试多个处理器"""
        results = []

        def handler1(event):
            results.append(f"handler1: {event.data}")

        def handler2(event):
            results.append(f"handler2: {event.data}")

        # 订阅多个处理器
        self.publisher.subscribe(TestEvent, handler1, order=2)
        self.publisher.subscribe(TestEvent, handler2, order=1)

        # 发布事件
        event = TestEvent("multi_test")
        self.publisher.publish(event)

        # 验证执行顺序（按order排序）
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0], "handler2: multi_test")  # order=1先执行
        self.assertEqual(results[1], "handler1: multi_test")  # order=2后执行

    def test_condition_filtering(self):
        """测试条件过滤"""

        def conditional_handler(event):
            self.handled_events.append(event)

        # 订阅带条件的处理器
        self.publisher.subscribe(TestEvent, conditional_handler, condition="event.data == 'valid'")

        # 发布满足条件的事件
        valid_event = TestEvent("valid")
        self.publisher.publish(valid_event)

        # 发布不满足条件的事件
        invalid_event = TestEvent("invalid")
        self.publisher.publish(invalid_event)

        # 只有满足条件的事件被处理
        self.assertEqual(len(self.handled_events), 1)
        self.assertEqual(self.handled_events[0].data, "valid")

    def test_inheritance_matching(self):
        """测试继承匹配"""

        def event_handler(event):
            self.handled_events.append(event)

        # 订阅父类事件
        self.publisher.subscribe(Event, event_handler)

        # 发布子类事件
        test_event = TestEvent("inheritance_test")
        app_event = TestApplicationEvent("login")

        self.publisher.publish(test_event)
        self.publisher.publish(app_event)

        # 父类处理器应该处理所有子类事件
        self.assertEqual(len(self.handled_events), 2)

    def test_error_handling(self):
        """测试错误处理"""

        def failing_handler(event):
            raise ValueError("Handler error")

        def normal_handler(event):
            self.handled_events.append(event)

        # 订阅处理器
        self.publisher.subscribe(TestEvent, failing_handler)
        self.publisher.subscribe(TestEvent, normal_handler)

        # 发布事件
        event = TestEvent("error_test")
        self.publisher.publish(event)  # 不应该抛出异常

        # 正常处理器仍然应该执行
        self.assertEqual(len(self.handled_events), 1)

        # 统计信息应该反映错误
        stats = self.publisher.get_stats()
        self.assertEqual(stats["failed_events"], 1)
        self.assertEqual(stats["handled_events"], 1)

    def test_clear_handlers(self):
        """测试清空处理器"""

        def test_handler(event):
            pass

        # 添加一些处理器
        self.publisher.subscribe(TestEvent, test_handler)
        self.publisher.subscribe(TestApplicationEvent, test_handler)

        self.assertEqual(self.publisher.get_handler_count(), 2)

        # 清空处理器
        self.publisher.clear_handlers()

        self.assertEqual(self.publisher.get_handler_count(), 0)
        self.assertEqual(len(self.publisher.get_registered_event_types()), 0)


class TestApplicationEventPublisher(unittest.TestCase):
    """应用事件发布器测试"""

    def setUp(self):
        """设置测试"""
        self.app_publisher = ApplicationEventPublisher()
        self.handled_events = []

    def tearDown(self):
        """清理测试"""
        self.app_publisher.shutdown()

    def test_app_publisher_initialization(self):
        """测试应用发布器初始化"""
        stats = self.app_publisher.get_stats()
        self.assertEqual(stats["published_events"], 0)
        self.assertEqual(stats["handled_events"], 0)

    def test_register_and_publish(self):
        """测试注册和发布"""

        def test_handler(event):
            self.handled_events.append(event)

        # 注册监听器
        handler_id = self.app_publisher.register_listener(TestEvent, test_handler)

        # 发布事件
        event = TestEvent("app_test")
        self.app_publisher.publish_event(event)

        # 验证结果
        self.assertEqual(len(self.handled_events), 1)
        self.assertEqual(self.handled_events[0].data, "app_test")

        # 取消注册
        success = self.app_publisher.unregister_listener(handler_id)
        self.assertTrue(success)


class TestApplicationEventPublisherAdvanced(unittest.TestCase):
    """测试ApplicationEventPublisher应用事件发布器高级功能"""

    def setUp(self):
        """设置测试环境"""
        self.app_publisher = ApplicationEventPublisher()

    def test_app_publisher_initialization(self):
        """测试应用事件发布器初始化"""
        self.assertIsNotNone(self.app_publisher._publisher)
        self.assertTrue(self.app_publisher._auto_discovery_enabled)

    def test_publish_event_sync(self):
        """测试同步发布事件"""
        event = TestApplicationEvent("test_data")

        # 注册一个处理器
        handler_called = []

        def test_handler(e):
            handler_called.append(e.data)

        self.app_publisher.register_listener(TestApplicationEvent, test_handler)

        # 发布事件
        self.app_publisher.publish_event(event)

        self.assertEqual(len(handler_called), 1)
        # TestApplicationEvent的data是字典格式
        self.assertEqual(handler_called[0]["action"], "test_data")

    def test_publish_event_async(self):
        """测试异步发布事件"""
        import asyncio

        async def test_async():
            event = TestApplicationEvent("async_data")

            # 注册一个异步处理器
            handler_called = []

            async def async_handler(e):
                handler_called.append(e.data)

            self.app_publisher.register_listener(TestApplicationEvent, async_handler, async_exec=True)

            # 异步发布事件
            await self.app_publisher.publish_event_async(event)

            self.assertEqual(len(handler_called), 1)
            # TestApplicationEvent的data是字典格式
            self.assertEqual(handler_called[0]["action"], "async_data")

        # 运行异步测试
        asyncio.run(test_async())

    def test_subscribe_and_unsubscribe(self):
        """测试订阅和取消订阅"""

        def test_handler(event):
            pass

        # 订阅
        handler_id = self.app_publisher.register_listener(TestApplicationEvent, test_handler)
        self.assertIsNotNone(handler_id)

        # 取消订阅
        result = self.app_publisher.unregister_listener(handler_id)
        self.assertTrue(result)

        # 再次取消订阅应该返回False
        result = self.app_publisher.unregister_listener(handler_id)
        self.assertFalse(result)

    def test_get_stats(self):
        """测试获取统计信息"""
        stats = self.app_publisher.get_stats()

        self.assertIn("published_events", stats)
        self.assertIn("handled_events", stats)
        self.assertIn("active_handlers", stats)

    def test_shutdown(self):
        """测试关闭发布器"""
        # 添加一个处理器
        self.app_publisher.register_listener(TestApplicationEvent, lambda _: None)

        # 关闭
        self.app_publisher.shutdown()

        # 验证处理器被清空 - 由于shutdown可能不会立即清空统计，我们检查是否可以正常关闭
        # 而不是检查具体的统计数字
        try:
            self.app_publisher.shutdown()  # 再次调用应该不会出错
        except Exception as e:
            self.fail(f"Shutdown should not raise exception: {e}")


class TestEventPublisherAdvanced(unittest.TestCase):
    """测试EventPublisher的高级功能"""

    def setUp(self):
        """设置测试环境"""
        self.publisher = EventPublisher()

    def test_condition_evaluation(self):
        """测试条件表达式评估"""
        event = TestEvent("test_data")

        # 测试条件为True的处理器
        handler_called = []

        def conditional_handler(e):
            handler_called.append("true_condition")

        self.publisher.subscribe(TestEvent, conditional_handler, condition="event.data == 'test_data'")

        # 测试条件为False的处理器
        def false_conditional_handler(e):
            handler_called.append("false_condition")

        self.publisher.subscribe(TestEvent, false_conditional_handler, condition="event.data == 'other_data'")

        # 发布事件
        self.publisher.publish(event)

        # 只有条件为True的处理器应该被调用
        self.assertEqual(len(handler_called), 1)
        self.assertEqual(handler_called[0], "true_condition")

    def test_handler_priority_ordering(self):
        """测试处理器优先级排序"""
        event = TestEvent("test_data")
        execution_order = []

        # 添加不同优先级的处理器
        def handler_low(e):
            execution_order.append("low")

        def handler_high(e):
            execution_order.append("high")

        def handler_medium(e):
            execution_order.append("medium")

        # 按非顺序添加处理器
        self.publisher.subscribe(TestEvent, handler_medium, order=5)
        self.publisher.subscribe(TestEvent, handler_high, order=1)  # 数字越小优先级越高
        self.publisher.subscribe(TestEvent, handler_low, order=10)

        # 发布事件
        self.publisher.publish(event)

        # 验证执行顺序
        self.assertEqual(execution_order, ["high", "medium", "low"])

    def test_error_handling_in_handlers(self):
        """测试处理器中的错误处理"""
        event = TestEvent("test_data")
        successful_calls = []

        def failing_handler(e):
            raise ValueError("Handler failed")

        def successful_handler(e):
            successful_calls.append("success")

        # 添加处理器
        self.publisher.subscribe(TestEvent, failing_handler)
        self.publisher.subscribe(TestEvent, successful_handler)

        # 发布事件，即使有处理器失败，其他处理器也应该继续执行
        self.publisher.publish(event)

        # 验证成功的处理器仍然被调用
        self.assertEqual(len(successful_calls), 1)

    def test_shutdown_behavior(self):
        """测试关闭行为"""
        # 添加处理器
        self.publisher.subscribe(TestEvent, lambda _: None)

        # 验证有处理器
        stats = self.publisher.get_stats()
        self.assertGreater(stats["active_handlers"], 0)

        # 关闭发布器
        self.publisher.shutdown()

        # 验证关闭后不能再发布事件
        with self.assertRaises(RuntimeError):
            self.publisher.publish(TestEvent("test"))

        # 验证关闭后不能再订阅
        with self.assertRaises(RuntimeError):
            self.publisher.subscribe(TestEvent, lambda _: None)


if __name__ == "__main__":
    unittest.main()
