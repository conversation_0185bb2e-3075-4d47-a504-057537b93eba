# 示例项目模板

本文档提供了创建新示例项目的标准模板，确保所有示例都遵循统一的结构和风格。

## 📁 目录结构模板

### 简单示例模板（基础和进阶示例）

适用于：01-hello-world, 02-dependency-injection, 03-configuration, 04-events, 05-auto-configuration, 06-bean-lifecycle, 07-multi-module

```
samples/XX-simple-example/
├── README.md                    # 项目文档 (必需)
├── main.py                      # 应用入口，包含所有代码 (必需)
├── requirements.txt             # Python依赖 (必需)
└── resources/                   # 配置文件目录
    ├── application.yml          # 基础配置 (必需)
    ├── application-dev.yml      # 开发环境配置 (可选)
    └── application-prod.yml     # 生产环境配置 (可选)
```

### 复杂系统模板（实战示例）

适用于：08-web-service, 09-data-access, 10-scheduled-tasks, 11-actuator-endpoints, 12-library-system

```
samples/XX-complex-system/
├── README.md                    # 项目文档 (必需)
├── main.py                      # 应用入口 (必需)
├── requirements.txt             # Python依赖 (必需)
├── requirements-dev.txt         # 开发依赖 (推荐)
├── resources/                   # 配置文件目录
│   ├── application.yml          # 基础配置 (必需)
│   ├── application-dev.yml      # 开发环境配置
│   └── application-prod.yml     # 生产环境配置
├── src/                         # 源代码目录
│   ├── __init__.py
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   └── user.py
│   ├── services/                # 业务服务
│   │   ├── __init__.py
│   │   └── user_service.py
│   ├── controllers/             # 控制器 (Web示例)
│   │   ├── __init__.py
│   │   └── user_controller.py
│   ├── config/                  # 配置类
│   │   ├── __init__.py
│   │   └── app_config.py
│   └── exceptions/              # 自定义异常
│       ├── __init__.py
│       └── business_exceptions.py
├── tests/                       # 测试代码
│   ├── __init__.py
│   ├── unit/                    # 单元测试
│   │   ├── __init__.py
│   │   ├── test_services.py
│   │   └── test_models.py
│   ├── integration/             # 集成测试
│   │   ├── __init__.py
│   │   └── test_app.py
│   └── conftest.py              # pytest配置
├── docs/                        # 详细文档
│   ├── architecture.md          # 架构说明
│   ├── api.md                   # API文档 (Web示例)
│   └── deployment.md            # 部署指南
├── scripts/                     # 辅助脚本
│   ├── setup.sh                 # 环境设置
│   ├── run.sh                   # 运行脚本
│   └── test.sh                  # 测试脚本
├── Dockerfile                   # Docker支持 (可选)
├── docker-compose.yml           # 容器编排 (可选)
├── .gitignore                   # Git忽略文件
└── .github/                     # GitHub配置 (可选)
    └── workflows/
        └── ci.yml               # CI/CD配置
```

## 📝 README.md 模板

````markdown
# XX-示例名称

## 📖 项目简介

[简要描述示例项目的目的和功能]

## 🎯 学习目标

通过本示例，你将学会：

-   [ ] 目标 1
-   [ ] 目标 2
-   [ ] 目标 3

## ✨ 功能特性

-   特性 1
-   特性 2
-   特性 3

## 🛠️ 技术栈

-   **Mini-Boot** - 主框架
-   **Python 3.8+** - 编程语言
-   **其他依赖** - 说明

## 🚀 快速开始

### 环境要求

-   Python 3.8+
-   pip 或 poetry

### 安装和运行

1. 克隆项目

```bash
git clone https://github.com/your-org/mini-boot.git
cd mini-boot/samples/XX-example-name
```
````

2. 安装依赖

```bash
pip install -r requirements.txt
```

3. 运行应用

```bash
python main.py
```

### 验证运行

[描述如何验证应用正常运行]

## 📚 代码解释

### 核心组件

#### 1. 应用入口 (main.py)

[解释应用启动逻辑]

#### 2. 配置管理

[解释配置文件和配置类]

#### 3. 业务逻辑

[解释主要的业务组件]

### 关键概念

#### 概念 1

[详细解释]

#### 概念 2

[详细解释]

## ⚙️ 配置说明

### 配置文件

| 配置项   | 说明     | 默认值      | 示例   |
| -------- | -------- | ----------- | ------ |
| app.name | 应用名称 | example-app | my-app |
| app.port | 端口号   | 8080        | 9000   |

### 环境变量

| 变量名       | 说明       | 必需 | 示例             |
| ------------ | ---------- | ---- | ---------------- |
| DATABASE_URL | 数据库连接 | 否   | postgresql://... |

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行单元测试
python -m pytest tests/unit/

# 运行集成测试
python -m pytest tests/integration/

# 生成覆盖率报告
python -m pytest tests/ --cov=src/
```

### 测试说明

[解释测试策略和重要测试用例]

## 🐳 部署 (实战示例)

### Docker 部署

```bash
# 构建镜像
docker build -t example-app .

# 运行容器
docker run -p 8080:8080 example-app
```

### Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 📊 监控 (实战示例)

### 健康检查

-   健康检查端点: `GET /health`
-   就绪检查端点: `GET /ready`

### 性能监控

[说明性能监控相关内容]

## ❓ 常见问题

### Q: 问题 1？

A: 答案 1

### Q: 问题 2？

A: 答案 2

## 🔗 相关资源

-   [Mini-Boot 官方文档](https://docs.mini-boot.org)
-   [相关示例](../README.md)
-   [API 参考](https://api.mini-boot.org)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个示例！

## 📄 许可证

本项目采用 MIT 许可证。

````

## 🐍 代码模板

### main.py 模板

```python
#!/usr/bin/env python3
"""
示例应用入口

这是一个Mini-Boot框架的示例应用，展示了[具体功能]。
"""

import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from miniboot import Application
from miniboot.context import ApplicationContext

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """应用主函数"""
    try:
        # 创建应用实例
        app = Application()

        # 设置配置文件路径
        app.set_config_locations([
            "resources/application.yml",
            "resources/application-${PROFILE:dev}.yml"
        ])

        # 启动应用
        logger.info("Starting Mini-Boot example application...")
        app.run()

        # 获取应用上下文
        context = app.get_context()

        # 演示核心功能
        demonstrate_features(context)

        logger.info("Application started successfully!")

        # 保持应用运行
        input("Press Enter to stop the application...")

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)
    finally:
        logger.info("Application stopped.")


def demonstrate_features(context: ApplicationContext):
    """演示应用功能"""
    # TODO: 实现功能演示逻辑
    pass


if __name__ == "__main__":
    main()
````

### 配置类模板

```python
"""
应用配置类

定义应用的配置和Bean。
"""

from miniboot.annotations import Configuration, Bean, Value
from miniboot.context import ApplicationContext


@Configuration
class AppConfig:
    """应用配置类"""

    @Value("${app.name:example-app}")
    def __init__(self, app_name: str):
        self.app_name = app_name

    @Bean
    def example_service(self) -> 'ExampleService':
        """创建示例服务Bean"""
        from src.services import ExampleService
        return ExampleService()
```

### 服务类模板

```python
"""
示例服务类

提供核心业务逻辑。
"""

from typing import Optional, List
from miniboot.annotations import Component, Autowired, Value
import logging


@Component
class ExampleService:
    """示例服务类"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def process_data(self, data: str) -> str:
        """处理数据

        Args:
            data: 输入数据

        Returns:
            处理后的数据

        Raises:
            ValueError: 当输入数据无效时
        """
        if not data:
            raise ValueError("Data cannot be empty")

        self.logger.info(f"Processing data: {data}")

        # TODO: 实现具体的业务逻辑
        result = f"Processed: {data}"

        self.logger.info(f"Processing completed: {result}")
        return result
```

### 测试模板

```python
"""
示例服务测试

测试ExampleService的功能。
"""

import unittest
from unittest.mock import Mock, patch

from src.services import ExampleService


class TestExampleService(unittest.TestCase):
    """示例服务测试类"""

    def setUp(self):
        """测试前置设置"""
        self.service = ExampleService()

    def test_process_data_success(self):
        """测试数据处理成功场景"""
        # Given
        input_data = "test data"
        expected_result = "Processed: test data"

        # When
        result = self.service.process_data(input_data)

        # Then
        self.assertEqual(result, expected_result)

    def test_process_data_empty_input(self):
        """测试空输入数据场景"""
        # Given
        input_data = ""

        # When & Then
        with self.assertRaises(ValueError) as context:
            self.service.process_data(input_data)

        self.assertEqual(str(context.exception), "Data cannot be empty")


if __name__ == '__main__':
    unittest.main()
```

---

## 📋 使用说明

1. **复制模板**: 复制相应的模板文件到新示例目录
2. **替换占位符**: 将 `XX-example-name` 等占位符替换为实际内容
3. **实现功能**: 根据示例需求实现具体功能
4. **完善文档**: 补充详细的说明和示例
5. **添加测试**: 编写充分的测试用例
6. **验证质量**: 使用检查清单验证示例质量

这个模板确保了所有示例项目的一致性和质量标准！
