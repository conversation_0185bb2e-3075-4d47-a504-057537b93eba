#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能监控自动配置

实现 Actuator 性能监控的自动配置功能，包括：
- PerformanceMonitor：性能监控器自动创建和启动
- 基于配置的条件化启动

配置条件：
- miniboot.starters.actuator.performance-monitoring=true (默认启用)
- 依赖 ActuatorStarterAutoConfiguration 已配置

使用示例：
    # application.yml
    miniboot:
        starters:
            actuator:
                performance-monitoring: true  # 启用性能监控
                metrics:
                    performance:
                        collection-interval: 10s  # 数据收集间隔
"""

import asyncio
from typing import Optional

from loguru import logger

from miniboot.annotations import (Autowired, Bean, ConditionalOnBean,
                                  ConditionalOnProperty)
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.monitoring.interfaces import MonitoringContext

from ..performance import PerformanceMonitor
from ..properties import ActuatorProperties


@ConditionalOnProperty(name="miniboot.starters.actuator.performance-monitoring", match_if_missing=True)
class PerformanceMonitorAutoConfiguration(AutoConfiguration):
    """性能监控自动配置类

    当满足以下条件时自动配置性能监控：
    1. miniboot.starters.actuator.performance-monitoring=true (默认启用)
    2. ActuatorStarterAutoConfiguration 已配置

    注册的 Bean：
    - performance_monitor: 性能监控器
    """

    def __init__(self):
        super().__init__()
        logger.info("🔧 PerformanceMonitorAutoConfiguration 初始化")

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="performance-monitor-auto-configuration",
            description="Actuator 性能监控自动配置",
            priority=300,  # 较低优先级，在基础设施之后
            auto_configure_after=["actuator-starter-auto-configuration"],
        )


    @Bean
    def performance_monitor(
        self,
        actuator_properties: ActuatorProperties,
        monitoring_context: Optional[MonitoringContext] = None
    ) -> "PerformanceMonitor":
        """创建性能监控器 Bean

        Args:
            actuator_properties: Actuator 配置属性
            monitoring_context: 监控上下文（可选）

        Returns:
            PerformanceMonitor: 性能监控器实例
        """
        # 从配置中获取监控间隔
        collection_interval = actuator_properties.performance.collection_interval
        logger.info(f"🔧 读取到的配置 collection_interval: {collection_interval}")
        logger.info(f"🔧 actuator_properties.performance: {actuator_properties.performance}")

        # 调试：检查配置属性的所有字段
        logger.info(f"🔧 performance.cache_ttl: {actuator_properties.performance.cache_ttl}")
        logger.info(f"🔧 performance.max_metrics: {actuator_properties.performance.max_metrics}")
        logger.info(f"🔧 performance.cleanup_interval: {actuator_properties.performance.cleanup_interval}")

        # 使用配置的间隔，只对无效值进行调整
        if collection_interval <= 0:
            collection_interval = 5.0  # 默认5秒
            logger.warning(f"⚠️  配置的收集间隔无效，使用默认值: {collection_interval}s")
        elif collection_interval < 1.0:
            collection_interval = 1.0  # 最小1秒间隔
            logger.info(f"🔧 调整 collection_interval 为: {collection_interval}s (避免过于频繁)")
        analysis_interval = collection_interval * 3  # 分析间隔是收集间隔的3倍

        monitor = PerformanceMonitor(
            collection_interval=collection_interval,
            analysis_interval=analysis_interval
        )

        # 配置监控目标（如果有监控上下文）
        if monitoring_context:
            monitor.configure_targets(actuator_context=monitoring_context)
            logger.info("🎯 Performance monitor targets configured with monitoring context")

        logger.info(f"Created PerformanceMonitor bean (collection: {collection_interval}s, analysis: {analysis_interval}s)")
        return monitor

    @Bean
    def performance_endpoint_provider(self, performance_monitor: PerformanceMonitor) -> "PerformanceEndpoint":
        """创建性能端点提供者"""
        from ..performance import PerformanceEndpoint

        endpoint = PerformanceEndpoint(performance_monitor)
        logger.debug("Created PerformanceEndpoint bean")
        return endpoint
