#!/usr/bin/env python
"""
* @author: cz
* @description: 横幅打印器实现
"""

import os
import sys
from abc import ABC, abstractmethod
from io import StringIO
from typing import TextIO

from .constants import BannerConstants
from .properties import BannerConfig, BannerMode
from .resource import BannerResource


def _generate_content(resource: BannerResource, config: BannerConfig) -> str:
    """生成横幅内容的通用函数

    Args:
        resource: 横幅资源
        config: 横幅配置

    Returns:
        str: 生成的横幅内容
    """
    string_output = StringIO()
    console_printer = ConsoleBannerPrinter(string_output)
    console_printer.print(resource, config)
    return string_output.getvalue()


def _console_config(config: BannerConfig) -> BannerConfig:
    """创建控制台模式的配置副本

    Args:
        config: 原始配置

    Returns:
        BannerConfig: 控制台模式的配置副本
    """


    return BannerConfig(
        enabled=True,
        mode=BannerMode.CONSOLE,
        location=config.location,
        charset=config.charset,
        show_version=config.show_version,
        show_environment=config.show_environment,
        show_startup_time=config.show_startup_time,
        colors=False,  # 日志和文件不需要颜色
        width=config.width,
        properties=config.properties,
    )


class BannerPrinter(ABC):
    """横幅打印器抽象基类"""

    @abstractmethod
    def print(self, resource: BannerResource, config: BannerConfig) -> None:
        """打印横幅

        Args:
            resource: 横幅资源
            config: 横幅配置
        """
        pass

    @classmethod
    def print_from_env(cls, environment, app_name: str = None, app_version: str = None) -> None:
        """从环境配置打印横幅

        Args:
            environment: 环境对象
            app_name: 应用名称
            app_version: 应用版本
        """
        try:
            from .resource import BannerResource
            from .properties import BannerConfig

            # 创建默认配置
            config = BannerConfig()

            # 创建横幅资源
            resource = BannerResource()

            # 设置应用信息
            if app_name:
                resource.set_variable("application.name", app_name)
            if app_version:
                resource.set_variable("application.version", app_version)

            # 使用控制台打印器
            printer = ConsoleBannerPrinter()
            printer.print(resource, config)

        except Exception as e:
            # 如果横幅打印失败，静默处理
            import sys
            print(f"Failed to print banner: {e}", file=sys.stderr)


class ConsoleBannerPrinter(BannerPrinter):
    """控制台横幅打印器"""

    def __init__(self, output: TextIO = sys.stdout):
        self.output = output

    def print(self, resource: BannerResource, config: BannerConfig) -> None:
        """打印横幅到控制台"""
        if not config.console_enabled():
            return

        content = self._process(resource, config)

        # 检查是否支持彩色输出并添加颜色
        if config.colors:
            # 检查是否为交互式终端
            supports_color = True
            if not (hasattr(sys.stdout, "isatty") and sys.stdout.isatty()):
                supports_color = False
            elif os.name == "nt":
                # Windows 支持 ANSI 颜色
                supports_color = True
            else:
                # Unix/Linux 支持
                supports_color = "TERM" in os.environ and os.environ["TERM"] != "dumb"

            if supports_color:
                # 添加颜色 - ANSI 颜色代码
                colors = {
                    "reset": "\033[0m",
                    "bold": "\033[1m",
                    "green": "\033[32m",
                    "blue": "\033[34m",
                    "cyan": "\033[36m",
                    "bright_green": "\033[92m",
                    "bright_blue": "\033[94m",
                    "bright_cyan": "\033[96m",
                    "bright_yellow": "\033[93m",
                }

                lines = content.split("\n")
                colored_lines = []

                for line in lines:
                    if "___" in line and "|" in line:
                        # ASCII艺术横幅 - 使用亮青色和粗体
                        colored_lines.append(f"{colors['bright_cyan']}{colors['bold']}{line}{colors['reset']}")
                    elif "::" in line:
                        # 应用名称行 - 使用亮绿色和粗体
                        colored_lines.append(f"{colors['bright_green']}{colors['bold']}{line}{colors['reset']}")
                    elif line.strip().startswith("应用信息:") or line.strip().startswith("系统信息:"):
                        # 信息标题 - 使用黄色和粗体
                        colored_lines.append(f"{colors['bright_yellow']}{colors['bold']}{line}{colors['reset']}")
                    elif ":" in line and ("Python版本:" in line or "CPU核心:" in line or "系统:" in line):
                        # 系统信息行 - 使用青色
                        colored_lines.append(f"{colors['cyan']}{line}{colors['reset']}")
                    elif ":" in line and ("描述:" in line or "环境:" in line or "启动时间:" in line):
                        # 应用信息行 - 使用蓝色
                        colored_lines.append(f"{colors['blue']}{line}{colors['reset']}")
                    else:
                        colored_lines.append(line)

                content = "\n".join(colored_lines)

        print(content, file=self.output)

        # 打印附加信息
        if config.show_version or config.show_environment or config.show_startup_time:
            self._show_info(resource, config)

    def _process(self, resource: BannerResource, config: BannerConfig) -> str:
        """处理横幅内容,替换模板变量"""
        content = resource.content()
        variables = resource.variables()

        # 添加自定义属性
        variables.update(config.properties)

        # 替换模板变量
        for key, value in variables.items():
            placeholder = "${" + key + "}"
            content = content.replace(placeholder, str(value))

        # 处理宽度限制
        if config.width > 0:
            content = self._resize(content, config.width)

        return content

    def _resize(self, content: str, width: int) -> str:
        """调整内容宽度"""
        lines = content.split("\n")
        adjusted_lines = []

        for line in lines:
            if len(line) > width:
                # 截断过长的行
                adjusted_lines.append(line[:width])
            else:
                adjusted_lines.append(line)

        return "\n".join(adjusted_lines)



    def _show_info(self, resource: BannerResource, config: BannerConfig) -> None:
        """打印附加信息"""
        variables = resource.variables()
        info_lines = []

        if config.show_version:
            app_version = variables.get("application.version", "unknown")
            python_version = variables.get("python.version", "unknown")
            info_lines.append(f"Application Version: {app_version}")
            info_lines.append(f"Python Version: {python_version}")

        if config.show_environment:
            os_name = variables.get("os.name", "unknown")
            os_version = variables.get("os.version", "unknown")
            info_lines.append(f"Operating System: {os_name} {os_version}")

        if config.show_startup_time:
            startup_time = variables.get("startup.time", "unknown")
            info_lines.append(f"Started at: {startup_time}")

        if info_lines:
            print("", file=self.output)  # 空行分隔
            for line in info_lines:
                print(f" {line}", file=self.output)
            print("", file=self.output)  # 空行结尾


class FileBannerPrinter(BannerPrinter):
    """文件横幅打印器"""

    def __init__(self, file_path: str, charset: str = BannerConstants.DEFAULT_CHARSET):
        self.file_path = file_path
        self.charset = charset

    def print(self, resource: BannerResource, config: BannerConfig) -> None:
        """打印横幅到文件"""
        # 使用工具函数生成内容
        content = _generate_content(resource, config)

        # 写入文件
        try:
            from pathlib import Path

            Path(self.file_path).write_text(content, encoding=self.charset)
        except OSError as e:
            # 如果写入失败,回退到控制台输出
            print(f"Failed to write banner to file {self.file_path}: {e}", file=sys.stderr)
            console_printer = ConsoleBannerPrinter()
            console_printer.print(resource, config)


class LogBannerPrinter(BannerPrinter):
    """日志横幅打印器"""

    def __init__(self, logger=None):
        self.logger = logger

    def print(self, resource: BannerResource, config: BannerConfig) -> None:
        """打印横幅到日志"""
        if not config.log_enabled():
            return

        if not self.logger:
            # 如果没有提供logger,静默返回
            return

        # 使用工具函数生成内容
        console_config = _console_config(config)
        content = _generate_content(resource, console_config)

        # 按行输出到日志
        for line in content.split("\n"):
            if line.strip():  # 跳过空行
                self.logger.info(line)
