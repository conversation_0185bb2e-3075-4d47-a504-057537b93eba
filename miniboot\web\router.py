#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 统一控制器注册表模块 - 提供控制器注册、管理和路由映射功能

统一控制器注册表模块

提供控制器注册、管理和路由映射功能.

主要功能:
- ControllerRegistry - 统一控制器注册表
- 控制器信息管理
- 路由信息提取和注册
- 参数绑定处理
- 响应处理
"""

import time
from dataclasses import dataclass, field
from typing import Any, Callable, Optional

from fastapi import FastAPI
from loguru import logger

from ..annotations.web import controller_path as get_controller_path
from ..annotations.web import has_route as has_route_mapping
from ..annotations.web import is_controller, is_rest_controller
from ..annotations.web import path_variables as get_path_variables
from ..annotations.web import request_body as get_request_body
from ..annotations.web import request_headers as get_request_headers
from ..annotations.web import request_params as get_request_params
from ..annotations.web import response_body as get_response_body
from ..annotations.web import response_status as get_response_status
from ..annotations.web import route_info as get_route_info


@dataclass
class ControllerInfo:
    """控制器信息数据类

    用于存储和管理单个控制器的完整信息.
    这是控制器级别的抽象, 代表一个完整的控制器类及其相关元数据.

    设计原则:
    - 单一职责: 专注于控制器级别的信息管理
    - 性能友好: 轻量级数据结构, 高效访问
    - 类型安全: 完整的类型注解确保编译时检查

    生命周期:
    1. 创建: 控制器注册时由 ControllerRegistry 创建
    2. 填充: 扫描控制器方法, 提取路由信息, 填充 methods 列表
    3. 使用: 在路由注册、监控、调试等场景中使用
    4. 销毁: 控制器注销时清理相关信息
    """

    # === 基础属性 (必需, 所有模式下都会使用) ===

    name: str
    """控制器名称

    通常为控制器类名, 用作唯一标识符.
    在控制器注册表中作为键值使用, 必须保证唯一性.

    示例: "UserController", "OrderController"
    """

    instance: Any
    """控制器实例对象

    控制器类的实例化对象, 用于实际的请求处理.
    包含了控制器的状态和行为, 是路由处理的执行主体.

    类型: 控制器类的实例, 通常是被 @Controller 或 @RestController 装饰的类
    """

    controller_class: type
    """控制器类型

    控制器的类对象(非实例), 用于类型检查、反射操作和元数据提取.
    通过此字段可以访问类级别的注解、方法签名等信息.

    类型: type, 指向控制器的类定义
    """

    path: str
    """控制器基础路径

    从 @Controller 或 @RestController 注解中提取的路径前缀.
    所有该控制器下的路由都会以此路径作为前缀.

    示例: "/api/users", "/admin", "" (空字符串表示根路径)
    格式要求: 以 "/" 开头, 不以 "/" 结尾 (除非是根路径 "/")
    """

    is_rest: bool
    """是否为 REST 控制器

    标识控制器是否使用 @RestController 注解.
    REST 控制器会自动处理 JSON 序列化/反序列化, 设置合适的 Content-Type.

    True:  使用 @RestController, 自动 JSON 处理
    False: 使用 @Controller, 需要手动处理响应格式
    """

    methods: list[str] = field(default_factory=list)
    """控制器包含的方法名列表

    存储该控制器中所有被路由注解标记的方法名称.
    用于快速查询控制器的功能范围, 支持调试和监控.

    示例: ["list_users", "create_user", "get_user", "update_user"]
    注意: 只包含有路由映射的方法, 不包含普通的辅助方法
    """


@dataclass
class RouteInfo:
    """路由信息数据类

    用于存储和管理单个路由的详细信息.
    这是方法级别的抽象, 代表控制器中一个具体的请求处理方法.

    设计原则:
    - 精确映射: 一对一映射到控制器中的具体方法
    - 完整信息: 包含路由处理所需的所有关键信息
    - 类型安全: 严格的类型定义确保运行时安全

    与 ControllerInfo 的关系:
    - 从属关系: 每个 RouteInfo 都属于一个特定的 ControllerInfo
    - 组合关系: 多个 RouteInfo 组成一个完整的 ControllerInfo
    - 生命周期: 随控制器的注册/注销而创建/销毁

    使用场景:
    - 路由注册: 将路由信息注册到 FastAPI 应用
    - 请求分发: 根据路由信息分发 HTTP 请求
    - 性能监控: 跟踪单个路由的性能指标
    - 缓存管理: 基于路由特征决定缓存策略
    - 批处理优化: 识别可以批处理的路由
    """

    # === 基础路由属性 (必需, 所有模式下都会使用) ===

    controller_name: str
    """所属控制器名称

    标识该路由属于哪个控制器, 与 ControllerInfo.name 对应.
    用于建立路由与控制器之间的关联关系.

    示例: "UserController", "OrderController"
    约束: 必须与已注册的控制器名称匹配
    """

    method_name: str
    """方法名称

    控制器中处理该路由的方法名称.
    用于反射调用、调试信息和性能监控.

    示例: "list_users", "create_user", "get_user_by_id"
    约束: 必须是控制器类中存在的公共方法
    """

    path: str
    """路由路径

    从 @RequestMapping 等注解中提取的相对路径.
    不包含控制器基础路径, 仅为方法级别的路径.

    示例: "/", "/{id}", "/search", "/batch"
    格式: 可以包含路径参数 {param}, 支持 FastAPI 路径语法
    """

    http_method: str
    """HTTP 方法

    该路由支持的 HTTP 方法, 大写格式.
    决定了客户端应该使用哪种 HTTP 动词来访问该路由.

    支持的值: "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"
    示例: "GET" (查询), "POST" (创建), "PUT" (更新), "DELETE" (删除)
    """

    handler: Callable
    """处理函数

    实际处理请求的可调用对象, 通常是控制器实例的方法.
    FastAPI 会调用此函数来处理匹配的 HTTP 请求.

    类型: Callable, 通常是绑定方法 (bound method)
    签名: 应该符合 FastAPI 的处理函数签名要求
    """

    full_path: str
    """完整路径

    控制器基础路径 + 方法路径组成的完整 URL 路径.
    这是客户端实际访问的 URL 路径.

    计算公式: controller.path + route.path
    示例: "/api/users/", "/api/users/{id}", "/admin/dashboard"
    格式: 标准化的 URL 路径, 以 "/" 开头
    """

    parameters: dict[str, Any] = field(default_factory=dict)
    """参数信息

    从方法签名中提取的参数信息, 包括类型、默认值、验证规则等.
    用于请求参数验证、文档生成和调试信息.

    数据结构示例:
    {
        "user_id": {
            "type": "int",           # 参数类型
            "required": True,        # 是否必需
            "location": "path",      # 参数位置: path/query/body/header
            "description": "用户ID"   # 参数描述
        },
        "page": {
            "type": "int",
            "required": False,
            "default": 1,
            "location": "query",
            "description": "页码"
        }
    }

    用途:
    - 参数验证: 自动验证请求参数的类型和格式
    - 文档生成: 生成 OpenAPI/Swagger 文档
    - 调试信息: 提供参数相关的调试信息
    """

    response_info: Optional[dict[str, Any]] = None
    """响应信息

    描述该路由的响应格式、状态码、响应模型等信息.
    用于文档生成、响应验证和客户端代码生成.

    数据结构示例:
    {
        "status_code": 200,                    # 默认状态码
        "response_model": "UserResponse",      # 响应模型类名
        "content_type": "application/json",    # 响应内容类型
        "description": "用户信息",             # 响应描述
        "examples": {                          # 响应示例
            "success": {
                "user_id": 123,
                "username": "john_doe",
                "email": "<EMAIL>"
            }
        }
    }

    用途:
    - API 文档: 生成详细的 API 响应文档
    - 响应验证: 验证响应数据的格式和类型
    - 客户端生成: 为客户端 SDK 提供类型信息
    """


class ControllerRegistry:
    """统一控制器注册表

    Mini-Boot Web 模块的核心组件, 负责控制器的注册、管理和路由映射.

    核心功能:
    1. 控制器注册: 扫描和注册被 @Controller/@RestController 装饰的类
    2. 路由映射: 提取控制器方法的路由信息并注册到 FastAPI
    3. 生命周期管理: 管理控制器的完整生命周期
    4. 监控支持: 提供控制器和路由的监控数据

    设计原则:
    - API 兼容性: 保持完全的向后兼容, 用户无需修改现有代码
    - 性能优先: 确保最佳性能和最低延迟
    - 可观测性: 提供丰富的监控和调试信息
    - 扩展性: 支持自定义处理器和插件机制

    使用场景:
    - 微服务架构: 快速注册和管理大量微服务控制器
    - 企业应用: 复杂业务逻辑的控制器管理
    - API 网关: 动态路由注册和流量管理
    - 开发调试: 提供丰富的调试和监控信息

    生命周期:
    1. 初始化: 创建注册表实例
    2. 注册阶段: 扫描和注册控制器, 提取路由信息
    3. 运行阶段: 处理请求, 收集监控数据
    4. 清理阶段: 注销控制器, 清理资源
    """

    def __init__(self, app: Optional[FastAPI] = None):
        """初始化控制器注册表

        创建一个控制器注册表实例, 专注于核心功能.

        初始化过程:
        1. 设置 FastAPI 应用实例 (可选)
        2. 初始化基础数据结构 (控制器字典、路由列表、缓存)
        3. 记录初始化日志

        Args:
            app (Optional[FastAPI]): FastAPI 应用实例
                - 如果提供, 控制器注册时会自动注册路由到该应用
                - 如果为 None, 可以后续通过 set_app() 方法设置
                - 支持运行时动态设置和更换应用实例
        """
        # FastAPI 应用实例
        # 用于注册路由, 支持运行时设置和更换
        self.app = app

        # === 基础数据结构 ===

        self.controllers: dict[str, ControllerInfo] = {}
        """控制器注册表

        存储所有已注册的控制器信息, 以控制器名称为键.

        数据结构: {controller_name: ControllerInfo}
        示例: {"UserController": ControllerInfo(...), "OrderController": ControllerInfo(...)}

        用途:
        - 控制器查找: 根据名称快速查找控制器信息
        - 重复检查: 防止同一控制器被重复注册
        - 监控统计: 统计已注册的控制器数量和类型
        - 生命周期管理: 管理控制器的注册和注销
        """

        self.routes: list[RouteInfo] = []
        """路由信息列表

        存储所有已注册的路由信息, 按注册顺序排列.

        数据结构: [RouteInfo, RouteInfo, ...]
        特点:
        - 有序列表: 保持路由的注册顺序
        - 完整信息: 包含路由的所有详细信息
        - 跨控制器: 包含所有控制器的路由信息

        用途:
        - 路由查询: 查找特定的路由信息
        - 统计分析: 分析路由的分布和特征
        - 调试信息: 提供路由相关的调试数据
        - 文档生成: 生成 API 文档和路由清单
        """

        self._route_cache: dict[str, RouteInfo] = {}
        """路由缓存

        为了提高路由查找性能而建立的缓存字典.

        数据结构: {route_key: RouteInfo}
        键格式: "controller_name:method_name:path"
        示例: {"UserController:get_user:/users/{id}": RouteInfo(...)}

        用途:
        - 性能优化: 避免重复遍历路由列表
        - 快速查找: O(1) 时间复杂度的路由查找
        - 内存权衡: 用少量内存换取查找性能

        维护策略:
        - 注册时更新: 新路由注册时自动添加到缓存
        - 注销时清理: 路由注销时从缓存中移除
        - 定期清理: 避免缓存过度膨胀
        """

        # 记录初始化完成
        logger.info("ControllerRegistry initialized")

    async def register(self, controller_instance: Any, controller_name: str, app: FastAPI) -> bool:
        """注册控制器

        这是控制器注册的主要入口点, 提供统一的注册接口.

        注册流程:
        1. 参数验证: 验证控制器实例和名称的有效性
        2. 重复检查: 检查控制器是否已经注册
        3. 信息提取: 提取控制器的路由和元数据信息
        4. 路由注册: 将路由注册到 FastAPI 应用
        5. 缓存更新: 更新内部缓存和索引
        6. 日志记录: 记录注册结果和统计信息

        Args:
            controller_instance (Any): 控制器实例
                - 必须是被 @Controller 或 @RestController 装饰的类的实例
                - 实例必须包含有效的路由方法
                - 支持任何符合控制器规范的 Python 对象

            controller_name (str): 控制器名称
                - 用作控制器的唯一标识符
                - 如果为空字符串, 会自动使用类名
                - 必须在注册表中保持唯一性

        Returns:
            bool: 注册是否成功
                - True: 控制器成功注册, 所有路由已添加到应用
                - False: 注册失败, 可能的原因包括:
                  * 控制器实例无效
                  * 控制器名称重复
                  * 路由注册失败
                  * 系统异常

        异常处理:
        - 捕获所有注册过程中的异常
        - 记录详细的错误日志
        - 确保部分失败不影响其他控制器
        - 提供友好的错误信息
        """
        start_time = time.time()
        logger.debug(f"Starting controller registration at {start_time}")

        try:
            # 验证控制器
            controller_class = controller_instance.__class__
            if not is_controller(controller_class):
                logger.error(f"Class {controller_class.__name__} is not a valid controller")
                return False

            # 生成控制器名称
            if not controller_name:
                controller_name = controller_class.__name__

            # 检查是否已注册
            if controller_name in self.controllers:
                logger.warning(f"Controller {controller_name} already registered")
                return False

            # 创建控制器信息
            controller_info = ControllerInfo(
                name=controller_name,
                instance=controller_instance,
                controller_class=controller_class,
                path=get_controller_path(controller_class),
                is_rest=is_rest_controller(controller_class),
            )

            # 提取路由信息
            routes = await self._get_routes(controller_info)
            controller_info.methods = [route.method_name for route in routes]

            # 注册控制器
            self.controllers[controller_name] = controller_info
            self.routes.extend(routes)

            # 注册路由到FastAPI
            await self._apply_routes(routes, app)

            logger.info(f"Registered controller: {controller_name} with {len(routes)} routes")
            return True

        except Exception as e:
            logger.error(f"Failed to register controller {controller_name}: {e}")
            return False

    async def _get_routes(self, controller_info: ControllerInfo) -> list[RouteInfo]:
        """提取路由信息"""
        routes = []
        controller_path = controller_info.path

        for method_name in dir(controller_info.instance):
            if method_name.startswith("_"):
                continue

            method = getattr(controller_info.instance, method_name)
            if not callable(method) or not has_route_mapping(method):
                continue

            try:
                # 获取路由信息
                route_info = get_route_info(method)
                if not route_info:
                    continue

                # 构建完整路径
                method_path = route_info.get("path", "")
                # 正确拼接控制器路径和方法路径
                if controller_path and method_path:
                    # 确保控制器路径不以/结尾，方法路径以/开头
                    controller_base = controller_path.rstrip("/")
                    method_relative = method_path if method_path.startswith("/") else f"/{method_path}"
                    full_path = f"{controller_base}{method_relative}"
                elif controller_path:
                    # 只有控制器路径
                    full_path = controller_path.rstrip("/")
                elif method_path:
                    # 只有方法路径
                    full_path = method_path if method_path.startswith("/") else f"/{method_path}"
                else:
                    # 都没有，使用根路径
                    full_path = "/"

                # 创建路由信息
                route = RouteInfo(
                    controller_name=controller_info.name,
                    method_name=method_name,
                    path=method_path,
                    http_method=route_info.get("method", "GET"),
                    handler=method,
                    full_path=full_path,
                    parameters=await self._get_params(method),
                    response_info=await self._get_response(method),
                )

                routes.append(route)

            except Exception as e:
                logger.error(f"Failed to extract route info for {method_name}: {e}")
                continue

        return routes

    async def _get_params(self, method: Callable) -> dict[str, Any]:
        """提取方法参数信息"""
        try:
            parameters = {}

            # 获取路径变量
            path_vars = get_path_variables(method)
            if path_vars:
                parameters["path_variables"] = path_vars

            # 获取请求参数
            request_params = get_request_params(method)
            if request_params:
                parameters["request_params"] = request_params

            # 获取请求头
            headers = get_request_headers(method)
            if headers:
                parameters["headers"] = headers

            # 获取请求体
            body = get_request_body(method)
            if body:
                parameters["request_body"] = body

            return parameters

        except Exception as e:
            logger.error(f"Failed to extract parameters for method {method.__name__}: {e}")
            return {}

    async def _get_response(self, method: Callable) -> Optional[dict[str, Any]]:
        """提取响应信息"""
        try:
            response_info = {}

            # 获取响应状态
            status = get_response_status(method)
            if status:
                response_info["status"] = status

            # 获取响应体
            body = get_response_body(method)
            if body:
                response_info["body"] = body

            return response_info if response_info else None

        except Exception as e:
            logger.error(f"Failed to extract response info for method {method.__name__}: {e}")
            return None

    async def _apply_routes(self, routes: list[RouteInfo], app: FastAPI) -> None:
        """将路由注册到FastAPI应用"""
        for route in routes:
            try:
                # 根据HTTP方法注册路由
                method = route.http_method.lower()
                if hasattr(app, method):
                    route_func = getattr(app, method)
                    route_func(route.full_path)(route.handler)
                    logger.debug(f"Registered route: {method.upper()} {route.full_path}")
                else:
                    logger.warning(f"Unsupported HTTP method: {route.http_method}")

            except Exception as e:
                logger.error(f"Failed to register route {route.full_path}: {e}")

    async def get_controller(self, controller_name: str) -> Optional[ControllerInfo]:
        """异步获取控制器信息

        Args:
            controller_name: 控制器名称

        Returns:
            控制器信息,如果不存在则返回None
        """
        return self.controllers.get(controller_name)

    async def get_controllers(self) -> dict[str, ControllerInfo]:
        """异步获取所有控制器

        Returns:
            控制器字典
        """
        return self.controllers.copy()

    async def get_routes(self, controller_name: Optional[str] = None) -> list[RouteInfo]:
        """异步获取路由信息

        Args:
            controller_name: 控制器名称,如果为None则返回所有路由

        Returns:
            路由信息列表
        """
        if controller_name:
            return [route for route in self.routes if route.controller_name == controller_name]
        return self.routes.copy()

    async def get_controller_routes(self, controller_name: str) -> list[RouteInfo]:
        """异步获取指定控制器的路由信息

        Args:
            controller_name: 控制器名称

        Returns:
            路由信息列表
        """
        return [route for route in self.routes if route.controller_name == controller_name]

    async def remove(self, controller_name: str) -> bool:
        """异步注销控制器

        Args:
            controller_name: 控制器名称

        Returns:
            是否注销成功
        """
        if controller_name not in self.controllers:
            return False

        # 移除控制器
        del self.controllers[controller_name]

        # 移除相关路由
        self.routes = [route for route in self.routes if route.controller_name != controller_name]

        # 清理路由缓存
        self._route_cache.clear()

        logger.info(f"Removed controller: {controller_name}")
        return True

    async def clear(self) -> None:
        """异步清空所有注册信息"""
        self.controllers.clear()
        self.routes.clear()
        self._route_cache.clear()

        logger.info("Cleared all controller registrations")

    def cleanup(self) -> None:
        """清理控制器注册表资源"""
        self.controllers.clear()
        self.routes.clear()
        self._route_cache.clear()
        logger.debug("ControllerRegistry cleanup completed")
