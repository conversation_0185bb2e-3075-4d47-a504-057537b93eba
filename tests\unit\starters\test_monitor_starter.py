#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Monitor Starter测试
"""

import pytest
import time
from unittest.mock import Mock

from miniboot.starters.monitor.properties import MonitorProperties
from miniboot.starters.monitor.service import MonitorService, HealthCheckResult
from miniboot.starters.monitor.configuration import MonitorAutoConfiguration
from miniboot.context import ApplicationContext


class TestMonitorProperties:
    """Monitor配置属性测试"""

    def test_default_values(self):
        """测试默认值"""
        props = MonitorProperties()

        assert props.enabled is True
        assert props.auto_start is True
        assert props.collect_interval == 60
        assert props.metrics_enabled is True
        assert props.health_enabled is True
        assert props.events_enabled is True
        assert props.storage_type == "memory"
        assert props.performance_enabled is True
        assert props.performance_sample_rate == 1.0

    def test_validation_success(self):
        """测试验证成功"""
        props = MonitorProperties()
        props.validate()  # 应该不抛出异常

    def test_validation_collect_interval(self):
        """测试收集间隔验证"""
        props = MonitorProperties()
        props.collect_interval = 0

        with pytest.raises(ValueError, match="collect_interval must be positive"):
            props.validate()

    def test_validation_storage_type(self):
        """测试存储类型验证"""
        props = MonitorProperties()
        props.storage_type = "invalid"

        with pytest.raises(ValueError, match="storage_type must be one of"):
            props.validate()

    def test_validation_storage_path(self):
        """测试存储路径验证"""
        props = MonitorProperties()
        props.storage_type = "file"
        props.storage_path = None

        with pytest.raises(ValueError, match="storage_path is required"):
            props.validate()

    def test_validation_performance_sample_rate(self):
        """测试性能采样率验证"""
        props = MonitorProperties()
        props.performance_sample_rate = 1.5

        with pytest.raises(ValueError, match="performance_sample_rate must be between 0.0 and 1.0"):
            props.validate()

    def test_get_metrics_config(self):
        """测试获取指标配置"""
        props = MonitorProperties()
        config = props.get_metrics_config()

        assert config["enabled"] == props.metrics_enabled
        assert config["export_interval"] == props.metrics_export_interval
        assert config["include_details"] == props.metrics_include_details

    def test_get_alert_threshold(self):
        """测试获取告警阈值"""
        props = MonitorProperties()
        props.alert_thresholds = {"cpu": 80.0, "memory": 90.0}

        assert props.get_alert_threshold("cpu") == 80.0
        assert props.get_alert_threshold("memory") == 90.0
        assert props.get_alert_threshold("disk") is None

    def test_is_performance_monitoring_enabled(self):
        """测试性能监控启用检查"""
        props = MonitorProperties()

        # 默认启用
        assert props.is_performance_monitoring_enabled() is True

        # 禁用性能监控
        props.performance_enabled = False
        assert props.is_performance_monitoring_enabled() is False

        # 采样率为0
        props.performance_enabled = True
        props.performance_sample_rate = 0.0
        assert props.is_performance_monitoring_enabled() is False


class TestMonitorService:
    """Monitor服务测试"""

    def setup_method(self):
        """设置测试"""
        self.properties = MonitorProperties()
        self.properties.auto_start = False  # 测试时不自动启动
        self.service = MonitorService(self.properties)

    def test_service_initialization(self):
        """测试服务初始化"""
        assert self.service.properties == self.properties
        assert self.service._running is False
        assert self.service.metrics_collector is not None
        assert self.service.health_checker is not None
        assert self.service.event_publisher is not None

    def test_start_stop_service(self):
        """测试启动和停止服务"""
        # 启动服务
        self.service.start()
        assert self.service._running is True

        # 停止服务
        self.service.stop()
        assert self.service._running is False

    def test_record_custom_metric(self):
        """测试记录自定义指标"""
        self.service.record_custom_metric("test.metric", 42.0, {"tag": "value"})

        metrics = self.service.get_metrics()
        assert "test.metric" in metrics
        assert metrics["test.metric"]["value"] == 42.0
        assert metrics["test.metric"]["tags"]["tag"] == "value"

    def test_register_health_check(self):
        """测试注册健康检查"""

        def test_check():
            return HealthCheckResult("test", "UP", {"detail": "ok"})

        self.service.register_health_check("test_check", test_check)

        health = self.service.get_health()
        assert "test_check" in health["checks"]
        assert health["checks"]["test_check"]["status"] == "UP"

    def test_get_stats(self):
        """测试获取统计信息"""
        stats = self.service.get_stats()

        assert "running" in stats
        assert "metrics_count" in stats
        assert "health_checks_count" in stats
        assert "event_listeners_count" in stats
        assert "collect_interval" in stats
        assert "auto_start" in stats

    def test_event_publisher(self):
        """测试事件发布"""
        events = []

        def event_listener(event):
            events.append(event)

        self.service.event_publisher.add_listener(event_listener)
        self.service.event_publisher.publish_event("test.event", {"data": "value"})

        assert len(events) == 1
        assert events[0]["type"] == "test.event"
        assert events[0]["data"]["data"] == "value"


class TestMonitorAutoConfiguration:
    """Monitor自动配置测试"""

    def setup_method(self):
        """设置测试"""
        self.config = MonitorAutoConfiguration()
        self.context = Mock(spec=ApplicationContext)

    def test_metadata(self):
        """测试配置元数据"""
        metadata = self.config.get_metadata()

        assert metadata.name == "monitor-auto-configuration"
        assert metadata.description == "监控功能自动配置"
        assert metadata.priority == 100  # 高优先级

    def test_starter_info(self):
        """测试Starter信息"""
        assert self.config.get_starter_name() == "miniboot-starter-monitor"
        assert self.config.get_starter_version() == "1.0.0"
        assert "监控功能Starter" in self.config.get_starter_description()

    def test_configuration_properties_classes(self):
        """测试配置属性类"""
        classes = self.config.get_configuration_properties_classes()
        assert MonitorProperties in classes

    def test_monitor_service_bean(self):
        """测试Monitor服务Bean创建"""
        monitor_properties = MonitorProperties()

        service = self.config.monitor_service(monitor_properties)

        assert isinstance(service, MonitorService)
        assert service.properties == monitor_properties

    def test_default_health_checks_bean(self):
        """测试默认健康检查Bean创建"""
        monitor_properties = MonitorProperties()
        monitor_service = MonitorService(monitor_properties)

        health_checks = self.config.default_health_checks(monitor_service)

        assert health_checks is not None
        assert health_checks.monitor_service == monitor_service

    def test_initialize_starter(self):
        """测试Starter初始化"""
        # 模拟上下文
        monitor_properties = MonitorProperties()
        monitor_properties.auto_start = False  # 测试时不自动启动
        monitor_service = MonitorService(monitor_properties)

        self.context.get_bean.side_effect = lambda name: {"monitor_properties": monitor_properties, "monitor_service": monitor_service}.get(name)

        # 应该不抛出异常
        self.config._initialize_starter(self.context)

        # 验证get_bean被调用
        assert self.context.get_bean.call_count >= 1

    def test_check_application_context_health(self):
        """测试应用上下文健康检查"""
        # 模拟运行中的上下文
        self.context.is_running.return_value = True
        self.context.get_bean_names.return_value = ["bean1", "bean2"]

        result = self.config._check_application_context_health(self.context)

        assert result.name == "application_context"
        assert result.status == "UP"
        assert result.details["running"] is True
        assert result.details["bean_count"] == 2

    def test_check_application_context_health_down(self):
        """测试应用上下文健康检查 - 停止状态"""
        # 模拟停止的上下文
        self.context.is_running.return_value = False

        result = self.config._check_application_context_health(self.context)

        assert result.name == "application_context"
        assert result.status == "DOWN"
        assert result.details["running"] is False


class TestHealthCheckResult:
    """健康检查结果测试"""

    def test_health_check_result_creation(self):
        """测试健康检查结果创建"""
        result = HealthCheckResult(name="test_check", status="UP", details={"key": "value"})

        assert result.name == "test_check"
        assert result.status == "UP"
        assert result.details["key"] == "value"
        assert result.timestamp > 0

    def test_health_check_result_default_timestamp(self):
        """测试健康检查结果默认时间戳"""
        before = time.time()
        result = HealthCheckResult("test", "UP")
        after = time.time()

        assert before <= result.timestamp <= after
