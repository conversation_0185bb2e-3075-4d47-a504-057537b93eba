#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Actuator 端点基础接口和实现

提供 Actuator 模块的端点基础接口、操作类型定义和端点操作模型.
统一的异步优先端点实现,支持高性能异步操作和智能缓存.
"""

import asyncio
import threading
import time
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Awaitable, Callable, Dict, List, Optional, Union

from loguru import logger


@dataclass
class EndpointInfo:
    """端点信息"""
    name: str
    path: str
    methods: List[str]
    description: str


class EndpointProvider(ABC):
    """端点提供者接口

    所有端点都应该实现这个接口，以便监控系统能够发现和管理它们。
    """

    @abstractmethod
    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息

        Returns:
            EndpointInfo: 端点的基本信息
        """
        pass

    @abstractmethod
    async def handle_request(self, **kwargs) -> Any:
        """处理请求

        Args:
            **kwargs: 请求参数

        Returns:
            Any: 处理结果
        """
        pass

    @abstractmethod
    def is_enabled(self) -> bool:
        """检查端点是否启用

        Returns:
            bool: 是否启用
        """
        pass


class OperationType(Enum):
    """端点操作类型"""

    READ = "READ"
    WRITE = "WRITE"
    DELETE = "DELETE"


@dataclass
class EndpointOperation:
    """端点操作定义"""

    operation_type: OperationType
    method: str  # HTTP方法
    path: str = ""  # 子路径
    handler: Optional[Callable] = None  # 处理函数


class CacheStrategy(Enum):
    """缓存策略枚举"""

    LRU = "LRU"  # 最近最少使用
    LFU = "LFU"  # 最少使用频率
    FIFO = "FIFO"  # 先进先出
    TTL = "TTL"  # 基于时间的过期


@dataclass
class CacheEntry:
    """缓存条目"""

    key: str
    value: Any
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)
    ttl: Optional[float] = None  # 生存时间(秒)

    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl


@dataclass
class PerformanceStats:
    """性能统计"""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    concurrent_requests: int = 0
    total_response_time: float = 0.0
    min_response_time: float = float("inf")
    max_response_time: float = 0.0
    last_request_time: Optional[float] = None

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100

    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests

    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total_cache_requests = self.cache_hits + self.cache_misses
        if total_cache_requests == 0:
            return 0.0
        return (self.cache_hits / total_cache_requests) * 100


class SmartCache:
    """智能缓存实现"""

    def __init__(self, max_size: int = 1000, strategy: CacheStrategy = CacheStrategy.LRU, default_ttl: Optional[float] = None):
        self.max_size = max_size
        self.strategy = strategy
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None

            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                return None

            # 更新访问信息
            entry.access_count += 1
            entry.last_access = time.time()

            return entry.value

    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """存储缓存值"""
        with self._lock:
            # 如果缓存已满,执行驱逐策略
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict()

            # 使用指定TTL或默认TTL
            entry_ttl = ttl if ttl is not None else self.default_ttl

            # 创建缓存条目
            entry = CacheEntry(key=key, value=value, timestamp=time.time(), ttl=entry_ttl)

            self._cache[key] = entry

    def _evict(self) -> None:
        """执行驱逐策略"""
        if not self._cache:
            return

        if self.strategy == CacheStrategy.LRU:
            # 驱逐最近最少使用的
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_access)
        elif self.strategy == CacheStrategy.LFU:
            # 驱逐使用频率最低的
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].access_count)
        elif self.strategy == CacheStrategy.FIFO:
            # 驱逐最早添加的
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].timestamp)
        else:  # TTL
            # 驱逐最早过期的
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k].timestamp)

        del self._cache[oldest_key]

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()

    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)

    def cleanup_expired(self) -> int:
        """清理过期条目,返回清理数量"""
        with self._lock:
            expired_keys = [key for key, entry in self._cache.items() if entry.is_expired()]

            for key in expired_keys:
                del self._cache[key]

            return len(expired_keys)


class Endpoint(ABC):
    """端点基础接口"""

    def __init__(self, endpoint_id: str, enabled: bool = True, sensitive: bool = False):
        """
        初始化端点

        Args:
            endpoint_id: 端点ID
            enabled: 是否启用
            sensitive: 是否敏感(需要认证)
        """
        self.id = endpoint_id
        self.enabled = enabled
        self.sensitive = sensitive

    @abstractmethod
    def operations(self) -> list[EndpointOperation]:
        """返回端点支持的操作列表"""
        pass

    @abstractmethod
    def invoke(self, operation_type: OperationType, **kwargs) -> Any:
        """执行端点操作"""
        pass

    def __str__(self) -> str:
        return f"Endpoint(id={self.id}, enabled={self.enabled}, sensitive={self.sensitive})"

    def __repr__(self) -> str:
        return self.__str__()


class BaseEndpoint(Endpoint):
    """端点基础实现类"""

    def __init__(self, endpoint_id: str, enabled: bool = True, sensitive: bool = False):
        super().__init__(endpoint_id, enabled, sensitive)
        self._operations_cache: Optional[list[EndpointOperation]] = None

    def operations(self) -> list[EndpointOperation]:
        """返回端点支持的操作列表(带缓存)"""
        if self._operations_cache is None:
            self._operations_cache = self._create_operations()
        return self._operations_cache

    @abstractmethod
    def _create_operations(self) -> list[EndpointOperation]:
        """创建操作列表(子类实现)"""
        pass

    def clear_operations_cache(self) -> None:
        """清空操作缓存"""
        self._operations_cache = None
