{"version": "2.0.0", "tasks": [{"label": "Run All Tests", "type": "shell", "command": "uv", "args": ["run", "python", "tests/test_runner.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Unit Tests", "type": "shell", "command": "uv", "args": ["run", "python", "tests/test_runner.py", "--unit"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Integration Tests", "type": "shell", "command": "uv", "args": ["run", "python", "tests/test_runner.py", "--integration"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Performance Tests", "type": "shell", "command": "uv", "args": ["run", "python", "tests/test_runner.py", "--benchmark"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Tests with Coverage", "type": "shell", "command": "uv", "args": ["run", "python", "tests/test_runner.py", "--coverage"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Format Code", "type": "shell", "command": "uv", "args": ["run", "ruff", "format", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Check Code", "type": "shell", "command": "uv", "args": ["run", "ruff", "check", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Fix Code Issues", "type": "shell", "command": "uv", "args": ["run", "ruff", "check", "--fix", "."], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Type Check", "type": "shell", "command": "uv", "args": ["run", "mypy", "miniboot"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "uv", "args": ["sync"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}