#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Schedule相关异常类

任务调度系统相关的异常。
"""

from ..base import ApplicationError, BusinessError, SystemError, ValidationError


# 任务执行相关异常 (BusinessError)
class TaskExecutionError(BusinessError):
    """任务执行错误 - 任务执行过程中的错误"""
    max_attempts = 3
    base_delay = 1.0


class TaskTimeoutError(BusinessError):
    """任务超时错误 - 任务执行超时"""
    max_attempts = 2
    base_delay = 2.0


class TaskInterruptedError(BusinessError):
    """任务中断错误 - 任务执行被中断"""
    max_attempts = 2
    base_delay = 1.0


class TaskFailureError(BusinessError):
    """任务失败错误 - 任务执行失败"""
    max_attempts = 3
    base_delay = 1.0


# 任务调度相关异常 (ApplicationError)
class TaskSchedulingError(ApplicationError):
    """任务调度错误 - 任务调度过程中的错误"""
    max_attempts = 2
    base_delay = 2.0


class SchedulerNotStartedError(ApplicationError):
    """调度器未启动错误 - 调度器尚未启动"""
    retryable = False


class SchedulerAlreadyStartedError(ApplicationError):
    """调度器已启动错误 - 调度器已经启动"""
    retryable = False


class SchedulerShutdownError(ApplicationError):
    """调度器关闭错误 - 调度器关闭过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


class SchedulerConfigurationError(ApplicationError):
    """调度器配置错误 - 调度器配置相关的错误"""
    retryable = False


# 任务定义相关异常 (ValidationError)
class TaskDefinitionError(ValidationError):
    """任务定义错误 - 任务定义相关的错误"""
    # 继承ValidationError的属性：retryable = False


class InvalidTaskDefinitionError(ValidationError):
    """无效任务定义错误 - 任务定义无效"""
    # 继承ValidationError的属性：retryable = False


class TaskValidationError(ValidationError):
    """任务验证错误 - 任务验证失败"""
    # 继承ValidationError的属性：retryable = False


# 任务状态相关异常 (BusinessError)
class TaskStateError(BusinessError):
    """任务状态错误 - 任务状态相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class TaskNotFoundError(BusinessError):
    """任务未找到错误 - 找不到指定的任务"""
    retryable = False


class TaskAlreadyExistsError(BusinessError):
    """任务已存在错误 - 任务已经存在"""
    retryable = False


class TaskNotScheduledError(BusinessError):
    """任务未调度错误 - 任务尚未被调度"""
    retryable = False


class TaskAlreadyScheduledError(BusinessError):
    """任务已调度错误 - 任务已经被调度"""
    retryable = False


# 触发器相关异常 (ValidationError)
class TriggerError(ValidationError):
    """触发器错误 - 触发器相关的错误"""
    # 继承ValidationError的属性：retryable = False


class InvalidTriggerError(ValidationError):
    """无效触发器错误 - 触发器配置无效"""
    # 继承ValidationError的属性：retryable = False


class TriggerMisfireError(BusinessError):
    """触发器错过执行错误 - 触发器错过了执行时间"""
    max_attempts = 2
    base_delay = 1.0


class CronExpressionError(ValidationError):
    """Cron表达式错误 - Cron表达式格式错误"""
    # 继承ValidationError的属性：retryable = False


# 任务队列相关异常 (BusinessError)
class TaskQueueError(BusinessError):
    """任务队列错误 - 任务队列相关的错误"""
    max_attempts = 3
    base_delay = 1.0


class TaskQueueFullError(BusinessError):
    """任务队列满错误 - 任务队列已满"""
    max_attempts = 5
    base_delay = 0.5
    strategy = "linear"


class TaskQueueEmptyError(BusinessError):
    """任务队列空错误 - 任务队列为空"""
    retryable = False


# 线程池相关异常 (ApplicationError)
class ThreadPoolError(ApplicationError):
    """线程池错误 - 线程池相关的错误"""
    max_attempts = 2
    base_delay = 2.0


class ThreadPoolExhaustionError(ApplicationError):
    """线程池耗尽错误 - 线程池资源耗尽"""
    max_attempts = 3
    base_delay = 1.0


class ThreadPoolShutdownError(ApplicationError):
    """线程池关闭错误 - 线程池关闭过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


# 任务持久化相关异常 (ApplicationError)
class TaskPersistenceError(ApplicationError):
    """任务持久化错误 - 任务持久化过程中的错误"""
    max_attempts = 3
    base_delay = 1.0


class TaskStoreError(ApplicationError):
    """任务存储错误 - 任务存储相关的错误"""
    max_attempts = 3
    base_delay = 1.0


class TaskRetrievalError(ApplicationError):
    """任务检索错误 - 任务检索过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


# 任务监控相关异常 (BusinessError)
class TaskMonitoringError(BusinessError):
    """任务监控错误 - 任务监控相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class TaskMetricsError(BusinessError):
    """任务指标错误 - 任务指标收集相关的错误"""
    max_attempts = 2
    base_delay = 0.5


class TaskHistoryError(BusinessError):
    """任务历史错误 - 任务历史记录相关的错误"""
    max_attempts = 2
    base_delay = 1.0


# 任务依赖相关异常 (BusinessError)
class TaskDependencyError(BusinessError):
    """任务依赖错误 - 任务依赖相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class TaskDependencyNotMetError(BusinessError):
    """任务依赖未满足错误 - 任务依赖条件未满足"""
    max_attempts = 3
    base_delay = 2.0


class CircularTaskDependencyError(BusinessError):
    """循环任务依赖错误 - 检测到任务之间的循环依赖"""
    retryable = False


# 任务锁相关异常 (BusinessError)
class TaskLockError(BusinessError):
    """任务锁错误 - 任务锁相关的错误"""
    max_attempts = 3
    base_delay = 0.5


class TaskLockAcquisitionError(BusinessError):
    """任务锁获取错误 - 任务锁获取失败"""
    max_attempts = 5
    base_delay = 0.2
    strategy = "linear"


class TaskLockTimeoutError(BusinessError):
    """任务锁超时错误 - 任务锁获取超时"""
    max_attempts = 3
    base_delay = 1.0


# 任务安全相关异常 (SystemError)
class TaskSecurityError(SystemError):
    """任务安全错误 - 任务安全相关的错误"""
    # 继承SystemError的属性：retryable = False


class TaskPermissionError(SystemError):
    """任务权限错误 - 任务权限相关的错误"""
    # 继承SystemError的属性：retryable = False


# 任务资源相关异常 (ApplicationError)
class TaskResourceError(ApplicationError):
    """任务资源错误 - 任务资源相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class TaskResourceExhaustionError(ApplicationError):
    """任务资源耗尽错误 - 任务资源耗尽"""
    max_attempts = 3
    base_delay = 2.0
