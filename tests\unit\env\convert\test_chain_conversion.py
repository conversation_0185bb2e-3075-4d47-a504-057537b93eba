#!/usr/bin/env python
"""
* @author: cz
* @description: 链式转换功能测试
"""

import unittest

from miniboot.env.convert import ConversionError, Converter, DefaultConversionService


class CustomStringToIntConverter(Converter[int]):
    """自定义字符串到整数转换器"""

    def can_convert(self, source_type, target_type):
        return source_type is str and target_type is int

    def convert(self, source, _target_type):
        return int(source)


class CustomIntToBoolConverter(Converter[bool]):
    """自定义整数到布尔值转换器"""

    def can_convert(self, source_type, target_type):
        return source_type is int and target_type is bool

    def convert(self, source, _target_type):
        return source != 0


class ChainConversionTestCase(unittest.TestCase):
    """链式转换功能测试"""

    def setUp(self):
        self.service = DefaultConversionService()

    def test_direct_conversion_priority(self):
        """测试直接转换优先于链式转换"""
        # 直接转换应该优先
        result = self.service.convert("123", int)
        self.assertEqual(123, result)
        self.assertIsInstance(result, int)

    def test_simple_chain_conversion(self):
        """测试简单链式转换: str -> int -> float"""
        # 这应该通过链式转换: str -> int -> float
        result = self.service.convert("123", float)
        self.assertEqual(123.0, result)
        self.assertIsInstance(result, float)

    def test_complex_chain_conversion(self):
        """测试复杂链式转换: str -> int -> float -> str"""
        # 添加自定义转换器来创建更复杂的链
        self.service.add_converter(CustomStringToIntConverter())

        # 这应该通过链式转换工作
        result = self.service.convert("456", float)
        self.assertEqual(456.0, result)

    def test_can_convert_chain(self):
        """测试链式转换能力检查"""
        # 检查是否可以进行链式转换
        self.assertTrue(self.service.can_convert_chain(str, float))
        self.assertTrue(self.service.can_convert_chain(str, int))

        # 测试基本的链式转换能力
        self.assertTrue(self.service.can_convert_chain(str, float, max_depth=3))

    def test_convert_chain_explicit(self):
        """测试显式链式转换调用"""
        # 显式调用链式转换
        result = self.service.convert_chain("789", float)
        self.assertEqual(789.0, result)
        self.assertIsInstance(result, float)

    def test_chain_conversion_with_custom_converters(self):
        """测试使用自定义转换器的链式转换"""
        # 添加自定义转换器
        self.service.add_converter(CustomStringToIntConverter())
        self.service.add_converter(CustomIntToBoolConverter())

        # 测试 str -> int -> bool 链式转换
        result = self.service.convert("1", bool)
        self.assertTrue(result)

        result = self.service.convert("0", bool)
        self.assertFalse(result)

    def test_max_depth_limit(self):
        """测试最大深度限制"""
        # 测试深度限制的基本功能
        # 使用默认服务,它有 str->int 和 int->float 转换器

        # 深度1应该只能做直接转换
        result = self.service.can_convert_chain(str, int, max_depth=1)
        self.assertTrue(result)  # str -> int 是直接转换

        # 测试多步转换的深度限制
        result = self.service.can_convert_chain(str, float, max_depth=1)
        # 这取决于是否有直接的 str->float 转换器
        # 如果没有,深度1应该返回False

        result = self.service.can_convert_chain(str, float, max_depth=2)
        self.assertTrue(result)  # 深度2可以完成 str -> int -> float

    def test_no_conversion_path_found(self):
        """测试找不到转换路径的情况"""
        # 尝试不可能的转换(使用一个确实无法转换的类型)
        with self.assertRaises(ConversionError):
            self.service.convert_chain({"key": "value"}, int)

    def test_conversion_path_finding(self):
        """测试转换路径查找"""
        # 测试内部路径查找方法
        path = self.service._find_conversion_path(str, float, max_depth=3)
        self.assertIsNotNone(path)
        self.assertEqual(float, path[-1])  # 最后应该是目标类型
        # 路径可能是 [int, float] 或者直接 [float],取决于转换器的注册顺序

    def test_circular_conversion_prevention(self):
        """测试防止循环转换"""

        # 创建可能导致循环的转换器
        class CircularConverter1(Converter[str]):
            def can_convert(self, source_type, target_type):
                return source_type is int and target_type is str

            def convert(self, source, _target_type):
                return str(source)

        class CircularConverter2(Converter[int]):
            def can_convert(self, source_type, target_type):
                return source_type is str and target_type is int

            def convert(self, source, _target_type):
                return int(source)

        service = DefaultConversionService()
        service.add_converter(CircularConverter1())
        service.add_converter(CircularConverter2())

        # 应该能够找到路径而不会陷入无限循环
        path = service._find_conversion_path(str, int, max_depth=3)
        self.assertIsNotNone(path)
        self.assertEqual([int], path)

    def test_get_converter_target_types(self):
        """测试获取转换器目标类型"""
        from miniboot.env.convert.strings import StringToIntConverter

        converter = StringToIntConverter()
        target_types = self.service._get_converter_target_types(converter, str)

        self.assertIn(int, target_types)
        self.assertNotIn(float, target_types)  # 这个转换器不支持 str -> float

    def test_chain_conversion_with_none_value(self):
        """测试 None 值的链式转换"""
        result = self.service.convert_chain(None, str)
        self.assertIsNone(result)

    def test_chain_conversion_same_type(self):
        """测试相同类型的链式转换"""
        result = self.service.convert_chain("test", str)
        self.assertEqual("test", result)

    def test_integration_with_regular_convert(self):
        """测试与常规转换方法的集成"""
        # 常规 convert 方法应该自动尝试链式转换
        result = self.service.convert("123", float)
        self.assertEqual(123.0, result)

        # 验证这确实是通过链式转换完成的
        # (因为没有直接的 str -> float 转换器)
        self.assertTrue(self.service.can_convert(str, float))

    def test_performance_with_deep_chains(self):
        """测试深度链式转换的性能"""

        # 创建一个较深的转换链
        class StepConverter(Converter):
            def __init__(self, from_type, to_type):
                self.from_type = from_type
                self.to_type = to_type

            def can_convert(self, source_type, target_type):
                return source_type is self.from_type and target_type is self.to_type

            def convert(self, source, _target_type):
                return self.to_type(source)

        service = DefaultConversionService()

        # 创建转换链: str -> int -> float -> str
        service.add_converter(StepConverter(str, int))
        service.add_converter(StepConverter(int, float))
        service.add_converter(StepConverter(float, str))

        # 测试转换
        result = service.convert("123", str)
        # 结果可能是 123 或 123.0,取决于转换路径
        self.assertIn(result, ["123", "123.0"])


if __name__ == "__main__":
    unittest.main()
