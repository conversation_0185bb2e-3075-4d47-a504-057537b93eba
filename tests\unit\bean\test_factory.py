#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean工厂组件单元测试 - 基于实际API
"""

import contextlib
import unittest
from unittest.mock import Mock, patch

from miniboot.bean.factory import AsyncContextDetector
from miniboot.bean.definition import BeanDefinition, BeanScope, BeanStatus
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.errors import BeanCircularDependencyError


# ==================== 测试用Bean类 ====================

class TestService:
    """测试用的服务类"""
    def __init__(self, name: str = "test"):
        self.name = name
        self.initialized = False
        self.destroyed = False

    def init(self) -> None:
        """初始化方法"""
        self.initialized = True

    def destroy(self) -> None:
        """销毁方法"""
        self.destroyed = True


class DatabaseService:
    """数据库服务 - I/O密集型"""
    def __init__(self):
        self.connected = False
        self.connection_count = 0

    def connect(self) -> None:
        self.connected = True
        self.connection_count += 1

    def disconnect(self) -> None:
        self.connected = False

    def destroy(self) -> None:
        self.disconnect()


class DependentService:
    """有依赖的服务"""
    def __init__(self):
        self.database_service = None
        self.test_service = None
        self.initialized = False

    def set_database_service(self, service: DatabaseService) -> None:
        self.database_service = service

    def set_test_service(self, service: TestService) -> None:
        self.test_service = service

    def init(self) -> None:
        self.initialized = True

    def destroy(self) -> None:
        self.initialized = False


class AsyncContextDetectorTestCase(unittest.TestCase):
    """AsyncContextDetector测试"""

    def test_is_async_context_active_false(self):
        """测试异步上下文检测 - 非异步环境"""
        result = AsyncContextDetector.is_async_context_active()
        self.assertFalse(result)

    def test_should_use_async_processing(self):
        """测试是否应该使用异步处理"""
        result = AsyncContextDetector.should_use_async_processing("testBean")
        self.assertIsInstance(result, bool)

    @patch('asyncio.get_running_loop')
    def test_is_async_context_active_true(self, mock_get_loop):
        """测试异步上下文检测 - 异步环境"""
        mock_loop = Mock()
        mock_get_loop.return_value = mock_loop

        result = AsyncContextDetector.is_async_context_active()
        self.assertTrue(result)

    @patch('asyncio.get_running_loop')
    def test_is_async_context_active_runtime_error(self, mock_get_loop):
        """测试异步上下文检测 - RuntimeError"""
        mock_get_loop.side_effect = RuntimeError("No running event loop")

        result = AsyncContextDetector.is_async_context_active()
        self.assertFalse(result)


class BeanRegistryIntegrationTestCase(unittest.TestCase):
    """Bean注册表集成测试"""

    def setUp(self):
        """测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()

    def tearDown(self):
        """测试后清理"""
        self.registry = None

    def test_registry_basic_operations(self):
        """测试注册表基本操作"""
        # 创建Bean定义
        definition = BeanDefinition("testBean", TestService, BeanScope.SINGLETON)

        # 注册Bean定义
        self.registry.register("testBean", definition)

        # 检查是否存在
        self.assertTrue(self.registry.has_definition("testBean"))

        # 获取Bean定义
        retrieved = self.registry.get_definition("testBean")
        self.assertEqual(retrieved.bean_name, "testBean")
        self.assertEqual(retrieved.bean_class, TestService)

    def test_registry_bean_names(self):
        """测试获取Bean名称列表"""
        # 注册多个Bean
        def1 = BeanDefinition("bean1", TestService)
        def2 = BeanDefinition("bean2", TestService)

        self.registry.register("bean1", def1)
        self.registry.register("bean2", def2)

        # 获取Bean名称
        names = self.registry.get_bean_names()
        self.assertIn("bean1", names)
        self.assertIn("bean2", names)
        self.assertEqual(len(names), 2)

    def test_registry_bean_count(self):
        """测试Bean计数"""
        initial_count = self.registry.count()

        # 注册Bean
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 检查计数
        new_count = self.registry.count()
        self.assertEqual(new_count, initial_count + 1)

    def test_registry_remove_definition(self):
        """测试移除Bean定义"""
        # 注册Bean
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 确认存在
        self.assertTrue(self.registry.has_definition("testBean"))

        # 移除Bean
        self.registry.remove("testBean")

        # 确认已移除
        self.assertFalse(self.registry.has_definition("testBean"))

    def test_registry_clear(self):
        """测试清空注册表"""
        # 注册一些Bean
        def1 = BeanDefinition("bean1", TestService)
        def2 = BeanDefinition("bean2", TestService)

        self.registry.register("bean1", def1)
        self.registry.register("bean2", def2)

        # 确认有Bean
        self.assertGreater(self.registry.count(), 0)

        # 清空
        self.registry.clear()

        # 确认已清空
        self.assertEqual(self.registry.count(), 0)

    def test_registry_error_handling(self):
        """测试注册表错误处理"""
        # 测试获取不存在的Bean定义
        with self.assertRaises(KeyError):
            self.registry.get_definition("nonExistentBean")

        # 测试空名称
        with self.assertRaises(ValueError):
            definition = BeanDefinition("test", TestService)
            self.registry.register("", definition)

    def test_registry_thread_safety_basic(self):
        """测试注册表基本线程安全"""
        import threading

        results = []
        errors = []

        def register_beans(thread_id):
            try:
                for i in range(10):
                    bean_name = f"bean_{thread_id}_{i}"
                    definition = BeanDefinition(bean_name, TestService)
                    self.registry.register(bean_name, definition)
                    results.append(bean_name)
            except Exception as e:
                errors.append((thread_id, e))

        # 创建多个线程
        threads = [
            threading.Thread(target=register_beans, args=(i,))
            for i in range(3)
        ]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 检查结果
        self.assertEqual(len(errors), 0)  # 不应该有错误
        self.assertEqual(len(results), 30)  # 应该注册30个Bean


class BeanDefinitionValidationTestCase(unittest.TestCase):
    """Bean定义验证测试"""

    def test_bean_definition_creation(self):
        """测试Bean定义创建"""
        definition = BeanDefinition("testBean", TestService, BeanScope.SINGLETON)

        self.assertEqual(definition.bean_name, "testBean")
        self.assertEqual(definition.bean_class, TestService)
        self.assertEqual(definition.scope, BeanScope.SINGLETON)

    def test_bean_definition_scope_methods(self):
        """测试Bean定义作用域方法"""
        singleton_def = BeanDefinition("singleton", TestService, BeanScope.SINGLETON)
        prototype_def = BeanDefinition("prototype", TestService, BeanScope.PROTOTYPE)

        # 测试单例检查
        self.assertTrue(singleton_def.singleton())
        self.assertFalse(prototype_def.singleton())

        # 测试原型检查
        self.assertFalse(singleton_def.prototype())
        self.assertTrue(prototype_def.prototype())

    def test_bean_definition_lifecycle_methods(self):
        """测试Bean定义生命周期方法"""
        definition = BeanDefinition(
            "testBean",
            TestService,
            init_method_name="init",
            destroy_method_name="destroy"
        )

        self.assertTrue(definition.has_init())
        self.assertTrue(definition.has_destroy())
        self.assertEqual(definition.init_method_name, "init")
        self.assertEqual(definition.destroy_method_name, "destroy")

    def test_bean_definition_lazy_init(self):
        """测试Bean定义延迟初始化"""
        lazy_def = BeanDefinition("lazy", TestService, lazy_init=True)
        eager_def = BeanDefinition("eager", TestService, lazy_init=False)

        self.assertTrue(lazy_def.lazy())
        self.assertFalse(eager_def.lazy())

    def test_bean_definition_primary(self):
        """测试Bean定义主要标记"""
        primary_def = BeanDefinition("primary", TestService, primary=True)
        normal_def = BeanDefinition("normal", TestService, primary=False)

        self.assertTrue(primary_def.is_primary())
        self.assertFalse(normal_def.is_primary())


class BeanScopeTestCase(unittest.TestCase):
    """Bean作用域测试"""

    def test_bean_scope_values(self):
        """测试Bean作用域值"""
        self.assertEqual(BeanScope.SINGLETON.value, "singleton")
        self.assertEqual(BeanScope.PROTOTYPE.value, "prototype")

    def test_bean_scope_methods(self):
        """测试Bean作用域方法"""
        singleton = BeanScope.SINGLETON
        prototype = BeanScope.PROTOTYPE

        self.assertTrue(singleton.is_singleton())
        self.assertFalse(singleton.is_prototype())

        self.assertFalse(prototype.is_singleton())
        self.assertTrue(prototype.is_prototype())

    def test_bean_scope_string_conversion(self):
        """测试Bean作用域字符串转换"""
        singleton = BeanScope.from_string("singleton")
        prototype = BeanScope.from_string("prototype")

        self.assertEqual(singleton, BeanScope.SINGLETON)
        self.assertEqual(prototype, BeanScope.PROTOTYPE)

        # 测试无效值
        with self.assertRaises(ValueError):
            BeanScope.from_string("invalid")


class BeanStatusTestCase(unittest.TestCase):
    """Bean状态测试"""

    def test_bean_status_values(self):
        """测试Bean状态值"""
        self.assertEqual(BeanStatus.NOT_CREATED.value, "not_created")
        self.assertEqual(BeanStatus.CREATING.value, "creating")
        self.assertEqual(BeanStatus.CREATED.value, "created")
        self.assertEqual(BeanStatus.DESTROYED.value, "destroyed")

    def test_bean_status_transitions(self):
        """测试Bean状态转换"""
        # 测试有效转换
        self.assertTrue(BeanStatus.NOT_CREATED.can_transition_to(BeanStatus.CREATING))
        self.assertTrue(BeanStatus.CREATING.can_transition_to(BeanStatus.CREATED))
        self.assertTrue(BeanStatus.CREATED.can_transition_to(BeanStatus.INITIALIZING))
        self.assertTrue(BeanStatus.CREATED.can_transition_to(BeanStatus.DESTROYING))
        self.assertTrue(BeanStatus.INITIALIZING.can_transition_to(BeanStatus.INITIALIZED))
        self.assertTrue(BeanStatus.DESTROYING.can_transition_to(BeanStatus.DESTROYED))

        # 测试无效转换
        self.assertFalse(BeanStatus.CREATED.can_transition_to(BeanStatus.CREATING))
        self.assertFalse(BeanStatus.CREATED.can_transition_to(BeanStatus.DESTROYED))  # 不能直接销毁
        self.assertFalse(BeanStatus.DESTROYED.can_transition_to(BeanStatus.CREATED))


# ==================== DefaultBeanFactory核心功能测试 ====================

class DefaultBeanFactoryTestCase(unittest.TestCase):
    """DefaultBeanFactory核心功能测试"""

    def setUp(self):
        """测试前准备"""
        from miniboot.bean.factory import DefaultBeanFactory
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    def tearDown(self):
        """测试后清理"""
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()

    def test_factory_initialization(self):
        """测试工厂初始化"""
        from miniboot.bean.factory import DefaultBeanFactory

        # 使用默认注册表初始化
        factory1 = DefaultBeanFactory()
        self.assertIsNotNone(factory1._registry)

        # 使用自定义注册表初始化
        custom_registry = DefaultBeanDefinitionRegistry()
        factory2 = DefaultBeanFactory(custom_registry)
        self.assertIs(factory2._registry, custom_registry)

    def test_get_bean_simple(self):
        """测试简单Bean获取"""
        # 注册Bean定义
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 获取Bean
        bean = self.factory.get_bean("testService")

        self.assertIsInstance(bean, TestService)
        self.assertEqual(bean.name, "test")

    def test_get_bean_with_type_check(self):
        """测试带类型检查的Bean获取"""
        # 注册Bean定义
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 正确类型检查
        bean = self.factory.get_bean("testService", TestService)
        self.assertIsInstance(bean, TestService)

        # 错误类型检查
        with self.assertRaises(TypeError):
            self.factory.get_bean("testService", DatabaseService)

    def test_get_bean_not_found(self):
        """测试获取不存在的Bean"""
        with self.assertRaises(KeyError):
            self.factory.get_bean("nonExistentBean")

    def test_singleton_bean_caching(self):
        """测试单例Bean缓存"""
        # 注册单例Bean
        definition = BeanDefinition("singletonService", TestService, BeanScope.SINGLETON)
        self.registry.register("singletonService", definition)

        # 多次获取应该返回同一实例
        bean1 = self.factory.get_bean("singletonService")
        bean2 = self.factory.get_bean("singletonService")

        self.assertIs(bean1, bean2)

    def test_prototype_bean_creation(self):
        """测试原型Bean创建"""
        # 注册原型Bean
        definition = BeanDefinition("prototypeService", TestService, BeanScope.PROTOTYPE)
        self.registry.register("prototypeService", definition)

        # 多次获取应该返回不同实例
        bean1 = self.factory.get_bean("prototypeService")
        bean2 = self.factory.get_bean("prototypeService")

        self.assertIsNot(bean1, bean2)
        self.assertIsInstance(bean1, TestService)
        self.assertIsInstance(bean2, TestService)

    def test_contains_bean(self):
        """测试Bean存在性检查"""
        # 注册Bean
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 检查存在性
        self.assertTrue(self.factory.contains_bean("testService"))
        self.assertFalse(self.factory.contains_bean("nonExistentBean"))

    def test_is_singleton(self):
        """测试单例检查"""
        # 注册单例Bean
        singleton_def = BeanDefinition("singletonService", TestService, BeanScope.SINGLETON)
        self.registry.register("singletonService", singleton_def)

        # 注册原型Bean
        prototype_def = BeanDefinition("prototypeService", TestService, BeanScope.PROTOTYPE)
        self.registry.register("prototypeService", prototype_def)

        # 检查单例状态
        self.assertTrue(self.factory.is_singleton("singletonService"))
        self.assertFalse(self.factory.is_singleton("prototypeService"))
        self.assertFalse(self.factory.is_singleton("nonExistentBean"))

    def test_get_type(self):
        """测试获取Bean类型"""
        # 注册Bean
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 获取类型
        bean_type = self.factory.get_type("testService")
        self.assertEqual(bean_type, TestService)

        # 不存在的Bean
        self.assertIsNone(self.factory.get_type("nonExistentBean"))

    def test_destroy_singletons(self):
        """测试销毁单例Bean"""
        # 注册单例Bean
        definition = BeanDefinition("singletonService", TestService, BeanScope.SINGLETON)
        self.registry.register("singletonService", definition)

        # 创建Bean
        bean = self.factory.get_bean("singletonService")
        self.assertIsNotNone(bean)

        # 销毁单例
        self.factory.destroy_singletons()

        # 再次获取应该创建新实例
        new_bean = self.factory.get_bean("singletonService")
        self.assertIsNot(bean, new_bean)

    def test_bean_with_destroy_method(self):
        """测试带销毁方法的Bean"""
        # 注册Bean
        definition = BeanDefinition("destroyableService", TestService, BeanScope.SINGLETON)
        self.registry.register("destroyableService", definition)

        # 创建Bean
        bean = self.factory.get_bean("destroyableService")
        self.assertFalse(bean.destroyed)

        # 销毁Bean
        self.factory.destroy_singletons()

        # 验证销毁方法被调用
        self.assertTrue(bean.destroyed)


# ==================== DefaultBeanFactory高级功能测试 ====================

class DefaultBeanFactoryAdvancedTestCase(unittest.TestCase):
    """DefaultBeanFactory高级功能测试"""

    def setUp(self):
        """测试前准备"""
        from miniboot.bean.factory import DefaultBeanFactory
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    def tearDown(self):
        """测试后清理"""
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()

    def test_get_beans_of_type(self):
        """测试按类型获取Bean"""
        # 注册多个TestService类型的Bean
        def1 = BeanDefinition("testService1", TestService, BeanScope.SINGLETON)
        def2 = BeanDefinition("testService2", TestService, BeanScope.SINGLETON)
        def3 = BeanDefinition("dbService", DatabaseService, BeanScope.SINGLETON)

        self.registry.register("testService1", def1)
        self.registry.register("testService2", def2)
        self.registry.register("dbService", def3)

        # 获取TestService类型的所有Bean
        test_services = self.factory.get_beans_of_type(TestService)

        self.assertEqual(len(test_services), 2)
        self.assertIn("testService1", test_services)
        self.assertIn("testService2", test_services)
        self.assertNotIn("dbService", test_services)

        # 获取DatabaseService类型的Bean
        db_services = self.factory.get_beans_of_type(DatabaseService)
        self.assertEqual(len(db_services), 1)
        self.assertIn("dbService", db_services)

    def test_get_beans_of_type_include_non_singletons(self):
        """测试按类型获取Bean（包含非单例）"""
        # 注册不同作用域的Bean
        singleton_def = BeanDefinition("singletonService", TestService, BeanScope.SINGLETON)
        prototype_def = BeanDefinition("prototypeService", TestService, BeanScope.PROTOTYPE)

        self.registry.register("singletonService", singleton_def)
        self.registry.register("prototypeService", prototype_def)

        # 包含非单例Bean
        all_services = self.factory.get_beans_of_type(TestService, include_non_singletons=True)
        self.assertEqual(len(all_services), 2)

        # 只包含单例Bean
        singleton_services = self.factory.get_beans_of_type(TestService, include_non_singletons=False)
        self.assertEqual(len(singleton_services), 1)
        self.assertIn("singletonService", singleton_services)
        self.assertNotIn("prototypeService", singleton_services)

    def test_get_bean_names_for_type(self):
        """测试按类型获取Bean名称"""
        # 注册Bean
        def1 = BeanDefinition("testService1", TestService)
        def2 = BeanDefinition("testService2", TestService)
        def3 = BeanDefinition("dbService", DatabaseService)

        self.registry.register("testService1", def1)
        self.registry.register("testService2", def2)
        self.registry.register("dbService", def3)

        # 获取TestService类型的Bean名称
        test_names = self.factory.get_bean_names_for_type(TestService)

        self.assertEqual(len(test_names), 2)
        self.assertIn("testService1", test_names)
        self.assertIn("testService2", test_names)
        self.assertNotIn("dbService", test_names)

    def test_bean_definition_count(self):
        """测试Bean定义数量"""
        # 初始状态
        self.assertEqual(self.factory.get_bean_definition_count(), 0)

        # 注册Bean
        def1 = BeanDefinition("service1", TestService)
        def2 = BeanDefinition("service2", DatabaseService)

        self.registry.register("service1", def1)
        self.assertEqual(self.factory.get_bean_definition_count(), 1)

        self.registry.register("service2", def2)
        self.assertEqual(self.factory.get_bean_definition_count(), 2)

    def test_bean_definition_names(self):
        """测试获取Bean定义名称"""
        # 注册Bean
        def1 = BeanDefinition("service1", TestService)
        def2 = BeanDefinition("service2", DatabaseService)

        self.registry.register("service1", def1)
        self.registry.register("service2", def2)

        # 获取所有Bean名称
        names = self.factory.get_bean_definition_names()

        self.assertEqual(len(names), 2)
        self.assertIn("service1", names)
        self.assertIn("service2", names)

    def test_async_context_detection(self):
        """测试异步上下文检测"""
        # 获取异步检测器
        detector = self.factory._async_detector

        # 在同步环境中应该返回False
        self.assertFalse(detector.is_async_context_active())

        # 测试执行模式检测
        mode = detector.detect_execution_mode()
        self.assertEqual(mode, "sync")

        # 测试事件循环信息
        loop_info = detector.get_event_loop_info()
        self.assertFalse(loop_info["has_loop"])

    def test_bean_status_tracking(self):
        """测试Bean状态跟踪"""
        # 注册Bean
        definition = BeanDefinition("statusService", TestService)
        self.registry.register("statusService", definition)

        # 初始状态
        status = self.factory.get_bean_status("statusService")
        self.assertEqual(status, BeanStatus.NOT_CREATED)

        # 创建Bean后状态应该改变
        bean = self.factory.get_bean("statusService")
        self.assertIsNotNone(bean)

        # 检查状态是否更新
        status = self.factory.get_bean_status("statusService")
        self.assertIn(status, [BeanStatus.CREATED, BeanStatus.INITIALIZED])

    def test_performance_stats(self):
        """测试性能统计"""
        # 注册Bean
        definition = BeanDefinition("perfService", TestService, BeanScope.SINGLETON)
        self.registry.register("perfService", definition)

        # 获取Bean触发统计
        bean = self.factory.get_bean("perfService")
        self.assertIsNotNone(bean)

        # 获取性能统计
        stats = self.factory.get_performance_stats()

        self.assertIsInstance(stats, dict)
        # 验证统计信息包含预期字段
        expected_fields = ["total_beans_created", "cache_hits", "cache_misses"]
        for field in expected_fields:
            if field in stats:
                self.assertIsInstance(stats[field], (int, float))

    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        # 创建循环依赖的Bean类
        class ServiceA:
            def __init__(self):
                self.service_b = None

        class ServiceB:
            def __init__(self):
                self.service_a = None

        # 注册循环依赖的Bean
        def_a = BeanDefinition("serviceA", ServiceA)
        def_b = BeanDefinition("serviceB", ServiceB)

        # 添加循环依赖关系
        def_a.add_dependency("serviceB")
        def_b.add_dependency("serviceA")

        self.registry.register("serviceA", def_a)
        self.registry.register("serviceB", def_b)

        # 尝试创建应该检测到循环依赖
        with self.assertRaises((BeanCircularDependencyError, RecursionError)):
            self.factory.get_bean("serviceA")


if __name__ == '__main__':
    unittest.main()
