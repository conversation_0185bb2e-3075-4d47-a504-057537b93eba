"""
Load Monitor Auto Configuration

负载监控自动配置类，提供 LoadMonitor 的自动配置和 Bean 注册。
"""

from typing import Optional

from miniboot.annotations import Bean
from miniboot.autoconfigure import (AutoConfiguration,
                                    AutoConfigurationMetadata,
                                    ConditionalOnProperty)

from ..monitor import LoadMonitor
from ..properties import LoadMonitorProperties


@ConditionalOnProperty(name="miniboot.starters.web.load-monitoring.enabled", match_if_missing=True)
class LoadMonitorAutoConfiguration(AutoConfiguration):
    """负载监控自动配置

    当满足以下条件时自动配置 LoadMonitor：
    1. 配置属性 miniboot.starters.web.load-monitoring.enabled 为 true（默认为 true）

    提供的 Bean：
    - load_monitor_properties: LoadMonitorProperties 配置属性
    - load_monitor: LoadMonitor 负载监控器实例
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取自动配置元数据

        Returns:
            自动配置元数据
        """
        return AutoConfigurationMetadata(
            name="load-monitor-auto-configuration",
            description="Web 负载监控自动配置，提供系统和应用负载的实时监控功能",
            priority=400,  # 中等优先级，在基础组件之后，业务组件之前
        )

    @Bean
    def load_monitor_properties(self) -> LoadMonitorProperties:
        """创建负载监控配置属性 Bean

        Returns:
            LoadMonitorProperties 实例
        """
        properties = LoadMonitorProperties()

        # 验证配置参数
        properties.validate()

        return properties

    @Bean
    def load_monitor(self, properties: LoadMonitorProperties) -> LoadMonitor:
        """创建负载监控器 Bean

        Args:
            properties: 负载监控配置属性（自动注入）

        Returns:
            LoadMonitor 实例
        """
        # 创建 LoadMonitor 实例
        monitor = LoadMonitor(properties)

        # 容器会自动调用 @PostConstruct 方法启动监控

        return monitor
