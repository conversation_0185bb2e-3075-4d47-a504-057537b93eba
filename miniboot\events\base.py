#!/usr/bin/env python
"""
* @author: cz
* @description: 事件基类系统

实现Mini-Boot框架的事件基类,包括Event抽象基类和ApplicationEvent应用事件基类.
提供事件的基本属性管理、状态跟踪和数据存储功能.
"""

import threading
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Optional

from miniboot.utils import SingletonMeta


class EventIdGenerator(metaclass=SingletonMeta):
    """线程安全的事件ID生成器

    生成递增的数字ID,支持高并发环境下的线程安全操作.
    ID格式:{timestamp_prefix}{sequence_number}
    """

    # 类级别的初始化锁
    _init_lock = threading.RLock()

    def __init__(self):
        """初始化ID生成器"""
        # 防止重复初始化(单例模式要求)，使用双重检查锁定
        if not hasattr(self, '_initialized'):
            with self._init_lock:
                # 双重检查：防止竞态条件
                if not hasattr(self, '_initialized'):
                    self._lock = threading.Lock()
                    self._counter = 0
                    self._timestamp_prefix = int(time.time() * 1000) % 1000000  # 6位时间戳前缀
                    self._initialized = True

    def generate_id(self) -> str:
        """生成唯一的事件ID

        Returns:
            str: 格式为 {timestamp_prefix}{sequence_number} 的唯一ID
        """
        with self._lock:
            self._counter += 1
            # 如果计数器超过999999,重置并更新时间戳前缀
            if self._counter > 999999:
                self._counter = 1
                self._timestamp_prefix = int(time.time() * 1000) % 1000000

            return f"{self._timestamp_prefix:06d}{self._counter:06d}"

    def reset(self) -> None:
        """重置ID生成器(主要用于测试)"""
        with self._lock:
            self._counter = 0
            self._timestamp_prefix = int(time.time() * 1000) % 1000000

    def get_stats(self) -> dict:
        """获取生成器统计信息"""
        with self._lock:
            return {
                'current_counter': self._counter,
                'timestamp_prefix': self._timestamp_prefix,
                'total_generated': self._counter
            }





class Event(ABC):
    """事件抽象基类

    所有事件类型的基础接口,定义了事件的基本属性和行为.
    提供事件的唯一标识、时间戳、数据存储等核心功能.
    """

    def __init__(self, source: Any = None, **kwargs):
        """初始化事件

        Args:
            source: 事件源对象
            **kwargs: 额外的事件数据
        """
        self._id = EventIdGenerator().generate_id()
        self._timestamp = datetime.now()
        self._source = source
        self._data = kwargs.copy()
        self._processed = False
        self._processing_time: Optional[float] = None

    @property
    def id(self) -> str:
        """事件唯一标识符"""
        return self._id

    @property
    def timestamp(self) -> datetime:
        """事件创建时间戳"""
        return self._timestamp

    @property
    def source(self) -> Any:
        """事件源对象"""
        return self._source

    @property
    def data(self) -> dict:
        """事件数据字典"""
        return self._data.copy()

    @property
    def processed(self) -> bool:
        """事件是否已被处理"""
        return self._processed

    @property
    def processing_time(self) -> Optional[float]:
        """事件处理耗时(秒)"""
        return self._processing_time

    def get_data(self, key: str, default: Any = None) -> Any:
        """获取事件数据

        Args:
            key: 数据键
            default: 默认值

        Returns:
            Any: 数据值
        """
        return self._data.get(key, default)

    def set_data(self, key: str, value: Any) -> None:
        """设置事件数据

        Args:
            key: 数据键
            value: 数据值
        """
        self._data[key] = value

    def mark_processed(self, processing_time: Optional[float] = None) -> None:
        """标记事件为已处理

        Args:
            processing_time: 处理耗时(秒)
        """
        self._processed = True
        self._processing_time = processing_time

    @abstractmethod
    def get_event_type(self) -> str:
        """获取事件类型名称

        Returns:
            str: 事件类型名称
        """
        pass

    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.get_event_type()}(id={self.id}, timestamp={self.timestamp})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}(id={self.id}, "
                f"timestamp={self.timestamp}, source={self.source}, "
                f"processed={self.processed})")


class ApplicationEvent(Event):
    """应用事件基类

    应用级事件的基础类,提供应用相关的事件功能.
    继承自Event基类,添加了应用特定的属性和方法.
    """

    def __init__(self, source: Any = None, application_name: str = "mini-boot", **kwargs):
        """初始化应用事件

        Args:
            source: 事件源对象
            application_name: 应用名称
            **kwargs: 额外的事件数据
        """
        super().__init__(source, **kwargs)
        self._application_name = application_name

    @property
    def application_name(self) -> str:
        """应用名称"""
        return self._application_name

    def get_event_type(self) -> str:
        """获取事件类型名称"""
        return "ApplicationEvent"


# === 具体事件类已移至 types.py ===
# 使用组合式事件实现，避免重复定义
