#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: <PERSON>ck Starter测试
"""

import pytest
from unittest.mock import Mock

from miniboot.starters.mock.properties import MockProperties
from miniboot.starters.mock.service import MockService
from miniboot.starters.mock.configuration import MockAutoConfiguration
from miniboot.context import ApplicationContext


class TestMockProperties:
    """Mock配置属性测试"""

    def test_default_values(self):
        """测试默认值"""
        props = MockProperties()

        assert props.enabled is True
        assert props.auto_mock is True
        assert props.strict_mode is False
        assert props.data_source == "random"
        assert props.response_delay == 0
        assert props.failure_rate == 0.0
        assert props.cache_enabled is True
        assert props.cache_size == 1000
        assert props.cache_ttl == 3600

    def test_validation_success(self):
        """测试验证成功"""
        props = MockProperties()
        props.validate()  # 应该不抛出异常

    def test_validation_failure_rate(self):
        """测试失败率验证"""
        props = MockProperties()
        props.failure_rate = 1.5  # 超出范围

        with pytest.raises(ValueError, match="failure_rate must be between 0.0 and 1.0"):
            props.validate()

    def test_validation_timeout(self):
        """测试超时验证"""
        props = MockProperties()
        props.timeout = -1

        with pytest.raises(ValueError, match="timeout must be positive"):
            props.validate()

    def test_validation_data_source_file(self):
        """测试文件数据源验证"""
        props = MockProperties()
        props.data_source = "file"
        props.data_file_path = None

        with pytest.raises(ValueError, match="data_file_path is required"):
            props.validate()

    def test_pattern_matching(self):
        """测试URL模式匹配"""
        props = MockProperties()
        props.patterns = [r"^https://api\.example\.com/.*", r"^http://localhost:.*"]

        assert props.is_pattern_matched("https://api.example.com/users") is True
        assert props.is_pattern_matched("http://localhost:8080/test") is True
        assert props.is_pattern_matched("https://other.com/api") is False

    def test_pattern_matching_empty(self):
        """测试空模式匹配"""
        props = MockProperties()
        props.patterns = []

        # 空模式应该匹配所有URL
        assert props.is_pattern_matched("https://any.url.com") is True


class TestMockService:
    """Mock服务测试"""

    def setup_method(self):
        """设置测试"""
        self.properties = MockProperties()
        self.service = MockService(self.properties)

    def test_mock_request_basic(self):
        """测试基本Mock请求"""
        response = self.service.mock_request("https://api.example.com/users")

        assert response is not None
        assert response["mock"] is True
        assert "status" in response
        assert "data" in response

    def test_mock_request_with_delay(self):
        """测试带延迟的Mock请求"""
        import time

        self.properties.response_delay = 100  # 100ms
        self.service = MockService(self.properties)

        start_time = time.time()
        response = self.service.mock_request("https://api.example.com/users")
        end_time = time.time()

        # 应该有延迟
        assert (end_time - start_time) >= 0.1
        assert response["mock"] is True

    def test_mock_request_with_failure(self):
        """测试Mock请求失败"""
        self.properties.failure_rate = 1.0  # 100%失败率
        self.service = MockService(self.properties)

        with pytest.raises(Exception, match="Mock failure"):
            self.service.mock_request("https://api.example.com/users")

    def test_cache_functionality(self):
        """测试缓存功能"""
        # 第一次请求
        response1 = self.service.mock_request("https://api.example.com/users")

        # 第二次请求应该返回缓存结果
        response2 = self.service.mock_request("https://api.example.com/users")

        assert response1 == response2

    def test_add_remove_rule(self):
        """测试添加和移除规则"""
        pattern = r"^https://api\.example\.com/users$"
        response_data = {"custom": "response"}

        # 添加规则
        self.service.add_rule(pattern, response_data)

        # 测试规则生效
        response = self.service.mock_request("https://api.example.com/users")
        assert response == response_data

        # 移除规则
        self.service.remove_rule(pattern)

        # 测试规则移除后使用默认响应
        response = self.service.mock_request("https://api.example.com/users")
        assert response != response_data

    def test_clear_cache(self):
        """测试清空缓存"""
        # 先生成一些缓存
        self.service.mock_request("https://api.example.com/users")
        assert len(self.service.cache._cache) > 0

        # 清空缓存
        self.service.clear_cache()
        assert len(self.service.cache._cache) == 0

    def test_get_stats(self):
        """测试获取统计信息"""
        stats = self.service.get_stats()

        assert "enabled" in stats
        assert "data_source" in stats
        assert "cache_enabled" in stats
        assert "cache_size" in stats
        assert "rules_count" in stats
        assert "patterns_count" in stats


class TestMockAutoConfiguration:
    """Mock自动配置测试"""

    def setup_method(self):
        """设置测试"""
        self.config = MockAutoConfiguration()
        self.context = Mock(spec=ApplicationContext)

    def test_metadata(self):
        """测试配置元数据"""
        metadata = self.config.get_metadata()

        assert metadata.name == "mock-auto-configuration"
        assert metadata.description == "Mock功能自动配置"
        assert metadata.priority == 200

    def test_starter_info(self):
        """测试Starter信息"""
        assert self.config.get_starter_name() == "miniboot-starter-mock"
        assert self.config.get_starter_version() == "1.0.0"
        assert "Mock功能Starter" in self.config.get_starter_description()

    def test_configuration_properties_classes(self):
        """测试配置属性类"""
        classes = self.config.get_configuration_properties_classes()
        assert MockProperties in classes

    def test_mock_service_bean(self):
        """测试Mock服务Bean创建"""
        mock_properties = MockProperties()

        service = self.config.mock_service(mock_properties)

        assert isinstance(service, MockService)
        assert service.properties == mock_properties

    def test_mock_interceptor_bean(self):
        """测试Mock拦截器Bean创建"""
        mock_properties = MockProperties()
        mock_service = MockService(mock_properties)

        interceptor = self.config.mock_interceptor(mock_service)

        assert interceptor is not None
        assert interceptor.mock_service == mock_service
        assert interceptor.is_enabled() is True

    def test_initialize_starter(self):
        """测试Starter初始化"""
        # 模拟上下文
        mock_properties = MockProperties()
        self.context.get_bean.return_value = mock_properties

        # 应该不抛出异常
        self.config._initialize_starter(self.context)

        # 验证get_bean被调用
        self.context.get_bean.assert_called_with("mock_properties")
