#!/usr/bin/env python
"""
* @author: cz
* @description: 绑定结果测试
"""

import unittest

from miniboot.env.bind import BindingResult


class BindingResultTestCase(unittest.TestCase):
    """绑定结果测试"""

    def test_initial_state(self):
        """测试初始状态"""
        result = BindingResult()

        self.assertIsNone(result.target)
        self.assertFalse(result.has_errors)
        self.assertEqual(0, len(result.errors))

    def test_target_assignment(self):
        """测试目标对象赋值"""
        target_obj = {"test": "value"}
        result = BindingResult(target_obj)

        self.assertEqual(target_obj, result.target)

    def test_add_error(self):
        """测试添加错误"""
        result = BindingResult()

        # 添加第一个错误
        result.add_error("prop1", "Error message 1")
        self.assertTrue(result.has_errors)
        self.assertEqual(1, len(result.errors))

        # 添加第二个错误
        result.add_error("prop2", "Error message 2", ValueError("test"))
        self.assertEqual(2, len(result.errors))

        # 检查错误内容
        errors = result.errors
        self.assertEqual("prop1", errors[0].property_name)
        self.assertEqual("Error message 1", errors[0].message)
        self.assertIsNone(errors[0].cause)

        self.assertEqual("prop2", errors[1].property_name)
        self.assertEqual("Error message 2", errors[1].message)
        self.assertIsInstance(errors[1].cause, ValueError)

    def test_get_errors_for_property(self):
        """测试获取特定属性的错误"""
        result = BindingResult()

        # 添加多个错误
        result.add_error("prop1", "Error 1 for prop1")
        result.add_error("prop2", "Error for prop2")
        result.add_error("prop1", "Error 2 for prop1")

        # 获取 prop1 的错误
        prop1_errors = result.get_errors_for_property("prop1")
        self.assertEqual(2, len(prop1_errors))
        self.assertEqual("Error 1 for prop1", prop1_errors[0].message)
        self.assertEqual("Error 2 for prop1", prop1_errors[1].message)

        # 获取 prop2 的错误
        prop2_errors = result.get_errors_for_property("prop2")
        self.assertEqual(1, len(prop2_errors))
        self.assertEqual("Error for prop2", prop2_errors[0].message)

        # 获取不存在属性的错误
        prop3_errors = result.get_errors_for_property("prop3")
        self.assertEqual(0, len(prop3_errors))

    def test_string_representation_no_errors(self):
        """测试无错误时的字符串表示"""
        target_obj = "test_target"
        result = BindingResult(target_obj)

        str_repr = str(result)
        self.assertIn("test_target", str_repr)
        self.assertIn("errors=0", str_repr)

    def test_string_representation_with_errors(self):
        """测试有错误时的字符串表示"""
        result = BindingResult("test_target")
        result.add_error("prop1", "Error message 1")
        result.add_error("prop2", "Error message 2")

        str_repr = str(result)
        self.assertIn("test_target", str_repr)
        self.assertIn("errors=2", str_repr)
        self.assertIn("prop1: Error message 1", str_repr)
        self.assertIn("prop2: Error message 2", str_repr)

    def test_errors_immutability(self):
        """测试错误列表的不可变性"""
        result = BindingResult()
        result.add_error("prop1", "Error message")

        # 获取错误列表
        errors1 = result.errors
        errors2 = result.errors

        # 应该是不同的实例(副本)
        self.assertIsNot(errors1, errors2)
        self.assertEqual(errors1, errors2)

        # 修改返回的列表不应该影响原始数据
        errors1.clear()
        self.assertEqual(1, len(result.errors))


if __name__ == "__main__":
    unittest.main()
