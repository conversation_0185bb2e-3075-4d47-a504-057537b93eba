<execution>
  <constraint>
    ## Python架构设计客观限制
    
    ### 语言特性约束
    - **GIL限制**: Python的全局解释器锁限制了真正的多线程并行
    - **性能特性**: 解释型语言的性能特点需要在架构设计中考虑
    - **动态特性**: 动态类型和运行时特性带来的性能和调试挑战
    - **内存管理**: Python的垃圾回收机制对大型应用的影响
    
    ### 生态系统约束
    - **依赖管理**: Python包管理的复杂性和版本冲突问题
    - **部署环境**: 不同Python版本和操作系统的兼容性要求
    - **第三方库**: 依赖第三方库的稳定性和维护状况
    - **标准库限制**: Python标准库在某些领域的功能限制
    
    ### 企业级约束
    - **性能要求**: 企业级应用对性能和并发的高要求
    - **安全要求**: 企业环境对安全性的严格要求
    - **可维护性**: 大型团队协作开发的代码质量要求
    - **兼容性**: 与现有系统和工具链的集成要求
  </constraint>
  
  <rule>
    ## 架构设计强制规则
    
    ### 设计原则规则
    - **单一职责**: 每个模块和类必须有明确的单一职责
    - **开闭原则**: 对扩展开放，对修改封闭的设计
    - **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
    - **接口隔离**: 客户端不应依赖它不需要的接口
    
    ### 代码质量规则
    - **类型注解**: 所有公共API必须提供完整的类型注解
    - **文档字符串**: 所有公共方法必须有详细的docstring
    - **异常处理**: 必须有完善的异常处理和错误信息
    - **测试覆盖**: 核心功能的测试覆盖率必须达到90%以上
    
    ### 性能规则
    - **懒加载**: 非核心功能必须采用懒加载策略
    - **缓存机制**: 重复计算的结果必须进行缓存
    - **资源管理**: 必须正确管理文件、网络等系统资源
    - **内存优化**: 避免内存泄漏和不必要的内存占用
    
    ### 安全规则
    - **输入验证**: 所有外部输入必须进行严格验证
    - **权限控制**: 实现细粒度的权限控制机制
    - **敏感信息**: 敏感信息不得以明文形式存储或传输
    - **依赖安全**: 定期检查和更新依赖库的安全漏洞
  </rule>
  
  <guideline>
    ## 架构设计指导原则
    
    ### 模块化设计指导
    - **高内聚低耦合**: 模块内部功能紧密相关，模块间依赖最小化
    - **分层架构**: 建立清晰的分层结构，每层有明确的职责
    - **插件机制**: 设计灵活的插件系统支持功能扩展
    - **配置驱动**: 通过配置而非代码修改来改变系统行为
    
    ### API设计指导
    - **一致性**: 保持API命名和使用模式的一致性
    - **简洁性**: API应该简单易用，避免过度复杂的参数
    - **可发现性**: 提供良好的IDE支持和自动补全
    - **向后兼容**: 新版本应保持对旧版本API的兼容
    
    ### 性能优化指导
    - **性能监控**: 建立完善的性能监控和分析机制
    - **瓶颈识别**: 定期进行性能分析找出系统瓶颈
    - **优化策略**: 采用合适的优化策略如缓存、异步等
    - **基准测试**: 建立基准测试确保优化效果
    
    ### 文档和测试指导
    - **文档驱动**: 先写文档再写代码的开发方式
    - **示例丰富**: 提供丰富的使用示例和最佳实践
    - **测试驱动**: 采用TDD方法确保代码质量
    - **持续集成**: 建立自动化的测试和部署流程
  </guideline>
  
  <process>
    ## 架构设计执行流程
    
    ### 需求分析阶段
    1. **用户调研**: 深入了解目标用户的需求和痛点
    2. **场景分析**: 分析主要使用场景和边界条件
    3. **竞品研究**: 研究现有解决方案的优缺点
    4. **需求文档**: 编写详细的需求规格说明书
    
    ### 架构设计阶段
    1. **概念设计**: 确定系统的核心概念和抽象
    2. **模块划分**: 将系统分解为独立的功能模块
    3. **接口设计**: 设计模块间的接口和数据流
    4. **技术选型**: 选择合适的技术栈和第三方库
    
    ### 详细设计阶段
    1. **类图设计**: 设计详细的类结构和关系
    2. **序列图**: 描述关键流程的执行序列
    3. **数据模型**: 设计数据结构和存储方案
    4. **配置方案**: 设计配置文件格式和加载机制
    
    ### 实现验证阶段
    1. **原型开发**: 开发核心功能的原型验证设计
    2. **性能测试**: 验证架构的性能表现
    3. **集成测试**: 测试各模块间的集成效果
    4. **用户反馈**: 收集早期用户的使用反馈
    
    ### 迭代优化阶段
    1. **问题收集**: 收集使用过程中发现的问题
    2. **性能分析**: 分析系统的性能瓶颈
    3. **架构调整**: 根据反馈调整架构设计
    4. **版本规划**: 制定后续版本的功能规划
  </process>
  
  <criteria>
    ## 架构质量评价标准
    
    ### 功能性标准
    - **完整性**: 架构能够支持所有预期的功能需求
    - **正确性**: 系统行为符合设计规范和用户期望
    - **可靠性**: 系统在各种条件下都能稳定运行
    - **容错性**: 具备良好的错误处理和恢复能力
    
    ### 性能标准
    - **响应时间**: 关键操作的响应时间满足性能要求
    - **吞吐量**: 系统能够处理预期的并发请求量
    - **资源利用**: 合理利用CPU、内存等系统资源
    - **可扩展性**: 能够通过水平或垂直扩展提升性能
    
    ### 可维护性标准
    - **代码质量**: 代码结构清晰，符合编码规范
    - **文档完整**: 提供完整的架构文档和API文档
    - **测试覆盖**: 具备完善的测试用例和高覆盖率
    - **模块化**: 良好的模块化设计便于维护和扩展
    
    ### 用户体验标准
    - **易用性**: API设计直观，学习成本低
    - **一致性**: 整体设计风格和使用模式一致
    - **文档质量**: 提供清晰的使用指南和示例
    - **社区支持**: 活跃的社区和及时的问题解答
  </criteria>
</execution>
