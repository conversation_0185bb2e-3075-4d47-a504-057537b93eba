#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置优先级管理器测试
"""

import unittest

from miniboot.env.priority import (
    ConfigurationLayer,
    ConfigurationMergeStrategy,
    ConfigurationPriority,
    ConfigurationPriorityManager,
    ConfigurationSearchPath,
    get_priority_manager,
)


class ConfigurationPriorityTestCase(unittest.TestCase):
    """配置优先级枚举测试"""

    def test_priority_values(self):
        """测试优先级数值"""
        self.assertEqual(ConfigurationPriority.COMMAND_LINE, 10000)
        self.assertEqual(ConfigurationPriority.SYSTEM_ENVIRONMENT, 9000)
        self.assertEqual(ConfigurationPriority.USER_PROFILE_SPECIFIC, 8000)
        self.assertEqual(ConfigurationPriority.USER_DEFAULT, 7000)
        self.assertEqual(ConfigurationPriority.FRAMEWORK_PROFILE_SPECIFIC, 6000)
        self.assertEqual(ConfigurationPriority.FRAMEWORK_DEFAULT, 5000)
        self.assertEqual(ConfigurationPriority.SYSTEM_DEFAULT, 1000)

    def test_priority_order(self):
        """测试优先级顺序"""
        priorities = [
            ConfigurationPriority.COMMAND_LINE,
            ConfigurationPriority.SYSTEM_ENVIRONMENT,
            ConfigurationPriority.USER_PROFILE_SPECIFIC,
            ConfigurationPriority.USER_DEFAULT,
            ConfigurationPriority.FRAMEWORK_PROFILE_SPECIFIC,
            ConfigurationPriority.FRAMEWORK_DEFAULT,
            ConfigurationPriority.SYSTEM_DEFAULT,
        ]

        # 验证优先级是降序排列
        for i in range(len(priorities) - 1):
            self.assertGreater(priorities[i], priorities[i + 1])


class ConfigurationSearchPathTestCase(unittest.TestCase):
    """配置搜索路径测试"""

    def setUp(self):
        """设置测试环境"""
        self.search_path = ConfigurationSearchPath()

    def test_default_paths(self):
        """测试默认搜索路径"""
        user_paths = self.search_path.get_user_paths()
        framework_paths = self.search_path.get_framework_paths()
        system_paths = self.search_path.get_system_paths()

        # 验证用户路径
        self.assertIn(".", user_paths)
        self.assertIn("./config", user_paths)
        self.assertIn("./resources", user_paths)

        # 验证框架路径
        self.assertIn("miniboot/resources", framework_paths)
        self.assertIn("miniboot/config", framework_paths)

        # 验证系统路径
        self.assertIn("/etc/miniboot", system_paths)
        self.assertIn("~/.miniboot", system_paths)

    def test_add_user_path(self):
        """测试添加用户路径"""
        # 由于get_user_paths()会重新加载环境变量路径，我们直接测试内部状态
        original_paths = self.search_path._user_paths.copy()

        # 添加到最前面（使用唯一路径）
        unique_path1 = "/custom/unique/path/1"
        self.search_path.add_user_path(unique_path1, first=True)
        self.assertIn(unique_path1, self.search_path._user_paths)
        self.assertEqual(self.search_path._user_paths[0], unique_path1)

        # 添加到最后面（使用唯一路径）
        unique_path2 = "/custom/unique/path/2"
        self.search_path.add_user_path(unique_path2, first=False)
        self.assertIn(unique_path2, self.search_path._user_paths)
        self.assertEqual(self.search_path._user_paths[-1], unique_path2)

    def test_add_framework_path(self):
        """测试添加框架路径"""
        original_count = len(self.search_path.get_framework_paths())

        self.search_path.add_framework_path("/framework/path")
        framework_paths = self.search_path.get_framework_paths()
        self.assertEqual(len(framework_paths), original_count + 1)
        self.assertEqual(framework_paths[0], "/framework/path")

    def test_get_all_paths(self):
        """测试获取所有路径"""
        all_paths = self.search_path.get_all_paths()
        user_paths = self.search_path.get_user_paths()
        framework_paths = self.search_path.get_framework_paths()
        system_paths = self.search_path.get_system_paths()

        # 验证所有路径包含各层路径
        for path in user_paths:
            self.assertIn(path, all_paths)
        for path in framework_paths:
            self.assertIn(path, all_paths)
        for path in system_paths:
            self.assertIn(path, all_paths)

    def test_duplicate_path_prevention(self):
        """测试重复路径防护"""
        original_count = len(self.search_path.get_user_paths())

        # 添加已存在的路径
        self.search_path.add_user_path(".")
        user_paths = self.search_path.get_user_paths()

        # 路径数量不应增加
        self.assertEqual(len(user_paths), original_count)


class ConfigurationPriorityManagerTestCase(unittest.TestCase):
    """配置优先级管理器测试"""

    def setUp(self):
        """设置测试环境"""
        self.manager = ConfigurationPriorityManager()

    def test_get_priority_for_source(self):
        """测试获取配置源优先级"""
        # 命令行参数
        priority = self.manager.get_priority_for_source("commandLine", ConfigurationLayer.SYSTEM)
        self.assertEqual(priority, ConfigurationPriority.COMMAND_LINE)

        # 系统环境变量
        priority = self.manager.get_priority_for_source("systemEnvironment", ConfigurationLayer.SYSTEM)
        self.assertEqual(priority, ConfigurationPriority.SYSTEM_ENVIRONMENT)

        # 用户默认配置
        priority = self.manager.get_priority_for_source("application.yml", ConfigurationLayer.USER, False)
        self.assertEqual(priority, ConfigurationPriority.USER_DEFAULT)

        # 用户Profile配置
        priority = self.manager.get_priority_for_source("application-dev.yml", ConfigurationLayer.USER, True)
        self.assertEqual(priority, ConfigurationPriority.USER_PROFILE_SPECIFIC)

        # 框架默认配置
        priority = self.manager.get_priority_for_source("application.yml", ConfigurationLayer.FRAMEWORK, False)
        self.assertEqual(priority, ConfigurationPriority.FRAMEWORK_DEFAULT)

        # 框架Profile配置
        priority = self.manager.get_priority_for_source("application-prod.yml", ConfigurationLayer.FRAMEWORK, True)
        self.assertEqual(priority, ConfigurationPriority.FRAMEWORK_PROFILE_SPECIFIC)

    def test_get_merge_strategy(self):
        """测试获取合并策略"""
        # 默认策略
        strategy = self.manager.get_merge_strategy("unknown.property")
        self.assertEqual(strategy, ConfigurationMergeStrategy.OVERRIDE)

        # 特定属性策略
        strategy = self.manager.get_merge_strategy("miniboot.web.cors.allowed-origins")
        self.assertEqual(strategy, ConfigurationMergeStrategy.APPEND)

        strategy = self.manager.get_merge_strategy("miniboot.logging.loggers")
        self.assertEqual(strategy, ConfigurationMergeStrategy.MERGE)

    def test_set_merge_strategy(self):
        """测试设置合并策略"""
        # 设置新的合并策略
        self.manager.set_merge_strategy("custom.property", ConfigurationMergeStrategy.MERGE)
        strategy = self.manager.get_merge_strategy("custom.property")
        self.assertEqual(strategy, ConfigurationMergeStrategy.MERGE)

        # 设置通配符策略
        self.manager.set_merge_strategy("custom.*", ConfigurationMergeStrategy.APPEND)
        strategy = self.manager.get_merge_strategy("custom.sub.property")
        self.assertEqual(strategy, ConfigurationMergeStrategy.APPEND)

    def test_is_user_configuration_higher_priority(self):
        """测试用户配置优先级判断"""
        user_priority = ConfigurationPriority.USER_DEFAULT
        framework_priority = ConfigurationPriority.FRAMEWORK_DEFAULT

        result = self.manager.is_user_configuration_higher_priority(user_priority, framework_priority)
        self.assertTrue(result)

        # 反向测试
        result = self.manager.is_user_configuration_higher_priority(framework_priority, user_priority)
        self.assertFalse(result)

    def test_validate_priority_order(self):
        """测试优先级顺序验证"""
        # 正确的降序
        correct_order = [10000, 9000, 8000, 7000]
        self.assertTrue(self.manager.validate_priority_order(correct_order))

        # 错误的顺序
        incorrect_order = [7000, 9000, 8000, 10000]
        self.assertFalse(self.manager.validate_priority_order(incorrect_order))

        # 相等的优先级
        equal_priorities = [8000, 8000, 7000]
        self.assertTrue(self.manager.validate_priority_order(equal_priorities))

    def test_get_search_path(self):
        """测试获取搜索路径管理器"""
        search_path = self.manager.get_search_path()
        self.assertIsInstance(search_path, ConfigurationSearchPath)


class GlobalPriorityManagerTestCase(unittest.TestCase):
    """全局优先级管理器测试"""

    def test_get_priority_manager(self):
        """测试获取全局优先级管理器"""
        manager1 = get_priority_manager()
        manager2 = get_priority_manager()

        # 应该返回同一个实例
        self.assertIs(manager1, manager2)
        self.assertIsInstance(manager1, ConfigurationPriorityManager)


if __name__ == "__main__":
    unittest.main()
