#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Actuator 安全集成器 - 将安全功能集成到 Actuator 中

提供安全功能的统一集成,包括:
- 安全管理器初始化
- 安全中间件集成
- 端点安全保护
- 安全配置管理
- 安全监控和审计
"""

import asyncio
from typing import Any, Dict, Optional

from fastapi import FastAPI, HTTPException, Request, Response
from loguru import logger

from ..properties import ActuatorProperties, SecurityProperties
from .manager import ProductionSecurityManager, SecurityLevel
from .middleware import SecurityExceptionHandler, SecurityMiddleware


class SecurityIntegration:
    """Actuator 安全集成器"""

    def __init__(self, properties: ActuatorProperties):
        """初始化安全集成器

        Args:
            properties: Actuator 配置属性
        """
        self.properties = properties
        self.security_config = properties.security

        # 安全组件
        self.security_manager: Optional[ProductionSecurityManager] = None
        self.security_middleware: Optional[SecurityMiddleware] = None
        self.exception_handler = SecurityExceptionHandler()

        # 集成状态
        self._initialized = False
        self._enabled = self.security_config.enabled

        logger.info(f"SecurityIntegration initialized: enabled={self._enabled}")

    async def initialize_async(self) -> None:
        """异步初始化安全集成"""
        if self._initialized:
            logger.debug("Security integration already initialized")
            return

        try:
            if not self._enabled:
                logger.info("Security is disabled, skipping initialization")
                self._initialized = True
                return

            # 获取安全级别
            security_level = self._get_security_level()

            # 创建安全管理器
            self.security_manager = ProductionSecurityManager(self.security_config, security_level)

            # 创建安全中间件
            self.security_middleware = create_security_middleware(self.security_manager)

            # 验证安全配置
            await self._validate_security_config()

            self._initialized = True
            logger.info("✅ Security integration initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize security integration: {e}")
            raise

    def integrate_with_app(self, app: FastAPI) -> None:
        """集成到 FastAPI 应用

        Args:
            app: FastAPI 应用实例
        """
        if not self._enabled or not self._initialized:
            logger.debug("Security integration disabled or not initialized")
            return

        try:
            # 添加安全中间件
            self._add_security_middleware(app)

            # 添加安全端点
            self._add_security_endpoints(app)

            # 添加异常处理器
            self._add_exception_handlers(app)

            logger.info("✅ Security integration added to FastAPI app")

        except Exception as e:
            logger.error(f"❌ Failed to integrate security with app: {e}")
            raise

    async def protect_endpoint(self, request: Request, endpoint: str) -> bool:
        """保护端点访问

        Args:
            request: HTTP 请求
            endpoint: 端点名称

        Returns:
            bool: 是否允许访问

        Raises:
            HTTPException: 访问被拒绝时抛出异常
        """
        if not self._enabled or not self.security_middleware:
            return True  # 安全未启用,允许访问

        try:
            # 检查 HTTPS 要求
            self.security_middleware.check_https_requirement(request)

            # 验证 CORS
            if not self.security_middleware.validate_cors(request):
                raise self.exception_handler.handle_access_denied(endpoint)

            # 认证请求
            token = await self.security_middleware.authenticate_request(request)

            # 授权端点访问
            if not await self.security_middleware.authorize_endpoint_access(request, endpoint, token):
                if not token:
                    raise self.exception_handler.handle_authentication_required()
                else:
                    raise self.exception_handler.handle_access_denied(endpoint)

            return True

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error protecting endpoint {endpoint}: {e}")
            raise self.exception_handler.handle_access_denied(endpoint)

    def add_security_headers(self, response: Response) -> None:
        """添加安全头部到响应

        Args:
            response: HTTP 响应
        """
        if self._enabled and self.security_middleware:
            self.security_middleware.add_security_headers(response)

    def get_security_status(self) -> Dict[str, Any]:
        """获取安全状态

        Returns:
            Dict[str, Any]: 安全状态信息
        """
        if not self._enabled:
            return {"enabled": False, "message": "Security is disabled"}

        if not self.security_manager:
            return {"enabled": True, "initialized": False, "message": "Security manager not initialized"}

        status = self.security_manager.get_security_status()
        status["integration_initialized"] = self._initialized
        return status

    async def cleanup_async(self) -> None:
        """异步清理资源"""
        try:
            if self.security_manager:
                # 清理过期令牌
                expired_count = self.security_manager.cleanup_expired_tokens()
                if expired_count > 0:
                    logger.info(f"Cleaned up {expired_count} expired tokens")

            logger.debug("Security integration cleanup completed")

        except Exception as e:
            logger.error(f"Error during security cleanup: {e}")

    def _get_security_level(self) -> SecurityLevel:
        """获取安全级别"""
        # 优先使用配置中的安全级别
        level_str = self.security_config.security_level.upper()

        try:
            return SecurityLevel(level_str.lower())
        except ValueError:
            # 如果配置无效,从环境变量获取
            logger.warning(f"Invalid security level in config: {level_str}, using environment")
            return get_security_level_from_environment()

    async def _validate_security_config(self) -> None:
        """验证安全配置"""
        warnings = []

        # 检查用户名和密码
        if self.security_config.username == "admin":
            warnings.append("Using default username 'admin' is not secure")

        if self.security_config.password == "admin":
            warnings.append("Using default password 'admin' is not secure")

        if len(self.security_config.password) < 8:
            warnings.append("Password is too short, recommend at least 8 characters")

        # 检查 HTTPS 配置
        security_level = self._get_security_level()
        if security_level in [SecurityLevel.STAGING, SecurityLevel.PRODUCTION]:
            if not self.security_config.require_https:
                warnings.append(f"HTTPS should be required in {security_level.value} environment")

        # 检查 CORS 配置
        if self.security_config.cors_enabled and "*" in self.security_config.allowed_origins:
            if security_level in [SecurityLevel.STAGING, SecurityLevel.PRODUCTION]:
                warnings.append("CORS with wildcard origins is not secure in production")

        # 记录警告
        for warning in warnings:
            logger.warning(f"Security configuration warning: {warning}")

        if warnings:
            logger.warning(f"Found {len(warnings)} security configuration warnings")

    def _add_security_middleware(self, app: FastAPI) -> None:
        """添加安全中间件"""

        @app.middleware("http")
        async def security_middleware(request: Request, call_next):
            # 检查是否为 Actuator 端点
            if not request.url.path.startswith(self.properties.base_path):
                response = await call_next(request)
                return response

            try:
                # 提取端点名称
                endpoint_path = request.url.path[len(self.properties.base_path) :].lstrip("/")
                endpoint = endpoint_path.split("/")[0] if endpoint_path else "root"

                # 保护端点
                await self.protect_endpoint(request, endpoint)

                # 继续处理请求
                response = await call_next(request)

                # 添加安全头部
                self.add_security_headers(response)

                return response

            except HTTPException as e:
                # 返回安全异常
                from fastapi.responses import JSONResponse

                return JSONResponse(status_code=e.status_code, content={"detail": e.detail}, headers=e.headers)

    def _add_security_endpoints(self, app: FastAPI) -> None:
        """添加安全相关端点"""

        @app.get(f"{self.properties.base_path}/security/status")
        async def security_status(request: Request):
            """获取安全状态"""
            await self.protect_endpoint(request, "security")
            return self.get_security_status()

        @app.post(f"{self.properties.base_path}/security/cleanup")
        async def security_cleanup(request: Request):
            """清理安全资源"""
            await self.protect_endpoint(request, "security")
            await self.cleanup_async()
            return {"message": "Security cleanup completed"}

    def _add_exception_handlers(self, app: FastAPI) -> None:
        """添加异常处理器"""

        @app.exception_handler(401)
        async def handle_unauthorized(request: Request, exc: HTTPException):
            return JSONResponse(
                status_code=401, content={"detail": "Authentication required"}, headers={"WWW-Authenticate": 'Basic realm="Actuator", Bearer'}
            )

        @app.exception_handler(403)
        async def handle_forbidden(request: Request, exc: HTTPException):
            return JSONResponse(status_code=403, content={"detail": "Access denied"})
