#!/usr/bin/env python
"""
* @author: cz
* @description: 事件ID生成器并发测试

专门测试事件ID生成器在高并发环境下的线程安全性和性能。
"""

import threading
import time
import unittest
from collections import Counter
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from miniboot.events import ApplicationEvent
from miniboot.events.base import EventIdGenerator


class TestEventIdConcurrency(unittest.TestCase):
    """事件ID生成器并发测试"""

    def setUp(self):
        """设置测试"""
        self.generator = EventIdGenerator()

    def test_high_concurrency_id_generation(self):
        """测试高并发ID生成"""
        num_threads = 20
        ids_per_thread = 500
        total_ids = num_threads * ids_per_thread

        generated_ids = []
        lock = threading.Lock()

        def generate_batch():
            """批量生成ID"""
            local_ids = []
            for _ in range(ids_per_thread):
                local_ids.append(self.generator.generate_id())

            with lock:
                generated_ids.extend(local_ids)

        # 创建并启动线程
        threads = []
        start_time = time.time()

        for _ in range(num_threads):
            thread = threading.Thread(target=generate_batch)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(generated_ids), total_ids)
        self.assertEqual(len(set(generated_ids)), total_ids)  # 所有ID都应该唯一

        print(f"生成 {total_ids} 个ID耗时: {end_time - start_time:.3f}秒")
        print(f"平均每秒生成: {total_ids / (end_time - start_time):.0f} 个ID")

    def test_thread_pool_executor_safety(self):
        """测试线程池执行器安全性"""
        num_workers = 10
        num_tasks = 1000

        def generate_single_id():
            """生成单个ID"""
            return self.generator.generate_id()

        generated_ids = []

        # 使用线程池执行器
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            # 提交任务
            futures = [executor.submit(generate_single_id) for _ in range(num_tasks)]

            # 收集结果
            for future in as_completed(futures):
                generated_ids.append(future.result())

        # 验证唯一性
        self.assertEqual(len(generated_ids), num_tasks)
        self.assertEqual(len(set(generated_ids)), num_tasks)

    def test_event_creation_concurrency(self):
        """测试事件创建并发性"""
        num_threads = 15
        events_per_thread = 200
        total_events = num_threads * events_per_thread

        created_events = []
        lock = threading.Lock()

        def create_events():
            """批量创建事件"""
            local_events = []
            for i in range(events_per_thread):
                event = ApplicationEvent(data={"index": i})
                local_events.append(event)

            with lock:
                created_events.extend(local_events)

        # 创建并启动线程
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=create_events)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(created_events), total_events)

        # 提取所有事件ID
        event_ids = [event.event_id for event in created_events]

        # 验证ID唯一性
        self.assertEqual(len(set(event_ids)), total_events)

        # 验证ID格式
        for event_id in event_ids:
            self.assertTrue(event_id.isdigit())
            self.assertEqual(len(event_id), 20)

    def test_singleton_generator_thread_safety(self):
        """测试单例生成器线程安全性"""
        num_threads = 25
        ids_per_thread = 100
        total_ids = num_threads * ids_per_thread

        generated_ids = []
        lock = threading.Lock()

        def use_singleton_generator():
            """使用单例生成器"""
            generator = EventIdGenerator()
            local_ids = []

            for _ in range(ids_per_thread):
                local_ids.append(generator.generate_id())

            with lock:
                generated_ids.extend(local_ids)

        # 创建并启动线程
        threads = []
        for _ in range(num_threads):
            thread = threading.Thread(target=use_singleton_generator)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(generated_ids), total_ids)
        self.assertEqual(len(set(generated_ids)), total_ids)

    def test_id_sequence_under_load(self):
        """测试负载下的ID序列性"""
        num_iterations = 1000
        generated_ids = []

        # 快速连续生成ID
        start_time = time.time()
        for _ in range(num_iterations):
            generated_ids.append(self.generator.generate_id())
        end_time = time.time()

        # 验证唯一性
        self.assertEqual(len(set(generated_ids)), num_iterations)

        # 分析序列性
        timestamp_groups = {}
        for event_id in generated_ids:
            timestamp = event_id[:14]
            sequence = int(event_id[14:])

            if timestamp not in timestamp_groups:
                timestamp_groups[timestamp] = []
            timestamp_groups[timestamp].append(sequence)

        # 验证每个时间戳组内的序列号是连续的
        for timestamp, sequences in timestamp_groups.items():
            sequences.sort()
            for i in range(1, len(sequences)):
                self.assertEqual(sequences[i], sequences[i - 1] + 1, f"时间戳 {timestamp} 下序列号不连续: {sequences}")

        print(f"快速生成 {num_iterations} 个ID耗时: {end_time - start_time:.3f}秒")
        print(f"时间戳组数: {len(timestamp_groups)}")

    def test_stress_test(self):
        """压力测试"""
        num_threads = 50
        duration_seconds = 2  # 运行2秒

        generated_ids = []
        lock = threading.Lock()
        stop_flag = threading.Event()

        def stress_generate():
            """压力生成ID"""
            local_ids = []
            while not stop_flag.is_set():
                local_ids.append(self.generator.generate_id())
                # 短暂休眠以模拟实际使用场景
                time.sleep(0.001)

            with lock:
                generated_ids.extend(local_ids)

        # 启动压力测试线程
        threads = []
        start_time = time.time()

        for _ in range(num_threads):
            thread = threading.Thread(target=stress_generate)
            threads.append(thread)
            thread.start()

        # 运行指定时间
        time.sleep(duration_seconds)
        stop_flag.set()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        end_time = time.time()
        actual_duration = end_time - start_time

        # 验证结果
        total_generated = len(generated_ids)
        unique_count = len(set(generated_ids))

        self.assertEqual(total_generated, unique_count)  # 所有ID都应该唯一
        self.assertGreater(total_generated, 0)  # 应该生成了一些ID

        print("压力测试结果:")
        print(f"  运行时间: {actual_duration:.3f}秒")
        print(f"  线程数: {num_threads}")
        print(f"  生成ID总数: {total_generated}")
        print(f"  平均每秒: {total_generated / actual_duration:.0f} 个ID")
        print(f"  平均每线程每秒: {total_generated / actual_duration / num_threads:.1f} 个ID")

    def test_id_distribution(self):
        """测试ID分布特性"""
        num_ids = 10000
        generated_ids = []

        # 生成大量ID
        for _ in range(num_ids):
            generated_ids.append(self.generator.generate_id())

        # 分析时间戳分布
        timestamp_counter = Counter()
        sequence_counter = Counter()

        for event_id in generated_ids:
            timestamp = event_id[:14]
            sequence = event_id[14:]

            timestamp_counter[timestamp] += 1
            sequence_counter[sequence] += 1

        # 验证分布特性
        self.assertGreater(len(timestamp_counter), 0)
        self.assertEqual(len(sequence_counter), num_ids)  # 每个序列号都应该唯一

        print("ID分布分析:")
        print(f"  总ID数: {num_ids}")
        print(f"  时间戳种类: {len(timestamp_counter)}")
        print(f"  最大单秒ID数: {max(timestamp_counter.values())}")
        print(f"  平均单秒ID数: {sum(timestamp_counter.values()) / len(timestamp_counter):.1f}")


if __name__ == "__main__":
    unittest.main(verbosity=2)
