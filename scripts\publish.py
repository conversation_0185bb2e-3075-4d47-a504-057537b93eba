#!/usr/bin/env python3
"""
Mini-Boot 框架发布脚本

基于 docs/19.deployment-release.md 实现自动化发布流程
"""

import argparse
import os
import re
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Any, Optional

try:
    import yaml
except ImportError:
    print("❌ 需要安装 PyYAML: uv add pyyaml")
    sys.exit(1)


class Publisher:
    """发布管理器"""

    def __init__(self, dry_run: bool = False, skip_tests: bool = False, config_file: Optional[str] = None):
        self.dry_run = dry_run
        self.skip_tests = skip_tests
        self.project_root = Path(__file__).parent.parent
        self.dist_dir = self.project_root / "dist"
        self.config = self.load_config(config_file)

    def load_config(self, config_file: Optional[str] = None) -> dict[str, Any]:
        """加载配置文件"""
        if config_file is None:
            config_file = "scripts/publish.yml"

        config_path = self.project_root / config_file

        if not config_path.exists():
            self.log(f"⚠️  配置文件 {config_path} 不存在,使用默认配置", "WARN")
            return self.get_default_config()

        try:
            with Path(config_path).open(encoding="utf-8") as f:
                config = yaml.safe_load(f)
            self.log(f"✅ 已加载配置文件: {config_path}")
            return self.expand_env_vars(config)
        except (FileNotFoundError, yaml.YAMLError, OSError) as e:
            self.log(f"❌ 加载配置文件失败: {e}", "ERROR")
            return self.get_default_config()

    def get_default_config(self) -> dict[str, Any]:
        """获取默认配置"""
        return {
            "repository": {
                "name": "nexus-private",
                "url": "http://localhost:8081/repository/pypi-private/",
                "username": "${NEXUS_USERNAME}",
                "password": "${NEXUS_PASSWORD}",
            },
            "checks": {"quality_check": True, "run_tests": True, "git_status": True},
            "build": {"clean_dist": True, "auto_version": False},
        }

    def expand_env_vars(self, config: dict[str, Any]) -> dict[str, Any]:
        """展开环境变量"""

        def expand_value(value):
            if isinstance(value, str):
                # 替换 ${VAR_NAME} 格式的环境变量
                pattern = r"\$\{([^}]+)\}"
                matches = re.findall(pattern, value)
                for var_name in matches:
                    env_value = os.getenv(var_name, f"${{{var_name}}}")
                    value = value.replace(f"${{{var_name}}}", env_value)
                return value
            if isinstance(value, dict):
                return {k: expand_value(v) for k, v in value.items()}
            if isinstance(value, list):
                return [expand_value(item) for item in value]
            return value

        return expand_value(config)

    def log(self, message: str, level: str = "INFO") -> None:
        """输出日志"""
        print(f"[{level}] {message}")

    def run_command(self, cmd: list, check: bool = True) -> subprocess.CompletedProcess:
        """执行命令"""
        self.log(f"执行命令: {' '.join(cmd)}")
        if self.dry_run:
            self.log("DRY RUN: 跳过命令执行")
            return subprocess.CompletedProcess(cmd, 0)
        return subprocess.run(cmd, check=check, cwd=self.project_root)

    def check_environment(self) -> bool:
        """检查发布环境"""
        self.log("检查发布环境...")

        # 检查必要工具
        try:
            subprocess.run(["uv", "--version"], check=True, capture_output=True)
            self.log("✅ uv 工具可用")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("❌ uv 工具不可用", "ERROR")
            return False

        # 检查项目根目录
        if not (self.project_root / "pyproject.toml").exists():
            self.log("❌ 未找到 pyproject.toml 文件", "ERROR")
            return False

        self.log("✅ 环境检查通过")
        return True

    def run_quality_checks(self) -> bool:
        """运行代码质量检查"""
        if self.skip_tests:
            self.log("跳过代码质量检查")
            return True

        self.log("运行代码质量检查...")

        try:
            # 运行测试
            self.log("运行测试套件...")
            self.run_command(["uv", "run", "tests/test_runner.py"])
            self.log("✅ 测试通过")

            # 运行代码质量检查
            self.log("运行代码质量检查...")
            self.run_command(["uv", "run", "tests/test_runner.py", "--quality"])
            self.log("✅ 代码质量检查通过")

        except subprocess.CalledProcessError as e:
            self.log(f"❌ 质量检查失败: {e}", "ERROR")
            return False

        return True

    def format_and_check_code(self) -> bool:
        """格式化代码并进行质量检查"""
        if self.skip_tests:
            self.log("跳过代码格式化和质量检查")
            return True

        self.log("格式化代码并进行质量检查...")

        try:
            # 1. 格式化代码
            self.log("格式化代码...")
            self.run_command(["uv", "run", "ruff", "format", "."])
            self.log("✅ 代码格式化完成")

            # 2. 修复可自动修复的问题
            self.log("修复代码问题...")
            self.run_command(["uv", "run", "ruff", "check", "--fix", "."], check=False)
            self.log("✅ 代码问题修复完成")

            # 3. 运行质量检查
            return self.run_quality_checks()

        except subprocess.CalledProcessError as e:
            self.log(f"❌ 代码格式化失败: {e}", "ERROR")
            return False

    def auto_update_version(self) -> bool:
        """自动更新版本号"""
        self.log("自动更新版本号...")

        try:
            # 获取版本递增类型
            version_config = self.config.get("version", {})
            bump_type = version_config.get("default_bump", "patch")

            # 调用版本管理脚本
            cmd = ["uv", "run", "scripts/version.py", "--bump", bump_type]
            self.run_command(cmd)
            self.log(f"✅ 版本号已自动递增({bump_type})")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 版本号更新失败: {e}", "ERROR")
            return False

    def clean_dist(self) -> None:
        """清理构建目录"""
        self.log("清理构建目录...")
        if self.dist_dir.exists() and not self.dry_run:
            shutil.rmtree(self.dist_dir)
        self.log("✅ 构建目录已清理")

    def build_package(self) -> bool:
        """构建包"""
        self.log("构建包...")

        try:
            self.run_command(["uv", "build"])
            self.log("✅ 包构建成功")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 包构建失败: {e}", "ERROR")
            return False

    def publish_to_private_repo(self) -> bool:
        """发布到私有仓库"""
        self.log("发布到私有仓库...")

        # 从配置文件获取仓库信息
        repo_config = self.config.get("repository", {})
        nexus_url = repo_config.get("url", "http://localhost:8081/repository/pypi-private/")
        nexus_username = repo_config.get("username")
        nexus_password = repo_config.get("password")

        # 检查是否还有未展开的环境变量
        if not nexus_username or "${" in str(nexus_username):
            self.log("❌ 仓库用户名未配置或环境变量未设置", "ERROR")
            return False

        if not nexus_password or "${" in str(nexus_password):
            self.log("❌ 仓库密码未配置或环境变量未设置", "ERROR")
            return False

        self.log(f"发布到仓库: {nexus_url}")

        try:
            # 使用 uv publish 发布到私有仓库
            cmd = [
                "uv",
                "publish",
                "--publish-url",
                nexus_url,
                "--username",
                nexus_username,
                "--password",
                nexus_password,
            ]
            self.run_command(cmd)
            self.log("✅ 发布成功")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"❌ 发布失败: {e}", "ERROR")
            return False

    def create_git_tag(self) -> bool:
        """创建 Git 标签"""
        self.log("创建 Git 标签...")

        try:
            # 获取当前版本号
            with (self.project_root / "pyproject.toml").open(encoding="utf-8") as f:
                content = f.read()

            match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)
            if not match:
                self.log("❌ 无法获取版本号", "ERROR")
                return False

            version = match.group(1)
            version_config = self.config.get("version", {})
            tag_prefix = version_config.get("tag_prefix", "v")
            tag_name = f"{tag_prefix}{version}"
            release_notes = version_config.get("release_notes", f"Release {version}")

            # 检查标签是否已存在
            check_cmd = ["git", "tag", "-l", tag_name]
            result = subprocess.run(check_cmd, capture_output=True, text=True, cwd=self.project_root, check=False)
            if result.stdout.strip():
                self.log(f"⚠️  标签 {tag_name} 已存在,跳过创建", "WARN")
                return True

            # 创建标签
            create_cmd = ["git", "tag", "-a", tag_name, "-m", release_notes]
            self.run_command(create_cmd)

            # 推送标签到远程仓库
            push_cmd = ["git", "push", "origin", tag_name]
            self.run_command(push_cmd)

            self.log(f"✅ 已创建并推送 Git 标签: {tag_name}")
            return True

        except subprocess.CalledProcessError as e:
            self.log(f"❌ Git 标签创建失败: {e}", "ERROR")
            return False
        except (OSError, ValueError) as e:
            self.log(f"❌ 标签创建过程中出错: {e}", "ERROR")
            return False

    def publish(self) -> bool:
        """执行完整发布流程"""
        self.log("开始发布流程...")

        # 1. 环境检查
        if not self.check_environment():
            return False

        # 2. 代码格式化和质量检查
        if not self.format_and_check_code():
            return False

        # 3. 自动更新版本号(如果配置启用)
        if self.config.get("build", {}).get("auto_version", False) and not self.auto_update_version():
            return False

        # 4. 清理构建目录
        self.clean_dist()

        # 5. 构建包
        if not self.build_package():
            return False

        # 6. 发布到私有仓库
        if not self.publish_to_private_repo():
            return False

        # 7. 创建 Git 标签(如果配置启用)
        if self.config.get("version", {}).get("create_tag", False) and not self.create_git_tag():
            # 标签创建失败不影响发布成功
            self.log("⚠️  Git 标签创建失败,但发布已完成", "WARN")

        self.log("🎉 发布完成!")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Mini-Boot 框架发布脚本")
    parser.add_argument("--dry-run", action="store_true", help="试运行,不执行实际操作")
    parser.add_argument("--skip-tests", action="store_true", help="跳过测试(不推荐)")
    parser.add_argument("--config", help="指定配置文件路径(默认: scripts/publish.yml)")

    args = parser.parse_args()

    publisher = Publisher(dry_run=args.dry_run, skip_tests=args.skip_tests, config_file=args.config)

    try:
        success = publisher.publish()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        publisher.log("发布被用户中断", "WARN")
        sys.exit(1)
    except (OSError, RuntimeError) as e:
        publisher.log(f"发布过程中出现未预期的错误: {e}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()
