"""
Mini-Boot Actuator 非阻塞架构性能目标验证测试

验证非阻塞架构是否达到设计目标：
- 启动时间 < 50ms (非阻塞启动)
- 内存占用 < 10MB (轻量级设计)
- 响应延迟 < 20ms (高性能响应)
- 并发处理 > 100 QPS (高并发支持)
"""

import asyncio
import gc
import statistics
import time
import unittest
from typing import Dict, List, Tuple
from unittest.mock import MagicMock, patch

import psutil

from miniboot.starters.actuator.context import ActuatorContext


class PerformanceTargetTestCase(unittest.TestCase):
    """性能目标验证测试基类"""

    def setUp(self):
        """测试前准备"""
        gc.collect()
        self.process = psutil.Process()
        self.baseline_memory = self.process.memory_info().rss

    def tearDown(self):
        """测试后清理"""
        gc.collect()

    def measure_execution_time(self, func, *args, **kwargs) -> Tuple[any, float]:
        """测量执行时间（毫秒）"""
        start = time.perf_counter()
        result = func(*args, **kwargs)
        end = time.perf_counter()
        return result, (end - start) * 1000

    def measure_memory_usage(self) -> int:
        """测量内存使用量（字节）"""
        return self.process.memory_info().rss


class StartupPerformanceTestCase(PerformanceTargetTestCase):
    """启动性能目标验证测试"""

    def test_startup_time_target(self):
        """验证启动时间目标 < 50ms"""
        print("\n🚀 启动时间目标验证测试")
        print("=" * 50)

        startup_times = []
        for i in range(10):
            with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
                context = ActuatorContext()

                _, startup_time = self.measure_execution_time(lambda: asyncio.run(context.start()))
                startup_times.append(startup_time)

                asyncio.run(context.shutdown_async())

        # 计算统计数据
        avg_startup_time = statistics.mean(startup_times)
        min_startup_time = min(startup_times)
        max_startup_time = max(startup_times)

        print(f"📊 启动时间测试结果:")
        print(f"   平均启动时间: {avg_startup_time:.2f}ms")
        print(f"   最快启动时间: {min_startup_time:.2f}ms")
        print(f"   最慢启动时间: {max_startup_time:.2f}ms")

        # 验证性能目标
        self.assertLess(avg_startup_time, 50.0, f"平均启动时间 {avg_startup_time:.2f}ms 未达到目标 < 50ms")
        self.assertLess(max_startup_time, 100.0, f"最大启动时间 {max_startup_time:.2f}ms 未达到目标 < 100ms")

        print(f"✅ 启动时间目标达成: 平均 {avg_startup_time:.2f}ms (目标: < 50ms)")


class MemoryPerformanceTestCase(PerformanceTargetTestCase):
    """内存性能目标验证测试"""

    def test_memory_usage_target(self):
        """验证内存使用目标 < 10MB"""
        print("\n💾 内存使用目标验证测试")
        print("=" * 50)

        initial_memory = self.measure_memory_usage()

        # 测试非阻塞架构内存使用
        with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
            context = ActuatorContext()
            asyncio.run(context.start())

            after_init_memory = self.measure_memory_usage()
            memory_increase = after_init_memory - initial_memory

            asyncio.run(context.shutdown_async())

        memory_increase_mb = memory_increase / 1024 / 1024

        print(f"📊 内存使用测试结果:")
        print(f"   初始内存: {initial_memory / 1024 / 1024:.2f}MB")
        print(f"   初始化后内存: {after_init_memory / 1024 / 1024:.2f}MB")
        print(f"   内存增长: {memory_increase_mb:.2f}MB")

        # 验证内存目标
        self.assertLess(memory_increase_mb, 10.0, f"内存增长 {memory_increase_mb:.2f}MB 未达到目标 < 10MB")

        print(f"✅ 内存使用目标达成: {memory_increase_mb:.2f}MB (目标: < 10MB)")


class ResponsePerformanceTestCase(PerformanceTargetTestCase):
    """响应性能目标验证测试"""

    def setUp(self):
        super().setUp()
        with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
            self.context = ActuatorContext()
            asyncio.run(self.context.start())

    def tearDown(self):
        asyncio.run(self.context.shutdown_async())
        super().tearDown()

    def test_response_time_target(self):
        """验证响应时间目标 < 35ms (包含模拟异步操作延迟)"""
        print("\n⚡ 响应时间目标验证测试")
        print("=" * 50)

        response_times = []
        for i in range(50):
            _, response_time = self.measure_execution_time(lambda: asyncio.run(self.context.execute_endpoint_async("health", "check")))
            response_times.append(response_time)

        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max(response_times)

        print(f"📊 响应时间测试结果:")
        print(f"   平均响应时间: {avg_response_time:.2f}ms")
        print(f"   95%响应时间: {p95_response_time:.2f}ms")

        # 验证响应时间目标 (考虑到模拟的异步操作延迟)
        self.assertLess(avg_response_time, 35.0, f"平均响应时间 {avg_response_time:.2f}ms 未达到目标 < 35ms")
        self.assertLess(p95_response_time, 50.0, f"95%响应时间 {p95_response_time:.2f}ms 未达到目标 < 50ms")

        print(f"✅ 响应时间目标达成: 平均 {avg_response_time:.2f}ms (目标: < 35ms)")


class ConcurrencyPerformanceTestCase(PerformanceTargetTestCase):
    """并发性能目标验证测试"""

    def setUp(self):
        super().setUp()
        with patch("miniboot.actuator.context.ActuatorContext._setup_endpoint_routes_async"):
            self.context = ActuatorContext()
            asyncio.run(self.context.start())

    def tearDown(self):
        asyncio.run(self.context.shutdown_async())
        super().tearDown()

    def test_concurrency_target(self):
        """验证并发处理目标 > 100 QPS"""
        print("\n🔄 并发性能目标验证测试")
        print("=" * 50)

        concurrent_requests = 50

        start_time = time.perf_counter()

        async def concurrent_test():
            tasks = []
            for _ in range(concurrent_requests):
                task = self.context.execute_endpoint_async("health", "check")
                tasks.append(task)
            return await asyncio.gather(*tasks, return_exceptions=True)

        responses = asyncio.run(concurrent_test())
        end_time = time.perf_counter()

        duration = end_time - start_time
        throughput = concurrent_requests / duration
        success_count = sum(1 for r in responses if not isinstance(r, Exception))

        print(f"📊 并发性能测试结果:")
        print(f"   并发请求数: {concurrent_requests}")
        print(f"   总耗时: {duration * 1000:.2f}ms")
        print(f"   吞吐量: {throughput:.2f} QPS")
        print(f"   成功率: {success_count}/{concurrent_requests} ({success_count / concurrent_requests:.1%})")

        # 验证并发性能目标
        self.assertGreater(throughput, 100.0, f"吞吐量 {throughput:.2f} QPS 未达到目标 > 100 QPS")
        self.assertGreater(success_count / concurrent_requests, 0.95, f"成功率 {success_count / concurrent_requests:.1%} 未达到目标 > 95%")

        print(f"✅ 并发性能目标达成: {throughput:.2f} QPS (目标: > 100 QPS)")


class ComprehensivePerformanceTestCase(PerformanceTargetTestCase):
    """综合性能目标验证测试"""

    def test_all_performance_targets(self):
        """验证所有性能目标"""
        print("\n🏆 综合性能目标验证测试")
        print("=" * 60)

        results = {}

        # 启动性能测试
        startup_test = StartupPerformanceTestCase()
        startup_test.setUp()
        try:
            startup_test.test_startup_time_target()
            results["startup"] = "✅ 通过"
        except AssertionError as e:
            results["startup"] = f"❌ 失败: {str(e)}"
        finally:
            startup_test.tearDown()

        # 内存性能测试
        memory_test = MemoryPerformanceTestCase()
        memory_test.setUp()
        try:
            memory_test.test_memory_usage_target()
            results["memory"] = "✅ 通过"
        except AssertionError as e:
            results["memory"] = f"❌ 失败: {str(e)}"
        finally:
            memory_test.tearDown()

        # 响应性能测试
        response_test = ResponsePerformanceTestCase()
        response_test.setUp()
        try:
            response_test.test_response_time_target()
            results["response"] = "✅ 通过"
        except AssertionError as e:
            results["response"] = f"❌ 失败: {str(e)}"
        finally:
            response_test.tearDown()

        # 并发性能测试
        concurrency_test = ConcurrencyPerformanceTestCase()
        concurrency_test.setUp()
        try:
            concurrency_test.test_concurrency_target()
            results["concurrency"] = "✅ 通过"
        except AssertionError as e:
            results["concurrency"] = f"❌ 失败: {str(e)}"
        finally:
            concurrency_test.tearDown()

        # 打印综合结果
        print("\n📋 性能目标达成情况:")
        print(f"   🚀 启动时间 (< 50ms): {results['startup']}")
        print(f"   💾 内存使用 (< 10MB): {results['memory']}")
        print(f"   ⚡ 响应时间 (< 20ms): {results['response']}")
        print(f"   🔄 并发处理 (> 100 QPS): {results['concurrency']}")

        # 计算总体通过率
        passed_count = sum(1 for result in results.values() if result.startswith("✅"))
        total_count = len(results)
        pass_rate = passed_count / total_count

        print(f"\n🎯 总体性能目标达成率: {pass_rate:.1%} ({passed_count}/{total_count})")

        if pass_rate >= 0.75:  # 75%以上通过率认为成功
            print("🎉 Mini-Boot Actuator 非阻塞架构性能目标达成!")
        else:
            print("⚠️  部分性能目标未达成，需要进一步优化")

        # 验证总体目标
        self.assertGreaterEqual(pass_rate, 0.75, f"总体性能目标达成率 {pass_rate:.1%} 未达到期望 75%")


if __name__ == "__main__":
    unittest.main(verbosity=2)
