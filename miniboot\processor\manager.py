#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 处理器管理器 - 实现Bean后置处理器的统一管理

处理器管理器负责Bean后置处理器的生命周期管理、性能监控、
配置管理和统一协调,提供高级的处理器管理功能.
"""

import threading
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Optional

from loguru import logger

from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import BeanPostProcessor
from .registry import BeanPostProcessorRegistry


@dataclass
class ProcessorMetrics:
    """处理器执行指标"""

    processor_name: str
    total_executions: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    max_execution_time: float = 0.0
    min_execution_time: float = float("inf")
    error_count: int = 0
    last_execution_time: Optional[datetime] = None
    last_error_time: Optional[datetime] = None
    last_error_message: Optional[str] = None


@dataclass
class ProcessorConfig:
    """处理器配置"""

    enabled: bool = True
    timeout_seconds: Optional[float] = None
    retry_count: int = 0
    retry_delay: float = 0.1
    error_threshold: int = 10
    circuit_breaker_enabled: bool = False
    circuit_breaker_timeout: float = 60.0
    custom_properties: dict[str, Any] = field(default_factory=dict)


class ProcessorState(Enum):
    """处理器状态枚举

    定义Bean后置处理器的各种运行状态,提供类型安全的状态管理.
    """

    ACTIVE = "ACTIVE"
    """处理器处于活动状态,正常处理Bean"""

    DISABLED = "DISABLED"
    """处理器已被禁用,不参与Bean处理"""

    ERROR = "ERROR"
    """处理器处于错误状态,暂时停止处理"""

    CIRCUIT_OPEN = "CIRCUIT_OPEN"
    """熔断器已打开,处理器暂时不可用"""

    def is_available(self) -> bool:
        """检查处理器是否可用于处理Bean

        Returns:
            True如果处理器可用,False否则
        """
        return self == ProcessorState.ACTIVE

    def can_transition_to(self, target_state: "ProcessorState") -> bool:
        """检查是否可以转换到目标状态

        Args:
            target_state: 目标状态

        Returns:
            True如果可以转换,False否则
        """
        # 定义允许的状态转换
        allowed_transitions = {
            ProcessorState.ACTIVE: {ProcessorState.DISABLED, ProcessorState.ERROR, ProcessorState.CIRCUIT_OPEN},
            ProcessorState.DISABLED: {ProcessorState.ACTIVE},
            ProcessorState.ERROR: {ProcessorState.ACTIVE, ProcessorState.DISABLED},
            ProcessorState.CIRCUIT_OPEN: {ProcessorState.ACTIVE, ProcessorState.DISABLED},
        }

        return target_state in allowed_transitions.get(self, set())


class BeanPostProcessorManager:
    """
    Bean后置处理器管理器

    提供Bean后置处理器的高级管理功能,包括:
    - 处理器生命周期管理:启用/禁用、动态注册/注销
    - 性能监控:执行时间、错误率、吞吐量统计
    - 配置管理:处理器配置、超时控制、重试机制
    - 熔断保护:错误阈值控制、熔断器模式
    - 批量操作:批量注册、批量配置、批量监控

    Example:
        manager = BeanPostProcessorManager()

        # 注册处理器
        manager.register_processor(MyProcessor(), ProcessorConfig(timeout_seconds=5.0))

        # 获取性能指标
        metrics = manager.get_processor_metrics("MyProcessor")

        # 禁用处理器
        manager.disable_processor("MyProcessor")
    """

    def __init__(self, registry: Optional[BeanPostProcessorRegistry] = None):
        """
        初始化处理器管理器

        Args:
            registry: Bean后置处理器注册表,如果为None则创建新的注册表
        """
        super().__init__()  # 初始化异常处理混入
        self._registry = registry or BeanPostProcessorRegistry()
        self._processor_configs: dict[str, ProcessorConfig] = {}
        self._processor_metrics: dict[str, ProcessorMetrics] = {}
        self._processor_states: dict[str, ProcessorState] = {}
        self._circuit_breaker_states: dict[str, datetime] = {}
        self._lock = threading.RLock()

        # 性能监控配置
        self._monitoring_enabled = True
        self._metrics_retention_days = 7

    def register(self, processor: BeanPostProcessor, config: Optional[ProcessorConfig] = None) -> None:
        """
        注册Bean后置处理器

        Args:
            processor: 要注册的处理器实例
            config: 处理器配置,如果为None则使用默认配置

        Raises:
            ValueError: 如果处理器为None或已经注册
        """
        if processor is None:
            raise ValueError("处理器不能为None")

        processor_name = processor.__class__.__name__

        with self._lock:
            # 注册到注册表
            self._registry.register(processor)

            # 设置配置
            self._processor_configs[processor_name] = config or ProcessorConfig()

            # 初始化指标
            self._processor_metrics[processor_name] = ProcessorMetrics(processor_name)

            # 设置状态
            self._processor_states[processor_name] = ProcessorState.ACTIVE

            logger.info(f"Registered processor: {processor_name}")

    def unregister(self, processor_type: type[BeanPostProcessor]) -> bool:
        """
        注销指定类型的处理器

        Args:
            processor_type: 要注销的处理器类型

        Returns:
            True如果成功注销,False如果处理器不存在
        """
        processor_name = processor_type.__name__

        with self._lock:
            # 从注册表中注销
            success = self._registry.unregister(processor_type)

            if success:
                # 清理配置和指标
                self._processor_configs.pop(processor_name, None)
                self._processor_metrics.pop(processor_name, None)
                self._processor_states.pop(processor_name, None)
                self._circuit_breaker_states.pop(processor_name, None)

                logger.info(f"Unregistered processor: {processor_name}")

            return success

    def enable(self, processor_name: str) -> bool:
        """
        启用指定的处理器

        Args:
            processor_name: 处理器名称

        Returns:
            True如果成功启用,False如果处理器不存在
        """
        with self._lock:
            if processor_name in self._processor_configs:
                self._processor_configs[processor_name].enabled = True
                self._processor_states[processor_name] = ProcessorState.ACTIVE
                logger.info(f"Enabled processor: {processor_name}")
                return True
            return False

    def disable(self, processor_name: str) -> bool:
        """
        禁用指定的处理器

        Args:
            processor_name: 处理器名称

        Returns:
            True如果成功禁用,False如果处理器不存在
        """
        with self._lock:
            if processor_name in self._processor_configs:
                self._processor_configs[processor_name].enabled = False
                self._processor_states[processor_name] = ProcessorState.DISABLED
                logger.info(f"Disabled processor: {processor_name}")
                return True
            return False

    def apply_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        应用所有启用的初始化前处理器

        Args:
            bean: 要处理的Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        if bean is None:
            return bean

        with self._lock:
            processors = self._registry.get_processors()
            current_bean = bean

            for processor in processors:
                processor_name = processor.__class__.__name__

                # 检查处理器是否启用和支持
                if not self._is_processor_available(processor_name, current_bean, bean_name):
                    continue

                # 执行处理器
                current_bean = self._execute_processor(processor, processor.pre_process, current_bean, bean_name, "before_initialization")

                if current_bean is None:
                    break

            return current_bean

    def apply_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        应用所有启用的初始化后处理器

        Args:
            bean: 要处理的Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        if bean is None:
            return bean

        with self._lock:
            processors = self._registry.get_processors()
            current_bean = bean

            for processor in processors:
                processor_name = processor.__class__.__name__

                # 检查处理器是否启用和支持
                if not self._is_processor_available(processor_name, current_bean, bean_name):
                    continue

                # 执行处理器
                current_bean = self._execute_processor(processor, processor.post_process, current_bean, bean_name, "after_initialization")

                if current_bean is None:
                    break

            return current_bean

    def metrics(self, processor_name: str) -> Optional[ProcessorMetrics]:
        """
        获取处理器的执行指标

        Args:
            processor_name: 处理器名称

        Returns:
            处理器指标,如果处理器不存在返回None
        """
        with self._lock:
            return self._processor_metrics.get(processor_name)

    def get_all_processor_metrics(self) -> dict[str, ProcessorMetrics]:
        """
        获取所有处理器的执行指标

        Returns:
            处理器指标字典
        """
        with self._lock:
            return self._processor_metrics.copy()

    def config(self, processor_name: str) -> Optional[ProcessorConfig]:
        """
        获取处理器配置

        Args:
            processor_name: 处理器名称

        Returns:
            处理器配置,如果处理器不存在返回None
        """
        with self._lock:
            return self._processor_configs.get(processor_name)

    def update_processor_config(self, processor_name: str, config: ProcessorConfig) -> bool:
        """
        更新处理器配置

        Args:
            processor_name: 处理器名称
            config: 新的配置

        Returns:
            True如果成功更新,False如果处理器不存在
        """
        with self._lock:
            if processor_name in self._processor_configs:
                self._processor_configs[processor_name] = config
                logger.info(f"Updated config for processor: {processor_name}")
                return True
            return False

    def get_processor_state(self, processor_name: str) -> Optional[ProcessorState]:
        """
        获取处理器状态

        Args:
            processor_name: 处理器名称

        Returns:
            处理器状态,如果处理器不存在返回None
        """
        with self._lock:
            return self._processor_states.get(processor_name)

    def get_processors_by_state(self, state: ProcessorState) -> list[str]:
        """
        获取指定状态的处理器列表

        Args:
            state: 处理器状态

        Returns:
            处理器名称列表
        """
        with self._lock:
            return [name for name, s in self._processor_states.items() if s == state]

    def reset_processor_metrics(self, processor_name: str) -> bool:
        """
        重置处理器指标

        Args:
            processor_name: 处理器名称

        Returns:
            True如果成功重置,False如果处理器不存在
        """
        with self._lock:
            if processor_name in self._processor_metrics:
                self._processor_metrics[processor_name] = ProcessorMetrics(processor_name)
                logger.info(f"Reset metrics for processor: {processor_name}")
                return True
            return False

    def get_registry(self) -> BeanPostProcessorRegistry:
        """
        获取底层的处理器注册表

        Returns:
            处理器注册表实例
        """
        return self._registry

    def _is_processor_available(self, processor_name: str, _bean: Any, _bean_name: str) -> bool:
        """
        检查处理器是否可用

        Args:
            processor_name: 处理器名称
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True如果处理器可用
        """
        # 检查配置是否启用
        config = self._processor_configs.get(processor_name)
        if not config or not config.enabled:
            return False

        # 检查状态
        state = self._processor_states.get(processor_name)
        if state in [ProcessorState.DISABLED, ProcessorState.ERROR]:
            return False

        # 检查熔断器
        if state == ProcessorState.CIRCUIT_OPEN:
            circuit_time = self._circuit_breaker_states.get(processor_name)
            if circuit_time and datetime.now() - circuit_time < timedelta(seconds=config.circuit_breaker_timeout):
                return False
            else:
                # 熔断器超时,重置状态
                self._processor_states[processor_name] = ProcessorState.ACTIVE
                self._circuit_breaker_states.pop(processor_name, None)

        return True

    def _execute_processor(self, processor: BeanPostProcessor, method: Callable, bean: Any, bean_name: str, phase: str) -> Any:
        """
        执行处理器方法

        Args:
            processor: 处理器实例
            method: 要执行的方法
            bean: Bean实例
            bean_name: Bean名称
            phase: 执行阶段

        Returns:
            处理后的Bean实例
        """
        processor_name = processor.__class__.__name__
        config = self._processor_configs.get(processor_name, ProcessorConfig())

        start_time = time.time()

        try:
            # 检查处理器是否支持此Bean
            if not processor.supports(bean, bean_name):
                return bean

            # 执行处理器方法
            result = method(bean, bean_name)

            # 更新成功指标
            execution_time = time.time() - start_time
            self._update_success_metrics(processor_name, execution_time)

            return result if result is not None else bean

        except Exception as e:
            # 更新错误指标
            execution_time = time.time() - start_time
            self._update_error_metrics(processor_name, execution_time, str(e))

            # 检查是否需要触发熔断器
            self._check_circuit_breaker(processor_name, config)

            # 使用统一异常处理
            processing_error = BeanProcessingError(
                f"处理器 {processor_name} 在 {phase} 阶段执行失败", bean_name=bean_name, processor_name=processor_name, cause=e
            )

            # 异步处理异常(记录和统计)
            import asyncio

            try:
                loop = asyncio.get_event_loop()
                loop.create_task(
                    self.handle_exception(
                        processing_error,
                        f"process_bean_{phase}",
                        additional_data={"bean_name": bean_name, "processor_name": processor_name, "phase": phase, "execution_time": execution_time},
                    )
                )
            except RuntimeError:
                # 没有事件循环,直接记录
                logger.error(f"Processor {processor_name} failed in {phase} phase: {e}")

            # 重新抛出异常
            raise processing_error from e

    def _update_success_metrics(self, processor_name: str, execution_time: float) -> None:
        """更新成功执行指标"""
        metrics = self._processor_metrics.get(processor_name)
        if metrics:
            metrics.total_executions += 1
            metrics.total_execution_time += execution_time
            metrics.average_execution_time = metrics.total_execution_time / metrics.total_executions
            metrics.max_execution_time = max(metrics.max_execution_time, execution_time)
            metrics.min_execution_time = min(metrics.min_execution_time, execution_time)
            metrics.last_execution_time = datetime.now()

    def _update_error_metrics(self, processor_name: str, execution_time: float, error_message: str) -> None:
        """更新错误执行指标"""
        metrics = self._processor_metrics.get(processor_name)
        if metrics:
            metrics.total_executions += 1
            metrics.error_count += 1
            metrics.total_execution_time += execution_time
            metrics.average_execution_time = metrics.total_execution_time / metrics.total_executions
            metrics.last_execution_time = datetime.now()
            metrics.last_error_time = datetime.now()
            metrics.last_error_message = error_message

    def _check_circuit_breaker(self, processor_name: str, config: ProcessorConfig) -> None:
        """检查是否需要触发熔断器"""
        if not config.circuit_breaker_enabled:
            return

        metrics = self._processor_metrics.get(processor_name)
        if metrics and metrics.error_count >= config.error_threshold:
            self._processor_states[processor_name] = ProcessorState.CIRCUIT_OPEN
            self._circuit_breaker_states[processor_name] = datetime.now()
            logger.warning(f"Circuit breaker opened for processor: {processor_name}")

    def get_processor_count(self) -> int:
        """
        获取已注册的处理器数量

        Returns:
            处理器数量
        """
        return self._registry.get_processor_count()

    def is_monitoring_enabled(self) -> bool:
        """
        检查是否启用了性能监控

        Returns:
            True如果启用了监控
        """
        return self._monitoring_enabled

    def set_monitoring_enabled(self, enabled: bool) -> None:
        """
        设置性能监控开关

        Args:
            enabled: 是否启用监控
        """
        self._monitoring_enabled = enabled
        logger.info(f"Processor monitoring {'enabled' if enabled else 'disabled'}")
