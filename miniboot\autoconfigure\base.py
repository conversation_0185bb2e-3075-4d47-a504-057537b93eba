#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置基类模块

包含：
- AutoConfiguration: 自动配置基类
- StarterAutoConfiguration: Starter自动配置基类
"""

import inspect
from abc import ABC, abstractmethod
from typing import Any, Optional

from loguru import logger

from miniboot.context import ApplicationContext

from ..errors.domains.autoconfigure import \
    ConditionEvaluationError as ConditionEvaluationException
from .conditions import ConditionEvaluator, get_conditions
from .metadata import AutoConfigurationMetadata
from .strategy import ConditionEvaluationChain


class AutoConfiguration(ABC):
    """自动配置基类

    提供通用的自动配置功能,支持条件化装配和优先级控制.
    参考Spring Boot的AutoConfiguration设计.
    """

    def __init__(self):
        """初始化自动配置"""
        self._condition_chain = ConditionEvaluationChain()

    @abstractmethod
    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据

        Returns:
            AutoConfigurationMetadata: 配置元数据
        """
        pass

    def should_configure(self, context: ApplicationContext) -> bool:
        """判断是否应该执行配置

        Args:
            context: 应用上下文

        Returns:
            bool: 是否应该配置
        """
        try:
            metadata = self.get_metadata()

            # 1. 检查装饰器条件
            decorator_conditions = get_conditions(self.__class__)
            if decorator_conditions:
                evaluator = ConditionEvaluator()
                if not evaluator.evaluate_conditions(decorator_conditions, context):
                    logger.debug(f"Configuration {metadata.name} skipped due to decorator conditions")
                    return False

            # 2. 检查元数据中的字符串条件(向后兼容)
            for condition in metadata.conditions:
                if not self._evaluate_condition(condition, context):
                    logger.debug(f"Configuration {metadata.name} skipped due to condition: {condition}")
                    return False

            # 3. 检查冲突
            for conflict in metadata.conflicts_with:
                if self._has_configuration(conflict, context):
                    logger.debug(f"Configuration {metadata.name} skipped due to conflict with: {conflict}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error evaluating configuration conditions for {self.__class__.__name__}: {e}")
            return False

    def _evaluate_condition(self, condition: str, context: ApplicationContext) -> bool:
        """评估字符串条件

        Args:
            condition: 条件字符串
            context: 应用上下文

        Returns:
            bool: 条件是否满足
        """
        try:
            # 使用条件评估链来评估条件
            return self._condition_chain.evaluate_condition(condition, context)
        except Exception as e:
            logger.warning(f"Failed to evaluate condition '{condition}': {e}")
            return True  # 默认返回True，避免阻塞配置

    def configure(self, context: ApplicationContext) -> None:
        """执行配置逻辑

        Args:
            context: 应用上下文
        """
        try:
            metadata = self.get_metadata()
            logger.info(f"Applying auto-configuration: {metadata.name}")

            # 默认实现:扫描当前类的@Bean方法并注册
            self._register_bean_methods(context)

            # 执行自定义配置逻辑
            self._do_configure(context)

            logger.debug(f"Auto-configuration {metadata.name} applied successfully")

        except Exception as e:
            metadata = self.get_metadata()
            logger.error(f"Failed to apply auto-configuration {metadata.name}: {e}")
            from ..errors.domains.autoconfigure import \
                AutoConfigurationError as AutoConfigurationException

            raise AutoConfigurationException(
                message=f"Failed to apply configuration {metadata.name}: {e}", configuration_name=metadata.name, cause=e
            ) from e



    def _do_configure(self, _context: ApplicationContext) -> None:
        """执行自定义配置逻辑

        子类可以重写此方法来实现特定的配置逻辑.

        Args:
            context: 应用上下文
        """
        # 默认空实现,子类可以重写
        # 这不是抽象方法,因为它有默认实现
        return



    def _has_configuration(self, _config_name: str, _context: ApplicationContext) -> bool:
        """检查是否存在指定配置

        Args:
            config_name: 配置名称
            context: 应用上下文

        Returns:
            bool: 是否存在配置
        """
        # 这里可以实现配置存在性检查
        # 暂时返回False,后续在registry中实现
        return False

    def _register_bean_methods(self, context: ApplicationContext) -> None:
        """注册Bean方法

        Args:
            context: 应用上下文
        """
        import inspect as py_inspect
        from collections import defaultdict, deque

        from miniboot.annotations.base import is_bean

        # 第一步：收集所有Bean方法和它们的依赖关系
        bean_methods = {}
        dependencies = defaultdict(list)

        for method_name in dir(self):
            method = getattr(self, method_name)

            # 检查是否是方法且标注了@Bean
            if inspect.ismethod(method) and is_bean(method):
                bean_name = getattr(method, "__bean_name__", method_name)
                bean_methods[bean_name] = method

                # 分析依赖关系
                sig = py_inspect.signature(method)
                for param in sig.parameters.values():
                    if param.name != 'self':
                        dependencies[bean_name].append(param.name)

        # 第二步：按依赖关系排序（简单的拓扑排序）
        bean_factory = context.get_bean_factory()
        created_beans = set()

        # 多轮创建，直到所有Bean都创建完成或无法继续
        max_rounds = len(bean_methods) + 1
        for round_num in range(max_rounds):
            created_in_this_round = False

            for bean_name, method in list(bean_methods.items()):
                if bean_name in created_beans:
                    continue

                # 检查依赖是否都已满足
                method_deps = dependencies[bean_name]
                can_create = True
                args = []

                for dep_name in method_deps:
                    if bean_factory.contains(dep_name) or dep_name in created_beans:
                        # 依赖已存在，获取它
                        try:
                            dependency = bean_factory.get(dep_name)
                            args.append(dependency)
                        except Exception as e:
                            logger.warning(f"Failed to get dependency '{dep_name}' for bean '{bean_name}': {e}")
                            can_create = False
                            break
                    else:
                        # 依赖不存在，不能创建这个Bean
                        can_create = False
                        break

                if can_create:
                    try:
                        # 创建Bean实例
                        bean_instance = method(*args)

                        # 应用后置处理器（如果Bean工厂支持）
                        if hasattr(bean_factory, '_post_processors'):
                            # 应用初始化前处理器
                            for processor in bean_factory._post_processors:
                                try:
                                    if hasattr(processor, 'post_process_before_initialization'):
                                        bean_instance = processor.post_process_before_initialization(bean_instance, bean_name)
                                        logger.debug(f"Applied pre-initialization processor: {processor.__class__.__name__} to bean: {bean_name}")
                                except Exception as e:
                                    logger.warning(f"Pre-initialization processor {processor.__class__.__name__} failed for bean {bean_name}: {e}")

                            # 调用 InitializingBean 接口的 after_properties_set 方法
                            if hasattr(bean_instance, 'after_properties_set') and callable(getattr(bean_instance, 'after_properties_set')):
                                try:
                                    logger.debug(f"Calling after_properties_set for InitializingBean: {bean_name}")
                                    bean_instance.after_properties_set()
                                    logger.debug(f"Completed after_properties_set for InitializingBean: {bean_name}")
                                except Exception as e:
                                    logger.warning(f"InitializingBean.after_properties_set failed for bean {bean_name}: {e}")

                            # 应用初始化后处理器
                            for processor in bean_factory._post_processors:
                                try:
                                    if hasattr(processor, 'post_process_after_initialization'):
                                        bean_instance = processor.post_process_after_initialization(bean_instance, bean_name)
                                        logger.debug(f"Applied post-initialization processor: {processor.__class__.__name__} to bean: {bean_name}")
                                except Exception as e:
                                    logger.warning(f"Post-initialization processor {processor.__class__.__name__} failed for bean {bean_name}: {e}")

                        # 注册到容器
                        bean_factory.register_singleton(bean_name, bean_instance)
                        created_beans.add(bean_name)
                        created_in_this_round = True

                        logger.debug(f"Registered bean '{bean_name}' from method '{method.__name__}' with {len(args)} dependencies")

                    except Exception as e:
                        logger.error(f"Failed to register bean '{bean_name}' from method '{method.__name__}': {e}")
                        raise

            # 如果这一轮没有创建任何Bean，说明有循环依赖或其他问题
            if not created_in_this_round:
                remaining_beans = [name for name in bean_methods.keys() if name not in created_beans]
                if remaining_beans:
                    logger.warning(f"Could not create beans due to unresolved dependencies: {remaining_beans}")
                break




class StarterAutoConfiguration(AutoConfiguration):
    """Starter自动配置基类

    专门为Starter模块设计的自动配置基类,提供额外的Starter特性.
    参考现有的MockConfiguration和MonitorConfiguration实现.
    """

    @abstractmethod
    def get_starter_name(self) -> str:
        """获取Starter名称

        Returns:
            str: Starter名称,如'miniboot-starter-web'
        """
        pass

    @abstractmethod
    def get_starter_version(self) -> str:
        """获取Starter版本

        Returns:
            str: Starter版本号
        """
        pass

    def get_starter_description(self) -> Optional[str]:
        """获取Starter描述

        Returns:
            Optional[str]: Starter描述
        """
        return None

    def get_auto_configuration_classes(self) -> list[type]:
        """获取自动配置类列表

        Returns:
            List[Type]: 自动配置类列表
        """
        return []

    def get_configuration_properties_classes(self) -> list[type]:
        """获取配置属性类列表

        Returns:
            List[Type]: 配置属性类列表
        """
        return []

    def register_beans(self, context: ApplicationContext) -> None:
        """注册Starter相关的Bean

        Args:
            context: 应用上下文
        """
        # 默认实现:调用父类的Bean注册逻辑
        self._register_bean_methods(context)

        # 额外的Starter特定Bean注册逻辑
        self._register_starter_beans(context)

    def configure(self, context: ApplicationContext) -> None:
        """执行Starter配置逻辑

        Args:
            context: 应用上下文
        """
        try:
            self.get_metadata()
            logger.info(f"Configuring Starter: {self.get_starter_name()} v{self.get_starter_version()}")

            # 1. 注册配置属性类
            self._register_props(context)

            # 2. 注册Bean
            self.register_beans(context)

            # 3. 执行Starter特定的初始化逻辑
            self._initialize_starter(context)

            logger.info(f"Starter {self.get_starter_name()} configured successfully")

        except Exception as e:
            logger.error(f"Failed to configure Starter {self.get_starter_name()}: {e}")
            raise

    def get_starter_info(self) -> dict[str, Any]:
        """获取Starter信息

        Returns:
            Starter信息字典
        """
        return {
            "name": self.get_starter_name(),
            "version": self.get_starter_version(),
            "description": self.get_starter_description(),
            "configuration_classes": [cls.__name__ for cls in self.get_auto_configuration_classes()],
            "properties_classes": [cls.__name__ for cls in self.get_configuration_properties_classes()],
        }

    def _register_props(self, context: ApplicationContext) -> None:
        """注册配置属性类

        Args:
            context: 应用上下文
        """
        for props_class in self.get_configuration_properties_classes():
            try:
                # 获取Bean名称
                bean_name = self._get_props_name(props_class)

                # 创建配置属性实例
                props_instance = props_class()

                # 通过Bean工厂注册，这样会自动应用Bean后处理器
                bean_factory = context.get_bean_factory()

                # 先注册为单例
                bean_factory.register_singleton(bean_name, props_instance)

                # 手动应用Bean后处理器来处理@ConfigurationProperties绑定
                processors = bean_factory.processors()
                for processor in processors:
                    if hasattr(processor, 'post_process_before_initialization'):
                        props_instance = processor.post_process_before_initialization(props_instance, bean_name)

                # 更新注册的实例
                bean_factory.register_singleton(bean_name, props_instance)

                logger.debug(f"Registered configuration properties: {bean_name}")

            except Exception as e:
                logger.error(f"Failed to register configuration properties {props_class.__name__}: {e}")
                raise

    def _create_props(self, props_class: type, context: ApplicationContext) -> Any:
        """创建配置属性实例

        Args:
            props_class: 配置属性类
            context: 应用上下文

        Returns:
            Any: 配置属性实例
        """
        # 创建默认实例
        props_instance = props_class()

        # 从环境中读取配置并绑定到属性实例
        if hasattr(props_instance, "get_prefix"):
            prefix = props_instance.get_prefix()
            if prefix:
                env = context.get_environment()
                properties = env.get_properties_with_prefix(prefix)

                # 更新属性值
                props_instance.update_from_dict(properties)

        return props_instance

    def _get_props_name(self, props_class: type) -> str:
        """获取配置属性Bean名称

        Args:
            props_class: 配置属性类

        Returns:
            Bean名称
        """
        # 将类名转换为小写下划线格式
        class_name = props_class.__name__
        if class_name.endswith("Properties"):
            class_name = class_name[:-10]  # 去掉'Properties'后缀

        # 转换为下划线格式
        import re

        snake_name = re.sub("([a-z0-9])([A-Z])", r"\1_\2", class_name).lower()
        return f"{snake_name}_properties"

    def _register_starter_beans(self, context: ApplicationContext) -> None:
        """注册Starter特定的Bean

        子类可以重写此方法来注册特定的Bean.

        Args:
            context: 应用上下文
        """
        # 默认空实现,子类可以重写
        pass

    def _initialize_starter(self, context: ApplicationContext) -> None:
        """初始化Starter

        子类可以重写此方法来执行特定的初始化逻辑.

        Args:
            context: 应用上下文
        """
        # 默认空实现,子类可以重写
        pass

    def _do_configure(self, context: ApplicationContext) -> None:
        """执行自定义配置逻辑

        重写父类方法,提供Starter特定的配置逻辑.

        Args:
            context: 应用上下文
        """
        # Starter的配置逻辑已经在configure方法中实现
        # 这里可以添加额外的配置逻辑
        pass
