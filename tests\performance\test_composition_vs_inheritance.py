#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 组合模式vs继承模式性能对比测试

对比重构前后的性能差异，验证组合模式的性能优势。
"""

import asyncio
import time
import unittest
from unittest.mock import Mock

# 新的统一实现
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.proxy import BeanProxy
from miniboot.events.event_types import ApplicationStartedEvent


class PerformanceTestBean:
    """性能测试用的Bean"""

    def __init__(self, name: str):
        self.name = name
        self.call_count = 0
        self.data = {"key": "value", "number": 42}

    def simple_method(self) -> str:
        """简单方法"""
        self.call_count += 1
        return f"result_{self.call_count}"

    def complex_method(self, param1: str, param2: int) -> dict:
        """复杂方法"""
        self.call_count += 1
        return {"param1": param1, "param2": param2, "call_count": self.call_count, "data": self.data}

    def get_data(self) -> dict:
        """获取数据"""
        return self.data.copy()


class PerformanceAsyncBean:
    """性能测试用的异步Bean"""

    def __init__(self, name: str):
        self.name = name
        self.call_count = 0

    async def async_method(self) -> str:
        """异步方法"""
        self.call_count += 1
        await asyncio.sleep(0.001)  # 1ms延迟
        return f"async_result_{self.call_count}"

    def sync_method(self) -> str:
        """同步方法"""
        self.call_count += 1
        return f"sync_result_{self.call_count}"


class CompositionVsInheritancePerformanceTestCase(unittest.TestCase):
    """组合模式vs继承模式性能对比测试"""

    def setUp(self):
        """测试前置设置"""
        self.test_iterations = 1000
        self.bean_count = 100

    def test_bean_factory_performance_comparison(self):
        """Bean工厂性能对比"""
        print("\n=== Bean工厂性能对比 ===")

        # 测试统一Bean工厂
        bean_factory = DefaultBeanFactory()

        # 注册Bean
        beans = []
        for i in range(self.bean_count):
            bean = PerformanceTestBean(f"bean_{i}")
            beans.append(bean)
            bean_factory.register_singleton(f"bean_{i}", bean)

        # 测试统一工厂获取Bean性能
        start_time = time.time()
        for _ in range(self.test_iterations):
            for i in range(self.bean_count):
                bean = bean_factory.get_bean(f"bean_{i}")
                self.assertIsNotNone(bean)
        factory_time = time.time() - start_time

        print(f"统一Bean工厂: {factory_time:.4f}秒 ({self.test_iterations * self.bean_count}次获取)")
        print(f"平均每次获取: {(factory_time / (self.test_iterations * self.bean_count)) * 1000:.4f}ms")

        # 清理
        bean_factory.shutdown()

        # 验证性能要求
        self.assertLess(factory_time, 5.0, "统一Bean工厂性能应该在5秒内")

    def test_proxy_performance_comparison(self):
        """代理性能对比"""
        print("\n=== 代理性能对比 ===")

        sync_bean = PerformanceTestBean("syncBean")
        async_bean = PerformanceAsyncBean("asyncBean")

        # 测试Bean代理
        bean_proxy = BeanProxy(target=sync_bean, bean_name="performanceTestProxy")

        # 测试Bean代理方法调用性能
        start_time = time.time()
        for _ in range(self.test_iterations):
            result = bean_proxy.sync_method()
            self.assertIsNotNone(result)
        proxy_time = time.time() - start_time

        print(f"Bean代理: {proxy_time:.4f}秒 ({self.test_iterations}次方法调用)")
        print(f"平均每次调用: {(proxy_time / self.test_iterations) * 1000:.4f}ms")

        # 测试直接方法调用（基准测试）
        # 重置计数器
        sync_bean.call_count = 0

        start_time = time.time()
        for _ in range(self.test_iterations):
            result = sync_bean.sync_method()
            self.assertIsNotNone(result)
        direct_time = time.time() - start_time

        print(f"直接方法调用: {direct_time:.4f}秒 ({self.test_iterations}次方法调用)")
        print(f"平均每次调用: {(direct_time / self.test_iterations) * 1000:.4f}ms")

        # 性能对比
        if proxy_time > direct_time:
            overhead = ((proxy_time - direct_time) / direct_time) * 100
            print(f"代理开销: {overhead:.2f}%")
            # 代理开销应该在合理范围内（小于50%）
            self.assertLess(overhead, 50.0, "代理开销不应超过50%")

        # 清理
        composite_proxy.shutdown()

        # 验证性能要求
        self.assertLess(composite_proxy_time, 2.0, "组合式代理性能应该在2秒内")

    def test_event_system_performance_comparison(self):
        """事件系统性能对比"""
        print("\n=== 事件系统性能对比 ===")

        # 测试组合式事件创建性能
        start_time = time.time()
        composite_events = []
        for _i in range(self.test_iterations):
            event = ApplicationStartedEvent(application=Mock(), startup_time=1.5)
            composite_events.append(event)
        composite_event_time = time.time() - start_time

        print(f"组合式事件创建: {composite_event_time:.4f}秒 ({self.test_iterations}个事件)")
        print(f"平均每个事件: {(composite_event_time / self.test_iterations) * 1000:.4f}ms")

        # 测试事件数据访问性能
        start_time = time.time()
        for event in composite_events:
            startup_time = event.get_startup_time()
            event_type = event.event_type
            source = event.source
            self.assertIsNotNone(startup_time)
            self.assertIsNotNone(event_type)
            self.assertIsNotNone(source)
        composite_access_time = time.time() - start_time

        print(f"组合式事件数据访问: {composite_access_time:.4f}秒 ({self.test_iterations}个事件)")

        # 清理事件
        for event in composite_events:
            event.cleanup()

        # 验证性能要求
        self.assertLess(composite_event_time, 1.0, "组合式事件创建性能应该在1秒内")
        self.assertLess(composite_access_time, 0.5, "组合式事件访问性能应该在0.5秒内")

    def test_memory_usage_comparison(self):
        """内存使用对比"""
        print("\n=== 内存使用对比 ===")

        import os

        import psutil

        process = psutil.Process(os.getpid())

        # 测试组合式Bean工厂内存使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        composite_factory = CompositeBeanFactory()
        beans = []

        for i in range(self.bean_count):
            bean = PerformanceTestBean(f"memTestBean_{i}")
            beans.append(bean)
            composite_factory.register_singleton(f"memTestBean_{i}", bean)

        after_factory_memory = process.memory_info().rss / 1024 / 1024  # MB
        factory_memory_usage = after_factory_memory - initial_memory

        # 创建代理
        proxies = []
        for i in range(50):  # 创建50个代理
            proxy = CompositeProxy(sync_bean=beans[i], bean_name=f"memTestProxy_{i}")
            proxies.append(proxy)

        after_proxy_memory = process.memory_info().rss / 1024 / 1024  # MB
        proxy_memory_usage = after_proxy_memory - after_factory_memory

        print(f"Bean工厂内存使用: {factory_memory_usage:.2f}MB ({self.bean_count}个Bean)")
        print(f"代理内存使用: {proxy_memory_usage:.2f}MB (50个代理)")
        print(f"平均每个Bean: {(factory_memory_usage / self.bean_count) * 1024:.2f}KB")
        print(f"平均每个代理: {(proxy_memory_usage / 50) * 1024:.2f}KB")

        # 清理
        for proxy in proxies:
            proxy.shutdown()
        composite_factory.shutdown()

        # 验证内存使用合理性
        self.assertLess(factory_memory_usage, 50.0, "Bean工厂内存使用应该小于50MB")
        self.assertLess(proxy_memory_usage, 20.0, "代理内存使用应该小于20MB")

    def test_concurrent_access_performance(self):
        """并发访问性能测试"""
        print("\n=== 并发访问性能测试 ===")

        import concurrent.futures

        # 创建组合式Bean工厂
        factory = CompositeBeanFactory()
        bean = PerformanceTestBean("concurrentTestBean")
        factory.register_singleton("concurrentTestBean", bean)

        # 创建组合式代理
        proxy = CompositeProxy(sync_bean=bean, bean_name="concurrentTestProxy")

        def worker_function(worker_id: int, iterations: int) -> float:
            """工作线程函数"""
            start_time = time.time()
            for _i in range(iterations):
                # 通过工厂获取Bean
                factory_bean = factory.get_bean("concurrentTestBean")
                result1 = factory_bean.simple_method()

                # 通过代理访问Bean
                result2 = proxy.simple_method()

                self.assertIsNotNone(result1)
                self.assertIsNotNone(result2)

            return time.time() - start_time

        # 并发测试
        thread_count = 10
        iterations_per_thread = 100

        start_time = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=thread_count) as executor:
            futures = [executor.submit(worker_function, i, iterations_per_thread) for i in range(thread_count)]

            results = [future.result() for future in concurrent.futures.as_completed(futures)]

        total_time = time.time() - start_time
        avg_thread_time = sum(results) / len(results)

        print(f"并发测试总时间: {total_time:.4f}秒")
        print(f"平均线程时间: {avg_thread_time:.4f}秒")
        print(f"总操作数: {thread_count * iterations_per_thread * 2}")
        print(f"平均每操作: {(total_time / (thread_count * iterations_per_thread * 2)) * 1000:.4f}ms")

        # 清理
        proxy.shutdown()
        factory.shutdown()

        # 验证并发性能
        self.assertLess(total_time, 10.0, "并发测试应该在10秒内完成")

    def test_cache_effectiveness(self):
        """缓存效果测试"""
        print("\n=== 缓存效果测试 ===")

        bean = PerformanceTestBean("cacheTestBean")
        proxy = CompositeProxy(sync_bean=bean, bean_name="cacheTestProxy", cache_size=512)

        # 预热缓存
        for _ in range(100):
            proxy.simple_method()
            proxy.get_data()

        # 测试缓存命中性能
        start_time = time.time()
        for _ in range(self.test_iterations):
            proxy.simple_method()  # 应该命中缓存
        cache_hit_time = time.time() - start_time

        # 获取缓存统计
        stats = proxy.get_stats()
        cache_stats = stats.get("cache_stats", {})

        if cache_stats:
            hits = cache_stats.get("hits", 0)
            misses = cache_stats.get("misses", 0)
            hit_rate = hits / (hits + misses) if (hits + misses) > 0 else 0

            print(f"缓存命中率: {hit_rate * 100:.2f}%")
            print(f"缓存命中: {hits}, 缓存未命中: {misses}")
            print(f"缓存访问时间: {cache_hit_time:.4f}秒 ({self.test_iterations}次)")

            # 验证缓存效果
            self.assertGreater(hit_rate, 0.8, "缓存命中率应该大于80%")

        # 清理
        proxy.shutdown()

    def tearDown(self):
        """测试后清理"""
        # 强制垃圾回收
        import gc

        gc.collect()


if __name__ == "__main__":
    # 运行性能测试
    unittest.main(verbosity=2)
