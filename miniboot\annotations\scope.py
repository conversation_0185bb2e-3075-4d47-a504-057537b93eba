#!/usr/bin/env python
"""
* @author: cz
* @description: 作用域注解实现

实现 @Scope 注解和相关的作用域注解,用于指定 Bean 的作用域.
支持 Web 相关作用域(request、session、application、websocket).
"""

from typing import Any, Callable, Optional, Union

from .metadata import Scope as ScopeEnum
from .metadata import ScopeMetadata


def Scope(  # noqa: N802
    value: Union[str, ScopeEnum] = ScopeEnum.SINGLETON, proxy_mode: str = "default"
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """Scope 作用域注解装饰器

    指定 Bean 的作用域,支持单例、原型和 Web 相关作用域.

    Args:
        value: 作用域值,可以是字符串或 Scope 枚举
        proxy_mode: 代理模式(default、no、interfaces、target_class)

    Returns:
        装饰器函数

    Examples:
        # 单例作用域(默认)
        @Scope()
        class SingletonService:
            pass

        # 原型作用域
        @Scope("prototype")
        class PrototypeService:
            pass

        # 请求作用域
        @Scope("request")
        class RequestService:
            pass

        # 会话作用域
        @Scope("session")
        class SessionService:
            pass

        # 应用作用域
        @Scope("application")
        class ApplicationService:
            pass

        # WebSocket 作用域
        @Scope("websocket")
        class WebSocketService:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 处理作用域值
        scope_value = ScopeEnum.from_string(value) if isinstance(value, str) else value

        # 创建作用域元数据
        metadata = ScopeMetadata(scope=scope_value, proxy_mode=proxy_mode)

        # 存储元数据
        target.__scope_metadata__ = metadata
        target.__is_scoped__ = True
        target.__scope_value__ = scope_value
        target.__proxy_mode__ = proxy_mode

        return target

    return decorator


def RequestScope(  # noqa: N802
    proxy_mode: str = "default",
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """请求作用域注解装饰器

    指定 Bean 为请求作用域,每个 HTTP 请求创建一个实例.

    注意：此函数已废弃，请使用web模块中的Web作用域注解

    Args:
        proxy_mode: 代理模式

    Returns:
        装饰器函数

    Examples:
        @RequestScope()
        class RequestService:
            pass
    """
    return Scope("request", proxy_mode)


def SessionScope(  # noqa: N802
    proxy_mode: str = "default",
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """会话作用域注解装饰器

    指定 Bean 为会话作用域,每个 HTTP 会话创建一个实例.

    注意：此函数已废弃，请使用web模块中的Web作用域注解

    Args:
        proxy_mode: 代理模式

    Returns:
        装饰器函数

    Examples:
        @SessionScope()
        class SessionService:
            pass
    """
    return Scope("session", proxy_mode)


def ApplicationScope(  # noqa: N802
    proxy_mode: str = "default",
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """应用作用域注解装饰器

    指定 Bean 为应用作用域,整个 Web 应用创建一个实例.

    注意：此函数已废弃，请使用web模块中的Web作用域注解

    Args:
        proxy_mode: 代理模式

    Returns:
        装饰器函数

    Examples:
        @ApplicationScope()
        class ApplicationService:
            pass
    """
    return Scope("application", proxy_mode)


def WebSocketScope(  # noqa: N802
    proxy_mode: str = "default",
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """WebSocket 作用域注解装饰器

    指定 Bean 为 WebSocket 作用域,每个 WebSocket 会话创建一个实例.

    注意：此函数已废弃，请使用web模块中的Web作用域注解

    Args:
        proxy_mode: 代理模式

    Returns:
        装饰器函数

    Examples:
        @WebSocketScope()
        class WebSocketService:
            pass
    """
    return Scope("websocket", proxy_mode)


def PrototypeScope(  # noqa: N802
    proxy_mode: str = "default",
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """原型作用域注解装饰器

    指定 Bean 为原型作用域,每次获取都创建新实例.

    Args:
        proxy_mode: 代理模式

    Returns:
        装饰器函数

    Examples:
        @PrototypeScope()
        class PrototypeService:
            pass
    """
    return Scope(ScopeEnum.PROTOTYPE, proxy_mode)


def SingletonScope(  # noqa: N802
    proxy_mode: str = "default",
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """单例作用域注解装饰器

    指定 Bean 为单例作用域,容器中只有一个实例.

    Args:
        proxy_mode: 代理模式

    Returns:
        装饰器函数

    Examples:
        @SingletonScope()
        class SingletonService:
            pass
    """
    return Scope(ScopeEnum.SINGLETON, proxy_mode)


def is_scoped(target: Any) -> bool:
    """检查目标是否有作用域注解

    Args:
        target: 要检查的目标(类或方法)

    Returns:
        True 表示有作用域注解
    """
    return hasattr(target, "__is_scoped__") and target.__is_scoped__


def get_scope_metadata(target: Any) -> Optional[ScopeMetadata]:
    """获取作用域注解的元数据

    Args:
        target: 目标对象

    Returns:
        作用域元数据,如果没有则返回 None
    """
    return getattr(target, "__scope_metadata__", None)


def get_scope_value(target: Any) -> Optional[ScopeEnum]:
    """获取作用域值

    Args:
        target: 目标对象

    Returns:
        作用域值,如果没有则返回 None
    """
    return getattr(target, "__scope_value__", None)


def get_proxy_mode(target: Any) -> Optional[str]:
    """获取代理模式

    Args:
        target: 目标对象

    Returns:
        代理模式,如果没有则返回 None
    """
    return getattr(target, "__proxy_mode__", None)


# 便利函数
def has_request_scope(target: Any) -> bool:
    """检查是否为请求作用域

    注意：此函数已废弃，请使用web模块中的has_web_scope函数
    """
    scope = get_scope_value(target)
    return scope.value == "request" if scope else False


def has_session_scope(target: Any) -> bool:
    """检查是否为会话作用域

    注意：此函数已废弃，请使用web模块中的has_web_scope函数
    """
    scope = get_scope_value(target)
    return scope.value == "session" if scope else False


def has_application_scope(target: Any) -> bool:
    """检查是否为应用作用域

    注意：此函数已废弃，请使用web模块中的has_web_scope函数
    """
    scope = get_scope_value(target)
    return scope.value == "application" if scope else False


def has_websocket_scope(target: Any) -> bool:
    """检查是否为 WebSocket 作用域

    注意：此函数已废弃，请使用web模块中的has_web_scope函数
    """
    scope = get_scope_value(target)
    return scope.value == "websocket" if scope else False


def has_prototype_scope(target: Any) -> bool:
    """检查是否为原型作用域"""
    scope = get_scope_value(target)
    return scope == ScopeEnum.PROTOTYPE if scope else False


def has_singleton_scope(target: Any) -> bool:
    """检查是否为单例作用域"""
    scope = get_scope_value(target)
    return scope == ScopeEnum.SINGLETON if scope else False


def has_web_scope(target: Any) -> bool:
    """检查是否为 Web 相关作用域

    注意：此函数已废弃，Web作用域检查应使用web模块中的has_web_scope函数
    """
    # 由于Web作用域已移至web模块，这里只能检查是否不是核心作用域
    scope = get_scope_value(target)
    if not scope:
        return False
    # 如果不是核心作用域，可能是Web作用域
    return scope not in (ScopeEnum.SINGLETON, ScopeEnum.PROTOTYPE)
