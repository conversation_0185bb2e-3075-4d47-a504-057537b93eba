#!/usr/bin/env python
"""
* @author: cz
* @description: 基础注解实现

实现Mini-Boot框架的基础注解,包括@Component、@Service、@Repository、@Configuration等.
这些注解用于标记类为Spring Boot风格的组件,支持依赖注入和自动装配.
"""

import inspect
from typing import Callable, Optional, Union

from .metadata import BeanMetadata, ComponentMetadata, Scope


def Component(  # noqa: N802
    name_or_cls: Union[str, type, None] = None,
    *,
    name: Optional[str] = None,
    scope: Scope = Scope.SINGLETON,
    lazy: bool = False,
    primary: bool = False,
    depends_on: Optional[list[str]] = None,
) -> Union[Callable[[type], type], type]:
    """组件注解装饰器

    用于标记一个类为组件,可以被容器管理.支持无参数调用和带参数调用两种方式.

    Args:
        name_or_cls: 当直接装饰类时为类对象,当带参数调用时为Bean名称
        name: Bean名称,默认为类名首字母小写
        scope: Bean作用域,默认为单例
        lazy: 是否延迟初始化,默认为False
        primary: 是否为主要Bean,默认为False
        depends_on: 依赖的Bean名称列表

    Returns:
        装饰后的类或装饰器函数

    Examples:
        # 无参数使用
        @Component
        class UserService:
            pass

        # 带参数使用
        @Component(name="userService", scope=Scope.SINGLETON)
        class UserService:
            pass
    """

    def decorator(cls: type) -> type:
        # 确定Bean名称
        bean_name = (name_or_cls if isinstance(name_or_cls, str) else cls.__name__[0].lower() + cls.__name__[1:]) if name is None else name

        # 检查pending标记并合并属性
        final_lazy = lazy
        final_primary = primary
        final_depends_on = list(depends_on or [])

        # 检查@Lazy pending标记
        if hasattr(cls, "__lazy_pending__"):
            final_lazy = cls.__lazy_pending__
            delattr(cls, "__lazy_pending__")

        # 检查@Primary pending标记
        if hasattr(cls, "__primary_pending__"):
            final_primary = True
            delattr(cls, "__primary_pending__")

        # 检查@DependsOn pending标记
        if hasattr(cls, "__depends_on_pending__"):
            final_depends_on.extend(cls.__depends_on_pending__)
            delattr(cls, "__depends_on_pending__")

        # 创建组件元数据
        metadata = ComponentMetadata(name=bean_name, scope=scope, lazy=final_lazy, primary=final_primary, depends_on=final_depends_on)

        # 存储元数据到类属性
        cls.__component_metadata__ = metadata
        cls.__is_component__ = True
        cls.__component_name__ = bean_name
        cls.__component_scope__ = scope

        return cls

    # 如果直接传入类,不带参数(@Component)
    if isinstance(name_or_cls, type):
        cls = name_or_cls
        return decorator(cls)

    # 带参数调用(@Component(...))
    return decorator


def Service(  # noqa: N802
    name_or_cls: Union[str, type, None] = None, *, name: Optional[str] = None
) -> Union[Callable[[type], type], type]:
    """服务注解装饰器

    @Service 是 @Component 的特化,用于标记服务类.

    Args:
        name_or_cls: 当直接装饰类时为类对象,当带参数调用时为Bean名称
        name: Bean名称,默认为类名首字母小写

    Returns:
        装饰后的类或装饰器函数

    Examples:
        @Service
        class UserService:
            pass

        @Service("userService")
        class UserService:
            pass
    """

    # 直接复用Component装饰器,只需添加服务标记
    def add_service_marker(cls: type) -> type:
        cls.__is_service__ = True
        return cls

    # 如果直接传入类,不带参数(@Service)
    if isinstance(name_or_cls, type):
        cls = name_or_cls
        cls = Component(cls)  # 先应用Component
        return add_service_marker(cls)  # 再添加Service标记

    # 带参数调用(@Service(...) 或 @Service("name"))
    def decorator(cls: type) -> type:
        # 确定Bean名称
        bean_name = (name_or_cls if isinstance(name_or_cls, str) else None) if name is None else name

        # 应用Component装饰器
        cls = Component(name=bean_name)(cls) if bean_name else Component(cls)

        # 添加服务标记
        return add_service_marker(cls)

    return decorator


def Repository(  # noqa: N802
    name_or_cls: Union[str, type, None] = None, *, name: Optional[str] = None
) -> Union[Callable[[type], type], type]:
    """仓储注解装饰器

    @Repository 是 @Component 的特化,用于标记数据访问类.

    Args:
        name_or_cls: 当直接装饰类时为类对象,当带参数调用时为Bean名称
        name: Bean名称,默认为类名首字母小写

    Returns:
        装饰后的类或装饰器函数
    """

    # 直接复用Component装饰器,只需添加仓储标记
    def add_repository_marker(cls: type) -> type:
        cls.__is_repository__ = True
        return cls

    # 如果直接传入类,不带参数(@Repository)
    if isinstance(name_or_cls, type):
        cls = name_or_cls
        cls = Component(cls)  # 先应用Component
        return add_repository_marker(cls)  # 再添加Repository标记

    # 带参数调用(@Repository(...) 或 @Repository("name"))
    def decorator(cls: type) -> type:
        # 确定Bean名称
        bean_name = (name_or_cls if isinstance(name_or_cls, str) else None) if name is None else name

        # 应用Component装饰器
        cls = Component(name=bean_name)(cls) if bean_name else Component(cls)

        # 添加仓储标记
        return add_repository_marker(cls)

    return decorator


def Configuration(cls: Optional[type] = None) -> Union[Callable[[type], type], type]:  # noqa: N802
    """配置类注解装饰器

    标记一个类为配置类,通常用于集中管理Bean的定义.
    配置类本身也会被注册为一个Bean.

    Args:
        cls: 被装饰的类

    Returns:
        装饰后的类或装饰器函数

    Examples:
        @Configuration
        class AppConfig:
            @Bean
            def data_source(self):
                return DataSource()
    """

    def decorator(cls: type) -> type:
        # 标记为配置类
        cls.__is_configuration__ = True
        cls.__configuration__ = True

        # 配置类本身也是一个组件
        cls = Component(cls)

        return cls

    if cls is None:
        return decorator
    return decorator(cls)


def Bean(  # noqa: N802
    func: Optional[Callable] = None,
    *,
    name: Optional[str] = None,
    scope: Scope = Scope.SINGLETON,
    init_method: Optional[str] = None,
    destroy_method: Optional[str] = None,
    depends_on: Optional[list[str]] = None,
) -> Union[Callable, Callable[[Callable], Callable]]:
    """Bean定义注解装饰器

    用于在配置类中定义Bean的方法注解.

    Args:
        func: 被装饰的方法
        name: Bean名称,默认为方法名
        scope: Bean作用域,默认为单例
        init_method: 初始化方法名
        destroy_method: 销毁方法名
        depends_on: 依赖的Bean名称列表

    Returns:
        装饰后的方法或装饰器函数

    Examples:
        @Configuration
        class DatabaseConfig:
            @Bean(name="dataSource")
            def create_data_source(self):
                return DataSource("********************************")
    """

    def decorator(func: Callable) -> Callable:
        # 创建Bean元数据
        metadata = BeanMetadata(
            name=name or func.__name__, scope=scope, init_method=init_method, destroy_method=destroy_method, depends_on=depends_on or []
        )

        # 存储元数据到方法属性
        func.__bean_metadata__ = metadata
        func.__is_bean__ = True
        func.__bean__ = True
        func.__bean_name__ = name or func.__name__
        func.__bean_scope__ = scope

        return func

    if func is None:
        return decorator
    return decorator(func)


def MiniBootApplication(  # noqa: N802
    cls: Optional[type] = None, *, base_packages: Optional[list[str]] = None
) -> Union[Callable[[type], type], type]:
    """应用入口注解装饰器

    相当于@Configuration + @ComponentScan + @EnableAutoConfiguration的组合.

    Args:
        cls: 被装饰的类
        base_packages: 要扫描的包路径列表

    Returns:
        装饰后的类或装饰器函数

    Examples:
        @MiniBootApplication
        class Application:
            pass

        @MiniBootApplication(base_packages=["com.example"])
        class Application:
            pass
    """

    def decorator(cls: type) -> type:
        # 应用@Configuration注解
        cls = Configuration(cls)

        # 应用@ComponentScan注解
        if base_packages is None:
            # 默认扫描当前包
            module = cls.__module__
            current_package = module.rsplit(".", 1)[0] if "." in module else module
            scan_packages = [current_package]
        else:
            scan_packages = base_packages

        # 导入config模块的ComponentScan
        from .config import ComponentScan

        cls = ComponentScan(base_packages=scan_packages)(cls)

        # 标记为应用入口
        cls.__is_miniboot_application__ = True
        cls.__miniboot_application__ = True

        return cls

    if cls is None:
        return decorator
    return decorator(cls)


# 工具函数


def is_component(cls_or_method) -> bool:
    """检查类或方法是否有@Component注解"""
    # 对于类对象,直接检查
    if inspect.isclass(cls_or_method):
        return hasattr(cls_or_method, "__is_component__") and getattr(cls_or_method, "__is_component__", False)
    # 对于方法,直接检查
    return getattr(cls_or_method, "__is_component__", False)


def is_service(cls_or_method) -> bool:
    """检查类或方法是否有@Service注解"""
    # 对于类对象,直接检查
    if inspect.isclass(cls_or_method):
        return hasattr(cls_or_method, "__is_service__") and getattr(cls_or_method, "__is_service__", False)
    # 对于方法,直接检查
    return getattr(cls_or_method, "__is_service__", False)


def is_repository(cls_or_method) -> bool:
    """检查类或方法是否有@Repository注解"""
    # 对于类对象,直接检查
    if inspect.isclass(cls_or_method):
        return hasattr(cls_or_method, "__is_repository__") and getattr(cls_or_method, "__is_repository__", False)
    # 对于方法,直接检查
    return getattr(cls_or_method, "__is_repository__", False)


def is_configuration(cls_or_method) -> bool:
    """检查类或方法是否有@Configuration注解"""
    # 对于类对象,直接检查
    if inspect.isclass(cls_or_method):
        return hasattr(cls_or_method, "__is_configuration__") and getattr(cls_or_method, "__is_configuration__", False)
    # 对于方法,直接检查
    return getattr(cls_or_method, "__is_configuration__", False)


def is_bean(method) -> bool:
    """检查方法是否有@Bean注解"""
    return getattr(method, "__is_bean__", False)


def is_miniboot_application(cls) -> bool:
    """检查类是否有@MiniBootApplication注解"""
    # 对于类对象,直接检查
    if inspect.isclass(cls):
        return hasattr(cls, "__is_miniboot_application__") and getattr(cls, "__is_miniboot_application__", False)
    # 直接检查
    return getattr(cls, "__is_miniboot_application__", False)


def component_metadata(cls):
    """获取组件元数据"""
    return getattr(cls, "__component_metadata__", None)


def bean_metadata(method):
    """获取Bean元数据"""
    return getattr(method, "__bean_metadata__", None)
