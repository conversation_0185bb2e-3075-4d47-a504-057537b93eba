# Mini-Boot 日志系统设计文档

## 1. 概述

Mini-Boot 框架的日志系统基于 [loguru](https://github.com/Delgan/loguru) 实现，采用简洁的设计理念。loguru 提供了一个全局的根日志对象，我们只需要在应用启动时配置这个根日志对象，其他模块直接 `from loguru import logger` 就能使用配置好的日志实例。

## 2. 设计理念

### 2.1 全局日志对象

loguru 的核心设计是提供一个全局的 `logger` 对象，所有模块共享同一个日志实例：

```python
# 在应用启动时配置日志
from miniboot.log import configure_logger
configure_logger(environment)

# 在任何模块中使用日志
from loguru import logger
logger.info("这是一条日志")
```

### 2.2 配置驱动

日志系统与环境配置模块集成，支持从多种配置源加载日志配置：

-   **环境变量**: `LOGGING_LEVEL=DEBUG`
-   **配置文件**: `application.yml`
-   **命令行参数**: `--logging.level=DEBUG`
-   **默认配置**: 代码中的默认值

## 3. 核心实现

### 3.1 日志配置函数

```python
# miniboot/logger.py
import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional

def configure_logger(environment=None):
    """配置全局日志对象

    Args:
        environment: 环境配置对象，如果为None则使用默认配置
    """
    # 移除默认处理器
    logger.remove()

    # 从环境配置加载日志配置
    config = _load_config(environment)

    # 配置控制台输出
    if config.get('console', {}).get('enabled', True):
        _configure_console(config['console'])

    # 配置文件输出
    if config.get('file', {}).get('enabled', True):
        _configure_file(config['file'])

    logger.info("日志系统初始化完成")

def _load_config(environment):
    """从环境配置加载日志配置"""
    if not environment:
        return _get_default_config()

    # 从环境配置获取日志配置
    logging_config = {}

    # 控制台配置
    console_config = {
        'enabled': environment.get_property_or_default('logging.console.enabled', True),
        'level': environment.get_property_or_default('logging.level', 'INFO'),
        'colorize': environment.get_property_or_default('logging.format.color', True),
        'format': environment.get_property_or_default(
            'logging.format.pattern',
            '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}'
        )
    }

    # 文件配置
    file_config = {
        'enabled': environment.get_property_or_default('logging.file.enabled', True),
        'path': environment.get_property_or_default('logging.file.path', 'logs/app.log'),
        'level': environment.get_property_or_default('logging.level', 'DEBUG'),
        'rotation': environment.get_property_or_default('logging.rotation.size', '500 MB'),
        'retention': environment.get_property_or_default('logging.rotation.time', '10 days'),
        'compression': environment.get_property_or_default('logging.compression', 'zip'),
        'format': environment.get_property_or_default(
            'logging.format.pattern',
            '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}'
        )
    }

    return {
        'console': console_config,
        'file': file_config
    }

def _get_default_config():
    """获取默认配置"""
    return {
        'console': {
            'enabled': True,
            'level': 'INFO',
            'colorize': True,
            'format': '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}'
        },
        'file': {
            'enabled': True,
            'path': 'logs/app.log',
            'level': 'DEBUG',
            'rotation': '500 MB',
            'retention': '10 days',
            'compression': 'zip',
            'format': '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}'
        }
    }

def _configure_console(console_config):
    """配置控制台输出"""
    logger.add(
        sys.stderr,
        level=console_config.get('level', 'INFO'),
        format=console_config.get('format'),
        colorize=console_config.get('colorize', True),
        backtrace=True,
        diagnose=True
    )

def _configure_file(file_config):
    """配置文件输出"""
    log_path = file_config.get('path', 'logs/app.log')

    # 确保日志目录存在
    os.makedirs(os.path.dirname(log_path), exist_ok=True)

    logger.add(
        log_path,
        level=file_config.get('level', 'DEBUG'),
        rotation=file_config.get('rotation', '500 MB'),
        retention=file_config.get('retention', '10 days'),
        compression=file_config.get('compression', 'zip'),
        format=file_config.get('format'),
        encoding='utf-8',
        backtrace=True,
        diagnose=True
    )

def update_log_level(level: str):
    """动态更新日志级别"""
    # loguru 不支持动态更新级别，需要重新配置
    # 这里可以实现重新配置的逻辑
    logger.info(f"日志级别更新为: {level}")
```

## 4. 配置选项

日志系统通过环境配置模块进行配置，支持 `application.yml` 配置文件：

```yaml
logging:
    level: INFO # 日志级别
    console:
        enabled: true # 是否启用控制台输出
    file:
        enabled: true # 是否启用文件输出
        path: logs/app.log # 日志文件路径
    rotation:
        size: "500 MB" # 日志轮转大小
        time: "10 days" # 日志保留时间
    compression: zip # 压缩格式
    format:
        pattern: null # 自定义格式模式
        color: true # 是否启用颜色
```

### 4.1 配置项说明

| 配置项                  | 默认值       | 说明                                                            |
| ----------------------- | ------------ | --------------------------------------------------------------- |
| logging.level           | INFO         | 日志级别：TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL |
| logging.console.enabled | true         | 是否启用控制台输出                                              |
| logging.file.enabled    | true         | 是否启用文件输出                                                |
| logging.file.path       | logs/app.log | 日志文件路径                                                    |
| logging.rotation.size   | 500 MB       | 日志轮转大小                                                    |
| logging.rotation.time   | 10 days      | 日志保留时间                                                    |
| logging.compression     | zip          | 压缩格式                                                        |
| logging.format.pattern  | null         | 自定义日志格式模式                                              |
| logging.format.color    | true         | 是否启用彩色输出                                                |

## 5. 使用示例

### 5.1 应用启动时配置日志

```python
# main.py - 应用入口
from miniboot.env import StandardEnvironment
from miniboot.logger import configure_logger

def main():
    # 创建环境配置
    env = StandardEnvironment()

    # 配置日志系统
    configure_logger(env)

    # 启动应用
    app.run()

if __name__ == "__main__":
    main()
```

### 5.2 在模块中使用日志

```python
# service/user_service.py
from loguru import logger

class UserService:
    def create_user(self, username: str):
        logger.info("开始创建用户: {}", username)

        try:
            # 业务逻辑
            user = self._save_user(username)
            logger.info("用户创建成功", extra={"user_id": user.id, "username": username})
            return user
        except Exception as e:
            logger.error("用户创建失败: {}", e)
            raise

    def _save_user(self, username: str):
        logger.debug("保存用户到数据库: {}", username)
        # 保存逻辑
        return User(id=1, username=username)
```

### 5.3 不同级别的日志

```python
from loguru import logger

# 不同级别的日志
logger.trace("跟踪信息")      # 最详细的调试信息
logger.debug("调试信息")      # 开发调试信息
logger.info("普通信息")       # 一般信息
logger.success("成功信息")    # 成功操作
logger.warning("警告信息")    # 警告
logger.error("错误信息")      # 错误
logger.critical("严重错误")   # 严重错误
```

### 5.4 结构化日志

```python
from loguru import logger

# 使用 bind 方法
logger.bind(user_id=123, action="login").info("用户登录")

# 使用 extra 参数
logger.info("订单创建", extra={
    "order_id": "ORD-001",
    "user_id": 123,
    "amount": 99.99,
    "currency": "USD"
})
```

### 5.5 异常日志

```python
from loguru import logger

try:
    result = 1 / 0
except Exception as e:
    # 自动记录异常堆栈
    logger.exception("计算发生异常")

    # 或者手动记录
    logger.error("除零错误: {}", str(e))
```

## 6. 与环境模块集成

### 6.1 配置优先级

日志系统与环境配置模块深度集成，支持从多种配置源加载日志配置（按优先级）：

1. **命令行参数**: `--logging.level=DEBUG`
2. **环境变量**: `LOGGING_LEVEL=DEBUG`
3. **配置文件**: `application.yml`
4. **默认配置**: 代码中的默认值

```python
# 应用启动时自动从环境配置加载日志配置
from miniboot.env import StandardEnvironment
from miniboot.log import configure_logger

env = StandardEnvironment()
configure_logger(env)  # 自动从环境配置加载日志设置
```

### 6.2 多环境配置

支持不同环境使用不同的日志配置：

```yaml
# application.yml (默认配置)
logging:
    level: INFO
    file:
        enabled: true

---
# application-dev.yml (开发环境)
logging:
    level: DEBUG
    console:
        enabled: true
    file:
        enabled: false

---
# application-prod.yml (生产环境)
logging:
    level: WARNING
    file:
        path: /var/log/miniboot/app.log
    rotation:
        size: "1 GB"
        time: "30 days"
```

### 6.3 环境变量配置

```bash
# 通过环境变量配置日志
export LOGGING_LEVEL=DEBUG
export LOGGING_FILE_PATH=/tmp/app.log
export LOGGING_CONSOLE_ENABLED=false

# 启动应用
python main.py
```

## 7. 高级功能

### 7.1 自定义格式化器

```python
from loguru import logger

# 自定义JSON格式
def json_formatter(record):
    import json
    return json.dumps({
        "timestamp": record["time"].isoformat(),
        "level": record["level"].name,
        "message": record["message"],
        "extra": record.get("extra", {})
    }) + "\n"

# 在配置时使用自定义格式化器
def configure_logger_with_json(environment):
    logger.remove()

    # 控制台使用默认格式
    logger.add(sys.stderr, level="INFO")

    # 文件使用JSON格式
    logger.add("logs/app.json", format=json_formatter, level="DEBUG")
```

### 7.2 性能监控装饰器

```python
import time
from functools import wraps
from loguru import logger

def log_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info("函数执行完成: {} ({:.3f}s)", func.__name__, execution_time)
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error("函数执行失败: {} ({:.3f}s) - {}", func.__name__, execution_time, str(e))
            raise
    return wrapper

# 使用示例
@log_performance
def process_data(data):
    time.sleep(1)
    return len(data)
```

## 8. 最佳实践

### 8.1 日志级别使用指南

1. **TRACE**: 最详细的跟踪信息，通常只在开发调试时使用
2. **DEBUG**: 开发和调试信息，生产环境应禁用
3. **INFO**: 正常操作信息，记录重要的业务流程
4. **SUCCESS**: 成功操作的确认信息
5. **WARNING**: 潜在问题警告，不影响正常运行
6. **ERROR**: 错误信息，但系统可以继续运行
7. **CRITICAL**: 严重错误，可能导致系统无法正常运行

### 8.2 结构化日志最佳实践

```python
from loguru import logger

# 好的做法：使用结构化日志
logger.info("用户登录成功", extra={
    "user_id": user.id,
    "username": user.username,
    "ip_address": request.remote_addr,
    "login_time": datetime.now().isoformat()
})

# 避免的做法：字符串拼接
logger.info(f"用户 {user.username} 从 {request.remote_addr} 登录成功")
```

### 8.3 性能考虑

1. **合理设置日志级别**: 生产环境避免 DEBUG 级别日志
2. **日志轮转策略**: 根据磁盘空间和保留需求配置轮转
3. **避免敏感信息**: 不要在日志中记录密码、密钥等敏感信息
4. **使用结构化日志**: 便于后期分析和处理

### 8.4 生产环境配置建议

```yaml
# 生产环境日志配置
logging:
    level: INFO
    console:
        enabled: false # 生产环境关闭控制台输出
    file:
        enabled: true
        path: /var/log/miniboot/app.log
    rotation:
        size: "1 GB"
        time: "30 days"
    compression: zip
```

## 9. 总结

Mini-Boot 框架的日志系统采用简洁的设计理念，基于 loguru 的全局日志对象，具有以下特点：

### 核心设计理念

1. **全局日志对象**: 使用 loguru 的全局 logger，所有模块共享同一个日志实例
2. **配置驱动**: 在应用启动时通过环境配置模块配置日志系统
3. **简洁易用**: 其他模块只需 `from loguru import logger` 即可使用

### 主要特性

1. **环境集成**: 与环境配置模块深度集成，支持多环境配置
2. **配置优先级**: 支持命令行参数、环境变量、配置文件等多种配置源
3. **高性能**: 基于 loguru，原生支持异步日志和高性能输出
4. **结构化**: 原生支持结构化日志记录
5. **自动轮转**: 内置文件轮转和压缩功能
6. **彩色输出**: 原生支持彩色终端输出

### 使用流程

```python
# 1. 应用启动时配置日志
from miniboot.env import StandardEnvironment
from miniboot.log import configure_logger

env = StandardEnvironment()
configure_logger(env)

# 2. 在任何模块中使用日志
from loguru import logger
logger.info("这是一条日志")
```

### 与 Go 版本对比

| 特性       | Go 版本(logrus) | Python 版本(loguru) |
| ---------- | --------------- | ------------------- |
| 设计复杂度 | 中等            | 简洁                |
| 配置方式   | YAML 配置       | 环境模块集成        |
| 性能       | 高              | 更高                |
| 异步支持   | 不支持          | 原生支持            |
| 结构化     | Fields          | bind/extra          |
| 彩色输出   | 第三方库        | 原生支持            |
| 文件轮转   | 第三方库        | 原生支持            |

---

_本文档定义了 Mini-Boot 框架的简洁日志系统设计，基于 loguru 的全局日志对象提供完整的日志记录功能。_
