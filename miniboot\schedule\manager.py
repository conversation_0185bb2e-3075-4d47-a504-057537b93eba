#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 任务管理功能 - 实现任务的添加、移除和管理
"""

import asyncio
import threading
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Callable, Optional

from apscheduler.triggers.date import DateTrigger

from ..annotations.schedule import ScheduledConfig
from ..errors import SchedulerConfigurationError as ScheduleConfigurationError
from ..errors import TaskExecutionError as ScheduleExecutionError
from .task import ScheduledTask, TaskFactory, TaskType


class TaskExecutionMetrics:
    """任务执行指标"""

    def __init__(self):
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        self.total_execution_time = 0.0
        self.min_execution_time = float("inf")
        self.max_execution_time = 0.0
        self.last_execution_time = None
        self.last_execution_duration = None
        self.consecutive_failures = 0
        self.last_success_time = None
        self.last_failure_time = None
        self._lock = threading.Lock()

    def record_execution_start(self) -> float:
        """记录执行开始,返回开始时间"""
        return time.time()

    def record_execution_success(self, start_time: float) -> None:
        """记录执行成功"""
        duration = time.time() - start_time

        with self._lock:
            self.total_executions += 1
            self.successful_executions += 1
            self.total_execution_time += duration
            self.min_execution_time = min(self.min_execution_time, duration)
            self.max_execution_time = max(self.max_execution_time, duration)
            self.last_execution_time = datetime.now(timezone.utc)
            self.last_execution_duration = duration
            self.consecutive_failures = 0
            self.last_success_time = self.last_execution_time

    def record_execution_failure(self, start_time: float) -> None:
        """记录执行失败"""
        duration = time.time() - start_time

        with self._lock:
            self.total_executions += 1
            self.failed_executions += 1
            self.total_execution_time += duration
            self.last_execution_time = datetime.now(timezone.utc)
            self.last_execution_duration = duration
            self.consecutive_failures += 1
            self.last_failure_time = self.last_execution_time

    def get_average_execution_time(self) -> float:
        """获取平均执行时间"""
        with self._lock:
            if self.total_executions == 0:
                return 0.0
            return self.total_execution_time / self.total_executions

    def get_success_rate(self) -> float:
        """获取成功率"""
        with self._lock:
            if self.total_executions == 0:
                return 0.0
            return self.successful_executions / self.total_executions

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        with self._lock:
            # 在锁内直接计算,避免调用其他需要锁的方法
            success_rate = 0.0
            if self.total_executions > 0:
                success_rate = self.successful_executions / self.total_executions

            average_execution_time = 0.0
            if self.total_executions > 0:
                average_execution_time = self.total_execution_time / self.total_executions

            return {
                "total_executions": self.total_executions,
                "successful_executions": self.successful_executions,
                "failed_executions": self.failed_executions,
                "success_rate": success_rate,
                "average_execution_time": average_execution_time,
                "min_execution_time": self.min_execution_time if self.min_execution_time != float("inf") else 0.0,
                "max_execution_time": self.max_execution_time,
                "last_execution_time": self.last_execution_time.isoformat() if self.last_execution_time else None,
                "last_execution_duration": self.last_execution_duration,
                "consecutive_failures": self.consecutive_failures,
                "last_success_time": self.last_success_time.isoformat() if self.last_success_time else None,
                "last_failure_time": self.last_failure_time.isoformat() if self.last_failure_time else None,
            }


class TaskWrapper:
    """任务包装器 - 增强任务执行功能"""

    def __init__(self, task: ScheduledTask, scheduler_ref):
        self.task = task
        self.scheduler_ref = scheduler_ref
        self.metrics = TaskExecutionMetrics()
        self.retry_count = 0
        self.max_retries = 3
        self.retry_delay = 1.0  # 秒
        self._lock = threading.Lock()

    async def execute_with_monitoring(self) -> Any:
        """带监控的任务执行"""
        start_time = self.metrics.record_execution_start()

        try:
            # 标记任务开始执行
            self.task.mark_execution_start()

            # 执行任务
            result = await self.task.execute()

            # 记录成功
            self.metrics.record_execution_success(start_time)
            self.task.mark_execution_success()

            # 重置重试计数
            with self._lock:
                self.retry_count = 0

            return result

        except Exception as e:
            # 记录失败
            self.metrics.record_execution_failure(start_time)
            self.task.mark_execution_failure(e)

            # 处理重试逻辑(同步处理,避免异步问题)
            self._handle_retry_sync(e)

            raise ScheduleExecutionError(f"任务执行失败: {e}", task_name=self.task.name, execution_time=datetime.now(timezone.utc).isoformat()) from e

    def execute_with_monitoring_sync(self) -> Any:
        """带监控的同步任务执行"""
        start_time = self.metrics.record_execution_start()

        try:
            # 标记任务开始执行
            self.task.mark_execution_start()

            # 执行任务(使用asyncio.run处理异步任务)
            if asyncio.iscoroutinefunction(self.task.execute):
                result = asyncio.run(self.task.execute())
            else:
                # 对于同步任务,需要包装成协程
                async def sync_wrapper():
                    return await self.task.execute()

                result = asyncio.run(sync_wrapper())

            # 记录成功
            self.metrics.record_execution_success(start_time)
            self.task.mark_execution_success()

            # 重置重试计数
            with self._lock:
                self.retry_count = 0

            return result

        except Exception as e:
            # 记录失败
            self.metrics.record_execution_failure(start_time)
            self.task.mark_execution_failure(e)

            # 处理重试逻辑
            self._handle_retry_sync(e)

            raise ScheduleExecutionError(f"任务执行失败: {e}", task_name=self.task.name, execution_time=datetime.now(timezone.utc).isoformat()) from e

    def _handle_retry_sync(self, _error: Exception) -> None:
        """处理重试逻辑(同步版本)"""
        with self._lock:
            if self.retry_count < self.max_retries:
                self.retry_count += 1

                # 计算重试延迟(指数退避)
                self.retry_delay * (2 ** (self.retry_count - 1))

                # 记录重试信息(暂时只记录,实际重试由调度器处理)
                # 这里可以添加重试逻辑,比如重新调度任务
                pass

    def get_metrics(self) -> dict[str, Any]:
        """获取任务指标"""
        return self.metrics.to_dict()


class FixedDelayTaskHandler:
    """固定延迟任务处理器"""

    def __init__(self, scheduler_ref):
        self.scheduler_ref = scheduler_ref
        self._delay_tasks: dict[str, dict[str, Any]] = {}
        self._lock = threading.Lock()

    def add_fixed_delay_task(self, task: ScheduledTask, task_wrapper: TaskWrapper) -> str:
        """添加固定延迟任务"""
        if task.task_type != TaskType.FIXED_DELAY:
            raise ScheduleConfigurationError("只能添加固定延迟类型的任务")

        task_id = task.task_id
        # 使用解析后的秒数值
        config = task.config
        delay_seconds = getattr(config, "_fixed_delay_seconds", None)
        if delay_seconds is None:
            delay_seconds = config._parse_duration(config.fixed_delay)

        initial_delay = getattr(config, "_initial_delay_seconds", None)
        if initial_delay is None:
            initial_delay = config._parse_duration(config.initial_delay or "0s", allow_zero=True)

        with self._lock:
            self._delay_tasks[task_id] = {
                "task": task,
                "wrapper": task_wrapper,
                "delay_seconds": delay_seconds,
                "initial_delay": initial_delay,
                "next_execution": None,
                "running": False,
            }

        # 调度初始执行
        initial_run_time = datetime.now(timezone.utc) + timedelta(seconds=initial_delay)

        # 创建初始触发器
        trigger = DateTrigger(run_date=initial_run_time, timezone=self.scheduler_ref.timezone)

        # 创建固定延迟作业函数
        job_func = self._create_fixed_delay_job_func(task_id)

        # 添加到调度器(固定延迟任务始终使用默认执行器)
        self.scheduler_ref._scheduler.add_job(
            func=job_func,
            trigger=trigger,
            id=task_id,
            name=task.name,
            executor="default",  # 固定延迟任务使用默认执行器避免异步问题
            replace_existing=True,
        )

        return task_id

    def _create_fixed_delay_job_func(self, task_id: str) -> Callable:
        """创建固定延迟作业函数"""

        def fixed_delay_job():
            """固定延迟作业执行函数"""
            with self._lock:
                if task_id not in self._delay_tasks:
                    return

                task_info = self._delay_tasks[task_id]
                if task_info["running"]:
                    return  # 避免重复执行

                task_info["running"] = True

            try:
                # 执行任务
                wrapper = task_info["wrapper"]
                # 使用同步版本的监控执行,避免异步问题
                wrapper.execute_with_monitoring_sync()

            finally:
                # 调度下次执行
                with self._lock:
                    if task_id in self._delay_tasks:
                        task_info = self._delay_tasks[task_id]
                        task_info["running"] = False

                        # 计算下次执行时间
                        next_run_time = datetime.now(timezone.utc) + timedelta(seconds=task_info["delay_seconds"])

                        # 重新调度
                        trigger = DateTrigger(run_date=next_run_time, timezone=self.scheduler_ref.timezone)

                        try:
                            self.scheduler_ref._scheduler.modify_job(job_id=task_id, trigger=trigger)
                        except Exception:
                            # 如果修改失败,重新添加作业
                            self.scheduler_ref._scheduler.add_job(
                                func=fixed_delay_job,
                                trigger=trigger,
                                id=task_id,
                                name=task_info["task"].name,
                                executor="default",  # 固定延迟任务使用默认执行器
                                replace_existing=True,
                            )

        return fixed_delay_job

    def remove_fixed_delay_task(self, task_id: str) -> bool:
        """移除固定延迟任务"""
        with self._lock:
            if task_id in self._delay_tasks:
                del self._delay_tasks[task_id]
                return True
            return False

    def get_fixed_delay_tasks(self) -> dict[str, dict[str, Any]]:
        """获取所有固定延迟任务"""
        with self._lock:
            return self._delay_tasks.copy()


class TaskManager:
    """任务管理器 - 提供高级任务管理功能"""

    def __init__(self, scheduler_ref):
        """
        初始化任务管理器

        Args:
            scheduler_ref: 调度器引用
        """
        self.scheduler_ref = scheduler_ref
        self.task_wrappers: dict[str, TaskWrapper] = {}
        self.fixed_delay_handler = FixedDelayTaskHandler(scheduler_ref)
        self._lock = threading.RLock()

    def add_task(self, task: ScheduledTask, max_retries: int = 3, retry_delay: float = 1.0) -> str:
        """
        添加任务到调度器

        Args:
            task: 要添加的任务
            max_retries: 最大重试次数
            retry_delay: 重试延迟(秒)

        Returns:
            任务ID
        """
        # 创建任务包装器
        wrapper = TaskWrapper(task, self.scheduler_ref)
        wrapper.max_retries = max_retries
        wrapper.retry_delay = retry_delay

        with self._lock:
            self.task_wrappers[task.task_id] = wrapper

        try:
            # 根据任务类型选择不同的添加策略
            if task.task_type == TaskType.FIXED_DELAY:
                return self._add_fixed_delay_task(task, wrapper)
            else:
                return self._add_regular_task(task, wrapper)

        except Exception:
            # 如果添加失败,清理包装器
            with self._lock:
                self.task_wrappers.pop(task.task_id, None)
            raise

    def _add_regular_task(self, task: ScheduledTask, wrapper: TaskWrapper) -> str:
        """添加常规任务(非固定延迟)"""
        # 注册任务到调度器的任务注册表
        task_id = self.scheduler_ref.task_registry.register_task(task)

        # 创建触发器
        trigger = self.scheduler_ref._create_trigger(task)

        # 创建作业函数
        job_func = self._create_monitored_job_func(wrapper)

        # 确定执行器
        executor = "asyncio" if task.is_async and self.scheduler_ref.use_asyncio else "default"

        # 添加作业到调度器
        self.scheduler_ref._scheduler.add_job(func=job_func, trigger=trigger, id=task_id, name=task.name, executor=executor, replace_existing=True)

        return task_id

    def _add_fixed_delay_task(self, task: ScheduledTask, wrapper: TaskWrapper) -> str:
        """添加固定延迟任务"""
        # 注册任务到调度器的任务注册表
        self.scheduler_ref.task_registry.register_task(task)

        # 使用固定延迟处理器
        return self.fixed_delay_handler.add_fixed_delay_task(task, wrapper)

    def _create_monitored_job_func(self, wrapper: TaskWrapper) -> Callable:
        """创建带监控的作业函数"""

        async def async_monitored_job():
            """异步版本的带监控作业函数"""
            return await wrapper.execute_with_monitoring()

        def sync_monitored_job():
            """同步版本的带监控作业函数"""
            return wrapper.execute_with_monitoring_sync()

        # 根据任务类型和调度器配置返回相应的函数
        if wrapper.task.is_async and self.scheduler_ref.use_asyncio:
            return async_monitored_job
        else:
            return sync_monitored_job

    def remove_task(self, task_id: str) -> bool:
        """
        移除任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功移除
        """
        try:
            # 从调度器中移除作业
            self.scheduler_ref._scheduler.remove_job(task_id)

            # 从任务注册表中移除
            task = self.scheduler_ref.task_registry.get_task(task_id)
            if task:
                task.mark_cancelled()

            self.scheduler_ref.task_registry.unregister_task(task_id)

            # 清理任务包装器
            with self._lock:
                self.task_wrappers.pop(task_id, None)

            # 如果是固定延迟任务,从处理器中移除
            self.fixed_delay_handler.remove_fixed_delay_task(task_id)

            return True

        except Exception:
            return False

    def metrics(self, task_id: str) -> Optional[dict[str, Any]]:
        """
        获取任务执行指标

        Args:
            task_id: 任务ID

        Returns:
            任务指标字典,如果任务不存在返回None
        """
        with self._lock:
            wrapper = self.task_wrappers.get(task_id)
            if wrapper:
                return wrapper.get_metrics()
            return None

    def all_metrics(self) -> dict[str, dict[str, Any]]:
        """获取所有任务的执行指标"""
        with self._lock:
            return {task_id: wrapper.get_metrics() for task_id, wrapper in self.task_wrappers.items()}

    def create_method(
        self,
        method: Callable,
        instance: Optional[Any] = None,
        config: Optional[ScheduledConfig] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ) -> str:
        """
        创建并添加方法任务

        Args:
            method: 要执行的方法
            instance: 方法所属的实例
            config: 调度配置
            name: 任务名称
            description: 任务描述
            max_retries: 最大重试次数
            retry_delay: 重试延迟

        Returns:
            任务ID
        """
        task = TaskFactory.create_method(method=method, instance=instance, config=config, name=name, description=description)

        return self.add_task(task, max_retries, retry_delay)

    def create_lambda(
        self,
        func: Callable,
        config: Optional[ScheduledConfig] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
        args: tuple = (),
        kwargs: Optional[dict[str, Any]] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ) -> str:
        """
        创建并添加Lambda任务

        Args:
            func: 要执行的函数
            config: 调度配置
            name: 任务名称
            description: 任务描述
            args: 函数参数
            kwargs: 函数关键字参数
            max_retries: 最大重试次数
            retry_delay: 重试延迟

        Returns:
            任务ID
        """
        task = TaskFactory.create_lambda(func=func, config=config, name=name, description=description, args=args, kwargs=kwargs)

        return self.add_task(task, max_retries, retry_delay)

    def from_method(
        self, method: Callable, instance: Optional[Any] = None, max_retries: int = 3, retry_delay: float = 1.0
    ) -> Optional[str]:
        """
        从@Scheduled装饰的方法创建并添加任务

        Args:
            method: 带@Scheduled装饰的方法
            instance: 方法所属的实例
            max_retries: 最大重试次数
            retry_delay: 重试延迟

        Returns:
            任务ID,如果方法没有@Scheduled装饰返回None
        """
        task = TaskFactory.from_method(method, instance)
        if task:
            return self.add_task(task, max_retries, retry_delay)
        return None

    def update_task_config(self, task_id: str, max_retries: Optional[int] = None, retry_delay: Optional[float] = None) -> bool:
        """
        更新任务配置

        Args:
            task_id: 任务ID
            max_retries: 新的最大重试次数
            retry_delay: 新的重试延迟

        Returns:
            是否成功更新
        """
        with self._lock:
            wrapper = self.task_wrappers.get(task_id)
            if not wrapper:
                return False

            if max_retries is not None:
                wrapper.max_retries = max_retries

            if retry_delay is not None:
                wrapper.retry_delay = retry_delay

            return True

    def get_task_summary(self) -> dict[str, Any]:
        """获取任务管理器摘要信息"""
        with self._lock:
            total_tasks = len(self.task_wrappers)

            # 统计各种指标
            total_executions = sum(w.metrics.total_executions for w in self.task_wrappers.values())
            total_successes = sum(w.metrics.successful_executions for w in self.task_wrappers.values())
            total_failures = sum(w.metrics.failed_executions for w in self.task_wrappers.values())

            # 计算平均成功率
            avg_success_rate = 0.0
            if total_tasks > 0:
                success_rates = [w.metrics.get_success_rate() for w in self.task_wrappers.values()]
                avg_success_rate = sum(success_rates) / len(success_rates)

            return {
                "total_managed_tasks": total_tasks,
                "total_executions": total_executions,
                "total_successes": total_successes,
                "total_failures": total_failures,
                "average_success_rate": avg_success_rate,
                "fixed_delay_tasks": len(self.fixed_delay_handler.get_fixed_delay_tasks()),
            }

    def clear_all_tasks(self) -> None:
        """清空所有管理的任务"""
        with self._lock:
            # 清空任务包装器
            self.task_wrappers.clear()

            # 清空固定延迟任务
            self.fixed_delay_handler._delay_tasks.clear()
