# Ruff 配置文件 - Mini-Boot 项目代码质量标准
# 行长度限制
line-length = 150

# Python 版本
target-version = "py39"

# 排除的文件和目录
exclude = [
    ".git",
    ".venv",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
]

[lint]
# 启用的检查规则
select = [
    # Pyflakes
    "F",
    # pycodestyle
    "E",
    "W",
    # isort
    "I",
    # pep8-naming
    "N",
    # pyupgrade (包括类型注解现代化)
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # flake8-comprehensions
    "C4",
    # flake8-pie
    "PIE",
    # flake8-unused-arguments
    "ARG",
    # flake8-use-pathlib
    "PTH",
]

# 忽略的规则
ignore = [
    # 行长度由格式化工具处理
    "E501",
    # 允许在 __init__.py 中使用 import *
    "F403",
    "F405",
    # 允许抽象方法中的 pass 语句
    "PIE790",
    # 允许函数内部导入
    "E402",
    # 允许UTF-8编码声明（解决IDE中文字符告警）
    "UP009",
    # 忽略导入排序问题（由格式化工具处理）
    "I001",
    # 允许注解函数使用大写开头（Spring Boot风格）
    "N802",
]

# 每个文件的最大复杂度
mccabe = { max-complexity = 10 }

[lint.per-file-ignores]
# 测试文件可以有更宽松的规则
"tests/**/*.py" = [
    "ARG001",  # 测试函数可能有未使用的参数
    "S101",    # 允许使用 assert
]

# 代码质量检查工具本身不需要检查
"tests/testutils/code_quality.py" = [
    "F601",    # 允许重复的字典键（中文标点符号映射表需要）
]

# 事件异常类允许使用Exception后缀
"miniboot/events/exceptions.py" = [
    "N818",    # 允许EventException命名（基础异常类）
]

# __init__.py 文件的特殊规则
"**/__init__.py" = [
    "F401",    # 允许未使用的导入（用于重新导出）
]

[lint.isort]
# 导入排序配置
known-first-party = ["miniboot"]
force-single-line = false
lines-after-imports = 2

[format]
# 格式化配置
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# 文档字符串格式化
docstring-code-format = true
docstring-code-line-length = 80
