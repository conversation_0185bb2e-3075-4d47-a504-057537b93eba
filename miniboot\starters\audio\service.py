#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 统一音频服务实现
"""

import time
from pathlib import Path
from typing import Union

from .exceptions import AudioException
from .models import AudioItem, AudioItemType
from .players import ChineseTTSPlayer, MP3Player
from .properties import AudioProperties


class AudioService:
    """统一的音频服务

    整合音频文件播放和TTS功能,提供统一的服务接口.
    """

    def __init__(self, properties: AudioProperties):
        self.properties = properties
        self.audio_player = None
        self.tts_player = None
        self._initialized = False

    async def initialize(self) -> None:
        """初始化音频服务"""
        if self._initialized:
            return

        # 初始化音频播放器
        if self.properties.player.enabled:
            self.audio_player = MP3Player(volume=self.properties.player.volume, timeout_seconds=self.properties.player.timeout_seconds)

        # 初始化TTS播放器
        if self.properties.tts.enabled:
            self.tts_player = ChineseTTSPlayer(
                rate=self.properties.tts.rate, volume=self.properties.tts.volume, language=self.properties.tts.language
            )

        self._initialized = True

    # ==================== 音频文件播放 ====================
    async def play_file(self, file_path: str, **kwargs) -> bool:
        """播放音频文件

        Args:
            file_path: 音频文件路径
            **kwargs: 额外参数

        Returns:
            bool: 播放是否成功
        """
        if not self.properties.player.enabled:
            return False

        await self.initialize()

        if not self.audio_player:
            raise AudioException("Audio player not initialized")

        return self.audio_player.play(file_path)

    async def play_files(self, file_paths: list[str], interval: float = 0.1) -> bool:
        """连续播放多个音频文件

        Args:
            file_paths: 音频文件路径列表
            interval: 播放间隔(秒)

        Returns:
            bool: 是否全部播放成功
        """
        if not self.properties.player.enabled:
            return False

        await self.initialize()

        if not self.audio_player:
            raise AudioException("Audio player not initialized")

        return self.audio_player.play_multiple(file_paths, interval)

    # ==================== 文本转语音 ====================
    async def speak(self, text: str, **kwargs) -> bool:
        """播放文本语音

        Args:
            text: 要播放的文本
            **kwargs: 额外参数(如语速、音量等)

        Returns:
            bool: 播放是否成功
        """
        if not self.properties.tts.enabled:
            return False

        await self.initialize()

        if not self.tts_player:
            raise AudioException("TTS player not initialized")

        return self.tts_player.speak(text)

    async def speak_texts(self, texts: list[str], interval: float = 0.2) -> bool:
        """连续播放多段文本语音

        Args:
            texts: 文本列表
            interval: 播放间隔(秒)

        Returns:
            bool: 是否全部播放成功
        """
        if not self.properties.tts.enabled:
            return False

        await self.initialize()

        if not self.tts_player:
            raise AudioException("TTS player not initialized")

        return self.tts_player.speak_multiple(texts, interval)

    # ==================== 混合播放 ====================
    async def play_sequence(self, items: list[Union[str, AudioItem]], interval: float = 0.1) -> bool:
        """播放混合序列(音频文件 + 文本语音)

        Args:
            items: 播放项目列表,可以是文件路径字符串或AudioItem对象
            interval: 播放间隔(秒)

        Returns:
            bool: 是否全部播放成功
        """
        await self.initialize()

        success_count = 0
        total_count = len(items)

        for i, item in enumerate(items, 1):
            success = False

            if isinstance(item, str):
                # 字符串:判断是文件路径还是文本
                if self._is_audio_file(item):
                    success = await self.play_file(item)
                else:
                    success = await self.speak(item)
            elif isinstance(item, AudioItem):
                # AudioItem对象
                if item.type == AudioItemType.FILE:
                    success = await self.play_file(item.content)
                elif item.type == AudioItemType.TEXT:
                    success = await self.speak(item.content)

            if success:
                success_count += 1

            # 播放间隔
            if i < total_count and interval > 0:
                time.sleep(interval)

        return success_count == total_count

    # ==================== 控制方法 ====================
    async def stop(self) -> None:
        """停止所有播放"""
        # 停止音频播放
        if self.audio_player:
            self.audio_player.stop()

        # 停止TTS播放(pyttsx3通常是同步的,可能需要特殊处理)
        # TTS播放通常在speak方法中已经完成,这里主要是为了接口完整性
        pass

    def is_audio_enabled(self) -> bool:
        """检查音频播放是否启用"""
        return self.properties.player.enabled

    def is_tts_enabled(self) -> bool:
        """检查TTS是否启用"""
        return self.properties.tts.enabled

    def set_audio_volume(self, volume: float) -> None:
        """设置音频播放音量"""
        if 0.0 <= volume <= 1.0:
            self.properties.player.volume = volume
            if self.audio_player:
                self.audio_player.set_volume(volume)

    def set_tts_properties(self, rate: int = None, volume: float = None) -> None:
        """设置TTS属性"""
        if rate is not None:
            self.properties.tts.rate = rate
        if volume is not None and 0.0 <= volume <= 1.0:
            self.properties.tts.volume = volume

        if self.tts_player:
            self.tts_player.set_properties(rate, volume)

    # ==================== 辅助方法 ====================
    def _is_audio_file(self, path: str) -> bool:
        """判断是否为音频文件路径"""
        try:
            file_path = Path(path)

            # 检查文件扩展名
            if file_path.suffix.lower() in self.properties.player.supported_formats:
                return True

            # 检查文件是否存在
            return bool(file_path.exists() and file_path.is_file())
        except Exception:
            # 如果路径解析失败,认为是文本
            return False
