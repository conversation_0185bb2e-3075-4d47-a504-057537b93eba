"""类型转换模块

提供灵活的类型转换机制,支持自定义转换器注册和链式转换.
"""

from .converter import ConversionError, ConversionService, Converter
from .duration import (StrToDurationConverter, StrToFloatCompositeConverter,
                       StrToPercentageConverter, StrToSizeConverter)
from .service import DefaultConversionService
from .strings import (FloatToStringConverter, IntToFloatConverter,
                      StrToBoolConverter, StrToFloatConverter,
                      StrToIntConverter, StrToListConverter)
from .types import TypeDescriptor

__all__ = [
    # 核心接口
    "Converter", "ConversionService", "ConversionError", "DefaultConversionService", "TypeDescriptor",
    # 字符串转换器
    "StrToBoolConverter", "StrToIntConverter", "StrToFloatConverter", "StrToListConverter",
    "IntToFloatConverter", "FloatToStringConverter",
    # 时间间隔和大小转换器
    "StrToDurationConverter", "StrToSizeConverter", "StrToPercentageConverter", "StrToFloatCompositeConverter"
]
