#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 响应处理模块 - 提供统一的API响应格式和响应处理功能

Web 响应处理模块

提供统一的API响应格式和响应处理功能。

主要功能:
- ApiResponse - 统一API响应格式
- BusinessError - 业务异常类
- ValidationError - 校验异常类
- 响应构建工具方法
- 异常处理工具
"""

import json
import traceback
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Optional, Union


class ResponseStatus(Enum):
    """响应状态枚举"""

    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ErrorType(Enum):
    """错误类型枚举"""

    BUSINESS_ERROR = "business_error"
    VALIDATION_ERROR = "validation_error"
    SYSTEM_ERROR = "system_error"
    NETWORK_ERROR = "network_error"
    PERMISSION_ERROR = "permission_error"
    RESOURCE_ERROR = "resource_error"
    TIMEOUT_ERROR = "timeout_error"


class ApiResponse:
    """统一API响应格式

    提供标准化的API响应结构,包含状态码、消息和数据。
    支持更丰富的响应信息和元数据。
    """

    def __init__(
        self,
        success: bool,
        code: int,
        message: str,
        data: Optional[Any] = None,
        timestamp: Optional[str] = None,
        error_code: Optional[str] = None,
        error_type: Optional[ErrorType] = None,
        request_id: Optional[str] = None,
        status: Optional[ResponseStatus] = None,
        metadata: Optional[dict[str, Any]] = None,
        errors: Optional[list[dict[str, Any]]] = None,
    ):
        """初始化API响应

        Args:
            success: 是否成功
            code: HTTP状态码
            message: 响应消息
            data: 响应数据
            timestamp: 时间戳
            error_code: 错误代码
            error_type: 错误类型
            request_id: 请求ID
            status: 响应状态
            metadata: 元数据
            errors: 错误详情列表
        """
        self.is_success = success
        self.code = code
        self.message = message
        self.data = data
        self.timestamp = timestamp or datetime.now().isoformat()
        self.error_code = error_code
        self.error_type = error_type
        self.request_id = request_id or str(uuid.uuid4())
        self.status = status or (ResponseStatus.SUCCESS if self.is_success else ResponseStatus.ERROR)
        self.metadata = metadata or {}
        self.errors = errors or []

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式

        Returns:
            响应数据的字典表示
        """
        result = {
            "success": self.is_success,
            "code": self.code,
            "message": self.message,
            "timestamp": self.timestamp,
            "request_id": self.request_id,
            "status": self.status.value if self.status else None,
        }

        if self.data is not None:
            result["data"] = self.data

        if self.error_code:
            result["error_code"] = self.error_code

        if self.error_type:
            result["error_type"] = self.error_type.value

        if self.errors:
            result["errors"] = self.errors

        if self.metadata:
            result["metadata"] = self.metadata

        return result

    def json(self) -> str:
        """转换为JSON字符串

        Returns:
            响应数据的JSON字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, default=str)

    @classmethod
    def success(
        cls,
        data: Optional[Any] = None,
        message: str = "操作成功",
        code: int = 200,
        request_id: Optional[str] = None,
        metadata: Optional[dict[str, Any]] = None,
    ) -> "ApiResponse":
        """创建成功响应

        Args:
            data: 响应数据
            message: 响应消息
            code: 状态码
            request_id: 请求ID
            metadata: 元数据

        Returns:
            成功响应对象
        """
        return cls(success=True, code=code, message=message, data=data, request_id=request_id, status=ResponseStatus.SUCCESS, metadata=metadata)

    @classmethod
    def error(
        cls,
        message: str = "操作失败",
        code: int = 500,
        data: Optional[Any] = None,
        error_code: Optional[str] = None,
        error_type: Optional[ErrorType] = None,
        request_id: Optional[str] = None,
        errors: Optional[list[dict[str, Any]]] = None,
        metadata: Optional[dict[str, Any]] = None,
    ) -> "ApiResponse":
        """创建错误响应

        Args:
            message: 错误消息
            code: 错误码
            data: 错误详细数据
            error_code: 错误代码
            error_type: 错误类型
            request_id: 请求ID
            errors: 错误详情列表
            metadata: 元数据

        Returns:
            错误响应对象
        """
        return cls(
            success=False,
            code=code,
            message=message,
            data=data,
            error_code=error_code,
            error_type=error_type or ErrorType.SYSTEM_ERROR,
            request_id=request_id,
            status=ResponseStatus.ERROR,
            errors=errors,
            metadata=metadata,
        )

    @classmethod
    def validation_error(
        cls,
        errors: Union[Any, list[dict[str, Any]]],
        message: str = "参数校验失败",
        request_id: Optional[str] = None,
        metadata: Optional[dict[str, Any]] = None,
    ) -> "ApiResponse":
        """创建参数校验错误响应

        Args:
            errors: 校验错误详情
            message: 错误消息
            request_id: 请求ID
            metadata: 元数据

        Returns:
            校验错误响应对象
        """
        # 标准化错误格式
        if isinstance(errors, list):
            error_list = errors
        elif isinstance(errors, dict):
            error_list = [{"field": k, "message": v} for k, v in errors.items()]
        else:
            error_list = [{"message": str(errors)}]

        return cls.error(
            message=message,
            code=400,
            error_code="VALIDATION_ERROR",
            error_type=ErrorType.VALIDATION_ERROR,
            request_id=request_id,
            errors=error_list,
            metadata=metadata,
        )

    @classmethod
    def not_found(cls, message: str = "资源不存在", request_id: Optional[str] = None) -> "ApiResponse":
        """创建资源不存在响应

        Args:
            message: 错误消息
            request_id: 请求ID

        Returns:
            404错误响应对象
        """
        return cls.error(message=message, code=404, error_code="RESOURCE_NOT_FOUND", error_type=ErrorType.RESOURCE_ERROR, request_id=request_id)

    @classmethod
    def unauthorized(cls, message: str = "未授权访问", request_id: Optional[str] = None) -> "ApiResponse":
        """创建未授权响应

        Args:
            message: 错误消息
            request_id: 请求ID

        Returns:
            401错误响应对象
        """
        return cls.error(message=message, code=401, error_code="UNAUTHORIZED", error_type=ErrorType.PERMISSION_ERROR, request_id=request_id)

    @classmethod
    def forbidden(cls, message: str = "禁止访问", request_id: Optional[str] = None) -> "ApiResponse":
        """创建禁止访问响应

        Args:
            message: 错误消息
            request_id: 请求ID

        Returns:
            403错误响应对象
        """
        return cls.error(message=message, code=403, error_code="FORBIDDEN", error_type=ErrorType.PERMISSION_ERROR, request_id=request_id)

    @classmethod
    def internal_error(cls, message: str = "服务器内部错误", request_id: Optional[str] = None) -> "ApiResponse":
        """创建服务器内部错误响应

        Args:
            message: 错误消息
            request_id: 请求ID

        Returns:
            500错误响应对象
        """
        return cls.error(message=message, code=500, error_code="INTERNAL_ERROR", error_type=ErrorType.SYSTEM_ERROR, request_id=request_id)

    @classmethod
    def timeout_error(cls, message: str = "请求超时", request_id: Optional[str] = None) -> "ApiResponse":
        """创建超时错误响应

        Args:
            message: 错误消息
            request_id: 请求ID

        Returns:
            超时错误响应对象
        """
        return cls.error(message=message, code=408, error_code="TIMEOUT_ERROR", error_type=ErrorType.TIMEOUT_ERROR, request_id=request_id)

    @classmethod
    def business_error(
        cls, message: str, error_code: str = "BUSINESS_ERROR", code: int = 400, data: Optional[Any] = None, request_id: Optional[str] = None
    ) -> "ApiResponse":
        """创建业务错误响应

        Args:
            message: 错误消息
            error_code: 错误代码
            code: HTTP状态码
            data: 错误数据
            request_id: 请求ID

        Returns:
            业务错误响应对象
        """
        return cls.error(message=message, code=code, error_code=error_code, error_type=ErrorType.BUSINESS_ERROR, data=data, request_id=request_id)


class BusinessError(Exception):
    """业务异常类

    用于表示业务逻辑错误，会被全局异常处理器捕获并转换为API响应。
    支持更丰富的错误信息和上下文。
    """

    def __init__(
        self,
        message: str = "业务处理失败",
        error_code: str = "BUSINESS_ERROR",
        code: int = 400,
        data: Optional[Any] = None,
        context: Optional[dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        """初始化业务异常

        Args:
            message: 错误消息
            error_code: 错误代码
            code: HTTP状态码
            data: 错误详细数据
            context: 错误上下文信息
            cause: 引起异常的原因
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.code = code
        self.data = data
        self.context = context or {}
        self.cause = cause
        self.timestamp = datetime.now().isoformat()

    def to_response(self, request_id: Optional[str] = None) -> ApiResponse:
        """转换为API响应

        Args:
            request_id: 请求ID

        Returns:
            API响应对象
        """
        metadata = {"timestamp": self.timestamp, "context": self.context}

        if self.cause:
            metadata["cause"] = str(self.cause)

        return ApiResponse.business_error(message=self.message, error_code=self.error_code, code=self.code, data=self.data, request_id=request_id)

    def add_context(self, key: str, value: Any) -> "BusinessError":
        """添加上下文信息

        Args:
            key: 上下文键
            value: 上下文值

        Returns:
            自身实例，支持链式调用
        """
        self.context[key] = value
        return self


class ValidationError(Exception):
    """参数校验异常类

    用于表示参数校验错误，支持详细的字段级错误信息。
    """

    def __init__(
        self,
        message: str = "参数校验失败",
        validation_details: Optional[Union[dict[str, Any], list[dict[str, Any]]]] = None,
        field: Optional[str] = None,
        value: Optional[Any] = None,
    ):
        """初始化校验异常

        Args:
            message: 错误消息
            validation_details: 校验错误详情
            field: 错误字段名
            value: 错误字段值
        """
        super().__init__(message)
        self.message = message
        self.validation_details = validation_details or []
        self.field = field
        self.value = value
        self.timestamp = datetime.now().isoformat()

        # 为了兼容性，保留errors属性
        self.errors = validation_details

    def to_response(self, request_id: Optional[str] = None) -> ApiResponse:
        """转换为API响应

        Args:
            request_id: 请求ID

        Returns:
            API响应对象
        """
        metadata = {"timestamp": self.timestamp}

        if self.field:
            metadata["field"] = self.field
        if self.value is not None:
            metadata["value"] = self.value

        return ApiResponse.validation_error(errors=self.validation_details, message=self.message, request_id=request_id, metadata=metadata)

    def add_field_error(self, field: str, message: str, value: Optional[Any] = None) -> "ValidationError":
        """添加字段错误

        Args:
            field: 字段名
            message: 错误消息
            value: 字段值

        Returns:
            自身实例，支持链式调用
        """
        if not isinstance(self.validation_details, list):
            self.validation_details = []

        error_info = {"field": field, "message": message}
        if value is not None:
            error_info["value"] = value

        self.validation_details.append(error_info)
        self.errors = self.validation_details
        return self


class SystemError(Exception):
    """系统异常类

    用于表示系统级错误，如数据库连接失败、外部服务不可用等。
    """

    def __init__(
        self,
        message: str = "系统错误",
        error_code: str = "SYSTEM_ERROR",
        code: int = 500,
        cause: Optional[Exception] = None,
        context: Optional[dict[str, Any]] = None,
    ):
        """初始化系统异常

        Args:
            message: 错误消息
            error_code: 错误代码
            code: HTTP状态码
            cause: 引起异常的原因
            context: 错误上下文
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.code = code
        self.cause = cause
        self.context = context or {}
        self.timestamp = datetime.now().isoformat()

    def to_response(self, request_id: Optional[str] = None) -> ApiResponse:
        """转换为API响应

        Args:
            request_id: 请求ID

        Returns:
            API响应对象
        """
        metadata = {"timestamp": self.timestamp, "context": self.context}

        if self.cause:
            metadata["cause"] = str(self.cause)
            metadata["cause_type"] = type(self.cause).__name__

        return ApiResponse.error(
            message=self.message,
            code=self.code,
            error_code=self.error_code,
            error_type=ErrorType.SYSTEM_ERROR,
            request_id=request_id,
            metadata=metadata,
        )


class ExceptionHandler:
    """异常处理工具类

    提供统一的异常处理和响应转换功能。
    """

    @staticmethod
    def handle_exception(exception: Exception, request_id: Optional[str] = None, include_traceback: bool = False) -> ApiResponse:
        """处理异常并转换为API响应

        Args:
            exception: 异常对象
            request_id: 请求ID
            include_traceback: 是否包含堆栈跟踪

        Returns:
            API响应对象
        """
        # 业务异常
        if isinstance(exception, BusinessError):
            return exception.to_response(request_id)

        # 校验异常
        if isinstance(exception, ValidationError):
            return exception.to_response(request_id)

        # 系统异常
        if isinstance(exception, SystemError):
            return exception.to_response(request_id)

        # HTTP异常（FastAPI）
        if hasattr(exception, "status_code") and hasattr(exception, "detail"):
            return ApiResponse.error(
                message=str(getattr(exception, "detail", "HTTP错误")),
                code=getattr(exception, "status_code", 500),
                error_code="HTTP_ERROR",
                error_type=ErrorType.SYSTEM_ERROR,
                request_id=request_id,
            )

        # 其他异常
        metadata = {"exception_type": type(exception).__name__, "timestamp": datetime.now().isoformat()}

        if include_traceback:
            metadata["traceback"] = traceback.format_exc()

        return ApiResponse.internal_error(message=str(exception) or "未知错误", request_id=request_id)

    @staticmethod
    def create_error_context(
        request_path: Optional[str] = None,
        request_method: Optional[str] = None,
        user_id: Optional[str] = None,
        additional_info: Optional[dict[str, Any]] = None,
    ) -> dict[str, Any]:
        """创建错误上下文信息

        Args:
            request_path: 请求路径
            request_method: 请求方法
            user_id: 用户ID
            additional_info: 额外信息

        Returns:
            错误上下文字典
        """
        context = {"timestamp": datetime.now().isoformat(), "request_id": str(uuid.uuid4())}

        if request_path:
            context["request_path"] = request_path
        if request_method:
            context["request_method"] = request_method
        if user_id:
            context["user_id"] = user_id
        if additional_info:
            context.update(additional_info)

        return context
