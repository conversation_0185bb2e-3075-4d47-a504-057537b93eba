#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean注册表系统
"""

import threading

from loguru import logger

from .base import BeanDefinitionRegistry
from .definition import BeanDefinition


class DefaultBeanDefinitionRegistry(BeanDefinitionRegistry):
    """默认Bean定义注册表实现

    提供线程安全的Bean定义存储和管理功能。
    使用字典存储Bean定义，支持高效的查询和操作。
    """

    def __init__(self, enable_thread_safety: bool = True):
        """初始化Bean定义注册表

        Args:
            enable_thread_safety: 是否启用线程安全，默认为True
        """
        self._bean_definitions: dict[str, BeanDefinition] = {}
        self._lock = threading.RLock() if enable_thread_safety else None

        # 统计信息
        self._registration_count = 0
        self._removal_count = 0

    def register(self, name: str, bean_definition: BeanDefinition) -> None:
        """注册Bean定义

        Args:
            name: Bean名称
            bean_definition: Bean定义对象

        Raises:
            BeanDefinitionError: 如果Bean定义无效
            IllegalArgumentError: 如果参数无效
        """
        self._validate_registration_args(name, bean_definition)

        if self._lock:
            with self._lock:
                self._register_bean_definition_internal(name, bean_definition)
        else:
            self._register_bean_definition_internal(name, bean_definition)

    def _validate_registration_args(self, name: str, bean_definition: BeanDefinition) -> None:
        """验证注册参数"""
        if not name or not name.strip():
            raise ValueError("Bean name cannot be empty")

        if not bean_definition:
            raise ValueError("Bean definition cannot be None")

        if bean_definition.bean_name != name:
            raise ValueError(f"Bean definition name '{bean_definition.bean_name}' does not match registration name '{name}'")

    def _register_bean_definition_internal(self, name: str, bean_definition: BeanDefinition) -> None:
        """内部注册Bean定义方法（无锁版本）"""
        # 检查是否已存在（允许覆盖）
        is_override = name in self._bean_definitions

        self._bean_definitions[name] = bean_definition
        self._registration_count += 1

        # 记录Bean注册信息
        if is_override:
            logger.warning(f"Overriding existing bean definition: {name} -> {bean_definition.bean_class.__name__}")
        else:
            logger.debug(f"Registered new bean definition: {name} -> {bean_definition.bean_class.__name__}")

    def get_definition(self, name: str) -> BeanDefinition:
        """获取Bean定义

        Args:
            name: Bean名称

        Returns:
            BeanDefinition: Bean定义对象

        Raises:
            NoSuchBeanDefinitionError: 如果Bean定义不存在
        """
        if not name or not name.strip():
            raise ValueError("Bean name cannot be empty")

        if self._lock:
            with self._lock:
                return self._get_bean_definition_internal(name)
        else:
            return self._get_bean_definition_internal(name)

    def _get_bean_definition_internal(self, name: str) -> BeanDefinition:
        """内部获取Bean定义方法（无锁版本）"""
        if name not in self._bean_definitions:
            logger.warning(f"Bean definition not found: {name}")
            raise KeyError(f"No bean definition found for name: {name}")

        return self._bean_definitions[name]

    def has_definition(self, name: str) -> bool:
        """检查Bean定义是否存在

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean定义存在返回True，否则返回False
        """
        if not name:
            return False

        if self._lock:
            with self._lock:
                return name in self._bean_definitions
        else:
            return name in self._bean_definitions

    def names(self) -> list[str]:
        """获取所有Bean定义名称

        Returns:
            List[str]: Bean定义名称列表，如果没有定义则返回空列表
        """
        if self._lock:
            with self._lock:
                return list(self._bean_definitions.keys())
        else:
            return list(self._bean_definitions.keys())

    def get_bean_names(self) -> list[str]:
        """获取所有Bean定义名称

        Returns:
            List[str]: Bean定义名称列表
        """
        return self.names()

    def get_bean_definition_names(self) -> list[str]:
        """获取所有Bean定义名称 - 兼容性方法

        这是names方法的别名，用于向后兼容。

        Returns:
            List[str]: Bean定义名称列表
        """
        return self.names()

    def get_bean_definition_count(self) -> int:
        """获取Bean定义数量 - 兼容性方法

        这是count方法的别名，用于向后兼容。

        Returns:
            int: Bean定义的总数量
        """
        return self.count()

    def remove(self, name: str) -> None:
        """移除Bean定义

        Args:
            name: Bean名称

        Raises:
            NoSuchBeanDefinitionError: 如果Bean定义不存在
        """
        if not name or not name.strip():
            raise ValueError("Bean name cannot be empty")

        if self._lock:
            with self._lock:
                self._remove_bean_definition_internal(name)
        else:
            self._remove_bean_definition_internal(name)

    def _remove_bean_definition_internal(self, name: str) -> None:
        """内部移除Bean定义方法（无锁版本）"""
        if name not in self._bean_definitions:
            logger.warning(f"Attempted to remove non-existent bean definition: {name}")
            raise KeyError(f"No bean definition found for name: {name}")

        bean_definition = self._bean_definitions[name]
        del self._bean_definitions[name]
        self._removal_count += 1
        logger.debug(f"Removed bean definition: {name} -> {bean_definition.bean_class.__name__}")

    def count(self) -> int:
        """获取Bean定义数量

        Returns:
            int: Bean定义的总数量
        """
        if self._lock:
            with self._lock:
                return len(self._bean_definitions)
        else:
            return len(self._bean_definitions)

    def find_by_type(self, bean_type: type) -> dict[str, BeanDefinition]:
        """根据类型查找Bean定义

        Args:
            bean_type: Bean类型

        Returns:
            Dict[str, BeanDefinition]: Bean名称到Bean定义的映射
        """
        if not bean_type:
            return {}

        if self._lock:
            with self._lock:
                return self._find_bean_definitions_by_type_internal(bean_type)
        else:
            return self._find_bean_definitions_by_type_internal(bean_type)

    def _find_bean_definitions_by_type_internal(self, bean_type: type) -> dict[str, BeanDefinition]:
        """内部按类型查找Bean定义方法（无锁版本）"""
        result = {}
        for name, definition in self._bean_definitions.items():
            # 检查精确匹配或子类匹配
            try:
                if definition.bean_class == bean_type or issubclass(definition.bean_class, bean_type):
                    result[name] = definition
            except TypeError:
                # 处理某些类型无法进行issubclass检查的情况
                if definition.bean_class == bean_type:
                    result[name] = definition

        return result

    def find_primary_by_type(self, bean_type: type) -> dict[str, BeanDefinition]:
        """根据类型查找主要Bean定义

        Args:
            bean_type: Bean类型

        Returns:
            Dict[str, BeanDefinition]: 主要Bean名称到Bean定义的映射
        """
        all_definitions = self.find_bean_definitions_by_type(bean_type)
        return {name: definition for name, definition in all_definitions.items() if definition.is_primary()}

    def singleton_names(self) -> list[str]:
        """获取所有单例Bean名称

        Returns:
            List[str]: 单例Bean名称列表
        """
        if self._lock:
            with self._lock:
                return self._get_singleton_bean_names_internal()
        else:
            return self._get_singleton_bean_names_internal()

    def _get_singleton_bean_names_internal(self) -> list[str]:
        """内部获取单例Bean名称方法（无锁版本）"""
        return [name for name, definition in self._bean_definitions.items() if definition.singleton()]

    def prototype_names(self) -> list[str]:
        """获取所有原型Bean名称

        Returns:
            List[str]: 原型Bean名称列表
        """
        if self._lock:
            with self._lock:
                return self._get_prototype_bean_names_internal()
        else:
            return self._get_prototype_bean_names_internal()

    def _get_prototype_bean_names_internal(self) -> list[str]:
        """内部获取原型Bean名称方法（无锁版本）"""
        return [name for name, definition in self._bean_definitions.items() if definition.prototype()]

    def get_by_names(self, bean_names: list[str]) -> dict[str, BeanDefinition]:
        """根据名称列表批量获取Bean定义

        Args:
            bean_names: Bean名称列表

        Returns:
            Dict[str, BeanDefinition]: Bean名称到Bean定义的映射

        Raises:
            NoSuchBeanDefinitionError: 如果任何Bean定义不存在
        """
        if not bean_names:
            return {}

        if self._lock:
            with self._lock:
                return self._get_bean_definitions_by_names_internal(bean_names)
        else:
            return self._get_bean_definitions_by_names_internal(bean_names)

    def _get_bean_definitions_by_names_internal(self, bean_names: list[str]) -> dict[str, BeanDefinition]:
        """内部批量获取Bean定义方法（无锁版本）"""
        result = {}
        for bean_name in bean_names:
            if bean_name not in self._bean_definitions:
                raise KeyError(f"No bean definition found for name: {bean_name}")
            result[bean_name] = self._bean_definitions[bean_name]

        return result

    def clear(self) -> None:
        """清空所有Bean定义"""
        if self._lock:
            with self._lock:
                self._clear_internal()
        else:
            self._clear_internal()

    def _clear_internal(self) -> None:
        """内部清空方法（无锁版本）"""
        self._bean_definitions.clear()
        self._registration_count = 0
        self._removal_count = 0

    def stats(self) -> dict[str, int]:
        """获取注册表统计信息

        Returns:
            Dict[str, int]: 统计信息，包含注册次数、移除次数等
        """
        if self._lock:
            with self._lock:
                return self._get_registry_stats_internal()
        else:
            return self._get_registry_stats_internal()

    def _get_registry_stats_internal(self) -> dict[str, int]:
        """内部获取统计信息方法（无锁版本）"""
        return {
            "total_definitions": len(self._bean_definitions),
            "singleton_count": len(self._get_singleton_bean_names_internal()),
            "prototype_count": len(self._get_prototype_bean_names_internal()),
            "registration_count": self._registration_count,
            "removal_count": self._removal_count,
        }

    # 魔术方法支持
    def __len__(self) -> int:
        """获取Bean定义数量"""
        return self.get_bean_definition_count()

    def __contains__(self, bean_name: str) -> bool:
        """检查是否包含指定Bean定义"""
        return self.contains_bean_definition(bean_name)

    def __iter__(self):
        """迭代Bean名称"""
        return iter(self.get_bean_definition_names())

    def __str__(self) -> str:
        """字符串表示"""
        count = self.get_bean_definition_count()
        return f"DefaultBeanDefinitionRegistry(count={count})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        names = self.get_bean_definition_names()
        return f"DefaultBeanDefinitionRegistry(bean_names={names})"
