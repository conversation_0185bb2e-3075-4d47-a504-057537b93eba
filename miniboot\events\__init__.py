"""
事件系统模块

提供 Mini-Boot 框架的事件发布、监听和处理功能.

主要功能:
- 事件基类 (Event) - 事件类型定义
- 应用事件基类 (ApplicationEvent) - 应用级事件支持
- 内置事件类型 - 应用生命周期和Bean生命周期事件
- 事件发布器 (EventPublisher) - 支持同步和异步事件发布
- 事件监听器 (@EventListener) - 事件监听器注册
- 事件总线 (EventBus) - 事件路由和分发机制
"""

# 导入异常类
from ..errors import EventHandlerError
from ..errors import EventHandlerExecutionError as AsyncEventExecutionError
from ..errors import EventListenerRegistrationError
from ..errors import EventPublishError
from ..errors import EventPublishError as EventException
from ..errors import EventPublishError as EventPublisherShutdownError
from ..errors import EventTypeError
from ..errors import EventTypeError as ConditionParseError
from ..errors import EventTypeError as EventAnnotationError
# 导入事件基类
from .base import ApplicationEvent, Event, EventIdGenerator
# 导入新的组合式事件系统
from .composite import CompositeEvent
from .errors import (DefaultEventErrorHandler, ErrorHandler, ErrorRecord,
                     ErrorSeverity, EventErrorCollector, RecoveryStrategy)
# Bean集成功能已移至 processor.event 模块
from .properties import (ErrorHandlingConfig, EventsProperties, ListenerConfig,
                         PerformanceConfig, PublisherConfig, SecurityConfig)
from .publisher import (ApplicationEventPublisher, EventHandlerInfo,
                        EventPublisher)
# 导入事件类型和具体事件实现
from .types import (ApplicationFailedEvent, ApplicationReadyEvent,
                    ApplicationStartedEvent, ApplicationStoppedEvent,
                    ApplicationStoppingEvent, AsyncBeanCreationEvent,
                    BeanCreatedEvent, BeanDestroyedEvent, BeanInitializedEvent,
                    EventTypes)

# 导出所有公共接口
__all__ = [
    # 事件基类
    "Event",
    "ApplicationEvent",
    # 事件类型常量
    "EventTypes",
    # 组合式事件系统
    "CompositeEvent",
    # 具体事件类（基于组合式事件）
    "ApplicationStartedEvent",
    "ApplicationStoppedEvent",
    "ApplicationStoppingEvent",
    "ApplicationReadyEvent",
    "ApplicationFailedEvent",
    "BeanCreatedEvent",
    "BeanDestroyedEvent",
    "BeanInitializedEvent",
    "AsyncBeanCreationEvent",
    # ID生成器
    "EventIdGenerator",
    # 事件发布器
    "EventHandlerInfo",
    "EventPublisher",
    "ApplicationEventPublisher",
    # 配置类
    "EventsProperties",
    "PublisherConfig",
    "ListenerConfig",
    "ErrorHandlingConfig",
    "PerformanceConfig",
    "SecurityConfig",
    # Bean集成功能已移至 processor.event 模块
    # 异常类
    "EventException",
    "EventHandlerError",
    "EventPublishError",
    "ConditionParseError",
    "EventListenerRegistrationError",
    "EventTypeError",
    "EventPublisherShutdownError",
    "AsyncEventExecutionError",
    "EventAnnotationError",
    # 错误处理
    "ErrorSeverity",
    "RecoveryStrategy",
    "ErrorRecord",
    "ErrorHandler",
    "DefaultEventErrorHandler",
    "EventErrorCollector",
]
