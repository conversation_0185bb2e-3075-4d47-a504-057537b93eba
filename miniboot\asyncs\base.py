#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 异步组件基类 - 提供通用的初始化检查和生命周期管理
"""

import functools
import threading
from abc import ABC, abstractmethod
from typing import Any, Callable, Optional

from loguru import logger

from ..utils import SingletonMeta


def async_singleton_manager(reset_method_name: str = "reset_instance"):
    """异步单例管理装饰器

    为类添加统一的单例管理功能

    Args:
        reset_method_name: 重置方法名称

    Returns:
        装饰器函数
    """
    def decorator(cls):
        _instance = None
        _lock = threading.Lock()

        def get_instance(*args, **kwargs):
            nonlocal _instance
            if _instance is None:
                with _lock:
                    if _instance is None:
                        _instance = cls(*args, **kwargs)
            return _instance

        def reset_instance():
            nonlocal _instance
            with _lock:
                _instance = None

        # 添加类方法
        setattr(cls, 'get_instance', classmethod(lambda cls, *args, **kwargs: get_instance(*args, **kwargs)))
        setattr(cls, reset_method_name, staticmethod(reset_instance))

        return cls
    return decorator


class AsyncComponentBase(ABC):
    """异步组件基类

    提供通用的初始化检查机制和生命周期管理功能.
    所有异步组件都应该继承此基类以确保一致的初始化行为.

    特性:
    1. 线程安全的初始化检查
    2. 防止重复初始化
    3. 统一的生命周期管理
    4. 可选的清理机制
    """

    def __init__(self):
        """初始化异步组件基类"""
        self._initialized = False
        self._lock = threading.RLock()
        self._component_name = self.__class__.__name__

    def _ensure_initialized(self) -> None:
        """确保组件已初始化

        使用双重检查锁定模式确保线程安全的初始化.
        如果组件未初始化,会调用 _do_initialize() 方法进行初始化.
        """
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    try:
                        logger.debug(f"Initializing {self._component_name}...")
                        self._do_initialize()
                        self._initialized = True
                        logger.debug(f"{self._component_name} initialized successfully")
                    except Exception as e:
                        logger.error(f"Failed to initialize {self._component_name}: {e}")
                        raise

    @abstractmethod
    def _do_initialize(self) -> None:
        """执行具体的初始化逻辑

        子类必须实现此方法来定义具体的初始化行为.
        此方法在线程安全的环境中被调用,不需要额外的同步.

        Raises:
            Exception: 初始化失败时抛出异常
        """
        pass

    def is_initialized(self) -> bool:
        """检查组件是否已初始化

        Returns:
            bool: 如果组件已初始化返回 True,否则返回 False
        """
        return self._initialized

    def _cleanup(self) -> None:
        """清理组件资源

        子类可以重写此方法来实现自定义的清理逻辑.
        默认实现只是重置初始化状态.
        """
        with self._lock:
            if self._initialized:
                logger.debug(f"Cleaning up {self._component_name}...")
                try:
                    self._do_cleanup()
                    self._initialized = False
                    logger.debug(f"{self._component_name} cleaned up successfully")
                except Exception as e:
                    logger.error(f"Failed to cleanup {self._component_name}: {e}")
                    # 即使清理失败,也要重置状态
                    self._initialized = False

    def _do_cleanup(self) -> None:
        """执行具体的清理逻辑

        子类可以重写此方法来实现自定义的清理行为.
        默认实现为空,不执行任何操作.
        """
        pass

    def get_component_name(self) -> str:
        """获取组件名称

        Returns:
            str: 组件名称
        """
        return self._component_name


from abc import ABCMeta


class AsyncSingletonMeta(ABCMeta):
    """异步组件专用的单例元类

    继承自 ABCMeta,兼容抽象基类,同时提供单例功能.
    """

    _instances = {}
    _lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        """创建或获取单例实例"""
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

    @classmethod
    def reset_instance(mcs, cls) -> None:
        """重置指定类的单例实例,支持异步组件清理"""
        with mcs._lock:
            if cls in mcs._instances:
                instance = mcs._instances[cls]
                # 如果是异步组件,调用其清理方法
                if hasattr(instance, '_cleanup') and callable(getattr(instance, '_cleanup')):
                    try:
                        instance._cleanup()
                    except Exception as e:
                        logger.warning(f"Failed to cleanup async component {cls.__name__}: {e}")
                # 如果有标准的cleanup方法,也调用
                elif hasattr(instance, "cleanup") and callable(getattr(instance, "cleanup")):
                    try:
                        instance.cleanup()
                    except Exception as e:
                        logger.warning(f"Failed to cleanup instance {cls.__name__}: {e}")
                del mcs._instances[cls]

    @classmethod
    def get_instance(mcs, cls) -> Optional[Any]:
        """获取单例实例(如果存在)"""
        return mcs._instances.get(cls)

    @classmethod
    def has_instance(mcs, cls) -> bool:
        """检查是否已有单例实例"""
        return cls in mcs._instances


class AsyncSingletonComponentBase(AsyncComponentBase, metaclass=AsyncSingletonMeta):
    """异步单例组件基类

    结合 AsyncComponentBase 和单例模式的基类.
    适用于需要全局唯一实例的异步组件.
    使用自定义的 AsyncSingletonMeta 元类避免冲突.
    """

    def __init__(self, *args, **kwargs):
        """初始化异步单例组件"""
        # 防止重复初始化(单例模式要求)
        if not hasattr(self, '_singleton_component_initialized'):
            super().__init__()
            self._singleton_component_initialized = True


class AsyncSingletonMixin:
    """异步单例混入类

    为现有类提供单例功能的混入类.
    适用于不能继承 AsyncSingletonComponentBase 的情况.
    """

    def __init_subclass__(cls, **kwargs):
        """子类初始化时自动应用单例元类"""
        super().__init_subclass__(**kwargs)
        # 如果子类没有显式指定元类,自动使用 AsyncSingletonMeta
        if not hasattr(cls, '__metaclass__') and type(cls) is not AsyncSingletonMeta:
            # 创建一个新的类,使用 AsyncSingletonMeta 元类
            new_cls = AsyncSingletonMeta(cls.__name__, cls.__bases__, dict(cls.__dict__))
            # 更新模块信息
            new_cls.__module__ = cls.__module__
            new_cls.__qualname__ = cls.__qualname__
            return new_cls


# 导出的公共接口
