# Mini-Boot

轻量级 Python Web 框架，提供类似 Spring Boot 的开发体验。

## 特性

-   🚀 **IoC 容器** - 依赖注入和组件管理
-   📝 **注解驱动** - 基于注解的组件扫描和配置
-   🌐 **Web 集成** - FastAPI 集成和路由处理
-   ⚡ **自动配置** - 智能的自动配置机制
-   📊 **监控端点** - 健康检查和系统监控
-   🔄 **事件系统** - 发布订阅事件机制
-   ⏰ **任务调度** - 定时任务和异步处理
-   🧩 **Starter 机制** - 模块化的功能扩展

## 快速开始

### 安装

```bash
pip install miniboot
```

### 基本使用

```python
from miniboot import MiniBootApplication
from miniboot.annotations import Component, Service, Autowired

@Service
class HelloService:
    def get_message(self):
        return "Hello from Mini-Boot!"

@Component
class HelloController:
    @Autowired
    def __init__(self, hello_service: HelloService):
        self.hello_service = hello_service

    def hello(self):
        return self.hello_service.get_message()

if __name__ == "__main__":
    MiniBootApplication.run()
```

## 文档

详细文档请参考 [docs](./docs) 目录。

## 许可证

MIT License
