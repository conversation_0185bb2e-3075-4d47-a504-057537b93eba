#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 应用上下文 Actuator 健康检查扩展

为应用上下文提供 Actuator 健康检查的自动集成扩展,实现:
- 自动检测和注册健康检查集成
- 提供便捷的健康检查配置方法
- 集成到应用上下文生命周期
"""

import asyncio
from typing import TYPE_CHECKING, Any, Dict, List, Optional

from loguru import logger

from ..annotations import Component
from ..bean.lifecycle import Lifecycle

if TYPE_CHECKING:
    from ..actuator.lifecycle import LifecycleHealthIntegration
    from .application import ApplicationContext


class ActuatorHealthExtension:
    """Actuator 健康检查扩展

    为应用上下文提供健康检查集成的扩展功能.
    """

    def __init__(self, application_context: "ApplicationContext"):
        """初始化 Actuator 健康检查扩展

        Args:
            application_context: 应用上下文实例
        """
        self.application_context = application_context
        self.health_integration: Optional["LifecycleHealthIntegration"] = None
        self._enabled = True
        self._auto_start = True

        logger.debug("ActuatorHealthExtension initialized")

    async def initialize(self) -> None:
        """初始化健康检查扩展"""
        if not self._enabled:
            logger.debug("Actuator health extension is disabled")
            return

        try:
            # 检查是否已经有健康检查集成
            if self._check_existing_health_integration():
                logger.debug("Health integration already exists, skipping auto-registration")
                return

            # 创建并注册健康检查集成
            await self._create_and_register_health_integration()

            logger.info("✅ Actuator health extension initialized")

        except Exception as e:
            logger.error(f"❌ Failed to initialize actuator health extension: {e}")
            raise

    def _check_existing_health_integration(self) -> bool:
        """检查是否已存在健康检查集成"""
        try:
            # 检查Bean工厂中是否已有健康检查集成
            if hasattr(self.application_context, "_bean_factory"):
                bean_factory = self.application_context._bean_factory

                # 检查是否已注册LifecycleHealthIntegration
                if hasattr(bean_factory, "contains_bean"):
                    return bean_factory.contains_bean("lifecycleHealthIntegration") or bean_factory.contains_bean("healthIntegration")

                # 检查是否已有相关Bean定义
                if hasattr(self.application_context, "_bean_registry"):
                    registry = self.application_context._bean_registry
                    bean_names = registry.get_bean_definition_names()

                    for name in bean_names:
                        definition = registry.get_bean_definition(name)
                        if definition and "LifecycleHealthIntegration" in str(definition.bean_class):
                            return True

            return False

        except Exception as e:
            logger.debug(f"Error checking existing health integration: {e}")
            return False

    async def _create_and_register_health_integration(self) -> None:
        """创建并注册健康检查集成"""
        try:
            # 使用实际存在的健康监控组件
            from miniboot.starters.actuator.endpoints.health import \
                AsyncHealthEndpoint
            from miniboot.starters.actuator.monitoring.health import \
                HealthMonitor

            # 创建健康监控器实例
            health_monitor = HealthMonitor(
                check_interval=30.0,
                max_history=1000,
                enable_alerts=True
            )

            # 创建健康检查端点
            health_endpoint = AsyncHealthEndpoint()

            # 注册为单例Bean
            self.application_context.register_singleton("healthMonitor", health_monitor)
            self.application_context.register_singleton("healthEndpoint", health_endpoint)

            # 保存引用
            self.health_integration = health_monitor

            # 如果应用上下文已经运行,立即启动健康检查
            if self.application_context.is_running() and self._auto_start:
                await self._start_health_integration()

            logger.info("Health integration created and registered")

        except Exception as e:
            logger.error(f"Failed to create and register health integration: {e}")
            raise

    async def _start_health_integration(self) -> None:
        """启动健康检查集成"""
        try:
            if self.health_integration and hasattr(self.health_integration, "start"):
                await self.health_integration.start()

            logger.debug("Health integration started")

        except Exception as e:
            logger.error(f"Failed to start health integration: {e}")
            raise

    async def start(self) -> None:
        """启动健康检查扩展"""
        if self.health_integration and self._auto_start:
            await self._start_health_integration()

    async def stop(self) -> None:
        """停止健康检查扩展"""
        try:
            if self.health_integration and hasattr(self.health_integration, "stop"):
                await self.health_integration.stop()

            logger.debug("Health extension stopped")

        except Exception as e:
            logger.error(f"Failed to stop health extension: {e}")

    def enable(self) -> None:
        """启用健康检查扩展"""
        self._enabled = True
        logger.debug("Actuator health extension enabled")

    def disable(self) -> None:
        """禁用健康检查扩展"""
        self._enabled = False
        logger.debug("Actuator health extension disabled")

    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self._enabled

    def set_auto_start(self, auto_start: bool) -> None:
        """设置是否自动启动"""
        self._auto_start = auto_start
        logger.debug(f"Health extension auto-start set to {auto_start}")

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康检查状态"""
        try:
            status = {
                "extension_enabled": self._enabled,
                "auto_start": self._auto_start,
                "integration_available": self.health_integration is not None,
            }

            if self.health_integration and hasattr(self.health_integration, "get_health_status"):
                status["integration_status"] = self.health_integration.get_health_status()

            return status

        except Exception as e:
            return {"extension_enabled": self._enabled, "error": str(e)}


def add_actuator_health_extension(application_context: "ApplicationContext") -> ActuatorHealthExtension:
    """为应用上下文添加 Actuator 健康检查扩展

    Args:
        application_context: 应用上下文实例

    Returns:
        ActuatorHealthExtension: 健康检查扩展实例
    """
    extension = ActuatorHealthExtension(application_context)

    # 将扩展注册到应用上下文
    if hasattr(application_context, "_extensions"):
        application_context._extensions["actuator_health"] = extension
    else:
        # 如果没有扩展字典,创建一个
        application_context._extensions = {"actuator_health": extension}

    logger.info("Actuator health extension added to application context")
    return extension


def get_actuator_health_extension(application_context: "ApplicationContext") -> Optional[ActuatorHealthExtension]:
    """获取应用上下文的 Actuator 健康检查扩展

    Args:
        application_context: 应用上下文实例

    Returns:
        Optional[ActuatorHealthExtension]: 健康检查扩展实例,如果不存在则返回None
    """
    if hasattr(application_context, "_extensions"):
        return application_context._extensions.get("actuator_health")
    return None


async def enable_actuator_health_integration(application_context: "ApplicationContext", auto_start: bool = True) -> ActuatorHealthExtension:
    """启用应用上下文的 Actuator 健康检查集成

    这是一个便捷函数,用于快速启用健康检查集成.

    Args:
        application_context: 应用上下文实例
        auto_start: 是否自动启动健康检查

    Returns:
        ActuatorHealthExtension: 健康检查扩展实例
    """
    # 检查是否已有扩展
    extension = get_actuator_health_extension(application_context)

    if not extension:
        # 创建新的扩展
        extension = add_actuator_health_extension(application_context)

    # 配置扩展
    extension.enable()
    extension.set_auto_start(auto_start)

    # 初始化扩展
    await extension.initialize()

    # 如果应用上下文正在运行且设置了自动启动,立即启动
    if application_context.is_running() and auto_start:
        await extension.start()

    logger.info("Actuator health integration enabled")
    return extension


async def disable_actuator_health_integration(application_context: "ApplicationContext") -> None:
    """禁用应用上下文的 Actuator 健康检查集成

    Args:
        application_context: 应用上下文实例
    """
    extension = get_actuator_health_extension(application_context)

    if extension:
        await extension.stop()
        extension.disable()
        logger.info("Actuator health integration disabled")
    else:
        logger.debug("No actuator health extension found to disable")


def is_actuator_health_enabled(application_context: "ApplicationContext") -> bool:
    """检查应用上下文是否启用了 Actuator 健康检查集成

    Args:
        application_context: 应用上下文实例

    Returns:
        bool: 如果启用了健康检查集成返回True,否则返回False
    """
    extension = get_actuator_health_extension(application_context)
    return extension is not None and extension.is_enabled()


def get_actuator_health_status(application_context: "ApplicationContext") -> Dict[str, Any]:
    """获取应用上下文的 Actuator 健康检查状态

    Args:
        application_context: 应用上下文实例

    Returns:
        Dict[str, Any]: 健康检查状态信息
    """
    extension = get_actuator_health_extension(application_context)

    if extension:
        return extension.get_health_status()
    else:
        return {"extension_available": False, "message": "Actuator health extension not found"}
