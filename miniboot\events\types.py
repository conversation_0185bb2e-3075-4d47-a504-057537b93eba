#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 事件类型定义和具体事件实现

定义所有事件类型常量和具体事件类,
使用组合式事件替代复杂的继承层次.
"""

from typing import Any, Optional

from .composite import CompositeEvent

# === 事件类型常量 ===


class EventTypes:
    """事件类型常量定义"""

    # 应用生命周期事件
    APPLICATION_STARTED = "ApplicationStartedEvent"
    APPLICATION_STOPPED = "ApplicationStoppedEvent"
    APPLICATION_READY = "ApplicationReadyEvent"
    APPLICATION_FAILED = "ApplicationFailedEvent"

    # Bean生命周期事件
    BEAN_CREATED = "BeanCreatedEvent"
    BEAN_DESTROYED = "BeanDestroyedEvent"
    BEAN_INITIALIZED = "BeanInitializedEvent"

    # 异步Bean事件
    ASYNC_BEAN_CREATION_STARTED = "AsyncBeanCreationStartedEvent"
    ASYNC_BEAN_CREATION_COMPLETED = "AsyncBeanCreationCompletedEvent"
    ASYNC_BEAN_CREATION_FAILED = "AsyncBeanCreationFailedEvent"

    ASYNC_BEAN_INJECTION_STARTED = "AsyncBeanInjectionStartedEvent"
    ASYNC_BEAN_INJECTION_COMPLETED = "AsyncBeanInjectionCompletedEvent"
    ASYNC_BEAN_INJECTION_FAILED = "AsyncBeanInjectionFailedEvent"

    ASYNC_BEAN_INITIALIZATION_STARTED = "AsyncBeanInitializationStartedEvent"
    ASYNC_BEAN_INITIALIZATION_COMPLETED = "AsyncBeanInitializationCompletedEvent"
    ASYNC_BEAN_INITIALIZATION_FAILED = "AsyncBeanInitializationFailedEvent"


# === 具体事件类(使用组合式事件) ===


class ApplicationStartedEvent(CompositeEvent):
    """应用启动事件

    使用组合式事件实现,避免继承层次.
    """

    def __init__(self, application: Any, startup_time: Optional[float] = None):
        """初始化应用启动事件

        Args:
            application: 应用程序实例
            startup_time: 启动耗时(秒)
        """
        data = {}
        if startup_time is not None:
            data["startup_time"] = startup_time

        super().__init__(event_type=EventTypes.APPLICATION_STARTED, source=application, data=data)

    def get_startup_time(self) -> Optional[float]:
        """获取启动耗时"""
        return self.get_data("startup_time")


class ApplicationStoppedEvent(CompositeEvent):
    """应用停止事件"""

    def __init__(self, application: Any, shutdown_time: Optional[float] = None):
        """初始化应用停止事件

        Args:
            application: 应用程序实例
            shutdown_time: 关闭耗时(秒)
        """
        data = {}
        if shutdown_time is not None:
            data["shutdown_time"] = shutdown_time

        super().__init__(event_type=EventTypes.APPLICATION_STOPPED, source=application, data=data)

    def get_shutdown_time(self) -> Optional[float]:
        """获取关闭耗时"""
        return self.get_data("shutdown_time")


class ApplicationStoppingEvent(CompositeEvent):
    """应用停止中事件"""

    def __init__(self, application: Any):
        """初始化应用停止中事件

        Args:
            application: 应用程序实例
        """
        super().__init__(event_type="ApplicationStoppingEvent", source=application)


class ApplicationReadyEvent(CompositeEvent):
    """应用就绪事件"""

    def __init__(self, application: Any):
        """初始化应用就绪事件

        Args:
            application: 应用程序实例
        """
        super().__init__(event_type=EventTypes.APPLICATION_READY, source=application)


class ApplicationFailedEvent(CompositeEvent):
    """应用失败事件"""

    def __init__(self, application: Any, exception: Exception):
        """初始化应用失败事件

        Args:
            application: 应用程序实例
            exception: 导致失败的异常
        """
        data = {"exception_type": type(exception).__name__, "exception_message": str(exception), "exception": exception}

        super().__init__(event_type=EventTypes.APPLICATION_FAILED, source=application, data=data)

    def get_exception(self) -> Exception:
        """获取异常对象"""
        return self.get_data("exception")

    def get_exception_type(self) -> str:
        """获取异常类型"""
        return self.get_data("exception_type")

    def get_exception_message(self) -> str:
        """获取异常消息"""
        return self.get_data("exception_message")


class BeanCreatedEvent(CompositeEvent):
    """Bean创建事件"""

    def __init__(self, bean_name: str, bean_class: str, source: Any = None):
        """初始化Bean创建事件

        Args:
            bean_name: Bean名称
            bean_class: Bean类名
            source: 事件源
        """
        data = {"bean_name": bean_name, "bean_class": bean_class}

        super().__init__(event_type=EventTypes.BEAN_CREATED, source=source, data=data)

        # 注册为Bean事件
        self.register_bean_event(bean_name)

    def get_bean_name(self) -> str:
        """获取Bean名称"""
        return self.get_data("bean_name")

    def get_bean_class(self) -> str:
        """获取Bean类名"""
        return self.get_data("bean_class")


class BeanDestroyedEvent(CompositeEvent):
    """Bean销毁事件"""

    def __init__(self, bean_name: str, bean_class: str, source: Any = None):
        """初始化Bean销毁事件

        Args:
            bean_name: Bean名称
            bean_class: Bean类名
            source: 事件源
        """
        data = {"bean_name": bean_name, "bean_class": bean_class}

        super().__init__(event_type=EventTypes.BEAN_DESTROYED, source=source, data=data)

        # 注册为Bean事件
        self.register_bean_event(bean_name)

    def get_bean_name(self) -> str:
        """获取Bean名称"""
        return self.get_data("bean_name")

    def get_bean_class(self) -> str:
        """获取Bean类名"""
        return self.get_data("bean_class")


class BeanInitializedEvent(CompositeEvent):
    """Bean初始化事件"""

    def __init__(self, bean_name: str, bean_class: str, source: Any = None):
        """初始化Bean初始化事件

        Args:
            bean_name: Bean名称
            bean_class: Bean类名
            source: 事件源
        """
        data = {"bean_name": bean_name, "bean_class": bean_class}

        super().__init__(event_type=EventTypes.BEAN_INITIALIZED, source=source, data=data)

        # 注册为Bean事件
        self.register_bean_event(bean_name)

    def get_bean_name(self) -> str:
        """获取Bean名称"""
        return self.get_data("bean_name")

    def get_bean_class(self) -> str:
        """获取Bean类名"""
        return self.get_data("bean_class")


# === 异步Bean事件(使用组合式事件替代继承) ===


class AsyncBeanCreationEvent(CompositeEvent):
    """异步Bean创建事件"""

    def __init__(
        self,
        bean_name: str,
        bean_class: str,
        event_type: str,
        source: Any = None,
        duration: Optional[float] = None,
        success: Optional[bool] = None,
        error: Optional[Exception] = None,
    ):
        """初始化异步Bean创建事件

        Args:
            bean_name: Bean名称
            bean_class: Bean类名
            event_type: 具体事件类型
            source: 事件源
            duration: 耗时
            success: 是否成功
            error: 错误信息
        """
        data = {"bean_name": bean_name, "bean_class": bean_class, "duration": duration, "success": success, "error": error}

        super().__init__(event_type=event_type, source=source, data=data)

        # 注册为Bean事件和异步事件
        self.register_bean_event(bean_name)
        self.register_as_async({"bean_name": bean_name, "bean_class": bean_class, "operation": "creation"})

    def get_bean_name(self) -> str:
        """获取Bean名称"""
        return self.get_data("bean_name")

    def get_bean_class(self) -> str:
        """获取Bean类名"""
        return self.get_data("bean_class")

    def get_duration(self) -> Optional[float]:
        """获取耗时"""
        return self.get_data("duration")

    def is_success(self) -> Optional[bool]:
        """是否成功"""
        return self.get_data("success")

    def get_error(self) -> Optional[Exception]:
        """获取错误信息"""
        return self.get_data("error")
