#!/usr/bin/env python
"""
* @author: cz
* @description: 占位符解析集成测试
"""

import os
import tempfile
import unittest
from pathlib import Path

from miniboot.env import StandardEnvironment


class PlaceholderIntegrationTestCase(unittest.TestCase):
    """占位符解析集成测试"""

    def setUp(self):
        """设置测试环境"""
        # 创建临时目录和配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.resources_dir = Path(self.temp_dir) / "resources"
        self.resources_dir.mkdir(exist_ok=True)

        # 保存原始工作目录
        self.original_cwd = Path.cwd()

        # 切换到临时目录
        os.chdir(self.temp_dir)

        # 创建测试配置文件
        self._create_test_config()

    def tearDown(self):
        """清理测试环境"""
        # 恢复原始工作目录
        os.chdir(self.original_cwd)

        # 清理临时文件
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def _create_test_config(self):
        """创建测试配置文件"""
        # 主配置文件
        main_config = """
miniboot:
  profiles:
    active: dev

  application:
    name: test-app
    version: 1.0.0

  server:
    port: 8080
    host: localhost
    name: ${miniboot.application.name}
    full-name: ${miniboot.application.name}-${miniboot.application.version}
    url: http://${miniboot.server.host}:${miniboot.server.port}

  datasource:
    driver: sqlite3
    database-name: ${miniboot.application.name:default-app}-db
    connection-string: ${miniboot.datasource.driver}://${miniboot.datasource.database-name}

  logging:
    level: INFO
    file: logs/${miniboot.application.name}.log
"""

        # dev 配置文件
        dev_config = """
miniboot:
  application:
    name: test-app-dev

  server:
    port: 8081
    debug: true

  logging:
    level: DEBUG
"""

        # 写入配置文件
        with (self.resources_dir / "application.yml").open("w", encoding="utf-8") as f:
            f.write(main_config)

        with (self.resources_dir / "application-dev.yml").open("w", encoding="utf-8") as f:
            f.write(dev_config)

    def test_basic_placeholder_resolution(self):
        """测试基本占位符解析"""
        env = StandardEnvironment()

        # 测试简单占位符
        server_name = env.get_property("miniboot.server.name")
        app_name = env.get_property("miniboot.application.name")

        self.assertEqual(server_name, app_name)
        self.assertEqual(server_name, "test-app-dev")  # dev profile 覆盖了应用名称

    def test_complex_placeholder_resolution(self):
        """测试复杂占位符解析"""
        env = StandardEnvironment()

        # 测试多个占位符组合
        server_full_name = env.get_property("miniboot.server.full-name")
        expected = "test-app-dev-1.0.0"  # dev覆盖了name,但version来自主配置
        self.assertEqual(server_full_name, expected)

        # 测试URL构建
        server_url = env.get_property("miniboot.server.url")
        expected_url = "http://localhost:8081"  # dev覆盖了port
        self.assertEqual(server_url, expected_url)

    def test_placeholder_with_default_value(self):
        """测试带默认值的占位符"""
        env = StandardEnvironment()

        # 测试带默认值的占位符
        db_name = env.get_property("miniboot.datasource.database-name")
        expected = "test-app-dev-db"  # 使用实际值而不是默认值
        self.assertEqual(db_name, expected)

    def test_nested_placeholder_resolution(self):
        """测试嵌套占位符解析"""
        env = StandardEnvironment()

        # 测试嵌套占位符
        connection_string = env.get_property("miniboot.datasource.connection-string")
        expected = "sqlite3://test-app-dev-db"
        self.assertEqual(connection_string, expected)

    def test_placeholder_in_file_path(self):
        """测试文件路径中的占位符"""
        env = StandardEnvironment()

        # 测试日志文件路径中的占位符
        log_file = env.get_property("miniboot.logging.file")
        expected = "logs/test-app-dev.log"
        self.assertEqual(log_file, expected)

    def test_manual_placeholder_resolution(self):
        """测试手动占位符解析"""
        env = StandardEnvironment()

        # 测试各种占位符格式
        test_cases = [
            ("${miniboot.application.name}", "test-app-dev"),
            ("${miniboot.application.version}", "1.0.0"),
            ("${miniboot.application.name}-${miniboot.application.version}", "test-app-dev-1.0.0"),
            ("${miniboot.nonexistent:default-value}", "default-value"),
            ("${miniboot.nonexistent}", "${miniboot.nonexistent}"),
            ("prefix-${miniboot.application.name}-suffix", "prefix-test-app-dev-suffix"),
        ]

        for test_input, expected in test_cases:
            with self.subTest(input=test_input):
                resolved = env.resolve_placeholders(test_input)
                self.assertEqual(resolved, expected)

    def test_required_placeholder_resolution(self):
        """测试必需占位符解析"""
        env = StandardEnvironment()

        # 测试成功的必需占位符解析
        resolved = env.resolve_required_placeholders("${miniboot.application.name}")
        self.assertEqual(resolved, "test-app-dev")

        # 测试失败的必需占位符解析
        with self.assertRaises(ValueError) as context:
            env.resolve_required_placeholders("${miniboot.nonexistent}")

        self.assertIn("miniboot.nonexistent", str(context.exception))

    def test_profile_specific_placeholder_resolution(self):
        """测试 Profile 特定的占位符解析"""
        env = StandardEnvironment()

        # 验证 Profile 配置正确覆盖了占位符解析
        active_profiles = env.get_active_profiles()
        self.assertIn("dev", active_profiles)

        # 验证 dev profile 中的配置影响了占位符解析
        server_name = env.get_property("miniboot.server.name")
        self.assertEqual(server_name, "test-app-dev")  # 来自 dev profile

        server_url = env.get_property("miniboot.server.url")
        self.assertEqual(server_url, "http://localhost:8081")  # port 来自 dev profile

    def test_circular_placeholder_prevention(self):
        """测试循环占位符预防"""
        # 创建包含循环引用的配置
        circular_config = """
miniboot:
  test:
    a: ${miniboot.test.b}
    b: ${miniboot.test.a}
"""

        with (self.resources_dir / "application-circular.yml").open("w", encoding="utf-8") as f:
            f.write(circular_config)

        # 注意:当前实现可能不会检测循环引用,这是一个已知限制
        # 这个测试主要是为了记录这种情况
        env = StandardEnvironment()

        # 获取循环引用的属性应该保持原始占位符格式
        env.get_property("miniboot.test.a")
        # 由于循环引用,应该保持原始格式或有某种处理机制


if __name__ == "__main__":
    unittest.main()
