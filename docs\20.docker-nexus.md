# Docker Nexus 私有 PyPI 仓库部署指南

## 1. 概述

本文档指导如何使用 Docker 部署 Nexus Repository 私有 PyPI 仓库，供所有开发人员使用。Nexus Repository 是一个企业级的制品仓库管理器，支持多种包格式（PyPI、Maven、npm 等），提供强大的 Web 管理界面和企业级功能。通过 Docker 容器化部署，可以实现快速部署、易于维护、高可用性的私有包管理服务。

## 2. Nexus Repository 特性

### 2.1 主要功能

-   **多格式支持**: 支持 PyPI、Maven、npm、Docker 等多种包格式
-   **包托管**: 支持上传和托管私有 Python 包
-   **包代理**: 自动代理和缓存 PyPI.org 的包，提高下载速度
-   **用户管理**: 完整的用户和角色权限管理系统
-   **Web 界面**: 功能强大的 Web 管理界面
-   **仓库组合**: 支持将多个仓库组合成一个虚拟仓库
-   **企业级**: 支持 LDAP、SSO、备份恢复等企业功能

### 2.2 适用场景

-   大型企业和组织
-   需要多种包格式支持的环境
-   需要企业级安全和管理功能
-   对稳定性和可扩展性有高要求的场景

## 3. Docker 部署配置

### 3.1 创建项目目录

```bash
mkdir nexus-pypi
cd nexus-pypi
```

> **重要提示**: Nexus 容器使用用户 ID 200，需要确保数据目录有正确的权限。

### 3.2 创建 docker-compose.yml

```yaml
version: "3.8"

services:
    nexus:
        image: sonatype/nexus3:latest
        container_name: nexus-pypi
        ports:
            - "8081:8081"
        volumes:
            - ./nexus-data:/nexus-data
        environment:
            - INSTALL4J_ADD_VM_PARAMS=-Xms1g -Xmx1g -XX:MaxDirectMemorySize=2g
        restart: unless-stopped
        networks:
            - nexus-network

networks:
    nexus-network:
        driver: bridge
```

### 3.3 设置数据目录权限

```bash
# 创建数据目录并设置正确的权限（Nexus容器使用用户ID 200）
mkdir -p nexus-data
sudo chown -R 200:200 nexus-data
```

### 3.4 启动服务

```bash
# 启动Nexus服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志（首次启动需要等待2-3分钟）
docker-compose logs -f nexus
```

### 3.5 获取初始密码

```bash
# 等待服务完全启动后，获取初始admin密码
docker exec nexus-pypi cat /nexus-data/admin.password
```

## 4. Nexus 配置

### 4.1 首次登录

1. 访问 `http://localhost:8081`
2. 点击右上角"Sign in"
3. 用户名: `admin`
4. 密码: 使用上面获取的初始密码
5. 按照向导完成初始设置

### 4.2 创建 PyPI 仓库

#### 创建 PyPI (hosted) 仓库

1. 登录 Nexus 管理界面
2. 点击"设置"图标 → "Repositories"
3. 点击"Create repository"
4. 选择"pypi (hosted)"
5. 配置仓库:
    - **Name**: `pypi-hosted`
    - **Online**: 勾选
    - **Storage** → **Blob store**: `default`
    - **Hosted** → **Deployment policy**: `Allow redeploy`

#### 创建 PyPI (proxy) 仓库

1. 点击"Create repository"
2. 选择"pypi (proxy)"
3. 配置仓库:
    - **Name**: `pypi-proxy`
    - **Online**: 勾选
    - **Proxy** → **Remote storage**: `https://pypi.org/`
    - **Storage** → **Blob store**: `default`

#### 创建 PyPI (group) 仓库

1. 点击"Create repository"
2. 选择"pypi (group)"
3. 配置仓库:
    - **Name**: `pypi-group`
    - **Online**: 勾选
    - **Group** → **Member repositories**:
        - 添加 `pypi-hosted`
        - 添加 `pypi-proxy`
    - **Storage** → **Blob store**: `default`

### 4.3 创建用户和角色

#### 创建角色

1. 点击"设置"图标 → "Security" → "Roles"
2. 点击"Create role"
3. 配置角色:
    - **Type**: `Nexus role`
    - **Role ID**: `pypi-developer`
    - **Role name**: `PyPI Developer`
    - **Privileges**: 添加以下权限
        - `nx-repository-view-pypi-pypi-hosted-*`
        - `nx-repository-view-pypi-pypi-proxy-read`
        - `nx-repository-view-pypi-pypi-group-read`

#### 创建用户

1. 点击"设置"图标 → "Security" → "Users"
2. 点击"Create local user"
3. 配置用户:
    - **ID**: `developer1`
    - **First name**: `Developer`
    - **Last name**: `One`
    - **Email**: `<EMAIL>`
    - **Password**: `dev123`
    - **Status**: `Active`
    - **Roles**: 添加 `pypi-developer`

## 5. 客户端配置

### 5.1 发布配置

#### 使用 uv 发布

```bash
# 配置发布环境变量
export UV_PUBLISH_URL=http://*************:8081/repository/pypi-hosted/
export UV_PUBLISH_USERNAME=developer1
export UV_PUBLISH_PASSWORD=dev123

# 发布包
uv publish
```

#### 使用 twine 发布

```bash
# 安装twine
pip install twine

# 发布包
twine upload --repository-url http://*************:8081/repository/pypi-hosted/ \
             --username developer1 \
             --password dev123 \
             dist/*
```

### 5.2 安装配置

#### 全局配置

创建 `~/.pip/pip.conf` (Linux/Mac) 或 `%APPDATA%\pip\pip.ini` (Windows):

```ini
[global]
index-url = http://*************:8081/repository/pypi-group/simple/
trusted-host = *************
```

#### 项目配置

在项目的 `pyproject.toml` 中配置：

```toml
[[tool.uv.index]]
name = "company-nexus"
url = "http://*************:8081/repository/pypi-group/simple/"
default = true

[[tool.uv.index]]
name = "nexus-hosted"
url = "http://*************:8081/repository/pypi-hosted/simple/"
```

### 5.3 使用示例

```bash
# 安装私有包
uv add miniboot --index-url http://*************:8081/repository/pypi-group/simple/

# 如果配置了全局设置，可以直接安装
uv add miniboot
pip install miniboot
```

## 6. 管理和维护

### 6.1 用户管理

通过 Nexus Web 界面管理用户：

1. 访问 `http://*************:8081`
2. 登录管理员账户
3. 点击"设置"图标 → "Security" → "Users"
4. 可以创建、编辑、删除用户

### 6.2 包管理

#### 查看包信息

1. 在 Nexus 界面中点击"Browse"
2. 选择相应的仓库查看包列表
3. 点击包名查看详细信息和版本

#### 删除包

1. 在"Browse"中找到要删除的包
2. 点击包名进入详情页
3. 选择要删除的版本
4. 点击"Delete"按钮

### 6.3 日志管理

```bash
# 查看实时日志
docker-compose logs -f nexus

# 查看最近的日志
docker-compose logs --tail=100 nexus

# 导出日志到文件
docker-compose logs nexus > nexus.log
```

## 7. 故障排除

### 7.1 常见问题

#### 服务无法启动

```bash
# 检查端口占用
netstat -tlnp | grep 8081

# 检查Docker状态
docker-compose ps
docker-compose logs nexus
```

#### 权限问题

**错误信息**: `mkdir: cannot create directory '/opt/sonatype/nexus/../sonatype-work/nexus3/log': Permission denied`

**原因**: Nexus 容器内部使用用户 ID 200，数据目录权限不正确

**解决方案**:

```bash
# 方法一：修改目录权限（推荐）
docker-compose down
sudo chown -R 200:200 nexus-data
docker-compose up -d

# 方法二：完全重置
docker-compose down
sudo rm -rf nexus-data
mkdir nexus-data
sudo chown 200:200 nexus-data
docker-compose up -d

# 方法三：在docker-compose.yml中添加用户配置
# 在nexus服务中添加: user: "200:200"
```

#### 检查数据目录权限

```bash
# 检查权限
ls -la ./nexus-data

# 应该看到类似输出：
# drwxr-xr-x 3 <USER> <GROUP> 4096 Dec  1 10:00 nexus-data
```

#### 无法获取初始密码

```bash
# 等待服务完全启动（可能需要2-3分钟）
docker-compose logs -f nexus

# 手动查找密码文件
docker exec nexus-pypi find /nexus-data -name "admin.password" -type f
docker exec nexus-pypi cat /nexus-data/admin.password
```

#### 包上传失败

1. **检查用户权限**: 确保用户有上传权限
2. **检查仓库配置**: 确认 hosted 仓库允许重新部署
3. **检查网络**: 确认能访问 Nexus 服务

#### 包安装失败

```bash
# 测试Nexus连接
curl http://localhost:8081/repository/pypi-group/simple/

# 检查仓库URL配置
# 确保使用正确的仓库URL格式
```

### 7.2 性能优化

#### 资源配置

在 `docker-compose.yml` 中调整 JVM 参数：

```yaml
environment:
    - INSTALL4J_ADD_VM_PARAMS=-Xms2g -Xmx4g -XX:MaxDirectMemorySize=4g
```

#### 存储优化

```bash
# 定期清理未使用的blob
# 在Nexus界面中：Administration → System → Tasks
# 创建"Admin - Compact blob store"任务
```

## 8. 高级配置

### 8.1 HTTPS 配置

#### 使用反向代理

创建 `nginx.conf`:

```nginx
server {
    listen 80;
    server_name nexus.company.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name nexus.company.com;

    ssl_certificate /etc/nginx/certs/nexus.company.com.crt;
    ssl_certificate_key /etc/nginx/certs/nexus.company.com.key;

    client_max_body_size 100M;

    location / {
        proxy_pass http://nexus:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 8.2 备份和恢复

#### 数据备份

```bash
# 停止服务
docker-compose stop

# 备份数据
tar czf nexus-backup-$(date +%Y%m%d).tar.gz ./nexus-data

# 重启服务
docker-compose start
```

#### 数据恢复

```bash
# 停止服务
docker-compose stop

# 恢复数据
tar xzf nexus-backup-20231201.tar.gz

# 重启服务
docker-compose start
```

### 8.3 监控配置

#### 健康检查

```bash
# 检查Nexus状态
curl -u admin:password http://localhost:8081/service/rest/v1/status

# 检查仓库状态
curl -u admin:password http://localhost:8081/service/rest/v1/repositories
```

## 9. 企业级功能

### 9.1 LDAP 集成

1. 在 Nexus 界面中：Administration → Security → LDAP
2. 配置 LDAP 连接参数
3. 设置用户和组映射
4. 测试 LDAP 连接

### 9.2 清理策略

1. Administration → Repository → Cleanup Policies
2. 创建清理策略：
    - **Name**: `pypi-cleanup`
    - **Format**: `pypi`
    - **Criteria**: 设置保留条件（如保留最近 30 天的包）
3. 将策略应用到仓库

### 9.3 安全扫描

Nexus Professional 版本支持：

-   漏洞扫描
-   许可证分析
-   组件信息

## 10. 网络配置

### 10.1 局域网访问

如果需要在局域网中访问，修改配置：

1. 将 `localhost` 替换为服务器的 IP 地址
2. 确保防火墙允许 8081 端口
3. 在客户端配置中使用服务器 IP：

```ini
[global]
index-url = http://*************:8081/repository/pypi-group/simple/
trusted-host = *************
```

```toml
[[tool.uv.index]]
name = "company-nexus"
url = "http://*************:8081/repository/pypi-group/simple/"
default = true
```

### 10.2 多环境部署

可以为不同环境创建不同的仓库：

-   `pypi-dev` - 开发环境
-   `pypi-test` - 测试环境
-   `pypi-prod` - 生产环境

---

_本文档指导如何使用 Docker 部署 Nexus 私有 PyPI 仓库。_
