#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 异步信息端点 - 高性能异步实现

提供高性能的异步应用信息端点,支持并发收集应用信息、长缓存策略和智能信息管理.

核心特性:
- 并发执行多个信息贡献者
- 异步文件系统操作
- 智能缓存减少重复计算
- 长缓存策略(适合相对静态的信息)
- 异步信息贡献者接口
"""

import asyncio
import os
import platform
import sys
import time
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from loguru import logger

from miniboot.monitoring.interfaces import EndpointInfo, EndpointProvider
from miniboot.utils import cached, timeout

from .base import Endpoint, EndpointOperation, OperationType


# 定义 InfoContributor 基类
class InfoContributor:
    """信息贡献者基类"""

    def __init__(self, name: str):
        self.name = name

    def contribute(self) -> Dict[str, Any]:
        """贡献信息"""
        return {}


class InfoEndpoint(Endpoint, EndpointProvider):
    """信息端点 - 实现 EndpointProvider 接口"""

    def __init__(self):
        super().__init__(endpoint_id="info", enabled=True, sensitive=False)
        self.contributors = {}

    def operations(self) -> List[EndpointOperation]:
        """返回端点支持的操作"""
        return [
            EndpointOperation(
                operation_type=OperationType.READ,
                method="GET",
                path=""
            )
        ]

    def invoke(self, operation_type: OperationType, **kwargs) -> Any:
        """执行端点操作"""
        if operation_type == OperationType.READ:
            # 返回同步的信息结果
            return self.get_info_sync()
        else:
            raise ValueError(f"Unsupported operation type: {operation_type}")

    def get_info_sync(self) -> Dict[str, Any]:
        """同步版本的信息获取"""
        return {
            "app": {
                "name": "Mini-Boot",
                "version": "1.0.0"
            },
            "timestamp": datetime.now().isoformat()
        }

    async def get_info(self) -> Dict[str, Any]:
        """获取应用信息"""
        return {"app": {"name": "Mini-Boot", "version": "1.0.0"}, "timestamp": datetime.now().isoformat()}

    async def info(self) -> Dict[str, Any]:
        """获取应用信息 - 兼容性方法"""
        return await self.get_info()

    # 实现 EndpointProvider 接口方法
    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息"""
        return EndpointInfo(
            name="info",
            path="/actuator/info",
            methods=["GET"],
            description="Application information endpoint"
        )

    async def handle_request(self, request, **kwargs) -> Dict[str, Any]:
        """处理请求"""
        return await self.get_info()

    def is_enabled(self) -> bool:
        """检查端点是否启用"""
        return self.enabled


class AsyncInfoContributor(ABC):
    """异步信息贡献者接口"""

    def __init__(self, name: str, timeout: float = 5.0):
        """初始化异步信息贡献者

        Args:
            name: 贡献者名称
            timeout: 贡献超时时间(秒)
        """
        self.name = name
        self.timeout = timeout

    @abstractmethod
    async def contribute(self) -> Dict[str, Any]:
        """贡献信息"""
        pass


class AsyncAppInfoContributor(AsyncInfoContributor):
    """异步应用信息贡献者"""

    def __init__(
        self, app_name: str = "Mini-Boot Application", app_version: str = "1.0.0", app_description: str = "Mini-Boot框架应用", timeout: float = 2.0
    ):
        """初始化异步应用信息贡献者

        Args:
            app_name: 应用名称
            app_version: 应用版本
            app_description: 应用描述
            timeout: 贡献超时时间(秒)
        """
        super().__init__("app", timeout)
        self.app_name = app_name
        self.app_version = app_version
        self.app_description = app_description

    @timeout(2.0)
    async def contribute(self) -> Dict[str, Any]:
        """异步贡献应用信息"""
        try:
            # 模拟异步操作
            await asyncio.sleep(0.01)

            return {
                "name": self.app_name,
                "version": self.app_version,
                "description": self.app_description,
                "encoding": sys.getdefaultencoding(),
                "timezone": str(datetime.now().astimezone().tzinfo),
                "startup_time": datetime.now().isoformat(),
            }
        except Exception as e:
            logger.error(f"App info contribution failed: {e}")
            return {"error": str(e)}


class AsyncBuildInfoContributor(AsyncInfoContributor):
    """异步构建信息贡献者"""

    def __init__(self, timeout: float = 3.0):
        super().__init__("build", timeout)

    @timeout(3.0)
    async def contribute(self) -> Dict[str, Any]:
        """异步贡献构建信息"""
        try:
            loop = asyncio.get_event_loop()

            # 并发获取平台信息
            platform_task = loop.run_in_executor(None, platform.platform)
            architecture_task = loop.run_in_executor(None, lambda: platform.architecture()[0])
            machine_task = loop.run_in_executor(None, platform.machine)
            processor_task = loop.run_in_executor(None, platform.processor)
            node_task = loop.run_in_executor(None, platform.node)

            # 等待所有任务完成
            platform_info, architecture, machine, processor, node = await asyncio.gather(
                platform_task, architecture_task, machine_task, processor_task, node_task, return_exceptions=True
            )

            return {
                "time": datetime.now().isoformat(),
                "python_version": sys.version,
                "python_implementation": platform.python_implementation(),
                "python_compiler": platform.python_compiler(),
                "platform": platform_info if not isinstance(platform_info, Exception) else "unknown",
                "architecture": architecture if not isinstance(architecture, Exception) else "unknown",
                "machine": machine if not isinstance(machine, Exception) else "unknown",
                "processor": processor if not isinstance(processor, Exception) else "unknown",
                "node": node if not isinstance(node, Exception) else "unknown",
            }
        except Exception as e:
            logger.error(f"Build info contribution failed: {e}")
            return {"error": str(e)}


class AsyncGitInfoContributor(AsyncInfoContributor):
    """异步 Git 信息贡献者"""

    def __init__(self, git_branch: str = "main", git_commit: str = "unknown", timeout: float = 5.0):
        """初始化异步 Git 信息贡献者

        Args:
            git_branch: Git分支
            git_commit: Git提交哈希
            timeout: 贡献超时时间(秒)
        """
        super().__init__("git", timeout)
        self.git_branch = git_branch
        self.git_commit = git_commit

    @timeout(5.0)
    async def contribute(self) -> Dict[str, Any]:
        """贡献 Git 信息"""
        try:
            git_info = {"branch": self.git_branch, "commit": {"id": self.git_commit, "time": datetime.now().isoformat()}}

            # 异步获取环境变量信息
            loop = asyncio.get_event_loop()
            env_task = loop.run_in_executor(None, self._get_env_git_info)
            file_task = loop.run_in_executor(None, self._read_git_info_async)

            # 等待任务完成
            env_info, file_info = await asyncio.gather(env_task, file_task, return_exceptions=True)

            # 合并信息(环境变量优先)
            if not isinstance(file_info, Exception):
                git_info.update(file_info)
            if not isinstance(env_info, Exception):
                git_info.update(env_info)

            return git_info

        except Exception as e:
            logger.error(f"Git info contribution failed: {e}")
            return {"error": str(e)}

    def _get_env_git_info(self) -> Dict[str, Any]:
        """从环境变量获取 Git 信息"""
        git_info = {}

        if "GIT_BRANCH" in os.environ:
            git_info["branch"] = os.environ["GIT_BRANCH"]

        if "GIT_COMMIT" in os.environ:
            git_info["commit"] = {"id": os.environ["GIT_COMMIT"]}

        if "GIT_COMMIT_TIME" in os.environ:
            if "commit" not in git_info:
                git_info["commit"] = {}
            git_info["commit"]["time"] = os.environ["GIT_COMMIT_TIME"]

        return git_info

    def _read_git_info_async(self) -> Dict[str, Any]:
        """异步读取 .git 目录信息"""
        git_info = {}

        try:
            # 尝试读取当前分支
            head_file = Path(".git/HEAD")
            if head_file.exists():
                with head_file.open(encoding="utf-8") as f:
                    head_content = f.read().strip()
                    if head_content.startswith("ref: refs/heads/"):
                        git_info["branch"] = head_content[16:]  # 移除 "ref: refs/heads/"

            # 尝试读取最新提交哈希
            if "branch" in git_info:
                ref_file = Path(f".git/refs/heads/{git_info['branch']}")
                if ref_file.exists():
                    with ref_file.open(encoding="utf-8") as f:
                        commit_hash = f.read().strip()
                        if commit_hash:
                            git_info["commit"] = {
                                "id": commit_hash[:7],  # 短哈希
                                "id_full": commit_hash,  # 完整哈希
                                "time": datetime.now().isoformat(),
                            }
        except Exception:
            pass

        return git_info


class AsyncEnvironmentInfoContributor(AsyncInfoContributor):
    """异步环境信息贡献者"""

    def __init__(self, timeout: float = 2.0):
        super().__init__("environment", timeout)

    @timeout(2.0)
    async def contribute(self) -> Dict[str, Any]:
        """异步贡献环境信息"""
        try:
            loop = asyncio.get_event_loop()

            # 异步获取环境变量
            env_task = loop.run_in_executor(None, self._get_environment_info)
            env_info = await env_task

            return env_info

        except Exception as e:
            logger.error(f"Environment info contribution failed: {e}")
            return {"error": str(e)}

    def _get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        env_info = {
            "java_home": os.environ.get("JAVA_HOME"),
            "path": os.environ.get("PATH"),
            "user": os.environ.get("USER") or os.environ.get("USERNAME"),
            "home": os.environ.get("HOME") or os.environ.get("USERPROFILE"),
            "temp": os.environ.get("TEMP") or os.environ.get("TMP"),
            "shell": os.environ.get("SHELL"),
            "lang": os.environ.get("LANG"),
            "python_path": os.environ.get("PYTHONPATH"),
            "virtual_env": os.environ.get("VIRTUAL_ENV"),
            "conda_env": os.environ.get("CONDA_DEFAULT_ENV"),
        }

        # 过滤掉None值
        return {k: v for k, v in env_info.items() if v is not None}


class AsyncSystemInfoContributor(AsyncInfoContributor):
    """异步系统信息贡献者"""

    def __init__(self, timeout: float = 3.0):
        super().__init__("system", timeout)

    @timeout(3.0)
    async def contribute(self) -> Dict[str, Any]:
        """贡献系统信息"""
        try:
            loop = asyncio.get_event_loop()

            # 并发获取系统信息
            tasks = [
                loop.run_in_executor(None, lambda: time.time()),
                loop.run_in_executor(None, lambda: os.getcwd()),
                loop.run_in_executor(None, lambda: len(os.listdir("."))),
                loop.run_in_executor(None, lambda: sys.executable),
                loop.run_in_executor(None, lambda: sys.prefix),
            ]

            current_time, cwd, file_count, executable, prefix = await asyncio.gather(*tasks, return_exceptions=True)

            return {
                "timestamp": current_time if not isinstance(current_time, Exception) else time.time(),
                "working_directory": cwd if not isinstance(cwd, Exception) else "unknown",
                "files_in_cwd": file_count if not isinstance(file_count, Exception) else 0,
                "python_executable": executable if not isinstance(executable, Exception) else "unknown",
                "python_prefix": prefix if not isinstance(prefix, Exception) else "unknown",
                "process_id": os.getpid(),
            }

        except Exception as e:
            logger.error(f"System info contribution failed: {e}")
            return {"error": str(e)}


class AsyncInfoEndpoint:
    """异步信息端点

    提供高性能的异步应用信息收集,支持并发执行多个信息贡献者.
    """

    def __init__(
        self,
        app_name: str = "Mini-Boot Application",
        app_version: str = "1.0.0",
        app_description: str = "Mini-Boot框架应用",
        cache_ttl: float = 300.0,  # 5分钟缓存(信息相对静态)
        max_concurrent: int = 10,
        timeout: float = 15.0,
    ):
        """初始化异步信息端点

        Args:
            app_name: 应用名称
            app_version: 应用版本
            app_description: 应用描述
            cache_ttl: 缓存TTL(秒)
            max_concurrent: 最大并发数
            timeout: 总超时时间(秒)
        """
        # 初始化基本属性(替代 super().__init__)
        self.endpoint_id = "info"
        self.enabled = True
        self.sensitive = False
        self.cache_enabled = True
        self.cache_ttl = cache_ttl
        self.max_concurrent = max_concurrent
        self.timeout = timeout

        # 注册默认异步信息贡献者
        self.contributors: Dict[str, AsyncInfoContributor] = {
            "app": AsyncAppInfoContributor(app_name, app_version, app_description),
            "build": AsyncBuildInfoContributor(),
            "git": AsyncGitInfoContributor(),
            "environment": AsyncEnvironmentInfoContributor(),
            "system": AsyncSystemInfoContributor(),
        }

        logger.info(f"AsyncInfoEndpoint initialized with {len(self.contributors)} contributors")

    def _create_operations(self) -> List[EndpointOperation]:
        """创建操作列表"""
        return [EndpointOperation(operation_type=OperationType.READ, method="GET", handler=self._get_info_async)]

    async def _execute_operation_async(self, operation_type: OperationType, **kwargs) -> Any:
        """执行异步操作"""
        if operation_type == OperationType.READ:
            return await self._get_info_async(**kwargs)
        else:
            raise ValueError(f"Unsupported operation: {operation_type}")

    @cached(ttl=300.0)  # 5分钟缓存
    async def _get_info_async(self, **kwargs) -> Dict[str, Any]:
        """异步获取应用信息 - 并发执行所有信息贡献者"""
        start_time = time.time()

        try:
            # 并发执行所有信息贡献者
            contributor_tasks = []
            contributor_names = []

            for name, contributor in self.contributors.items():
                task = asyncio.create_task(self._contribute_timeout(name, contributor), name=f"info_contribute_{name}")
                contributor_tasks.append(task)
                contributor_names.append(name)

            # 等待所有贡献者完成
            contributor_results = await asyncio.gather(*contributor_tasks, return_exceptions=True)

            # 处理结果
            info = {}
            for name, result in zip(contributor_names, contributor_results):
                if isinstance(result, Exception):
                    info[name] = {"error": str(result)}
                    logger.error(f"Info contribution failed for {name}: {result}")
                else:
                    if result:  # 只添加非空的贡献
                        info[name] = result

            execution_time = time.time() - start_time

            # 添加元信息
            info["_meta"] = {
                "timestamp": datetime.now().isoformat(),
                "execution_time_ms": round(execution_time * 1000, 2),
                "contributors_count": len(self.contributors),
                "concurrent_execution": True,
            }

            return info

        except Exception as e:
            logger.error(f"Info collection execution failed: {e}")
            return {
                "_meta": {
                    "timestamp": datetime.now().isoformat(),
                    "execution_time_ms": round((time.time() - start_time) * 1000, 2),
                    "contributors_count": len(self.contributors),
                    "concurrent_execution": False,
                    "error": str(e),
                }
            }

    async def _contribute_timeout(self, name: str, contributor: AsyncInfoContributor) -> Dict[str, Any]:
        """带超时的信息贡献"""
        try:
            return await asyncio.wait_for(contributor.contribute_async(), timeout=contributor.timeout)
        except asyncio.TimeoutError:
            logger.warning(f"Info contribution timeout for {name} after {contributor.timeout}s")
            return {"error": f"Timeout after {contributor.timeout}s"}
        except Exception as e:
            logger.error(f"Info contribution error for {name}: {e}")
            return {"error": str(e)}

    def register_contributor(self, contributor: AsyncInfoContributor) -> None:
        """注册异步信息贡献者"""
        if not isinstance(contributor, AsyncInfoContributor):
            raise TypeError("contributor must be an instance of AsyncInfoContributor")
        self.contributors[contributor.name] = contributor
        logger.info(f"Registered async info contributor: {contributor.name}")

    def unregister_contributor(self, name: str) -> bool:
        """注销信息贡献者"""
        if name in self.contributors:
            del self.contributors[name]
            logger.info(f"Unregistered info contributor: {name}")
            return True
        return False

    def get_contributor(self, name: str) -> Optional[AsyncInfoContributor]:
        """获取信息贡献者"""
        return self.contributors.get(name)

    def get_all_contributors(self) -> Dict[str, AsyncInfoContributor]:
        """获取所有信息贡献者"""
        return self.contributors.copy()

    def set_app_info(self, name: str, version: str, description: str = None) -> None:
        """设置应用信息"""
        app_contributor = self.contributors.get("app")
        if isinstance(app_contributor, AsyncAppInfoContributor):
            app_contributor.app_name = name
            app_contributor.app_version = version
            if description:
                app_contributor.app_description = description

    def set_git_info(self, branch: str, commit: str) -> None:
        """设置 Git 信息"""
        git_contributor = self.contributors.get("git")
        if isinstance(git_contributor, AsyncGitInfoContributor):
            git_contributor.git_branch = branch
            git_contributor.git_commit = commit

    def __str__(self) -> str:
        return f"AsyncInfoEndpoint(contributors={len(self.contributors)}, cache_enabled={self.cache_enabled})"
