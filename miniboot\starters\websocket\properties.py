#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket Starter 配置属性类
"""

from dataclasses import dataclass, field

from miniboot.annotations import ConfigurationProperties
from miniboot.autoconfigure.properties import StarterProperties


@dataclass
class ServerConfig:
    """WebSocket 服务器配置"""

    host: str = "0.0.0.0"
    port: int = 8080
    read_timeout: int = 10
    write_timeout: int = 10
    idle_timeout: int = 60


@dataclass
class AuthConfig:
    """认证配置"""

    enabled: bool = False
    token_header: str = "Authorization"
    token_query_param: str = "token"
    token_cookie: str = "auth_token"


@dataclass
class JWTConfig:
    """JWT 配置"""

    enabled: bool = False
    secret_key: str = "mini-boot-websocket-jwt-secret-key"
    issuer: str = "mini-boot-websocket"
    expiration_time: int = 3600


@dataclass
class SecurityConfig:
    """安全配置"""

    allowed_origins: list[str] = field(default_factory=lambda: ["*"])
    auth: AuthConfig = field(default_factory=AuthConfig)
    jwt: JWTConfig = field(default_factory=JWTConfig)


@dataclass
class CompressionConfig:
    """压缩配置"""

    enabled: bool = False
    level: int = 6


@dataclass
class TimeoutConfig:
    """超时配置"""

    enabled: bool = False
    duration: int = 5
    ping_interval: int = 54
    pong_timeout: int = 60


@dataclass
class ConnectionLimitConfig:
    """连接限制配置"""

    enabled: bool = False
    max_connections_per_user: int = 5
    max_total_connections: int = 1000


@ConfigurationProperties(prefix="miniboot.starters.websocket")
@dataclass
class WebSocketProperties(StarterProperties):
    """WebSocket Starter 配置属性

    提供 WebSocket 功能的完整配置选项,包括服务器配置、安全配置、
    压缩配置、超时配置和连接限制配置等.
    """

    # 基础配置
    enabled: bool = False  # 默认禁用,根据需要启用
    path: str = "/ws"
    max_message_size: int = 512 * 1024  # 512KB

    # 服务器配置
    server: ServerConfig = field(default_factory=ServerConfig)

    # 安全配置
    security: SecurityConfig = field(default_factory=SecurityConfig)

    # 压缩配置
    compression: CompressionConfig = field(default_factory=CompressionConfig)

    # 超时配置
    timeout: TimeoutConfig = field(default_factory=TimeoutConfig)

    # 连接限制配置
    connection_limit: ConnectionLimitConfig = field(default_factory=ConnectionLimitConfig)

    def validate(self) -> None:
        """验证配置属性的有效性

        Raises:
            ValueError: 配置无效时抛出
        """
        super().validate()

        if self.max_message_size <= 0:
            raise ValueError("max_message_size must be positive")

        if self.server.port <= 0 or self.server.port > 65535:
            raise ValueError("server.port must be between 1 and 65535")

        if self.server.read_timeout <= 0:
            raise ValueError("server.read_timeout must be positive")

        if self.server.write_timeout <= 0:
            raise ValueError("server.write_timeout must be positive")

        if self.server.idle_timeout <= 0:
            raise ValueError("server.idle_timeout must be positive")

        if self.compression.enabled and (self.compression.level < 1 or self.compression.level > 9):
            raise ValueError("compression.level must be between 1 and 9")

        if self.connection_limit.enabled:
            if self.connection_limit.max_connections_per_user <= 0:
                raise ValueError("connection_limit.max_connections_per_user must be positive")

            if self.connection_limit.max_total_connections <= 0:
                raise ValueError("connection_limit.max_total_connections must be positive")
