#!/usr/bin/env python
"""
* @author: cz
* @description: Profile 注解实现

实现 @Profile 注解,用于基于环境配置的条件化 Bean 创建.
支持多环境配置和复杂的 Profile 表达式.
"""

from typing import Any, Callable, Union, Optional
from .metadata import ProfileMetadata


def Profile(  # noqa: N802
    *profiles: str, expression: Optional[str] = None
) -> Callable[[Union[type, Callable[..., Any]]], Union[type, Callable[..., Any]]]:
    """Profile 条件装配注解装饰器

    根据当前激活的 Profile 决定是否创建 Bean.
    支持多个 Profile 和复杂的 Profile 表达式.

    Args:
        *profiles: Profile 名称列表,支持多个 Profile
        expression: Profile 表达式,支持逻辑运算符(&, |, !)

    Returns:
        装饰器函数

    Examples:
        # 单个 Profile
        @Profile("dev")
        class DevService:
            pass

        # 多个 Profile(OR 关系)
        @Profile("dev", "test")
        class DevTestService:
            pass

        # Profile 表达式
        @Profile(expression="dev & !prod")
        class DevNotProdService:
            pass

        # 复杂表达式
        @Profile(expression="(dev | test) & !prod")
        class NonProdService:
            pass
    """

    def decorator(target: Union[type, Callable[..., Any]]) -> Union[type, Callable[..., Any]]:
        # 创建 Profile 元数据
        metadata = ProfileMetadata(profiles=list(profiles), expression=expression)

        # 存储元数据
        target.__profile_metadata__ = metadata  # type: ignore[union-attr]
        target.__is_profile__ = True  # type: ignore[union-attr]
        target.__profile_names__ = list(profiles)  # type: ignore[union-attr]
        target.__profile_expression__ = expression  # type: ignore[union-attr]

        return target

    return decorator


def is_profile(target: Any) -> bool:
    """检查目标是否有 @Profile 注解

    Args:
        target: 要检查的目标(类或方法)

    Returns:
        True 表示有 @Profile 注解
    """
    return hasattr(target, "__is_profile__") and target.__is_profile__


def get_profile_metadata(target: Any) -> Optional[ProfileMetadata]:
    """获取 @Profile 注解的元数据

    Args:
        target: 目标对象

    Returns:
        Profile 元数据,如果没有则返回 None
    """
    return getattr(target, "__profile_metadata__", None)


def get_profile_names(target: Any) -> list[str]:
    """获取 @Profile 注解的 Profile 名称列表

    Args:
        target: 目标对象

    Returns:
        Profile 名称列表
    """
    return getattr(target, "__profile_names__", [])


def get_profile_expression(target: Any) -> Optional[str]:
    """获取 @Profile 注解的表达式

    Args:
        target: 目标对象

    Returns:
        Profile 表达式,如果没有则返回 None
    """
    return getattr(target, "__profile_expression__", None)
