#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置模块导出
"""

from ..errors.domains.autoconfigure import \
    AutoConfigurationError as AutoConfigurationException
from ..errors.domains.autoconfigure import \
    ConfigurationClassError as ConfigurationConflictException
from .base import AutoConfiguration, StarterAutoConfiguration
from .conditions import (ConditionalOnBean, ConditionalOnClass,
                         ConditionalOnMissingBean, ConditionalOnProperty,
                         ConditionalOnResource, ConditionEvaluator,
                         ConfigurationCondition, get_conditions,
                         has_conditions)
from .loader import (AutoConfigurationDiscovery, AutoConfigurationLoader,
                     AutoConfigurationRegistry)
from .metadata import AutoConfigurationMetadata
from .properties import StarterProperties

__all__ = [
    # 基础类
    "AutoConfiguration",
    "StarterAutoConfiguration",
    "StarterProperties",
    "AutoConfigurationMetadata",
    # 注册表和发现器
    "AutoConfigurationRegistry",
    "AutoConfigurationDiscovery",
    "AutoConfigurationLoader",
    # 异常类
    "AutoConfigurationException",
    "ConfigurationConflictException",
    # 条件类
    "ConfigurationCondition",
    "ConditionEvaluator",
    # 条件装饰器
    "ConditionalOnProperty",
    "ConditionalOnClass",
    "ConditionalOnBean",
    "ConditionalOnMissingBean",
    "ConditionalOnResource",
    "get_conditions",
    "has_conditions",
]
