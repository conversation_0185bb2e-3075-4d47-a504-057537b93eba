#!/usr/bin/env python
"""
* @author: cz
* @description: YAML属性源加载器单元测试
"""

import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

from miniboot.env.resource.loader import DefaultResourceLoader
from miniboot.env.resource.yaml_loader import YamlPropertySourceLoader
from miniboot.env.sources import PropertySource


class YamlPropertySourceLoaderTestCase(unittest.TestCase):
    """YAML属性源加载器测试用例"""

    def setUp(self):
        """测试前准备"""
        self.loader = YamlPropertySourceLoader()
        self.resource_loader = DefaultResourceLoader()
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """测试后清理"""
        import shutil

        if Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)

    def test_get_file_extensions(self):
        """测试获取支持的文件扩展名"""
        extensions = self.loader.get_file_extensions()
        self.assertEqual(extensions, [".yaml", ".yml"])

    def test_load_simple_yaml(self):
        """测试加载简单YAML文件"""
        # 创建测试YAML文件
        yaml_content = """
app:
  name: test-app
  version: 1.0.0
database:
  host: localhost
  port: 5432
"""
        yaml_file = Path(self.temp_dir) / "test.yaml"
        yaml_file.write_text(yaml_content, encoding="utf-8")

        # 加载YAML文件
        property_sources = self.loader.load("test", self.resource_loader, str(yaml_file))

        self.assertEqual(len(property_sources), 1)
        source = property_sources[0]
        self.assertIsInstance(source, PropertySource)
        self.assertEqual(source.name, "test")

        # 验证属性值
        self.assertEqual(source.get_property("app.name"), "test-app")
        self.assertEqual(source.get_property("app.version"), "1.0.0")
        self.assertEqual(source.get_property("database.host"), "localhost")
        self.assertEqual(source.get_property("database.port"), 5432)

    def test_load_yaml_with_lists(self):
        """测试加载包含列表的YAML文件"""
        yaml_content = """
servers:
  - name: server1
    host: ***********
  - name: server2
    host: ***********
tags:
  - production
  - web
"""
        yaml_file = Path(self.temp_dir) / "test.yaml"
        yaml_file.write_text(yaml_content, encoding="utf-8")

        property_sources = self.loader.load("test", self.resource_loader, str(yaml_file))
        source = property_sources[0]

        # 验证列表属性
        self.assertEqual(source.get_property("servers[0].name"), "server1")
        self.assertEqual(source.get_property("servers[0].host"), "***********")
        self.assertEqual(source.get_property("servers[1].name"), "server2")
        self.assertEqual(source.get_property("servers[1].host"), "***********")
        self.assertEqual(source.get_property("tags[0]"), "production")
        self.assertEqual(source.get_property("tags[1]"), "web")

    def test_load_empty_yaml(self):
        """测试加载空YAML文件"""
        yaml_file = Path(self.temp_dir) / "empty.yaml"
        yaml_file.write_text("", encoding="utf-8")

        property_sources = self.loader.load("empty", self.resource_loader, str(yaml_file))

        self.assertEqual(len(property_sources), 1)
        source = property_sources[0]
        self.assertEqual(source.name, "empty")
        # 空文件应该有空的属性字典
        self.assertEqual(len(source.properties), 0)

    def test_load_yaml_with_null_values(self):
        """测试加载包含null值的YAML文件"""
        yaml_content = """
app:
  name: test-app
  description: null
  enabled: true
"""
        yaml_file = Path(self.temp_dir) / "test.yaml"
        yaml_file.write_text(yaml_content, encoding="utf-8")

        property_sources = self.loader.load("test", self.resource_loader, str(yaml_file))
        source = property_sources[0]

        self.assertEqual(source.get_property("app.name"), "test-app")
        self.assertIsNone(source.get_property("app.description"))
        self.assertTrue(source.get_property("app.enabled"))

    def test_load_invalid_yaml(self):
        """测试加载无效YAML文件"""
        # 创建无效的YAML文件
        invalid_yaml = """
app:
  name: test-app
    invalid: indentation
"""
        yaml_file = Path(self.temp_dir) / "invalid.yaml"
        yaml_file.write_text(invalid_yaml, encoding="utf-8")

        # 应该抛出异常
        from miniboot.env.resource.loader import PropertySourceLoadError

        with self.assertRaises(PropertySourceLoadError):
            self.loader.load("invalid", self.resource_loader, str(yaml_file))

    def test_load_non_existent_file(self):
        """测试加载不存在的文件"""
        non_existent_file = str(Path(self.temp_dir) / "non_existent.yaml")

        from miniboot.env.resource.loader import PropertySourceLoadError

        with self.assertRaises(PropertySourceLoadError):
            self.loader.load("test", self.resource_loader, non_existent_file)

    @patch("miniboot.env.resource.yaml_loader.yaml", None)
    def test_yaml_not_available(self):
        """测试YAML库不可用时的情况"""
        yaml_file = Path(self.temp_dir) / "test.yaml"
        yaml_file.write_text("app:\n  name: test", encoding="utf-8")

        from miniboot.env.resource.loader import PropertySourceLoadError

        with self.assertRaises(PropertySourceLoadError):
            self.loader.load("test", self.resource_loader, str(yaml_file))

    def test_flatten_dict_complex(self):
        """测试复杂字典展平功能"""
        complex_data = {
            "app": {
                "name": "test-app",
                "database": {"host": "localhost", "port": 5432, "credentials": {"username": "admin", "password": "secret"}},
                "servers": [{"name": "server1", "port": 8080}, {"name": "server2", "port": 8081}],
            }
        }

        flattened = self.loader._flatten_dict(complex_data)

        # 验证关键的展平属性存在
        self.assertEqual(flattened["app.name"], "test-app")
        self.assertEqual(flattened["app.database.host"], "localhost")
        self.assertEqual(flattened["app.database.port"], 5432)
        self.assertEqual(flattened["app.database.credentials.username"], "admin")
        self.assertEqual(flattened["app.database.credentials.password"], "secret")
        self.assertEqual(flattened["app.servers[0].name"], "server1")
        self.assertEqual(flattened["app.servers[0].port"], 8080)
        self.assertEqual(flattened["app.servers[1].name"], "server2")
        self.assertEqual(flattened["app.servers[1].port"], 8081)

        # 验证原始列表也被保存
        self.assertIn("app.servers", flattened)
        self.assertIsInstance(flattened["app.servers"], list)


if __name__ == "__main__":
    unittest.main()
