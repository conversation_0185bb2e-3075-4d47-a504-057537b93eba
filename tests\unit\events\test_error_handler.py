#!/usr/bin/env python
"""
* @author: cz
* @description: 事件错误处理器单元测试

测试事件错误处理器的功能，包括错误收集、恢复策略、统计分析等。
"""

import time
import unittest
from datetime import datetime
from unittest.mock import Mock

from miniboot.errors.events import AsyncEventExecutionError, EventHandlerError, EventPublishError
from miniboot.events.error_handler import DefaultEventErrorHandler, ErrorHandler, ErrorRecord, ErrorSeverity, EventErrorCollector, RecoveryStrategy


class TestErrorRecord(unittest.TestCase):
    """测试ErrorRecord数据类"""

    def test_error_record_creation(self):
        """测试错误记录创建"""
        error = ValueError("Test error")
        severity = ErrorSeverity.HIGH
        context = {"handler": "test_handler"}

        record = ErrorRecord(error, severity, context)

        self.assertEqual(record.error, error)
        self.assertEqual(record.severity, severity)
        self.assertEqual(record.context, context)
        self.assertIsInstance(record.timestamp, datetime)
        self.assertFalse(record.recovery_attempted)
        self.assertFalse(record.recovery_successful)
        self.assertEqual(record.retry_count, 0)

    def test_error_record_to_dict(self):
        """测试错误记录转换为字典"""
        error = RuntimeError("Test error")
        record = ErrorRecord(error, ErrorSeverity.MEDIUM, {"test": "value"})
        record.recovery_attempted = True
        record.retry_count = 2

        result = record.to_dict()

        self.assertEqual(result["error_type"], "RuntimeError")
        self.assertEqual(result["error_message"], "Test error")
        self.assertEqual(result["severity"], "medium")
        self.assertEqual(result["context"], {"test": "value"})
        self.assertTrue(result["recovery_attempted"])
        self.assertFalse(result["recovery_successful"])
        self.assertEqual(result["retry_count"], 2)
        self.assertIn("timestamp", result)


class TestDefaultEventErrorHandler(unittest.TestCase):
    """测试DefaultEventErrorHandler默认错误处理器"""

    def setUp(self):
        """设置测试环境"""
        self.handler = DefaultEventErrorHandler(max_retries=3)

    def test_can_handle_event_exceptions(self):
        """测试是否可以处理事件异常"""
        event_error = EventHandlerError("handler", "Event", "Failed")
        non_event_error = ValueError("Not an event error")

        self.assertTrue(self.handler.can_handle(event_error))
        self.assertFalse(self.handler.can_handle(non_event_error))

    def test_handle_event_handler_error_retry(self):
        """测试处理事件处理器错误 - 重试策略"""
        error = EventHandlerError("handler", "Event", "Failed")
        context = {"retry_count": 1}

        strategy = self.handler.handle_error(error, context)

        self.assertEqual(strategy, RecoveryStrategy.RETRY)

    def test_handle_event_handler_error_ignore(self):
        """测试处理事件处理器错误 - 忽略策略"""
        error = EventHandlerError("handler", "Event", "Failed")
        context = {"retry_count": 5}  # 超过最大重试次数

        strategy = self.handler.handle_error(error, context)

        self.assertEqual(strategy, RecoveryStrategy.IGNORE)

    def test_handle_event_publish_error_fallback(self):
        """测试处理事件发布错误 - 降级策略"""
        error = EventPublishError("Event", "Failed", ["handler1"])
        context = {}

        strategy = self.handler.handle_error(error, context)

        self.assertEqual(strategy, RecoveryStrategy.FALLBACK)

    def test_handle_async_execution_error_retry(self):
        """测试处理异步执行错误 - 重试策略"""
        error = AsyncEventExecutionError("Event", "handler", "Failed", 2.0)
        context = {"retry_count": 1}

        strategy = self.handler.handle_error(error, context)

        self.assertEqual(strategy, RecoveryStrategy.RETRY)

    def test_get_severity_handler_error(self):
        """测试获取处理器错误严重程度"""
        error = EventHandlerError("handler", "Event", "Failed")

        severity = self.handler.get_severity(error)

        self.assertEqual(severity, ErrorSeverity.MEDIUM)

    def test_get_severity_publish_error_high(self):
        """测试获取发布错误严重程度 - 高严重程度"""
        failed_handlers = [f"handler{i}" for i in range(10)]  # 10个失败处理器
        error = EventPublishError("Event", "Failed", failed_handlers)

        severity = self.handler.get_severity(error)

        self.assertEqual(severity, ErrorSeverity.HIGH)

    def test_get_severity_async_error_high(self):
        """测试获取异步错误严重程度 - 高严重程度"""
        error = AsyncEventExecutionError("Event", "handler", "Failed", 35.0)  # 执行时间过长

        severity = self.handler.get_severity(error)

        self.assertEqual(severity, ErrorSeverity.HIGH)


class TestEventErrorCollector(unittest.TestCase):
    """测试EventErrorCollector错误收集器"""

    def setUp(self):
        """设置测试环境"""
        self.collector = EventErrorCollector(max_records=100, retention_hours=1)

    def test_add_and_remove_error_handler(self):
        """测试添加和移除错误处理器"""
        handler = Mock(spec=ErrorHandler)

        # 添加处理器
        self.collector.add_error_handler(handler)
        self.assertIn(handler, self.collector._error_handlers)

        # 移除处理器
        result = self.collector.remove_error_handler(handler)
        self.assertTrue(result)
        self.assertNotIn(handler, self.collector._error_handlers)

        # 移除不存在的处理器
        result = self.collector.remove_error_handler(handler)
        self.assertFalse(result)

    def test_collect_error_with_custom_handler(self):
        """测试使用自定义处理器收集错误"""
        # 创建模拟处理器
        handler = Mock(spec=ErrorHandler)
        handler.can_handle.return_value = True
        handler.get_severity.return_value = ErrorSeverity.HIGH
        handler.handle_error.return_value = RecoveryStrategy.RETRY

        self.collector.add_error_handler(handler)

        error = EventHandlerError("handler", "Event", "Failed")
        context = {"test": "value"}

        strategy = self.collector.collect_error(error, context)

        self.assertEqual(strategy, RecoveryStrategy.RETRY)
        handler.can_handle.assert_called_once_with(error)
        handler.get_severity.assert_called_once_with(error)
        handler.handle_error.assert_called_once_with(error, context)

        # 检查错误记录
        self.assertEqual(len(self.collector._records), 1)
        record = self.collector._records[0]
        self.assertEqual(record.error, error)
        self.assertEqual(record.severity, ErrorSeverity.HIGH)

    def test_collect_error_with_default_handler(self):
        """测试使用默认处理器收集错误"""
        error = EventHandlerError("handler", "Event", "Failed")
        context = {"retry_count": 1}

        strategy = self.collector.collect_error(error, context)

        self.assertEqual(strategy, RecoveryStrategy.RETRY)
        self.assertEqual(len(self.collector._records), 1)

    def test_error_statistics(self):
        """测试错误统计信息"""
        # 添加多个错误
        errors = [
            EventHandlerError("handler1", "Event", "Failed"),
            EventHandlerError("handler2", "Event", "Failed"),
            EventPublishError("Event", "Failed"),
        ]

        for error in errors:
            self.collector.collect_error(error, {})

        stats = self.collector.get_error_statistics()

        self.assertEqual(stats["total_errors"], 3)
        self.assertEqual(stats["error_counts_by_type"]["EventHandlerError"], 2)
        self.assertEqual(stats["error_counts_by_type"]["EventPublishError"], 1)
        self.assertIn("error_counts_by_severity", stats)
        self.assertIn("recent_errors", stats)
        self.assertEqual(len(stats["recent_errors"]), 3)

    def test_error_trends(self):
        """测试错误趋势分析"""
        # 添加一些错误
        for i in range(5):
            error = EventHandlerError(f"handler{i}", "Event", "Failed")
            self.collector.collect_error(error, {})

        trends = self.collector.get_error_trends(hours=24)

        self.assertEqual(trends["total_errors"], 5)
        self.assertEqual(trends["period_hours"], 24)
        self.assertGreater(trends["error_rate"], 0)
        self.assertIn("most_common_errors", trends)
        self.assertIn("severity_distribution", trends)

    def test_error_trends_empty(self):
        """测试空错误趋势分析"""
        trends = self.collector.get_error_trends(hours=24)

        self.assertEqual(trends["total_errors"], 0)
        self.assertEqual(trends["error_rate"], 0.0)
        self.assertEqual(trends["most_common_errors"], [])
        self.assertEqual(trends["severity_distribution"], {})

    def test_cleanup_expired_records(self):
        """测试清理过期记录"""
        # 创建一个保留时间很短的收集器
        collector = EventErrorCollector(retention_hours=0.0001)  # 约0.36秒

        # 添加错误
        error = EventHandlerError("handler", "Event", "Failed")
        collector.collect_error(error, {})

        self.assertEqual(len(collector._records), 1)

        # 等待记录过期
        time.sleep(0.5)  # 等待更长时间确保过期

        # 触发清理
        collector._cleanup_expired_records()

        self.assertEqual(len(collector._records), 0)

    def test_clear_records(self):
        """测试清空记录"""
        # 添加一些错误
        for i in range(3):
            error = EventHandlerError(f"handler{i}", "Event", "Failed")
            self.collector.collect_error(error, {})

        self.assertEqual(len(self.collector._records), 3)

        # 清空记录
        count = self.collector.clear_records()

        self.assertEqual(count, 3)
        self.assertEqual(len(self.collector._records), 0)
        self.assertEqual(len(self.collector._error_counts), 0)
        self.assertEqual(len(self.collector._severity_counts), 0)

    def test_thread_safety(self):
        """测试线程安全性"""
        import threading

        def add_errors():
            for i in range(10):
                error = EventHandlerError(f"handler{i}", "Event", "Failed")
                self.collector.collect_error(error, {})

        # 创建多个线程同时添加错误
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=add_errors)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证所有错误都被正确记录
        self.assertEqual(len(self.collector._records), 50)


if __name__ == "__main__":
    unittest.main()
