"""
Load Monitoring Starter

负载监控 Starter 组件，提供系统和应用负载的实时监控功能。

主要功能:
- 系统资源监控 (CPU、内存)
- 应用性能监控 (响应时间、吞吐量)
- 负载状态评估和回调通知
- 配置驱动的监控策略

使用方式:
```yaml
miniboot:
  starters:
    web:
      load-monitoring:
        enabled: true
        monitor-interval: 3.0
        cpu-threshold: 80.0
        memory-threshold: 85.0
```
"""

from .monitor import LoadMonitor
from .properties import LoadMonitorProperties

__all__ = [
    "LoadMonitor",
    "LoadMonitorProperties",
]