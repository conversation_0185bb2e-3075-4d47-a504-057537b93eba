#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置与应用上下文集成模块
"""

from typing import Any, Optional

from loguru import logger

from miniboot.context import ApplicationContext

from ..errors.domains.autoconfigure import \
    AutoConfigurationError as AutoConfigurationException
from .loader import AutoConfigurationLoader


class AutoConfigurationIntegrator:
    """自动配置集成器

    负责将自动配置机制集成到ApplicationContext的启动流程中.
    """

    def __init__(self, context: ApplicationContext):
        """初始化集成器

        Args:
            context: 应用上下文实例
        """
        self.context = context
        self.loader = AutoConfigurationLoader()
        self.registry = self.loader.get_registry()
        self._enabled = True
        self._excluded_configurations: list[str] = []
        self._additional_packages: list[str] = []

    def set_enabled(self, enabled: bool) -> None:
        """设置是否启用自动配置

        Args:
            enabled: 是否启用
        """
        self._enabled = enabled
        logger.debug(f"Auto configuration {'enabled' if enabled else 'disabled'}")

    def exclude_configurations(self, *config_names: str) -> None:
        """排除指定的自动配置

        Args:
            config_names: 要排除的配置名称列表
        """
        self._excluded_configurations.extend(config_names)
        logger.debug(f"Excluded auto configurations: {config_names}")

    def add_scan_packages(self, *packages: str) -> None:
        """添加要扫描的包

        Args:
            packages: 包名列表
        """
        self._additional_packages.extend(packages)
        logger.debug(f"Added scan packages: {packages}")

    def execute_configs(self) -> dict[str, bool]:
        """执行自动配置

        Returns:
            Dict[str, bool]: 配置名称到执行结果的映射
        """
        if not self._enabled:
            logger.info("Auto configuration is disabled, skipping execution")
            return {}

        try:
            logger.info("Executing auto configurations...")

            # 执行所有配置
            results = self.registry.configure_all(self.context)

            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)

            logger.info(f"Auto configuration execution completed: {success_count}/{total_count} successful")

            # 记录失败的配置
            failed_configs = [name for name, success in results.items() if not success]
            if failed_configs:
                logger.warning(f"Failed auto configurations: {failed_configs}")

            return results

        except Exception as e:
            logger.error(f"Failed to execute auto configurations: {e}")
            raise AutoConfigurationException(f"Auto configuration execution failed: {e}")

    def get_configuration_info(self) -> dict[str, Any]:
        """获取配置信息

        Returns:
            Dict[str, Any]: 配置信息
        """
        info = self.registry.get_registry_info()
        info.update(
            {
                "enabled": self._enabled,
                "excluded_configurations": self._excluded_configurations.copy(),
                "additional_packages": self._additional_packages.copy(),
            }
        )
        return info

    def _remove_excluded_configurations(self) -> None:
        """移除排除的配置"""
        for config_name in self._excluded_configurations:
            if config_name in self.registry._configurations:
                del self.registry._configurations[config_name]
                if config_name in self.registry._metadata_cache:
                    del self.registry._metadata_cache[config_name]
                logger.debug(f"Excluded auto configuration: {config_name}")


class AutoConfigurationContextEnhancer:
    """自动配置上下文增强器

    为ApplicationContext添加自动配置支持.
    """

    @staticmethod
    def enhance_context(
        context: ApplicationContext,
        enabled: bool = True,
        excluded_configurations: Optional[list[str]] = None,
        additional_packages: Optional[list[str]] = None,
    ) -> AutoConfigurationIntegrator:
        """增强应用上下文以支持自动配置

        Args:
            context: 应用上下文
            enabled: 是否启用自动配置
            excluded_configurations: 排除的配置列表
            additional_packages: 额外扫描的包列表

        Returns:
            AutoConfigurationIntegrator: 自动配置集成器
        """
        integrator = AutoConfigurationIntegrator(context)

        # 配置集成器
        integrator.set_enabled(enabled)

        if excluded_configurations:
            integrator.exclude_configurations(*excluded_configurations)

        if additional_packages:
            integrator.add_scan_packages(*additional_packages)

        # 将集成器附加到上下文
        if not hasattr(context, "_auto_config_integrator"):
            context._auto_config_integrator = integrator

        logger.debug("ApplicationContext enhanced with auto configuration support")
        return integrator

    @staticmethod
    def get_integrator(context: ApplicationContext) -> Optional[AutoConfigurationIntegrator]:
        """获取上下文的自动配置集成器

        Args:
            context: 应用上下文

        Returns:
            Optional[AutoConfigurationIntegrator]: 集成器实例或None
        """
        return getattr(context, "_auto_config_integrator", None)


def enable_auto_configuration(
    context: ApplicationContext, excluded_configurations: Optional[list[str]] = None, additional_packages: Optional[list[str]] = None
) -> AutoConfigurationIntegrator:
    """为应用上下文启用自动配置

    这是一个便捷函数,用于快速为现有的ApplicationContext启用自动配置功能.

    Args:
        context: 应用上下文
        excluded_configurations: 要排除的配置名称列表
        additional_packages: 额外要扫描的包列表

    Returns:
        AutoConfigurationIntegrator: 自动配置集成器

    Example:
        >>> context = DefaultApplicationContext()
        >>> integrator = enable_auto_configuration(
        ...     context,
        ...     excluded_configurations=["unwanted-config"],
        ...     additional_packages=["my.custom.configs"],
        ... )
        >>> await context.start()
    """
    return AutoConfigurationContextEnhancer.enhance_context(
        context=context, enabled=True, excluded_configurations=excluded_configurations, additional_packages=additional_packages
    )


def disable_auto_configuration(context: ApplicationContext) -> None:
    """为应用上下文禁用自动配置

    Args:
        context: 应用上下文
    """
    integrator = AutoConfigurationContextEnhancer.get_integrator(context)
    if integrator:
        integrator.set_enabled(False)
        logger.info("Auto configuration disabled for context")
    else:
        logger.debug("No auto configuration integrator found for context")


def get_auto_configuration_info(context: ApplicationContext) -> Optional[dict[str, Any]]:
    """获取应用上下文的自动配置信息

    Args:
        context: 应用上下文

    Returns:
        Optional[Dict[str, Any]]: 配置信息或None
    """
    integrator = AutoConfigurationContextEnhancer.get_integrator(context)
    if integrator:
        return integrator.get_configuration_info()
    return None
