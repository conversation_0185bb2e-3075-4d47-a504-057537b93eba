# Mini-Boot 示例项目集合

欢迎来到 Mini-Boot 框架的示例项目集合！这里包含了从基础入门到企业级应用的完整学习路径。

## 🎯 学习路径

### 🌱 基础示例 (Foundation Examples)

适合框架新手和 Python 初学者

-   **[01-hello-world](01-hello-world/)** - 最简单的 Mini-Boot 应用
-   **[02-dependency-injection](02-dependency-injection/)** - 依赖注入基础
-   **[03-configuration](03-configuration/)** - 配置管理和属性注入
-   **[04-events](04-events/)** - 事件系统基础

### 🚀 进阶示例 (Advanced Examples)

适合有一定经验的开发者

-   **[05-auto-configuration](05-auto-configuration/)** - 自定义自动配置
-   **[06-bean-lifecycle](06-bean-lifecycle/)** - Bean 生命周期管理
-   **[07-starters](07-starters/)** - Starters 使用和开发
-   **[08-multi-module](08-multi-module/)** - 多模块应用架构

### 🏢 实战示例 (Real-world Examples)

适合准备在生产环境使用的开发者

-   **[09-web-service](09-web-service/)** - Web API 服务
-   **[10-data-access](10-data-access/)** - 数据访问层
-   **[11-scheduled-tasks](11-scheduled-tasks/)** - 定时任务系统
-   **[12-actuator-endpoints](12-actuator-endpoints/)** - Actuator 监控端点
-   **[13-library-system](13-library-system/)** - 图书管理系统

## 🛠️ 快速开始

### 环境要求

-   Python 3.8+
-   pip 或 poetry
-   Git

### 运行示例

```bash
# 克隆项目
git clone https://github.com/your-org/mini-boot.git
cd mini-boot/samples

# 选择一个示例项目
cd 01-hello-world

# 安装依赖
pip install -r requirements.txt

# 运行示例
python main.py
```

## 📚 学习建议

### 对于初学者

1. 从 `01-hello-world` 开始，理解基本概念
2. 按顺序学习基础示例 (01-04)
3. 阅读每个示例的 README.md 文档
4. 尝试修改代码，观察效果

### 对于有经验的开发者

1. 可以直接从进阶示例开始 (05-07)
2. 重点关注实战示例 (08-13)
3. 参考最佳实践和设计模式
4. 考虑在实际项目中应用

### 对于企业开发者

1. 重点学习实战示例
2. 关注性能、监控、部署相关内容
3. 参考完整的电商系统示例
4. 考虑微服务架构的应用

## 🔧 技术栈

### 核心框架

-   **Mini-Boot** - 主框架
-   **Python 3.8+** - 编程语言

### 常用集成

-   **FastAPI** - Web 框架
-   **SQLAlchemy** - ORM
-   **PostgreSQL/MySQL** - 数据库
-   **Redis** - 缓存
-   **RabbitMQ** - 消息队列
-   **Docker** - 容器化

## 📖 文档结构

每个示例项目都包含：

```
XX-example-name/
├── README.md           # 项目文档和使用指南
├── main.py            # 应用入口点
├── requirements.txt   # Python依赖
├── config/           # 配置文件
├── src/              # 源代码
├── tests/            # 测试代码
├── docs/             # 详细文档
└── scripts/          # 辅助脚本
```

## 🤝 贡献指南

我们欢迎社区贡献新的示例项目！

### 贡献流程

1. Fork 项目
2. 创建特性分支
3. 添加示例项目
4. 编写文档和测试
5. 提交 Pull Request

### 示例要求

-   代码清晰易懂
-   包含完整文档
-   提供测试用例
-   遵循项目规范

## 🆘 获取帮助

### 常见问题

-   查看各示例的 FAQ 部分
-   搜索 GitHub Issues
-   参考官方文档

### 社区支持

-   GitHub Issues - 报告问题和建议
-   Discussions - 技术讨论和交流
-   Wiki - 详细文档和教程

### 联系方式

-   邮箱：<EMAIL>
-   官网：https://mini-boot.org
-   文档：https://docs.mini-boot.org

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。

## 🙏 致谢

感谢所有为 Mini-Boot 项目做出贡献的开发者和社区成员！

---

**开始你的 Mini-Boot 学习之旅吧！** 🚀

如果你是第一次使用，建议从 [01-hello-world](01-hello-world/) 开始。
