#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 连接处理器
"""

from typing import Any, Callable

from loguru import logger

from ..annotations import get_websocket_handlers
from ..exceptions import WebSocketConnectionException
from ..session import WebSocketSession


class WebSocketConnectionHandler:
    """WebSocket 连接处理器

    负责处理 WebSocket 连接的建立和断开事件.
    """

    def __init__(self):
        """初始化连接处理器"""
        self._connect_handlers: list[Callable] = []
        self._disconnect_handlers: list[Callable] = []
        self._error_handlers: list[Callable] = []

    def register_controller(self, controller_instance: Any) -> None:
        """注册 WebSocket 控制器

        Args:
            controller_instance: 控制器实例
        """
        handlers = get_websocket_handlers(controller_instance)

        # 注册连接处理器
        self._connect_handlers.extend(handlers["connect"])

        # 注册断开连接处理器
        self._disconnect_handlers.extend(handlers["disconnect"])

        # 注册错误处理器
        self._error_handlers.extend(handlers["error"])

        logger.debug(f"Registered connection handlers for controller: {controller_instance.__class__.__name__}")

    async def handle_connect(self, session: WebSocketSession) -> None:
        """处理连接建立事件

        Args:
            session: WebSocket 会话

        Raises:
            WebSocketConnectionException: 连接处理失败时抛出
        """
        try:
            logger.info(f"WebSocket connection established: {session.get_id()}")

            # 调用所有连接处理器
            for handler in self._connect_handlers:
                try:
                    await handler(session)
                except Exception as e:
                    logger.error(f"Error in connect handler {handler.__name__}: {e}")
                    # 连接处理器出错不应该中断连接建立过程
                    # 但可以记录错误并继续处理其他处理器

            logger.debug(f"Connection handlers completed for session: {session.get_id()}")

        except Exception as e:
            logger.error(f"Failed to handle connection for session {session.get_id()}: {e}")
            raise WebSocketConnectionException(f"Connection handling failed: {e}", cause=e)

    async def handle_disconnect(self, session: WebSocketSession) -> None:
        """处理连接断开事件

        Args:
            session: WebSocket 会话
        """
        try:
            logger.info(f"WebSocket connection disconnected: {session.get_id()}")

            # 调用所有断开连接处理器
            for handler in self._disconnect_handlers:
                try:
                    await handler(session)
                except Exception as e:
                    logger.error(f"Error in disconnect handler {handler.__name__}: {e}")
                    # 断开连接处理器出错不应该影响其他处理器的执行

            logger.debug(f"Disconnect handlers completed for session: {session.get_id()}")

        except Exception as e:
            logger.error(f"Failed to handle disconnection for session {session.get_id()}: {e}")
            # 断开连接处理失败不抛出异常,避免影响清理过程

    async def handle_error(self, session: WebSocketSession, error: Exception) -> None:
        """处理连接错误事件

        Args:
            session: WebSocket 会话
            error: 发生的错误
        """
        try:
            logger.error(f"WebSocket error in session {session.get_id()}: {error}")

            # 调用所有错误处理器
            for handler in self._error_handlers:
                try:
                    await handler(session, error)
                except Exception as e:
                    logger.error(f"Error in error handler {handler.__name__}: {e}")
                    # 错误处理器本身出错不应该影响其他处理器

            logger.debug(f"Error handlers completed for session: {session.get_id()}")

        except Exception as e:
            logger.error(f"Failed to handle error for session {session.get_id()}: {e}")
            # 错误处理失败不抛出异常,避免递归错误

    def get_handler_counts(self) -> dict:
        """获取处理器数量统计

        Returns:
            dict: 处理器类型和数量的映射
        """
        return {"connect": len(self._connect_handlers), "disconnect": len(self._disconnect_handlers), "error": len(self._error_handlers)}

    def clear_handlers(self) -> None:
        """清空所有处理器"""
        self._connect_handlers.clear()
        self._disconnect_handlers.clear()
        self._error_handlers.clear()

        logger.debug("Cleared all WebSocket connection handlers")


class ConnectionLifecycleManager:
    """连接生命周期管理器

    管理 WebSocket 连接的完整生命周期,包括连接前的准备、连接中的维护和连接后的清理.
    """

    def __init__(self):
        """初始化生命周期管理器"""
        self._pre_connect_hooks: list[Callable] = []
        self._post_connect_hooks: list[Callable] = []
        self._pre_disconnect_hooks: list[Callable] = []
        self._post_disconnect_hooks: list[Callable] = []

    def add_pre_connect_hook(self, hook: Callable) -> None:
        """添加连接前钩子

        Args:
            hook: 钩子函数
        """
        self._pre_connect_hooks.append(hook)

    def add_post_connect_hook(self, hook: Callable) -> None:
        """添加连接后钩子

        Args:
            hook: 钩子函数
        """
        self._post_connect_hooks.append(hook)

    def add_pre_disconnect_hook(self, hook: Callable) -> None:
        """添加断开连接前钩子

        Args:
            hook: 钩子函数
        """
        self._pre_disconnect_hooks.append(hook)

    def add_post_disconnect_hook(self, hook: Callable) -> None:
        """添加断开连接后钩子

        Args:
            hook: 钩子函数
        """
        self._post_disconnect_hooks.append(hook)

    async def execute_pre_connect_hooks(self, session: WebSocketSession) -> bool:
        """执行连接前钩子

        Args:
            session: WebSocket 会话

        Returns:
            bool: 是否允许继续连接
        """
        for hook in self._pre_connect_hooks:
            try:
                result = await hook(session)
                if result is False:
                    logger.info(f"Pre-connect hook rejected connection for session: {session.get_id()}")
                    return False
            except Exception as e:
                logger.error(f"Error in pre-connect hook: {e}")
                return False

        return True

    async def execute_post_connect_hooks(self, session: WebSocketSession) -> None:
        """执行连接后钩子

        Args:
            session: WebSocket 会话
        """
        for hook in self._post_connect_hooks:
            try:
                await hook(session)
            except Exception as e:
                logger.error(f"Error in post-connect hook: {e}")

    async def execute_pre_disconnect_hooks(self, session: WebSocketSession) -> None:
        """执行断开连接前钩子

        Args:
            session: WebSocket 会话
        """
        for hook in self._pre_disconnect_hooks:
            try:
                await hook(session)
            except Exception as e:
                logger.error(f"Error in pre-disconnect hook: {e}")

    async def execute_post_disconnect_hooks(self, session: WebSocketSession) -> None:
        """执行断开连接后钩子

        Args:
            session: WebSocket 会话
        """
        for hook in self._post_disconnect_hooks:
            try:
                await hook(session)
            except Exception as e:
                logger.error(f"Error in post-disconnect hook: {e}")


class ConnectionValidator:
    """连接验证器

    用于验证 WebSocket 连接的有效性.
    """

    def __init__(self):
        """初始化连接验证器"""
        self._validators: list[Callable] = []

    def add_validator(self, validator: Callable) -> None:
        """添加验证器

        Args:
            validator: 验证器函数,返回 True 表示验证通过
        """
        self._validators.append(validator)

    async def validate_connection(self, session: WebSocketSession) -> bool:
        """验证连接

        Args:
            session: WebSocket 会话

        Returns:
            bool: 验证是否通过
        """
        for validator in self._validators:
            try:
                if not await validator(session):
                    logger.info(f"Connection validation failed for session: {session.get_id()}")
                    return False
            except Exception as e:
                logger.error(f"Error in connection validator: {e}")
                return False

        return True

    def clear_validators(self) -> None:
        """清空所有验证器"""
        self._validators.clear()


class ConnectionMetrics:
    """连接指标收集器

    收集 WebSocket 连接的各种指标.
    """

    def __init__(self):
        """初始化指标收集器"""
        self._total_connections = 0
        self._active_connections = 0
        self._failed_connections = 0
        self._connection_durations = []

    def record_connection_attempt(self) -> None:
        """记录连接尝试"""
        self._total_connections += 1

    def record_connection_success(self) -> None:
        """记录连接成功"""
        self._active_connections += 1

    def record_connection_failure(self) -> None:
        """记录连接失败"""
        self._failed_connections += 1

    def record_disconnection(self, duration_seconds: float) -> None:
        """记录断开连接

        Args:
            duration_seconds: 连接持续时间(秒)
        """
        self._active_connections -= 1
        self._connection_durations.append(duration_seconds)

    def get_metrics(self) -> dict:
        """获取连接指标

        Returns:
            dict: 连接指标
        """
        avg_duration = sum(self._connection_durations) / len(self._connection_durations) if self._connection_durations else 0

        return {
            "total_connections": self._total_connections,
            "active_connections": self._active_connections,
            "failed_connections": self._failed_connections,
            "success_rate": (self._total_connections - self._failed_connections) / max(self._total_connections, 1),
            "average_duration_seconds": avg_duration,
            "total_disconnections": len(self._connection_durations),
        }

    def reset_metrics(self) -> None:
        """重置指标"""
        self._total_connections = 0
        self._active_connections = 0
        self._failed_connections = 0
        self._connection_durations.clear()
