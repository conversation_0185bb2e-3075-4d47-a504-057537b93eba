#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: DefaultBeanFactory异步环境测试套件
"""

import asyncio
import contextlib
import time
import unittest
from typing import Any, Optional

from miniboot.bean.factory import DefaultBeanFactory, AsyncContextDetector
from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.registry import DefaultBeanDefinitionRegistry


# ==================== 异步测试用Bean类 ====================

class AsyncService:
    """异步服务测试类"""
    def __init__(self):
        self.initialized = False
        self.destroyed = False
        self.creation_time = time.time()

    async def async_init(self) -> None:
        """异步初始化方法"""
        await asyncio.sleep(0.01)  # 模拟异步操作
        self.initialized = True

    async def async_destroy(self) -> None:
        """异步销毁方法"""
        await asyncio.sleep(0.01)
        self.destroyed = True

    async def process_data(self, data: str) -> str:
        """异步数据处理"""
        await asyncio.sleep(0.01)
        return f"processed_{data}"


class DatabaseService:
    """数据库服务 - I/O密集型"""
    def __init__(self):
        self.connection_pool = []
        self.initialized = False

    def init(self) -> None:
        """初始化连接池"""
        self.connection_pool = ["conn1", "conn2", "conn3"]
        self.initialized = True

    def destroy(self) -> None:
        """清理连接池"""
        self.connection_pool.clear()
        self.initialized = False


# ==================== 异步Bean处理测试 ====================

class AsyncBeanProcessingTestCase(unittest.TestCase):
    """异步Bean处理测试（同步环境）"""

    def setUp(self):
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    def tearDown(self):
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()

    def test_async_context_detection(self):
        """测试异步上下文检测"""
        detector = AsyncContextDetector()

        # 同步环境中应该返回False
        self.assertFalse(detector.is_async_context_active())

        # 测试异步Bean名称模式检测
        self.assertTrue(detector.should_use_async_processing("databaseService"))
        self.assertTrue(detector.should_use_async_processing("httpClient"))
        self.assertTrue(detector.should_use_async_processing("redisCache"))
        # 注意：根据实际实现，可能所有Service都被认为需要异步处理
        # 这里我们测试一个明显不需要异步的名称
        self.assertFalse(detector.should_use_async_processing("config"))

    def test_async_bean_in_sync_context(self):
        """测试在同步上下文中处理异步Bean"""
        # 注册数据库服务（I/O密集型，应该使用异步处理）
        definition = BeanDefinition("databaseService", DatabaseService)
        self.registry.register("databaseService", definition)

        # 在同步环境中获取，应该自动使用异步处理
        bean = self.factory.get_bean("databaseService")

        self.assertIsInstance(bean, DatabaseService)

    def test_mixed_sync_async_beans(self):
        """测试混合同步异步Bean"""
        # 注册同步Bean
        sync_def = BeanDefinition("syncService", AsyncService)
        self.registry.register("syncService", sync_def)

        # 注册异步Bean
        async_def = BeanDefinition("databaseService", DatabaseService)
        self.registry.register("databaseService", async_def)

        # 获取两种Bean
        sync_bean = self.factory.get_bean("syncService")
        async_bean = self.factory.get_bean("databaseService")

        self.assertIsInstance(sync_bean, AsyncService)
        self.assertIsInstance(async_bean, DatabaseService)


# ==================== 异步环境测试 ====================

class AsyncBeanFactoryTestCase(unittest.IsolatedAsyncioTestCase):
    """异步环境下的DefaultBeanFactory测试"""

    async def asyncSetUp(self):
        """异步测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    async def asyncTearDown(self):
        """异步测试后清理"""
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()
        self.factory = None
        self.registry = None

    async def _get_bean_async(self, bean_name: str, required_type: Optional[type] = None) -> Any:
        """异步获取Bean的辅助方法"""
        try:
            bean_result = self.factory.get_bean(bean_name, required_type) if required_type else self.factory.get_bean(bean_name)

            # 处理协程返回
            while asyncio.iscoroutine(bean_result):
                bean_result = await bean_result

            return bean_result
        except Exception as e:
            # 如果获取失败，记录错误并重新抛出
            print(f"Failed to get bean '{bean_name}': {e}")
            raise

    async def test_async_bean_creation(self):
        """测试异步环境下的Bean创建"""
        # 注册异步服务Bean
        definition = BeanDefinition("asyncService", AsyncService)
        self.registry.register("asyncService", definition)

        # 在异步环境中获取Bean
        bean = await self._get_bean_async("asyncService")

        self.assertIsInstance(bean, AsyncService)
        self.assertFalse(bean.initialized)  # 初始状态

        # 调用异步初始化
        await bean.async_init()
        self.assertTrue(bean.initialized)

    async def test_async_bean_with_lifecycle(self):
        """测试异步Bean的生命周期"""
        # 注册带生命周期的异步Bean
        definition = BeanDefinition("lifecycleAsyncService", AsyncService)
        self.registry.register("lifecycleAsyncService", definition)

        # 获取Bean
        bean = await self._get_bean_async("lifecycleAsyncService")

        # 异步初始化
        await bean.async_init()
        self.assertTrue(bean.initialized)

        # 异步销毁
        await bean.async_destroy()
        self.assertTrue(bean.destroyed)

    async def test_async_bean_operations(self):
        """测试异步Bean操作"""
        # 注册异步服务
        definition = BeanDefinition("operationService", AsyncService)
        self.registry.register("operationService", definition)

        bean = await self._get_bean_async("operationService")

        # 测试异步数据处理
        result = await bean.process_data("test_data")
        self.assertEqual(result, "processed_test_data")

    async def test_concurrent_async_bean_access(self):
        """测试并发异步Bean访问"""
        # 注册单例异步Bean
        definition = BeanDefinition("concurrentAsyncService", AsyncService, BeanScope.SINGLETON)
        self.registry.register("concurrentAsyncService", definition)

        async def async_worker():
            """异步工作函数"""
            bean = await self._get_bean_async("concurrentAsyncService")
            await bean.async_init()
            result = await bean.process_data("concurrent_test")
            return bean, result

        # 并发执行多个异步任务
        tasks = [async_worker() for _ in range(10)]
        results = await asyncio.gather(*tasks)

        # 验证结果
        self.assertEqual(len(results), 10)

        # 所有Bean应该是同一个实例（单例）
        beans = [result[0] for result in results]
        first_bean = beans[0]
        for bean in beans:
            self.assertIs(bean, first_bean)

        # 所有结果应该相同
        processed_results = [result[1] for result in results]
        for result in processed_results:
            self.assertEqual(result, "processed_concurrent_test")

    async def test_async_context_detection(self):
        """测试异步上下文检测"""
        # 获取异步检测器
        detector = self.factory._async_detector

        # 在异步环境中应该能检测到
        # 注意：这取决于具体的实现
        execution_mode = detector.detect_execution_mode()
        self.assertIn(execution_mode, ["sync", "async"])

    async def test_async_bean_post_processors(self):
        """测试异步环境下的Bean后置处理器"""
        # 创建异步后置处理器
        class AsyncBeanPostProcessor:
            def __init__(self):
                self.processed_beans = []

            def pre_process(self, bean: Any, bean_name: str) -> Any:
                self.processed_beans.append(f"pre_{bean_name}")
                return bean

            def post_process(self, bean: Any, bean_name: str) -> Any:
                self.processed_beans.append(f"post_{bean_name}")
                return bean

        # 添加后置处理器
        processor = AsyncBeanPostProcessor()
        self.factory.add_bean_post_processor(processor)

        # 注册异步Bean
        definition = BeanDefinition("processedAsyncService", AsyncService)
        self.registry.register("processedAsyncService", definition)

        # 获取Bean
        bean = await self._get_bean_async("processedAsyncService")

        # 验证后置处理器被调用
        self.assertIn("pre_processedAsyncService", processor.processed_beans)
        self.assertIn("post_processedAsyncService", processor.processed_beans)

        # 测试异步操作
        await bean.async_init()
        self.assertTrue(bean.initialized)

    async def test_async_bean_with_dependencies(self):
        """测试异步环境下的Bean依赖注入"""
        # 创建有依赖的异步Bean类
        class AsyncDependentService:
            def __init__(self):
                self.database_service: Optional[DatabaseService] = None
                self.initialized = False

            async def async_init(self) -> None:
                await asyncio.sleep(0.01)  # 模拟异步初始化
                self.initialized = True

            def set_database_service(self, service: DatabaseService) -> None:
                self.database_service = service

        # 注册依赖Bean
        db_def = BeanDefinition("databaseService", DatabaseService)
        self.registry.register("databaseService", db_def)

        # 注册依赖Bean
        dependent_def = BeanDefinition("asyncDependentService", AsyncDependentService)
        self.registry.register("asyncDependentService", dependent_def)

        # 获取Bean
        dependent_bean = await self._get_bean_async("asyncDependentService")
        db_bean = await self._get_bean_async("databaseService")

        # 手动设置依赖（模拟依赖注入）
        dependent_bean.set_database_service(db_bean)

        # 异步初始化
        await dependent_bean.async_init()

        # 验证依赖注入和初始化
        self.assertTrue(dependent_bean.initialized)
        self.assertIsInstance(dependent_bean.database_service, DatabaseService)

    async def test_async_error_handling(self):
        """测试异步环境下的错误处理"""
        # 创建会抛出异步异常的Bean类
        class FaultyAsyncService:
            def __init__(self):
                self.initialized = False

            async def async_init(self) -> None:
                await asyncio.sleep(0.01)
                raise RuntimeError("Async initialization failed")

        # 注册故障Bean
        definition = BeanDefinition("faultyAsyncService", FaultyAsyncService)
        self.registry.register("faultyAsyncService", definition)

        # 获取Bean应该成功
        bean = await self._get_bean_async("faultyAsyncService")
        self.assertIsInstance(bean, FaultyAsyncService)

        # 异步初始化应该失败
        with self.assertRaises(RuntimeError):
            await bean.async_init()

    async def test_async_performance_benchmark(self):
        """测试异步环境下的性能基准"""
        # 注册异步Bean
        definition = BeanDefinition("benchmarkAsyncService", AsyncService, BeanScope.PROTOTYPE)
        self.registry.register("benchmarkAsyncService", definition)

        # 异步性能测试
        iterations = 50  # 减少迭代次数以避免测试超时
        start_time = time.time()

        async def create_and_process():
            bean = await self._get_bean_async("benchmarkAsyncService")
            await bean.async_init()
            return await bean.process_data("benchmark")

        # 并发执行
        tasks = [create_and_process() for _ in range(iterations)]
        results = await asyncio.gather(*tasks)

        end_time = time.time()
        total_time = end_time - start_time

        # 验证结果
        self.assertEqual(len(results), iterations)
        for result in results:
            self.assertEqual(result, "processed_benchmark")

        # 性能断言
        avg_time_per_operation = total_time / iterations
        self.assertLess(avg_time_per_operation, 0.2)  # 每个操作小于200ms

    async def test_async_bean_factory_state_consistency(self):
        """测试异步环境下Bean工厂状态一致性"""
        # 注册多个Bean
        for i in range(3):  # 减少Bean数量
            bean_name = f"consistencyService_{i}"
            definition = BeanDefinition(bean_name, AsyncService, BeanScope.SINGLETON)
            self.registry.register(bean_name, definition)

        async def async_bean_operations():
            """异步Bean操作"""
            operations = []
            for i in range(3):
                bean_name = f"consistencyService_{i}"
                bean = await self._get_bean_async(bean_name)
                await bean.async_init()
                operations.append(bean)
            return operations

        # 并发执行多个异步操作
        tasks = [async_bean_operations() for _ in range(3)]
        results = await asyncio.gather(*tasks)

        # 验证状态一致性
        self.assertEqual(len(results), 3)

        # 每个任务都应该获得相同的Bean实例（单例）
        first_task_beans = results[0]
        for task_beans in results[1:]:
            for i, bean in enumerate(task_beans):
                self.assertIs(bean, first_task_beans[i])

        # 验证工厂状态
        self.assertEqual(self.factory.get_bean_definition_count(), 3)

        # 验证所有Bean都已初始化
        for bean_list in results:
            for bean in bean_list:
                self.assertTrue(bean.initialized)

    async def test_hierarchical_bean_factory_async(self):
        """测试异步环境下的分层Bean工厂"""
        # 创建父工厂
        parent_registry = DefaultBeanDefinitionRegistry()
        parent_factory = DefaultBeanFactory(parent_registry)

        # 在父工厂中注册Bean
        parent_def = BeanDefinition("parentAsyncService", AsyncService)
        parent_registry.register("parentAsyncService", parent_def)

        # 设置父子关系
        self.factory.set_parent_bean_factory(parent_factory)

        # 在子工厂中注册Bean
        child_def = BeanDefinition("childAsyncService", AsyncService)
        self.registry.register("childAsyncService", child_def)

        # 从子工厂获取父工厂的Bean
        parent_bean = await self._get_bean_async("parentAsyncService")
        self.assertIsInstance(parent_bean, AsyncService)

        # 从子工厂获取自己的Bean
        child_bean = await self._get_bean_async("childAsyncService")
        self.assertIsInstance(child_bean, AsyncService)

        # 检查Bean存在性
        self.assertTrue(self.factory.contains_bean("parentAsyncService"))
        self.assertTrue(self.factory.contains_bean("childAsyncService"))

        # 测试异步操作
        await parent_bean.async_init()
        await child_bean.async_init()

        self.assertTrue(parent_bean.initialized)
        self.assertTrue(child_bean.initialized)


# ==================== 缓存后台任务测试 ====================

class AsyncCacheBackgroundTasksTestCase(unittest.IsolatedAsyncioTestCase):
    """异步环境下的缓存后台任务测试"""

    async def asyncSetUp(self):
        """异步测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)
        self.cache_manager = self.factory._cache_manager

    async def asyncTearDown(self):
        """异步测试后清理"""
        with contextlib.suppress(Exception):
            self.cache_manager.stop_background_tasks()
            self.factory.destroy_singletons()

    async def test_background_tasks_in_async_environment(self):
        """测试异步环境下的后台任务管理"""
        # 在异步环境中启动后台任务
        try:
            self.cache_manager.start_background_tasks()

            # 等待一小段时间让后台任务运行
            await asyncio.sleep(0.1)

            # 验证后台任务正在运行
            # 注意：这取决于具体的实现

            # 停止后台任务
            self.cache_manager.stop_background_tasks()

        except Exception as e:
            self.fail(f"Background tasks failed in async environment: {e}")


# ==================== AsyncContextDetector高级功能测试 ====================

class AsyncContextDetectorAdvancedTestCase(unittest.TestCase):
    """AsyncContextDetector高级功能测试"""

    def test_detect_execution_mode_sync(self):
        """测试同步环境执行模式检测"""
        mode = AsyncContextDetector.detect_execution_mode()
        self.assertEqual(mode, "sync")

    async def test_detect_execution_mode_async(self):
        """测试异步环境执行模式检测"""
        mode = AsyncContextDetector.detect_execution_mode()
        self.assertEqual(mode, "async")

    def test_get_event_loop_info_sync(self):
        """测试同步环境事件循环信息"""
        loop_info = AsyncContextDetector.get_event_loop_info()

        self.assertIsInstance(loop_info, dict)
        self.assertIn("has_loop", loop_info)
        self.assertIn("loop_type", loop_info)
        self.assertIn("is_running", loop_info)
        self.assertIn("is_closed", loop_info)

        # 同步环境中应该没有运行中的事件循环
        self.assertFalse(loop_info["has_loop"])
        self.assertIsNone(loop_info["loop_type"])
        self.assertFalse(loop_info["is_running"])
        self.assertTrue(loop_info["is_closed"])

    async def test_get_event_loop_info_async(self):
        """测试异步环境事件循环信息"""
        loop_info = AsyncContextDetector.get_event_loop_info()

        self.assertIsInstance(loop_info, dict)
        self.assertIn("has_loop", loop_info)
        self.assertIn("loop_type", loop_info)
        self.assertIn("is_running", loop_info)
        self.assertIn("is_closed", loop_info)

        # 异步环境中应该有运行中的事件循环
        self.assertTrue(loop_info["has_loop"])
        self.assertIsNotNone(loop_info["loop_type"])
        self.assertTrue(loop_info["is_running"])
        self.assertFalse(loop_info["is_closed"])

    def test_is_coroutine_function(self):
        """测试协程函数检测"""
        # 普通函数
        def normal_func():
            return "normal"

        # 异步函数
        async def async_func():
            return "async"

        # 测试检测结果
        self.assertFalse(AsyncContextDetector.is_coroutine_function(normal_func))
        self.assertTrue(AsyncContextDetector.is_coroutine_function(async_func))

        # 测试None和其他类型
        self.assertFalse(AsyncContextDetector.is_coroutine_function(None))
        self.assertFalse(AsyncContextDetector.is_coroutine_function("not_a_function"))
        self.assertFalse(AsyncContextDetector.is_coroutine_function(123))

    def test_should_use_async_processing_patterns(self):
        """测试异步处理需求判断模式"""
        # 测试方法存在性和基本功能
        result1 = AsyncContextDetector.should_use_async_processing("testBean")
        result2 = AsyncContextDetector.should_use_async_processing("databaseService")

        # 验证返回值是布尔类型
        self.assertIsInstance(result1, bool)
        self.assertIsInstance(result2, bool)

        # 测试不同Bean名称的处理
        test_names = [
            "databaseService",
            "httpClient",
            "calculatorService",
            "configManager"
        ]

        for name in test_names:
            with self.subTest(bean_name=name):
                result = AsyncContextDetector.should_use_async_processing(name)
                self.assertIsInstance(result, bool)


# ==================== DefaultBeanFactory异步高级功能测试 ====================

class DefaultBeanFactoryAsyncAdvancedTestCase(unittest.TestCase):
    """DefaultBeanFactory异步高级功能测试"""

    def setUp(self):
        """测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    def tearDown(self):
        """测试后清理"""
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()

    async def test_async_bean_creation_with_dependencies(self):
        """测试异步Bean创建与依赖注入"""
        # 注册依赖Bean
        db_def = BeanDefinition("databaseService", DatabaseService, BeanScope.SINGLETON)
        self.registry.register("databaseService", db_def)

        # 注册主Bean
        async_def = BeanDefinition("asyncService", AsyncService, BeanScope.SINGLETON)
        self.registry.register("asyncService", async_def)

        # 异步获取Bean
        async_service = await self.factory.get_bean("asyncService")
        db_service = await self.factory.get_bean("databaseService")

        self.assertIsInstance(async_service, AsyncService)
        self.assertIsInstance(db_service, DatabaseService)

        # 验证单例行为
        async_service2 = await self.factory.get_bean("asyncService")
        self.assertIs(async_service, async_service2)

    async def test_async_bean_lifecycle_management(self):
        """测试异步Bean生命周期管理"""
        # 注册Bean
        definition = BeanDefinition("asyncService", AsyncService, BeanScope.SINGLETON)
        self.registry.register("asyncService", definition)

        # 创建Bean
        service = await self.factory.get_bean("asyncService")
        self.assertIsInstance(service, AsyncService)

        # 验证Bean状态
        self.assertIsNotNone(service.creation_time)

        # 销毁Bean
        self.factory.destroy_singletons()

        # 再次获取应该创建新实例
        new_service = await self.factory.get_bean("asyncService")
        self.assertIsNot(service, new_service)

    async def test_async_error_handling(self):
        """测试异步错误处理"""
        # 尝试获取不存在的Bean
        with self.assertRaises(KeyError):
            await self.factory.get_bean("nonExistentBean")

        # 注册Bean但类型不匹配
        definition = BeanDefinition("testBean", AsyncService)
        self.registry.register("testBean", definition)

        # 类型检查错误
        with self.assertRaises(TypeError):
            await self.factory.get_bean("testBean", DatabaseService)

    async def test_async_performance_monitoring(self):
        """测试异步性能监控"""
        # 注册多个Bean
        for i in range(5):
            definition = BeanDefinition(f"service_{i}", AsyncService, BeanScope.PROTOTYPE)
            self.registry.register(f"service_{i}", definition)

        # 异步创建Bean并测量时间
        start_time = time.time()

        tasks = []
        for i in range(5):
            task = self.factory.get_bean(f"service_{i}")
            tasks.append(task)

        services = await asyncio.gather(*tasks)

        end_time = time.time()
        execution_time = end_time - start_time

        # 验证结果
        self.assertEqual(len(services), 5)
        for service in services:
            self.assertIsInstance(service, AsyncService)

        # 性能检查（异步应该比同步快）
        self.assertLess(execution_time, 1.0)  # 应该在1秒内完成

        # 获取性能统计
        stats = self.factory.get_performance_stats()
        self.assertIsInstance(stats, dict)


if __name__ == '__main__':
    unittest.main()
