#!/usr/bin/env python
# encoding: utf-8
"""
单例模式工具模块

提供线程安全的单例元类和相关工具函数.

主要功能:
- SingletonMeta: 线程安全的单例元类
- 单例实例管理和重置功能
- 测试友好的单例重置机制

使用示例:
    class MyService(metaclass=SingletonMeta):
        def __init__(self):
            if not hasattr(self, '_initialized'):
                # 初始化逻辑
                self._data = {}
                self._initialized = True

    # 使用
    service1 = MyService()
    service2 = MyService()
    assert service1 is service2  # 同一个实例

    # 测试时重置
    SingletonMeta.reset_instance(MyService)
"""

import threading
from typing import Any, Optional


class SingletonMeta(type):
    """线程安全的单例元类

    特点:
    1. 线程安全的单例实现,使用双重检查锁定模式
    2. 支持重置实例,便于测试和调试
    3. 自动管理实例生命周期
    4. 支持清理资源的 cleanup 方法调用

    使用方式:
        class MyClass(metaclass=SingletonMeta):
            def __init__(self):
                if not hasattr(self, '_initialized'):
                    # 只在首次创建时初始化
                    self._data = {}
                    self._initialized = True

            def cleanup(self):
                # 可选的清理方法
                self._data.clear()
    """

    _instances: dict[type, Any] = {}
    _lock = threading.Lock()

    def __call__(cls: "SingletonMeta", *args: Any, **kwargs: Any) -> Any:
        """创建或获取单例实例

        使用双重检查锁定模式确保线程安全:
        1. 首次检查:避免不必要的锁获取
        2. 加锁:确保线程安全
        3. 二次检查:防止重复创建
        """
        if cls not in cls._instances:
            with cls._lock:
                # 双重检查锁定模式
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

    @classmethod
    def reset_instance(cls, target_cls: type) -> None:
        """重置指定类的单例实例

        主要用于测试场景,确保测试间的隔离.
        如果实例有 cleanup 方法,会在重置前调用.

        Args:
            target_cls: 要重置的单例类
        """
        with cls._lock:
            if target_cls in cls._instances:
                instance = cls._instances[target_cls]
                # 调用清理方法(如果存在)
                if hasattr(instance, "cleanup") and callable(instance.cleanup):
                    try:
                        instance.cleanup()
                    except Exception as e:
                        # 清理失败不应该影响重置过程
                        import logging

                        logging.warning(f"Failed to cleanup instance {target_cls.__name__}: {e}")
                del cls._instances[target_cls]

    @classmethod
    def reset_all_instances(cls) -> None:
        """重置所有单例实例

        清理所有已创建的单例实例,主要用于:
        1. 测试套件的全局清理
        2. 应用程序关闭时的资源清理
        3. 开发调试时的状态重置
        """
        with cls._lock:
            # 先调用所有实例的清理方法
            for target_cls, instance in list(cls._instances.items()):
                if hasattr(instance, "cleanup") and callable(instance.cleanup):
                    try:
                        instance.cleanup()
                    except Exception as e:
                        import logging

                        logging.warning(f"Failed to cleanup instance {target_cls.__name__}: {e}")

            # 清空实例字典
            cls._instances.clear()

    @classmethod
    def get_instance(cls, target_cls: type) -> Optional[Any]:
        """获取指定类的单例实例(如果存在)

        Args:
            target_cls: 单例类

        Returns:
            单例实例,如果不存在则返回 None
        """
        return cls._instances.get(target_cls)

    @classmethod
    def has_instance(cls, target_cls: type) -> bool:
        """检查指定类是否已有单例实例

        Args:
            target_cls: 单例类

        Returns:
            如果已有实例返回 True,否则返回 False
        """
        return target_cls in cls._instances

    @classmethod
    def get_all_instances(cls) -> dict[type, Any]:
        """获取所有单例实例的副本

        Returns:
            包含所有单例实例的字典副本
        """
        with cls._lock:
            return cls._instances.copy()

    @classmethod
    def get_instance_count(cls) -> int:
        """获取当前单例实例的数量

        Returns:
            单例实例数量
        """
        return len(cls._instances)


class SingletonFactory:
    """单例工厂类

    提供统一的单例创建和管理接口,便于集中管理所有单例实例.
    """

    @staticmethod
    def create_instance(cls: type, *args: Any, **kwargs: Any) -> Any:
        """创建单例实例

        Args:
            cls: 使用 SingletonMeta 的类
            *args: 构造函数参数
            **kwargs: 构造函数关键字参数

        Returns:
            单例实例
        """
        if not isinstance(cls, SingletonMeta):
            raise TypeError(f"Class {cls.__name__} must use SingletonMeta metaclass")

        return cls(*args, **kwargs)

    @staticmethod
    def reset_instance(cls: type) -> None:
        """重置单例实例"""
        SingletonMeta.reset_instance(cls)

    @staticmethod
    def reset_all() -> None:
        """重置所有单例实例"""
        SingletonMeta.reset_all_instances()

    @staticmethod
    def get_instance_info() -> dict[str, Any]:
        """获取单例实例信息

        Returns:
            包含实例统计信息的字典
        """
        instances = SingletonMeta.get_all_instances()
        return {
            "total_count": len(instances),
            "instance_types": [cls.__name__ for cls in instances],
            "memory_usage": sum(getattr(instance, "__sizeof__", lambda: 0)() for instance in instances.values()),
        }


def singleton_class(cls: type) -> type:
    """单例类装饰器

    为普通类添加单例行为的装饰器.

    Args:
        cls: 要转换为单例的类

    Returns:
        使用单例元类的新类

    Example:
        @singleton_class
        class MyService:
            def __init__(self):
                self.data = {}
    """
    # 创建一个新的类,使用 SingletonMeta 元类
    singleton_cls = type(cls.__name__, cls.__bases__, dict(cls.__dict__), metaclass=SingletonMeta)

    # 保持原始类的模块信息
    singleton_cls.__module__ = cls.__module__
    singleton_cls.__qualname__ = cls.__qualname__

    return singleton_cls
