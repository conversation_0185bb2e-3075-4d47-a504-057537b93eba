#!/usr/bin/env python
"""
* @author: cz
* @description: 启动横幅配置类
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional

from .constants import BannerConstants


class BannerMode(Enum):
    """横幅显示模式"""

    CONSOLE = "console"  # 控制台输出
    LOG = "log"  # 日志输出
    OFF = "off"  # 关闭横幅


@dataclass
class BannerConfig:
    """横幅配置类

    配置横幅的显示方式、内容和格式等参数
    """

    # 基本配置
    mode: BannerMode = BannerMode.CONSOLE
    """横幅显示模式"""

    enabled: bool = True
    """是否启用横幅显示"""

    # 内容配置
    location: Optional[str] = None
    """自定义横幅文件路径,如 'classpath:banner.txt'"""

    charset: str = BannerConstants.DEFAULT_CHARSET
    """横幅文件字符编码"""

    # 显示配置
    show_version: bool = True
    """是否显示版本信息"""

    show_environment: bool = True
    """是否显示环境信息"""

    show_startup_time: bool = True
    """是否显示启动时间"""

    # 格式配置
    colors: bool = True
    """是否使用彩色输出"""

    width: int = BannerConstants.DEFAULT_WIDTH
    """横幅宽度"""

    # 自定义属性
    properties: dict[str, Any] = field(default_factory=dict)
    """自定义属性,用于横幅模板变量替换"""

    def __post_init__(self):
        """初始化后处理"""
        if not self.enabled:
            self.mode = BannerMode.OFF

    @classmethod
    def from_env(cls, env) -> "BannerConfig":
        """从环境配置创建横幅配置

        Args:
            env: 环境配置对象

        Returns:
            BannerConfig: 横幅配置实例
        """
        config = cls()

        # 基本配置
        if env.contains_property("miniboot.banner.enabled"):
            enabled_value = env.get_property("miniboot.banner.enabled", "true")
            if isinstance(enabled_value, bool):
                config.enabled = enabled_value
            else:
                config.enabled = str(enabled_value).lower() in ("true", "1", "yes", "on")

        if env.contains_property("miniboot.banner.mode"):
            mode_str = env.get_property("miniboot.banner.mode", "console")
            try:
                config.mode = BannerMode(str(mode_str).lower())
            except ValueError:
                config.mode = BannerMode.CONSOLE

        # 内容配置
        if env.contains_property("miniboot.banner.location"):
            config.location = env.get_property("miniboot.banner.location")

        if env.contains_property("miniboot.banner.charset"):
            config.charset = env.get_property("miniboot.banner.charset", "UTF-8")

        # 显示配置
        if env.contains_property("miniboot.banner.show-version"):
            config.show_version = env.get_property("miniboot.banner.show-version", True)

        if env.contains_property("miniboot.banner.show-environment"):
            config.show_environment = env.get_property("miniboot.banner.show-environment", True)

        if env.contains_property("miniboot.banner.show-startup-time"):
            config.show_startup_time = env.get_property("miniboot.banner.show-startup-time", True)

        # 格式配置
        if env.contains_property("miniboot.banner.colors"):
            config.colors = env.get_property("miniboot.banner.colors", True)

        if env.contains_property("miniboot.banner.width"):
            config.width = env.get_property("miniboot.banner.width", 80)

        # 自定义属性
        banner_props = {}
        for source in env.get_property_sources():
            if hasattr(source, "properties"):
                for key, value in source.properties.items():
                    if key.startswith("miniboot.banner.properties."):
                        prop_key = key[len("miniboot.banner.properties.") :]
                        banner_props[prop_key] = value

        config.properties = banner_props

        return config

    def is_enabled(self) -> bool:
        """检查横幅是否启用"""
        return self.enabled and self.mode != BannerMode.OFF

    def console_enabled(self) -> bool:
        """检查是否应该输出到控制台"""
        return self.is_enabled() and self.mode == BannerMode.CONSOLE

    def log_enabled(self) -> bool:
        """检查是否应该输出到日志"""
        return self.is_enabled() and self.mode == BannerMode.LOG
