#!/usr/bin/env python
"""
异步任务指标收集器和性能监控器

收集和管理异步任务的执行指标,提供监控和管理功能.
包含可配置的性能监控器,支持高性能的异步执行器指标收集.
"""

import threading
import time
from contextlib import contextmanager
from dataclasses import dataclass
from typing import Any, Optional
from weakref import WeakSet

from loguru import logger

from .base import AsyncSingletonMeta


@dataclass
class AsyncTaskMetric:
    """异步任务指标"""

    task_id: str
    method_name: str
    executor_name: str
    start_time: float
    end_time: Optional[float] = None
    status: str = "running"  # running, completed, failed, timeout
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    thread_name: Optional[str] = None

    def complete(self, success: bool = True, error_message: Optional[str] = None) -> None:
        """标记任务完成

        Args:
            success: 是否成功
            error_message: 错误信息
        """
        self.end_time = time.time()
        self.execution_time = self.end_time - self.start_time
        self.status = "completed" if success else "failed"
        if error_message:
            self.error_message = error_message

    def timeout(self) -> None:
        """标记任务超时"""
        self.end_time = time.time()
        self.execution_time = self.end_time - self.start_time
        self.status = "timeout"

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "method_name": self.method_name,
            "executor_name": self.executor_name,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "status": self.status,
            "error_message": self.error_message,
            "execution_time": self.execution_time,
            "thread_name": self.thread_name,
        }


@dataclass
class AsyncExecutorMetrics:
    """异步执行器指标"""

    executor_name: str
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    timeout_tasks: int = 0
    running_tasks: int = 0
    average_execution_time: float = 0.0
    min_execution_time: float = float("inf")
    max_execution_time: float = 0.0
    last_task_time: Optional[float] = None

    def task_completed(self, execution_time: float, success: bool = True) -> None:
        """更新任务完成指标

        Args:
            execution_time: 执行时间
            success: 是否成功
        """
        if success:
            self.completed_tasks += 1
        else:
            self.failed_tasks += 1

        self.running_tasks = max(0, self.running_tasks - 1)
        self.last_task_time = time.time()

        # 更新执行时间统计
        if execution_time > 0:
            self.min_execution_time = min(self.min_execution_time, execution_time)
            self.max_execution_time = max(self.max_execution_time, execution_time)

            # 计算平均执行时间
            total_completed = self.completed_tasks + self.failed_tasks
            if total_completed > 0:
                self.average_execution_time = (self.average_execution_time * (total_completed - 1) + execution_time) / total_completed

    def task_timeout(self, execution_time: float) -> None:
        """更新任务超时指标

        Args:
            execution_time: 执行时间
        """
        self.timeout_tasks += 1
        self.running_tasks = max(0, self.running_tasks - 1)
        self.last_task_time = time.time()

        if execution_time > 0:
            self.max_execution_time = max(self.max_execution_time, execution_time)

    def start_task(self) -> None:
        """开始任务"""
        self.total_tasks += 1
        self.running_tasks += 1

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "executor_name": self.executor_name,
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "timeout_tasks": self.timeout_tasks,
            "running_tasks": self.running_tasks,
            "average_execution_time": self.average_execution_time,
            "min_execution_time": self.min_execution_time if self.min_execution_time != float("inf") else 0.0,
            "max_execution_time": self.max_execution_time,
            "last_task_time": self.last_task_time,
        }


class AsyncTaskMetricsCollector(metaclass=AsyncSingletonMeta):
    """异步任务指标收集器

    收集和管理所有异步任务的执行指标.
    使用单例模式确保全局唯一的指标收集器.
    """

    def __init__(self, max_task_history: int = 1000):
        """初始化指标收集器

        Args:
            max_task_history: 最大任务历史记录数
        """
        # 防止重复初始化(单例模式要求)
        if hasattr(self, '_metrics_collector_initialized'):
            return

        self._max_task_history = max_task_history
        self._lock = threading.RLock()

        # 任务指标
        self._task_metrics: dict[str, AsyncTaskMetric] = {}
        self._task_history: list[AsyncTaskMetric] = []

        # 执行器指标
        self._executor_metrics: dict[str, AsyncExecutorMetrics] = {}
        self._metrics_collector_initialized = True

        # 全局指标
        self._global_metrics = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "timeout_tasks": 0,
            "running_tasks": 0,
            "active_executors": 0,
        }

        self._task_id_counter = 0

    def start_task(self, method_name: str, executor_name: str, thread_name: Optional[str] = None) -> str:
        """开始任务跟踪

        Args:
            method_name: 方法名称
            executor_name: 执行器名称
            thread_name: 线程名称

        Returns:
            任务ID
        """
        with self._lock:
            self._task_id_counter += 1
            task_id = f"task-{self._task_id_counter}"

            # 创建任务指标
            task_metric = AsyncTaskMetric(
                task_id=task_id, method_name=method_name, executor_name=executor_name, start_time=time.time(), thread_name=thread_name
            )

            self._task_metrics[task_id] = task_metric

            # 更新执行器指标
            if executor_name not in self._executor_metrics:
                self._executor_metrics[executor_name] = AsyncExecutorMetrics(executor_name)

            self._executor_metrics[executor_name].start_task()

            # 更新全局指标
            self._global_metrics["total_tasks"] += 1
            self._global_metrics["running_tasks"] += 1
            self._global_metrics["active_executors"] = len(self._executor_metrics)

            logger.debug(f"Started tracking task: {task_id} ({method_name}) on {executor_name}")
            return task_id

    def complete_task(self, task_id: str, success: bool = True, error_message: Optional[str] = None) -> None:
        """完成任务跟踪

        Args:
            task_id: 任务ID
            success: 是否成功
            error_message: 错误信息
        """
        with self._lock:
            if task_id not in self._task_metrics:
                logger.warning(f"Task {task_id} not found in metrics")
                return

            task_metric = self._task_metrics[task_id]
            task_metric.complete(success, error_message)

            # 更新执行器指标
            executor_name = task_metric.executor_name
            if executor_name in self._executor_metrics:
                self._executor_metrics[executor_name].task_completed(task_metric.execution_time or 0.0, success)

            # 更新全局指标
            if success:
                self._global_metrics["completed_tasks"] += 1
            else:
                self._global_metrics["failed_tasks"] += 1

            self._global_metrics["running_tasks"] = max(0, self._global_metrics["running_tasks"] - 1)

            # 移动到历史记录
            self._move_to_history(task_id)

            logger.debug(f"Completed tracking task: {task_id} (success: {success})")

    def timeout_task(self, task_id: str) -> None:
        """任务超时

        Args:
            task_id: 任务ID
        """
        with self._lock:
            if task_id not in self._task_metrics:
                logger.warning(f"Task {task_id} not found in metrics")
                return

            task_metric = self._task_metrics[task_id]
            task_metric.timeout()

            # 更新执行器指标
            executor_name = task_metric.executor_name
            if executor_name in self._executor_metrics:
                self._executor_metrics[executor_name].task_timeout(task_metric.execution_time or 0.0)

            # 更新全局指标
            self._global_metrics["timeout_tasks"] += 1
            self._global_metrics["running_tasks"] = max(0, self._global_metrics["running_tasks"] - 1)

            # 移动到历史记录
            self._move_to_history(task_id)

            logger.debug(f"Task timeout: {task_id}")

    def _move_to_history(self, task_id: str) -> None:
        """移动任务到历史记录

        Args:
            task_id: 任务ID
        """
        if task_id in self._task_metrics:
            task_metric = self._task_metrics.pop(task_id)
            self._task_history.append(task_metric)

            # 限制历史记录大小
            if len(self._task_history) > self._max_task_history:
                self._task_history.pop(0)

    def running(self) -> list[AsyncTaskMetric]:
        """获取正在运行的任务

        Returns:
            正在运行的任务列表
        """
        with self._lock:
            return list(self._task_metrics.values())

    def history(self, limit: Optional[int] = None) -> list[AsyncTaskMetric]:
        """获取任务历史记录

        Args:
            limit: 限制返回数量

        Returns:
            任务历史记录列表
        """
        with self._lock:
            history = self._task_history.copy()
            if limit:
                history = history[-limit:]
            return history

    def executor_metrics(self, executor_name: Optional[str] = None) -> dict[str, AsyncExecutorMetrics]:
        """获取执行器指标

        Args:
            executor_name: 执行器名称,None表示获取所有

        Returns:
            执行器指标字典
        """
        with self._lock:
            if executor_name:
                return {executor_name: self._executor_metrics.get(executor_name)}
            return self._executor_metrics.copy()

    def global_metrics(self) -> dict:
        """获取全局指标

        Returns:
            全局指标字典
        """
        with self._lock:
            return self._global_metrics.copy()

    def reset(self) -> None:
        """重置所有指标"""
        with self._lock:
            self._task_metrics.clear()
            self._task_history.clear()
            self._executor_metrics.clear()
            self._global_metrics = {
                "total_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "timeout_tasks": 0,
                "running_tasks": 0,
                "active_executors": 0,
            }
            self._task_id_counter = 0
            logger.info("Reset all async task metrics")

    def summary(self) -> dict:
        """获取指标摘要

        Returns:
            指标摘要字典
        """
        with self._lock:
            summary = {
                "global_metrics": self.global_metrics(),
                "executor_metrics": {name: metrics.to_dict() for name, metrics in self._executor_metrics.items()},
                "running_tasks_count": len(self._task_metrics),
                "history_tasks_count": len(self._task_history),
            }
            return summary





# ============================================================================
# 性能监控器 (从 performance_monitor.py 合并)
# ============================================================================

@dataclass
class PerformanceConfig:
    """性能监控配置"""

    # 基础开关
    enabled: bool = False

    # 指标收集级别
    collect_execution_time: bool = True
    collect_task_count: bool = True
    collect_error_rate: bool = True
    collect_thread_info: bool = False
    collect_memory_info: bool = False

    # 采样配置
    sampling_enabled: bool = False
    sampling_rate: float = 0.1  # 10% 采样率

    # 缓存配置
    cache_metrics: bool = True
    cache_duration: float = 1.0  # 缓存1秒

    # 历史记录配置
    max_history_size: int = 100
    enable_detailed_history: bool = False

    # 性能优化
    batch_update: bool = True
    batch_size: int = 10
    async_update: bool = False


@dataclass
class LightweightMetrics:
    """轻量级指标数据"""

    # 基础计数器
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0

    # 时间统计(仅在启用时收集)
    total_execution_time: float = 0.0
    min_execution_time: float = float("inf")
    max_execution_time: float = 0.0

    # 缓存的计算结果
    _cached_average_time: Optional[float] = None
    _cached_success_rate: Optional[float] = None
    _cache_timestamp: float = 0.0

    def avg_time(self, cache_duration: float = 1.0) -> float:
        """获取平均执行时间(带缓存)"""
        now = time.time()
        if self._cached_average_time is not None and now - self._cache_timestamp < cache_duration:
            return self._cached_average_time

        if self.successful_executions > 0:
            self._cached_average_time = self.total_execution_time / self.successful_executions
        else:
            self._cached_average_time = 0.0

        self._cache_timestamp = now
        return self._cached_average_time

    def success_rate(self, cache_duration: float = 1.0) -> float:
        """获取成功率(带缓存)"""
        now = time.time()
        if self._cached_success_rate is not None and now - self._cache_timestamp < cache_duration:
            return self._cached_success_rate

        if self.total_executions > 0:
            self._cached_success_rate = self.successful_executions / self.total_executions
        else:
            self._cached_success_rate = 0.0

        self._cache_timestamp = now
        return self._cached_success_rate




class ConfigurablePerformanceMonitor(metaclass=AsyncSingletonMeta):
    """可配置性能监控器

    提供高性能、可配置的异步执行器指标收集功能.
    支持多种优化策略:
    1. 可配置的指标收集级别
    2. 采样收集减少开销
    3. 缓存计算结果
    4. 批量更新减少锁竞争
    """

    def __init__(self, config: Optional[PerformanceConfig] = None):
        """初始化性能监控器

        Args:
            config: 性能监控配置
        """
        self.config = config or PerformanceConfig()
        self._lock = threading.RLock() if self.config.enabled else None

        # 指标存储
        self._executor_metrics: dict[str, LightweightMetrics] = {}
        self._global_metrics = LightweightMetrics()

        # 采样控制
        self._sample_counter = 0

        # 批量更新
        self._pending_updates: list = []
        self._batch_lock = threading.Lock() if self.config.batch_update else None

        # 监控的执行器集合(弱引用)
        self._monitored_executors: WeakSet = WeakSet()

        # 统计信息
        self._stats = {
            "total_samples": 0,
            "skipped_samples": 0,
            "cache_hits": 0,
            "cache_misses": 0,
        }

    def is_enabled(self) -> bool:
        """检查监控是否启用"""
        return self.config.enabled

    def should_sample(self) -> bool:
        """判断是否应该收集此次样本"""
        if not self.config.enabled:
            return False

        if not self.config.sampling_enabled:
            return True

        # 采样逻辑
        self._sample_counter += 1
        should_sample = (self._sample_counter % int(1 / self.config.sampling_rate)) == 0

        if should_sample:
            self._stats["total_samples"] += 1
        else:
            self._stats["skipped_samples"] += 1

        return should_sample

    @contextmanager
    def measure(self, executor_name: str, operation_name: str = ""):
        """测量执行时间的上下文管理器

        Args:
            executor_name: 执行器名称
            operation_name: 操作名称(暂未使用,预留扩展)

        Yields:
            测量上下文
        """
        # 暂时不使用operation_name,但保留以备将来扩展
        _ = operation_name
        if not self.should_collect_sample():
            # 不收集样本时,提供空的上下文
            yield
            return

        start_time = time.time() if self.config.collect_execution_time else None
        success = True

        try:
            yield
        except Exception:
            success = False
            # 在异常情况下也要记录指标
            if self.config.enabled:
                execution_time = time.time() - start_time if start_time else 0.0
                self._record_execution(executor_name, execution_time, success)
            raise
        else:
            # 成功情况下记录指标
            if self.config.enabled:
                execution_time = time.time() - start_time if start_time else 0.0
                self._record_execution(executor_name, execution_time, success)

    def _record_execution(self, executor_name: str, execution_time: float, success: bool):
        """记录执行结果

        Args:
            executor_name: 执行器名称
            execution_time: 执行时间
            success: 是否成功
        """
        if not self.config.enabled:
            return

        if self.config.batch_update and self._batch_lock:
            # 批量更新模式
            with self._batch_lock:
                self._pending_updates.append((executor_name, execution_time, success))
                if len(self._pending_updates) >= self.config.batch_size:
                    self._flush_batch_updates()
        else:
            # 立即更新模式
            self._update_metrics(executor_name, execution_time, success)

    def _flush_batch_updates(self):
        """刷新批量更新"""
        if not self._pending_updates:
            return

        # 聚合更新
        aggregated = {}
        for executor_name, execution_time, success in self._pending_updates:
            if executor_name not in aggregated:
                aggregated[executor_name] = {
                    "total_executions": 0,
                    "successful_executions": 0,
                    "failed_executions": 0,
                    "total_execution_time": 0.0,
                    "min_execution_time": float("inf"),
                    "max_execution_time": 0.0,
                }

            agg = aggregated[executor_name]
            agg["total_executions"] += 1
            if success:
                agg["successful_executions"] += 1
            else:
                agg["failed_executions"] += 1

            if self.config.collect_execution_time and execution_time > 0:
                agg["total_execution_time"] += execution_time
                agg["min_execution_time"] = min(agg["min_execution_time"], execution_time)
                agg["max_execution_time"] = max(agg["max_execution_time"], execution_time)

        # 批量应用更新
        with self._lock:
            for executor_name, agg in aggregated.items():
                metrics = self._get_or_create_metrics(executor_name)

                metrics.total_executions += agg["total_executions"]
                metrics.successful_executions += agg["successful_executions"]
                metrics.failed_executions += agg["failed_executions"]

                if self.config.collect_execution_time:
                    metrics.total_execution_time += agg["total_execution_time"]
                    if agg["min_execution_time"] != float("inf"):
                        metrics.min_execution_time = min(metrics.min_execution_time, agg["min_execution_time"])
                    metrics.max_execution_time = max(metrics.max_execution_time, agg["max_execution_time"])

                # 清除缓存
                metrics._cached_average_time = None
                metrics._cached_success_rate = None

        self._pending_updates.clear()

    def _update_metrics(self, executor_name: str, execution_time: float, success: bool):
        """更新指标(立即模式)

        Args:
            executor_name: 执行器名称
            execution_time: 执行时间
            success: 是否成功
        """
        if not self.config.enabled or not self._lock:
            return

        with self._lock:
            metrics = self._get_or_create_metrics(executor_name)

            # 更新计数器
            if self.config.collect_task_count:
                metrics.total_executions += 1
                if success:
                    metrics.successful_executions += 1
                else:
                    metrics.failed_executions += 1

            # 更新执行时间
            if self.config.collect_execution_time and execution_time > 0:
                metrics.total_execution_time += execution_time
                metrics.min_execution_time = min(metrics.min_execution_time, execution_time)
                metrics.max_execution_time = max(metrics.max_execution_time, execution_time)

            # 清除缓存
            metrics._cached_average_time = None
            metrics._cached_success_rate = None

    def _get_or_create_metrics(self, executor_name: str) -> LightweightMetrics:
        """获取或创建指标对象

        Args:
            executor_name: 执行器名称

        Returns:
            指标对象
        """
        if executor_name not in self._executor_metrics:
            self._executor_metrics[executor_name] = LightweightMetrics()
        return self._executor_metrics[executor_name]

    def executor_metrics(self, executor_name: str) -> Optional[dict[str, Any]]:
        """获取执行器指标

        Args:
            executor_name: 执行器名称

        Returns:
            指标字典
        """
        if not self.config.enabled or not self._lock:
            return None

        with self._lock:
            metrics = self._executor_metrics.get(executor_name)
            if not metrics:
                return None

            result = {
                "executor_name": executor_name,
                "total_executions": metrics.total_executions,
                "successful_executions": metrics.successful_executions,
                "failed_executions": metrics.failed_executions,
            }

            if self.config.collect_execution_time:
                result.update(
                    {
                        "total_execution_time": metrics.total_execution_time,
                        "min_execution_time": metrics.min_execution_time if metrics.min_execution_time != float("inf") else 0.0,
                        "max_execution_time": metrics.max_execution_time,
                        "average_execution_time": metrics.avg_time(self.config.cache_duration),
                    }
                )

            if self.config.collect_error_rate:
                result["success_rate"] = metrics.success_rate(self.config.cache_duration)

            return result

    def all_metrics(self) -> dict[str, Any]:
        """获取所有指标

        Returns:
            所有指标字典
        """
        if not self.config.enabled:
            return {"enabled": False}

        # 刷新待处理的批量更新
        if self.config.batch_update and self._pending_updates:
            with self._batch_lock:
                self._flush_batch_updates()

        with self._lock:
            result = {
                "enabled": True,
                "config": {
                    "sampling_enabled": self.config.sampling_enabled,
                    "sampling_rate": self.config.sampling_rate,
                    "cache_metrics": self.config.cache_metrics,
                    "batch_update": self.config.batch_update,
                },
                "executors": {},
                "stats": self._stats.copy(),
            }

            for executor_name in self._executor_metrics:
                executor_metrics = self.executor_metrics(executor_name)
                if executor_metrics:
                    result["executors"][executor_name] = executor_metrics

            return result

    def clear(self):
        """清理所有指标"""
        if not self.config.enabled:
            return

        with self._lock:
            self._executor_metrics.clear()
            self._global_metrics = LightweightMetrics()
            self._stats = {
                "total_samples": 0,
                "skipped_samples": 0,
                "cache_hits": 0,
                "cache_misses": 0,
            }

        if self.config.batch_update:
            with self._batch_lock:
                self._pending_updates.clear()

    def config_update(self, new_config: PerformanceConfig):
        """更新配置

        Args:
            new_config: 新的配置
        """
        old_enabled = self.config.enabled
        self.config = new_config

        # 如果从禁用变为启用,初始化锁
        if not old_enabled and new_config.enabled:
            self._lock = threading.RLock()
            if new_config.batch_update:
                self._batch_lock = threading.Lock()

        # 如果从启用变为禁用,清理资源
        elif old_enabled and not new_config.enabled:
            self.clear()
            self._lock = None
            self._batch_lock = None

    # === 测试兼容性别名方法 ===
    def should_collect_sample(self) -> bool:
        """是否应该收集样本 (测试兼容性别名)"""
        if not self.config.enabled:
            return False
        # 如果没有启用采样，总是收集
        if not self.config.sampling_enabled:
            return True
        if self.config.sampling_rate >= 1.0:
            return True
        import random
        return random.random() < self.config.sampling_rate

    def measure_execution(self, executor_name: str, operation_name: str):
        """测量执行时间 (测试兼容性别名)"""
        return self.measure(executor_name, operation_name)
