#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 核心作用域 Bean 后置处理器

实现核心作用域 Bean 后置处理器,用于处理核心作用域的 Bean 创建和管理.
支持 singleton、prototype 等核心作用域.
Web相关作用域由Web模块的处理器处理.
"""

from typing import Any, Optional

from loguru import logger

from ..annotations.scope import get_scope_value, is_scoped
from ..bean.definition import BeanScope
from ..bean.scopes import CoreScopeRegistry
from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import BeanPostProcessor, ProcessorOrder


class CoreScopeBeanPostProcessor(BeanPostProcessor):
    """核心作用域 Bean 后置处理器

    负责处理核心作用域（SINGLETON、PROTOTYPE）的 Bean 创建和管理.
    """

    def __init__(self):
        """初始化核心作用域处理器"""
        self._scope_registry = CoreScopeRegistry()
        self._processed_beans: set[str] = set()

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """Bean初始化前处理

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        if bean_name in self._processed_beans:
            return bean

        try:
            # 检查 Bean 类是否有作用域注解
            bean_class = bean.__class__
            if not is_scoped(bean_class):
                return bean

            # 获取作用域信息
            scope_value = get_scope_value(bean_class)
            if not scope_value:
                return bean

            # 只处理核心作用域（SINGLETON、PROTOTYPE）
            if not BeanScope.is_core_scope(scope_value.value):
                return bean

            # 转换为BeanScope枚举
            try:
                bean_scope = BeanScope.from_string(scope_value.value)
            except ValueError:
                logger.warning(f"Invalid core scope '{scope_value.value}' for bean '{bean_name}'")
                return bean

            # 获取作用域管理器
            scope_manager = self._scope_registry.get_scope_manager(bean_scope)
            if not scope_manager:
                logger.warning(f"No scope manager found for scope '{bean_scope.value}' for bean '{bean_name}'")
                return bean

            # 对于PROTOTYPE作用域，不进行缓存处理
            if bean_scope == BeanScope.PROTOTYPE:
                logger.debug(f"Bean '{bean_name}' is prototype scope, no caching")
                return bean

            # 对于SINGLETON作用域，检查是否已存在
            scope_context = scope_manager.get_scope_context()
            if not scope_context:
                # 创建单例作用域上下文
                scope_context = scope_manager.create_scope_context("singleton")

            # 检查作用域中是否已存在该Bean
            existing_bean = scope_context.get_bean(bean_name)
            if existing_bean is not None:
                logger.debug(f"Found existing singleton bean '{bean_name}'")
                return existing_bean

            # 将Bean存储到作用域中
            scope_context.put_bean(bean_name, bean)
            self._processed_beans.add(bean_name)

            logger.debug(f"Stored bean '{bean_name}' in {bean_scope.value} scope")
            return bean

        except Exception as e:
            raise BeanProcessingError(f"Failed to process scope for bean '{bean_name}': {e}") from e

    def post_process_after_initialization(self, bean: Any, _bean_name: str) -> Any:
        """Bean初始化后处理(核心作用域处理器不需要后处理)

        Args:
            bean: Bean实例
            _bean_name: Bean名称(未使用)

        Returns:
            原始Bean实例
        """
        return bean

    def get_order(self) -> int:
        """获取处理器执行顺序

        Returns:
            执行顺序值，数值越小优先级越高
        """
        return ProcessorOrder.SCOPE.value

    def supports_bean(self, bean: Any, bean_name: str) -> bool:
        """检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            如果支持处理返回True，否则返回False
        """
        if bean is None:
            return False

        # 检查 Bean 类是否有核心作用域注解
        bean_class = bean.__class__
        if not is_scoped(bean_class):
            return False

        scope_value = get_scope_value(bean_class)
        if not scope_value:
            return False

        return BeanScope.is_core_scope(scope_value.value)

    def get_scoped_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定作用域获取 Bean

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值

        Returns:
            Bean 实例,如果不存在则返回 None
        """
        try:
            bean_scope = BeanScope.from_string(scope_value.value)
        except ValueError:
            return None

        scope_manager = self._scope_registry.get_scope_manager(bean_scope)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.get_bean(bean_name)

    def remove_scoped_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定作用域移除 Bean

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值

        Returns:
            被移除的 Bean 实例
        """
        try:
            bean_scope = BeanScope.from_string(scope_value.value)
        except ValueError:
            return None

        scope_manager = self._scope_registry.get_scope_manager(bean_scope)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.remove_bean(bean_name)


class CoreScopedBeanFactory:
    """核心作用域 Bean 工厂

    提供核心作用域 Bean 的创建和获取功能.
    """

    def __init__(self):
        """初始化核心作用域 Bean 工厂"""
        self._scope_registry = CoreScopeRegistry()

    def get_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定核心作用域获取Bean

        Args:
            bean_name: Bean名称
            scope_value: 作用域值

        Returns:
            Bean实例，如果不存在则返回None
        """
        try:
            bean_scope = BeanScope.from_string(scope_value.value)
        except ValueError:
            return None

        scope_manager = self._scope_registry.get_scope_manager(bean_scope)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.get_bean(bean_name)

    def put_bean(self, bean_name: str, bean: Any, scope_value) -> bool:
        """将Bean存储到指定核心作用域

        Args:
            bean_name: Bean名称
            bean: Bean实例
            scope_value: 作用域值

        Returns:
            如果成功存储返回True，否则返回False
        """
        try:
            bean_scope = BeanScope.from_string(scope_value.value)
        except ValueError:
            return False

        scope_manager = self._scope_registry.get_scope_manager(bean_scope)
        if not scope_manager:
            return False

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            scope_context = scope_manager.create_scope_context("default")

        scope_context.put_bean(bean_name, bean)
        return True

    def clear_scope(self, scope_value) -> None:
        """清空指定核心作用域的所有 Bean

        Args:
            scope_value: 作用域值
        """
        try:
            bean_scope = BeanScope.from_string(scope_value.value)
        except ValueError:
            return

        scope_manager = self._scope_registry.get_scope_manager(bean_scope)
        if not scope_manager:
            return

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return

        # 获取所有 Bean 名称并逐个移除
        bean_names = list(scope_context.get_bean_names())
        for bean_name in bean_names:
            scope_context.remove_bean(bean_name)

        logger.debug(f"Cleared all beans from {bean_scope.value} scope")
