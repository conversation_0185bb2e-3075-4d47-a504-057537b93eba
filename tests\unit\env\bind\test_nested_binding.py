#!/usr/bin/env python
"""
* @author: cz
* @description: 嵌套对象绑定测试
"""

import unittest
from dataclasses import dataclass

from miniboot.env.bind import Binder
from miniboot.env.resolver import PropertyResolver


@dataclass
class DatabaseConfig:
    """数据库配置"""

    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = ""
    database: str = "test"


@dataclass
class RedisConfig:
    """Redis配置"""

    host: str = "localhost"
    port: int = 6379
    password: str = ""
    timeout: int = 5000


@dataclass
class ServerConfig:
    """服务器配置"""

    name: str = "default-server"
    port: int = 8080
    database: DatabaseConfig = None
    redis: RedisConfig = None


@dataclass
class ApplicationConfig:
    """应用配置"""

    name: str = "test-app"
    version: str = "1.0.0"
    server: ServerConfig = None


class MockPropertyResolver(PropertyResolver):
    """模拟属性解析器"""

    def __init__(self, properties: dict):
        self._properties = properties

    def get_property(self, key: str, default=None):
        return self._properties.get(key, default)

    def contains_property(self, key: str) -> bool:
        return key in self._properties

    def get_property_as(self, key: str, target_type, default=None):
        value = self.get_property(key, default)
        if value is None:
            return default
        return target_type(value)

    def resolve_placeholders(self, text: str) -> str:
        return text

    def resolve_required_placeholders(self, text: str) -> str:
        return text


class NestedBindingTestCase(unittest.TestCase):
    """嵌套对象绑定测试"""

    def test_simple_nested_binding(self):
        """测试简单嵌套绑定"""
        properties = {"app.name": "my-app", "app.version": "2.0.0", "app.server.name": "web-server", "app.server.port": "9090"}

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("app", ApplicationConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        app = result.target
        self.assertEqual("my-app", app.name)
        self.assertEqual("2.0.0", app.version)
        self.assertIsNotNone(app.server)
        self.assertEqual("web-server", app.server.name)
        self.assertEqual(9090, app.server.port)

    def test_deep_nested_binding(self):
        """测试深度嵌套绑定"""
        properties = {
            "app.name": "my-app",
            "app.server.name": "web-server",
            "app.server.port": "8080",
            "app.server.database.host": "db.example.com",
            "app.server.database.port": "5432",
            "app.server.database.username": "admin",
            "app.server.database.password": "secret",
            "app.server.database.database": "myapp",
            "app.server.redis.host": "redis.example.com",
            "app.server.redis.port": "6380",
            "app.server.redis.password": "redis-secret",
            "app.server.redis.timeout": "10000",
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("app", ApplicationConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        app = result.target
        self.assertEqual("my-app", app.name)

        # 检查服务器配置
        self.assertIsNotNone(app.server)
        self.assertEqual("web-server", app.server.name)
        self.assertEqual(8080, app.server.port)

        # 检查数据库配置
        self.assertIsNotNone(app.server.database)
        self.assertEqual("db.example.com", app.server.database.host)
        self.assertEqual(5432, app.server.database.port)
        self.assertEqual("admin", app.server.database.username)
        self.assertEqual("secret", app.server.database.password)
        self.assertEqual("myapp", app.server.database.database)

        # 检查Redis配置
        self.assertIsNotNone(app.server.redis)
        self.assertEqual("redis.example.com", app.server.redis.host)
        self.assertEqual(6380, app.server.redis.port)
        self.assertEqual("redis-secret", app.server.redis.password)
        self.assertEqual(10000, app.server.redis.timeout)

    def test_partial_nested_binding(self):
        """测试部分嵌套绑定"""
        properties = {
            "app.name": "my-app",
            "app.server.name": "web-server",
            "app.server.database.host": "db.example.com",
            "app.server.database.port": "5432",
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("app", ApplicationConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        app = result.target
        self.assertEqual("my-app", app.name)

        # 服务器配置应该存在
        self.assertIsNotNone(app.server)
        self.assertEqual("web-server", app.server.name)
        self.assertEqual(8080, app.server.port)  # 默认值

        # 数据库配置应该存在
        self.assertIsNotNone(app.server.database)
        self.assertEqual("db.example.com", app.server.database.host)
        self.assertEqual(5432, app.server.database.port)
        self.assertEqual("root", app.server.database.username)  # 默认值

        # Redis配置应该为None(没有相关属性)
        self.assertIsNone(app.server.redis)

    def test_nested_binding_with_defaults(self):
        """测试使用默认值的嵌套绑定"""
        properties = {"app.name": "my-app"}

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("app", ApplicationConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        app = result.target
        self.assertEqual("my-app", app.name)
        self.assertEqual("1.0.0", app.version)  # 默认值

        # 嵌套对象应该为None(没有相关属性)
        self.assertIsNone(app.server)

    def test_nested_binding_type_conversion(self):
        """测试嵌套绑定中的类型转换"""
        properties = {
            "config.name": "test-server",  # 确保服务器配置被创建
            "config.port": "9090",  # 字符串转整数
            "config.database.host": "localhost",  # 确保数据库配置被创建
            "config.database.port": "5432",  # 字符串转整数
            "config.redis.host": "localhost",  # 确保Redis配置被创建
            "config.redis.timeout": "15000",  # 字符串转整数
        }

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("config", ServerConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        server = result.target
        self.assertEqual(9090, server.port)
        self.assertIsInstance(server.port, int)

        self.assertIsNotNone(server.database)
        self.assertEqual(5432, server.database.port)
        self.assertIsInstance(server.database.port, int)

        self.assertIsNotNone(server.redis)
        self.assertEqual(15000, server.redis.timeout)
        self.assertIsInstance(server.redis.timeout, int)

    def test_nested_binding_empty_properties(self):
        """测试空属性的嵌套绑定"""
        properties = {}

        resolver = MockPropertyResolver(properties)
        binder = Binder(resolver)

        result = binder.bind("app", ApplicationConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        app = result.target
        # 所有值应该使用默认值
        self.assertEqual("test-app", app.name)
        self.assertEqual("1.0.0", app.version)
        self.assertIsNone(app.server)


if __name__ == "__main__":
    unittest.main()
