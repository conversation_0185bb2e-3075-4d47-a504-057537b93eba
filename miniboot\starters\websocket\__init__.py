#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket Starter 模块 - 提供基于 FastAPI WebSocket 的实时通信功能
"""

# 注解系统
from .annotations import WebSocketController, WebSocketOnConnect, WebSocketOnDisconnect, WebSocketOnError, WebSocketOnMessage, WebSocketSendTo
from .compression import CompressionAlgorithm, CompressionResult, WebSocketMessageCompressor

# 自动配置
from .configuration import WebSocketAutoConfiguration

# 异常类
from .exceptions import (
    WebSocketAuthenticationException,
    WebSocketConnectionException,
    WebSocketC<PERSON>rollerException,
    WebSocketException,
    WebSocketMessageException,
    WebSocketSessionException,
)
from .health import HealthStatus, WebSocketHealthIndicator

# 监控和健康检查
from .metrics import WebSocketMetrics

# 性能优化组件
from .pool import ConnectionState, PoolConfiguration, PooledConnection, WebSocketConnectionPool

# 配置属性
from .properties import WebSocketProperties

# 核心服务
from .service import WebSocketService

# 会话管理
from .session import WebSocketSession, WebSocketSessionManager

__all__ = [
    # 核心服务
    "WebSocketService",
    # 会话管理
    "WebSocketSession",
    "WebSocketSessionManager",
    # 配置属性
    "WebSocketProperties",
    # 监控和健康检查
    "WebSocketMetrics",
    "WebSocketHealthIndicator",
    "HealthStatus",
    # 性能优化组件
    "WebSocketConnectionPool",
    "PoolConfiguration",
    "PooledConnection",
    "ConnectionState",
    "WebSocketMessageCompressor",
    "CompressionAlgorithm",
    "CompressionResult",
    # 注解系统
    "WebSocketController",
    "WebSocketOnConnect",
    "WebSocketOnDisconnect",
    "WebSocketOnMessage",
    "WebSocketOnError",
    "WebSocketSendTo",
    # 异常类
    "WebSocketException",
    "WebSocketConnectionException",
    "WebSocketAuthenticationException",
    "WebSocketMessageException",
    "WebSocketSessionException",
    "WebSocketControllerException",
    # 自动配置
    "WebSocketAutoConfiguration",
]

__version__ = "1.0.0"
__author__ = "Mini-Boot Team"
__description__ = "WebSocket Starter for Mini-Boot Framework"
