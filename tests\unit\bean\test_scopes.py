#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean作用域管理器单元测试 - 全面覆盖测试
"""

import threading
import time
import unittest

from miniboot.bean.scopes import (
    ScopeManager,
    BeanScopeContext,
    SingletonScopeManager,
    PrototypeScopeManager,
    BeanScopeRegistry
)
from miniboot.bean.definition import BeanScope


# ==================== 测试用Bean类 ====================

class TestService:
    """测试用的服务类"""
    def __init__(self, name: str = "test"):
        self.name = name
        self.initialized = False
        self.destroyed = False

    def init(self) -> None:
        self.initialized = True

    def destroy(self) -> None:
        self.destroyed = True


class DatabaseService:
    """测试用数据库服务"""
    def __init__(self):
        self.connected = False
        self.connection_count = 0

    def connect(self) -> None:
        self.connected = True
        self.connection_count += 1

    def disconnect(self) -> None:
        self.connected = False


# ==================== Bean作用域上下文测试 ====================

class BeanScopeContextTestCase(unittest.TestCase):
    """Bean作用域上下文测试"""

    def setUp(self):
        self.context = BeanScopeContext("test_scope", BeanScope.SINGLETON)

    def test_context_initialization(self):
        """测试上下文初始化"""
        self.assertEqual(self.context.scope_id, "test_scope")
        self.assertEqual(self.context.scope_type, BeanScope.SINGLETON)
        self.assertIsNotNone(self.context.creation_time)
        self.assertIsInstance(self.context.creation_time, float)

    def test_context_creation_time_auto_set(self):
        """测试创建时间自动设置"""
        start_time = time.time()
        context = BeanScopeContext("test", BeanScope.SINGLETON)
        end_time = time.time()

        self.assertGreaterEqual(context.creation_time, start_time)
        self.assertLessEqual(context.creation_time, end_time)

    def test_context_creation_time_manual_set(self):
        """测试手动设置创建时间"""
        custom_time = 1234567890.0
        context = BeanScopeContext("test", BeanScope.SINGLETON, custom_time)
        self.assertEqual(context.creation_time, custom_time)

    def test_bean_storage_and_retrieval(self):
        """测试Bean存储和获取"""
        service = TestService("test1")

        # 存储Bean
        self.context.put_bean("testService", service)

        # 获取Bean
        retrieved = self.context.get_bean("testService")
        self.assertIs(retrieved, service)

    def test_bean_not_found(self):
        """测试获取不存在的Bean"""
        result = self.context.get_bean("nonExistentBean")
        self.assertIsNone(result)

    def test_bean_with_destruction_callback(self):
        """测试带销毁回调的Bean"""
        service = TestService("test1")
        callback_called = []

        def destruction_callback():
            callback_called.append(True)
            service.destroy()

        # 存储Bean和销毁回调
        self.context.put_bean("testService", service, destruction_callback)

        # 移除Bean应该触发回调
        removed = self.context.remove_bean("testService")

        self.assertIs(removed, service)
        self.assertTrue(callback_called)
        self.assertTrue(service.destroyed)

    def test_bean_removal_without_callback(self):
        """测试移除没有回调的Bean"""
        service = TestService("test1")
        self.context.put_bean("testService", service)

        removed = self.context.remove_bean("testService")
        self.assertIs(removed, service)

        # 再次获取应该返回None
        self.assertIsNone(self.context.get_bean("testService"))

    def test_get_bean_names(self):
        """测试获取Bean名称列表"""
        service1 = TestService("test1")
        service2 = DatabaseService()

        self.context.put_bean("service1", service1)
        self.context.put_bean("service2", service2)

        names = self.context.get_bean_names()
        self.assertEqual(len(names), 2)
        self.assertIn("service1", names)
        self.assertIn("service2", names)

    def test_context_destroy(self):
        """测试上下文销毁"""
        service1 = TestService("test1")
        service2 = TestService("test2")

        callback1_called = []
        callback2_called = []

        def callback1():
            callback1_called.append(True)
            service1.destroy()

        def callback2():
            callback2_called.append(True)
            service2.destroy()

        # 存储多个Bean
        self.context.put_bean("service1", service1, callback1)
        self.context.put_bean("service2", service2, callback2)

        # 销毁上下文
        self.context.destroy()

        # 验证所有回调都被调用
        self.assertTrue(callback1_called)
        self.assertTrue(callback2_called)
        self.assertTrue(service1.destroyed)
        self.assertTrue(service2.destroyed)

        # 验证Bean被清空
        self.assertEqual(len(self.context.get_bean_names()), 0)

    def test_thread_safety(self):
        """测试线程安全性"""
        results = []
        errors = []

        def worker():
            try:
                for i in range(50):  # 减少迭代次数避免测试超时
                    service = TestService(f"test_{i}")
                    self.context.put_bean(f"service_{i}", service)
                    retrieved = self.context.get_bean(f"service_{i}")
                    results.append(retrieved is service)
            except Exception as e:
                errors.append(e)

        # 并发访问
        threads = [threading.Thread(target=worker) for _ in range(5)]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(errors), 0)
        self.assertTrue(all(results))


# ==================== 单例作用域管理器测试 ====================

class SingletonScopeManagerTestCase(unittest.TestCase):
    """单例作用域管理器测试"""

    def setUp(self):
        self.manager = SingletonScopeManager()

    def test_scope_type(self):
        """测试作用域类型"""
        self.assertEqual(self.manager.get_scope_type(), BeanScope.SINGLETON)

    def test_create_scope_context(self):
        """测试创建作用域上下文"""
        context = self.manager.create_scope_context("singleton_scope")

        self.assertIsNotNone(context)
        self.assertEqual(context.scope_id, "singleton_scope")
        self.assertEqual(context.scope_type, BeanScope.SINGLETON)

    def test_get_scope_context(self):
        """测试获取作用域上下文"""
        # 初始状态应该没有上下文
        context = self.manager.get_scope_context()
        self.assertIsNone(context)

        # 创建后应该能获取到
        created_context = self.manager.create_scope_context("test")
        retrieved_context = self.manager.get_scope_context()

        self.assertIs(retrieved_context, created_context)

    def test_singleton_context_reuse(self):
        """测试单例上下文重用"""
        context1 = self.manager.create_scope_context("scope1")
        context2 = self.manager.create_scope_context("scope2")

        # 单例管理器应该重用同一个上下文
        self.assertIs(context1, context2)

    def test_destroy_scope_context(self):
        """测试销毁作用域上下文"""
        # 创建上下文并添加Bean
        context = self.manager.create_scope_context("test")
        service = TestService("test")
        context.put_bean("testService", service)

        # 销毁上下文
        self.manager.destroy_scope_context("test")

        # 验证上下文被清空
        new_context = self.manager.get_scope_context()
        self.assertIsNone(new_context)

    def test_thread_safety(self):
        """测试线程安全性"""
        contexts = []
        errors = []

        def worker():
            try:
                context = self.manager.create_scope_context(f"scope_{threading.current_thread().ident}")
                contexts.append(context)
            except Exception as e:
                errors.append(e)

        # 并发创建上下文
        threads = [threading.Thread(target=worker) for _ in range(10)]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(errors), 0)
        self.assertGreater(len(contexts), 0)

        # 所有上下文应该是同一个实例（单例）
        first_context = contexts[0]
        for context in contexts:
            self.assertIs(context, first_context)


# ==================== 原型作用域管理器测试 ====================

class PrototypeScopeManagerTestCase(unittest.TestCase):
    """原型作用域管理器测试"""

    def setUp(self):
        self.manager = PrototypeScopeManager()

    def test_scope_type(self):
        """测试作用域类型"""
        self.assertEqual(self.manager.get_scope_type(), BeanScope.PROTOTYPE)

    def test_get_scope_context_always_none(self):
        """测试获取作用域上下文总是返回None"""
        context = self.manager.get_scope_context()
        self.assertIsNone(context)

        # 即使创建了上下文，get_scope_context仍然返回None
        self.manager.create_scope_context("test")
        context = self.manager.get_scope_context()
        self.assertIsNone(context)

    def test_create_scope_context(self):
        """测试创建作用域上下文"""
        context = self.manager.create_scope_context("prototype_scope")

        self.assertIsNotNone(context)
        self.assertEqual(context.scope_id, "prototype_scope")
        self.assertEqual(context.scope_type, BeanScope.PROTOTYPE)

    def test_create_multiple_contexts(self):
        """测试创建多个上下文"""
        context1 = self.manager.create_scope_context("scope1")
        context2 = self.manager.create_scope_context("scope2")

        # 原型管理器应该创建不同的上下文
        self.assertIsNot(context1, context2)
        self.assertEqual(context1.scope_id, "scope1")
        self.assertEqual(context2.scope_id, "scope2")

    def test_destroy_scope_context_no_op(self):
        """测试销毁作用域上下文是无操作"""
        # 原型作用域的销毁操作应该是无操作，不抛出异常
        try:
            self.manager.destroy_scope_context("any_scope")
        except Exception as e:
            self.fail(f"destroy_scope_context should not raise exception: {e}")


# ==================== Bean作用域注册表测试 ====================

class BeanScopeRegistryTestCase(unittest.TestCase):
    """Bean作用域注册表测试"""

    def setUp(self):
        # 重置单例状态
        from miniboot.utils.singleton import SingletonMeta
        SingletonMeta.reset_instance(BeanScopeRegistry)
        self.registry = BeanScopeRegistry()

    def tearDown(self):
        # 清理注册表
        self.registry.cleanup()

    def test_singleton_behavior(self):
        """测试单例行为"""
        registry1 = BeanScopeRegistry()
        registry2 = BeanScopeRegistry()

        self.assertIs(registry1, registry2)

    def test_default_managers_registration(self):
        """测试默认管理器注册"""
        # 获取管理器并验证类型
        singleton_manager = self.registry.get_scope_manager(BeanScope.SINGLETON)
        prototype_manager = self.registry.get_scope_manager(BeanScope.PROTOTYPE)

        self.assertIsNotNone(singleton_manager)
        self.assertIsNotNone(prototype_manager)
        self.assertIsInstance(singleton_manager, SingletonScopeManager)
        self.assertIsInstance(prototype_manager, PrototypeScopeManager)

    def test_register_custom_scope_manager(self):
        """测试注册自定义作用域管理器"""
        custom_manager = SingletonScopeManager()  # 使用现有类型作为示例
        custom_scope = BeanScope.PROTOTYPE  # 覆盖现有的PROTOTYPE管理器

        # 注册自定义管理器
        self.registry.register_scope_manager(custom_scope, custom_manager)

        # 验证注册成功
        retrieved_manager = self.registry.get_scope_manager(custom_scope)
        self.assertIs(retrieved_manager, custom_manager)

    def test_cleanup_all_managers(self):
        """测试清理所有管理器"""
        # 获取管理器并创建一些上下文
        singleton_manager = self.registry.get_scope_manager(BeanScope.SINGLETON)
        self.assertIsNotNone(singleton_manager)
        if singleton_manager:
            singleton_manager.create_scope_context("test_scope")

        # 清理注册表
        self.registry.cleanup()

        # 验证清理后状态 - 管理器仍然存在
        new_singleton_manager = self.registry.get_scope_manager(BeanScope.SINGLETON)
        self.assertIsNotNone(new_singleton_manager)

    def test_thread_safety(self):
        """测试线程安全性"""
        results = []
        errors = []

        def worker():
            try:
                # 并发获取注册表实例
                registry = BeanScopeRegistry()
                manager = registry.get_scope_manager(BeanScope.SINGLETON)
                results.append(manager is not None)
            except Exception as e:
                errors.append(e)

        # 并发访问
        threads = [threading.Thread(target=worker) for _ in range(10)]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(errors), 0)
        self.assertTrue(all(results))


class ScopeManagerTestCase(unittest.TestCase):
    """ScopeManager测试"""

    def setUp(self):
        """测试前准备"""
        self.scope_manager = ScopeManager()

    def tearDown(self):
        """测试后清理"""
        pass

    def test_scope_manager_initialization(self):
        """测试作用域管理器初始化"""
        manager = ScopeManager()

        # 检查初始状态为空
        self.assertEqual(len(manager.get_registered_scopes()), 0)


if __name__ == '__main__':
    unittest.main()
