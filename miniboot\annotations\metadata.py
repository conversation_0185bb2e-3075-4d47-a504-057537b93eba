#!/usr/bin/env python
"""
* @author: cz
* @description: 注解元数据类定义

定义各种注解的元数据结构,用于存储注解参数和配置信息.
这些元数据类将被注解装饰器使用,存储在被装饰的类或方法上.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Optional


class Scope(Enum):
    """核心Bean作用域枚举

    只包含框架核心的作用域类型，Web相关作用域在web模块中定义。
    """

    SINGLETON = "singleton"  # 单例模式
    PROTOTYPE = "prototype"  # 原型模式

    @classmethod
    def from_string(cls, value: str) -> "Scope":
        """从字符串创建 Scope

        Args:
            value: 作用域字符串值

        Returns:
            Scope: 对应的作用域枚举

        Raises:
            ValueError: 如果值无效
        """
        value = value.lower().strip()
        for scope in cls:
            if scope.value == value:
                return scope
        raise ValueError(f"Invalid scope: {value}. Valid values are: {[s.value for s in cls]}")


@dataclass
class ComponentMetadata:
    """组件注解元数据"""

    name: Optional[str] = None  # Bean名称,默认为类名首字母小写
    scope: Scope = Scope.SINGLETON  # Bean作用域,默认为单例
    lazy: bool = False  # 是否延迟初始化
    primary: bool = False  # 是否为主要Bean
    depends_on: list[str] = field(default_factory=list)  # 依赖的Bean名称列表


@dataclass
class BeanMetadata:
    """Bean方法注解元数据"""

    name: Optional[str] = None  # Bean名称,默认为方法名
    scope: Scope = Scope.SINGLETON  # Bean作用域
    init_method: Optional[str] = None  # 初始化方法名
    destroy_method: Optional[str] = None  # 销毁方法名
    depends_on: list[str] = field(default_factory=list)  # 依赖的Bean名称列表


@dataclass
class AutowiredMetadata:
    """自动装配注解元数据"""

    required: bool = True  # 是否必需,默认为True
    name: Optional[str] = None  # 指定Bean名称,默认根据类型推断
    qualifier: Optional[str] = None  # 限定符,用于区分同类型的多个Bean


@dataclass
class ValueMetadata:
    """配置值注解元数据"""

    value: str  # 配置键或表达式
    required: bool = True  # 是否必需
    default_value: Optional[Any] = None  # 默认值


@dataclass
class ConfigurationPropertiesMetadata:
    """配置属性注解元数据"""

    prefix: str = ""  # 配置前缀
    ignore_unknown_fields: bool = True  # 是否忽略未知字段
    ignore_invalid_fields: bool = False  # 是否忽略无效字段
    validate: bool = True  # 是否进行配置验证


@dataclass
class ConditionalMetadata:
    """条件装配注解元数据"""

    condition_type: str  # 条件类型:OnProperty, OnBean, OnMissingBean, OnClass, OnMissingClass, OnWeb, OnExpression, OnResource
    properties: list[str] = field(default_factory=list)  # 属性名列表
    having_value: Optional[str] = None  # 期望的属性值
    match_if_missing: bool = False  # 属性不存在时是否匹配
    bean_names: list[str] = field(default_factory=list)  # Bean名称列表
    class_names: list[str] = field(default_factory=list)  # 类名列表
    prefix: Optional[str] = None  # 属性前缀
    expression: Optional[str] = None  # 条件表达式
    java_range: Optional[str] = None  # Java版本范围
    resources: list[str] = field(default_factory=list)  # 资源路径列表


@dataclass
class ProfileMetadata:
    """Profile 注解元数据"""

    profiles: list[str] = field(default_factory=list)  # Profile 名称列表
    expression: Optional[str] = None  # Profile 表达式


@dataclass
class ScopeMetadata:
    """作用域注解元数据"""

    scope: Scope = Scope.SINGLETON  # 作用域类型
    proxy_mode: str = "default"  # 代理模式


@dataclass
class ComponentScanMetadata:
    """组件扫描注解元数据"""

    base_packages: list[str] = field(default_factory=list)  # 扫描的基础包路径
    include_filters: list[str] = field(default_factory=list)  # 包含过滤器
    exclude_filters: list[str] = field(default_factory=list)  # 排除过滤器
    lazy_init: bool = False  # 是否延迟初始化


@dataclass
class PostConstructMetadata:
    """初始化方法注解元数据"""

    method: str  # 方法名


@dataclass
class PreDestroyMetadata:
    """销毁方法注解元数据"""

    method: str  # 方法名


@dataclass
class AsyncMetadata:
    """异步方法注解元数据"""

    pool: Optional[str] = None  # 线程池名称
    method: str = ""  # 方法名


@dataclass
class ScheduledMetadata:
    """调度方法注解元数据"""

    cron: Optional[str] = None  # cron表达式
    fixed_rate: Optional[str] = None  # 固定频率
    fixed_delay: Optional[str] = None  # 固定延迟
    initial_delay: Optional[str] = None  # 初始延迟
    zone: Optional[str] = None  # 时区
    concurrent: bool = False  # 是否允许并发执行


@dataclass
class EventListenerMetadata:
    """事件监听器注解元数据"""

    method: str  # 方法名称
    event_type: Optional[type] = None  # 事件类型
    condition: Optional[str] = None  # 执行条件表达式
    order: int = 0  # 执行顺序
    async_exec: bool = False  # 是否异步执行


@dataclass
class QualifierMetadata:
    """限定符注解元数据"""

    value: str  # 限定符值


@dataclass
class PrimaryMetadata:
    """主要Bean注解元数据"""

    pass  # 标记性注解,无需额外属性


@dataclass
class LazyMetadata:
    """延迟初始化注解元数据"""

    value: bool = True  # 是否延迟初始化


@dataclass
class DependsOnMetadata:
    """依赖关系注解元数据"""

    bean_names: list[str] = field(default_factory=list)  # 依赖的Bean名称列表


@dataclass
class OrderMetadata:
    """顺序注解元数据"""

    value: int = 0  # 顺序值,数值越小优先级越高


@dataclass
class EnableAutoConfigurationMetadata:
    """自动配置注解元数据"""

    exclude: list[str] = field(default_factory=list)  # 排除的自动配置类


@dataclass
class ImportMetadata:
    """导入注解元数据"""

    import_classes: list[type] = field(default_factory=list)  # 要导入的配置类列表


# 元数据类型映射,用于快速查找
METADATA_TYPES = {
    "component": ComponentMetadata,
    "bean": BeanMetadata,
    "autowired": AutowiredMetadata,
    "value": ValueMetadata,
    "configuration_properties": ConfigurationPropertiesMetadata,
    "conditional": ConditionalMetadata,
    "component_scan": ComponentScanMetadata,
    "post_construct": PostConstructMetadata,
    "pre_destroy": PreDestroyMetadata,
    "async": AsyncMetadata,
    "scheduled": ScheduledMetadata,
    "event_listener": EventListenerMetadata,
    "qualifier": QualifierMetadata,
    "primary": PrimaryMetadata,
    "lazy": LazyMetadata,
    "depends_on": DependsOnMetadata,
    "order": OrderMetadata,
    "import": ImportMetadata,
    # 添加类名映射,用于序列化
    "ComponentMetadata": ComponentMetadata,
    "BeanMetadata": BeanMetadata,
    "AutowiredMetadata": AutowiredMetadata,
    "ValueMetadata": ValueMetadata,
    "ConfigurationPropertiesMetadata": ConfigurationPropertiesMetadata,
    "ConditionalMetadata": ConditionalMetadata,
    "ComponentScanMetadata": ComponentScanMetadata,
    "PostConstructMetadata": PostConstructMetadata,
    "PreDestroyMetadata": PreDestroyMetadata,
    "AsyncMetadata": AsyncMetadata,
    "ScheduledMetadata": ScheduledMetadata,
    "EventListenerMetadata": EventListenerMetadata,
    "QualifierMetadata": QualifierMetadata,
    "PrimaryMetadata": PrimaryMetadata,
    "LazyMetadata": LazyMetadata,
    "DependsOnMetadata": DependsOnMetadata,
    "OrderMetadata": OrderMetadata,
    "ImportMetadata": ImportMetadata,
}


def get_metadata_type(annotation_type: str) -> type:
    """获取注解类型对应的元数据类

    Args:
        annotation_type: 注解类型名称

    Returns:
        对应的元数据类

    Raises:
        KeyError: 如果注解类型不存在
    """
    if annotation_type not in METADATA_TYPES:
        raise KeyError(f"Unknown annotation type: {annotation_type}")
    return METADATA_TYPES[annotation_type]


def create_metadata(annotation_type: str, **kwargs: Any) -> Any:
    """创建注解元数据实例

    Args:
        annotation_type: 注解类型名称
        **kwargs: 元数据参数

    Returns:
        元数据实例
    """
    metadata_class = get_metadata_type(annotation_type)
    return metadata_class(**kwargs)
