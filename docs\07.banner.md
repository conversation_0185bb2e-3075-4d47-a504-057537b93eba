# Mini-Boot Banner 系统

## 概述

Mini-Boot 框架的 Banner 系统负责在应用启动时打印美观的启动信息，包括应用名称、版本、环境信息等。Banner 系统支持彩色控制台输出和日志文件记录，并提供了丰富的自定义选项。

## 设计思路

Banner 系统的设计借鉴了 Spring Boot 的启动 Banner，主要特点包括：

1. **可配置性**：支持通过配置文件启用/禁用 Banner，自定义 Banner 内容和位置
2. **模板渲染**：支持使用 Jinja2 模板语法在 Banner 中嵌入动态信息
3. **多样化输出**：同时支持彩色控制台输出和纯文本日志文件输出
4. **优先级加载**：按照优先级从多个位置查找 Banner 文件
5. **默认实现**：提供默认的 Banner 内容，确保即使没有自定义 Banner 也能正常显示

## 核心实现

### Banner 类

```python
# miniboot/banner.py
import os
import sys
import platform
import psutil
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from jinja2 import Template
from colorama import init, Fore, Style
from loguru import logger

# 初始化colorama支持Windows彩色输出
init(autoreset=True)

class Banner:
    """Banner系统实现"""

    def __init__(self, environment=None):
        """初始化Banner系统

        Args:
            environment: 环境配置对象
        """
        self.environment = environment
        self._banner_files = ['banner.txt', 'banner.gif', 'banner.jpg', 'banner.png']

    def print_banner(self) -> None:
        """打印Banner"""
        if not self._is_banner_enabled():
            return

        try:
            # 获取Banner内容
            banner_content = self._get_banner_content()

            # 准备模板数据
            template_data = self._prepare_template_data()

            # 渲染Banner模板
            rendered_banner = self._render_banner(banner_content, template_data)

            # 输出到控制台
            self._print_to_console(rendered_banner)

            # 输出到日志
            self._print_to_log(rendered_banner)

        except Exception as e:
            logger.error(f"Banner打印失败: {e}")

    def _is_banner_enabled(self) -> bool:
        """检查Banner是否启用"""
        if not self.environment:
            return False
        return self.environment.get_property_or_default('mini.banner.enabled', False)

    def _get_banner_content(self) -> str:
        """获取Banner内容"""
        # 1. 检查配置的Banner位置
        custom_location = self.environment.get_property('mini.banner.location') if self.environment else None
        if custom_location and os.path.exists(custom_location):
            return self._read_file(custom_location)

        # 2. 按优先级查找Banner文件
        for banner_file in self._banner_files:
            if os.path.exists(banner_file):
                return self._read_file(banner_file)

        # 3. 使用默认Banner
        return self._get_default_banner()

    def _read_file(self, file_path: str) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            logger.warning(f"读取Banner文件失败 {file_path}: {e}")
            return self._get_default_banner()

    def _get_default_banner(self) -> str:
        """获取默认Banner"""
        return """
  __  __ _       _       ____              _
 |  \/  (_)     (_)     |  _ \            | |
 | \  / |_ _ __  _ ______| |_) | ___   ___ | |_
 | |\/| | | '_ \| |______|  _ < / _ \ / _ \| __|
 | |  | | | | | | |      | |_) | (_) | (_) | |_
 |_|  |_|_|_| |_|_|      |____/ \___/ \___/ \__|

:: {{name}} :: (v{{version}})

应用信息:
  描述: {{description}}
  环境: {{profile}}
  启动时间: {{start_time}}

系统信息:
  Python版本: {{python_version}}
  系统: {{platform}}
  CPU核心: {{cpu_count}}
  内存: {{memory}}
"""

    def _prepare_template_data(self) -> Dict[str, Any]:
        """准备模板数据"""
        data = {
            'name': self._get_app_name(),
            'version': self._get_app_version(),
            'description': self._get_app_description(),
            'profile': self._get_active_profile(),
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'python_version': platform.python_version(),
            'platform': f"{platform.system()}/{platform.machine()}",
            'cpu_count': psutil.cpu_count(),
            'memory': self._format_memory(psutil.virtual_memory().total)
        }
        return data

    def _get_app_name(self) -> str:
        """获取应用名称"""
        if self.environment:
            return self.environment.get_property_or_default('mini.application.name', 'Mini-Boot应用')
        return 'Mini-Boot应用'

    def _get_app_version(self) -> str:
        """获取应用版本"""
        if self.environment:
            return self.environment.get_property_or_default('mini.application.version', '1.0.0')
        return '1.0.0'

    def _get_app_description(self) -> str:
        """获取应用描述"""
        if self.environment:
            return self.environment.get_property_or_default('mini.application.description', '基于Python的轻量级框架')
        return '基于Python的轻量级框架'

    def _get_active_profile(self) -> str:
        """获取当前环境配置"""
        if self.environment:
            profiles = self.environment.get_active_profiles()
            return ', '.join(profiles) if profiles else 'default'
        return 'default'

    def _format_memory(self, bytes_value: int) -> str:
        """格式化内存大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"

    def _render_banner(self, banner_content: str, template_data: Dict[str, Any]) -> str:
        """渲染Banner模板"""
        try:
            template = Template(banner_content)
            return template.render(**template_data)
        except Exception as e:
            logger.warning(f"Banner模板渲染失败: {e}")
            return banner_content

    def _print_to_console(self, banner_content: str) -> None:
        """打印到控制台"""
        lines = banner_content.strip().split('\n')

        for line in lines:
            colored_line = self._apply_color_style(line)
            print(colored_line)

    def _apply_color_style(self, line: str) -> str:
        """应用颜色样式"""
        line_lower = line.lower().strip()

        # Logo部分 - 亮绿色加粗
        if any(char in line for char in ['_', '|', '/', '\\']):
            return f"{Fore.LIGHTGREEN_EX}{Style.BRIGHT}{line}{Style.RESET_ALL}"

        # 应用名称和版本 - 亮紫色加粗
        elif '::' in line and '(' in line:
            return f"{Fore.LIGHTMAGENTA_EX}{Style.BRIGHT}{line}{Style.RESET_ALL}"

        # 功能简介 - 亮青色
        elif line_lower.startswith(('应用信息', '系统信息')):
            return f"{Fore.LIGHTCYAN_EX}{Style.BRIGHT}{line}{Style.RESET_ALL}"

        # 服务地址 - 亮绿色带下划线
        elif 'http://' in line_lower or 'https://' in line_lower:
            return f"{Fore.LIGHTGREEN_EX}{Style.BRIGHT}{line}{Style.RESET_ALL}"

        # 信息内容 - 亮白色
        elif ':' in line and any(keyword in line_lower for keyword in ['描述', '环境', '启动时间', 'python版本', '系统', 'cpu', '内存']):
            return f"{Fore.LIGHTWHITE_EX}{line}{Style.RESET_ALL}"

        # 其他内容 - 默认颜色
        else:
            return line

    def _print_to_log(self, banner_content: str) -> None:
        """打印到日志文件"""
        # 移除ANSI颜色代码，记录纯文本到日志
        clean_content = self._remove_ansi_codes(banner_content)
        logger.info(f"应用启动Banner:\n{clean_content}")

    def _remove_ansi_codes(self, text: str) -> str:
        """移除ANSI颜色代码"""
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        return ansi_escape.sub('', text)

# 便捷函数
def print_banner(environment=None):
    """打印Banner的便捷函数"""
    banner = Banner(environment)
    banner.print_banner()
```

## 配置选项

Banner 系统支持以下配置选项：

| 配置项                       | 类型   | 默认值                   | 说明                   |
| ---------------------------- | ------ | ------------------------ | ---------------------- |
| mini.banner.enabled          | bool   | false                    | 是否启用 Banner        |
| mini.banner.location         | string | -                        | 自定义 Banner 文件位置 |
| mini.application.name        | string | Mini-Boot 应用           | 应用名称               |
| mini.application.version     | string | 1.0.0                    | 应用版本               |
| mini.application.description | string | 基于 Python 的轻量级框架 | 应用描述               |

## 模板数据

Banner 模板支持以下变量：

| 变量名             | 说明          |
| ------------------ | ------------- |
| {{name}}           | 应用名称      |
| {{version}}        | 应用版本      |
| {{description}}    | 应用描述      |
| {{profile}}        | 当前环境配置  |
| {{python_version}} | Python 版本   |
| {{platform}}       | 操作系统/架构 |
| {{cpu_count}}      | CPU 核心数    |
| {{memory}}         | 系统内存      |
| {{start_time}}     | 启动时间      |

## 自定义 Banner

### 方法一：创建 Banner 文件

在项目根目录创建以下文件之一（按优先级排序）：

1. banner.txt
2. banner.gif (暂不支持图片)
3. banner.jpg (暂不支持图片)
4. banner.png (暂不支持图片)

### 方法二：指定 Banner 位置

在配置文件中设置 Banner 文件位置：

```yaml
mini:
    banner:
        enabled: true
        location: "path/to/my-banner.txt"
```

### Banner 文件示例

```
  __  __ _       _       ____              _
 |  \/  (_)     (_)     |  _ \            | |
 | \  / |_ _ __  _ ______| |_) | ___   ___ | |_
 | |\/| | | '_ \| |______|  _ < / _ \ / _ \| __|
 | |  | | | | | | |      | |_) | (_) | (_) | |_
 |_|  |_|_|_| |_|_|      |____/ \___/ \___/ \__|

:: {{name}} :: (v{{version}})

应用信息:
  描述: {{description}}
  环境: {{profile}}
  启动时间: {{start_time}}

系统信息:
  Python版本: {{python_version}}
  系统: {{platform}}
  CPU核心: {{cpu_count}}
  内存: {{memory}}
```

## 使用示例

### 基本使用

```python
# main.py - 应用入口
from miniboot.env import StandardEnvironment
from miniboot.banner import print_banner
from miniboot.logger import configure_logger

def main():
    # 创建环境配置
    env = StandardEnvironment()

    # 配置日志系统
    configure_logger(env)

    # 打印启动Banner
    print_banner(env)

    # 启动应用
    app.run()

if __name__ == "__main__":
    main()
```

### 启用 Banner

在配置文件中启用 Banner：

```yaml
mini:
    banner:
        enabled: true
```

### 完整配置示例

```yaml
mini:
    application:
        name: "我的Mini-Boot应用"
        version: "2.1.0"
        description: "基于Python的微服务应用"
    banner:
        enabled: true
        location: "custom/banner.txt"

logging:
    level: INFO
    file:
        path: "logs/app.log"
```

### 多环境配置

```yaml
# application.yml (默认配置)
mini:
    banner:
        enabled: true
    application:
        name: "Mini-Boot应用"

---
# application-dev.yml (开发环境)
mini:
    banner:
        enabled: true
    application:
        name: "Mini-Boot应用 [开发环境]"

---
# application-prod.yml (生产环境)
mini:
    banner:
        enabled: false # 生产环境可以禁用Banner
    application:
        name: "Mini-Boot应用 [生产环境]"
```

## 与其他模块集成

### 与环境模块集成

Banner 系统与环境配置模块深度集成：

```python
from miniboot.env import StandardEnvironment
from miniboot.banner import Banner

# 创建环境配置
env = StandardEnvironment()

# Banner自动从环境配置获取应用信息
banner = Banner(env)
banner.print_banner()
```

### 与日志模块集成

Banner 输出会同时记录到日志文件：

```python
from miniboot.logger import configure_logger
from miniboot.banner import print_banner

# 先配置日志
configure_logger(env)

# 再打印Banner（会自动记录到日志）
print_banner(env)
```

## 实现特点

1. **彩色输出**：使用 `colorama` 库实现跨平台彩色控制台输出
2. **双重输出**：同时支持控制台和日志文件输出
3. **模板支持**：使用 `Jinja2` 模板引擎实现模板渲染
4. **优先级加载**：按优先级从多个位置查找 Banner 文件
5. **智能着色**：根据内容类型自动应用不同颜色样式
6. **系统信息**：自动获取 Python 版本、系统信息、CPU 和内存信息

## 依赖库

Banner 系统需要以下 Python 库：

```python
# requirements.txt
jinja2>=3.0.0      # 模板引擎
colorama>=0.4.0    # 跨平台彩色输出
psutil>=5.8.0      # 系统信息获取
loguru>=0.6.0      # 日志记录
```

## 注意事项

1. **默认禁用**：默认情况下 Banner 功能是禁用的，需要在配置中显式启用
2. **图片支持**：虽然支持图片类型的 Banner 文件名，但目前只支持文本内容
3. **编码支持**：Banner 文件需要使用 UTF-8 编码
4. **性能考虑**：Banner 只在应用启动时打印一次，不会影响运行时性能
5. **生产环境**：建议在生产环境中禁用 Banner 以减少日志输出

## 与 Go 版本对比

| 特性       | Go 版本       | Python 版本 |
| ---------- | ------------- | ----------- |
| 模板引擎   | text/template | Jinja2      |
| 彩色输出   | fatih/color   | colorama    |
| 系统信息   | runtime 包    | psutil      |
| 配置集成   | Environment   | Environment |
| 日志集成   | logrus        | loguru      |
| 文件优先级 | 支持          | 支持        |
| 自定义位置 | 支持          | 支持        |

---

_本文档定义了 Mini-Boot 框架的 Banner 系统设计，提供美观的应用启动信息展示。_
