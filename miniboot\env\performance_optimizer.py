#!/usr/bin/env python3
"""
配置加载性能优化器
提供配置加载的性能优化功能，包括缓存、并行加载、预加载等
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional, Set, Any
from pathlib import Path
import threading
from dataclasses import dataclass

from ..log.logger import get_logger

logger = get_logger(__name__)


@dataclass
class LoadingMetrics:
    """加载性能指标"""
    total_time: float = 0.0
    file_count: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    parallel_tasks: int = 0
    
    def hit_rate(self) -> float:
        """缓存命中率"""
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0


class ConfigurationCache:
    """配置文件缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: float = 300.0):
        self.max_size = max_size
        self.ttl = ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._timestamps: Dict[str, float] = {}
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Dict[str, Any]]:
        """获取缓存项"""
        with self._lock:
            if key not in self._cache:
                return None
            
            # 检查是否过期
            if time.time() - self._timestamps[key] > self.ttl:
                self._remove(key)
                return None
            
            return self._cache[key].copy()
    
    def put(self, key: str, value: Dict[str, Any]) -> None:
        """存储缓存项"""
        with self._lock:
            # 检查缓存大小
            if len(self._cache) >= self.max_size:
                self._evict_oldest()
            
            self._cache[key] = value.copy()
            self._timestamps[key] = time.time()
    
    def _remove(self, key: str) -> None:
        """移除缓存项"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
    
    def _evict_oldest(self) -> None:
        """淘汰最旧的缓存项"""
        if not self._timestamps:
            return
        
        oldest_key = min(self._timestamps.keys(), key=lambda k: self._timestamps[k])
        self._remove(oldest_key)
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)


class PerformanceOptimizer:
    """配置加载性能优化器"""
    
    def __init__(self, enable_cache: bool = True, enable_parallel: bool = True, max_workers: int = 4):
        self.enable_cache = enable_cache
        self.enable_parallel = enable_parallel
        self.max_workers = max_workers
        
        self.cache = ConfigurationCache() if enable_cache else None
        self.metrics = LoadingMetrics()
        
        # 线程池用于并行文件操作
        self._executor = ThreadPoolExecutor(max_workers=max_workers) if enable_parallel else None
    
    def optimize_file_discovery(self, search_paths: List[str], patterns: List[str]) -> List[Path]:
        """优化文件发现过程"""
        start_time = time.perf_counter()
        
        if self.enable_parallel and self._executor:
            # 并行搜索文件
            files = self._parallel_file_discovery(search_paths, patterns)
        else:
            # 串行搜索文件
            files = self._serial_file_discovery(search_paths, patterns)
        
        self.metrics.total_time += time.perf_counter() - start_time
        self.metrics.file_count += len(files)
        
        return files
    
    def _parallel_file_discovery(self, search_paths: List[str], patterns: List[str]) -> List[Path]:
        """并行文件发现"""
        futures = []
        
        for search_path in search_paths:
            future = self._executor.submit(self._search_path_for_files, search_path, patterns)
            futures.append(future)
        
        self.metrics.parallel_tasks += len(futures)
        
        # 收集结果
        all_files = []
        for future in futures:
            try:
                files = future.result(timeout=5.0)  # 5秒超时
                all_files.extend(files)
            except Exception as e:
                logger.warning(f"Failed to search files in parallel: {e}")
        
        return all_files
    
    def _serial_file_discovery(self, search_paths: List[str], patterns: List[str]) -> List[Path]:
        """串行文件发现"""
        all_files = []
        
        for search_path in search_paths:
            try:
                files = self._search_path_for_files(search_path, patterns)
                all_files.extend(files)
            except Exception as e:
                logger.warning(f"Failed to search files in {search_path}: {e}")
        
        return all_files
    
    def _search_path_for_files(self, search_path: str, patterns: List[str]) -> List[Path]:
        """在指定路径搜索文件"""
        files = []
        path = Path(search_path)
        
        if not path.exists() or not path.is_dir():
            return files
        
        try:
            for pattern in patterns:
                # 使用glob模式搜索
                matched_files = list(path.glob(pattern))
                files.extend(matched_files)
        except Exception as e:
            logger.debug(f"Error searching files in {search_path}: {e}")
        
        return files
    
    def optimize_file_loading(self, file_paths: List[Path]) -> Dict[str, Any]:
        """优化文件加载过程"""
        start_time = time.perf_counter()
        
        if self.enable_parallel and self._executor and len(file_paths) > 1:
            # 并行加载文件
            results = self._parallel_file_loading(file_paths)
        else:
            # 串行加载文件
            results = self._serial_file_loading(file_paths)
        
        self.metrics.total_time += time.perf_counter() - start_time
        
        return results
    
    def _parallel_file_loading(self, file_paths: List[Path]) -> Dict[str, Any]:
        """并行文件加载"""
        futures = {}
        
        for file_path in file_paths:
            future = self._executor.submit(self._load_single_file, file_path)
            futures[str(file_path)] = future
        
        self.metrics.parallel_tasks += len(futures)
        
        # 收集结果
        results = {}
        for file_path_str, future in futures.items():
            try:
                content = future.result(timeout=10.0)  # 10秒超时
                if content is not None:
                    results[file_path_str] = content
            except Exception as e:
                logger.warning(f"Failed to load file {file_path_str}: {e}")
        
        return results
    
    def _serial_file_loading(self, file_paths: List[Path]) -> Dict[str, Any]:
        """串行文件加载"""
        results = {}
        
        for file_path in file_paths:
            try:
                content = self._load_single_file(file_path)
                if content is not None:
                    results[str(file_path)] = content
            except Exception as e:
                logger.warning(f"Failed to load file {file_path}: {e}")
        
        return results
    
    def _load_single_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载单个文件"""
        if not file_path.exists() or not file_path.is_file():
            return None
        
        # 检查缓存
        cache_key = f"{file_path}:{file_path.stat().st_mtime}"
        
        if self.cache:
            cached_content = self.cache.get(cache_key)
            if cached_content is not None:
                self.metrics.cache_hits += 1
                return cached_content
            
            self.metrics.cache_misses += 1
        
        # 加载文件内容
        try:
            content = self._read_file_content(file_path)
            
            # 存储到缓存
            if self.cache and content:
                self.cache.put(cache_key, content)
            
            return content
            
        except Exception as e:
            logger.debug(f"Error loading file {file_path}: {e}")
            return None
    
    def _read_file_content(self, file_path: Path) -> Dict[str, Any]:
        """读取文件内容"""
        # 这里应该根据文件类型选择合适的解析器
        # 为了简化，这里返回基本的文件信息
        return {
            "path": str(file_path),
            "size": file_path.stat().st_size,
            "mtime": file_path.stat().st_mtime,
            "exists": True
        }
    
    def get_metrics(self) -> LoadingMetrics:
        """获取性能指标"""
        return self.metrics
    
    def reset_metrics(self) -> None:
        """重置性能指标"""
        self.metrics = LoadingMetrics()
    
    def shutdown(self) -> None:
        """关闭优化器"""
        if self._executor:
            self._executor.shutdown(wait=True)
        
        if self.cache:
            self.cache.clear()


# 全局优化器实例
_global_optimizer: Optional[PerformanceOptimizer] = None


def get_global_optimizer() -> PerformanceOptimizer:
    """获取全局性能优化器"""
    global _global_optimizer
    
    if _global_optimizer is None:
        _global_optimizer = PerformanceOptimizer()
    
    return _global_optimizer


def configure_optimizer(enable_cache: bool = True, enable_parallel: bool = True, max_workers: int = 4) -> None:
    """配置全局优化器"""
    global _global_optimizer
    
    if _global_optimizer:
        _global_optimizer.shutdown()
    
    _global_optimizer = PerformanceOptimizer(
        enable_cache=enable_cache,
        enable_parallel=enable_parallel,
        max_workers=max_workers
    )


def shutdown_optimizer() -> None:
    """关闭全局优化器"""
    global _global_optimizer
    
    if _global_optimizer:
        _global_optimizer.shutdown()
        _global_optimizer = None
