#!/usr/bin/env python
"""
* @author: cz
* @description: 事件发布器系统

实现Mini-Boot框架的事件发布器,支持同步和异步事件发布、监听器管理和智能路由.
"""

import asyncio
import threading
import traceback
import weakref
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Callable, Optional

from loguru import logger

from ..errors import EventPublishError
from .base import Event


@dataclass
class EventHandlerInfo:
    """事件处理器信息

    封装事件监听器的详细信息,包括处理器函数、事件类型、执行条件等.
    支持弱引用以防止内存泄漏。
    """

    # 基本信息
    handler: Callable[[Event], Any]
    event_type: type[Event]
    instance: Optional[Any] = None  # 处理器所属的实例(用于方法调用)

    # 执行配置
    order: int = 0
    async_exec: bool = False
    condition: Optional[str] = None
    use_weak_ref: bool = True  # 是否使用弱引用

    # 元数据
    handler_name: str = field(default="")
    handler_id: str = field(default="")
    created_at: datetime = field(default_factory=datetime.now)

    # 弱引用存储（不包含在dataclass字段中）
    _weak_handler: Optional[weakref.ReferenceType] = field(default=None, init=False, repr=False)
    _weak_instance: Optional[weakref.ReferenceType] = field(default=None, init=False, repr=False)
    _method_name: Optional[str] = field(default=None, init=False, repr=False)

    def __post_init__(self):
        """初始化后处理"""
        if not self.handler_name:
            self.handler_name = getattr(self.handler, "__name__", str(self.handler))

        if not self.handler_id:
            # 生成唯一的处理器ID
            instance_id = id(self.instance) if self.instance else 0
            handler_id = id(self.handler)
            self.handler_id = f"{self.event_type.__name__}_{instance_id}_{handler_id}"

        # 设置弱引用（如果启用）
        if self.use_weak_ref:
            try:
                # 对于实例方法，我们需要特殊处理
                if self.instance is not None:
                    self._weak_instance = weakref.ref(self.instance)

                    # 检查是否是绑定方法
                    if hasattr(self.handler, '__self__') and hasattr(self.handler, '__name__'):
                        # 绑定方法：存储方法名
                        self._method_name = self.handler.__name__
                    elif hasattr(self.handler, '__name__'):
                        # 未绑定方法：存储方法名
                        self._method_name = self.handler.__name__
                    else:
                        # 其他可调用对象：尝试创建弱引用
                        self._weak_handler = weakref.ref(self.handler)

                    # 清除强引用以允许垃圾回收
                    original_handler = self.handler
                    original_instance = self.instance
                    self.handler = None
                    self.instance = None
                else:
                    # 对于函数，直接创建弱引用
                    self._weak_handler = weakref.ref(self.handler)
                    # 清除强引用
                    self.handler = None
            except TypeError:
                # 某些对象不支持弱引用，回退到强引用
                self.use_weak_ref = False

    def is_valid(self) -> bool:
        """检查处理器是否仍然有效（弱引用未被回收）

        Returns:
            如果处理器仍然有效返回True，否则返回False
        """
        if not self.use_weak_ref:
            return True

        # 检查实例是否仍然存在
        if self._weak_instance is not None:
            instance = self._weak_instance()
            if instance is None:
                return False

            # 如果是方法，检查方法是否仍然存在
            if self._method_name is not None:
                return hasattr(instance, self._method_name)

        # 检查处理器是否仍然存在
        if self._weak_handler is not None:
            return self._weak_handler() is not None

        return True

    def get_handler(self) -> Optional[Callable[[Event], Any]]:
        """获取实际的处理器函数

        Returns:
            处理器函数，如果弱引用已被回收则返回None
        """
        if not self.use_weak_ref:
            return self.handler

        # 如果是实例方法
        if self._weak_instance is not None:
            instance = self._weak_instance()
            if instance is None:
                return None

            if self._method_name is not None:
                return getattr(instance, self._method_name, None)

        # 如果是函数
        if self._weak_handler is not None:
            return self._weak_handler()

        # 如果使用弱引用但强引用已被清除，返回None
        return None

    def can_handle(self, event: Event) -> bool:
        """检查是否可以处理指定事件

        Args:
            event: 要检查的事件

        Returns:
            如果可以处理返回True,否则返回False
        """
        # 检查事件类型匹配
        if not isinstance(event, self.event_type):
            return False

        # 检查条件表达式
        if self.condition:
            try:
                # 简单的条件检查实现
                # 在实际应用中,这里可以集成更复杂的表达式引擎
                return self._evaluate_condition(event)
            except Exception:
                # 条件评估失败,默认不处理
                return False

        return True

    def _evaluate_condition(self, event: Event) -> bool:
        """评估条件表达式

        Args:
            event: 事件对象

        Returns:
            条件评估结果
        """
        if not self.condition:
            return True

        # 创建评估上下文
        context = {"event": event, "source": event.source, "timestamp": event.timestamp, "event_id": event.event_id, "processed": event.processed}

        # 如果是ApplicationEvent,添加数据访问
        if hasattr(event, "data"):
            context["data"] = event.data

        try:
            # 简单的表达式评估
            # 支持基本的比较操作和属性访问
            return eval(self.condition, {"__builtins__": {}}, context)
        except Exception:
            return False

    async def execute(self, event: Event) -> Any:
        """执行事件处理器

        Args:
            event: 要处理的事件

        Returns:
            处理器执行结果
        """
        # 获取实际的处理器函数
        handler = self.get_handler()
        if handler is None:
            raise RuntimeError(f"Handler {self.handler_name} is no longer available (weak reference expired)")

        try:
            if asyncio.iscoroutinefunction(handler):
                return await handler(event)
            else:
                return handler(event)
        except Exception as e:
            # 记录错误但不中断其他处理器
            logger.error(f"Error executing handler {self.handler_name}: {e}")
            raise

    def execute_sync(self, event: Event) -> Any:
        """同步执行事件处理器

        Args:
            event: 要处理的事件

        Returns:
            处理器执行结果
        """
        # 获取实际的处理器函数
        handler = self.get_handler()
        if handler is None:
            raise RuntimeError(f"Handler {self.handler_name} is no longer available (weak reference expired)")

        try:
            return handler(event)
        except Exception as e:
            logger.error(f"Error executing handler {self.handler_name}: {e}")
            raise

    def __str__(self) -> str:
        """字符串表示"""
        return f"EventHandlerInfo(handler={self.handler_name}, event_type={self.event_type.__name__}, order={self.order}, async={self.async_exec})"

    def __repr__(self) -> str:
        """详细表示"""
        return (
            f"EventHandlerInfo(handler={self.handler_name}, "
            f"event_type={self.event_type.__name__}, "
            f"order={self.order}, async_exec={self.async_exec}, "
            f"condition={self.condition}, handler_id={self.handler_id})"
        )


class EventPublisher:
    """事件发布器

    负责管理事件监听器的注册、事件的发布和路由.
    支持同步和异步事件处理,提供线程安全的操作.
    """

    def __init__(self, max_workers: int = 10, thread_name_prefix: str = "event-publisher", config=None):
        """初始化事件发布器

        Args:
            max_workers: 线程池最大工作线程数
            thread_name_prefix: 线程名称前缀
            config: Events配置对象(可选)
        """
        # 如果提供了配置对象,使用配置中的值
        if config and hasattr(config, "publisher"):
            max_workers = config.publisher.thread_pool_size
            thread_name_prefix = config.publisher.thread_name_prefix
        # 处理器存储:事件类型 -> 处理器列表
        self._handlers: dict[type[Event], list[EventHandlerInfo]] = {}

        # 线程安全锁
        self._lock = threading.RLock()

        # 线程池执行器(用于异步执行同步处理器)
        self._executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix=thread_name_prefix)

        # 统计信息（使用原子操作保护）
        self._stats = {"published_events": 0, "handled_events": 0, "failed_events": 0, "registered_handlers": 0}
        self._stats_lock = threading.Lock()  # 专门用于统计信息的锁

        # 是否已关闭
        self._shutdown = False

        # 清理计数器（每处理一定数量的事件后清理无效处理器）
        self._cleanup_counter = 0
        self._cleanup_threshold = 100  # 每100个事件后清理一次

    def _update_stats(self, stat_name: str, increment: int = 1) -> None:
        """线程安全地更新统计信息

        Args:
            stat_name: 统计项名称
            increment: 增量值，默认为1
        """
        with self._stats_lock:
            if stat_name in self._stats:
                self._stats[stat_name] += increment

    def _cleanup_invalid_handlers(self) -> int:
        """清理无效的处理器（弱引用已被回收）

        Returns:
            清理的处理器数量
        """
        cleaned_count = 0

        with self._lock:
            for event_type, handlers in list(self._handlers.items()):
                # 过滤出仍然有效的处理器
                valid_handlers = []
                for handler_info in handlers:
                    if handler_info.is_valid():
                        valid_handlers.append(handler_info)
                    else:
                        cleaned_count += 1

                # 更新处理器列表
                if valid_handlers:
                    self._handlers[event_type] = valid_handlers
                else:
                    # 如果没有有效处理器，删除整个事件类型
                    del self._handlers[event_type]

        # 更新统计信息
        if cleaned_count > 0:
            self._update_stats("registered_handlers", -cleaned_count)

        return cleaned_count

    def subscribe(
        self,
        event_type: type[Event],
        handler: Callable[[Event], Any],
        instance: Optional[Any] = None,
        order: int = 0,
        async_exec: bool = False,
        condition: Optional[str] = None,
    ) -> str:
        """订阅事件

        Args:
            event_type: 事件类型
            handler: 事件处理器函数
            instance: 处理器所属实例(用于方法调用)
            order: 执行顺序,数值越小优先级越高
            async_exec: 是否异步执行
            condition: 执行条件表达式

        Returns:
            处理器ID,用于后续取消订阅
        """
        if self._shutdown:
            raise RuntimeError("EventPublisher has been shutdown")

        with self._lock:
            # 创建处理器信息（默认启用弱引用）
            handler_info = EventHandlerInfo(
                handler=handler,
                event_type=event_type,
                instance=instance,
                order=order,
                async_exec=async_exec,
                condition=condition,
                use_weak_ref=True,  # 默认启用弱引用
            )

            # 添加到处理器列表
            if event_type not in self._handlers:
                self._handlers[event_type] = []

            self._handlers[event_type].append(handler_info)

            # 按优先级排序
            self._handlers[event_type].sort(key=lambda h: h.order)

            # 更新统计
            self._update_stats("registered_handlers")

            return handler_info.handler_id

    def unsubscribe(self, handler_id: str) -> bool:
        """取消订阅事件

        Args:
            handler_id: 处理器ID

        Returns:
            如果成功取消订阅返回True,否则返回False
        """
        with self._lock:
            for event_type, handlers in self._handlers.items():
                for i, handler_info in enumerate(handlers):
                    if handler_info.handler_id == handler_id:
                        handlers.pop(i)
                        self._stats["registered_handlers"] -= 1

                        # 如果该事件类型没有处理器了,删除条目
                        if not handlers:
                            del self._handlers[event_type]

                        return True

            return False

    def unsubscribe_all(self, event_type: type[Event]) -> int:
        """取消指定事件类型的所有订阅

        Args:
            event_type: 事件类型

        Returns:
            取消订阅的处理器数量
        """
        with self._lock:
            if event_type in self._handlers:
                count = len(self._handlers[event_type])
                del self._handlers[event_type]
                self._stats["registered_handlers"] -= count
                return count

            return 0

    def get_handlers(self, event: Event) -> list[EventHandlerInfo]:
        """获取事件的处理器列表

        Args:
            event: 事件对象

        Returns:
            匹配的处理器列表,按优先级排序
        """
        # 定期清理无效处理器
        self._cleanup_counter += 1
        if self._cleanup_counter >= self._cleanup_threshold:
            self._cleanup_counter = 0
            cleaned = self._cleanup_invalid_handlers()
            if cleaned > 0:
                print(f"Cleaned up {cleaned} invalid event handlers")

        handlers = []
        event_type = type(event)

        with self._lock:
            # 精确匹配
            if event_type in self._handlers:
                handlers.extend(self._handlers[event_type])

            # 父类匹配
            for registered_type, type_handlers in self._handlers.items():
                if registered_type != event_type and issubclass(event_type, registered_type):
                    handlers.extend(type_handlers)

        # 过滤可以处理该事件的处理器（包括检查弱引用有效性）
        valid_handlers = []
        for h in handlers:
            if h.is_valid() and h.can_handle(event):
                valid_handlers.append(h)

        # 按优先级排序
        valid_handlers.sort(key=lambda h: h.order)

        return valid_handlers

    def publish(self, event: Event) -> None:
        """同步发布事件

        Args:
            event: 要发布的事件
        """
        if self._shutdown:
            raise RuntimeError("EventPublisher has been shutdown")

        self._update_stats("published_events")

        try:
            handlers = self.get_handlers(event)

            if not handlers:
                return

            # 执行同步处理器
            for handler_info in handlers:
                try:
                    if handler_info.async_exec:
                        # 异步处理器在线程池中执行
                        self._executor.submit(self._execute_async_handler, handler_info, event)
                    else:
                        # 同步执行
                        if asyncio.iscoroutinefunction(handler_info.handler):
                            # 协程函数需要特殊处理
                            self._handle_coroutine_in_sync_context(handler_info, event)
                        else:
                            handler_info.execute_sync(event)

                    self._update_stats("handled_events")

                except Exception as e:
                    self._update_stats("failed_events")
                    logger.error(f"Error handling event {event.event_type}: {e}")

            # 标记事件已处理
            event.mark_processed()

        except Exception as e:
            self._update_stats("failed_events")
            logger.error(f"Error publishing event {type(event).__name__}: {e}")

            # 创建事件发布异常
            error = EventPublishError(f"Failed to publish event {type(event).__name__}: {str(e)}")
            raise error from e

    async def publish_async(self, event: Event) -> None:
        """异步发布事件

        Args:
            event: 要发布的事件
        """
        if self._shutdown:
            raise RuntimeError("EventPublisher has been shutdown")

        self._update_stats("published_events")

        try:
            handlers = self.get_handlers(event)

            if not handlers:
                return

            # 分离同步和异步处理器
            sync_handlers = [h for h in handlers if not h.async_exec]
            async_handlers = [h for h in handlers if h.async_exec]

            # 并发执行所有处理器
            tasks = []

            # 同步处理器在线程池中执行
            for handler_info in sync_handlers:
                if asyncio.iscoroutinefunction(handler_info.handler):
                    # 协程函数直接执行
                    tasks.append(self._execute_handler_async(handler_info, event))
                else:
                    # 同步函数在线程池中执行
                    loop = asyncio.get_event_loop()
                    task = loop.run_in_executor(self._executor, self._execute_sync_handler_safe, handler_info, event)
                    tasks.append(task)

            # 异步处理器直接执行
            for handler_info in async_handlers:
                tasks.append(self._execute_handler_async(handler_info, event))

            # 等待所有处理器完成
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 统计结果
                for result in results:
                    if isinstance(result, Exception):
                        self._stats["failed_events"] += 1
                        logger.error(f"Error handling event {event.event_type}: {result}")
                    else:
                        self._stats["handled_events"] += 1

            # 标记事件已处理
            event.mark_processed()

        except Exception as e:
            self._stats["failed_events"] += 1
            logger.error(f"Error publishing event {type(event).__name__}: {e}")
            raise

    def _execute_async_handler(self, handler_info: EventHandlerInfo, event: Event) -> None:
        """在线程池中执行异步处理器"""
        try:
            if asyncio.iscoroutinefunction(handler_info.handler):
                # 创建新的事件循环来运行协程
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(handler_info.execute(event))
                finally:
                    loop.close()
            else:
                handler_info.execute_sync(event)
        except Exception as e:
            logger.error(f"Error in async handler {handler_info.handler_name}: {e}")

    def _execute_sync_handler_safe(self, handler_info: EventHandlerInfo, event: Event) -> None:
        """安全执行同步处理器"""
        try:
            handler_info.execute_sync(event)
        except Exception as e:
            logger.error(f"Error in sync handler {handler_info.handler_name}: {e}")
            raise

    async def _execute_handler_async(self, handler_info: EventHandlerInfo, event: Event) -> Any:
        """异步执行处理器"""
        try:
            return await handler_info.execute(event)
        except Exception as e:
            logger.error(f"Error in handler {handler_info.handler_name}: {e}")
            raise

    def _handle_coroutine_in_sync_context(self, handler_info: EventHandlerInfo, event: Event) -> None:
        """在同步上下文中处理协程函数"""
        try:
            # 尝试获取当前事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行,创建任务
                loop.create_task(handler_info.execute(event))
            else:
                # 如果事件循环未运行,直接运行
                loop.run_until_complete(handler_info.execute(event))
        except RuntimeError:
            # 没有事件循环,在线程池中执行
            self._executor.submit(self._execute_async_handler, handler_info, event)

    def get_handler_count(self, event_type: Optional[type[Event]] = None) -> int:
        """获取处理器数量

        Args:
            event_type: 事件类型,如果为None则返回总数

        Returns:
            处理器数量
        """
        with self._lock:
            if event_type is None:
                return sum(len(handlers) for handlers in self._handlers.values())
            else:
                return len(self._handlers.get(event_type, []))

    def get_registered_event_types(self) -> set[type[Event]]:
        """获取已注册的事件类型

        Returns:
            事件类型集合
        """
        with self._lock:
            return set(self._handlers.keys())

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息

        Returns:
            统计信息字典
        """
        with self._stats_lock:
            stats = self._stats.copy()

        with self._lock:
            stats["active_handlers"] = sum(len(handlers) for handlers in self._handlers.values())
            stats["event_types"] = len(self._handlers)
            stats["is_shutdown"] = self._shutdown
            return stats

    def clear_handlers(self) -> None:
        """清空所有处理器"""
        with self._lock:
            cleared_count = sum(len(handlers) for handlers in self._handlers.values())
            self._handlers.clear()
            self._update_stats("registered_handlers", -cleared_count)

    def cleanup_handlers(self) -> int:
        """手动清理无效的处理器

        Returns:
            清理的处理器数量
        """
        return self._cleanup_invalid_handlers()

    def shutdown(self, wait: bool = True) -> None:
        """关闭事件发布器

        Args:
            wait: 是否等待正在执行的任务完成
        """
        self._shutdown = True
        self._executor.shutdown(wait=wait)

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()

    def __str__(self) -> str:
        """字符串表示"""
        stats = self.get_stats()
        return (
            f"EventPublisher(handlers={stats['active_handlers']}, "
            f"types={stats['event_types']}, "
            f"published={stats['published_events']}, "
            f"handled={stats['handled_events']})"
        )

    def __repr__(self) -> str:
        """详细表示"""
        return f"EventPublisher(stats={self.get_stats()})"


class ApplicationEventPublisher(EventPublisher):
    """应用事件发布器

    继承自EventPublisher，为应用上下文提供事件发布功能。
    简化的实现，避免不必要的包装层。
    """

    def __init__(self, max_workers: int = 10, config=None):
        """初始化应用事件发布器

        Args:
            max_workers: 线程池最大工作线程数
            config: Events配置对象(可选)
        """
        # 直接继承EventPublisher，避免包装器模式的开销
        super().__init__(max_workers=max_workers, config=config)
        self._auto_discovery_enabled = True

    def enable_auto_discovery(self, enabled: bool = True) -> None:
        """启用或禁用自动发现功能

        Args:
            enabled: 是否启用自动发现
        """
        self._auto_discovery_enabled = enabled

    def is_auto_discovery_enabled(self) -> bool:
        """检查是否启用了自动发现

        Returns:
            是否启用自动发现
        """
        return self._auto_discovery_enabled

    async def publish_event_async(self, event) -> None:
        """异步发布事件 - 兼容性方法

        这是publish_async方法的别名，用于向后兼容。

        Args:
            event: 要发布的事件
        """
        await self.publish_async(event)

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()
