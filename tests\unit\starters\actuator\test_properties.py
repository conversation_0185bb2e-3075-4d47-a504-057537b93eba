#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Properties module simple unit tests - basic testing for properties functionality
"""

import unittest

from miniboot.starters.actuator.properties import (ActuatorConfigUtils,
                                                    ActuatorProperties,
                                                    CoreModulesProperties,
                                                    CustomMetricsProperties,
                                                    EndpointsProperties,
                                                    ExportProperties,
                                                    JsonExportProperties,
                                                    MetricsProperties,
                                                    NamingProperties,
                                                    PerformanceProperties,
                                                    PrometheusExportProperties,
                                                    SecurityProperties,
                                                    WebProperties)


class ActuatorPropertiesTestCase(unittest.TestCase):
    """Actuator properties unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.properties = ActuatorProperties()

    def test_actuator_properties_initialization(self) -> None:
        """Test actuator properties initialization"""
        self.assertIsInstance(self.properties, ActuatorProperties)
        self.assertTrue(self.properties.enabled)
        self.assertTrue(self.properties.auto_start)

    def test_actuator_properties_nested_objects(self) -> None:
        """Test actuator properties nested objects"""
        self.assertIsInstance(self.properties.metrics, MetricsProperties)
        self.assertIsInstance(self.properties.web, WebProperties)
        self.assertIsInstance(self.properties.security, SecurityProperties)
        self.assertIsInstance(self.properties.performance, PerformanceProperties)


class MetricsPropertiesTestCase(unittest.TestCase):
    """Metrics properties unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.properties = MetricsProperties()

    def test_metrics_properties_initialization(self) -> None:
        """Test metrics properties initialization"""
        self.assertIsInstance(self.properties, MetricsProperties)
        self.assertTrue(self.properties.enabled)
        self.assertEqual(self.properties.collection_interval, "30s")
        self.assertTrue(self.properties.cache_enabled)


class WebPropertiesTestCase(unittest.TestCase):
    """Web properties unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.properties = WebProperties()

    def test_web_properties_initialization(self) -> None:
        """Test web properties initialization"""
        self.assertIsInstance(self.properties, WebProperties)
        self.assertTrue(self.properties.enabled)
        self.assertEqual(self.properties.base_path, "/actuator")
        self.assertIsNone(self.properties.port)


class SecurityPropertiesTestCase(unittest.TestCase):
    """Security properties unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.properties = SecurityProperties()

    def test_security_properties_initialization(self) -> None:
        """Test security properties initialization"""
        self.assertIsInstance(self.properties, SecurityProperties)
        self.assertTrue(self.properties.enabled)
        self.assertEqual(self.properties.username, "admin")
        self.assertEqual(self.properties.password, "admin")


class PerformancePropertiesTestCase(unittest.TestCase):
    """Performance properties unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.properties = PerformanceProperties()

    def test_performance_properties_initialization(self) -> None:
        """Test performance properties initialization"""
        self.assertIsInstance(self.properties, PerformanceProperties)
        self.assertEqual(self.properties.collection_interval, 60.0)
        self.assertEqual(self.properties.cache_ttl, 30.0)
        self.assertEqual(self.properties.max_metrics, 10000)


class EndpointsPropertiesTestCase(unittest.TestCase):
    """Endpoints properties unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.properties = EndpointsProperties()

    def test_endpoints_properties_initialization(self) -> None:
        """Test endpoints properties initialization"""
        self.assertIsInstance(self.properties, EndpointsProperties)
        self.assertTrue(self.properties.health)
        self.assertTrue(self.properties.info)
        self.assertTrue(self.properties.metrics)


class ActuatorConfigUtilsTestCase(unittest.TestCase):
    """Actuator config utils unit test suite"""

    def test_actuator_config_utils_import(self) -> None:
        """Test actuator config utils can be imported"""
        self.assertIsNotNone(ActuatorConfigUtils)

    def test_actuator_config_utils_is_class(self) -> None:
        """Test actuator config utils is a class"""
        self.assertTrue(isinstance(ActuatorConfigUtils, type))

    def test_actuator_config_utils_has_methods(self) -> None:
        """Test actuator config utils has required methods"""
        self.assertTrue(hasattr(ActuatorConfigUtils, 'validate_endpoint_path'))
        self.assertTrue(hasattr(ActuatorConfigUtils, 'merge_tags'))


class PropertiesModuleTestCase(unittest.TestCase):
    """Properties module integration test suite"""

    def test_all_properties_classes_exist(self) -> None:
        """Test all properties classes exist and can be imported"""
        properties_classes = [
            ActuatorProperties,
            MetricsProperties,
            WebProperties,
            SecurityProperties,
            PerformanceProperties,
            EndpointsProperties,
            CoreModulesProperties,
            CustomMetricsProperties,
            NamingProperties,
            ExportProperties,
            PrometheusExportProperties,
            JsonExportProperties,
            ActuatorConfigUtils
        ]
        
        for properties_class in properties_classes:
            with self.subTest(properties_class=properties_class.__name__):
                self.assertIsNotNone(properties_class)
                self.assertTrue(isinstance(properties_class, type))

    def test_properties_instantiation(self) -> None:
        """Test properties classes can be instantiated"""
        instantiable_classes = [
            ActuatorProperties,
            MetricsProperties,
            WebProperties,
            SecurityProperties,
            PerformanceProperties,
            EndpointsProperties,
            CoreModulesProperties,
            CustomMetricsProperties,
            NamingProperties,
            ExportProperties,
            PrometheusExportProperties,
            JsonExportProperties
        ]
        
        for properties_class in instantiable_classes:
            with self.subTest(properties_class=properties_class.__name__):
                instance = properties_class()
                self.assertIsInstance(instance, properties_class)


if __name__ == "__main__":
    unittest.main()
