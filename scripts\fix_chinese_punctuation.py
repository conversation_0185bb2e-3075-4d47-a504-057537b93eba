#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
* @author: cz
* @description: 自动修复中文标点符号脚本
"""

from pathlib import Path


def fix_chinese_punctuation_in_file(file_path: Path) -> bool:
    """修复单个文件中的中文标点符号

    Args:
        file_path: 文件路径

    Returns:
        是否进行了修复
    """
    try:
        with file_path.open(encoding="utf-8") as f:
            content = f.read()

        original_content = content

        # 定义中文标点符号到英文标点符号的映射
        punctuation_map = {
            ",": ",",  # 中文逗号 -> 英文逗号
            ".": ".",  # 中文句号 -> 英文句号
            ";": ";",  # 中文分号 -> 英文分号
            ":": ":",  # 中文冒号 -> 英文冒号
            "(": "(",  # 中文左括号 -> 英文左括号
            ")": ")",  # 中文右括号 -> 英文右括号
            "[": "[",  # 中文左方括号 -> 英文左方括号
            "]": "]",  # 中文右方括号 -> 英文右方括号
            "!": "!",  # 中文感叹号 -> 英文感叹号
            "?": "?",  # 中文问号 -> 英文问号
            """: '"',  # 中文左双引号 -> 英文双引号
            """: '"',  # 中文右双引号 -> 英文双引号
            "'": "'",  # 中文右单引号 -> 英文单引号
            "<": "<",  # 中文左书名号 -> 英文小于号
            ">": ">",  # 中文右书名号 -> 英文大于号
            # 全角字符
            "．": ".",  # 全角右括号 -> 英文右括号
            "［": "[",  # 全角左方括号 -> 英文左方括号
            "］": "]",  # 全角右方括号 -> 英文右方括号
            "｛": "{",  # 全角左大括号 -> 英文左大括号
            "｝": "}",  # 全角问号 -> 英文问号
            "＇": "'",  # 全角单引号 -> 英文单引号
            "＂": '"',  # 全角双引号 -> 英文双引号
        }

        # 替换中文标点符号
        for chinese_punct, english_punct in punctuation_map.items():
            content = content.replace(chinese_punct, english_punct)

        # 如果内容有变化,写回文件
        if content != original_content:
            with file_path.open("w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ 修复: {file_path}")
            return True

        return False

    except Exception as e:
        print(f"❌ 错误: {file_path}: {e}")
        return False


def fix_chinese_punctuation_in_directory(directory: Path) -> tuple[int, int]:
    """修复目录下所有Python文件中的中文标点符号

    Args:
        directory: 目录路径

    Returns:
        (修复的文件数, 总检查的文件数)
    """
    fixed_count = 0
    total_count = 0

    # 跳过的目录
    skip_patterns = [
        "__pycache__",
        ".venv",
        "venv",
        ".git",
        "htmlcov",
        "build",
        "dist",
        "*.egg-info",
    ]

    for py_file in directory.rglob("*.py"):
        # 检查是否应该跳过
        if any(pattern in str(py_file) for pattern in skip_patterns):
            continue

        # 跳过代码质量检查文件,因为它需要保留中文标点符号来进行检查
        if py_file.name == "code_quality.py":
            continue

        total_count += 1
        if fix_chinese_punctuation_in_file(py_file):
            fixed_count += 1

    return fixed_count, total_count


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent

    print("🔧 开始修复中文标点符号...")
    print(f"📁 项目根目录: {project_root}")
    print()

    # 修复 miniboot 目录
    print("📦 修复 miniboot 目录...")
    miniboot_fixed, miniboot_total = fix_chinese_punctuation_in_directory(project_root / "miniboot")

    # 修复 tests 目录
    print("🧪 修复 tests 目录...")
    tests_fixed, tests_total = fix_chinese_punctuation_in_directory(project_root / "tests")

    # 统计结果
    total_fixed = miniboot_fixed + tests_fixed
    total_checked = miniboot_total + tests_total

    print()
    print("📊 修复统计:")
    print(f"   miniboot: {miniboot_fixed}/{miniboot_total} 个文件")
    print(f"   tests: {tests_fixed}/{tests_total} 个文件")
    print(f"   总计: {total_fixed}/{total_checked} 个文件")

    if total_fixed > 0:
        print(f"✅ 成功修复 {total_fixed} 个文件中的中文标点符号!")
    else:
        print("✅ 没有发现需要修复的中文标点符号问题!")


if __name__ == "__main__":
    main()
