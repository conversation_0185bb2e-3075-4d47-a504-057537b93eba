#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Configuration utilities unit tests - testing for ActuatorConfigUtils static methods
"""

import unittest
from typing import Dict

from miniboot.starters.actuator.properties import ActuatorConfigUtils


class ActuatorConfigUtilsTestCase(unittest.TestCase):
    """Actuator config utilities unit test suite"""

    def test_validate_endpoint_path_valid(self) -> None:
        """Test validating valid endpoint paths"""
        valid_paths = [
            "/actuator",
            "/health",
            "/metrics",
            "/info",
            "/actuator/health",
            "/api/v1/actuator"
        ]

        for path in valid_paths:
            with self.subTest(path=path):
                self.assertTrue(ActuatorConfigUtils.validate_endpoint_path(path))

    def test_validate_endpoint_path_invalid(self) -> None:
        """Test validating invalid endpoint paths"""
        invalid_paths = [
            "",  # Empty path
            "actuator",  # No leading slash
            "/actuator?param=value",  # Query parameter
            "/actuator#fragment",  # Fragment
            "/actuator&param=value",  # Ampersand
            "/actuator with space",  # Space
            "/actuator\ttab",  # Tab
            "/actuator\nnewline",  # Newline
        ]

        for path in invalid_paths:
            with self.subTest(path=path):
                self.assertFalse(ActuatorConfigUtils.validate_endpoint_path(path))

    def test_validate_endpoint_path_none(self) -> None:
        """Test validating None endpoint path"""
        # None should return False (handled by the method)
        self.assertFalse(ActuatorConfigUtils.validate_endpoint_path(None))

    def test_merge_tags_basic(self) -> None:
        """Test basic tag merging"""
        common_tags = {"app": "test", "version": "1.0"}
        specific_tags = {"endpoint": "health", "method": "GET"}

        merged = ActuatorConfigUtils.merge_tags(common_tags, specific_tags)

        expected = {"app": "test", "version": "1.0", "endpoint": "health", "method": "GET"}
        self.assertEqual(merged, expected)

    def test_merge_tags_override(self) -> None:
        """Test tag merging with override"""
        common_tags = {"app": "test", "version": "1.0", "env": "dev"}
        specific_tags = {"env": "prod", "endpoint": "metrics"}

        merged = ActuatorConfigUtils.merge_tags(common_tags, specific_tags)

        # Specific tags should override common tags
        expected = {"app": "test", "version": "1.0", "env": "prod", "endpoint": "metrics"}
        self.assertEqual(merged, expected)

    def test_merge_tags_empty(self) -> None:
        """Test tag merging with empty dictionaries"""
        # Empty common tags
        common_tags: Dict[str, str] = {}
        specific_tags = {"endpoint": "health"}
        merged = ActuatorConfigUtils.merge_tags(common_tags, specific_tags)
        self.assertEqual(merged, {"endpoint": "health"})

        # Empty specific tags
        common_tags = {"app": "test"}
        specific_tags = {}
        merged = ActuatorConfigUtils.merge_tags(common_tags, specific_tags)
        self.assertEqual(merged, {"app": "test"})

        # Both empty
        common_tags = {}
        specific_tags = {}
        merged = ActuatorConfigUtils.merge_tags(common_tags, specific_tags)
        self.assertEqual(merged, {})

    def test_merge_tags_immutability(self) -> None:
        """Test that original dictionaries are not modified"""
        common_tags = {"app": "test", "version": "1.0"}
        specific_tags = {"endpoint": "health"}

        original_common = common_tags.copy()
        original_specific = specific_tags.copy()

        merged = ActuatorConfigUtils.merge_tags(common_tags, specific_tags)

        # Original dictionaries should remain unchanged
        self.assertEqual(common_tags, original_common)
        self.assertEqual(specific_tags, original_specific)

        # Merged should contain all tags
        self.assertEqual(len(merged), 3)
        self.assertIn("app", merged)
        self.assertIn("version", merged)
        self.assertIn("endpoint", merged)


if __name__ == "__main__":
    unittest.main()
