#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 参数绑定器模块 - 简化的参数解析和绑定系统，支持基础类型转换和验证

参数绑定器模块

简化的参数解析和绑定系统，支持基础类型转换和验证。

主要功能:
- ParameterBinder - 参数绑定器
- 基础类型转换（str, int, float, bool, list, dict）
- Pydantic 模型支持
- 请求数据提取和参数绑定
"""

import inspect
import json
from enum import Enum
from typing import Any, Callable, Optional, get_type_hints

from fastapi import HTTPException, Request
from loguru import logger
from pydantic import BaseModel, ValidationError


class ParameterSource(Enum):
    """参数来源"""

    PATH = "path"
    QUERY = "query"
    HEADER = "header"
    BODY = "body"
    FORM = "form"


class ParameterBinder:
    """简化的参数绑定器

    支持基础类型转换和参数绑定功能。
    """

    def __init__(self):
        """初始化参数绑定器"""
        # 函数签名缓存
        self._signature_cache: dict[str, dict[str, Any]] = {}

        logger.debug("ParameterBinder initialized")

    async def bind_parameters(self, function: Callable, request: Request, path_params: Optional[dict[str, Any]] = None) -> dict[str, Any]:
        """绑定函数参数

        Args:
            function: 目标函数
            request: HTTP请求
            path_params: 路径参数

        Returns:
            绑定的参数字典
        """
        try:
            # 获取函数签名信息
            param_info = self._get_function_signature(function)

            # 提取请求数据
            request_data = await self._extract_request_data(request, path_params or {})

            # 绑定参数
            bound_params = {}

            for param_name, param_config in param_info.items():
                # 获取原始值
                raw_value = self._get_parameter_value(param_name, param_config, request_data)

                # 类型转换
                if raw_value is not None:
                    try:
                        converted_value = self._convert_value(raw_value, param_config["type"])
                        bound_params[param_name] = converted_value
                    except (ValueError, TypeError) as e:
                        if param_config["required"]:
                            raise HTTPException(status_code=400, detail=f"Parameter '{param_name}' conversion failed: {str(e)}") from e
                elif param_config["required"]:
                    raise HTTPException(status_code=400, detail=f"Required parameter '{param_name}' is missing")
                elif param_config["default"] is not inspect.Parameter.empty:
                    bound_params[param_name] = param_config["default"]

            return bound_params

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Parameter binding failed: {e}")
            raise HTTPException(status_code=500, detail="Internal parameter binding error") from e

    def _get_function_signature(self, function: Callable) -> dict[str, Any]:
        """获取函数签名信息（带缓存）"""
        function_key = f"{function.__module__}.{function.__name__}"

        if function_key in self._signature_cache:
            return self._signature_cache[function_key]

        # 分析函数签名
        signature = inspect.signature(function)
        type_hints = get_type_hints(function)

        param_info = {}

        for param_name, param in signature.parameters.items():
            # 跳过特殊参数
            if param_name in ["self", "cls", "request"]:
                continue

            param_type = type_hints.get(param_name, str)
            source = self._determine_parameter_source(param_name, param_type)

            param_info[param_name] = {
                "type": param_type,
                "source": source,
                "required": param.default == inspect.Parameter.empty,
                "default": param.default,
            }

        # 缓存结果
        self._signature_cache[function_key] = param_info
        return param_info

    def _determine_parameter_source(self, param_name: str, param_type: type) -> ParameterSource:
        """确定参数来源"""
        # 简单的启发式规则
        if param_name in ["id", "user_id", "item_id"] or param_name.endswith("_id"):
            return ParameterSource.PATH
        elif param_type in [dict, list] or (hasattr(param_type, "__bases__") and BaseModel in param_type.__bases__):
            return ParameterSource.BODY
        else:
            return ParameterSource.QUERY

    async def _extract_request_data(self, request: Request, path_params: dict[str, Any]) -> dict[str, Any]:
        """提取请求数据"""
        data = {"path": path_params, "query": dict(request.query_params), "headers": dict(request.headers), "body": None, "form": None}

        # 提取请求体
        try:
            content_type = request.headers.get("content-type", "")
            if content_type.startswith("application/json"):
                data["body"] = await request.json()
            elif content_type.startswith("application/x-www-form-urlencoded"):
                form_data = await request.form()
                data["form"] = dict(form_data)
        except Exception as e:
            logger.debug(f"Failed to extract request body: {e}")

        return data

    def _get_parameter_value(self, param_name: str, param_config: dict[str, Any], request_data: dict[str, Any]) -> Any:
        """获取参数值"""
        source = param_config["source"]

        if source == ParameterSource.PATH:
            return request_data["path"].get(param_name)
        elif source == ParameterSource.QUERY:
            return request_data["query"].get(param_name)
        elif source == ParameterSource.HEADER:
            return request_data["headers"].get(param_name)
        elif source == ParameterSource.BODY:
            return request_data["body"]
        elif source == ParameterSource.FORM:
            return request_data["form"]
        else:
            return None

    def _convert_value(self, value: Any, target_type: type) -> Any:
        """类型转换"""
        if value is None:
            return None

        # 如果已经是目标类型，直接返回
        if isinstance(value, target_type):
            return value

        # 基础类型转换
        if target_type is str:
            return str(value)
        elif target_type is int:
            return int(value)
        elif target_type is float:
            return float(value)
        elif target_type is bool:
            return self._convert_bool(value)
        elif target_type is list:
            return self._convert_list(value)
        elif target_type is dict:
            return self._convert_dict(value)
        elif hasattr(target_type, "__bases__") and BaseModel in target_type.__bases__:
            return self._convert_pydantic_model(value, target_type)
        else:
            # 尝试直接转换
            return target_type(value)

    def _convert_bool(self, value: Any) -> bool:
        """转换布尔值"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ("true", "1", "yes", "on")
        return bool(value)

    def _convert_list(self, value: Any) -> list:
        """转换列表"""
        if isinstance(value, list):
            return value
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value.split(",")
        return [value]

    def _convert_dict(self, value: Any) -> dict:
        """转换字典"""
        if isinstance(value, dict):
            return value
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError as e:
                raise ValueError(f"Cannot parse '{value}' as JSON") from e
        raise ValueError(f"Cannot convert {type(value)} to dict")

    def _convert_pydantic_model(self, value: Any, model_class: type[BaseModel]) -> BaseModel:
        """转换 Pydantic 模型"""
        try:
            if isinstance(value, dict):
                return model_class(**value)
            elif isinstance(value, str):
                data = json.loads(value)
                return model_class(**data)
            else:
                return model_class.model_validate(value)
        except (ValidationError, json.JSONDecodeError) as e:
            raise ValueError(f"Cannot parse value as {model_class.__name__}: {e}") from e

    def clear_cache(self) -> None:
        """清理缓存"""
        self._signature_cache.clear()
        logger.debug("Parameter signature cache cleared")

    def cleanup(self) -> None:
        """清理参数绑定器资源"""
        self.clear_cache()
        logger.debug("ParameterBinder cleanup completed")
