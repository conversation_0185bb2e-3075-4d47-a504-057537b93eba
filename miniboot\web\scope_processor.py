#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web作用域 Bean 后置处理器

实现Web作用域 Bean 后置处理器,用于处理Web相关作用域的 Bean 创建和管理.
支持 request、session、application、websocket 等作用域.
"""

from typing import Any, Optional

from loguru import logger

from ..annotations.scope import get_scope_value, is_scoped
from ..errors import ProcessorExecutionError as BeanProcessingError
from ..processor.base import BeanPostProcessor, ProcessorOrder
from .scopes import WebBeanScope, WebScopeRegistry


def has_web_scope(bean_class: type) -> bool:
    """检查Bean类是否有Web作用域注解

    Args:
        bean_class: Bean类

    Returns:
        bool: 如果有Web作用域注解返回True
    """
    if not is_scoped(bean_class):
        return False

    scope_value = get_scope_value(bean_class)
    if not scope_value:
        return False

    try:
        WebBeanScope.from_string(scope_value.value)
        return True
    except ValueError:
        return False


class WebScopeBeanPostProcessor(BeanPostProcessor):
    """Web作用域 Bean 后置处理器

    负责处理Web相关作用域的 Bean 创建和管理.
    """

    def __init__(self):
        """初始化Web作用域处理器"""
        self._scope_registry = WebScopeRegistry()
        self._processed_beans: set[str] = set()

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """Bean初始化前处理

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        if bean_name in self._processed_beans:
            return bean

        try:
            # 检查 Bean 类是否有作用域注解
            bean_class = bean.__class__
            if not is_scoped(bean_class):
                return bean

            # 获取作用域信息
            scope_value = get_scope_value(bean_class)
            if not scope_value:
                return bean

            # 只处理 Web 相关作用域
            if not has_web_scope(bean_class):
                return bean

            # 转换为WebBeanScope枚举
            try:
                web_scope = WebBeanScope.from_string(scope_value.value)
            except ValueError:
                logger.warning(f"Invalid web scope '{scope_value.value}' for bean '{bean_name}'")
                return bean

            # 获取作用域管理器
            scope_manager = self._scope_registry.get_scope_manager(web_scope)
            if not scope_manager:
                logger.warning(f"No scope manager found for scope '{web_scope.value}' for bean '{bean_name}'")
                return bean

            # 获取当前作用域上下文
            scope_context = scope_manager.get_scope_context()
            if not scope_context:
                logger.warning(f"No active scope context for scope '{web_scope.value}' for bean '{bean_name}'")
                return bean

            # 检查作用域中是否已存在该Bean
            existing_bean = scope_context.get_bean(bean_name)
            if existing_bean is not None:
                logger.debug(f"Found existing bean '{bean_name}' in {web_scope.value} scope")
                return existing_bean

            # 将Bean存储到作用域中
            scope_context.put_bean(bean_name, bean)
            self._processed_beans.add(bean_name)

            logger.debug(f"Stored bean '{bean_name}' in {web_scope.value} scope")
            return bean

        except Exception as e:
            raise BeanProcessingError(f"Failed to process web scope for bean '{bean_name}': {e}") from e

    def post_process_after_initialization(self, bean: Any, _bean_name: str) -> Any:
        """Bean初始化后处理(Web作用域处理器不需要后处理)

        Args:
            bean: Bean实例
            _bean_name: Bean名称(未使用)

        Returns:
            原始Bean实例
        """
        return bean

    def get_order(self) -> int:
        """获取处理器执行顺序

        Returns:
            执行顺序值，数值越小优先级越高
        """
        return ProcessorOrder.SCOPE.value

    def supports_bean(self, bean: Any, bean_name: str) -> bool:
        """检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            如果支持处理返回True，否则返回False
        """
        if bean is None:
            return False

        # 检查 Bean 类是否有Web作用域注解
        return has_web_scope(bean.__class__)

    def get_scoped_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定Web作用域获取 Bean

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值

        Returns:
            Bean 实例,如果不存在则返回 None
        """
        try:
            web_scope = WebBeanScope.from_string(scope_value.value)
        except ValueError:
            return None

        scope_manager = self._scope_registry.get_scope_manager(web_scope)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.get_bean(bean_name)

    def remove_scoped_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定Web作用域移除 Bean

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值

        Returns:
            被移除的 Bean 实例
        """
        try:
            web_scope = WebBeanScope.from_string(scope_value.value)
        except ValueError:
            return None

        scope_manager = self._scope_registry.get_scope_manager(web_scope)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.remove_bean(bean_name)


class WebScopedBeanFactory:
    """Web作用域 Bean 工厂

    提供Web作用域 Bean 的创建和获取功能.
    """

    def __init__(self):
        """初始化Web作用域 Bean 工厂"""
        self._scope_registry = WebScopeRegistry()

    def get_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定Web作用域获取Bean

        Args:
            bean_name: Bean名称
            scope_value: 作用域值

        Returns:
            Bean实例，如果不存在则返回None
        """
        try:
            web_scope = WebBeanScope.from_string(scope_value.value)
        except ValueError:
            return None

        scope_manager = self._scope_registry.get_scope_manager(web_scope)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.get_bean(bean_name)

    def put_bean(self, bean_name: str, bean: Any, scope_value, scope_id: Optional[str] = None) -> bool:
        """将Bean存储到指定Web作用域

        Args:
            bean_name: Bean名称
            bean: Bean实例
            scope_value: 作用域值
            scope_id: 作用域标识符

        Returns:
            如果成功存储返回True，否则返回False
        """
        try:
            web_scope = WebBeanScope.from_string(scope_value.value)
        except ValueError:
            return False

        scope_manager = self._scope_registry.get_scope_manager(web_scope)
        if not scope_manager:
            return False

        scope_context = scope_manager.get_scope_context(scope_id)
        if not scope_context and scope_id:
            scope_context = scope_manager.create_scope_context(scope_id)

        if scope_context:
            scope_context.put_bean(bean_name, bean)
            return True

        return False

    def clear_scope(self, scope_value, scope_id: Optional[str] = None) -> None:
        """清空指定Web作用域的所有 Bean

        Args:
            scope_value: 作用域值
            scope_id: 作用域标识符
        """
        try:
            web_scope = WebBeanScope.from_string(scope_value.value)
        except ValueError:
            return

        scope_manager = self._scope_registry.get_scope_manager(web_scope)
        if not scope_manager:
            return

        scope_context = scope_manager.get_scope_context(scope_id)
        if not scope_context:
            return

        # 获取所有 Bean 名称并逐个移除
        bean_names = list(scope_context.get_bean_names())
        for bean_name in bean_names:
            scope_context.remove_bean(bean_name)

        logger.debug(f"Cleared all beans from {web_scope.value} scope")
