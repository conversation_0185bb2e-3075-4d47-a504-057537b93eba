#!/usr/bin/env python
"""
* @author: cz
* @description: 转换服务测试
"""

import unittest

from miniboot.env.convert import ConversionError, Converter, DefaultConversionService


class DefaultConversionServiceTestCase(unittest.TestCase):
    """默认转换服务测试"""

    def setUp(self):
        self.service = DefaultConversionService()

    def test_can_convert(self):
        """测试转换能力检查"""
        # 测试相同类型
        self.assertTrue(self.service.can_convert(str, str))

        # 测试支持的转换
        self.assertTrue(self.service.can_convert(str, bool))
        self.assertTrue(self.service.can_convert(str, int))
        self.assertTrue(self.service.can_convert(str, float))
        self.assertTrue(self.service.can_convert(str, list))

        # 测试不支持的转换(使用一个确实无法转换的类型)
        class CustomType:
            pass

        self.assertFalse(self.service.can_convert(CustomType, list))

    def test_convert_same_type(self):
        """测试相同类型转换"""
        value = "test"
        result = self.service.convert(value, str)
        self.assertEqual(value, result)

    def test_convert_none_value(self):
        """测试 None 值转换"""
        result = self.service.convert(None, str)
        self.assertIsNone(result)

    def test_convert_string_to_types(self):
        """测试字符串到各种类型的转换"""
        # 字符串到布尔值
        self.assertTrue(self.service.convert("true", bool))
        self.assertFalse(self.service.convert("false", bool))

        # 字符串到整数
        self.assertEqual(123, self.service.convert("123", int))

        # 字符串到浮点数
        self.assertEqual(123.45, self.service.convert("123.45", float))

        # 字符串到列表
        self.assertEqual(["a", "b"], self.service.convert("a,b", list))

    def test_convert_to_string(self):
        """测试转换到字符串"""
        self.assertEqual("123", self.service.convert(123, str))
        self.assertEqual("True", self.service.convert(True, str))
        self.assertEqual("[1, 2, 3]", self.service.convert([1, 2, 3], str))

    def test_convert_unsupported_type(self):
        """测试不支持的类型转换"""

        class CustomType:
            pass

        with self.assertRaises(ConversionError):
            self.service.convert(CustomType(), list)

    def test_add_custom_converter(self):
        """测试添加自定义转换器"""

        class CustomConverter(Converter[str]):
            def can_convert(self, source_type, target_type):
                return source_type is dict and target_type is str

            def convert(self, source, _target_type):
                return f"dict:{len(source)}"

        # 添加自定义转换器
        custom_converter = CustomConverter()
        self.service.add_converter(custom_converter)

        # 测试自定义转换
        result = self.service.convert({"a": 1, "b": 2}, str)
        self.assertEqual("dict:2", result)


if __name__ == "__main__":
    unittest.main()
