#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean定义模块单元测试
"""

import unittest

from miniboot.bean.definition import (
    BeanDefinition, BeanScope, BeanStatus,
    PropertyValue, ConstructorArgument
)


class TestService:
    """测试用的服务类"""
    def __init__(self, name: str = "test"):
        self.name = name


class BeanScopeTestCase(unittest.TestCase):
    """Bean作用域枚举测试"""

    def test_scope_values(self):
        """测试作用域枚举值"""
        self.assertEqual(BeanScope.SINGLETON.value, "singleton")
        self.assertEqual(BeanScope.PROTOTYPE.value, "prototype")

    def test_scope_string_representation(self):
        """测试作用域字符串表示"""
        self.assertEqual(str(BeanScope.SINGLETON), "singleton")
        self.assertEqual(str(BeanScope.PROTOTYPE), "prototype")

    def test_scope_repr(self):
        """测试作用域repr表示"""
        self.assertEqual(repr(BeanScope.SINGLETON), "BeanScope.SINGLETON")
        self.assertEqual(repr(BeanScope.PROTOTYPE), "BeanScope.PROTOTYPE")

    def test_from_string_valid(self):
        """测试从字符串创建作用域 - 有效值"""
        self.assertEqual(BeanScope.from_string("singleton"), BeanScope.SINGLETON)
        self.assertEqual(BeanScope.from_string("prototype"), BeanScope.PROTOTYPE)
        self.assertEqual(BeanScope.from_string("SINGLETON"), BeanScope.SINGLETON)
        self.assertEqual(BeanScope.from_string(" prototype "), BeanScope.PROTOTYPE)

    def test_from_string_invalid(self):
        """测试从字符串创建作用域 - 无效值"""
        with self.assertRaises(ValueError) as context:
            BeanScope.from_string("invalid")
        self.assertIn("Invalid core scope value", str(context.exception))

    def test_is_singleton(self):
        """测试是否为单例作用域"""
        self.assertTrue(BeanScope.SINGLETON.is_singleton())
        self.assertFalse(BeanScope.PROTOTYPE.is_singleton())

    def test_is_prototype(self):
        """测试是否为原型作用域"""
        self.assertTrue(BeanScope.PROTOTYPE.is_prototype())
        self.assertFalse(BeanScope.SINGLETON.is_prototype())

    def test_is_core_scope(self):
        """测试是否为核心作用域"""
        self.assertTrue(BeanScope.is_core_scope("singleton"))
        self.assertTrue(BeanScope.is_core_scope("prototype"))
        self.assertFalse(BeanScope.is_core_scope("request"))
        self.assertFalse(BeanScope.is_core_scope("invalid"))


class BeanStatusTestCase(unittest.TestCase):
    """Bean状态枚举测试"""

    def test_status_values(self):
        """测试状态枚举值"""
        self.assertEqual(BeanStatus.NOT_CREATED.value, "not_created")
        self.assertEqual(BeanStatus.CREATING.value, "creating")
        self.assertEqual(BeanStatus.CREATED.value, "created")
        self.assertEqual(BeanStatus.FAILED.value, "failed")

    def test_is_active(self):
        """测试是否为活跃状态"""
        self.assertTrue(BeanStatus.CREATED.is_active())
        self.assertTrue(BeanStatus.INITIALIZED.is_active())
        self.assertFalse(BeanStatus.NOT_CREATED.is_active())
        self.assertFalse(BeanStatus.CREATING.is_active())
        self.assertFalse(BeanStatus.FAILED.is_active())

    def test_is_in_progress(self):
        """测试是否正在处理中"""
        self.assertTrue(BeanStatus.CREATING.is_in_progress())
        self.assertTrue(BeanStatus.INITIALIZING.is_in_progress())
        self.assertTrue(BeanStatus.DESTROYING.is_in_progress())
        self.assertFalse(BeanStatus.CREATED.is_in_progress())
        self.assertFalse(BeanStatus.NOT_CREATED.is_in_progress())

    def test_is_completed(self):
        """测试是否已完成"""
        self.assertTrue(BeanStatus.INITIALIZED.is_completed())
        self.assertTrue(BeanStatus.DESTROYED.is_completed())
        self.assertTrue(BeanStatus.FAILED.is_completed())
        self.assertFalse(BeanStatus.CREATING.is_completed())
        self.assertFalse(BeanStatus.NOT_CREATED.is_completed())

    def test_can_transition_to_valid(self):
        """测试有效状态转换"""
        # NOT_CREATED -> CREATING
        self.assertTrue(BeanStatus.NOT_CREATED.can_transition_to(BeanStatus.CREATING))

        # CREATING -> CREATED or FAILED
        self.assertTrue(BeanStatus.CREATING.can_transition_to(BeanStatus.CREATED))
        self.assertTrue(BeanStatus.CREATING.can_transition_to(BeanStatus.FAILED))

        # CREATED -> INITIALIZING
        self.assertTrue(BeanStatus.CREATED.can_transition_to(BeanStatus.INITIALIZING))

        # FAILED -> CREATING (重试)
        self.assertTrue(BeanStatus.FAILED.can_transition_to(BeanStatus.CREATING))

    def test_can_transition_to_invalid(self):
        """测试无效状态转换"""
        # NOT_CREATED 不能直接到 CREATED
        self.assertFalse(BeanStatus.NOT_CREATED.can_transition_to(BeanStatus.CREATED))

        # DESTROYED 是终态
        self.assertFalse(BeanStatus.DESTROYED.can_transition_to(BeanStatus.CREATING))

        # CREATING 不能到 INITIALIZING
        self.assertFalse(BeanStatus.CREATING.can_transition_to(BeanStatus.INITIALIZING))


class PropertyValueTestCase(unittest.TestCase):
    """属性值测试"""

    def test_property_value_creation(self):
        """测试属性值创建"""
        prop = PropertyValue("name", "test_value")
        self.assertEqual(prop.name, "name")
        self.assertEqual(prop.value, "test_value")
        self.assertFalse(prop.is_reference())

    def test_property_value_reference(self):
        """测试属性值引用"""
        prop = PropertyValue("service", ref="userService")
        self.assertEqual(prop.name, "service")
        self.assertEqual(prop.ref, "userService")
        self.assertTrue(prop.is_reference())

    def test_property_value_validation(self):
        """测试属性值验证"""
        with self.assertRaises(ValueError):
            PropertyValue("", "value")  # 空名称

        with self.assertRaises(ValueError):
            PropertyValue(None, "value")  # None名称


class ConstructorArgumentTestCase(unittest.TestCase):
    """构造函数参数测试"""

    def test_constructor_argument_creation(self):
        """测试构造函数参数创建"""
        arg = ConstructorArgument(0, "test_value")
        self.assertEqual(arg.index, 0)
        self.assertEqual(arg.value, "test_value")
        self.assertIsNone(arg.type_hint)
        self.assertFalse(arg.is_reference())

    def test_constructor_argument_with_type(self):
        """测试带类型的构造函数参数"""
        arg = ConstructorArgument(0, "test", type_hint=str)
        self.assertEqual(arg.type_hint, str)

    def test_constructor_argument_reference(self):
        """测试构造函数参数引用"""
        arg = ConstructorArgument(0, ref="userService")
        self.assertTrue(arg.is_reference())

    def test_constructor_argument_validation(self):
        """测试构造函数参数验证"""
        with self.assertRaises(ValueError):
            ConstructorArgument(-1, "value")  # 负索引


class BeanDefinitionTestCase(unittest.TestCase):
    """Bean定义测试"""

    def test_bean_definition_creation_minimal(self):
        """测试最小Bean定义创建"""
        definition = BeanDefinition("testBean", TestService)

        self.assertEqual(definition.bean_name, "testBean")
        self.assertEqual(definition.bean_class, TestService)
        self.assertEqual(definition.scope, BeanScope.SINGLETON)
        self.assertFalse(definition.lazy_init)
        self.assertFalse(definition.primary)
        self.assertEqual(len(definition.depends_on), 0)

    def test_bean_definition_creation_full(self):
        """测试完整Bean定义创建"""
        definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestService,
            scope=BeanScope.PROTOTYPE,
            factory_method_name="create",
            init_method_name="init",
            destroy_method_name="destroy",
            lazy_init=True,
            primary=True,
            depends_on=["dependency1", "dependency2"],
            description="Test bean"
        )

        self.assertEqual(definition.scope, BeanScope.PROTOTYPE)
        self.assertEqual(definition.factory_method_name, "create")
        self.assertEqual(definition.init_method_name, "init")
        self.assertEqual(definition.destroy_method_name, "destroy")
        self.assertTrue(definition.lazy_init)
        self.assertTrue(definition.primary)
        self.assertEqual(definition.depends_on, ["dependency1", "dependency2"])
        self.assertEqual(definition.description, "Test bean")

    def test_bean_definition_validation_empty_name(self):
        """测试Bean定义验证 - 空名称"""
        with self.assertRaises(ValueError) as context:
            BeanDefinition("", TestService)
        self.assertIn("Bean name cannot be empty", str(context.exception))

    def test_bean_definition_validation_none_class(self):
        """测试Bean定义验证 - None类"""
        with self.assertRaises(ValueError) as context:
            BeanDefinition("testBean", None)
        self.assertIn("Bean class cannot be None", str(context.exception))

    def test_bean_definition_validation_invalid_scope(self):
        """测试Bean定义验证 - 无效作用域"""
        with self.assertRaises(ValueError) as context:
            BeanDefinition("testBean", TestService, scope="invalid")
        self.assertIn("Scope must be a BeanScope enum", str(context.exception))

    def test_singleton_check(self):
        """测试单例检查"""
        singleton_def = BeanDefinition("test", TestService, BeanScope.SINGLETON)
        prototype_def = BeanDefinition("test", TestService, BeanScope.PROTOTYPE)

        self.assertTrue(singleton_def.singleton())
        self.assertFalse(prototype_def.singleton())

    def test_is_singleton(self):
        """测试是否为单例"""
        singleton_def = BeanDefinition("test", TestService, BeanScope.SINGLETON)
        prototype_def = BeanDefinition("test", TestService, BeanScope.PROTOTYPE)

        self.assertTrue(singleton_def.singleton())
        self.assertFalse(prototype_def.singleton())

    def test_is_prototype(self):
        """测试是否为原型"""
        singleton_def = BeanDefinition("test", TestService, BeanScope.SINGLETON)
        prototype_def = BeanDefinition("test", TestService, BeanScope.PROTOTYPE)

        self.assertFalse(singleton_def.prototype())
        self.assertTrue(prototype_def.prototype())

    def test_is_primary(self):
        """测试是否为主要Bean"""
        primary_def = BeanDefinition("test", TestService, primary=True)
        normal_def = BeanDefinition("test", TestService, primary=False)

        self.assertTrue(primary_def.is_primary())
        self.assertFalse(normal_def.is_primary())

    def test_has_dependencies(self):
        """测试是否有依赖"""
        no_deps = BeanDefinition("test", TestService)
        with_deps = BeanDefinition("test", TestService, depends_on=["dep1"])

        self.assertFalse(no_deps.has_dependencies())
        self.assertTrue(with_deps.has_dependencies())

    def test_add_dependency(self):
        """测试添加依赖"""
        definition = BeanDefinition("test", TestService)

        definition.add_dependency("dep1")
        self.assertIn("dep1", definition.depends_on)

        # 重复添加应该被忽略
        definition.add_dependency("dep1")
        self.assertEqual(definition.depends_on.count("dep1"), 1)

        # 空依赖应该被忽略
        definition.add_dependency("")
        definition.add_dependency(None)
        self.assertNotIn("", definition.depends_on)
        self.assertNotIn(None, definition.depends_on)

    def test_remove_dependency(self):
        """测试移除依赖"""
        definition = BeanDefinition("test", TestService, depends_on=["dep1", "dep2"])

        # 移除存在的依赖
        result = definition.remove_dependency("dep1")
        self.assertTrue(result)
        self.assertNotIn("dep1", definition.depends_on)
        self.assertIn("dep2", definition.depends_on)

        # 移除不存在的依赖
        result = definition.remove_dependency("dep3")
        self.assertFalse(result)

    def test_string_representation(self):
        """测试字符串表示"""
        definition = BeanDefinition("testBean", TestService, BeanScope.SINGLETON)
        str_repr = str(definition)

        self.assertIn("testBean", str_repr)
        self.assertIn("TestService", str_repr)
        self.assertIn("singleton", str_repr)


# ==================== PropertyValue高级功能测试 ====================

class PropertyValueAdvancedTestCase(unittest.TestCase):
    """PropertyValue高级功能测试"""

    def test_property_value_validation_errors(self):
        """测试属性值验证错误"""
        # 测试既没有value也没有ref的情况
        with self.assertRaises(ValueError) as context:
            PropertyValue(name="testProp")
        self.assertIn("Either value or ref must be specified", str(context.exception))

        # 测试同时有value和ref的情况
        with self.assertRaises(ValueError) as context:
            PropertyValue(name="testProp", value="testValue", ref="testRef")
        self.assertIn("Cannot specify both value and ref", str(context.exception))

    def test_property_value_is_reference(self):
        """测试属性值引用检查"""
        # 值属性
        value_prop = PropertyValue(name="valueProp", value="testValue")
        self.assertFalse(value_prop.is_reference())

        # 引用属性
        ref_prop = PropertyValue(name="refProp", ref="testRef")
        self.assertTrue(ref_prop.is_reference())

    def test_property_value_get_resolved_value(self):
        """测试获取解析后的属性值"""
        # 值属性
        value_prop = PropertyValue(name="valueProp", value="testValue")
        self.assertEqual(value_prop.get_resolved_value(), "testValue")

        # 引用属性
        ref_prop = PropertyValue(name="refProp", ref="testRef")
        self.assertEqual(ref_prop.get_resolved_value(), "testRef")


# ==================== ConstructorArgument高级功能测试 ====================

class ConstructorArgumentAdvancedTestCase(unittest.TestCase):
    """ConstructorArgument高级功能测试"""

    def test_constructor_argument_validation_errors(self):
        """测试构造函数参数验证错误"""
        # 测试既没有value也没有ref的情况
        with self.assertRaises(ValueError) as context:
            ConstructorArgument(index=0)
        self.assertIn("Either value or ref must be specified", str(context.exception))

        # 测试同时有value和ref的情况
        with self.assertRaises(ValueError) as context:
            ConstructorArgument(index=0, value="testValue", ref="testRef")
        self.assertIn("Cannot specify both value and ref", str(context.exception))

    def test_constructor_argument_is_reference(self):
        """测试构造函数参数引用检查"""
        # 值参数
        value_arg = ConstructorArgument(index=0, value="testValue")
        self.assertFalse(value_arg.is_reference())

        # 引用参数
        ref_arg = ConstructorArgument(index=1, ref="testRef")
        self.assertTrue(ref_arg.is_reference())

    def test_constructor_argument_get_resolved_value(self):
        """测试获取构造函数参数解析后的值"""
        # 值参数
        value_arg = ConstructorArgument(index=0, value="testValue")
        self.assertEqual(value_arg.get_resolved_value(), "testValue")

        # 引用参数
        ref_arg = ConstructorArgument(index=1, ref="testRef")
        self.assertEqual(ref_arg.get_resolved_value(), "testRef")


# ==================== BeanDefinition高级功能测试 ====================

class BeanDefinitionAdvancedTestCase(unittest.TestCase):
    """BeanDefinition高级功能测试"""

    def setUp(self):
        """测试前准备"""
        self.definition = BeanDefinition("testBean", TestService)

    def test_add_arg_with_sorting(self):
        """测试添加构造函数参数并排序"""
        # 添加参数（乱序）
        self.definition.add_arg(index=2, value="arg2")
        self.definition.add_arg(index=0, value="arg0")
        self.definition.add_arg(index=1, value="arg1")

        # 验证参数按索引排序
        self.assertEqual(len(self.definition.constructor_args), 3)
        self.assertEqual(self.definition.constructor_args[0].index, 0)
        self.assertEqual(self.definition.constructor_args[1].index, 1)
        self.assertEqual(self.definition.constructor_args[2].index, 2)

        # 验证链式调用
        result = self.definition.add_arg(index=3, value="arg3")
        self.assertIs(result, self.definition)

    def test_add_arg_with_ref(self):
        """测试添加引用类型构造函数参数"""
        self.definition.add_arg(index=0, ref="otherBean", type_hint=str, name="dependency")

        arg = self.definition.constructor_args[0]
        self.assertEqual(arg.index, 0)
        self.assertEqual(arg.ref, "otherBean")
        self.assertEqual(arg.type_hint, str)
        self.assertEqual(arg.name, "dependency")
        self.assertTrue(arg.is_reference())

    def test_property_method(self):
        """测试获取属性方法"""
        # 添加属性
        self.definition.add_property("prop1", value="value1")
        self.definition.add_property("prop2", ref="refBean")

        # 获取存在的属性
        prop1 = self.definition.property("prop1")
        self.assertIsNotNone(prop1)
        self.assertEqual(prop1.name, "prop1")
        self.assertEqual(prop1.value, "value1")

        # 获取不存在的属性
        prop_none = self.definition.property("nonExistent")
        self.assertIsNone(prop_none)

    def test_constructor_arg_access(self):
        """测试构造函数参数访问"""
        # 添加参数
        self.definition.add_arg(index=0, value="arg0")
        self.definition.add_arg(index=1, value="arg1")

        # 验证参数存在
        self.assertEqual(len(self.definition.constructor_args), 2)

        # 验证参数内容
        arg0 = self.definition.constructor_args[0]
        self.assertEqual(arg0.index, 0)
        self.assertEqual(arg0.value, "arg0")

        arg1 = self.definition.constructor_args[1]
        self.assertEqual(arg1.index, 1)
        self.assertEqual(arg1.value, "arg1")

    def test_bean_definition_repr(self):
        """测试Bean定义详细字符串表示"""
        definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestService,
            scope=BeanScope.SINGLETON,
            lazy_init=True,
            primary=True
        )

        repr_str = repr(definition)

        self.assertIn("BeanDefinition", repr_str)
        self.assertIn("testBean", repr_str)
        self.assertIn("TestService", repr_str)
        self.assertIn("singleton", repr_str)  # 小写形式
        self.assertIn("lazy_init=True", repr_str)
        self.assertIn("primary=True", repr_str)

    def test_bean_definition_complex_configuration(self):
        """测试复杂Bean定义配置"""
        definition = BeanDefinition(
            bean_name="complexBean",
            bean_class=TestService,
            scope=BeanScope.PROTOTYPE,
            factory_method_name="createInstance",
            factory_bean_name="factoryBean",
            init_method_name="init",
            destroy_method_name="destroy",
            lazy_init=True,
            primary=True,
            depends_on=["dep1", "dep2"],
            description="Complex bean for testing",
            source="test_definition.py"
        )

        # 验证所有属性
        self.assertEqual(definition.bean_name, "complexBean")
        self.assertEqual(definition.bean_class, TestService)
        self.assertEqual(definition.scope, BeanScope.PROTOTYPE)
        self.assertEqual(definition.factory_method_name, "createInstance")
        self.assertEqual(definition.factory_bean_name, "factoryBean")
        self.assertEqual(definition.init_method_name, "init")
        self.assertEqual(definition.destroy_method_name, "destroy")
        self.assertTrue(definition.lazy_init)
        self.assertTrue(definition.primary)
        self.assertEqual(definition.depends_on, ["dep1", "dep2"])
        self.assertEqual(definition.description, "Complex bean for testing")
        self.assertEqual(definition.source, "test_definition.py")

        # 验证便利方法
        self.assertFalse(definition.singleton())  # PROTOTYPE
        self.assertTrue(definition.lazy_init)  # 直接访问属性
        self.assertTrue(definition.is_primary())
        self.assertTrue(definition.has_dependencies())


if __name__ == '__main__':
    unittest.main()
