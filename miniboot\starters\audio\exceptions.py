#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频播放相关异常定义
"""


class AudioException(Exception):
    """音频相关异常基类"""

    def __init__(self, message: str, cause: Exception = None):
        super().__init__(message)
        self.cause = cause


class AudioPlayerException(AudioException):
    """音频播放异常"""

    pass


class TTSException(AudioException):
    """TTS异常"""

    pass


class AudioFileException(AudioException):
    """音频文件异常"""

    pass


class AudioConfigurationException(AudioException):
    """音频配置异常"""

    pass
