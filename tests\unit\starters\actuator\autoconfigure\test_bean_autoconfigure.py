#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Bean autoconfigure simple unit tests - basic testing for Bean module auto-configuration
"""

import unittest
from unittest.mock import MagicMock, patch

from miniboot.starters.actuator.autoconfigure.bean import (BeanMetrics,
                                                            BeanMetricsAutoConfiguration,
                                                            BeanMetricsCollector)


class BeanMetricsTestCase(unittest.TestCase):
    """Bean metrics data class unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.metrics = BeanMetrics()

    def test_metrics_initialization(self) -> None:
        """Test Bean metrics initialization"""
        self.assertEqual(self.metrics.total_beans_created, 0)
        self.assertEqual(self.metrics.total_creation_time, 0.0)
        self.assertEqual(self.metrics.avg_creation_time, 0.0)
        self.assertEqual(self.metrics.cache_hit_rate, 0.0)

    def test_calculate_derived_metrics_with_data(self) -> None:
        """Test calculating derived metrics with actual data"""
        # Set up test data
        self.metrics.total_beans_created = 10
        self.metrics.total_creation_time = 5.0
        self.metrics.cache_hits_level1 = 8
        self.metrics.cache_hits_level2 = 2
        self.metrics.cache_hits_level3 = 1
        self.metrics.cache_misses = 1
        
        # Calculate derived metrics
        self.metrics.calculate_derived_metrics()
        
        # Verify calculations
        self.assertEqual(self.metrics.avg_creation_time, 0.5)  # 5.0 / 10
        self.assertAlmostEqual(self.metrics.cache_hit_rate, 0.917, places=2)  # 11 / 12

    def test_calculate_derived_metrics_with_zero_data(self) -> None:
        """Test calculating derived metrics with zero data"""
        # All values are zero by default
        self.metrics.calculate_derived_metrics()
        
        # Should not raise division by zero errors
        self.assertEqual(self.metrics.avg_creation_time, 0.0)
        self.assertEqual(self.metrics.cache_hit_rate, 0.0)


class BeanMetricsCollectorTestCase(unittest.TestCase):
    """Bean metrics collector unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.collector = BeanMetricsCollector()

    def test_collector_initialization(self) -> None:
        """Test Bean metrics collector initialization"""
        self.assertIsInstance(self.collector._metrics, BeanMetrics)
        self.assertEqual(len(self.collector._monitored_factories), 0)
        self.assertEqual(len(self.collector._monitored_caches), 0)
        self.assertEqual(len(self.collector._monitored_registries), 0)

    def test_get_collector_name(self) -> None:
        """Test getting collector name"""
        name = self.collector.get_collector_name()
        self.assertEqual(name, "bean-metrics-collector")

    def test_get_supported_metrics(self) -> None:
        """Test getting supported metrics list"""
        metrics = self.collector.get_supported_metrics()
        
        self.assertIsInstance(metrics, list)
        self.assertIn("bean.total_created", metrics)
        self.assertIn("bean.avg_creation_time", metrics)
        self.assertIn("bean.cache_hit_rate", metrics)

    def test_is_available(self) -> None:
        """Test checking collector availability"""
        # Bean metrics collector should always be available
        self.assertTrue(self.collector.is_available())

    def test_collect_metrics_basic(self) -> None:
        """Test basic metrics collection"""
        # Should not raise exception even with no registered components
        metrics_data = self.collector.collect_metrics()
        
        # Should return a list of MetricsData
        self.assertIsInstance(metrics_data, list)

    def test_reset_metrics(self) -> None:
        """Test resetting metrics"""
        # Set some initial data
        self.collector._metrics.total_beans_created = 10
        self.collector._metrics.total_creation_time = 5.0
        
        # Reset metrics
        self.collector.reset_metrics()
        
        # Verify reset
        self.assertEqual(self.collector._metrics.total_beans_created, 0)
        self.assertEqual(self.collector._metrics.total_creation_time, 0.0)


class BeanMetricsAutoConfigurationTestCase(unittest.TestCase):
    """Bean metrics auto-configuration unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.config = BeanMetricsAutoConfiguration()

    def test_configuration_initialization(self) -> None:
        """Test auto-configuration initialization"""
        self.assertIsInstance(self.config, BeanMetricsAutoConfiguration)

    def test_get_metadata(self) -> None:
        """Test getting configuration metadata"""
        metadata = self.config.get_metadata()
        
        self.assertEqual(metadata.name, "bean-metrics-auto-configuration")
        self.assertIn("Bean 模块指标采集自动配置", metadata.description)
        self.assertEqual(metadata.priority, 200)
        self.assertIn("actuator-starter-auto-configuration", metadata.auto_configure_after)

    def test_bean_metrics_collector_creation(self) -> None:
        """Test Bean metrics collector Bean creation"""
        collector = self.config.bean_metrics_collector()
        
        self.assertIsInstance(collector, BeanMetricsCollector)
        self.assertEqual(collector.get_collector_name(), "bean-metrics-collector")
        self.assertTrue(collector.is_available())

    @patch('miniboot.starters.actuator.autoconfigure.bean.logger')
    def test_bean_creation_logging(self, mock_logger) -> None:
        """Test logging during Bean creation"""
        self.config.bean_metrics_collector()
        
        # Verify debug log was called
        mock_logger.debug.assert_called_with("Created BeanMetricsCollector bean")


if __name__ == "__main__":
    unittest.main()
