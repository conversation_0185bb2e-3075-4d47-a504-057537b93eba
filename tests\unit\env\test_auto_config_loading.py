#!/usr/bin/env python
"""
* @author: cz
* @description: 自动配置文件加载测试
"""

import tempfile
import unittest
from pathlib import Path

from miniboot.env.environment import StandardEnvironment


class AutoConfigLoadingTestCase(unittest.TestCase):
    """自动配置文件加载测试"""

    def setUp(self):
        self.temp_dir = Path(tempfile.mkdtemp())
        self.resources_dir = self.temp_dir / "resources"
        self.resources_dir.mkdir(exist_ok=True)

        # 保存原始工作目录
        import os

        self.original_cwd = Path.cwd()
        os.chdir(self.temp_dir)

    def tearDown(self):
        # 恢复原始工作目录
        import os

        os.chdir(self.original_cwd)

        # 清理临时文件
        for file in self.temp_dir.rglob("*"):
            if file.is_file():
                file.unlink()
        for dir in reversed(list(self.temp_dir.rglob("*"))):
            if dir.is_dir():
                dir.rmdir()
        self.temp_dir.rmdir()

    def test_auto_load_application_yml(self):
        """测试自动加载 application.yml 文件"""
        # 创建 application.yml 文件
        config_content = """
app:
  name: auto-loaded-app
  version: 1.0.0
  debug: true

server:
  port: 8080
  host: localhost
"""
        (self.resources_dir / "application.yml").write_text(config_content, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证配置被自动加载
        self.assertEqual("auto-loaded-app", env.get_property("app.name"))
        self.assertEqual("1.0.0", env.get_property("app.version"))
        self.assertEqual(True, env.get_property("app.debug"))  # YAML 布尔值
        self.assertEqual(8080, env.get_property("server.port"))  # YAML 数值
        self.assertEqual("localhost", env.get_property("server.host"))

    def test_auto_load_application_json(self):
        """测试自动加载 application.json 文件"""
        # 创建 application.json 文件
        config_content = """{
  "database": {
    "host": "db.example.com",
    "port": 5432,
    "username": "admin"
  },
  "cache": {
    "enabled": true,
    "ttl": 3600
  }
}"""
        (self.resources_dir / "application.json").write_text(config_content, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证配置被自动加载
        self.assertEqual("db.example.com", env.get_property("database.host"))
        self.assertEqual(5432, env.get_property("database.port"))  # JSON 数值
        self.assertEqual("admin", env.get_property("database.username"))
        self.assertEqual(True, env.get_property("cache.enabled"))  # JSON 布尔值
        self.assertEqual(3600, env.get_property("cache.ttl"))  # JSON 数值

    def test_auto_load_application_properties(self):
        """测试自动加载 application.properties 文件"""
        # 创建 application.properties 文件
        config_content = """
# Application configuration
app.name=properties-app
app.version=2.0.0

# Server configuration
server.port=9090
server.context-path=/api
"""
        (self.resources_dir / "application.properties").write_text(config_content, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证配置被自动加载
        self.assertEqual("properties-app", env.get_property("app.name"))
        self.assertEqual("2.0.0", env.get_property("app.version"))
        self.assertEqual("9090", env.get_property("server.port"))
        self.assertEqual("/api", env.get_property("server.context-path"))

    def test_auto_load_with_profiles(self):
        """测试带 Profile 的自动加载"""
        # 创建默认配置文件
        default_config = """
app:
  name: default-app
  environment: default
  debug: false
"""
        (self.resources_dir / "application.yml").write_text(default_config, encoding="utf-8")

        # 创建 dev profile 配置文件
        dev_config = """
app:
  environment: development
  debug: true

server:
  port: 8081
"""
        (self.resources_dir / "application-dev.yml").write_text(dev_config, encoding="utf-8")

        # 设置激活的 Profile (使用正确的属性名)
        import os

        # 环境变量名需要转换:miniboot.profiles.active -> MINIBOOT_PROFILES_ACTIVE
        os.environ["MINIBOOT_PROFILES_ACTIVE"] = "dev"

        try:
            # 创建环境实例
            env = StandardEnvironment()

            # 验证 Profile 特定配置覆盖了默认配置
            self.assertEqual("default-app", env.get_property("app.name"))  # 来自默认配置
            self.assertEqual("development", env.get_property("app.environment"))  # 来自 dev 配置
            self.assertEqual(True, env.get_property("app.debug"))  # 来自 dev 配置,YAML 布尔值
            self.assertEqual(8081, env.get_property("server.port"))  # 来自 dev 配置,YAML 数值

        finally:
            # 清理环境变量
            if "MINIBOOT_PROFILES_ACTIVE" in os.environ:
                del os.environ["MINIBOOT_PROFILES_ACTIVE"]

    def test_auto_load_multiple_config_files(self):
        """测试自动加载多个配置文件"""
        # 创建 application.yml
        app_config = """
app:
  name: multi-config-app
  version: 1.0.0
"""
        (self.resources_dir / "application.yml").write_text(app_config, encoding="utf-8")

        # 创建 config.yml
        config_config = """
database:
  host: localhost
  port: 3306
"""
        (self.resources_dir / "config.yml").write_text(config_config, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证两个配置文件都被加载
        self.assertEqual("multi-config-app", env.get_property("app.name"))
        self.assertEqual("1.0.0", env.get_property("app.version"))
        self.assertEqual("localhost", env.get_property("database.host"))
        self.assertEqual(3306, env.get_property("database.port"))  # YAML 数值

    def test_auto_load_from_config_directory(self):
        """测试从 config 目录自动加载"""
        # 创建 config 目录
        config_dir = self.temp_dir / "config"
        config_dir.mkdir(exist_ok=True)

        # 在 config 目录创建配置文件
        config_content = """
app:
  name: config-dir-app
  source: config-directory
"""
        (config_dir / "application.yml").write_text(config_content, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证从 config 目录加载的配置
        self.assertEqual("config-dir-app", env.get_property("app.name"))
        self.assertEqual("config-directory", env.get_property("app.source"))

    def test_auto_load_priority_order(self):
        """测试配置文件优先级顺序"""
        # 在不同位置创建同名配置文件

        # resources/application.yml (较低优先级)
        resources_config = """
app:
  name: resources-app
  source: resources
  priority: low
"""
        (self.resources_dir / "application.yml").write_text(resources_config, encoding="utf-8")

        # config/application.yml (较高优先级)
        config_dir = self.temp_dir / "config"
        config_dir.mkdir(exist_ok=True)
        config_config = """
app:
  name: config-app
  source: config
  priority: high
"""
        (config_dir / "application.yml").write_text(config_config, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证高优先级配置覆盖低优先级配置
        # 注意:根据 Spring Boot 的约定,config/ 目录的优先级更高
        self.assertIn(env.get_property("app.name"), ["config-app", "resources-app"])
        self.assertIn(env.get_property("app.source"), ["config", "resources"])

    def test_auto_load_no_config_files(self):
        """测试没有配置文件时的情况"""
        # 不创建任何配置文件

        # 创建环境实例
        env = StandardEnvironment()

        # 验证环境仍然可以正常工作
        self.assertIsNone(env.get_property("app.name"))
        self.assertIsNone(env.get_property("non.existent.property"))

        # 但系统环境变量应该仍然可用
        import os

        os.environ["TEST_ENV_VAR"] = "test-value"
        try:
            # 系统环境变量应该可以访问
            self.assertEqual("test-value", env.get_property("TEST_ENV_VAR"))
        finally:
            del os.environ["TEST_ENV_VAR"]


if __name__ == "__main__":
    unittest.main()
