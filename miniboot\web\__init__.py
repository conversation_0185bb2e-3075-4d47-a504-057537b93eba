#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 框架集成模块 - 提供 Mini-Boot 框架与 FastAPI 的集成功能

Web 框架集成模块

提供 Mini-Boot 框架与 FastAPI 的集成功能.

主要功能:
- FastAPI 集成 - 与 FastAPI 框架的集成和自动配置
- 路由注解处理 (@RestController, @RequestMapping 等)
- 中间件支持 - 请求拦截器和中间件机制
- 全局异常处理 - 统一异常处理和错误响应
- 参数绑定 - 请求参数到方法参数的自动绑定
"""

# 从annotations模块导入Web装饰器
from ..annotations.web import (
    Controller,
    DeleteMapping,
    GetMapping,
    PatchMapping,
    PostMapping,
    PutMapping,
    RequestMapping,
    RestController,
    controller_path,
    has_route,
    is_controller,
    is_rest_controller,
    route_info,
)

# 核心组件导出
from .application import WebApplication, WebApplicationState
from .middleware import (
    BaseMiddleware,
    CompressionMiddleware,
    CorsMiddleware,
    CustomMiddleware,
    LoggingMiddleware,
    MiddlewareRegistry,
    ResponseMiddleware,
)
from .params import ParameterBinder
from .properties import CompressionConfig, CorsConfig, DocsConfig, LoggingConfig, StaticConfig, WebProperties
from .response import ApiResponse, BusinessError, ValidationError
from .router import ControllerInfo, ControllerRegistry, RouteInfo

# Web作用域处理器
from .scope_processor import (
    WebScopeBeanPostProcessor,
    WebScopedBeanFactory,
    has_web_scope,
)

# Web作用域管理
from .scopes import (
    ApplicationScopeManager,
    RequestScopeManager,
    SessionScopeManager,
    WebBeanScope,
    WebScopeContext,
    WebScopeManager,
    WebScopeRegistry,
    WebSocketScopeManager,
)


# 版本信息
__version__ = "0.0.1"

# 公开API
__all__ = [
    # 核心类
    "WebApplication",
    "WebApplicationState",
    "ControllerRegistry",
    "ControllerInfo",
    "RouteInfo",
    "ParameterBinder",
    # 中间件类
    "BaseMiddleware",
    "MiddlewareRegistry",
    "ResponseMiddleware",
    "CorsMiddleware",
    "CompressionMiddleware",
    "LoggingMiddleware",
    "CustomMiddleware",
    # 配置类
    "WebProperties",
    "CorsConfig",
    "CompressionConfig",
    "LoggingConfig",
    "DocsConfig",
    "StaticConfig",
    # 响应和异常
    "ApiResponse",
    "BusinessError",
    "ValidationError",
    # 注解装饰器
    "Controller",
    "RestController",
    "RequestMapping",
    "GetMapping",
    "PostMapping",
    "PutMapping",
    "DeleteMapping",
    "PatchMapping",
    # 工具函数
    "is_controller",
    "is_rest_controller",
    "controller_path",
    "has_route",
    "route_info",
    # Web作用域管理
    "WebBeanScope",
    "WebScopeContext",
    "WebScopeManager",
    "WebScopeRegistry",
    "RequestScopeManager",
    "SessionScopeManager",
    "ApplicationScopeManager",
    "WebSocketScopeManager",
    # Web作用域处理器
    "WebScopeBeanPostProcessor",
    "WebScopedBeanFactory",
    "has_web_scope",
    # 版本信息
    "__version__",
]
