#!/usr/bin/env python
"""
* @author: cz
* @description: 条件装配注解实现

实现Mini-Boot框架的条件装配注解,包括@Conditional、@ConditionalOnProperty等.
这些注解用于根据特定条件决定是否创建Bean,实现智能的自动配置功能.
"""

from typing import Callable, Optional, Union

from .metadata import ConditionalMetadata


def Conditional(
    condition_class: type,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基础条件装配注解装饰器

    根据指定的条件类决定是否创建Bean.条件类需要实现matches方法.

    Args:
        condition_class: 条件类,需要实现matches(context)方法

    Returns:
        装饰器函数

    Examples:
        class MyCondition:
            def matches(self, context):
                return True

        @Conditional(MyCondition)
        class ConditionalBean:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 创建条件元数据
        metadata = ConditionalMetadata(condition_type="Custom", class_names=[condition_class.__name__])

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_class__ = condition_class

        return target

    return decorator


def ConditionalOnProperty(  # noqa: N802
    name: str = None,
    *,
    having_value: Optional[str] = None,
    match_if_missing: bool = False,
    prefix: Optional[str] = None,
    value: Optional[str] = None,
    names: Optional[list[str]] = None,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基于配置属性的条件装配注解装饰器

    根据配置属性的值决定是否创建Bean.支持多种配置方式.

    Args:
        name: 配置属性名称(单个属性)
        having_value: 期望的属性值,如果为None则只检查属性是否存在
        match_if_missing: 当属性不存在时是否匹配,默认为False
        prefix: 属性前缀,用于批量检查以该前缀开头的属性
        value: having_value的别名,用于兼容Spring Boot风格
        names: 多个属性名称列表,支持检查多个属性

    Returns:
        装饰器函数

    Examples:
        # 单个属性检查
        @ConditionalOnProperty(name="app.feature.enabled", having_value="true")
        class FeatureBean:
            pass

        # 使用value参数(Spring Boot风格)
        @ConditionalOnProperty(name="app.debug", value="true")
        class DebugBean:
            pass

        # 多个属性检查
        @ConditionalOnProperty(names=["app.feature1.enabled", "app.feature2.enabled"])
        class MultiFeatureBean:
            pass

        # 前缀检查
        @ConditionalOnProperty(prefix="app.database")
        class DatabaseBean:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 处理参数兼容性
        actual_having_value = having_value or value

        # 确定要检查的属性列表
        property_names = []
        if names:
            property_names.extend(names)
        elif name:
            property_names.append(name)

        # 创建条件元数据
        metadata = ConditionalMetadata(
            condition_type="OnProperty", properties=property_names, having_value=actual_having_value, match_if_missing=match_if_missing, prefix=prefix
        )

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_property__ = name
        target.__condition_having_value__ = actual_having_value
        target.__condition_match_if_missing__ = match_if_missing
        target.__condition_prefix__ = prefix
        target.__condition_names__ = names

        return target

    return decorator


def ConditionalOnBean(  # noqa: N802
    bean_type: Optional[type] = None, *, name: Optional[str] = None
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基于Bean存在的条件装配注解装饰器

    当指定的Bean存在时才创建当前Bean.

    Args:
        bean_type: Bean的类型
        name: Bean的名称

    Returns:
        装饰器函数

    Examples:
        @ConditionalOnBean(DataSource)
        class DatabaseService:
            pass

        @ConditionalOnBean(name="primaryDataSource")
        class PrimaryDatabaseService:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 创建条件元数据
        bean_names = [name] if name else []
        class_names = [bean_type.__name__] if bean_type else []

        metadata = ConditionalMetadata(condition_type="OnBean", bean_names=bean_names, class_names=class_names)

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_bean_type__ = bean_type
        target.__condition_bean_name__ = name

        return target

    return decorator


def ConditionalOnMissingBean(  # noqa: N802
    bean_type: Optional[type] = None, *, name: Optional[str] = None
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基于Bean不存在的条件装配注解装饰器

    当指定的Bean不存在时才创建当前Bean.

    Args:
        bean_type: Bean的类型
        name: Bean的名称

    Returns:
        装饰器函数

    Examples:
        @ConditionalOnMissingBean(DataSource)
        class DefaultDataSource:
            pass

        @ConditionalOnMissingBean(name="customService")
        class DefaultService:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 创建条件元数据
        bean_names = [name] if name else []
        class_names = [bean_type.__name__] if bean_type else []

        metadata = ConditionalMetadata(condition_type="OnMissingBean", bean_names=bean_names, class_names=class_names)

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_bean_type__ = bean_type
        target.__condition_bean_name__ = name

        return target

    return decorator


def ConditionalOnClass(  # noqa: N802
    class_name: str = None, *, name: Optional[str] = None, names: Optional[list[str]] = None
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基于类存在的条件装配注解装饰器

    当指定的类在classpath中存在时才创建Bean.支持检查单个或多个类.

    Args:
        class_name: 类的完整名称(单个类)
        name: class_name的别名,用于兼容性
        names: 多个类名称列表,支持检查多个类

    Returns:
        装饰器函数

    Examples:
        # 单个类检查
        @ConditionalOnClass("redis.Redis")
        class RedisService:
            pass

        # 使用name参数
        @ConditionalOnClass(name="redis.Redis")
        class RedisService:
            pass

        # 多个类检查
        @ConditionalOnClass(names=["redis.Redis", "redis.ConnectionPool"])
        class RedisService:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 确定要检查的类名列表
        class_names = []
        if names:
            class_names.extend(names)
        elif class_name:
            class_names.append(class_name)
        elif name:
            class_names.append(name)

        # 创建条件元数据
        metadata = ConditionalMetadata(condition_type="OnClass", class_names=class_names)

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_class_name__ = class_name or name
        target.__condition_class_names__ = class_names

        return target

    return decorator


def ConditionalOnMissingClass(  # noqa: N802
    class_name: str = None, *, name: Optional[str] = None, names: Optional[list[str]] = None
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基于类不存在的条件装配注解装饰器

    当指定的类在classpath中不存在时才创建Bean.支持检查单个或多个类.

    Args:
        class_name: 类的完整名称(单个类)
        name: class_name的别名,用于兼容性
        names: 多个类名称列表,支持检查多个类

    Returns:
        装饰器函数

    Examples:
        # 单个类检查
        @ConditionalOnMissingClass("redis.Redis")
        class MockRedisService:
            pass

        # 多个类检查
        @ConditionalOnMissingClass(names=["redis.Redis", "redis.ConnectionPool"])
        class MockRedisService:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 确定要检查的类名列表
        class_names = []
        if names:
            class_names.extend(names)
        elif class_name:
            class_names.append(class_name)
        elif name:
            class_names.append(name)

        # 创建条件元数据
        metadata = ConditionalMetadata(condition_type="OnMissingClass", class_names=class_names)

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_class_name__ = class_name or name
        target.__condition_class_names__ = class_names

        return target

    return decorator


def ConditionalOnWeb(  # noqa: N802
    target: Optional[Union[type, Callable]] = None,
) -> Union[Union[type, Callable], Callable[[Union[type, Callable]], Union[type, Callable]]]:
    """基于Web环境的条件装配注解装饰器

    当应用运行在Web环境中时才创建Bean.

    Args:
        target: 被装饰的类或方法

    Returns:
        装饰后的类/方法或装饰器函数

    Examples:
        @ConditionalOnWeb
        class WebController:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 创建条件元数据
        metadata = ConditionalMetadata(condition_type="OnWeb")

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_web__ = True

        return target

    # 支持无参数调用
    if target is not None:
        return decorator(target)

    return decorator


def ConditionalOnExpression(  # noqa: N802
    expression: str,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基于表达式的条件装配注解装饰器

    根据表达式的计算结果决定是否创建Bean.表达式支持访问配置属性和环境变量.

    Args:
        expression: 条件表达式,支持Python表达式语法

    Returns:
        装饰器函数

    Examples:
        # 简单表达式
        @ConditionalOnExpression("'${app.env}' == 'development'")
        class DevBean:
            pass

        # 复杂表达式
        @ConditionalOnExpression("'${app.debug}' == 'true' and '${app.env}' != 'production'")
        class DebugBean:
            pass

        # 数值比较
        @ConditionalOnExpression("int('${app.max_connections:100}') > 50")
        class HighConnectionBean:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 创建条件元数据
        metadata = ConditionalMetadata(condition_type="OnExpression", expression=expression)

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_expression__ = expression

        return target

    return decorator


def ConditionalOnResource(  # noqa: N802
    resources: Union[str, list[str]],
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """基于资源存在的条件装配注解装饰器

    当指定的资源文件存在时才创建Bean.

    Args:
        resources: 资源路径或资源路径列表

    Returns:
        装饰器函数

    Examples:
        @ConditionalOnResource("config/database.yml")
        class DatabaseBean:
            pass

        @ConditionalOnResource(["config/redis.yml", "config/cache.yml"])
        class CacheBean:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        resource_list = resources if isinstance(resources, list) else [resources]

        # 创建条件元数据
        metadata = ConditionalMetadata(condition_type="OnResource", resources=resource_list)

        # 存储元数据
        target.__conditional_metadata__ = metadata
        target.__is_conditional__ = True
        target.__condition_resources__ = resource_list

        return target

    return decorator


# 工具函数
def is_conditional(target: Union[type, Callable]) -> bool:
    """检查类或方法是否有条件装配注解

    Args:
        target: 要检查的类或方法

    Returns:
        如果有条件装配注解返回True,否则返回False
    """
    return hasattr(target, "__is_conditional__") and target.__is_conditional__


def get_conditional_metadata(target: Union[type, Callable]) -> Optional[ConditionalMetadata]:
    """获取条件装配元数据

    Args:
        target: 要获取元数据的类或方法

    Returns:
        条件装配元数据,如果没有则返回None
    """
    return getattr(target, "__conditional_metadata__", None)


def get_condition_type(target: Union[type, Callable]) -> Optional[str]:
    """获取条件类型

    Args:
        target: 要获取条件类型的类或方法

    Returns:
        条件类型字符串,如果没有则返回None
    """
    metadata = get_conditional_metadata(target)
    return metadata.condition_type if metadata else None

def AutoConfigureAfter(
    *config_classes: type,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """自动配置顺序注解 - 在指定配置类之后执行

    用于控制自动配置类的执行顺序,确保依赖的配置类先执行.

    Args:
        *config_classes: 依赖的配置类列表

    Returns:
        装饰器函数

    Examples:
        @AutoConfigureAfter(ActuatorAutoConfiguration)
        class WebActuatorAutoConfiguration:
            pass

        @AutoConfigureAfter(DataSourceAutoConfiguration, JpaAutoConfiguration)
        class MyDatabaseAutoConfiguration:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 存储配置顺序信息
        target.__auto_configure_after__ = config_classes
        target.__has_auto_configure_order__ = True

        return target

    return decorator


def AutoConfigureBefore(
    *config_classes: type,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """自动配置顺序注解 - 在指定配置类之前执行

    用于控制自动配置类的执行顺序,确保当前配置类在指定类之前执行.

    Args:
        *config_classes: 要在其之前执行的配置类列表

    Returns:
        装饰器函数

    Examples:
        @AutoConfigureBefore(WebActuatorAutoConfiguration)
        class ActuatorAutoConfiguration:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 存储配置顺序信息
        target.__auto_configure_before__ = config_classes
        target.__has_auto_configure_order__ = True

        return target

    return decorator


def AutoConfigureOrder(  # noqa: N802
    order: int,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """自动配置顺序注解 - 指定执行优先级

    用于指定自动配置类的执行优先级,数值越小优先级越高.

    Args:
        order: 执行优先级,数值越小优先级越高

    Returns:
        装饰器函数

    Examples:
        @AutoConfigureOrder(100)
        class HighPriorityAutoConfiguration:
            pass

        @AutoConfigureOrder(1000)
        class LowPriorityAutoConfiguration:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 存储配置顺序信息
        target.__auto_configure_order__ = order
        target.__has_auto_configure_order__ = True

        return target

    return decorator

def Configuration(
    target: Optional[Union[type, Callable]] = None,
) -> Union[Union[type, Callable], Callable[[Union[type, Callable]], Union[type, Callable]]]:
    """配置类标识注解

    标识一个类为配置类,用于自动配置系统识别.

    Args:
        target: 被装饰的类

    Returns:
        装饰后的类或装饰器函数

    Examples:
        @Configuration
        class MyAutoConfiguration:
            pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 标识为配置类
        target.__is_configuration__ = True
        target.__configuration_type__ = "AutoConfiguration"

        return target

    # 支持无参数调用
    if target is not None:
        return decorator(target)

    return decorator


def is_auto_configuration(target: Union[type, Callable]) -> bool:
    """检查类是否为自动配置类

    Args:
        target: 要检查的类

    Returns:
        如果是自动配置类返回True,否则返回False
    """
    return hasattr(target, "__is_configuration__") and target.__is_configuration__


def has_auto_configure_order(target: Union[type, Callable]) -> bool:
    """检查类是否有自动配置顺序信息

    Args:
        target: 要检查的类

    Returns:
        如果有顺序信息返回True,否则返回False
    """
    return hasattr(target, "__has_auto_configure_order__") and target.__has_auto_configure_order__


def get_auto_configure_after(target: Union[type, Callable]) -> tuple[type, ...]:
    """获取自动配置依赖的后置类列表

    Args:
        target: 要获取信息的类

    Returns:
        依赖的后置类列表
    """
    return getattr(target, "__auto_configure_after__", ())


def get_auto_configure_before(target: Union[type, Callable]) -> tuple[type, ...]:
    """获取自动配置的前置类列表

    Args:
        target: 要获取信息的类

    Returns:
        前置类列表
    """
    return getattr(target, "__auto_configure_before__", ())


def get_auto_configure_order(target: Union[type, Callable]) -> int:
    """获取自动配置的执行优先级

    Args:
        target: 要获取信息的类

    Returns:
        执行优先级,默认为1000
    """
    return getattr(target, "__auto_configure_order__", 1000)


def is_bean_factory_method(method: Callable) -> bool:
    """检查方法是否为Bean工厂方法

    Args:
        method: 要检查的方法

    Returns:
        如果是Bean工厂方法返回True,否则返回False
    """
    # 使用现有的 __is_bean__ 属性
    return hasattr(method, "__is_bean__") and method.__is_bean__


def get_bean_name(method: Callable) -> Optional[str]:
    """获取Bean工厂方法定义的Bean名称

    Args:
        method: Bean工厂方法

    Returns:
        Bean名称,如果不是Bean工厂方法返回None
    """
    # 使用现有的 __bean_name__ 属性
    return getattr(method, "__bean_name__", None)
