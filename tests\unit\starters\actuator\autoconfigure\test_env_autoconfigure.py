#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Environment autoconfigure simple unit tests - basic testing for Environment module auto-configuration
"""

import unittest
from unittest.mock import MagicMock, patch

from miniboot.starters.actuator.autoconfigure.env import (
    EnvMetrics, EnvMetricsAutoConfiguration, EnvMetricsCollector)


class EnvMetricsTestCase(unittest.TestCase):
    """Environment metrics data class unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.metrics = EnvMetrics()

    def test_metrics_initialization(self) -> None:
        """Test Environment metrics initialization"""
        self.assertEqual(self.metrics.total_property_sources, 0)
        self.assertEqual(len(self.metrics.active_profiles), 0)
        self.assertEqual(self.metrics.total_config_files, 0)
        self.assertEqual(self.metrics.environment_variables_count, 0)

    def test_calculate_derived_metrics_with_data(self) -> None:
        """Test calculating derived metrics with actual data"""
        # Set up test data
        self.metrics.total_property_sources = 5
        self.metrics.active_profiles = ["dev", "test"]
        self.metrics.total_config_files = 3
        self.metrics.environment_variables_count = 100

        # Calculate derived metrics
        self.metrics.calculate_derived_metrics()

        # Verify calculations exist (actual calculation logic may vary)
        self.assertIsNotNone(self.metrics)

    def test_calculate_derived_metrics_with_zero_data(self) -> None:
        """Test calculating derived metrics with zero data"""
        # All values are zero by default
        self.metrics.calculate_derived_metrics()

        # Should not raise division by zero errors
        self.assertIsNotNone(self.metrics)


class EnvMetricsCollectorTestCase(unittest.TestCase):
    """Environment metrics collector unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.collector = EnvMetricsCollector()

    def test_collector_initialization(self) -> None:
        """Test Environment metrics collector initialization"""
        self.assertIsInstance(self.collector._metrics, EnvMetrics)

    def test_get_collector_name(self) -> None:
        """Test getting collector name"""
        name = self.collector.get_collector_name()
        self.assertEqual(name, "EnvMetricsCollector")

    def test_get_supported_metrics(self) -> None:
        """Test getting supported metrics list"""
        metrics = self.collector.get_supported_metrics()

        self.assertIsInstance(metrics, list)
        self.assertGreater(len(metrics), 0)

    def test_is_available(self) -> None:
        """Test checking collector availability"""
        # Environment metrics collector should always be available
        self.assertTrue(self.collector.is_available())

    def test_collect_metrics_basic(self) -> None:
        """Test basic metrics collection"""
        # Should not raise exception even with no registered components
        metrics_data = self.collector.collect_metrics()

        # Should return a list of MetricsData
        self.assertIsInstance(metrics_data, list)

    def test_reset_metrics(self) -> None:
        """Test resetting metrics"""
        # Set some initial data
        self.collector._metrics.total_property_sources = 5
        self.collector._metrics.active_profiles = ["dev", "test"]

        # Reset metrics
        self.collector.reset_metrics()

        # Verify reset
        self.assertEqual(self.collector._metrics.total_property_sources, 0)
        self.assertEqual(len(self.collector._metrics.active_profiles), 0)


class EnvMetricsAutoConfigurationTestCase(unittest.TestCase):
    """Environment metrics auto-configuration unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.config = EnvMetricsAutoConfiguration()

    def test_configuration_initialization(self) -> None:
        """Test auto-configuration initialization"""
        self.assertIsInstance(self.config, EnvMetricsAutoConfiguration)

    def test_get_metadata(self) -> None:
        """Test getting configuration metadata"""
        metadata = self.config.get_metadata()

        self.assertEqual(metadata.name, "env-metrics-auto-configuration")
        self.assertIn("环境配置模块指标采集自动配置", metadata.description)
        self.assertEqual(metadata.priority, 200)
        self.assertIn("actuator-starter-auto-configuration", metadata.auto_configure_after)

    def test_env_metrics_collector_creation(self) -> None:
        """Test Environment metrics collector Bean creation"""
        collector = self.config.env_metrics_collector()

        self.assertIsInstance(collector, EnvMetricsCollector)
        self.assertEqual(collector.get_collector_name(), "EnvMetricsCollector")
        self.assertTrue(collector.is_available())

    @patch('miniboot.starters.actuator.autoconfigure.env.logger')
    def test_bean_creation_logging(self, mock_logger) -> None:
        """Test logging during Bean creation"""
        self.config.env_metrics_collector()

        # Verify debug log was called
        mock_logger.debug.assert_called_with("Created EnvMetricsCollector bean")


if __name__ == "__main__":
    unittest.main()
