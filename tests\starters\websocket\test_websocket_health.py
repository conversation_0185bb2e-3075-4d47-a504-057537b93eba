#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 健康检查测试
"""

import unittest
from unittest.mock import MagicMock

from miniboot.starters.websocket.health import WebSocketHealthIndicator, HealthStatus, HealthDetail
from miniboot.starters.websocket.metrics import WebSocketMetrics


class WebSocketHealthIndicatorTestCase(unittest.IsolatedAsyncioTestCase):
    """WebSocket 健康检查指示器测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.metrics = WebSocketMetrics()
        self.service = MagicMock()
        self.service._initialized = True
        self.service._running = True
        self.service._controllers = {"test": "controller"}

        self.health_indicator = WebSocketHealthIndicator(self.service, self.metrics)

    def test_health_detail_initialization(self):
        """测试健康检查详情初始化"""
        detail = HealthDetail(HealthStatus.UP, "Service is healthy")

        self.assertEqual(detail.status, HealthStatus.UP)
        self.assertEqual(detail.message, "Service is healthy")
        self.assertEqual(detail.details, {})
        self.assertIsNotNone(detail.timestamp)

        # 测试转换为字典
        detail_dict = detail.to_dict()
        self.assertEqual(detail_dict["status"], "UP")
        self.assertEqual(detail_dict["message"], "Service is healthy")
        self.assertIn("timestamp", detail_dict)
        self.assertIn("details", detail_dict)

    def test_service_status_check_healthy(self):
        """测试服务状态检查 - 健康状态"""
        result = self.health_indicator._check_service_status()

        self.assertEqual(result.status, HealthStatus.UP)
        self.assertIn("WebSocket service is running", result.message)
        self.assertTrue(result.details["initialized"])
        self.assertTrue(result.details["running"])
        self.assertEqual(result.details["registered_controllers"], 1)

    def test_service_status_check_not_initialized(self):
        """测试服务状态检查 - 未初始化"""
        self.service._initialized = False

        result = self.health_indicator._check_service_status()

        self.assertEqual(result.status, HealthStatus.DOWN)
        self.assertIn("not initialized", result.message)

    def test_service_status_check_no_service(self):
        """测试服务状态检查 - 无服务"""
        health_indicator = WebSocketHealthIndicator(None, self.metrics)

        result = health_indicator._check_service_status()

        self.assertEqual(result.status, HealthStatus.OUT_OF_SERVICE)
        self.assertIn("not available", result.message)

    def test_connection_health_check_healthy(self):
        """测试连接健康检查 - 健康状态"""
        # 模拟健康的连接指标
        self.metrics.record_connection_attempt("session_1")
        self.metrics.record_connection_success("session_1")

        result = self.health_indicator._check_connection_health()

        self.assertEqual(result.status, HealthStatus.UP)
        self.assertIn("Connection health is good", result.message)
        self.assertEqual(result.details["success_rate"], 1.0)

    def test_connection_health_check_low_success_rate(self):
        """测试连接健康检查 - 成功率过低"""
        # 模拟低成功率
        for i in range(10):
            self.metrics.record_connection_attempt(f"session_{i}")
            if i < 5:  # 只有50%成功率
                self.metrics.record_connection_success(f"session_{i}")
            else:
                self.metrics.record_connection_failure(f"session_{i}", Exception("Failed"))

        result = self.health_indicator._check_connection_health()

        self.assertEqual(result.status, HealthStatus.DOWN)
        self.assertIn("success rate too low", result.message)

    def test_connection_health_check_no_metrics(self):
        """测试连接健康检查 - 无指标"""
        health_indicator = WebSocketHealthIndicator(self.service, None)

        result = health_indicator._check_connection_health()

        self.assertEqual(result.status, HealthStatus.UNKNOWN)
        self.assertIn("Metrics not available", result.message)

    def test_message_health_check_healthy(self):
        """测试消息健康检查 - 健康状态"""
        # 模拟健康的消息指标
        self.metrics.record_message_sent("session_1", "text", 100, 5.0)
        self.metrics.record_message_received("session_1", "json", 150, 3.0)

        result = self.health_indicator._check_message_health()

        self.assertEqual(result.status, HealthStatus.UP)
        self.assertIn("Message processing is healthy", result.message)

    def test_message_health_check_high_failure_rate(self):
        """测试消息健康检查 - 失败率过高"""
        # 模拟高失败率
        for i in range(10):
            self.metrics.record_message_sent(f"session_{i}", "text", 100)
            if i >= 8:  # 20%失败率，超过默认的10%阈值
                self.metrics.record_message_failure(f"session_{i}", "text", Exception("Failed"))

        result = self.health_indicator._check_message_health()

        self.assertEqual(result.status, HealthStatus.DOWN)
        self.assertIn("failure rate too high", result.message)

    def test_message_health_check_high_processing_time(self):
        """测试消息健康检查 - 处理时间过长"""
        # 模拟高处理时间
        self.metrics.record_message_sent("session_1", "text", 100, 6000.0)  # 6秒，超过5秒阈值

        result = self.health_indicator._check_message_health()

        self.assertEqual(result.status, HealthStatus.DOWN)
        self.assertIn("processing time too high", result.message)

    def test_performance_health_check_healthy(self):
        """测试性能健康检查 - 健康状态"""
        # 模拟健康的性能指标
        response_times = [100, 200, 150, 180, 120]  # 都在5秒以下
        for rt in response_times:
            self.metrics.record_response_time(rt)

        result = self.health_indicator._check_performance_health()

        self.assertEqual(result.status, HealthStatus.UP)
        self.assertIn("Performance is healthy", result.message)

    def test_performance_health_check_high_response_time(self):
        """测试性能健康检查 - 响应时间过长"""
        # 模拟高响应时间
        response_times = [100, 200, 6000, 180, 120]  # P95会是6000，超过5000阈值
        for rt in response_times:
            self.metrics.record_response_time(rt)

        result = self.health_indicator._check_performance_health()

        self.assertEqual(result.status, HealthStatus.DOWN)
        self.assertIn("P95 response time too high", result.message)

    def test_error_rate_check_healthy(self):
        """测试错误率检查 - 健康状态"""
        # 模拟低错误率
        for i in range(100):
            self.metrics.record_connection_attempt(f"session_{i}")
            self.metrics.record_connection_success(f"session_{i}")
            self.metrics.record_message_sent(f"session_{i}", "text", 100)

        # 只有少量错误
        self.metrics.record_error("test_error", "test message")

        result = self.health_indicator._check_error_rate()

        self.assertEqual(result.status, HealthStatus.UP)
        self.assertIn("Error rate is acceptable", result.message)

    async def test_connectivity_check_success(self):
        """测试连接性检查 - 成功"""
        result = await self.health_indicator._check_connectivity()

        self.assertEqual(result.status, HealthStatus.UP)
        self.assertIn("Connectivity test passed", result.message)
        self.assertIn("test_response_time_ms", result.details)

    def test_evaluate_overall_health_all_up(self):
        """测试整体健康评估 - 全部正常"""
        results = [
            HealthDetail(HealthStatus.UP, "Service OK"),
            HealthDetail(HealthStatus.UP, "Connection OK"),
            HealthDetail(HealthStatus.UP, "Message OK"),
        ]

        overall_status = self.health_indicator._evaluate_overall_health(results)

        self.assertEqual(overall_status, HealthStatus.UP)

    def test_evaluate_overall_health_one_down(self):
        """测试整体健康评估 - 有一个失败"""
        results = [
            HealthDetail(HealthStatus.UP, "Service OK"),
            HealthDetail(HealthStatus.DOWN, "Connection Failed"),
            HealthDetail(HealthStatus.UP, "Message OK"),
        ]

        overall_status = self.health_indicator._evaluate_overall_health(results)

        self.assertEqual(overall_status, HealthStatus.DOWN)

    def test_evaluate_overall_health_out_of_service(self):
        """测试整体健康评估 - 服务不可用"""
        results = [HealthDetail(HealthStatus.OUT_OF_SERVICE, "Service unavailable"), HealthDetail(HealthStatus.UP, "Connection OK")]

        overall_status = self.health_indicator._evaluate_overall_health(results)

        self.assertEqual(overall_status, HealthStatus.OUT_OF_SERVICE)

    def test_evaluate_overall_health_unknown(self):
        """测试整体健康评估 - 未知状态"""
        results = [HealthDetail(HealthStatus.UP, "Service OK"), HealthDetail(HealthStatus.UNKNOWN, "Status unknown")]

        overall_status = self.health_indicator._evaluate_overall_health(results)

        self.assertEqual(overall_status, HealthStatus.UNKNOWN)

    def test_get_status_message(self):
        """测试状态消息生成"""
        results = [HealthDetail(HealthStatus.UP, "Test 1"), HealthDetail(HealthStatus.UP, "Test 2")]

        # 测试健康状态消息
        message = self.health_indicator._get_status_message(HealthStatus.UP, results)
        self.assertIn("healthy", message)
        self.assertIn("2 checks", message)

        # 测试不健康状态消息
        results[1].status = HealthStatus.DOWN
        message = self.health_indicator._get_status_message(HealthStatus.DOWN, results)
        self.assertIn("unhealthy", message)
        self.assertIn("1 checks failed", message)

    async def test_full_health_check(self):
        """测试完整健康检查"""
        # 设置健康的指标
        self.metrics.record_connection_attempt("session_1")
        self.metrics.record_connection_success("session_1")
        self.metrics.record_message_sent("session_1", "text", 100, 5.0)
        self.metrics.record_response_time(100.0)

        # 执行健康检查
        health_detail = await self.health_indicator.check_health()

        # 验证结果
        self.assertEqual(health_detail.status, HealthStatus.UP)
        self.assertIn("checks", health_detail.details)
        self.assertIn("summary", health_detail.details)

        # 验证历史记录
        self.assertEqual(len(self.health_indicator._health_history), 1)
        self.assertTrue(self.health_indicator.is_healthy())

    async def test_health_check_with_failure(self):
        """测试健康检查 - 有失败情况"""
        # 设置不健康的服务状态
        self.service._initialized = False

        # 执行健康检查
        health_detail = await self.health_indicator.check_health()

        # 验证结果
        self.assertEqual(health_detail.status, HealthStatus.DOWN)
        self.assertFalse(self.health_indicator.is_healthy())

    async def test_health_check_exception_handling(self):
        """测试健康检查异常处理"""
        # 模拟异常
        self.health_indicator.websocket_service = None
        self.health_indicator.websocket_metrics = None

        # 强制引发异常
        original_method = self.health_indicator._check_service_status
        self.health_indicator._check_service_status = lambda: (_ for _ in ()).throw(Exception("Test error"))

        # 执行健康检查
        health_detail = await self.health_indicator.check_health()

        # 验证异常处理
        self.assertEqual(health_detail.status, HealthStatus.UNKNOWN)
        self.assertIn("Health check failed", health_detail.message)
        self.assertIn("error", health_detail.details)

        # 恢复原方法
        self.health_indicator._check_service_status = original_method

    def test_health_history_management(self):
        """测试健康检查历史管理"""
        # 添加多个健康检查记录
        for i in range(5):
            detail = HealthDetail(HealthStatus.UP, f"Check {i}")
            self.health_indicator._record_health_history(detail)

        # 验证历史记录
        history = self.health_indicator.get_health_history(limit=3)
        self.assertEqual(len(history), 3)
        self.assertEqual(history[-1]["message"], "Check 4")  # 最新的记录

    def test_health_summary(self):
        """测试健康状态摘要"""
        summary = self.health_indicator._get_health_summary()

        self.assertIn("check_interval_seconds", summary)
        self.assertIn("thresholds", summary)
        self.assertIn("metrics_available", summary)
        self.assertTrue(summary["metrics_available"])

        # 测试无指标情况
        health_indicator = WebSocketHealthIndicator(self.service, None)
        summary = health_indicator._get_health_summary()
        self.assertFalse(summary["metrics_available"])


if __name__ == "__main__":
    unittest.main()
