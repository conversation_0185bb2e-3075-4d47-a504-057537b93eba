#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 缓存管理端点 - 提供缓存监控和管理功能

提供缓存相关的管理端点,包括:
- 缓存统计信息
- 缓存内容查看
- 缓存清理操作
- 缓存配置管理
- 缓存性能监控
"""

from typing import Any, Dict, List, Optional

try:
    from fastapi import HTTPException
except ImportError:
    class HTTPException(Exception):
        def __init__(self, status_code: int, detail: str = ""):
            self.status_code = status_code
            self.detail = detail
            super().__init__(detail)
from loguru import logger

from ..endpoints import Endpoint, EndpointOperation, OperationType
from .cache import CacheManager


class CacheEndpoint(Endpoint):
    """缓存管理端点"""

    def __init__(self):
        """初始化缓存端点"""
        super().__init__("cache", enabled=True, sensitive=True)
        self.cache_manager = CacheManager()

    @property
    def operations(self) -> List[EndpointOperation]:
        """获取端点操作列表"""
        return [
            EndpointOperation(operation_type=OperationType.READ, operation_id="stats", description="获取所有缓存统计信息"),
            EndpointOperation(operation_type=OperationType.READ, operation_id="info", description="获取缓存详细信息"),
            EndpointOperation(operation_type=OperationType.WRITE, operation_id="clear", description="清空指定缓存"),
            EndpointOperation(operation_type=OperationType.WRITE, operation_id="clear-all", description="清空所有缓存"),
            EndpointOperation(operation_type=OperationType.READ, operation_id="names", description="获取所有缓存名称"),
        ]

    async def invoke_async(self, operation: str = "stats", **kwargs) -> Dict[str, Any]:
        """异步调用缓存端点操作"""
        try:
            if operation == "stats":
                return await self._get_cache_stats()
            elif operation == "info":
                cache_name = kwargs.get("cache_name")
                return await self._get_cache_info(cache_name)
            elif operation == "clear":
                cache_name = kwargs.get("cache_name")
                if not cache_name:
                    raise ValueError("cache_name is required for clear operation")
                return await self._clear_cache(cache_name)
            elif operation == "clear-all":
                return await self._clear_all_caches()
            elif operation == "names":
                return await self._get_cache_names()
            else:
                raise ValueError(f"Unknown operation: {operation}")

        except Exception as e:
            logger.error(f"Cache endpoint operation failed: {operation}, error: {e}")
            raise HTTPException(status_code=500, detail=f"Cache operation failed: {str(e)}")

    def invoke(self, operation: str = "stats", **kwargs) -> Dict[str, Any]:
        """同步调用缓存端点操作"""
        import asyncio

        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.invoke_async(operation, **kwargs))
        except RuntimeError:
            # 没有事件循环,创建新的
            return asyncio.run(self.invoke_async(operation, **kwargs))

    async def _get_cache_stats(self) -> Dict[str, Any]:
        """获取所有缓存统计信息"""
        try:
            all_stats = self.cache_manager.get_all_stats()
            cache_names = self.cache_manager.get_cache_names()

            # 计算总体统计
            total_stats = {
                "total_caches": len(cache_names),
                "total_hits": sum(stats.get("hits", 0) for stats in all_stats.values()),
                "total_misses": sum(stats.get("misses", 0) for stats in all_stats.values()),
                "total_evictions": sum(stats.get("evictions", 0) for stats in all_stats.values()),
                "total_size": sum(stats.get("size", 0) for stats in all_stats.values()),
                "average_hit_rate": 0.0,
            }

            # 计算平均命中率
            if total_stats["total_caches"] > 0:
                hit_rates = [stats.get("hit_rate", 0.0) for stats in all_stats.values()]
                total_stats["average_hit_rate"] = sum(hit_rates) / len(hit_rates)

            return {"summary": total_stats, "caches": all_stats, "timestamp": self._get_current_timestamp()}

        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {"error": str(e), "timestamp": self._get_current_timestamp()}

    async def _get_cache_info(self, cache_name: Optional[str] = None) -> Dict[str, Any]:
        """获取缓存详细信息"""
        try:
            if cache_name:
                # 获取指定缓存的详细信息
                cache = self.cache_manager.get_cache(cache_name)
                if not cache:
                    raise ValueError(f"Cache not found: {cache_name}")

                return {"cache_name": cache_name, "info": cache.get_cache_info(), "timestamp": self._get_current_timestamp()}
            else:
                # 获取所有缓存的详细信息
                cache_names = self.cache_manager.get_cache_names()
                all_info = {}

                for name in cache_names:
                    cache = self.cache_manager.get_cache(name)
                    if cache:
                        all_info[name] = cache.get_cache_info()

                return {"caches": all_info, "timestamp": self._get_current_timestamp()}

        except Exception as e:
            logger.error(f"Failed to get cache info: {e}")
            return {"error": str(e), "timestamp": self._get_current_timestamp()}

    async def _clear_cache(self, cache_name: str) -> Dict[str, Any]:
        """清空指定缓存"""
        try:
            cache = self.cache_manager.get_cache(cache_name)
            if not cache:
                raise ValueError(f"Cache not found: {cache_name}")

            # 获取清理前的统计
            stats_before = cache.get_stats()

            # 清空缓存
            await cache.clear()

            logger.info(f"Cache cleared: {cache_name}")

            return {"cache_name": cache_name, "cleared": True, "entries_removed": stats_before.size, "timestamp": self._get_current_timestamp()}

        except Exception as e:
            logger.error(f"Failed to clear cache {cache_name}: {e}")
            return {"cache_name": cache_name, "cleared": False, "error": str(e), "timestamp": self._get_current_timestamp()}

    async def _clear_all_caches(self) -> Dict[str, Any]:
        """清空所有缓存"""
        try:
            cache_names = self.cache_manager.get_cache_names()

            # 获取清理前的统计
            total_entries = 0
            for name in cache_names:
                cache = self.cache_manager.get_cache(name)
                if cache:
                    total_entries += cache.get_stats().size

            # 清空所有缓存
            await self.cache_manager.clear_all_caches()

            logger.info(f"All caches cleared: {len(cache_names)} caches")

            return {"cleared": True, "caches_count": len(cache_names), "entries_removed": total_entries, "timestamp": self._get_current_timestamp()}

        except Exception as e:
            logger.error(f"Failed to clear all caches: {e}")
            return {"cleared": False, "error": str(e), "timestamp": self._get_current_timestamp()}

    async def _get_cache_names(self) -> Dict[str, Any]:
        """获取所有缓存名称"""
        try:
            cache_names = self.cache_manager.get_cache_names()

            # 获取每个缓存的基本信息
            cache_info = []
            for name in cache_names:
                cache = self.cache_manager.get_cache(name)
                if cache:
                    stats = cache.get_stats()
                    cache_info.append(
                        {
                            "name": name,
                            "strategy": cache.strategy.value,
                            "max_size": cache.max_size,
                            "current_size": stats.size,
                            "hit_rate": stats.hit_rate,
                        }
                    )

            return {"cache_names": cache_names, "cache_info": cache_info, "total_count": len(cache_names), "timestamp": self._get_current_timestamp()}

        except Exception as e:
            logger.error(f"Failed to get cache names: {e}")
            return {"error": str(e), "timestamp": self._get_current_timestamp()}

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime

        return datetime.now().isoformat()

    def get_endpoint_info(self) -> Dict[str, Any]:
        """获取端点信息"""
        return {
            "id": self.endpoint_id,
            "enabled": self.enabled,
            "sensitive": self.sensitive,
            "description": "Cache management endpoint for monitoring and managing endpoint caches",
            "operations": [
                {"id": "stats", "type": "READ", "description": "Get cache statistics for all caches", "parameters": []},
                {
                    "id": "info",
                    "type": "READ",
                    "description": "Get detailed cache information",
                    "parameters": [
                        {"name": "cache_name", "type": "string", "required": False, "description": "Name of specific cache to get info for"}
                    ],
                },
                {
                    "id": "clear",
                    "type": "WRITE",
                    "description": "Clear specific cache",
                    "parameters": [{"name": "cache_name", "type": "string", "required": True, "description": "Name of cache to clear"}],
                },
                {"id": "clear-all", "type": "WRITE", "description": "Clear all caches", "parameters": []},
                {"id": "names", "type": "READ", "description": "Get all cache names and basic info", "parameters": []},
            ],
            "cache_strategies": ["lru", "lfu", "fifo", "ttl", "adaptive"],
        }
