 _____ _____ _____ _____     _____ _____ _____ _____
|     |     |   | |     |___|  _  |     |     |_   _|
| | | |-   -| | | |-   -|___|   __|  |  |  |  | | |
|_|_|_|_____|_|___|_____|   |__|  |_____|_____|_|_|

 :: ${application.name} :: (v${application.version})
 :: Powered by Mini-Boot :: (v${miniboot.version})

## 应用信息:
  名称: ${application.name}
  版本: ${application.version}
  描述: 基于 Mini-Boot 框架的 Python 应用
  环境: ${profiles.active:dev}
  启动时间: ${startup.time}

## 系统信息:
  Python版本: ${python.version}
  系统: ${os.name} ${os.version}
  平台: ${os.platform}
  CPU核心: ${cpu.cores}

## 功能简介:
  🚀 依赖注入 - 强大的 IoC 容器和依赖管理
  ⚙️  自动配置 - 智能的配置加载和环境管理
  🌐 Web服务 - 内置 FastAPI Web 服务器
  🔧 调度任务 - 灵活的定时任务调度系统
  ⭐ 事件系统 - 异步事件发布和订阅机制
  📊 监控端点 - Actuator 健康检查和指标监控
  🎯 异步处理 - 高性能异步任务处理

========================================================
