#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 配置属性模块 - 提供 Web 应用的统一配置管理功能

Web 配置属性模块

提供 Web 应用的统一配置管理功能。

主要功能:
- WebProperties - Web应用统一配置类
- CorsConfig - CORS跨域配置
- CompressionConfig - 压缩配置
- LoggingConfig - 日志配置
- DocsConfig - API文档配置
- StaticConfig - 静态文件配置

注意：背压控制、任务调度等功能现在由独立的 Starters 提供：
- miniboot.starters.web.backpressure - 背压控制
- miniboot.starters.web.task_scheduling - 任务调度
- miniboot.starters.web.load_monitoring - 负载监控
"""

from dataclasses import dataclass, field
from typing import Optional


@dataclass
class CorsConfig:
    """CORS跨域配置

    配置跨域资源共享(CORS)相关参数.
    """

    enabled: bool = True
    allowed_origins: list[str] = field(default_factory=lambda: ["*"])
    allowed_methods: list[str] = field(default_factory=lambda: ["GET", "POST", "PUT", "DELETE", "OPTIONS"])
    allowed_headers: list[str] = field(default_factory=lambda: ["*"])
    allow_credentials: bool = True

    def __post_init__(self) -> None:
        """初始化后处理,确保默认值正确设置"""
        if self.allowed_origins is None:
            self.allowed_origins = ["*"]
        if self.allowed_methods is None:
            self.allowed_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        if self.allowed_headers is None:
            self.allowed_headers = ["*"]


@dataclass
class CompressionConfig:
    """压缩配置

    配置HTTP响应压缩相关参数.
    """

    enabled: bool = True
    min_size: int = 1024  # 最小压缩大小(字节)


@dataclass
class LoggingConfig:
    """日志配置

    配置Web请求日志相关参数.
    """

    enabled: bool = True
    log_requests: bool = True  # 是否记录请求
    log_responses: bool = True  # 是否记录响应
    log_headers: bool = True  # 是否包含请求头
    log_body: bool = True  # 是否包含请求体
    log_query_params: bool = True  # 是否记录查询参数
    max_body_size: int = 10240  # 最大请求体大小(字节)


@dataclass
class DocsConfig:
    """API文档配置

    配置Swagger/OpenAPI文档相关参数.
    """

    enabled: bool = True
    title: str = "Mini-Boot API"
    description: str = "Mini-Boot Web API Documentation"


@dataclass
class StaticConfig:
    """静态文件配置

    配置静态文件服务相关参数.
    """

    enabled: bool = False  # 是否启用静态文件服务,默认禁用以减少资源消耗
    directory: str = "static"  # 静态文件目录
    mount_path: str = "/static"  # 挂载路径
    html: bool = True  # 是否启用HTML文件服务
    check_dir: bool = True  # 是否检查目录存在


@dataclass
class WebProperties:
    """Web配置属性

    Web应用的主配置类，包含核心Web功能的配置参数。

    注意：背压控制、任务调度等高级功能现在由独立的 Starters 提供。
    """

    # 基础配置
    enabled: bool = True
    host: str = "0.0.0.0"
    port: int = 8080
    title: str = "Mini-Boot Application"
    description: str = "Mini-Boot Web Application"
    version: str = "1.0.0"
    log_level: str = "INFO"

    # 传统Web配置
    cors: Optional[CorsConfig] = None
    compression: Optional[CompressionConfig] = None
    logging: Optional[LoggingConfig] = None
    docs: Optional[DocsConfig] = None
    static: Optional[StaticConfig] = None

    def __post_init__(self):
        """初始化后处理,确保子配置对象正确创建"""
        # 传统Web配置初始化
        if self.cors is None:
            self.cors = CorsConfig()
        if self.compression is None:
            self.compression = CompressionConfig()
        if self.logging is None:
            self.logging = LoggingConfig()
        if self.docs is None:
            self.docs = DocsConfig()
        if self.static is None:
            self.static = StaticConfig()

        # 注意：背压控制和异步优化配置现在由独立的 Starters 管理

    @classmethod
    def from_environment(cls, environment) -> "WebProperties":
        """从环境配置创建Web配置

        Args:
            environment: 环境配置对象

        Returns:
            WebProperties实例
        """
        return cls(
            # 基础配置
            enabled=environment.get_property("miniboot.web.enabled", True),
            host=environment.get_property("miniboot.web.host", "0.0.0.0"),
            port=environment.get_property("miniboot.web.port", 8080),
            title=environment.get_property("miniboot.web.title", "Mini-Boot Application"),
            description=environment.get_property("miniboot.web.description", "Mini-Boot Web Application"),
            version=environment.get_property("miniboot.web.version", "1.0.0"),
            log_level=environment.get_property("miniboot.web.log-level", "INFO"),
            # 传统Web配置
            cors=CorsConfig(
                enabled=environment.get_property("miniboot.web.cors.enabled", True),
                allowed_origins=environment.get_property("miniboot.web.cors.allowed-origins", ["*"]),
                allowed_methods=environment.get_property("miniboot.web.cors.allowed-methods", ["GET", "POST", "PUT", "DELETE", "OPTIONS"]),
                allowed_headers=environment.get_property("miniboot.web.cors.allowed-headers", ["*"]),
                allow_credentials=environment.get_property("miniboot.web.cors.allow-credentials", True),
            ),
            compression=CompressionConfig(
                enabled=environment.get_property("miniboot.web.compression.enabled", True),
                min_size=environment.get_property("miniboot.web.compression.min-size", 1024),
            ),
            logging=LoggingConfig(
                enabled=environment.get_property("miniboot.web.logging.enabled", True),
                log_requests=environment.get_property("miniboot.web.logging.log-requests", True),
                log_responses=environment.get_property("miniboot.web.logging.log-responses", True),
                log_headers=environment.get_property("miniboot.web.logging.log-headers", True),
                log_body=environment.get_property("miniboot.web.logging.log-body", True),
                log_query_params=environment.get_property("miniboot.web.logging.log-query-params", True),
                max_body_size=environment.get_property("miniboot.web.logging.max-body-size", 10240),
            ),
            docs=DocsConfig(
                enabled=environment.get_property("miniboot.web.docs.enabled", True),
                title=environment.get_property("miniboot.web.docs.title", "Mini-Boot API"),
                description=environment.get_property("miniboot.web.docs.description", "Mini-Boot Web API Documentation"),
            ),
            static=StaticConfig(
                enabled=environment.get_property("miniboot.web.static.enabled", False),
                directory=environment.get_property("miniboot.web.static.directory", "static"),
                mount_path=environment.get_property("miniboot.web.static.mount-path", "/static"),
                html=environment.get_property("miniboot.web.static.html", True),
                check_dir=environment.get_property("miniboot.web.static.check-dir", True),
            ),
        )
