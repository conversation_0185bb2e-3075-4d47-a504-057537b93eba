# Mini-Boot 环境配置管理系统 (env)

环境配置管理系统是 Mini-Boot 框架的核心组件之一，负责管理应用程序的配置信息，包括环境变量、配置文件、命令行参数等。本文档将详细介绍 env 目录下的代码结构和功能实现。

## 1. 架构概述

环境配置管理系统主要由以下几个核心部分组成：

-   **环境接口(Environment)**: 定义了环境配置的核心功能
-   **属性源(PropertySource)**: 配置数据的来源，如系统环境变量、YAML 配置文件等
-   **属性绑定(Binder)**: 将配置属性绑定到数据类对象
-   **类型转换(Converter)**: 在不同数据类型之间进行转换

### 1.1 实现架构路径

环境配置模块采用分层架构设计，各层职责清晰，便于扩展和维护：

1. **核心接口层**：PropertyResolver → Environment → ConfigurableEnvironment

    - 定义配置管理的核心抽象接口
    - 提供属性解析、占位符处理等基础功能

2. **属性源层**：PropertySource → 各种具体实现 (YAML/JSON/System/CommandLine)

    - 统一不同配置来源的访问接口
    - 支持优先级管理和动态添加/移除

3. **类型转换层**：Converter → ConversionService → 各种转换器实现

    - 提供灵活的类型转换机制
    - 支持自定义转换器注册和链式转换

4. **属性绑定层**：Binder → BindingResult → 数据类绑定

    - 实现配置到对象的自动绑定
    - 支持嵌套对象和复杂类型绑定

5. **资源加载层**：ResourceLoader → FileResource → .env 文件处理

    - 统一资源访问接口
    - 支持多种文件格式和环境变量处理

6. **标准实现**：StandardEnvironment (整合所有功能)
    - 提供开箱即用的完整实现
    - 集成所有层次的功能模块

### 1.2 目录结构

```
env/
├── __init__.py                      # 环境模块导出
├── environment.py                   # 环境接口定义
├── standard_environment.py         # 标准环境实现
├── property_resolver.py             # 属性解析器
├── bind/                           # 属性绑定相关代码
│   ├── __init__.py
│   ├── binder.py                   # 绑定器接口和实现
│   └── binding_result.py           # 绑定结果
├── convert/                        # 类型转换相关代码
│   ├── __init__.py
│   ├── converter.py                # 转换器接口
│   ├── conversion_service.py       # 转换服务
│   └── converters/                 # 内置转换器
│       ├── __init__.py
│       ├── string_converters.py    # 字符串转换器
│       └── collection_converters.py # 集合转换器
├── source/                         # 属性源相关代码
│   ├── __init__.py
│   ├── property_source.py          # 属性源接口
│   ├── mutable_property_sources.py # 可变属性源管理
│   ├── system_property_source.py   # 系统属性源
│   ├── yaml_property_source.py     # YAML属性源
│   ├── json_property_source.py     # JSON属性源
│   └── command_line_property_source.py # 命令行属性源
└── resource/                       # 资源加载相关代码
    ├── __init__.py
    ├── resource_loader.py          # 资源加载器
    └── file_resource.py            # 文件资源
```

## 2. 核心接口

### 2.1 环境接口(Environment)

`Environment`接口是整个环境配置管理系统的核心，它继承了`PropertyResolver`接口，并提供了获取配置文件、检查属性等功能：

```python
from abc import ABC, abstractmethod
from typing import List, Any, Optional, Type, Dict

class PropertyResolver(ABC):
    """属性解析器接口"""

    @abstractmethod
    def get_property(self, key: str) -> Optional[Any]:
        """获取属性值"""
        pass

    @abstractmethod
    def get_property_or_default(self, key: str, default_value: Any) -> Any:
        """获取属性值，如果不存在则返回默认值"""
        pass

    @abstractmethod
    def resolve_placeholders(self, text: str) -> str:
        """解析属性占位符"""
        pass

    @abstractmethod
    def resolve_required_placeholders(self, text: str) -> str:
        """解析必需的属性占位符"""
        pass

class Environment(PropertyResolver):
    """环境接口"""

    @abstractmethod
    def contains_property(self, key: str) -> bool:
        """检查是否包含属性"""
        pass

    @abstractmethod
    def get_active_profiles(self) -> List[str]:
        """获取激活的配置文件"""
        pass

    @abstractmethod
    def get_default_profiles(self) -> List[str]:
        """获取默认的配置文件"""
        pass

    @abstractmethod
    def accept_profiles(self, *profiles: str) -> bool:
        """是否接受指定的配置文件"""
        pass

    @abstractmethod
    def get_property_as(self, key: str, target_type: Type) -> Optional[Any]:
        """获取属性值并转换为指定类型"""
        pass

    @abstractmethod
    def get_property_or_default_as(self, key: str, default_value: Any, target_type: Type) -> Any:
        """获取属性值并转换为指定类型，如果不存在则返回默认值"""
        pass

class ConfigurableEnvironment(Environment):
    """可配置环境接口"""

    @abstractmethod
    def get_property_sources(self) -> 'MutablePropertySources':
        """获取所有属性源"""
        pass

    @abstractmethod
    def set_active_profiles(self, *profiles: str) -> None:
        """设置激活的配置文件"""
        pass

    @abstractmethod
    def merge(self, other: Environment) -> None:
        """合并其他环境配置"""
        pass
```

### 2.2 属性源(PropertySource)

`PropertySource`接口定义了配置属性的来源：

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class PropertySource(ABC):
    """属性源接口"""

    def __init__(self, name: str, priority: int = 0):
        self._name = name
        self._priority = priority

    @property
    def name(self) -> str:
        """获取属性源名称"""
        return self._name

    @property
    def priority(self) -> int:
        """获取优先级"""
        return self._priority

    @abstractmethod
    def get_property(self, name: str) -> Optional[Any]:
        """获取属性值"""
        pass

    @abstractmethod
    def get_properties(self) -> Dict[str, Any]:
        """获取所有配置属性"""
        pass

    def contains_property(self, name: str) -> bool:
        """检查是否包含属性"""
        return self.get_property(name) is not None
```

框架提供了多种属性源实现：

-   `SystemEnvironmentPropertySource`: 系统环境变量属性源
-   `YamlPropertySource`: YAML 配置文件属性源
-   `JsonPropertySource`: JSON 配置文件属性源
-   `CommandLinePropertySource`: 命令行参数属性源

### 2.3 属性绑定(Binder)

`Binder`接口定义了将配置属性绑定到数据类对象的功能：

```python
from abc import ABC, abstractmethod
from typing import Any, Type, Optional
from dataclasses import dataclass

class BindingResult:
    """绑定结果"""

    def __init__(self, value: Any = None, errors: List[str] = None):
        self.value = value
        self.errors = errors or []
        self.is_bound = value is not None

    @property
    def is_successful(self) -> bool:
        return self.is_bound and not self.errors

class Binder(ABC):
    """绑定器接口"""

    @abstractmethod
    def bind(self, target_type: Type, prefix: str = "") -> BindingResult:
        """将配置绑定到目标类型"""
        pass

# 使用示例
@dataclass
class ServerConfig:
    port: int = 8080
    host: str = "localhost"
    debug: bool = False

    class Config:
        # 配置属性映射
        property_mapping = {
            'port': 'server.port',
            'host': 'server.host',
            'debug': 'server.debug'
        }
```

### 2.4 类型转换(Converter)

`Converter`接口定义了类型转换的功能：

```python
from abc import ABC, abstractmethod
from typing import Any, Type, Optional

class TypeDescriptor:
    """类型描述符"""

    def __init__(self, type_class: Type):
        self.type_class = type_class

    def __eq__(self, other):
        return isinstance(other, TypeDescriptor) and self.type_class == other.type_class

class Converter(ABC):
    """转换器接口"""

    @abstractmethod
    def convert(self, value: Any, target_type: Type) -> Any:
        """将值转换为目标类型"""
        pass

    @abstractmethod
    def get_source_type(self) -> TypeDescriptor:
        """获取源类型"""
        pass

    @abstractmethod
    def get_target_type(self) -> TypeDescriptor:
        """获取目标类型"""
        pass

    def can_convert(self, source_type: Type, target_type: Type) -> bool:
        """检查是否可以转换"""
        return (self.get_source_type().type_class == source_type and
                self.get_target_type().type_class == target_type)

# 自定义转换器示例
class StringToCustomValueConverter(Converter):
    """字符串到自定义值转换器"""

    def convert(self, value: Any, target_type: Type) -> Any:
        if not isinstance(value, str):
            raise ValueError(f"Source is not string: {value}")
        return CustomValue(name=value)

    def get_source_type(self) -> TypeDescriptor:
        return TypeDescriptor(str)

    def get_target_type(self) -> TypeDescriptor:
        return TypeDescriptor(CustomValue)
```

## 3. 标准环境实现

`StandardEnvironment`是`Environment`接口的标准实现，它提供了以下功能：

-   管理多个属性源
-   解析属性占位符
-   支持配置文件
-   类型转换

```python
import os
import re
from typing import List, Any, Optional, Type
from .source.mutable_property_sources import MutablePropertySources
from .source.system_property_source import SystemEnvironmentPropertySource
from .convert.conversion_service import DefaultConversionService

class StandardEnvironment(ConfigurableEnvironment):
    """标准环境实现"""

    def __init__(self, resource_loader=None):
        self._property_sources = MutablePropertySources()
        self._active_profiles = []
        self._default_profiles = ["default"]
        self._resource_loader = resource_loader
        self._conversion_service = DefaultConversionService()

        # 添加系统环境变量源
        self._property_sources.add_last(SystemEnvironmentPropertySource())

    def contains_property(self, key: str) -> bool:
        """检查是否包含属性"""
        return self.get_property(key) is not None

    def get_property(self, key: str) -> Optional[Any]:
        """获取属性值"""
        for source in self._property_sources:
            value = source.get_property(key)
            if value is not None:
                return value
        return None

    def get_property_or_default(self, key: str, default_value: Any) -> Any:
        """获取属性值，如果不存在则返回默认值"""
        value = self.get_property(key)
        return value if value is not None else default_value

    def get_property_as(self, key: str, target_type: Type) -> Optional[Any]:
        """获取属性值并转换为指定类型"""
        value = self.get_property(key)
        if value is None:
            return None
        return self._conversion_service.convert(value, target_type)

    def get_property_or_default_as(self, key: str, default_value: Any, target_type: Type) -> Any:
        """获取属性值并转换为指定类型，如果不存在则返回默认值"""
        value = self.get_property_as(key, target_type)
        return value if value is not None else default_value

    def resolve_placeholders(self, text: str) -> str:
        """解析属性占位符 ${key:default}"""
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'

        def replace_placeholder(match):
            key = match.group(1)
            default = match.group(2) if match.group(2) is not None else ""
            return str(self.get_property_or_default(key, default))

        return re.sub(pattern, replace_placeholder, text)

    def resolve_required_placeholders(self, text: str) -> str:
        """解析必需的属性占位符"""
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'

        def replace_placeholder(match):
            key = match.group(1)
            value = self.get_property(key)
            if value is None:
                raise ValueError(f"Required property '{key}' not found")
            return str(value)

        return re.sub(pattern, replace_placeholder, text)
```

## 4. 属性源管理

`MutablePropertySources`类管理多个属性源，并按照优先级顺序进行查找：

```python
from typing import List, Iterator
from .property_source import PropertySource

class MutablePropertySources:
    """可变属性源管理器"""

    def __init__(self):
        self._property_sources: List[PropertySource] = []

    def add_first(self, source: PropertySource) -> None:
        """添加到最前面（最高优先级）"""
        self._property_sources.insert(0, source)

    def add_last(self, source: PropertySource) -> None:
        """添加到最后面（最低优先级）"""
        self._property_sources.append(source)

    def add_before(self, relative_name: str, source: PropertySource) -> None:
        """在指定属性源之前添加"""
        for i, ps in enumerate(self._property_sources):
            if ps.name == relative_name:
                self._property_sources.insert(i, source)
                return
        self.add_last(source)

    def add_after(self, relative_name: str, source: PropertySource) -> None:
        """在指定属性源之后添加"""
        for i, ps in enumerate(self._property_sources):
            if ps.name == relative_name:
                self._property_sources.insert(i + 1, source)
                return
        self.add_last(source)

    def remove(self, name: str) -> PropertySource:
        """移除指定名称的属性源"""
        for i, ps in enumerate(self._property_sources):
            if ps.name == name:
                return self._property_sources.pop(i)
        raise ValueError(f"PropertySource '{name}' not found")

    def replace(self, name: str, source: PropertySource) -> None:
        """替换指定名称的属性源"""
        for i, ps in enumerate(self._property_sources):
            if ps.name == name:
                self._property_sources[i] = source
                return
        raise ValueError(f"PropertySource '{name}' not found")

    def __iter__(self) -> Iterator[PropertySource]:
        """按优先级顺序迭代"""
        return iter(sorted(self._property_sources, key=lambda x: x.priority, reverse=True))

    def __len__(self) -> int:
        return len(self._property_sources)
```

## 5. YAML 配置源

`YamlPropertySource`实现了对 YAML 配置文件的支持，它将嵌套的 YAML 结构扁平化为键值对：

```python
import yaml
from typing import Dict, Any, Optional
from .property_source import PropertySource

class YamlPropertySource(PropertySource):
    """YAML属性源"""

    def __init__(self, name: str, file_path: str, priority: int = 100):
        super().__init__(name, priority)
        self._file_path = file_path
        self._properties = self._load_yaml()

    def _load_yaml(self) -> Dict[str, Any]:
        """加载YAML文件"""
        try:
            with open(self._file_path, 'r', encoding='utf-8') as file:
                data = yaml.safe_load(file)
                return self._flatten_properties("", data) if data else {}
        except FileNotFoundError:
            return {}
        except yaml.YAMLError as e:
            raise ValueError(f"Failed to parse YAML file {self._file_path}: {e}")

    def _flatten_properties(self, prefix: str, source: Any) -> Dict[str, Any]:
        """扁平化属性"""
        result = {}

        if isinstance(source, dict):
            for key, value in source.items():
                full_key = f"{prefix}.{key}" if prefix else key
                if isinstance(value, (dict, list)):
                    result.update(self._flatten_properties(full_key, value))
                else:
                    result[full_key] = value
        elif isinstance(source, list):
            # 对于数组类型，转换为JSON格式的字符串
            import json
            result[prefix] = json.dumps(source)
        else:
            result[prefix] = source

        return result

    def get_property(self, name: str) -> Optional[Any]:
        """获取属性值"""
        return self._properties.get(name)

    def get_properties(self) -> Dict[str, Any]:
        """获取所有配置属性"""
        return self._properties.copy()
```

## 6. 使用示例

### 6.1 创建环境并加载配置

```python
from miniboot.env import StandardEnvironment
from miniboot.env.source import YamlPropertySource, JsonPropertySource
from miniboot.env.resource import DefaultResourceLoader

# 创建资源加载器
loader = DefaultResourceLoader()

# 创建标准环境
env = StandardEnvironment(loader)

# 加载YAML配置文件
yaml_source = YamlPropertySource("application", "application.yml", 100)
env.get_property_sources().add_last(yaml_source)

# 加载JSON配置文件
json_source = JsonPropertySource("config", "config.json", 90)
env.get_property_sources().add_last(json_source)

# 获取配置属性
port = env.get_property("server.port")
print(f"Server Port: {port}")

# 获取带类型转换的属性
port_int = env.get_property_as("server.port", int)
debug_bool = env.get_property_as("app.debug", bool)
```

### 6.2 绑定配置到数据类

```python
from dataclasses import dataclass
from typing import Optional
from miniboot.env.bind import DefaultBinder

# 定义配置数据类
@dataclass
class ServerConfig:
    port: int = 8080
    host: str = "localhost"
    debug: bool = False
    timeout: float = 30.0

    class Config:
        # 配置属性映射
        property_mapping = {
            'port': 'server.port',
            'host': 'server.host',
            'debug': 'server.debug',
            'timeout': 'server.timeout'
        }

@dataclass
class DatabaseConfig:
    url: str = "sqlite:///app.db"
    username: Optional[str] = None
    password: Optional[str] = None
    pool_size: int = 5

    class Config:
        property_mapping = {
            'url': 'database.url',
            'username': 'database.username',
            'password': 'database.password',
            'pool_size': 'database.pool-size'
        }

# 创建绑定器
binder = DefaultBinder(env)

# 绑定配置
server_config_result = binder.bind(ServerConfig)
if server_config_result.is_successful:
    server_config = server_config_result.value
    print(f"Server Config: {server_config}")
else:
    print(f"Binding errors: {server_config_result.errors}")

database_config_result = binder.bind(DatabaseConfig)
if database_config_result.is_successful:
    database_config = database_config_result.value
    print(f"Database Config: {database_config}")
```

### 6.3 属性占位符解析

```python
# 配置文件内容示例 (application.yml)
"""
app:
  name: "MyApp"
  version: "1.0.0"
  description: "${app.name} version ${app.version}"

server:
  host: "${SERVER_HOST:localhost}"
  port: "${SERVER_PORT:8080}"
  url: "http://${server.host}:${server.port}"
"""

# 解析占位符
description = env.resolve_placeholders("${app.description}")
print(description)  # 输出: MyApp version 1.0.0

server_url = env.resolve_placeholders("${server.url}")
print(server_url)  # 输出: http://localhost:8080

# 解析必需的占位符（如果属性不存在会抛出异常）
try:
    required_value = env.resolve_required_placeholders("${required.property}")
except ValueError as e:
    print(f"Error: {e}")
```

## 7. 扩展点

环境配置管理系统提供了多个扩展点，用户可以根据需要进行扩展：

1. **自定义属性源**: 继承`PropertySource`类
2. **自定义类型转换器**: 实现`Converter`接口
3. **自定义环境实现**: 实现`Environment`接口

### 7.1 自定义属性源示例

```python
import redis
from typing import Dict, Any, Optional
from miniboot.env.source import PropertySource

class RedisPropertySource(PropertySource):
    """Redis属性源"""

    def __init__(self, name: str, redis_client: redis.Redis, key_prefix: str = "config:", priority: int = 150):
        super().__init__(name, priority)
        self._redis = redis_client
        self._key_prefix = key_prefix
        self._properties = self._load_from_redis()

    def _load_from_redis(self) -> Dict[str, Any]:
        """从Redis加载配置"""
        properties = {}
        try:
            keys = self._redis.keys(f"{self._key_prefix}*")
            for key in keys:
                config_key = key.decode('utf-8').replace(self._key_prefix, "")
                value = self._redis.get(key)
                if value:
                    properties[config_key] = value.decode('utf-8')
        except Exception as e:
            print(f"Failed to load properties from Redis: {e}")
        return properties

    def get_property(self, name: str) -> Optional[Any]:
        """获取属性值"""
        return self._properties.get(name)

    def get_properties(self) -> Dict[str, Any]:
        """获取所有配置属性"""
        return self._properties.copy()

# 使用自定义属性源
redis_client = redis.Redis(host='localhost', port=6379, db=0)
redis_source = RedisPropertySource("redis-config", redis_client)
env.get_property_sources().add_first(redis_source)
```

### 7.2 自定义类型转换器示例

```python
from datetime import datetime
from miniboot.env.convert import Converter, TypeDescriptor

class StringToDateTimeConverter(Converter):
    """字符串到日期时间转换器"""

    def __init__(self, date_format: str = "%Y-%m-%d %H:%M:%S"):
        self._date_format = date_format

    def convert(self, value: Any, target_type: Type) -> Any:
        if not isinstance(value, str):
            raise ValueError(f"Source is not string: {value}")
        try:
            return datetime.strptime(value, self._date_format)
        except ValueError as e:
            raise ValueError(f"Failed to parse datetime '{value}' with format '{self._date_format}': {e}")

    def get_source_type(self) -> TypeDescriptor:
        return TypeDescriptor(str)

    def get_target_type(self) -> TypeDescriptor:
        return TypeDescriptor(datetime)

# 注册自定义转换器
env._conversion_service.add_converter(StringToDateTimeConverter())
```

## 8. 总结

Mini-Boot 的环境配置管理系统提供了灵活、强大的配置管理功能，支持多种配置源、类型转换和属性绑定，可以满足各种复杂的配置需求。通过良好的接口设计和扩展点，用户可以根据需要进行自定义扩展，实现更加灵活的配置管理。

### 主要特性

1. **多配置源支持**: YAML、JSON、环境变量、命令行参数等
2. **属性占位符**: 支持 `${key:default}` 格式的占位符解析
3. **类型安全**: 自动类型转换和验证
4. **配置绑定**: 将配置自动绑定到数据类
5. **优先级管理**: 灵活的配置源优先级控制
6. **扩展性**: 支持自定义属性源和转换器

---

_本文档定义了 Mini-Boot 框架的环境配置管理模块设计，提供了灵活、强大的配置管理功能。_
