#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Actuator 端点注册表

提供端点的注册、管理和查询功能。
"""

from typing import Optional

from .base import Endpoint


class EndpointRegistry:
    """端点注册表"""

    def __init__(self):
        self._endpoints: dict[str, Endpoint] = {}

    def register(self, endpoint: Endpoint) -> None:
        """注册端点"""
        if not endpoint.enabled:
            return

        if endpoint.id in self._endpoints:
            raise ValueError(f"Endpoint '{endpoint.id}' already registered")

        self._endpoints[endpoint.id] = endpoint

    def unregister(self, endpoint_id: str) -> bool:
        """注销端点"""
        if endpoint_id in self._endpoints:
            del self._endpoints[endpoint_id]
            return True
        return False

    def get_endpoint(self, endpoint_id: str) -> Optional[Endpoint]:
        """获取端点"""
        return self._endpoints.get(endpoint_id)

    def get_endpoints(self) -> dict[str, Endpoint]:
        """获取所有端点"""
        return self._endpoints.copy()

    def get_enabled_endpoints(self) -> dict[str, Endpoint]:
        """获取所有启用的端点"""
        return {endpoint_id: endpoint for endpoint_id, endpoint in self._endpoints.items() if endpoint.enabled}

    def clear(self) -> None:
        """清空所有端点"""
        self._endpoints.clear()

    def __len__(self) -> int:
        return len(self._endpoints)

    def __contains__(self, endpoint_id: str) -> bool:
        return endpoint_id in self._endpoints

    def __iter__(self):
        return iter(self._endpoints.values())
