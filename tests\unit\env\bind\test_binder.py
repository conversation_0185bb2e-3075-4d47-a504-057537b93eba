#!/usr/bin/env python
"""
* @author: cz
* @description: 属性绑定器测试
"""

import unittest
from dataclasses import dataclass

from miniboot.env.bind import Binder, configuration_properties
from miniboot.env.convert import DefaultConversionService
from miniboot.env.resolver import PropertyResolver


class MockPropertyResolver(PropertyResolver):
    """模拟属性解析器"""

    def __init__(self, properties: dict = None):
        self._properties = properties or {}

    def get_property(self, key: str, default=None):
        return self._properties.get(key, default)

    def contains_property(self, key: str) -> bool:
        return key in self._properties


@dataclass
class DatabaseConfig:
    """数据库配置类"""

    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = ""
    enabled: bool = True


@dataclass
class ServerConfig:
    """服务器配置类"""

    name: str = "default"
    port: int = 8080
    debug: bool = False
    features: list[str] = None


@configuration_properties(prefix="app")
@dataclass
class AppConfig:
    """应用配置类"""

    name: str = "MyApp"
    version: str = "1.0.0"
    debug: bool = False


class BinderTestCase(unittest.TestCase):
    """属性绑定器测试"""

    def setUp(self):
        self.properties = {
            "database.host": "mysql.example.com",
            "database.port": "3306",
            "database.username": "admin",
            "database.password": "secret123",
            "database.enabled": "true",
            "server.name": "web-server",
            "server.port": "9090",
            "server.debug": "false",
            "server.features": "auth,logging,metrics",
            "app.name": "TestApp",
            "app.version": "2.0.0",
            "app.debug": "true",
        }
        self.resolver = MockPropertyResolver(self.properties)
        self.conversion_service = DefaultConversionService()
        self.binder = Binder(self.resolver, self.conversion_service)

    def test_bind_database_config(self):
        """测试绑定数据库配置"""
        result = self.binder.bind("database", DatabaseConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        config = result.target
        self.assertEqual("mysql.example.com", config.host)
        self.assertEqual(3306, config.port)
        self.assertEqual("admin", config.username)
        self.assertEqual("secret123", config.password)
        self.assertTrue(config.enabled)

    def test_bind_server_config(self):
        """测试绑定服务器配置"""
        result = self.binder.bind("server", ServerConfig)

        self.assertFalse(result.has_errors)
        self.assertIsNotNone(result.target)

        config = result.target
        self.assertEqual("web-server", config.name)
        self.assertEqual(9090, config.port)
        self.assertFalse(config.debug)
        self.assertEqual(["auth", "logging", "metrics"], config.features)

    def test_bind_with_defaults(self):
        """测试使用默认值绑定"""
        # 只提供部分属性
        partial_properties = {"partial.name": "custom-name"}
        resolver = MockPropertyResolver(partial_properties)
        binder = Binder(resolver, self.conversion_service)

        result = binder.bind("partial", ServerConfig)

        self.assertFalse(result.has_errors)
        config = result.target
        self.assertEqual("custom-name", config.name)
        self.assertEqual(8080, config.port)  # 使用默认值
        self.assertFalse(config.debug)  # 使用默认值

    def test_bind_empty_prefix(self):
        """测试空前缀绑定"""
        # 使用顶级属性
        top_level_properties = {"name": "top-level-server", "port": "7777", "debug": "true"}
        resolver = MockPropertyResolver(top_level_properties)
        binder = Binder(resolver, self.conversion_service)

        result = binder.bind("", ServerConfig)

        self.assertFalse(result.has_errors)
        config = result.target
        self.assertEqual("top-level-server", config.name)
        self.assertEqual(7777, config.port)
        self.assertTrue(config.debug)

    def test_bind_non_dataclass(self):
        """测试绑定非数据类"""

        class RegularClass:
            pass

        result = self.binder.bind("test", RegularClass)

        self.assertTrue(result.has_errors)
        self.assertEqual(1, len(result.errors))
        self.assertIn("not a dataclass", result.errors[0].message)

    def test_bind_with_conversion_error(self):
        """测试类型转换错误"""
        error_properties = {"error.port": "invalid_number"}
        resolver = MockPropertyResolver(error_properties)
        binder = Binder(resolver, self.conversion_service)

        result = binder.bind("error", ServerConfig)

        self.assertTrue(result.has_errors)
        errors = result.get_errors_for_property("error.port")
        self.assertEqual(1, len(errors))

    def test_configuration_properties_decorator(self):
        """测试配置属性装饰器"""
        # 检查装饰器是否正确设置了前缀
        self.assertTrue(hasattr(AppConfig, "_config_prefix"))
        self.assertEqual("app", AppConfig._config_prefix)


if __name__ == "__main__":
    unittest.main()
