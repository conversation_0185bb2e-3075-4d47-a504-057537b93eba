#!/usr/bin/env python
"""
* @author: cz
* @description: 属性源模块 - 提供各种属性源的实现和管理功能
"""

import os
import sys
from abc import ABC, abstractmethod
from collections.abc import Iterator
from typing import Any, Optional


class PropertySource(ABC):
    """属性源抽象基类"""

    def __init__(self, name: str, priority: int = 0):
        """初始化属性源

        Args:
            name: 属性源名称
            priority: 优先级,数字越大优先级越高
        """
        self._name = name
        self._priority = priority

    @property
    def name(self) -> str:
        """属性源名称"""
        return self._name

    @property
    def priority(self) -> int:
        """属性源优先级"""
        return self._priority

    @abstractmethod
    def get_property(self, key: str) -> Any:
        """获取属性值

        Args:
            key: 属性键

        Returns:
            属性值,如果不存在返回 None
        """
        raise NotImplementedError

    def contains_property(self, key: str) -> bool:
        """检查是否包含指定属性

        Args:
            key: 属性键

        Returns:
            如果包含该属性返回 True
        """
        return self.get_property(key) is not None

    def __str__(self) -> str:
        return f"{self.__class__.__name__}[{self._name}]"


class MutablePropertySources:
    """可变属性源管理器

    管理多个属性源,按优先级提供属性查找功能.
    """

    def __init__(self):
        """初始化属性源管理器"""
        self._property_sources: list[PropertySource] = []

    def add_first(self, property_source: PropertySource) -> None:
        """添加到最高优先级

        Args:
            property_source: 属性源
        """
        self._property_sources.insert(0, property_source)
        self._sort_by_priority()

    def add_last(self, property_source: PropertySource) -> None:
        """添加到最低优先级

        Args:
            property_source: 属性源
        """
        self._property_sources.append(property_source)
        self._sort_by_priority()

    def add_before(self, relative_name: str, property_source: PropertySource) -> None:
        """在指定属性源之前添加

        Args:
            relative_name: 参考属性源名称
            property_source: 要添加的属性源
        """
        for i, ps in enumerate(self._property_sources):
            if ps.name == relative_name:
                self._property_sources.insert(i, property_source)
                self._sort_by_priority()
                return
        # 如果没找到参考属性源,添加到最后
        self.add_last(property_source)

    def add_after(self, relative_name: str, property_source: PropertySource) -> None:
        """在指定属性源之后添加

        Args:
            relative_name: 参考属性源名称
            property_source: 要添加的属性源
        """
        for i, ps in enumerate(self._property_sources):
            if ps.name == relative_name:
                self._property_sources.insert(i + 1, property_source)
                self._sort_by_priority()
                return
        # 如果没找到参考属性源,添加到最后
        self.add_last(property_source)

    def remove(self, name: str) -> Optional[PropertySource]:
        """移除指定名称的属性源

        Args:
            name: 属性源名称

        Returns:
            被移除的属性源,如果不存在返回 None
        """
        for i, ps in enumerate(self._property_sources):
            if ps.name == name:
                return self._property_sources.pop(i)
        return None

    def replace(self, name: str, property_source: PropertySource) -> None:
        """替换指定名称的属性源

        Args:
            name: 要替换的属性源名称
            property_source: 新的属性源
        """
        for i, ps in enumerate(self._property_sources):
            if ps.name == name:
                self._property_sources[i] = property_source
                self._sort_by_priority()
                return
        # 如果没找到,添加到最后
        self.add_last(property_source)

    def get(self, name: str) -> Optional[PropertySource]:
        """获取指定名称的属性源

        Args:
            name: 属性源名称

        Returns:
            属性源实例,如果不存在返回 None
        """
        for ps in self._property_sources:
            if ps.name == name:
                return ps
        return None

    def contains(self, name: str) -> bool:
        """检查是否包含指定名称的属性源

        Args:
            name: 属性源名称

        Returns:
            如果包含返回 True
        """
        return self.get(name) is not None

    def get_property(self, key: str, default: Any = None) -> Any:
        """从所有属性源中获取属性值

        按优先级顺序查找,返回第一个找到的值.

        Args:
            key: 属性键
            default: 默认值

        Returns:
            属性值,如果所有属性源都没有该属性则返回默认值
        """
        for property_source in self._property_sources:
            value = property_source.get_property(key)
            if value is not None:
                return value
        return default

    def contains_property(self, key: str) -> bool:
        """检查是否包含指定属性

        Args:
            key: 属性键

        Returns:
            如果任一属性源包含该属性返回 True
        """
        return any(ps.contains_property(key) for ps in self._property_sources)

    def _sort_by_priority(self) -> None:
        """按优先级排序属性源"""
        self._property_sources.sort(key=lambda ps: ps.priority, reverse=True)

    def __iter__(self) -> Iterator[PropertySource]:
        """迭代所有属性源"""
        return iter(self._property_sources)

    def __len__(self) -> int:
        """获取属性源数量"""
        return len(self._property_sources)


class MapPropertySource(PropertySource):
    """基于字典的属性源"""

    def __init__(self, name: str, properties: dict, priority: int = 0):
        """初始化字典属性源

        Args:
            name: 属性源名称
            properties: 属性字典
            priority: 优先级
        """
        super().__init__(name, priority)
        self._properties = properties or {}

    def get_property(self, key: str) -> Any:
        """从字典获取属性值

        Args:
            key: 属性键

        Returns:
            属性值
        """
        return self._properties.get(key)

    def contains_property(self, key: str) -> bool:
        """检查是否包含指定属性"""
        return key in self._properties

    @property
    def properties(self) -> dict:
        """获取属性字典的副本"""
        return self._properties.copy()


class SystemEnvironmentPropertySource(PropertySource):
    """系统环境变量属性源"""

    def __init__(self, name: str = "systemEnvironment", priority: int = None):
        """初始化系统环境变量属性源

        Args:
            name: 属性源名称
            priority: 优先级,如果为None则使用配置优先级管理器的默认值
        """
        if priority is None:
            from .priority import ConfigurationLayer, get_priority

            priority = get_priority().get_priority_for_source("systemEnvironment", ConfigurationLayer.SYSTEM)

        super().__init__(name, priority)

    def get_property(self, key: str) -> Any:
        """从系统环境变量获取属性值

        支持以下格式的键名转换:
        - mini.app.name -> MINI_APP_NAME
        - mini.app-name -> MINI_APP_NAME

        Args:
            key: 属性键

        Returns:
            环境变量值
        """
        # 尝试直接获取
        value = os.environ.get(key)
        if value is not None:
            return value

        # 转换为环境变量格式
        environment_key = key.upper().replace(".", "_").replace("-", "_")
        return os.environ.get(environment_key)


class CommandLinePropertySource(PropertySource):
    """命令行参数属性源

    解析命令行参数并提供属性访问,支持多种参数格式:
    - --key=value
    - --key value
    - -Dkey=value (Java 风格)
    """

    def __init__(self, name: str = "commandLineArgs", args: Optional[list[str]] = None, priority: int = 1000):
        """初始化命令行属性源

        Args:
            name: 属性源名称
            args: 命令行参数列表,如果为 None 则使用 sys.argv[1:]
            priority: 优先级,默认为最高优先级
        """
        super().__init__(name, priority)
        self._args = args if args is not None else sys.argv[1:]
        self._properties = self._parse_args(self._args)

    def get_property(self, key: str) -> Any:
        """获取命令行参数值"""
        return self._properties.get(key)

    def contains_property(self, key: str) -> bool:
        """检查是否包含指定属性"""
        return key in self._properties

    @property
    def properties(self) -> dict[str, Any]:
        """获取所有属性的副本"""
        return self._properties.copy()

    def _parse_args(self, args: list[str]) -> dict[str, Any]:
        """解析命令行参数"""
        properties = {}
        i = 0

        while i < len(args):
            arg = args[i]

            if arg.startswith("-D"):
                # Java 风格参数: -Dkey=value
                self._parse_java_style_arg(arg, properties)
            elif arg.startswith("--"):
                # 长选项: --key=value 或 --key value
                i = self._parse_long_option(args, i, properties)
            elif arg.startswith("-") and i + 1 < len(args) and not args[i + 1].startswith("-"):
                # 短选项: -k value (跳过,不处理)
                i += 1  # 跳过值

            i += 1

        return properties

    def _parse_java_style_arg(self, arg: str, properties: dict[str, Any]) -> None:
        """解析 Java 风格参数 -Dkey=value"""
        if "=" in arg:
            key_value = arg[2:]  # 移除 -D 前缀
            key, value = key_value.split("=", 1)
            properties[key] = self._convert_value(value)
        else:
            # -Dkey 形式,设置为 true
            key = arg[2:]
            properties[key] = True

    def _parse_long_option(self, args: list[str], index: int, properties: dict[str, Any]) -> int:
        """解析长选项 --key=value 或 --key value"""
        arg = args[index]
        key = arg[2:]  # 移除 -- 前缀

        if "=" in key:
            # --key=value 形式
            key, value = key.split("=", 1)
            properties[key] = self._convert_value(value)
            return index
        else:
            # --key value 形式
            if index + 1 < len(args) and not args[index + 1].startswith("-"):
                value = args[index + 1]
                properties[key] = self._convert_value(value)
                return index + 1
            else:
                # --key 形式,设置为 true
                properties[key] = True
                return index

    def _convert_value(self, value: str) -> Any:
        """转换参数值到合适的类型"""
        # 尝试转换为布尔值
        if value.lower() in ("true", "false"):
            return value.lower() == "true"

        # 尝试转换为整数
        try:
            return int(value)
        except ValueError:
            pass

        # 尝试转换为浮点数
        try:
            return float(value)
        except ValueError:
            pass

        # 返回原始字符串
        return value
