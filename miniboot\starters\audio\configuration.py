#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频播放 Starter 自动配置
"""

from miniboot.annotations import Bean, ConditionalOnProperty
from miniboot.autoconfigure.base import StarterAutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata

from .properties import AudioProperties
from .service import AudioService


@ConditionalOnProperty(prefix="miniboot.audio", name="enabled", having_value="true", match_if_missing=True)
class AudioAutoConfiguration(StarterAutoConfiguration):
    """音频播放 Starter 自动配置类

    当 miniboot.audio.enabled=true 时自动配置音频播放功能.
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="audio-auto-configuration",
            description="音频播放功能自动配置",
            priority=300,  # 中等优先级
            auto_configure_after=[],  # 不依赖其他配置
        )

    def get_starter_name(self) -> str:
        """获取Starter名称"""
        return "miniboot-starter-audio"

    def get_starter_version(self) -> str:
        """获取Starter版本"""
        return "1.0.0"

    def get_starter_description(self) -> str:
        """获取Starter描述"""
        return "Mini-Boot 音频播放功能Starter,支持音频文件播放和TTS"

    def get_configuration_properties_classes(self) -> list[type]:
        """获取配置属性类列表"""
        return [AudioProperties]

    @Bean
    def audio_properties(self) -> AudioProperties:
        """创建音频配置属性Bean"""
        return AudioProperties()

    @Bean
    def audio_service(self, audio_properties: AudioProperties) -> AudioService:
        """创建统一的音频服务Bean"""
        return AudioService(audio_properties)
