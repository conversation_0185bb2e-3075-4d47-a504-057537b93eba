"""
Mini-Boot Actuator 性能基准测试运行器

自动运行所有基准测试并生成详细的性能报告
"""

import sys
import time
import json
import unittest
from datetime import datetime
from typing import Dict, List, Any
from io import StringIO
import platform
import psutil

from test_actuator_benchmarks import (
    StartupBenchmarkTestCase,
    MemoryBenchmarkTestCase,
    ResponseBenchmarkTestCase,
    ConcurrencyBenchmarkTestCase,
    StressBenchmarkTestCase,
)


class BenchmarkRunner:
    """性能基准测试运行器"""

    def __init__(self):
        self.results = {}
        self.system_info = self._collect_system_info()
        self.start_time = None
        self.end_time = None

    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        return {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "cpu_freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "timestamp": datetime.now().isoformat(),
        }

    def run_all_benchmarks(self) -> Dict[str, Any]:
        """运行所有基准测试"""
        print("🚀 开始运行 Mini-Boot Actuator 性能基准测试")
        print("=" * 60)

        self.start_time = time.perf_counter()

        # 定义测试套件
        test_suites = [
            ("启动时间基准测试", StartupBenchmarkTestCase),
            ("内存占用基准测试", MemoryBenchmarkTestCase),
            ("响应延迟基准测试", ResponseBenchmarkTestCase),
            ("并发处理基准测试", ConcurrencyBenchmarkTestCase),
            ("压力测试和稳定性测试", StressBenchmarkTestCase),
        ]

        overall_results = {"system_info": self.system_info, "test_results": {}, "summary": {}}

        total_tests = 0
        total_failures = 0
        total_errors = 0

        for suite_name, test_class in test_suites:
            print(f"\n📊 运行 {suite_name}")
            print("-" * 40)

            # 创建测试套件
            suite = unittest.TestLoader().loadTestsFromTestCase(test_class)

            # 捕获测试输出
            stream = StringIO()
            runner = unittest.TextTestRunner(stream=stream, verbosity=2, buffer=True)

            # 运行测试
            result = runner.run(suite)

            # 收集结果
            suite_result = {
                "tests_run": result.testsRun,
                "failures": len(result.failures),
                "errors": len(result.errors),
                "success_rate": (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun if result.testsRun > 0 else 0,
                "output": stream.getvalue(),
                "failure_details": [str(failure[1]) for failure in result.failures],
                "error_details": [str(error[1]) for error in result.errors],
            }

            overall_results["test_results"][suite_name] = suite_result

            # 累计统计
            total_tests += result.testsRun
            total_failures += len(result.failures)
            total_errors += len(result.errors)

            # 打印测试输出
            print(stream.getvalue())

            # 打印套件结果
            print(f"✅ {suite_name} 完成:")
            print(f"   测试数量: {result.testsRun}")
            print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
            print(f"   失败: {len(result.failures)}")
            print(f"   错误: {len(result.errors)}")
            print(f"   成功率: {suite_result['success_rate']:.1%}")

        self.end_time = time.perf_counter()
        total_duration = self.end_time - self.start_time

        # 生成总结
        overall_results["summary"] = {
            "total_duration": total_duration,
            "total_tests": total_tests,
            "total_failures": total_failures,
            "total_errors": total_errors,
            "overall_success_rate": (total_tests - total_failures - total_errors) / total_tests if total_tests > 0 else 0,
            "performance_targets": self._evaluate_performance_targets(overall_results),
        }

        return overall_results

    def _evaluate_performance_targets(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """评估性能目标达成情况"""
        targets = {
            "startup_time_target": {"description": "启动时间 < 50ms", "achieved": False, "details": "需要从测试输出中解析"},
            "memory_reduction_target": {"description": "内存减少 44%", "achieved": False, "details": "需要从测试输出中解析"},
            "response_improvement_target": {"description": "响应时间改善 80%", "achieved": False, "details": "需要从测试输出中解析"},
            "concurrency_improvement_target": {"description": "并发性能提升 4x", "achieved": False, "details": "需要从测试输出中解析"},
            "stability_target": {"description": "高并发稳定性 > 98%", "achieved": False, "details": "需要从测试输出中解析"},
        }

        # 这里可以添加更复杂的解析逻辑来从测试输出中提取具体的性能数据
        # 目前返回基本结构
        return targets

    def generate_report(self, results: Dict[str, Any], output_file: str = None) -> str:
        """生成性能测试报告"""
        report_lines = []

        # 报告头部
        report_lines.extend(
            [
                "# Mini-Boot Actuator 非阻塞架构性能基准测试报告",
                "",
                f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"**测试时长**: {results['summary']['total_duration']:.2f}秒",
                "",
                "## 系统环境",
                "",
                f"- **操作系统**: {results['system_info']['platform']}",
                f"- **Python版本**: {results['system_info']['python_version']}",
                f"- **CPU核心数**: {results['system_info']['cpu_count']}",
                f"- **总内存**: {results['system_info']['memory_total'] / 1024 / 1024 / 1024:.2f}GB",
                f"- **可用内存**: {results['system_info']['memory_available'] / 1024 / 1024 / 1024:.2f}GB",
                "",
            ]
        )

        # 总体结果
        summary = results["summary"]
        report_lines.extend(
            [
                "## 测试总结",
                "",
                f"- **总测试数**: {summary['total_tests']}",
                f"- **成功**: {summary['total_tests'] - summary['total_failures'] - summary['total_errors']}",
                f"- **失败**: {summary['total_failures']}",
                f"- **错误**: {summary['total_errors']}",
                f"- **总成功率**: {summary['overall_success_rate']:.1%}",
                "",
            ]
        )

        # 性能目标达成情况
        report_lines.extend(["## 性能目标达成情况", ""])

        for target_name, target_info in summary["performance_targets"].items():
            status = "✅" if target_info["achieved"] else "❌"
            report_lines.append(f"- {status} **{target_info['description']}**")

        report_lines.append("")

        # 详细测试结果
        report_lines.extend(["## 详细测试结果", ""])

        for suite_name, suite_result in results["test_results"].items():
            report_lines.extend(
                [
                    f"### {suite_name}",
                    "",
                    f"- **测试数量**: {suite_result['tests_run']}",
                    f"- **成功率**: {suite_result['success_rate']:.1%}",
                    f"- **失败数**: {suite_result['failures']}",
                    f"- **错误数**: {suite_result['errors']}",
                    "",
                ]
            )

            if suite_result["failure_details"]:
                report_lines.extend(["**失败详情**:", "```"])
                for failure in suite_result["failure_details"]:
                    report_lines.append(failure)
                report_lines.extend(["```", ""])

            if suite_result["error_details"]:
                report_lines.extend(["**错误详情**:", "```"])
                for error in suite_result["error_details"]:
                    report_lines.append(error)
                report_lines.extend(["```", ""])

        # 生成报告文本
        report_text = "\n".join(report_lines)

        # 保存到文件
        if output_file:
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(report_text)
            print(f"\n📄 性能测试报告已保存到: {output_file}")

        return report_text

    def save_raw_results(self, results: Dict[str, Any], output_file: str):
        """保存原始测试结果为JSON"""
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        print(f"📊 原始测试数据已保存到: {output_file}")


def main():
    """主函数"""
    runner = BenchmarkRunner()

    try:
        # 运行所有基准测试
        results = runner.run_all_benchmarks()

        # 生成报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"actuator_benchmark_report_{timestamp}.md"
        data_file = f"actuator_benchmark_data_{timestamp}.json"

        # 生成Markdown报告
        report_text = runner.generate_report(results, report_file)

        # 保存原始数据
        runner.save_raw_results(results, data_file)

        # 打印总结
        print("\n" + "=" * 60)
        print("🎉 性能基准测试完成!")
        print(f"📊 总成功率: {results['summary']['overall_success_rate']:.1%}")
        print(f"⏱️  总耗时: {results['summary']['total_duration']:.2f}秒")
        print(f"📄 报告文件: {report_file}")
        print(f"📊 数据文件: {data_file}")

        # 返回退出码
        if results["summary"]["total_failures"] > 0 or results["summary"]["total_errors"] > 0:
            sys.exit(1)
        else:
            sys.exit(0)

    except Exception as e:
        print(f"❌ 基准测试运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
