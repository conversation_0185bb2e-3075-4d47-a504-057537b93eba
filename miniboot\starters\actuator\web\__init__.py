#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Actuator Web 集成模块

提供 Actuator 与 Web 框架的集成功能，包括：
- Web 集成检查机制
- 动态路由注册
- 中间件集成
- 条件化 Web 自动配置

主要组件：
- ActuatorWebConditions: Actuator Web 集成条件检查
- ActuatorRouteRegistrar: 动态路由注册器
- WebMiddleware: Web 中间件集成
- WebAutoConfiguration: Web 自动配置类

使用示例：
    # 检查 Web 集成条件
    conditions = ActuatorWebConditions()
    if conditions.should_integrate():
        # 启用 Web 集成
        pass
"""

from .conditions import ActuatorWebConditions
from .routes import ActuatorRouteRegistrar

__all__ = [
    'ActuatorWebConditions',
    'ActuatorRouteRegistrar',
]
