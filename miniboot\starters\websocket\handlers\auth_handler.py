#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 认证处理器
"""

try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False
    jwt = None

from datetime import datetime, timed<PERSON>ta
from typing import Any, Optional

from fastapi import WebSocket

from ..exceptions import WebSocketAuthenticationException
from ..properties import SecurityConfig


class WebSocketAuthHandler:
    """WebSocket 认证处理器

    负责处理 WebSocket 连接的认证和授权逻辑.
    """

    def __init__(self, security_config: SecurityConfig):
        """初始化认证处理器

        Args:
            security_config: 安全配置
        """
        self.security_config = security_config

    async def authenticate(self, websocket: WebSocket) -> Optional[str]:
        """认证 WebSocket 连接

        Args:
            websocket: WebSocket 连接对象

        Returns:
            Optional[str]: 用户 ID,认证失败返回 None

        Raises:
            WebSocketAuthenticationException: 认证过程中发生错误
        """
        if not self.security_config.auth.enabled:
            return None

        try:
            # 1. 从不同来源获取 token
            token = await self._extract_token(websocket)
            if not token:
                raise WebSocketAuthenticationException("No authentication token provided")

            # 2. 验证 token
            if self.security_config.jwt.enabled:
                return await self._verify_jwt_token(token)
            else:
                return await self._verify_simple_token(token)

        except WebSocketAuthenticationException:
            raise
        except Exception as e:
            raise WebSocketAuthenticationException(f"Authentication failed: {e}", cause=e)

    async def _extract_token(self, websocket: WebSocket) -> Optional[str]:
        """从 WebSocket 连接中提取认证 token

        Args:
            websocket: WebSocket 连接对象

        Returns:
            Optional[str]: 提取的 token
        """
        # 1. 从查询参数获取
        query_params = dict(websocket.query_params)
        token_param = self.security_config.auth.token_query_param
        if token_param in query_params:
            return query_params[token_param]

        # 2. 从 headers 获取
        headers = dict(websocket.headers)
        auth_header = self.security_config.auth.token_header.lower()
        if auth_header in headers:
            auth_value = headers[auth_header]
            # 处理 Bearer token 格式
            if auth_value.startswith("Bearer "):
                return auth_value[7:]
            return auth_value

        # 3. 从 cookies 获取
        cookies = websocket.cookies
        cookie_name = self.security_config.auth.token_cookie
        if cookie_name in cookies:
            return cookies[cookie_name]

        return None

    async def _verify_jwt_token(self, token: str) -> str:
        """验证 JWT token

        Args:
            token: JWT token

        Returns:
            str: 用户 ID

        Raises:
            WebSocketAuthenticationException: JWT 验证失败
        """
        try:
            # 解码 JWT token
            payload = jwt.decode(token, self.security_config.jwt.secret_key, algorithms=["HS256"], issuer=self.security_config.jwt.issuer)

            # 检查过期时间
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.now():
                raise WebSocketAuthenticationException("JWT token has expired")

            # 提取用户 ID
            user_id = payload.get("sub") or payload.get("user_id")
            if not user_id:
                raise WebSocketAuthenticationException("JWT token does not contain user ID")

            return str(user_id)

        except jwt.InvalidTokenError as e:
            raise WebSocketAuthenticationException(f"Invalid JWT token: {e}", cause=e)
        except Exception as e:
            raise WebSocketAuthenticationException(f"JWT verification failed: {e}", cause=e)

    async def _verify_simple_token(self, token: str) -> str:
        """验证简单 token(非 JWT)

        Args:
            token: 简单 token

        Returns:
            str: 用户 ID

        Raises:
            WebSocketAuthenticationException: token 验证失败
        """
        # 这里可以实现自定义的 token 验证逻辑
        # 例如:查询数据库、调用外部认证服务等

        # 示例实现:假设 token 格式为 "user_id:timestamp:signature"
        try:
            parts = token.split(":")
            if len(parts) != 3:
                raise WebSocketAuthenticationException("Invalid token format")

            user_id, timestamp_str, signature = parts

            # 验证时间戳(可选)
            timestamp = int(timestamp_str)
            current_time = int(datetime.now().timestamp())
            if current_time - timestamp > self.security_config.jwt.expiration_time:
                raise WebSocketAuthenticationException("Token has expired")

            # 验证签名(简化实现)
            expected_signature = self._generate_signature(user_id, timestamp_str)
            if signature != expected_signature:
                raise WebSocketAuthenticationException("Invalid token signature")

            return user_id

        except ValueError as e:
            raise WebSocketAuthenticationException(f"Invalid token format: {e}", cause=e)
        except Exception as e:
            raise WebSocketAuthenticationException(f"Token verification failed: {e}", cause=e)

    def _generate_signature(self, user_id: str, timestamp: str) -> str:
        """生成 token 签名

        Args:
            user_id: 用户 ID
            timestamp: 时间戳

        Returns:
            str: 签名
        """
        import hashlib
        import hmac

        message = f"{user_id}:{timestamp}"
        signature = hmac.new(self.security_config.jwt.secret_key.encode(), message.encode(), hashlib.sha256).hexdigest()

        return signature[:16]  # 取前16位作为签名

    def generate_jwt_token(self, user_id: str, extra_claims: Optional[dict[str, Any]] = None) -> str:
        """生成 JWT token(用于测试或其他用途)

        Args:
            user_id: 用户 ID
            extra_claims: 额外的声明

        Returns:
            str: JWT token
        """
        now = datetime.now()
        exp = now + timedelta(seconds=self.security_config.jwt.expiration_time)

        payload = {"sub": user_id, "iss": self.security_config.jwt.issuer, "iat": int(now.timestamp()), "exp": int(exp.timestamp())}

        if extra_claims:
            payload.update(extra_claims)

        return jwt.encode(payload, self.security_config.jwt.secret_key, algorithm="HS256")

    def generate_simple_token(self, user_id: str) -> str:
        """生成简单 token(用于测试或其他用途)

        Args:
            user_id: 用户 ID

        Returns:
            str: 简单 token
        """
        timestamp = str(int(datetime.now().timestamp()))
        signature = self._generate_signature(user_id, timestamp)
        return f"{user_id}:{timestamp}:{signature}"

    async def authorize(self, user_id: str, resource: str, action: str) -> bool:
        """授权检查(可扩展)

        Args:
            user_id: 用户 ID
            resource: 资源
            action: 操作

        Returns:
            bool: 是否有权限
        """
        # 这里可以实现自定义的授权逻辑
        # 例如:基于角色的访问控制(RBAC)、基于属性的访问控制(ABAC)等

        # 示例实现:简单的权限检查
        return True  # 默认允许所有操作

    def is_origin_allowed(self, origin: str) -> bool:
        """检查来源是否被允许

        Args:
            origin: 请求来源

        Returns:
            bool: 是否允许
        """
        allowed_origins = self.security_config.allowed_origins

        # 如果配置了 "*",允许所有来源
        if "*" in allowed_origins:
            return True

        # 检查精确匹配
        if origin in allowed_origins:
            return True

        # 检查通配符匹配(简化实现)
        for allowed_origin in allowed_origins:
            if allowed_origin.endswith("*"):
                prefix = allowed_origin[:-1]
                if origin.startswith(prefix):
                    return True

        return False
