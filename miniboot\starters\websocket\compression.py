#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 消息压缩组件
"""

import gzip
import json
import lzma
import zlib
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Optional, Union

from loguru import logger

from miniboot.annotations import Component

from .exceptions import WebSocketMessageException


class CompressionAlgorithm(Enum):
    """压缩算法枚举"""

    NONE = "none"
    GZIP = "gzip"
    DEFLATE = "deflate"
    LZMA = "lzma"
    BROTLI = "brotli"


@dataclass
class CompressionResult:
    """压缩结果"""

    compressed_data: bytes
    original_size: int
    compressed_size: int
    algorithm: CompressionAlgorithm
    compression_ratio: float

    @property
    def space_saved(self) -> int:
        """节省的空间(字节)"""
        return self.original_size - self.compressed_size

    @property
    def space_saved_percentage(self) -> float:
        """节省空间的百分比"""
        if self.original_size == 0:
            return 0.0
        return (self.space_saved / self.original_size) * 100


@dataclass
class DecompressionResult:
    """解压结果"""

    decompressed_data: Union[str, bytes]
    original_size: int
    compressed_size: int
    algorithm: CompressionAlgorithm


class CompressionStrategy(ABC):
    """压缩策略抽象基类"""

    @abstractmethod
    def compress(self, data: Union[str, bytes], level: int = 6) -> bytes:
        """压缩数据

        Args:
            data: 要压缩的数据
            level: 压缩级别 (1-9)

        Returns:
            bytes: 压缩后的数据
        """
        pass

    @abstractmethod
    def decompress(self, data: bytes) -> Union[str, bytes]:
        """解压数据

        Args:
            data: 压缩的数据

        Returns:
            Union[str, bytes]: 解压后的数据
        """
        pass

    @abstractmethod
    def get_algorithm(self) -> CompressionAlgorithm:
        """获取压缩算法

        Returns:
            CompressionAlgorithm: 压缩算法
        """
        pass


class GzipCompressionStrategy(CompressionStrategy):
    """Gzip 压缩策略"""

    def compress(self, data: Union[str, bytes], level: int = 6) -> bytes:
        """使用 Gzip 压缩数据"""
        if isinstance(data, str):
            data = data.encode("utf-8")
        return gzip.compress(data, compresslevel=level)

    def decompress(self, data: bytes) -> bytes:
        """使用 Gzip 解压数据"""
        return gzip.decompress(data)

    def get_algorithm(self) -> CompressionAlgorithm:
        return CompressionAlgorithm.GZIP


class DeflateCompressionStrategy(CompressionStrategy):
    """Deflate 压缩策略"""

    def compress(self, data: Union[str, bytes], level: int = 6) -> bytes:
        """使用 Deflate 压缩数据"""
        if isinstance(data, str):
            data = data.encode("utf-8")
        return zlib.compress(data, level)

    def decompress(self, data: bytes) -> bytes:
        """使用 Deflate 解压数据"""
        return zlib.decompress(data)

    def get_algorithm(self) -> CompressionAlgorithm:
        return CompressionAlgorithm.DEFLATE


class LzmaCompressionStrategy(CompressionStrategy):
    """LZMA 压缩策略"""

    def compress(self, data: Union[str, bytes], level: int = 6) -> bytes:
        """使用 LZMA 压缩数据"""
        if isinstance(data, str):
            data = data.encode("utf-8")
        return lzma.compress(data, preset=level)

    def decompress(self, data: bytes) -> bytes:
        """使用 LZMA 解压数据"""
        return lzma.decompress(data)

    def get_algorithm(self) -> CompressionAlgorithm:
        return CompressionAlgorithm.LZMA


class BrotliCompressionStrategy(CompressionStrategy):
    """Brotli 压缩策略"""

    def __init__(self):
        """初始化 Brotli 压缩策略"""
        try:
            import brotli

            self.brotli = brotli
        except ImportError:
            logger.warning("Brotli library not available, falling back to gzip")
            self.brotli = None

    def compress(self, data: Union[str, bytes], level: int = 6) -> bytes:
        """使用 Brotli 压缩数据"""
        if not self.brotli:
            # 回退到 gzip
            return GzipCompressionStrategy().compress(data, level)

        if isinstance(data, str):
            data = data.encode("utf-8")
        return self.brotli.compress(data, quality=level)

    def decompress(self, data: bytes) -> bytes:
        """使用 Brotli 解压数据"""
        if not self.brotli:
            # 回退到 gzip
            return GzipCompressionStrategy().decompress(data)

        return self.brotli.decompress(data)

    def get_algorithm(self) -> CompressionAlgorithm:
        return CompressionAlgorithm.BROTLI


@Component
class WebSocketMessageCompressor:
    """WebSocket 消息压缩器

    提供高效的消息压缩和解压功能,支持多种压缩算法,
    自适应压缩策略,以及性能优化.
    """

    def __init__(self):
        """初始化消息压缩器"""
        # 压缩策略映射
        self._strategies: dict[CompressionAlgorithm, CompressionStrategy] = {
            CompressionAlgorithm.GZIP: GzipCompressionStrategy(),
            CompressionAlgorithm.DEFLATE: DeflateCompressionStrategy(),
            CompressionAlgorithm.LZMA: LzmaCompressionStrategy(),
            CompressionAlgorithm.BROTLI: BrotliCompressionStrategy(),
        }

        # 默认配置
        self.default_algorithm = CompressionAlgorithm.GZIP
        self.default_level = 6
        self.min_compress_size = 1024  # 最小压缩大小(字节)
        self.max_compress_size = 10 * 1024 * 1024  # 最大压缩大小(10MB)

        # 自适应压缩配置
        self.enable_adaptive_compression = True
        self.compression_ratio_threshold = 0.8  # 压缩比阈值

        # 统计信息
        self._stats = {
            "total_compressions": 0,
            "total_decompressions": 0,
            "bytes_before_compression": 0,
            "bytes_after_compression": 0,
            "compression_time_ms": 0,
            "decompression_time_ms": 0,
            "algorithm_usage": {alg.value: 0 for alg in CompressionAlgorithm},
        }

        logger.debug("WebSocketMessageCompressor initialized")

    def compress_message(
        self, data: Union[str, bytes, dict], algorithm: Optional[CompressionAlgorithm] = None, level: Optional[int] = None
    ) -> CompressionResult:
        """压缩消息

        Args:
            data: 要压缩的数据
            algorithm: 压缩算法,默认使用配置的算法
            level: 压缩级别 (1-9),默认使用配置的级别

        Returns:
            CompressionResult: 压缩结果

        Raises:
            WebSocketMessageException: 压缩失败时抛出
        """
        try:
            import time

            start_time = time.time()

            # 预处理数据
            processed_data = self._preprocess_data(data)
            original_size = len(processed_data)

            # 检查是否需要压缩
            if not self._should_compress(processed_data):
                return CompressionResult(
                    compressed_data=processed_data,
                    original_size=original_size,
                    compressed_size=original_size,
                    algorithm=CompressionAlgorithm.NONE,
                    compression_ratio=1.0,
                )

            # 选择压缩算法
            algorithm = algorithm or self._select_algorithm(processed_data)
            level = level or self.default_level

            # 执行压缩
            strategy = self._strategies[algorithm]
            compressed_data = strategy.compress(processed_data, level)
            compressed_size = len(compressed_data)

            # 计算压缩比
            compression_ratio = compressed_size / original_size if original_size > 0 else 1.0

            # 检查压缩效果
            if self.enable_adaptive_compression and compression_ratio > self.compression_ratio_threshold:
                # 压缩效果不好,返回原始数据
                return CompressionResult(
                    compressed_data=processed_data,
                    original_size=original_size,
                    compressed_size=original_size,
                    algorithm=CompressionAlgorithm.NONE,
                    compression_ratio=1.0,
                )

            # 更新统计信息
            compression_time = (time.time() - start_time) * 1000
            self._update_compression_stats(original_size, compressed_size, compression_time, algorithm)

            return CompressionResult(
                compressed_data=compressed_data,
                original_size=original_size,
                compressed_size=compressed_size,
                algorithm=algorithm,
                compression_ratio=compression_ratio,
            )

        except Exception as e:
            logger.error(f"Message compression failed: {e}")
            raise WebSocketMessageException(f"Compression failed: {e}", cause=e)

    def decompress_message(self, compressed_data: bytes, algorithm: CompressionAlgorithm, return_string: bool = True) -> DecompressionResult:
        """解压消息

        Args:
            compressed_data: 压缩的数据
            algorithm: 压缩算法
            return_string: 是否返回字符串格式

        Returns:
            DecompressionResult: 解压结果

        Raises:
            WebSocketMessageException: 解压失败时抛出
        """
        try:
            import time

            start_time = time.time()

            compressed_size = len(compressed_data)

            # 如果没有压缩,直接返回
            if algorithm == CompressionAlgorithm.NONE:
                decompressed_data = compressed_data.decode("utf-8") if return_string else compressed_data
                return DecompressionResult(
                    decompressed_data=decompressed_data, original_size=compressed_size, compressed_size=compressed_size, algorithm=algorithm
                )

            # 执行解压
            strategy = self._strategies[algorithm]
            decompressed_bytes = strategy.decompress(compressed_data)
            original_size = len(decompressed_bytes)

            # 转换为字符串(如果需要)
            if return_string:
                try:
                    decompressed_data = decompressed_bytes.decode("utf-8")
                except UnicodeDecodeError:
                    decompressed_data = decompressed_bytes
            else:
                decompressed_data = decompressed_bytes

            # 更新统计信息
            decompression_time = (time.time() - start_time) * 1000
            self._update_decompression_stats(decompression_time)

            return DecompressionResult(
                decompressed_data=decompressed_data, original_size=original_size, compressed_size=compressed_size, algorithm=algorithm
            )

        except Exception as e:
            logger.error(f"Message decompression failed: {e}")
            raise WebSocketMessageException(f"Decompression failed: {e}", cause=e)

    def _preprocess_data(self, data: Union[str, bytes, dict]) -> bytes:
        """预处理数据

        Args:
            data: 原始数据

        Returns:
            bytes: 处理后的字节数据
        """
        if isinstance(data, dict):
            # JSON 序列化
            json_str = json.dumps(data, ensure_ascii=False, separators=(",", ":"))
            return json_str.encode("utf-8")
        elif isinstance(data, str):
            return data.encode("utf-8")
        elif isinstance(data, bytes):
            return data
        else:
            # 其他类型转换为字符串
            return str(data).encode("utf-8")

    def _should_compress(self, data: bytes) -> bool:
        """判断是否应该压缩数据

        Args:
            data: 数据

        Returns:
            bool: 是否应该压缩
        """
        data_size = len(data)

        # 检查大小限制
        if data_size < self.min_compress_size:
            return False

        if data_size > self.max_compress_size:
            return False

        # 可以添加更多启发式规则
        # 例如:检查数据类型、重复性等

        return True

    def _select_algorithm(self, data: bytes) -> CompressionAlgorithm:
        """选择最佳压缩算法

        Args:
            data: 数据

        Returns:
            CompressionAlgorithm: 选择的算法
        """
        # 简单的算法选择策略
        data_size = len(data)

        if data_size < 10 * 1024:  # 小于 10KB
            return CompressionAlgorithm.GZIP
        elif data_size < 100 * 1024:  # 小于 100KB
            return CompressionAlgorithm.DEFLATE
        else:  # 大文件
            return CompressionAlgorithm.LZMA

    def _update_compression_stats(self, original_size: int, compressed_size: int, compression_time: float, algorithm: CompressionAlgorithm) -> None:
        """更新压缩统计信息

        Args:
            original_size: 原始大小
            compressed_size: 压缩后大小
            compression_time: 压缩时间(毫秒)
            algorithm: 使用的算法
        """
        self._stats["total_compressions"] += 1
        self._stats["bytes_before_compression"] += original_size
        self._stats["bytes_after_compression"] += compressed_size
        self._stats["compression_time_ms"] += compression_time
        self._stats["algorithm_usage"][algorithm.value] += 1

    def _update_decompression_stats(self, decompression_time: float) -> None:
        """更新解压统计信息

        Args:
            decompression_time: 解压时间(毫秒)
        """
        self._stats["total_decompressions"] += 1
        self._stats["decompression_time_ms"] += decompression_time

    def get_compression_stats(self) -> dict[str, Any]:
        """获取压缩统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self._stats.copy()

        # 计算平均值
        if stats["total_compressions"] > 0:
            stats["average_compression_ratio"] = stats["bytes_after_compression"] / stats["bytes_before_compression"]
            stats["average_compression_time_ms"] = stats["compression_time_ms"] / stats["total_compressions"]
            stats["total_bytes_saved"] = stats["bytes_before_compression"] - stats["bytes_after_compression"]
        else:
            stats["average_compression_ratio"] = 0.0
            stats["average_compression_time_ms"] = 0.0
            stats["total_bytes_saved"] = 0

        if stats["total_decompressions"] > 0:
            stats["average_decompression_time_ms"] = stats["decompression_time_ms"] / stats["total_decompressions"]
        else:
            stats["average_decompression_time_ms"] = 0.0

        return stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        self._stats = {
            "total_compressions": 0,
            "total_decompressions": 0,
            "bytes_before_compression": 0,
            "bytes_after_compression": 0,
            "compression_time_ms": 0,
            "decompression_time_ms": 0,
            "algorithm_usage": {alg.value: 0 for alg in CompressionAlgorithm},
        }

    def configure(self, **kwargs) -> None:
        """配置压缩器

        Args:
            **kwargs: 配置参数
        """
        if "default_algorithm" in kwargs:
            self.default_algorithm = kwargs["default_algorithm"]

        if "default_level" in kwargs:
            self.default_level = max(1, min(9, kwargs["default_level"]))

        if "min_compress_size" in kwargs:
            self.min_compress_size = max(0, kwargs["min_compress_size"])

        if "max_compress_size" in kwargs:
            self.max_compress_size = max(self.min_compress_size, kwargs["max_compress_size"])

        if "enable_adaptive_compression" in kwargs:
            self.enable_adaptive_compression = kwargs["enable_adaptive_compression"]

        if "compression_ratio_threshold" in kwargs:
            self.compression_ratio_threshold = max(0.0, min(1.0, kwargs["compression_ratio_threshold"]))

        logger.debug(f"WebSocketMessageCompressor configured: {kwargs}")

    def get_supported_algorithms(self) -> list[CompressionAlgorithm]:
        """获取支持的压缩算法

        Returns:
            List[CompressionAlgorithm]: 支持的算法列表
        """
        return list(self._strategies.keys())
