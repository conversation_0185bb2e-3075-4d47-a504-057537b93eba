#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 事件监听处理器 - 实现@EventListener注解的自动注册

事件监听处理器负责处理@EventListener注解标记的方法,
实现事件监听器的自动注册功能.支持同步和异步事件处理.
"""

import inspect
from typing import Any, Optional

from loguru import logger

from ..errors import ProcessorExecutionError as BeanProcessingError
# 事件系统模块是必需依赖
from ..events.publisher import EventPublisher
from .base import BeanPostProcessor, ProcessorOrder


def is_event_listener(obj: Any) -> bool:
    """检查对象是否有@EventListener注解"""
    return hasattr(obj, "__event_listener_metadata__") and obj.__event_listener_metadata__


class EventListenerProcessor(BeanPostProcessor):
    """
    @EventListener注解处理器

    负责处理@EventListener注解的事件监听器自动注册,支持:
    - 监听器扫描:扫描Bean中的@EventListener方法
    - 自动注册:将监听器注册到事件发布器
    - 事件类型推断:从方法参数自动推断事件类型
    - 条件表达式:支持条件表达式过滤事件
    - 异步处理:支持异步事件监听器

    处理器在Bean初始化后执行,确保Bean完全准备就绪后再注册事件监听器.

    Example:
        # 事件监听器
        @Component
        class UserEventListener:
            @EventListener
            def handle_user_created(self, event: UserCreatedEvent):
                print(f"User created: {event.user_id}")

            @EventListener(condition="event.user_id > 1000", async_exec=True)
            async def handle_vip_user(self, event: UserCreatedEvent):
                await self.send_vip_welcome_email(event.user_id)
    """

    def __init__(self, event_publisher=None):
        """
        初始化事件监听处理器

        Args:
            event_publisher: 事件发布器实例,用于注册监听器
        """
        self._event_publisher = event_publisher or EventPublisher()
        self._processed_beans: set[str] = set()
        self._listener_cache: dict[type, list[tuple]] = {}
        self._registered_handlers: dict[str, list[str]] = {}  # bean_name -> handler_ids

    def set_event_publisher(self, event_publisher) -> None:
        """
        设置事件发布器

        Args:
            event_publisher: 事件发布器实例
        """
        self._event_publisher = event_publisher

    def _do_post_process_before_initialization(self, bean: Any, _bean_name: str) -> Any:
        """
        在Bean初始化前处理(事件监听处理器不需要前处理)

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            原始Bean实例
        """
        return bean

    def _do_post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        在Bean初始化后处理@EventListener注解

        扫描Bean类中的@EventListener注解,注册事件监听器.

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例

        Raises:
            BeanProcessingError: 当事件监听器注册失败时
        """
        if bean is None or bean_name in self._processed_beans:
            return bean

        try:
            # 检查是否有@EventListener注解
            if self._has_event_listener_annotations(bean.__class__):
                # 注册事件监听器
                self._register_event_listeners(bean, bean_name)

                # 标记为已处理
                self._processed_beans.add(bean_name)

                logger.debug(f"Completed event listener registration for bean: {bean_name}")

            return bean

        except Exception as e:
            raise BeanProcessingError(
                f"Failed to process @EventListener annotations for bean '{bean_name}'",
                bean_name=bean_name,
                processor_name=self.__class__.__name__,
                cause=e,
            ) from e

    def get_order(self) -> int:
        """
        获取处理器执行顺序

        事件监听处理器需要在生命周期处理器之后执行,
        确保Bean完全初始化后再注册事件监听器.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.LIFECYCLE_PROCESSOR + 20  # 在生命周期处理器之后执行

    def supports(self, bean: Any, _bean_name: str) -> bool:
        """
        检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示支持处理此Bean
        """
        if bean is None:
            return False

        # 检查Bean类是否有@EventListener注解
        return self._has_event_listener_annotations(bean.__class__)

    def destroy_bean(self, bean: Any, bean_name: str) -> None:
        """
        销毁Bean时取消注册事件监听器

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        if bean is None or bean_name not in self._registered_handlers:
            return

        try:
            # 取消注册所有监听器
            handler_ids = self._registered_handlers.get(bean_name, [])
            for handler_id in handler_ids:
                if self._event_publisher:
                    self._event_publisher.unsubscribe(handler_id)

            # 清理记录
            if bean_name in self._registered_handlers:
                del self._registered_handlers[bean_name]

            logger.debug(f"Unregistered {len(handler_ids)} event listeners for bean: {bean_name}")

        except Exception as e:
            logger.warning(f"Failed to unregister event listeners for bean '{bean_name}': {e}")

    def _register_event_listeners(self, bean: Any, bean_name: str) -> None:
        """
        注册Bean中的事件监听器

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        if self._event_publisher is None:
            logger.warning(f"Event publisher not available for bean '{bean_name}', skipping event listener registration")
            return

        bean_class = bean.__class__

        # 获取缓存的监听器信息
        listener_info = self._get_event_listeners(bean_class)

        handler_ids = []

        for method_name, method, event_type, condition, order, async_exec in listener_info:
            try:
                # 注册事件监听器
                handler_id = self._event_publisher.subscribe(
                    event_type=event_type, handler=method, instance=bean, order=order, async_exec=async_exec, condition=condition
                )

                handler_ids.append(handler_id)
                logger.debug(f"Registered event listener '{method_name}' for event type '{event_type.__name__}' on bean '{bean_name}'")

            except Exception as e:
                logger.error(f"Failed to register event listener '{method_name}' for bean '{bean_name}': {e}")
                # 继续注册其他监听器

        # 记录注册的处理器ID
        if handler_ids:
            self._registered_handlers[bean_name] = handler_ids

    def _get_event_listeners(self, bean_class: type) -> list[tuple]:
        """
        获取类中的事件监听器信息

        Args:
            bean_class: Bean类

        Returns:
            监听器信息列表:[(method_name, method, event_type, condition, order, async_exec), ...]
        """
        if bean_class in self._listener_cache:
            return self._listener_cache[bean_class]

        listeners = []

        # 扫描类中的所有方法
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)

                if (inspect.isfunction(attr) or inspect.ismethod(attr)) and is_event_listener(attr):
                    # 获取@EventListener元数据
                    metadata = getattr(attr, "__event_listener_metadata__", None)
                    if metadata:
                        # 推断事件类型(如果未指定)
                        event_type = metadata.event_type
                        if event_type is None:
                            event_type = self._infer_event_type(attr)

                        if event_type:
                            listeners.append((attr_name, attr, event_type, metadata.condition, metadata.order, metadata.async_exec))
                        else:
                            logger.warning(f"Could not infer event type for method '{attr_name}' in class '{bean_class.__name__}'")

            except Exception:
                continue

        # 按执行顺序排序
        listeners.sort(key=lambda x: x[4])  # 按order排序

        # 缓存结果
        self._listener_cache[bean_class] = listeners
        return listeners

    def _has_event_listener_annotations(self, bean_class: type) -> bool:
        """
        检查类是否有@EventListener注解

        Args:
            bean_class: Bean类

        Returns:
            True表示有@EventListener注解
        """
        # 检查方法级别的@EventListener注解
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)
                if (inspect.isfunction(attr) or inspect.ismethod(attr)) and is_event_listener(attr):
                    return True
            except Exception:
                continue

        return False

    def _infer_event_type(self, method) -> Optional[type]:
        """
        从方法参数推断事件类型

        Args:
            method: 方法对象

        Returns:
            推断的事件类型
        """
        try:
            # 获取方法签名
            sig = inspect.signature(method)

            # 跳过self参数
            parameters = list(sig.parameters.values())[1:]

            if parameters:
                param = parameters[0]
                if param.annotation != inspect.Parameter.empty:
                    return param.annotation

            return None

        except Exception:
            return None

    def get_processed_beans_count(self) -> int:
        """
        获取已处理的Bean数量

        Returns:
            已处理的Bean数量
        """
        return len(self._processed_beans)

    def get_registered_listeners_count(self) -> int:
        """
        获取已注册的监听器数量

        Returns:
            已注册的监听器数量
        """
        return sum(len(handler_ids) for handler_ids in self._registered_handlers.values())
