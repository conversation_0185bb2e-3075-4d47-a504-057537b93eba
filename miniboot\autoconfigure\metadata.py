#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置元数据模块
"""

from dataclasses import dataclass, field
from typing import Optional


@dataclass
class AutoConfigurationMetadata:
    """自动配置元数据

    包含自动配置的所有元信息,用于控制配置的加载顺序、条件和依赖关系.
    """

    name: str  # 配置名称
    description: str  # 配置描述
    priority: int = 100  # 优先级(数字越小优先级越高)
    depends_on: list[str] = field(default_factory=list)  # 依赖的配置
    conflicts_with: list[str] = field(default_factory=list)  # 冲突的配置
    conditions: list[str] = field(default_factory=list)  # 启用条件
    properties_prefix: Optional[str] = None  # 配置属性前缀
    auto_configure_before: list[str] = field(default_factory=list)  # 在指定配置之前
    auto_configure_after: list[str] = field(default_factory=list)  # 在指定配置之后

    def validate(self) -> None:
        """验证元数据的有效性

        Raises:
            ValueError: 元数据验证失败
        """
        if not self.name:
            raise ValueError("Configuration name is required")

        if not self.description:
            raise ValueError("Configuration description is required")

        if self.priority < 0:
            raise ValueError("Priority must be non-negative")

        # 检查是否同时指定了depends_on和auto_configure_after
        if self.depends_on and self.auto_configure_after:
            overlapping = set(self.depends_on) & set(self.auto_configure_after)
            if overlapping:
                raise ValueError(f"Configuration cannot both depend on and configure after: {overlapping}")

    def get_all_dependencies(self) -> list[str]:
        """获取所有依赖配置

        Returns:
            所有依赖配置的列表
        """
        dependencies = []
        dependencies.extend(self.depends_on)
        dependencies.extend(self.auto_configure_after)
        return list(set(dependencies))  # 去重

    def has_dependency_on(self, config_name: str) -> bool:
        """检查是否依赖指定配置

        Args:
            config_name: 配置名称

        Returns:
            是否依赖指定配置
        """
        return config_name in self.get_all_dependencies()

    def conflicts_with_config(self, config_name: str) -> bool:
        """检查是否与指定配置冲突

        Args:
            config_name: 配置名称

        Returns:
            是否与指定配置冲突
        """
        return config_name in self.conflicts_with

    def should_configure_before(self, config_name: str) -> bool:
        """检查是否应该在指定配置之前配置

        Args:
            config_name: 配置名称

        Returns:
            是否应该在指定配置之前配置
        """
        return config_name in self.auto_configure_before

    def should_configure_after(self, config_name: str) -> bool:
        """检查是否应该在指定配置之后配置

        Args:
            config_name: 配置名称

        Returns:
            是否应该在指定配置之后配置
        """
        return config_name in self.auto_configure_after or config_name in self.depends_on
