#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Performance Endpoint - 性能监控HTTP端点

提供性能监控数据的HTTP访问接口，包括：
- 性能状态概览
- 详细指标数据
- 历史快照数据
- 实时性能分析
"""

import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from miniboot.monitoring.interfaces import EndpointInfo, EndpointProvider
from miniboot.starters.actuator.endpoints.base import (BaseEndpoint,
                                                       EndpointOperation,
                                                       OperationType)

from .monitor import PerformanceMonitor


class PerformanceEndpoint(BaseEndpoint, EndpointProvider):
    """性能监控端点 - 提供性能数据的HTTP访问接口"""

    def __init__(self, performance_monitor: Optional[PerformanceMonitor] = None):
        """初始化性能端点

        Args:
            performance_monitor: 性能监控器实例
        """
        super().__init__(endpoint_id="performance", enabled=True, sensitive=False)
        self._performance_monitor = performance_monitor
        logger.debug("PerformanceEndpoint initialized")

    def set_performance_monitor(self, performance_monitor: PerformanceMonitor) -> None:
        """设置性能监控器"""
        self._performance_monitor = performance_monitor
        logger.debug("Performance monitor set for PerformanceEndpoint")

    # ==================== BaseEndpoint 接口实现 ====================

    def _create_operations(self) -> List[EndpointOperation]:
        """创建操作列表"""
        return [
            EndpointOperation(
                operation_type=OperationType.READ,
                method="GET",
                path="",  # /actuator/performance - 性能状态概览
                handler=self._get_performance_status
            ),
            EndpointOperation(
                operation_type=OperationType.READ,
                method="GET",
                path="/metrics",  # /actuator/performance/metrics - 详细指标
                handler=self._get_performance_metrics
            ),
            EndpointOperation(
                operation_type=OperationType.READ,
                method="GET",
                path="/snapshots",  # /actuator/performance/snapshots - 历史快照
                handler=self._get_performance_snapshots
            )
        ]

    async def invoke(self, operation_type: OperationType, **kwargs) -> Any:
        """执行端点操作"""
        if operation_type == OperationType.READ:
            # 根据路径参数决定返回哪种数据
            path = kwargs.get('path', '')
            if path == '/metrics':
                return await self._get_performance_metrics(**kwargs)
            elif path == '/snapshots':
                return await self._get_performance_snapshots(**kwargs)
            else:
                return await self._get_performance_status(**kwargs)
        else:
            raise ValueError(f"Unsupported operation: {operation_type}")

    # ==================== EndpointProvider 接口实现 ====================

    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息"""
        return EndpointInfo(
            name="performance",
            path="/performance",
            methods=["GET"],
            description="Performance monitoring endpoint providing system performance metrics",
            enabled=True,
            sensitive=False
        )

    async def handle_request(self, request: Any = None, **kwargs) -> Any:
        """处理HTTP请求"""
        try:
            # 默认返回性能状态概览
            return await self._get_performance_status(**kwargs)
        except Exception as e:
            logger.error(f"Error handling performance request: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "status": "error"
            }

    def is_enabled(self) -> bool:
        """检查端点是否启用"""
        return self.enabled and self._performance_monitor is not None

    # ==================== 性能数据获取方法 ====================

    async def _get_performance_status(self, **kwargs) -> Dict[str, Any]:
        """获取性能状态概览"""
        if not self._performance_monitor:
            return {
                "status": "unavailable",
                "message": "Performance monitor not available",
                "timestamp": datetime.now().isoformat()
            }

        try:
            # 获取监控状态
            status = self._performance_monitor.get_status()

            return {
                "status": "available",
                "monitoring": status.get("monitoring", False),
                "latest_snapshot": status.get("latest_snapshot", {}),
                "analysis": status.get("analysis", {}),
                "recommendations": status.get("recommendations", []),
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error getting performance status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _get_performance_metrics(self, **kwargs) -> Dict[str, Any]:
        """获取详细性能指标"""
        if not self._performance_monitor:
            return {
                "error": "Performance monitor not available",
                "timestamp": datetime.now().isoformat()
            }

        try:
            # 获取指标参数
            metric_name = kwargs.get('metric', None)
            limit = int(kwargs.get('limit', 100))

            if metric_name:
                # 获取特定指标
                metrics = self._performance_monitor._data_collector.get_metrics(metric_name, limit)
                return {
                    "metric_name": metric_name,
                    "data": [
                        {
                            "name": m.name,
                            "value": m.value,
                            "timestamp": m.timestamp,
                            "unit": m.unit,
                            "category": m.category,
                            "tags": m.tags
                        } for m in metrics
                    ],
                    "count": len(metrics),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                # 获取所有可用指标的摘要
                latest_snapshot = self._performance_monitor._data_collector.get_latest_snapshot()
                if not latest_snapshot:
                    return {
                        "message": "No metrics data available",
                        "timestamp": datetime.now().isoformat()
                    }

                return {
                    "integration_metrics": latest_snapshot.integration_metrics,
                    "endpoint_metrics": latest_snapshot.endpoint_metrics,
                    "system_metrics": latest_snapshot.system_metrics,
                    "performance_score": latest_snapshot.performance_score,
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _get_performance_snapshots(self, **kwargs) -> Dict[str, Any]:
        """获取历史性能快照"""
        if not self._performance_monitor:
            return {
                "error": "Performance monitor not available",
                "timestamp": datetime.now().isoformat()
            }

        try:
            # 获取快照参数
            limit = int(kwargs.get('limit', 10))

            snapshots = self._performance_monitor._data_collector.get_snapshots(limit)

            return {
                "snapshots": [
                    {
                        "timestamp": snapshot.timestamp,
                        "performance_score": snapshot.performance_score,
                        "integration_metrics": snapshot.integration_metrics,
                        "endpoint_metrics": snapshot.endpoint_metrics,
                        "system_metrics": snapshot.system_metrics,
                        "datetime": datetime.fromtimestamp(snapshot.timestamp).isoformat()
                    } for snapshot in snapshots
                ],
                "count": len(snapshots),
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting performance snapshots: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    # ==================== 同步版本方法（用于invoke调用）====================

    def get_performance_status_sync(self) -> Dict[str, Any]:
        """同步版本的性能状态获取"""
        try:
            # 在事件循环中运行异步方法
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果已经在事件循环中，创建任务
                task = asyncio.create_task(self._get_performance_status())
                return asyncio.run_coroutine_threadsafe(task, loop).result(timeout=5.0)
            else:
                # 如果没有事件循环，直接运行
                return asyncio.run(self._get_performance_status())
        except Exception as e:
            logger.error(f"Error in sync performance status: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
