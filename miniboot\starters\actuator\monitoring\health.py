#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: HealthMonitor - 健康状态监控器

提供健康状态监控功能,支持:
- 健康状态变化检测
- 健康状态历史记录
- 健康状态告警
- 组件健康监控
- 自动恢复检测
"""

import asyncio
import threading
import time
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from loguru import logger

from ..collectors.aggregator import MetricsAggregator
from .alerts import AlertChannel, AlertSeverity, MonitoringAlerts


class HealthStatus(Enum):
    """健康状态枚举"""

    UP = "UP"
    DOWN = "DOWN"
    OUT_OF_SERVICE = "OUT_OF_SERVICE"
    UNKNOWN = "UNKNOWN"


@dataclass
class HealthSnapshot:
    """健康状态快照"""

    timestamp: datetime
    overall_status: HealthStatus
    components: Dict[str, Dict[str, Any]]
    details: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "overall_status": self.overall_status.value,
            "components": self.components,
            "details": self.details,
        }


@dataclass
class HealthChangeEvent:
    """健康状态变化事件"""

    component_name: str
    old_status: HealthStatus
    new_status: HealthStatus
    timestamp: datetime
    details: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "component_name": self.component_name,
            "old_status": self.old_status.value,
            "new_status": self.new_status.value,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details,
            "severity": self._get_severity().value,
        }

    def _get_severity(self) -> AlertSeverity:
        """获取事件严重级别"""
        if self.new_status == HealthStatus.DOWN:
            return AlertSeverity.CRITICAL
        elif self.new_status == HealthStatus.OUT_OF_SERVICE:
            return AlertSeverity.ERROR
        elif self.old_status == HealthStatus.DOWN and self.new_status == HealthStatus.UP:
            return AlertSeverity.INFO  # 恢复事件
        else:
            return AlertSeverity.WARNING


class HealthMonitor:
    """健康状态监控器"""

    def __init__(self, check_interval: float = 30.0, max_history: int = 1000, enable_alerts: bool = True):
        """初始化健康状态监控器

        Args:
            check_interval: 检查间隔(秒)
            max_history: 最大历史记录数
            enable_alerts: 是否启用告警
        """
        self.check_interval = check_interval
        self.max_history = max_history
        self.enable_alerts = enable_alerts

        # 指标聚合器
        self.metrics_aggregator = MetricsAggregator()

        # 健康状态历史
        self.health_history: deque[HealthSnapshot] = deque(maxlen=max_history)
        self.change_events: deque[HealthChangeEvent] = deque(maxlen=max_history)

        # 当前状态
        self.current_status: Optional[HealthSnapshot] = None
        self.component_status: Dict[str, HealthStatus] = {}

        # 告警系统
        self.alert_system: Optional[MonitoringAlerts] = None
        if enable_alerts:
            self.alert_system = MonitoringAlerts()
            self._setup_health_alert_rules()

        # 事件回调
        self.change_callbacks: List[Callable[[HealthChangeEvent], None]] = []

        # 运行状态
        self._running = False
        self._monitor_task: Optional[asyncio.Task] = None

        # 线程安全
        self._lock = threading.RLock()

        logger.info("HealthMonitor initialized")

    async def start(self) -> None:
        """启动健康监控器"""
        if self._running:
            logger.warning("HealthMonitor already running")
            return

        self._running = True

        # 启动告警系统
        if self.alert_system:
            await self.alert_system.start()

        # 启动监控任务
        self._monitor_task = asyncio.create_task(self._monitor_loop())

        logger.info("HealthMonitor started")

    async def stop(self) -> None:
        """停止健康监控器"""
        if not self._running:
            return

        self._running = False

        # 停止监控任务
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

        # 停止告警系统
        if self.alert_system:
            await self.alert_system.stop()

        logger.info("HealthMonitor stopped")

    def add_change_callback(self, callback: Callable[[HealthChangeEvent], None]) -> None:
        """添加状态变化回调

        Args:
            callback: 回调函数
        """
        self.change_callbacks.append(callback)
        logger.debug("Added health change callback")

    def remove_change_callback(self, callback: Callable[[HealthChangeEvent], None]) -> bool:
        """移除状态变化回调

        Args:
            callback: 回调函数

        Returns:
            bool: 是否成功移除
        """
        try:
            self.change_callbacks.remove(callback)
            logger.debug("Removed health change callback")
            return True
        except ValueError:
            return False

    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self._running:
            try:
                await self._check_health()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(5.0)

    async def _check_health(self) -> None:
        """检查健康状态"""
        try:
            # 获取健康状态
            health_data = await self.metrics_aggregator.get_health_status()

            # 创建健康快照
            snapshot = self._create_health_snapshot(health_data)

            # 检测状态变化
            await self._detect_changes(snapshot)

            # 更新当前状态
            with self._lock:
                self.current_status = snapshot
                self.health_history.append(snapshot)

        except Exception as e:
            logger.error(f"Failed to check health status: {e}")

    def _create_health_snapshot(self, health_data: Dict[str, Any]) -> HealthSnapshot:
        """创建健康状态快照"""
        timestamp = datetime.now()

        # 解析整体状态
        overall_status_str = health_data.get("status", "UNKNOWN")
        overall_status = HealthStatus(overall_status_str)

        # 解析组件状态
        components = {}
        components_data = health_data.get("components", {})

        for component_name, component_data in components_data.items():
            if isinstance(component_data, dict):
                component_status = component_data.get("status", "UNKNOWN")
                components[component_name] = {"status": component_status, "details": component_data.get("details", {})}

        # 创建快照
        snapshot = HealthSnapshot(timestamp=timestamp, overall_status=overall_status, components=components, details=health_data.get("details", {}))

        return snapshot

    async def _detect_changes(self, new_snapshot: HealthSnapshot) -> None:
        """检测健康状态变化"""
        if not self.current_status:
            # 首次检查,记录初始状态
            for component_name, component_data in new_snapshot.components.items():
                component_status = HealthStatus(component_data["status"])
                self.component_status[component_name] = component_status
            return

        current_time = datetime.now()

        # 检查整体状态变化
        if new_snapshot.overall_status != self.current_status.overall_status:
            event = HealthChangeEvent(
                component_name="overall",
                old_status=self.current_status.overall_status,
                new_status=new_snapshot.overall_status,
                timestamp=current_time,
                details={"type": "overall_status_change"},
            )
            await self._handle_change_event(event)

        # 检查组件状态变化
        for component_name, component_data in new_snapshot.components.items():
            new_status = HealthStatus(component_data["status"])
            old_status = self.component_status.get(component_name, HealthStatus.UNKNOWN)

            if new_status != old_status:
                event = HealthChangeEvent(
                    component_name=component_name,
                    old_status=old_status,
                    new_status=new_status,
                    timestamp=current_time,
                    details=component_data.get("details", {}),
                )
                await self._handle_change_event(event)

                # 更新组件状态
                self.component_status[component_name] = new_status

    async def _handle_change_event(self, event: HealthChangeEvent) -> None:
        """处理状态变化事件"""
        with self._lock:
            self.change_events.append(event)

        # 记录日志
        severity = event._get_severity()
        log_func = {
            AlertSeverity.INFO: logger.info,
            AlertSeverity.WARNING: logger.warning,
            AlertSeverity.ERROR: logger.error,
            AlertSeverity.CRITICAL: logger.critical,
        }.get(severity, logger.info)

        log_func(f"Health status changed: {event.component_name} {event.old_status.value} -> {event.new_status.value}")

        # 调用回调函数
        for callback in self.change_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)
            except Exception as e:
                logger.error(f"Error in health change callback: {e}")

        # 发送告警(如果启用)
        if self.alert_system and severity in [AlertSeverity.ERROR, AlertSeverity.CRITICAL]:
            await self._send_health_alert(event)

    async def _send_health_alert(self, event: HealthChangeEvent) -> None:
        """发送健康状态告警"""
        if not self.alert_system:
            return

        # 创建临时告警规则
        from .alerts import AlertRule

        rule = AlertRule(
            rule_id=f"health_{event.component_name}_{int(time.time())}",
            name=f"Health Status Change - {event.component_name}",
            description=f"Health status changed from {event.old_status.value} to {event.new_status.value}",
            metric_name=f"health.{event.component_name}.status",
            condition="== 'DOWN'",
            threshold="DOWN",
            severity=event._get_severity(),
            channels=[AlertChannel.LOG, AlertChannel.CONSOLE],
        )

        # 手动触发告警
        from .alerts import Alert, AlertStatus

        alert = Alert(
            alert_id=f"health_alert_{int(time.time() * 1000)}",
            rule_id=rule.rule_id,
            rule_name=rule.name,
            metric_name=rule.metric_name,
            metric_value=event.new_status.value,
            threshold=rule.threshold,
            severity=rule.severity,
            status=AlertStatus.ACTIVE,
            message=f"Health status change detected: {event.component_name} changed from {event.old_status.value} to {event.new_status.value}",
            created_at=event.timestamp,
            updated_at=event.timestamp,
            tags={"component": event.component_name, "type": "health_change"},
        )

        # 发送通知
        await self.alert_system.notifier.send_alert(alert, rule.channels)

    def _setup_health_alert_rules(self) -> None:
        """设置健康状态告警规则"""
        if not self.alert_system:
            return

        from .alerts import AlertRule

        # 整体健康状态告警规则
        overall_rule = AlertRule(
            rule_id="health_overall_down",
            name="Overall Health Status Down",
            description="Overall application health status is DOWN",
            metric_name="health.overall.status",
            condition="== 'DOWN'",
            threshold="DOWN",
            severity=AlertSeverity.CRITICAL,
            channels=[AlertChannel.LOG, AlertChannel.CONSOLE],
            cooldown_seconds=60,
            max_alerts_per_hour=5,
        )

        self.alert_system.add_rule(overall_rule)

        logger.debug("Health alert rules configured")

    def get_current_health(self) -> Optional[Dict[str, Any]]:
        """获取当前健康状态"""
        if self.current_status:
            return self.current_status.to_dict()
        return None

    def get_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取健康状态历史"""
        with self._lock:
            history = list(self.health_history)[-limit:] if limit > 0 else list(self.health_history)
            return [snapshot.to_dict() for snapshot in history]

    def get_events(self, limit: int = 50, component_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取状态变化事件"""
        with self._lock:
            events = list(self.change_events)

            if component_name:
                events = [event for event in events if event.component_name == component_name]

            # 按时间倒序排列
            events.sort(key=lambda x: x.timestamp, reverse=True)

            return [event.to_dict() for event in events[:limit]]

    def get_component_status(self, component_name: Optional[str] = None) -> Dict[str, Any]:
        """获取组件状态

        Args:
            component_name: 组件名称,None表示获取所有组件

        Returns:
            Dict[str, Any]: 组件状态信息
        """
        with self._lock:
            if component_name:
                status = self.component_status.get(component_name, HealthStatus.UNKNOWN)
                return {
                    "component_name": component_name,
                    "status": status.value,
                    "last_check": self.current_status.timestamp.isoformat() if self.current_status else None,
                }
            else:
                return {
                    "components": {name: status.value for name, status in self.component_status.items()},
                    "total_components": len(self.component_status),
                    "last_check": self.current_status.timestamp.isoformat() if self.current_status else None,
                }

    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        with self._lock:
            # 统计组件状态
            status_counts = {}
            for status in self.component_status.values():
                status_counts[status.value] = status_counts.get(status.value, 0) + 1

            # 计算健康分数
            total_components = len(self.component_status)
            up_components = status_counts.get("UP", 0)
            health_score = (up_components / total_components * 100) if total_components > 0 else 0

            return {
                "overall_status": self.current_status.overall_status.value if self.current_status else "UNKNOWN",
                "health_score": round(health_score, 2),
                "total_components": total_components,
                "status_distribution": status_counts,
                "total_change_events": len(self.change_events),
                "monitoring_enabled": self._running,
                "last_check": self.current_status.timestamp.isoformat() if self.current_status else None,
            }
