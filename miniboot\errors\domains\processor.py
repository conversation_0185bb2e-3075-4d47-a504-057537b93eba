#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Processor相关异常类

处理器系统相关的异常。
"""

from ..base import ApplicationError, BusinessError, ValidationError


# 处理器执行相关异常 (BusinessError)
class ProcessorExecutionError(BusinessError):
    """处理器执行错误 - 处理器执行过程中的错误"""
    max_attempts = 3
    base_delay = 1.0


class ProcessorTimeoutError(BusinessError):
    """处理器超时错误 - 处理器执行超时"""
    max_attempts = 2
    base_delay = 2.0


class ProcessorInterruptedError(BusinessError):
    """处理器中断错误 - 处理器执行被中断"""
    max_attempts = 2
    base_delay = 1.0


# 处理器配置相关异常 (ApplicationError)
class ProcessorConfigurationError(ApplicationError):
    """处理器配置错误 - 处理器配置相关的错误"""
    retryable = False


class ProcessorInitializationError(ApplicationError):
    """处理器初始化错误 - 处理器初始化失败"""
    max_attempts = 2
    base_delay = 2.0


class ProcessorRegistrationError(ApplicationError):
    """处理器注册错误 - 处理器注册过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


# 处理器状态相关异常 (BusinessError)
class ProcessorStateError(BusinessError):
    """处理器状态错误 - 处理器状态相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ProcessorNotFoundError(BusinessError):
    """处理器未找到错误 - 找不到指定的处理器"""
    retryable = False


class ProcessorAlreadyExistsError(BusinessError):
    """处理器已存在错误 - 处理器已经存在"""
    retryable = False


# 处理器链相关异常 (BusinessError)
class ProcessorChainError(BusinessError):
    """处理器链错误 - 处理器链相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ProcessorChainExecutionError(BusinessError):
    """处理器链执行错误 - 处理器链执行失败"""
    max_attempts = 3
    base_delay = 1.0


class ProcessorChainInterruptedError(BusinessError):
    """处理器链中断错误 - 处理器链执行被中断"""
    max_attempts = 2
    base_delay = 1.0


# 处理器验证相关异常 (ValidationError)
class ProcessorValidationError(ValidationError):
    """处理器验证错误 - 处理器验证失败"""
    # 继承ValidationError的属性：retryable = False


class InvalidProcessorError(ValidationError):
    """无效处理器错误 - 处理器配置无效"""
    # 继承ValidationError的属性：retryable = False


# 处理器资源相关异常 (ApplicationError)
class ProcessorResourceError(ApplicationError):
    """处理器资源错误 - 处理器资源相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ProcessorResourceExhaustionError(ApplicationError):
    """处理器资源耗尽错误 - 处理器资源耗尽"""
    max_attempts = 3
    base_delay = 2.0
