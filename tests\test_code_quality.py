"""
代码质量测试

确保项目代码没有编译警告、语法错误和格式问题.
"""

import unittest
from pathlib import Path

from tests.testutils.code_quality import CodeQualityChecker


class CodeQualityTestCase(unittest.TestCase):
    """代码质量测试用例"""

    def setUp(self):
        """设置测试环境"""
        self.project_root = Path(__file__).parent.parent
        self.miniboot_dir = self.project_root / "miniboot"
        self.tests_dir = self.project_root / "tests"

    @unittest.skip("代码质量问题已知，暂时跳过")
    def test_code_quality_with_ruff(self):
        """测试:项目中不应该有代码质量问题"""
        checker = CodeQualityChecker(self.project_root)
        results = checker.check_project_with_ruff(show_progress=True)

        if results:
            issues_text = "\n".join([f"  {issue}" for issue in results])
            self.fail(f"发现 {len(results)} 个代码质量问题:\n{issues_text}")


if __name__ == "__main__":
    unittest.main()
