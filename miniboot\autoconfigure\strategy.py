#!/usr/bin/env python
# encoding: utf-8
"""
条件评估策略模块

使用策略模式和责任链模式优化复杂的条件评估逻辑.
"""

from abc import ABC, abstractmethod
from typing import Any, Optional

from loguru import logger

from miniboot.context import ApplicationContext
from miniboot.errors.domains.autoconfigure import \
    ConditionEvaluationError as ConditionEvaluationException


class ConditionEvaluationStrategy(ABC):
    """条件评估策略接口"""

    @abstractmethod
    def can_handle(self, condition: str) -> bool:
        """检查是否能处理该条件

        Args:
            condition: 条件字符串

        Returns:
            是否能处理
        """
        pass

    @abstractmethod
    def evaluate(self, condition: str, context: ApplicationContext) -> bool:
        """评估条件

        Args:
            condition: 条件字符串
            context: 应用上下文

        Returns:
            条件是否满足
        """
        pass

    @abstractmethod
    def get_prefix(self) -> str:
        """获取条件前缀

        Returns:
            条件前缀字符串
        """
        pass


class PropertyConditionStrategy(ConditionEvaluationStrategy):
    """属性条件策略"""

    def can_handle(self, condition: str) -> bool:
        return condition.startswith("property:")

    def evaluate(self, condition: str, context: ApplicationContext) -> bool:
        """评估属性条件"""
        try:
            property_name = condition[9:]  # 去掉 "property:" 前缀
            env = context.get_environment()
            property_value = env.get_property(property_name)

            result = property_value is not None
            logger.debug(f"Property condition '{property_name}': {result}")
            return result

        except Exception as e:
            logger.warning(f"Error evaluating property condition '{condition}': {e}")
            return False

    def get_prefix(self) -> str:
        return "property:"


class ClassConditionStrategy(ConditionEvaluationStrategy):
    """类存在条件策略"""

    def can_handle(self, condition: str) -> bool:
        return condition.startswith("class:")

    def evaluate(self, condition: str, context: ApplicationContext) -> bool:
        """评估类存在条件"""
        try:
            class_name = condition[6:]  # 去掉 "class:" 前缀

            # 尝试导入类
            module_name = class_name.rsplit(".", 1)[0] if "." in class_name else class_name
            __import__(module_name)

            logger.debug(f"Class condition '{class_name}': True")
            return True

        except ImportError:
            logger.debug(f"Class condition '{class_name}': False (ImportError)")
            return False
        except Exception as e:
            logger.warning(f"Error evaluating class condition '{condition}': {e}")
            return False

    def get_prefix(self) -> str:
        return "class:"


class BeanConditionStrategy(ConditionEvaluationStrategy):
    """Bean存在条件策略"""

    def can_handle(self, condition: str) -> bool:
        return condition.startswith("bean:")

    def evaluate(self, condition: str, context: ApplicationContext) -> bool:
        """评估Bean存在条件"""
        try:
            bean_name = condition[5:]  # 去掉 "bean:" 前缀
            result = context.contains_bean(bean_name)

            logger.debug(f"Bean condition '{bean_name}': {result}")
            return result

        except Exception as e:
            logger.warning(f"Error evaluating bean condition '{condition}': {e}")
            return False

    def get_prefix(self) -> str:
        return "bean:"


class ResourceConditionStrategy(ConditionEvaluationStrategy):
    """资源存在条件策略"""

    def can_handle(self, condition: str) -> bool:
        return condition.startswith("resource:")

    def evaluate(self, condition: str, context: ApplicationContext) -> bool:
        """评估资源存在条件"""
        try:
            import os

            resource_path = condition[9:]  # 去掉 "resource:" 前缀
            result = os.path.exists(resource_path)

            logger.debug(f"Resource condition '{resource_path}': {result}")
            return result

        except Exception as e:
            logger.warning(f"Error evaluating resource condition '{condition}': {e}")
            return False

    def get_prefix(self) -> str:
        return "resource:"


class DefaultConditionStrategy(ConditionEvaluationStrategy):
    """默认条件策略 - 处理未知条件"""

    def can_handle(self, condition: str) -> bool:
        return True  # 总是能处理(作为最后的处理器)

    def evaluate(self, condition: str, context: ApplicationContext) -> bool:
        """默认返回True"""
        logger.debug(f"Unknown condition '{condition}': defaulting to True")
        return True

    def get_prefix(self) -> str:
        return ""


class ConditionEvaluationChain:
    """条件评估责任链"""

    def __init__(self):
        """初始化责任链"""
        self._strategies = []
        self._register_default_strategies()

    def _register_default_strategies(self) -> None:
        """注册默认策略"""
        self.register_strategy(PropertyConditionStrategy())
        self.register_strategy(ClassConditionStrategy())
        self.register_strategy(BeanConditionStrategy())
        self.register_strategy(ResourceConditionStrategy())
        # 默认策略必须最后注册
        self.register_strategy(DefaultConditionStrategy())

    def register_strategy(self, strategy: ConditionEvaluationStrategy) -> None:
        """注册策略

        Args:
            strategy: 条件评估策略
        """
        self._strategies.append(strategy)
        logger.debug(f"Registered condition strategy: {strategy.__class__.__name__}")

    def evaluate_condition(self, condition: str, context: ApplicationContext) -> bool:
        """评估条件

        Args:
            condition: 条件字符串
            context: 应用上下文

        Returns:
            条件是否满足

        Raises:
            ConditionEvaluationException: 评估失败时抛出
        """
        try:
            # 找到第一个能处理该条件的策略
            for strategy in self._strategies:
                if strategy.can_handle(condition):
                    result = strategy.evaluate(condition, context)
                    logger.debug(f"Condition '{condition}' evaluated by {strategy.__class__.__name__}: {result}")
                    return result

            # 理论上不会到达这里,因为有默认策略
            logger.warning(f"No strategy found for condition: {condition}")
            return True

        except Exception as e:
            logger.error(f"Error evaluating condition '{condition}': {e}")
            raise ConditionEvaluationException(condition, str(e)) from e

    def get_supported_prefixes(self) -> list[str]:
        """获取支持的条件前缀列表

        Returns:
            支持的前缀列表
        """
        prefixes = []
        for strategy in self._strategies:
            prefix = strategy.get_prefix()
            if prefix:  # 排除空前缀(默认策略)
                prefixes.append(prefix)
        return prefixes
