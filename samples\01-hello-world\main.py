#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Hello World 基础示例 - 展示 Mini-Boot 框架的最基本用法
"""

from miniboot import run_application
from miniboot.annotations import MiniBootApplication


@MiniBootApplication
class HelloWorldApp:
    """Hello World 应用主类"""

    @staticmethod
    def main():
        """应用主入口方法 - 类似 Spring Boot 的 main 方法"""
        print("\n" + "=" * 50)
        print("Hello World from Mini-Boot!")
        print("Mini-Boot application started successfully!")
        print("=" * 50 + "\n")

    @classmethod
    def run(cls, *args):
        """应用启动方法 - 类似 SpringApplication.run()"""
        run_application(cls)


if __name__ == "__main__":
    # 启动应用 - 类似 SpringApplication.run(HelloWorldApp.class, args)
    HelloWorldApp.run()
