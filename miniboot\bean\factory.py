#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 分层Bean工厂实现 - 集成依赖图的DefaultBeanFactory
"""

import asyncio
import threading
import time
from contextlib import contextmanager
from typing import Any, Optional, TypeVar

from loguru import logger

from miniboot.errors import BeanCircularDependencyError

from .base import BeanDefinitionRegistry, BeanFactory, ConfigurableBeanFactory, ListableBeanFactory
from .cache import CacheManager
from .definition import BeanDefinition, BeanStatus
from .graph import DependencyGraph
from .registry import DefaultBeanDefinitionRegistry
from .scopes import ScopeManager

T = TypeVar("T")


class AsyncContextDetector:
    """异步上下文检测器

    提供异步环境检测和Bean异步处理需求分析功能.
    """

    @staticmethod
    def is_async_context_active() -> bool:
        """检测当前是否在活跃的异步上下文中

        Returns:
            bool: 如果在异步上下文中返回True, 否则返回False
        """
        try:
            # 检查是否有运行中的事件循环
            loop = asyncio.get_running_loop()
            return loop is not None
        except RuntimeError:
            return False

    @staticmethod
    def should_use_async_processing(bean_name: str) -> bool:
        """判断Bean是否应该使用异步处理

        Args:
            bean_name: Bean名称

        Returns:
            bool: 如果应该使用异步处理返回True
        """
        # 基于Bean名称模式判断是否为I/O密集型
        io_intensive_patterns = ["database", "db", "connection", "client", "service", "repository", "dao", "api", "http", "redis", "cache"]

        bean_name_lower = bean_name.lower()
        return any(pattern in bean_name_lower for pattern in io_intensive_patterns)

    @staticmethod
    def detect_execution_mode() -> str:
        """检测当前执行模式

        Returns:
            str: 'async' 或 'sync'
        """
        return "async" if AsyncContextDetector.is_async_context_active() else "sync"

    @staticmethod
    def get_event_loop_info() -> dict[str, any]:
        """获取事件循环信息

        Returns:
            事件循环的详细信息
        """
        try:
            loop = asyncio.get_running_loop()
            return {"has_loop": True, "loop_type": type(loop).__name__, "is_running": loop.is_running(), "is_closed": loop.is_closed()}
        except RuntimeError:
            return {"has_loop": False, "loop_type": None, "is_running": False, "is_closed": True}

    @staticmethod
    def is_coroutine_function(func) -> bool:
        """检测函数是否为协程函数

        Args:
            func: 要检测的函数

        Returns:
            bool: 如果是协程函数返回True
        """
        return asyncio.iscoroutinefunction(func)


class DefaultBeanFactory(ConfigurableBeanFactory, ListableBeanFactory):
    """默认Bean工厂 - 集成依赖图管理

    这是重构后的核心Bean工厂实现，采用分层架构设计：
    1. 职责分离：将缓存、注入、生命周期等功能分离到专门的管理器
    2. 异步适配：自动检测执行环境并选择最优方式
    3. 依赖图管理：集成DependencyGraph实现Spring Boot风格的依赖驱动Bean创建
    4. 分层继承：实现完整的Bean工厂接口层次结构
    """

    def __init__(self, registry: Optional[BeanDefinitionRegistry] = None):
        """初始化DefaultBeanFactory

        Args:
            registry: Bean定义注册表，如果为None则创建默认实现
        """
        # 初始化依赖组件
        if registry is None:
            registry = DefaultBeanDefinitionRegistry()

        self._registry = registry
        self._parent_bean_factory: Optional[BeanFactory] = None

        # 分离的管理器组件
        self._cache_manager = CacheManager()
        self._scope_manager = ScopeManager()
        self._async_detector = AsyncContextDetector()

        # 依赖图管理器 - 核心新增功能
        self._dependency_graph = DependencyGraph()

        # Bean后置处理器管理（直接在工厂中管理，符合Spring Boot设计）
        self._bean_post_processors: list[Any] = []

        # 单例Bean存储
        self._singletons: dict[str, Any] = {}

        # Bean创建状态跟踪
        self._creating_beans: set[str] = set()  # 正在创建中的Bean
        self._created_beans: set[str] = set()   # 已创建完成的Bean

        # Bean状态跟踪 - 使用枚举管理状态
        self._bean_status: dict[str, BeanStatus] = {}  # Bean名称到状态的映射

        # 线程安全锁
        self._lock = threading.RLock()

        # 性能监控统计 - 简化版本
        self._performance_stats = {
            "total_beans_created": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "total_creation_time": 0.0,
            "creation_failures": 0,
            "circular_dependencies_resolved": 0,
        }

    # ===== Bean状态管理方法 =====

    def get_bean_status(self, bean_name: str) -> BeanStatus:
        """获取Bean的当前状态"""
        return self._bean_status.get(bean_name, BeanStatus.NOT_CREATED)

    def set_bean_status(self, bean_name: str, status: BeanStatus) -> None:
        """设置Bean状态（带状态转换验证）"""
        current_status = self.get_bean_status(bean_name)

        # 验证状态转换是否有效
        if current_status != status and not current_status.can_transition_to(status):
            logger.warning(f"Invalid status transition for bean '{bean_name}': {current_status} -> {status}")
            return

        self._bean_status[bean_name] = status
        logger.debug(f"Bean '{bean_name}' status changed: {current_status} -> {status}")

    def get_beans_by_status(self, status: BeanStatus) -> list[str]:
        """获取指定状态的所有Bean名称"""
        return [name for name, bean_status in self._bean_status.items() if bean_status == status]

    def get_bean(self, name: str, required_type: Optional[type] = None) -> Any:
        """获取Bean实例 - 自动适配同步/异步执行环境

        这是核心方法，自动检测执行环境并选择最优方式：
        1. 在异步环境中返回awaitable对象
        2. 在同步环境中直接返回Bean实例
        3. 根据Bean特性选择同步或异步处理

        Args:
            name: Bean名称
            required_type: 期望的Bean类型

        Returns:
            Bean实例或awaitable对象
        """
        # 检测当前执行环境
        if self._async_detector.is_async_context_active():
            # 在异步环境中, 返回awaitable对象
            return self._get_async(name, required_type)
        elif self._async_detector.should_use_async_processing(name):
            # 同步环境但Bean需要异步处理
            return asyncio.run(self._get_async(name, required_type))
        else:
            # 同步环境且Bean可同步处理
            return self._get_sync(name, required_type)

    def _get_sync(self, name: str, required_type: Optional[type] = None) -> Any:
        """同步Bean获取内部实现"""
        with self._lock:
            # 1. 检查缓存
            cached_bean = self._cache_manager.get_bean(name)
            if cached_bean:
                self._performance_stats["cache_hits"] += 1
                if required_type and not isinstance(cached_bean, required_type):
                    raise TypeError(f"Bean '{name}' is not of required type {required_type}")
                return cached_bean

            self._performance_stats["cache_misses"] += 1

            # 2. 检查单例
            if name in self._singletons:
                singleton = self._singletons[name]
                if required_type and not isinstance(singleton, required_type):
                    raise TypeError(f"Bean '{name}' is not of required type {required_type}")
                return singleton

            # 3. 检查Bean定义
            if not self._registry.has_definition(name):
                # 检查父工厂
                if self._parent_bean_factory and self._parent_bean_factory.contains_bean(name):
                    return self._parent_bean_factory.get_bean(name, required_type)
                raise KeyError(f"No bean definition found for name: {name}")

            # 4. 创建Bean
            bean_definition = self._registry.get_definition(name)
            bean = self._create_sync(name, bean_definition)

            # 5. 类型检查
            if required_type and not isinstance(bean, required_type):
                raise TypeError(f"Bean '{name}' is not of required type {required_type}")

            return bean

    async def _get_async(self, name: str, required_type: Optional[type] = None) -> Any:
        """异步Bean获取内部实现"""
        # 异步版本的Bean获取逻辑
        # 这里可以实现异步的Bean创建和依赖注入
        return self._get_sync(name, required_type)

    def _create_sync(self, name: str, bean_definition: BeanDefinition) -> Any:
        """同步创建Bean - 集成依赖图管理"""
        start_time = time.time()

        try:
            # 1. 检查循环依赖
            if name in self._creating_beans:
                cycle_path = self._dependency_graph.get_circular_dependency_path(name)
                if cycle_path:
                    raise BeanCircularDependencyError(f"Circular dependency detected: {' -> '.join(cycle_path)}")

            # 2. 标记Bean为创建中状态
            self._creating_beans.add(name)
            self.set_bean_status(name, BeanStatus.CREATING)

            try:
                # 3. 检查并创建依赖的Bean
                self._ensure_dependencies_created(name, bean_definition)

                # 4. 实例化Bean
                bean = self._instantiate_bean(bean_definition)

                # 5. 属性填充和依赖注入
                self._populate_bean_properties(bean, name, bean_definition)

                # 6. 执行前置处理器 (在初始化方法之前)
                for processor in self._bean_post_processors:
                    bean = processor.pre_process(bean, name)

                # 7. 执行后置处理器 (在初始化方法之后)
                for processor in self._bean_post_processors:
                    bean = processor.post_process(bean, name)

                # 8. 缓存Bean（如果是单例）
                if bean_definition.singleton():
                    self._singletons[name] = bean
                    self._cache_manager.put_bean(name, bean)

                # 9. 标记Bean为已创建状态
                self._created_beans.add(name)
                self.set_bean_status(name, BeanStatus.CREATED)

                # 10. 性能统计
                creation_time = time.time() - start_time
                self._performance_stats["total_beans_created"] += 1
                self._performance_stats["total_creation_time"] += creation_time

                logger.debug(f"Created bean '{name}' in {creation_time:.3f}s")
                return bean

            finally:
                # 11. 移除创建中状态
                self._creating_beans.discard(name)

        except Exception as e:
            # 失败时更新统计和状态
            self._performance_stats["creation_failures"] += 1
            self.set_bean_status(name, BeanStatus.FAILED)
            logger.error(f"Failed to create bean '{name}': {e}")
            raise

    def _ensure_dependencies_created(self, bean_name: str, bean_definition: BeanDefinition) -> None:
        """确保Bean的所有依赖都已创建

        Args:
            bean_name: Bean名称
            bean_definition: Bean定义
        """
        # 获取Bean的依赖列表
        dependencies = self._get_bean_dependencies(bean_definition)

        # 为每个依赖添加到依赖图中
        for dependency_name in dependencies:
            self._dependency_graph.add_dependency(bean_name, dependency_name)

            # 如果依赖Bean还未创建，先创建依赖Bean
            if dependency_name not in self._created_beans and dependency_name not in self._singletons:
                if self._registry.has_definition(dependency_name):
                    dependency_definition = self._registry.get_definition(dependency_name)
                    self._create_sync(dependency_name, dependency_definition)
                else:
                    logger.warning(f"Dependency '{dependency_name}' not found for bean '{bean_name}'")

    def _get_bean_dependencies(self, bean_definition: BeanDefinition) -> list[str]:
        """获取Bean的依赖列表

        Args:
            bean_definition: Bean定义

        Returns:
            依赖的Bean名称列表
        """
        dependencies = []

        # 从Bean定义中获取depends_on依赖
        if hasattr(bean_definition, 'depends_on') and bean_definition.depends_on:
            dependencies.extend(bean_definition.depends_on)

        # TODO: 这里可以扩展支持从@Autowired注解中自动发现依赖
        # 目前先返回显式声明的依赖

        return dependencies

    def _populate_bean_properties(self, bean: Any, bean_name: str, bean_definition: BeanDefinition) -> None:
        """填充Bean属性和依赖注入

        Args:
            bean: Bean实例
            bean_name: Bean名称
            bean_definition: Bean定义
        """
        # TODO: 实现属性注入逻辑
        # 这里可以集成@Autowired、@Value等注解的处理
        pass

    def _instantiate_bean(self, bean_definition: BeanDefinition) -> Any:
        """实例化Bean"""
        bean_class = bean_definition.bean_class
        if bean_class is None:
            raise ValueError(f"Bean class is None for definition: {bean_definition}")

        # 简单实例化（后续可以扩展支持构造函数参数）
        return bean_class()

    # ===== BeanFactory接口实现 =====

    def contains_bean(self, name: str) -> bool:
        """检查Bean是否存在"""
        # 检查本地Bean
        if self.contains_local_bean(name):
            return True

        # 检查父工厂
        if self._parent_bean_factory:
            return self._parent_bean_factory.contains_bean(name)

        return False

    def is_singleton(self, name: str) -> bool:
        """检查Bean是否为单例"""
        if self._registry.has_definition(name):
            bean_definition = self._registry.get_definition(name)
            return bean_definition.singleton()

        # 检查父工厂
        if self._parent_bean_factory:
            return self._parent_bean_factory.is_singleton(name)

        return False  # 不存在的Bean返回False而不是抛出异常

    def get_type(self, name: str) -> Optional[type]:
        """获取Bean类型"""
        if self._registry.has_definition(name):
            bean_definition = self._registry.get_definition(name)
            return bean_definition.bean_class

        # 检查父工厂
        if self._parent_bean_factory:
            return self._parent_bean_factory.get_type(name)

        return None

    def get_bean_names(self) -> list[str]:
        """获取所有Bean名称"""
        names = list(self._registry.names())

        # 添加父工厂的Bean名称
        if self._parent_bean_factory and hasattr(self._parent_bean_factory, "get_bean_names"):
            parent_names = self._parent_bean_factory.get_bean_names()
            names.extend(parent_names)

        return list(set(names))  # 去重

    # ===== HierarchicalBeanFactory接口实现 =====

    def get_parent_bean_factory(self) -> Optional[BeanFactory]:
        """获取父Bean工厂"""
        return self._parent_bean_factory

    def contains_local_bean(self, name: str) -> bool:
        """检查本地Bean是否存在（不包括父工厂）"""
        return name in self._singletons or self._registry.has_definition(name)

    # ===== ConfigurableBeanFactory接口实现 =====

    def set_parent_bean_factory(self, parent_bean_factory: Optional[BeanFactory]) -> None:
        """设置父Bean工厂"""
        self._parent_bean_factory = parent_bean_factory

    def register_scope(self, scope_name: str, scope) -> None:
        """注册Bean作用域"""
        self._scope_manager.register_scope(scope_name, scope)

    def add_bean_processor(self, bean_post_processor) -> None:
        """添加Bean后置处理器

        Args:
            bean_post_processor: Bean后置处理器实例
        """
        if bean_post_processor not in self._bean_post_processors:
            self._bean_post_processors.append(bean_post_processor)
            # 按优先级排序（如果处理器有get_order方法）
            self._bean_post_processors.sort(key=lambda p: getattr(p, "get_order", lambda: 0)())

    def get_bean_processor_count(self) -> int:
        """获取Bean后置处理器数量"""
        return len(self._bean_post_processors)

    def get_bean_post_processors(self) -> list[Any]:
        """获取所有Bean后置处理器"""
        return self._bean_post_processors.copy()

    def add_bean_post_processor(self, bean_post_processor) -> None:
        """添加Bean后置处理器 - 兼容性方法

        这是add_bean_processor方法的别名，用于向后兼容。

        Args:
            bean_post_processor: Bean后置处理器实例
        """
        self.add_bean_processor(bean_post_processor)

    def get_bean_definition_names(self) -> list[str]:
        """获取所有Bean定义名称"""
        return list(self._registry.names())

    def get_bean_definition_count(self) -> int:
        """获取Bean定义数量"""
        return self._registry.count()

    def is_prototype(self, name: str) -> bool:
        """检查Bean是否为原型作用域"""
        if not self._registry.has_definition(name):
            return False
        definition = self._registry.get_definition(name)
        return definition.prototype()

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计"""
        return self._performance_stats.copy()

    def preinstantiate_singletons(self, bean_names: Optional[list[str]] = None) -> dict[str, Any]:
        """预实例化单例Bean

        Args:
            bean_names: 要预实例化的Bean名称列表，如果为None则预实例化所有单例Bean

        Returns:
            预实例化的Bean实例字典
        """
        if bean_names is None:
            bean_names = list(self._registry.names())

        result = {}
        for name in bean_names:
            if self._registry.has_definition(name):
                definition = self._registry.get_definition(name)
                if definition.singleton():
                    try:
                        bean = self.get_bean(name)
                        result[name] = bean
                    except Exception as e:
                        logger.warning(f"Failed to preinstantiate singleton bean '{name}': {e}")
                        continue

        return result

    def destroy_singletons(self) -> None:
        """销毁所有单例Bean"""
        with self._lock:
            for name, bean in self._singletons.items():
                try:
                    # 调用销毁方法
                    if hasattr(bean, "destroy"):
                        bean.destroy()
                    logger.debug(f"Destroyed singleton bean: {name}")
                except Exception as e:
                    logger.error(f"Error destroying bean '{name}': {e}")

            # 清空缓存
            self._singletons.clear()
            self._cache_manager.clear()

    # ===== ListableBeanFactory接口实现 =====

    def get_beans_of_type(self, bean_type: type[T], include_non_singletons: bool = True, allow_eager_init: bool = True) -> dict[str, T]:
        """获取指定类型的所有Bean"""
        result = {}

        # 遍历所有Bean定义
        for name in self._registry.names():
            try:
                bean_definition = self._registry.get_definition(name)

                # 检查类型匹配
                if bean_definition.bean_class and issubclass(bean_definition.bean_class, bean_type):
                    # 检查是否包含非单例Bean
                    if not include_non_singletons and not bean_definition.singleton():
                        continue

                    # 获取Bean实例
                    if allow_eager_init or name in self._singletons:
                        bean = self.get_bean(name)
                        if isinstance(bean, bean_type):
                            result[name] = bean

            except Exception as e:
                logger.warning(f"Error getting bean '{name}' of type {bean_type}: {e}")

        return result

    def get_names_for_type(self, bean_type: type, include_non_singletons: bool = True, allow_eager_init: bool = True) -> list[str]:
        """获取指定类型的所有Bean名称"""
        result = []

        # 遍历所有Bean定义
        for name in self._registry.names():
            try:
                bean_definition = self._registry.get_definition(name)

                # 检查类型匹配
                if bean_definition.bean_class and issubclass(bean_definition.bean_class, bean_type):
                    # 检查是否包含非单例Bean
                    if not include_non_singletons and not bean_definition.singleton():
                        continue

                    # 检查是否允许急切初始化
                    if not allow_eager_init and bean_definition.lazy_init:
                        continue

                    result.append(name)

            except Exception as e:
                logger.warning(f"Error checking bean '{name}' for type {bean_type}: {e}")

        return result

    def get_bean_names_for_type(self, bean_type: type, include_non_singletons: bool = True, allow_eager_init: bool = True) -> list[str]:
        """获取指定类型的所有Bean名称

        这是ListableBeanFactory接口要求的方法，与get_names_for_type功能相同。

        Args:
            bean_type: Bean类型
            include_non_singletons: 是否包含非单例Bean
            allow_eager_init: 是否允许急切初始化

        Returns:
            匹配类型的Bean名称列表
        """
        return self.get_names_for_type(bean_type, include_non_singletons, allow_eager_init)

    # ===== 依赖图管理方法 =====

    def create_beans_in_dependency_order(self, bean_names: Optional[list[str]] = None) -> dict[str, Any]:
        """按依赖顺序批量创建Bean

        这是核心方法，实现Spring Boot风格的依赖图驱动Bean创建。

        Args:
            bean_names: 要创建的Bean名称列表，如果为None则创建所有已注册的Bean

        Returns:
            创建的Bean实例字典 {bean_name: bean_instance}

        Raises:
            BeanCircularDependencyError: 如果存在循环依赖
        """
        with self._lock:
            # 确定要创建的Bean列表
            if bean_names is None:
                bean_names = list(self._registry.names())

            logger.info(f"开始按依赖顺序创建 {len(bean_names)} 个Bean: {bean_names}")

            # 构建完整的依赖图
            self._build_dependency_graph(bean_names)

            # 获取创建顺序（拓扑排序）
            try:
                creation_order = self._dependency_graph.get_creation_order()
                logger.debug(f"Bean创建顺序: {creation_order}")
            except BeanCircularDependencyError as e:
                logger.error(f"检测到循环依赖: {e}")
                raise

            # 按顺序创建Bean
            created_beans = {}
            start_time = time.time()

            for bean_name in creation_order:
                if bean_name in bean_names and bean_name not in self._created_beans:
                    try:
                        if self._registry.has_definition(bean_name):
                            bean_definition = self._registry.get_definition(bean_name)
                            bean = self._create_sync(bean_name, bean_definition)
                            created_beans[bean_name] = bean
                            logger.debug(f"✓ 成功创建Bean: {bean_name}")
                        else:
                            logger.warning(f"Bean定义不存在: {bean_name}")
                    except Exception as e:
                        logger.error(f"✗ 创建Bean失败: {bean_name}, 错误: {e}")
                        raise

            total_time = time.time() - start_time
            logger.info(f"批量创建完成，共创建 {len(created_beans)} 个Bean，耗时 {total_time:.3f}s")

            return created_beans

    def _build_dependency_graph(self, bean_names: list[str]) -> None:
        """构建依赖图

        Args:
            bean_names: Bean名称列表
        """
        logger.debug("开始构建依赖图...")

        for bean_name in bean_names:
            if self._registry.has_definition(bean_name):
                bean_definition = self._registry.get_definition(bean_name)
                dependencies = self._get_bean_dependencies(bean_definition)

                for dependency_name in dependencies:
                    try:
                        self._dependency_graph.add_dependency(bean_name, dependency_name)
                        logger.debug(f"添加依赖关系: {bean_name} -> {dependency_name}")
                    except BeanCircularDependencyError as e:
                        logger.error(f"添加依赖时检测到循环依赖: {e}")
                        raise

        # 输出依赖图统计信息
        stats = self._dependency_graph.get_stats()
        logger.debug(f"依赖图构建完成: {stats}")

    def get_dependency_graph_stats(self) -> dict[str, Any]:
        """获取依赖图统计信息

        Returns:
            依赖图统计信息
        """
        return self._dependency_graph.get_stats()

    def clear_dependency_graph(self) -> None:
        """清空依赖图"""
        self._dependency_graph.clear()
        self._creating_beans.clear()
        self._created_beans.clear()
        logger.debug("依赖图已清空")

    # ===== 工厂管理方法 =====

    def register_bean_definition(self, name: str, bean_definition: BeanDefinition) -> None:
        """注册Bean定义"""
        self._registry.register(name, bean_definition)

    def get_bean_definition(self, name: str) -> BeanDefinition:
        """获取Bean定义"""
        return self._registry.get_definition(name)

    def has_bean_definition(self, name: str) -> bool:
        """检查Bean定义是否存在"""
        return self._registry.has_definition(name)

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        return self._performance_stats.copy()

    # ===== Bean信息查询方法 - 对标Spring Boot =====

    def get_bean_info(self) -> dict[str, Any]:
        """获取Bean信息快照 - 对标Spring Boot BeansEndpoint

        Returns:
            Bean信息字典，包含所有Bean的详细信息
        """
        bean_info = {}

        # 遍历所有Bean定义
        for bean_name in self._registry.names():
            try:
                bean_definition = self._registry.get_definition(bean_name)

                # 基本信息
                info = {
                    "scope": bean_definition.scope.value if hasattr(bean_definition.scope, 'value') else str(bean_definition.scope),
                    "type": bean_definition.bean_class.__name__ if bean_definition.bean_class else "Unknown",
                    "resource": getattr(bean_definition, 'resource', 'Unknown'),
                    "dependencies": self._get_bean_dependencies(bean_definition),
                    "aliases": [],  # TODO: 如果支持别名的话
                }

                # 实例状态 - 使用枚举
                bean_status = self.get_bean_status(bean_name)
                info["status"] = bean_status.value
                info["status_enum"] = bean_status.name
                info["is_active"] = bean_status.is_active()
                info["is_in_progress"] = bean_status.is_in_progress()

                # 实例信息
                if bean_name in self._singletons:
                    info["instance_type"] = type(self._singletons[bean_name]).__name__

                bean_info[bean_name] = info

            except Exception as e:
                logger.warning(f"Error getting info for bean '{bean_name}': {e}")
                bean_info[bean_name] = {"error": str(e)}

        return bean_info

    def get_simple_stats(self) -> dict[str, Any]:
        """获取简单统计信息

        Returns:
            包含Bean工厂基本统计的字典
        """
        # 按状态统计Bean数量
        status_stats = {}
        for status in BeanStatus:
            status_stats[status.value] = len(self.get_beans_by_status(status))

        return {
            "total_definitions": len(list(self._registry.names())),
            "created_singletons": len(self._singletons),
            "currently_creating": len(self._creating_beans),
            "dependency_graph_nodes": len(self._dependency_graph.get_all_nodes()) if hasattr(self._dependency_graph, 'get_all_nodes') else 0,
            "performance_stats": self._performance_stats.copy(),
            "status_statistics": status_stats,
        }

    def get_dependency_info(self) -> dict[str, Any]:
        """获取依赖关系信息

        Returns:
            包含依赖图信息的字典
        """
        try:
            return {
                "dependency_graph": self._dependency_graph.get_stats() if hasattr(self._dependency_graph, 'get_stats') else {},
                "creation_order": self._dependency_graph.get_creation_order() if hasattr(self._dependency_graph, 'get_creation_order') else [],
                "circular_dependencies": self._dependency_graph.get_circular_dependencies() if hasattr(self._dependency_graph, 'get_circular_dependencies') else [],
            }
        except Exception as e:
            logger.warning(f"Error getting dependency info: {e}")
            return {
                "dependency_graph": {},
                "creation_order": [],
                "circular_dependencies": [],
                "error": str(e)
            }

    @contextmanager
    def bean_creation_context(self, name: str):
        """Bean创建上下文管理器

        提供Bean创建的上下文管理，包括时间统计和日志记录。
        调用方需要自己创建工厂实例并使用此上下文管理器。

        Args:
            name: Bean名称

        Example:
            factory = DefaultBeanFactory()
            with factory.bean_creation_context("myBean"):
                # Bean创建逻辑
                pass
        """
        start_time = time.time()
        try:
            logger.debug(f"Starting creation of bean: {name}")
            yield
        finally:
            creation_time = time.time() - start_time
            logger.debug(f"Completed creation of bean '{name}' in {creation_time:.3f}s")
