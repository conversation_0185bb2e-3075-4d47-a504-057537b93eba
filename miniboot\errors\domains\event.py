#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Event相关异常类

事件系统相关的异常。
"""

from ..base import ApplicationError, BusinessError, ValidationError


# 事件发布相关异常 (BusinessError)
class EventPublishError(BusinessError):
    """事件发布错误 - 事件发布过程中的错误"""
    max_attempts = 3
    base_delay = 0.5
    strategy = "linear"


class EventPublisherError(BusinessError):
    """事件发布器错误 - 事件发布器相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class EventDispatchError(BusinessError):
    """事件分发错误 - 事件分发过程中的错误"""
    max_attempts = 3
    base_delay = 0.5


class EventBroadcastError(BusinessError):
    """事件广播错误 - 事件广播过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


# 事件处理相关异常 (BusinessError)
class EventHandlerError(BusinessError):
    """事件处理器错误 - 事件处理器执行过程中的错误"""
    max_attempts = 2
    base_delay = 0.5


class EventHandlerNotFoundError(BusinessError):
    """事件处理器未找到错误 - 找不到对应的事件处理器"""
    retryable = False


class EventHandlerRegistrationError(ApplicationError):
    """事件处理器注册错误 - 事件处理器注册过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


class EventHandlerExecutionError(BusinessError):
    """事件处理器执行错误 - 事件处理器执行失败"""
    max_attempts = 3
    base_delay = 0.5


# 事件监听相关异常 (BusinessError)
class EventListenerError(BusinessError):
    """事件监听器错误 - 事件监听器相关的错误"""
    max_attempts = 2
    base_delay = 0.5


class EventListenerRegistrationError(ApplicationError):
    """事件监听器注册错误 - 事件监听器注册失败"""
    max_attempts = 2
    base_delay = 1.0


class EventListenerExecutionError(BusinessError):
    """事件监听器执行错误 - 事件监听器执行失败"""
    max_attempts = 2
    base_delay = 0.5


# 事件类型相关异常 (ValidationError)
class EventTypeError(ValidationError):
    """事件类型错误 - 事件类型相关的错误"""
    # 继承ValidationError的属性：retryable = False


class InvalidEventTypeError(ValidationError):
    """无效事件类型错误 - 指定的事件类型无效"""
    # 继承ValidationError的属性：retryable = False


class EventSerializationError(ValidationError):
    """事件序列化错误 - 事件序列化过程中的错误"""
    # 继承ValidationError的属性：retryable = False


class EventDeserializationError(ValidationError):
    """事件反序列化错误 - 事件反序列化过程中的错误"""
    # 继承ValidationError的属性：retryable = False


# 事件队列相关异常 (BusinessError)
class EventQueueError(BusinessError):
    """事件队列错误 - 事件队列相关的错误"""
    max_attempts = 3
    base_delay = 1.0


class EventQueueFullError(BusinessError):
    """事件队列满错误 - 事件队列已满"""
    max_attempts = 5
    base_delay = 0.2
    strategy = "linear"


class EventQueueEmptyError(BusinessError):
    """事件队列空错误 - 事件队列为空"""
    retryable = False


class EventQueueTimeoutError(BusinessError):
    """事件队列超时错误 - 事件队列操作超时"""
    max_attempts = 2
    base_delay = 1.0


# 事件总线相关异常 (ApplicationError)
class EventBusError(ApplicationError):
    """事件总线错误 - 事件总线相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class EventBusNotStartedError(ApplicationError):
    """事件总线未启动错误 - 事件总线尚未启动"""
    retryable = False


class EventBusConfigurationError(ApplicationError):
    """事件总线配置错误 - 事件总线配置相关的错误"""
    retryable = False


# 事件过滤相关异常 (BusinessError)
class EventFilterError(BusinessError):
    """事件过滤器错误 - 事件过滤器相关的错误"""
    max_attempts = 2
    base_delay = 0.5


class EventFilterExecutionError(BusinessError):
    """事件过滤器执行错误 - 事件过滤器执行失败"""
    max_attempts = 2
    base_delay = 0.5


# 事件转换相关异常 (BusinessError)
class EventTransformError(BusinessError):
    """事件转换错误 - 事件转换过程中的错误"""
    max_attempts = 2
    base_delay = 0.5


class EventTransformerError(BusinessError):
    """事件转换器错误 - 事件转换器相关的错误"""
    max_attempts = 2
    base_delay = 0.5


# 事件路由相关异常 (BusinessError)
class EventRoutingError(BusinessError):
    """事件路由错误 - 事件路由过程中的错误"""
    max_attempts = 3
    base_delay = 0.5


class EventRouterError(BusinessError):
    """事件路由器错误 - 事件路由器相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class EventRouteNotFoundError(BusinessError):
    """事件路由未找到错误 - 找不到对应的事件路由"""
    retryable = False


# 事件存储相关异常 (ApplicationError)
class EventStoreError(ApplicationError):
    """事件存储错误 - 事件存储相关的错误"""
    max_attempts = 3
    base_delay = 1.0


class EventPersistenceError(ApplicationError):
    """事件持久化错误 - 事件持久化过程中的错误"""
    max_attempts = 3
    base_delay = 1.0


class EventRetrievalError(ApplicationError):
    """事件检索错误 - 事件检索过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


# 事件同步相关异常 (BusinessError)
class EventSynchronizationError(BusinessError):
    """事件同步错误 - 事件同步过程中的错误"""
    max_attempts = 3
    base_delay = 1.0


class EventOrderingError(BusinessError):
    """事件排序错误 - 事件排序过程中的错误"""
    max_attempts = 2
    base_delay = 0.5


# 事件安全相关异常 (ValidationError)
class EventSecurityError(ValidationError):
    """事件安全错误 - 事件安全相关的错误"""
    # 继承ValidationError的属性：retryable = False


class EventPermissionError(ValidationError):
    """事件权限错误 - 事件权限相关的错误"""
    # 继承ValidationError的属性：retryable = False


# 事件监控相关异常 (BusinessError)
class EventMonitoringError(BusinessError):
    """事件监控错误 - 事件监控相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class EventMetricsError(BusinessError):
    """事件指标错误 - 事件指标收集相关的错误"""
    max_attempts = 2
    base_delay = 0.5
