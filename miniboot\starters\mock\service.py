#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mock服务实现
"""

from loguru import logger

from .properties import MockProperties


class MockService:
    """Mock 演示实现"""

    def __init__(self, properties: MockProperties):
        """初始化 Mock 服务

        Args:
            properties: Mock配置属性
        """
        self.properties = properties

    def is_enabled(self) -> bool:
        """检查服务是否启用

        Returns:
            是否启用
        """
        return self.properties.enabled

    def print_mock(self, message: str) -> None:
        """打印 Mock 信息

        Args:
            message: 要打印的消息
        """
        if not self.is_enabled():
            return

        logger.info(f"{self.properties.prefix} {message}")

    def get_mock_data(self) -> dict:
        """获取Mock数据

        Returns:
            Mock数据字典
        """
        if not self.is_enabled():
            return {}

        return {
            "service": "MockService",
            "enabled": self.properties.enabled,
            "auto_mock": self.properties.auto_mock,
            "data_source": self.properties.data_source,
            "message": "This is mock data from MockService",
        }
