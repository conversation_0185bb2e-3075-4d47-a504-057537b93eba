"""
Actuator AutoConfigure 模块

自动配置模块，负责核心模块的指标采集自动配置。
基于条件注解实现智能化的配置加载。

自动配置类：
- BeanMetricsAutoConfiguration - Bean 工厂指标自动配置
- SchedulerMetricsAutoConfiguration - 调度器指标自动配置
- ContextMetricsAutoConfiguration - 应用上下文指标自动配置
- EnvMetricsAutoConfiguration - 环境配置指标自动配置
- WebAutoConfiguration - Web 集成自动配置

配置特性：
- 条件化加载：@ConditionalOnProperty, @ConditionalOnClass
- 智能检测：自动检测核心模块是否可用
- 配置隔离：每个模块独立配置，互不影响
- 性能优化：按需加载，避免不必要的资源消耗
"""

# 核心模块自动配置
from .bean import BeanMetricsAutoConfiguration, BeanMetricsCollector
from .context import ContextMetricsAutoConfiguration, ContextMetricsCollector
from .env import EnvMetricsAutoConfiguration, EnvMetricsCollector
from .performance import PerformanceMonitorAutoConfiguration
from .scheduler import (SchedulerMetricsAutoConfiguration,
                        SchedulerMetricsCollector)
from .web import WebAutoConfiguration

__all__ = [
    # 核心模块自动配置
    'BeanMetricsAutoConfiguration',
    'BeanMetricsCollector',
    'SchedulerMetricsAutoConfiguration',
    'SchedulerMetricsCollector',
    'ContextMetricsAutoConfiguration',
    'ContextMetricsCollector',
    'EnvMetricsAutoConfiguration',
    'EnvMetricsCollector',
    # 性能监控自动配置
    'PerformanceMonitorAutoConfiguration',
    # Web 集成自动配置
    'WebAutoConfiguration',
]
