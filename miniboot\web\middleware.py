#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 统一中间件管理器模块 - 提供自适应的中间件注册、配置和执行管理功能

统一中间件管理器模块

提供自适应的中间件注册、配置和执行管理功能.
支持传统模式和智能模式的自动切换.

主要功能:
- MiddlewareRegistry - 中间件注册表
- BaseMiddleware - 中间件基类
- ResponseMiddleware - 响应处理中间件
- 中间件注册和配置
- 中间件执行管理
"""

import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Callable, Optional

from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse
from loguru import logger

from ..errors import handle_context_exceptions, performance_monitor, timeout_handler
from .properties import WebProperties


class BaseMiddleware(ABC):
    """中间件基类"""

    def __init__(self, name: str):
        """初始化中间件

        Args:
            name: 中间件名称
        """
        self.name = name
        self.enabled = True

    @abstractmethod
    async def process_request(self, request: Request, call_next: Callable) -> Response:
        """处理请求

        Args:
            request: HTTP请求
            call_next: 下一个处理器

        Returns:
            HTTP响应
        """
        pass

    @abstractmethod
    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        """配置中间件

        Args:
            app: FastAPI应用实例
            properties: Web配置属性

        Returns:
            是否配置成功
        """
        pass

    def cleanup(self) -> None:
        """清理中间件资源

        子类可以重写此方法来实现自定义的资源清理逻辑.
        默认实现为空.
        """
        pass


class ResponseMiddleware(BaseMiddleware):
    """响应处理中间件

    统一处理响应格式,添加请求ID和响应时间等信息.
    继承自 BaseMiddleware,集成到统一的中间件管理系统中.
    """

    def __init__(
        self,
        name: str = "response",
        auto_wrap_response: bool = True,
        add_request_id: bool = True,
        add_response_time: bool = True,
    ):
        """初始化响应中间件

        Args:
            name: 中间件名称
            auto_wrap_response: 是否自动包装响应
            add_request_id: 是否添加请求ID
            add_response_time: 是否添加响应时间
        """
        super().__init__(name)
        self.auto_wrap_response = auto_wrap_response
        self.add_request_id = add_request_id
        self.add_response_time = add_response_time

        logger.debug(f"ResponseMiddleware '{name}' initialized")

    @handle_context_exceptions
    @performance_monitor(slow_threshold=1.0)
    @timeout_handler(timeout_seconds=30.0, timeout_message="Middleware processing timed out")
    async def process_request(self, request: Request, call_next: Callable) -> Response:
        """处理请求

        Args:
            request: HTTP请求
            call_next: 下一个处理函数

        Returns:
            HTTP响应
        """
        # 记录开始时间
        start_time = time.time()

        # 生成请求ID
        request_id = str(uuid.uuid4())
        if self.add_request_id:
            request.state.request_id = request_id

        try:
            # 调用下一个处理器
            response = await call_next(request)

            # 计算响应时间
            response_time = time.time() - start_time

            # 处理响应
            response = await self._handle_response(request, response, request_id, response_time)

            return response

        except Exception:
            # 异常会被全局异常处理器处理
            raise

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        """配置中间件

        Args:
            app: FastAPI应用实例（保留以备后续扩展）
            properties: Web配置属性（保留以备后续扩展）

        Returns:
            是否配置成功
        """
        try:
            # 保存引用以备后续使用
            self._app = app
            self._properties = properties

            # 响应中间件通常不需要特殊配置
            # 可以根据 properties 调整行为

            logger.debug(f"ResponseMiddleware '{self.name}' configured")
            return True

        except Exception as e:
            logger.error(f"Failed to configure ResponseMiddleware '{self.name}': {e}")
            return False

    async def _handle_response(self, request: Request, response: Response, request_id: str, response_time: float) -> Response:
        """处理响应

        Args:
            request: HTTP请求
            response: HTTP响应
            request_id: 请求ID
            response_time: 响应时间

        Returns:
            处理后的响应
        """
        # 添加响应头
        if self.add_request_id:
            response.headers["X-Request-ID"] = request_id

        if self.add_response_time:
            response.headers["X-Response-Time"] = f"{response_time:.3f}s"

        # 自动包装响应
        if self.auto_wrap_response and isinstance(response, JSONResponse):
            response = await self._wrap_json_response(request, response, request_id, response_time)

        return response

    async def _wrap_json_response(self, request: Request, response: JSONResponse, request_id: str, response_time: float) -> JSONResponse:
        """包装JSON响应

        Args:
            request: HTTP请求（保留以备后续扩展）
            response: JSON响应
            request_id: 请求ID
            response_time: 响应时间

        Returns:
            包装后的JSON响应
        """
        try:
            # 延迟导入避免循环依赖
            from .response import ApiResponse

            # 获取原始响应内容
            original_content = response.body

            # 尝试解析JSON内容
            parsed_content = None
            if original_content:
                try:
                    import json

                    if isinstance(original_content, bytes):
                        parsed_content = json.loads(original_content.decode("utf-8"))
                    elif isinstance(original_content, str):
                        parsed_content = json.loads(original_content)
                    else:
                        parsed_content = original_content
                except (json.JSONDecodeError, UnicodeDecodeError):
                    parsed_content = None

            # 如果已经是ApiResponse格式,直接返回
            if isinstance(parsed_content, dict) and "success" in parsed_content:
                return response

            # 包装为ApiResponse格式
            if response.status_code >= 400:
                # 错误响应
                api_response = ApiResponse.error(message="请求失败", code=response.status_code, data=parsed_content, request_id=request_id)
            else:
                # 成功响应
                api_response = ApiResponse.success(data=parsed_content, request_id=request_id, metadata={"response_time": f"{response_time:.3f}s"})

            # 创建新的响应
            return JSONResponse(status_code=response.status_code, content=api_response.to_dict(), headers=dict(response.headers))

        except Exception as e:
            logger.error(f"Failed to wrap JSON response: {e}")
            return response


class CorsMiddleware(BaseMiddleware):
    """CORS 中间件"""

    def __init__(
        self,
        name: str = "cors",
        allow_origins: Optional[list[str]] = None,
        allow_methods: Optional[list[str]] = None,
        allow_headers: Optional[list[str]] = None,
        expose_headers: Optional[list[str]] = None,
        allow_credentials: bool = False,
    ):
        super().__init__(name)
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        self.allow_headers = allow_headers or ["Content-Type", "Authorization", "X-Request-ID"]
        self.expose_headers = expose_headers or ["X-Request-ID", "X-Response-Time"]
        self.allow_credentials = allow_credentials

        logger.debug(f"CorsMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        # 处理预检请求
        if request.method == "OPTIONS":
            response = Response()
            self._add_cors_headers(response)
            return response

        # 处理正常请求
        response = await call_next(request)
        self._add_cors_headers(response)
        return response

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        try:
            # 保存引用以备后续使用
            self._app = app

            # 根据配置更新 CORS 设置
            if hasattr(properties, "cors") and properties.cors:
                if hasattr(properties.cors, "allowed_origins"):
                    self.allow_origins = properties.cors.allowed_origins
                if hasattr(properties.cors, "allowed_methods"):
                    self.allow_methods = properties.cors.allowed_methods
                if hasattr(properties.cors, "allowed_headers"):
                    self.allow_headers = properties.cors.allowed_headers
                if hasattr(properties.cors, "allow_credentials"):
                    self.allow_credentials = properties.cors.allow_credentials

            logger.debug(f"CorsMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure CorsMiddleware '{self.name}': {e}")
            return False

    def _add_cors_headers(self, response: Response):
        response.headers["Access-Control-Allow-Origin"] = ", ".join(self.allow_origins)
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
        response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
        response.headers["Access-Control-Expose-Headers"] = ", ".join(self.expose_headers)
        if self.allow_credentials:
            response.headers["Access-Control-Allow-Credentials"] = "true"


class CompressionMiddleware(BaseMiddleware):
    """压缩中间件

    支持 gzip 和 deflate 压缩算法，自动检测客户端支持的压缩格式。
    """

    def __init__(self, name: str = "compression", minimum_size: int = 1024, compression_level: int = 6):
        super().__init__(name)
        self.minimum_size = minimum_size
        self.compression_level = compression_level

        # 支持的压缩算法（按优先级排序）
        self.supported_encodings = ["gzip", "deflate"]

        # 可压缩的内容类型
        self.compressible_types = {
            "text/html",
            "text/plain",
            "text/css",
            "text/javascript",
            "application/json",
            "application/javascript",
            "application/xml",
            "text/xml",
            "application/rss+xml",
            "application/atom+xml",
        }

        logger.debug(f"CompressionMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # 检查是否需要压缩
        encoding = self._get_best_encoding(request, response)
        if encoding:
            try:
                # 执行实际的压缩
                compressed_response = await self._compress_response(response, encoding)
                return compressed_response
            except Exception as e:
                logger.warning(f"Compression failed: {e}, returning uncompressed response")
                return response

        return response

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        """配置压缩中间件

        Args:
            app: FastAPI应用实例（用于后续扩展）
            properties: Web属性配置

        Returns:
            配置是否成功
        """
        try:
            # 保存app引用以备后续使用
            self._app = app

            # 根据配置更新压缩设置
            if hasattr(properties, "compression") and properties.compression and hasattr(properties.compression, "min_size"):
                self.minimum_size = properties.compression.min_size
                # CompressionConfig 只有 enabled 和 min_size 属性
                # 其他属性使用默认值

            logger.debug(f"CompressionMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure CompressionMiddleware '{self.name}': {e}")
            return False

    def _get_best_encoding(self, request: Request, response: Response) -> Optional[str]:
        """获取最佳压缩编码

        Args:
            request: HTTP请求
            response: HTTP响应

        Returns:
            最佳压缩编码,如果不需要压缩则返回None
        """
        # 检查是否已经压缩
        if response.headers.get("content-encoding"):
            return None

        # 检查内容类型是否可压缩
        content_type = response.headers.get("content-type", "").split(";")[0].strip()
        if content_type not in self.compressible_types:
            return None

        # 检查响应大小
        content_length = len(response.body) if hasattr(response, "body") and response.body else int(response.headers.get("content-length", 0))

        if content_length < self.minimum_size:
            return None

        # 检查客户端支持的编码
        accept_encoding = request.headers.get("accept-encoding", "").lower()
        for encoding in self.supported_encodings:
            if encoding in accept_encoding:
                return encoding

        return None

    async def _compress_response(self, response: Response, encoding: str) -> Response:
        """压缩响应内容

        Args:
            response: 原始响应
            encoding: 压缩编码

        Returns:
            压缩后的响应
        """
        import gzip
        import zlib

        from fastapi.responses import Response as FastAPIResponse

        # 获取响应内容
        if hasattr(response, "body") and response.body:
            content = response.body
        else:
            # 对于流式响应，需要特殊处理
            return response

        # 执行压缩
        if encoding == "gzip":
            compressed_content = gzip.compress(content, compresslevel=self.compression_level)
        elif encoding == "deflate":
            compressed_content = zlib.compress(content, level=self.compression_level)
        else:
            return response

        # 创建新的响应
        compressed_response = FastAPIResponse(
            content=compressed_content,
            status_code=response.status_code,
            headers=dict(response.headers),
            media_type=response.headers.get("content-type"),
        )

        # 更新响应头
        compressed_response.headers["content-encoding"] = encoding
        compressed_response.headers["content-length"] = str(len(compressed_content))

        # 添加 Vary 头，告诉缓存服务器根据 Accept-Encoding 缓存不同版本
        vary_header = compressed_response.headers.get("vary", "")
        if "accept-encoding" not in vary_header.lower():
            if vary_header:
                compressed_response.headers["vary"] = f"{vary_header}, Accept-Encoding"
            else:
                compressed_response.headers["vary"] = "Accept-Encoding"

        return compressed_response


class LoggingMiddleware(BaseMiddleware):
    """详细日志中间件

    记录完整的HTTP请求和响应报文，包括头部、正文等详细信息。
    """

    def __init__(
        self,
        name: str = "logging",
        log_requests: bool = True,
        log_responses: bool = True,
        log_headers: bool = True,
        log_body: bool = True,
        log_query_params: bool = True,
        max_body_size: int = 10240,  # 10KB
        sensitive_headers: Optional[list[str]] = None,
    ):
        super().__init__(name)
        self.log_requests = log_requests
        self.log_responses = log_responses
        self.log_headers = log_headers
        self.log_body = log_body
        self.log_query_params = log_query_params
        self.max_body_size = max_body_size

        # 敏感头部列表（将被脱敏处理）
        self.sensitive_headers = {
            header.lower() for header in (sensitive_headers or ["authorization", "cookie", "set-cookie", "x-api-key", "x-auth-token"])
        }

        logger.debug(f"LoggingMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        request_id = getattr(request.state, "request_id", str(uuid.uuid4())[:8])

        # 记录详细请求信息
        if self.log_requests:
            await self._log_request_details(request, request_id)

        response = await call_next(request)

        # 记录详细响应信息
        if self.log_responses:
            duration = time.time() - start_time
            # 如果需要记录响应体，包装响应以捕获内容
            if self.log_body:
                response = await self._wrap_response_for_logging(response, request_id, duration)
            else:
                await self._log_response_details(response, request_id, duration)

        return response

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        try:
            # 保存引用以备后续使用
            self._app = app

            # 根据配置更新日志设置
            if hasattr(properties, "logging") and properties.logging:
                if hasattr(properties.logging, "log_requests"):
                    self.log_requests = properties.logging.log_requests
                if hasattr(properties.logging, "log_responses"):
                    self.log_responses = properties.logging.log_responses
                if hasattr(properties.logging, "log_headers"):
                    self.log_headers = properties.logging.log_headers
                if hasattr(properties.logging, "log_body"):
                    self.log_body = properties.logging.log_body
                if hasattr(properties.logging, "log_query_params"):
                    self.log_query_params = properties.logging.log_query_params
                if hasattr(properties.logging, "max_body_size"):
                    self.max_body_size = properties.logging.max_body_size

            logger.debug(f"LoggingMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure LoggingMiddleware '{self.name}': {e}")
            return False

    async def _log_request_details(self, request: Request, request_id: str):
        """记录详细的请求信息"""
        try:
            # 基本请求信息
            log_lines = [
                f"📥 [REQUEST {request_id}] {request.method} {request.url}",
                f"   Client: {request.client.host}:{request.client.port}" if request.client else "   Client: Unknown",
            ]

            # 查询参数
            if self.log_query_params and request.query_params:
                query_str = "&".join([f"{k}={v}" for k, v in request.query_params.items()])
                log_lines.append(f"   Query: {query_str}")

            # 请求头
            if self.log_headers:
                log_lines.append("   Headers:")
                for name, value in request.headers.items():
                    if name.lower() in self.sensitive_headers:
                        value = self._mask_sensitive_data(value)
                    log_lines.append(f"     {name}: {value}")

            # 请求体
            if self.log_body and request.method in ["POST", "PUT", "PATCH", "DELETE"]:
                body_content = await self._get_request_body(request)
                if body_content:
                    log_lines.append("   Body:")
                    log_lines.append(f"     {body_content}")

            # 输出所有日志行
            for line in log_lines:
                logger.info(line)

        except Exception as e:
            logger.warning(f"Failed to log request details: {e}")

    async def _log_response_details(self, response: Response, request_id: str, duration: float):
        """记录详细的响应信息"""
        try:
            # 基本响应信息
            log_lines = [f"📤 [RESPONSE {request_id}] {response.status_code} - {duration:.3f}s"]

            # 响应头
            if self.log_headers:
                log_lines.append("   Headers:")
                for name, value in response.headers.items():
                    if name.lower() in self.sensitive_headers:
                        value = self._mask_sensitive_data(value)
                    log_lines.append(f"     {name}: {value}")

            # 响应体
            if self.log_body:
                body_content = await self._get_response_body(response)
                if body_content:
                    log_lines.append("   Body:")
                    log_lines.append(f"     {body_content}")

            # 输出所有日志行
            for line in log_lines:
                logger.info(line)

        except Exception as e:
            logger.warning(f"Failed to log response details: {e}")

    async def _wrap_response_for_logging(self, response: Response, request_id: str, duration: float) -> Response:
        """包装响应以捕获响应体内容进行日志记录"""
        try:
            # 记录响应信息
            logger.info(f"📤 [RESPONSE {request_id}] {response.status_code} - {duration:.3f}s")

            # 记录响应头
            if self.log_headers:
                logger.info("   Headers:")
                for name, value in response.headers.items():
                    if name.lower() in self.sensitive_headers:
                        value = self._mask_sensitive_data(value)
                    logger.info(f"     {name}: {value}")

            # 记录JSON响应体
            if response.headers.get("content-type") == "application/json":
                from starlette.responses import StreamingResponse as StarletteStreamingResponse

                # 检查是否是 StreamingResponse (包括 Starlette 的内部类型)
                if hasattr(response, "body_iterator"):
                    body = []
                    try:
                        body_iterator = getattr(response, "body_iterator", None)
                        if body_iterator:
                            async for chunk in body_iterator:
                                body.append(chunk)
                            body = b"".join(body)

                            logger.info("   Body (JSON):")
                            logger.info(f"     {body.decode()}")
                            logger.info("-" * 80)

                            # 重新创建 StreamingResponse 返回给客户端
                            return StarletteStreamingResponse(
                                content=iter([body]), status_code=response.status_code, headers=dict(response.headers), media_type=response.media_type
                            )
                    except Exception as e:
                        logger.debug(f"Failed to process streaming response: {e}")
                        # 如果处理失败，尝试其他方法
                        pass

                # 对于非流式响应或处理失败的情况，尝试从其他方式获取内容
                body_content = await self._get_response_body(response)
                if body_content:
                    logger.info("   Body (JSON):")
                    logger.info(f"     {body_content}")
                logger.info("-" * 80)
                return response
            else:
                # 对于非JSON响应，直接记录基本信息
                await self._log_response_details(response, request_id, duration)
                return response

        except Exception as e:
            logger.warning(f"Failed to wrap response for logging: {e}")
            # 如果包装失败，至少记录基本信息
            await self._log_response_details(response, request_id, duration)
            return response

    async def _log_response_details_with_body(self, response: Response, request_id: str, duration: float, body: bytes):
        """记录包含响应体的详细响应信息"""
        try:
            # 基本响应信息
            log_lines = [f"📤 [RESPONSE {request_id}] {response.status_code} - {duration:.3f}s"]

            # 响应头
            if self.log_headers:
                log_lines.append("   Headers:")
                for name, value in response.headers.items():
                    if name.lower() in self.sensitive_headers:
                        value = self._mask_sensitive_data(value)
                    log_lines.append(f"     {name}: {value}")

            # 响应体
            if self.log_body and body:
                log_lines.append("   Body:")
                try:
                    # 限制日志大小
                    if len(body) > self.max_body_size:
                        truncated_body = body[: self.max_body_size]
                        try:
                            content = truncated_body.decode("utf-8")
                            log_lines.append(f"     {content}... [TRUNCATED - Total size: {len(body)} bytes]")
                        except UnicodeDecodeError:
                            log_lines.append(
                                f"     [BINARY DATA - Size: {len(body)} bytes, showing first {self.max_body_size} bytes as hex] {truncated_body.hex()}"
                            )
                    else:
                        # 尝试解码为文本
                        try:
                            content = body.decode("utf-8")
                            log_lines.append(f"     {content}")
                        except UnicodeDecodeError:
                            log_lines.append(f"     [BINARY DATA - Size: {len(body)} bytes] {body.hex()}")
                except Exception as body_error:
                    log_lines.append(f"     [ERROR reading body: {body_error}]")

            # 输出所有日志行
            for line in log_lines:
                logger.info(line)

        except Exception as e:
            logger.warning(f"Failed to log response details with body: {e}")

    async def _get_request_body(self, request: Request) -> Optional[str]:
        """获取请求体内容"""
        try:
            # 检查是否有请求体
            if not hasattr(request, "_body"):
                # 读取请求体
                body = await request.body()
                # 重新设置请求体，以便后续处理器可以读取
                request._body = body
            else:
                body = request._body

            if not body:
                return None

            # 限制日志大小
            if len(body) > self.max_body_size:
                truncated_body = body[: self.max_body_size]
                try:
                    content = truncated_body.decode("utf-8")
                    return f"{content}... [TRUNCATED - Total size: {len(body)} bytes]"
                except UnicodeDecodeError:
                    return f"[BINARY DATA - Size: {len(body)} bytes, showing first {self.max_body_size} bytes as hex] {truncated_body.hex()}"

            # 尝试解码为文本
            try:
                return body.decode("utf-8")
            except UnicodeDecodeError:
                return f"[BINARY DATA - Size: {len(body)} bytes] {body.hex()}"

        except Exception as e:
            logger.warning(f"Failed to get request body: {e}")
            return None

    async def _get_response_body(self, response: Response) -> Optional[str]:
        """获取响应体内容"""
        try:
            # 对于 FastAPI 的 JSONResponse，直接从 content 属性获取
            from fastapi.responses import JSONResponse

            if isinstance(response, JSONResponse):
                # JSONResponse 的内容存储在 content 属性中
                content = getattr(response, "content", None)
                if content is not None:
                    # 直接序列化 content
                    import json

                    try:
                        body_str = json.dumps(content, ensure_ascii=False)
                        logger.debug(f"Successfully extracted JSONResponse content: {len(body_str)} chars")

                        # 限制日志大小
                        if len(body_str) > self.max_body_size:
                            truncated = body_str[: self.max_body_size]
                            return f"{truncated}... [TRUNCATED - Total size: {len(body_str)} chars]"
                        return body_str
                    except Exception as json_error:
                        logger.debug(f"Failed to serialize JSONResponse content: {json_error}")
                        return str(content)

            # 对于其他类型的响应，尝试多种方法获取内容
            body = None

            # 方法1: 尝试从 response.body 获取
            if hasattr(response, "body") and response.body:
                body = response.body

            # 方法2: 尝试从 response._content 获取 (使用 getattr 避免类型检查警告)
            elif hasattr(response, "_content"):
                _content = getattr(response, "_content", None)
                if _content is not None:
                    body = _content

            # 方法3: 跳过复杂的 render 调用，避免参数问题
            # elif hasattr(response, "render"):
            #     # render 方法的参数要求复杂，暂时跳过

            if body is None:
                logger.debug(f"No response body found. Response type: {type(response)}")
                return None

            # 确保 body 是 bytes 类型
            if isinstance(body, memoryview):
                body = body.tobytes()
            elif isinstance(body, str):
                body = body.encode("utf-8")
            elif not isinstance(body, bytes):
                # 尝试序列化为 JSON 或字符串
                try:
                    import json

                    if hasattr(body, "__dict__") or isinstance(body, (dict, list)):
                        body = json.dumps(body, ensure_ascii=False).encode("utf-8")
                    else:
                        body = str(body).encode("utf-8")
                except Exception:
                    body = str(body).encode("utf-8")

            # 限制日志大小
            if len(body) > self.max_body_size:
                truncated_body = body[: self.max_body_size]
                try:
                    content = truncated_body.decode("utf-8")
                    return f"{content}... [TRUNCATED - Total size: {len(body)} bytes]"
                except UnicodeDecodeError:
                    return f"[BINARY DATA - Size: {len(body)} bytes, showing first {self.max_body_size} bytes as hex] {truncated_body.hex()}"

            # 尝试解码为文本
            try:
                return body.decode("utf-8")
            except UnicodeDecodeError:
                return f"[BINARY DATA - Size: {len(body)} bytes] {body.hex()}"

        except Exception as e:
            logger.warning(f"Failed to get response body: {e}")
            return None

    def _mask_sensitive_data(self, value: str) -> str:
        """脱敏处理敏感数据"""
        if not value:
            return value

        # 对于较短的值，显示前几个字符
        if len(value) <= 8:
            return "*" * len(value)

        # 对于较长的值，显示前后几个字符
        return f"{value[:3]}***{value[-3:]}"


class CustomMiddleware(BaseMiddleware):
    """自定义中间件"""

    def __init__(self, name: str, process_func: Optional[Callable] = None, configure_func: Optional[Callable] = None):
        super().__init__(name)
        self.process_func = process_func
        self.configure_func = configure_func

        logger.debug(f"CustomMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        if self.process_func:
            return await self.process_func(request, call_next)
        else:
            return await call_next(request)

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        try:
            if self.configure_func:
                return self.configure_func(app, properties)

            logger.debug(f"CustomMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure CustomMiddleware '{self.name}': {e}")
            return False


@dataclass
class MiddlewareInfo:
    """中间件信息"""

    name: str
    middleware: BaseMiddleware
    priority: int = 0
    enabled: bool = True
    configured: bool = False
    error: Optional[str] = None

    # 智能功能扩展(可选)
    execution_stats: dict[str, Any] = field(default_factory=dict)
    performance_metrics: dict[str, float] = field(default_factory=dict)
    optimization_hints: list[str] = field(default_factory=list)


class MiddlewareRegistry:
    """中间件注册表

    负责中间件的注册、配置和生命周期管理。
    提供统一的中间件管理接口，支持与FastAPI应用的无缝集成。

    主要功能:
    - 中间件注册和注销
    - 中间件配置管理
    - FastAPI集成
    - 状态查询和监控
    """

    def __init__(self):
        """初始化中间件注册表"""
        # 基础功能
        self.middlewares: dict[str, MiddlewareInfo] = {}

        logger.debug("MiddlewareRegistry initialized")

    @handle_context_exceptions
    @performance_monitor(slow_threshold=1.0)
    def register(self, middleware: BaseMiddleware) -> bool:
        """注册中间件

        Args:
            middleware: 中间件实例

        Returns:
            是否注册成功
        """
        if middleware.name in self.middlewares:
            logger.warning(f"Middleware {middleware.name} already registered")
            return False

        middleware_info = MiddlewareInfo(name=middleware.name, middleware=middleware, enabled=middleware.enabled)

        self.middlewares[middleware.name] = middleware_info

        logger.debug(f"Registered middleware: {middleware.name}")
        return True

    def unregister(self, name: str) -> bool:
        """注销中间件

        Args:
            name: 中间件名称

        Returns:
            是否注销成功
        """
        if name not in self.middlewares:
            return False

        del self.middlewares[name]

        logger.debug(f"Unregistered middleware: {name}")
        return True

    def contains(self, name: str) -> bool:
        """检查是否包含指定中间件

        Args:
            name: 中间件名称

        Returns:
            是否包含该中间件
        """
        return name in self.middlewares

    def clear(self) -> None:
        """清空所有中间件"""
        self.middlewares.clear()
        logger.debug("Cleared all middlewares")

    def enable(self, name: str) -> bool:
        """启用中间件

        Args:
            name: 中间件名称

        Returns:
            是否成功启用
        """
        if name not in self.middlewares:
            logger.warning(f"Middleware {name} not found")
            return False

        self.middlewares[name].enabled = True
        logger.debug(f"Enabled middleware: {name}")
        return True

    def disable(self, name: str) -> bool:
        """禁用中间件

        Args:
            name: 中间件名称

        Returns:
            是否成功禁用
        """
        if name not in self.middlewares:
            logger.warning(f"Middleware {name} not found")
            return False

        self.middlewares[name].enabled = False
        logger.debug(f"Disabled middleware: {name}")
        return True

    def is_enabled(self, name: str) -> bool:
        """检查中间件是否启用

        Args:
            name: 中间件名称

        Returns:
            是否启用,如果中间件不存在返回False
        """
        if name not in self.middlewares:
            return False
        return self.middlewares[name].enabled

    def count(self) -> int:
        """获取中间件数量

        Returns:
            中间件总数
        """
        return len(self.middlewares)

    def names(self) -> list[str]:
        """获取所有中间件名称

        Returns:
            中间件名称列表
        """
        return list(self.middlewares.keys())

    def configure(self, app: FastAPI, properties: WebProperties) -> None:
        """配置所有中间件

        Args:
            app: FastAPI应用实例
            properties: Web配置属性
        """
        success_count = 0
        total_count = len(self.middlewares)

        for name, info in self.middlewares.items():
            try:
                if info.middleware.configure(app, properties):
                    info.configured = True
                    info.error = None
                    success_count += 1
                    logger.debug(f"Configured middleware: {name}")
                else:
                    info.configured = False
                    info.error = "Configuration failed"
                    logger.error(f"Failed to configure middleware: {name}")

            except Exception as e:
                info.configured = False
                info.error = str(e)
                logger.error(f"Error configuring middleware {name}: {e}")

        logger.debug(f"Configured {success_count}/{total_count} middlewares")

        # 将配置好的中间件添加到 FastAPI 应用
        self._apply(app)

    def _apply(self, app: FastAPI):
        """将配置好的中间件应用到 FastAPI 应用（私有方法）

        Args:
            app: FastAPI应用实例
        """
        from starlette.middleware.base import BaseHTTPMiddleware

        for name, info in self.middlewares.items():
            if not info.configured:
                continue

            try:
                middleware = info.middleware

                # 统一包装器：将所有 BaseMiddleware 包装成 BaseHTTPMiddleware
                class UniversalHTTPMiddleware(BaseHTTPMiddleware):
                    def __init__(self, app, base_middleware):
                        super().__init__(app)
                        self.base_middleware = base_middleware

                    async def dispatch(self, request, call_next):
                        return await self.base_middleware.process_request(request, call_next)

                app.add_middleware(UniversalHTTPMiddleware, base_middleware=middleware)
                logger.debug(f"Added {middleware.__class__.__name__} '{name}' to FastAPI")

            except Exception as e:
                logger.error(f"Failed to add middleware '{name}' to FastAPI: {e}")

    def get(self, name: str) -> Optional[MiddlewareInfo]:
        """获取中间件信息

        Args:
            name: 中间件名称

        Returns:
            中间件信息,如果不存在则返回None
        """
        return self.middlewares.get(name)

    def list(self) -> dict[str, MiddlewareInfo]:
        """获取所有中间件信息

        Returns:
            中间件信息字典
        """
        return self.middlewares.copy()

    def get_all_middlewares(self) -> dict[str, MiddlewareInfo]:
        """获取所有中间件信息

        这是list方法的别名，用于向后兼容和语义清晰性.

        Returns:
            中间件信息字典，键为中间件名称，值为MiddlewareInfo对象
        """
        return self.list()

    def status(self) -> dict[str, Any]:
        """获取状态信息

        Returns:
            状态信息
        """
        status = {
            "total_middlewares": len(self.middlewares),
            "configured_middlewares": sum(1 for info in self.middlewares.values() if info.configured),
            "failed_middlewares": [
                {"name": name, "error": info.error} for name, info in self.middlewares.items() if not info.configured and info.error
            ],
        }

        # 简化模式：只返回基础状态
        status["mode"] = "simplified"

        return status

    def cleanup(self) -> None:
        """清理资源"""
        # 清理所有中间件资源
        for name, info in self.middlewares.items():
            try:
                # 如果中间件有清理方法，调用它
                if hasattr(info.middleware, "cleanup"):
                    info.middleware.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up middleware {name}: {e}")

        logger.debug("MiddlewareRegistry cleanup completed")
