#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频配置属性测试
"""

import pytest
from miniboot.starters.audio.properties import AudioProperties, AudioPlayerConfig, TTSConfig


class TestAudioPlayerConfig:
    """音频播放器配置测试"""

    def test_default_values(self):
        """测试默认值"""
        config = AudioPlayerConfig()

        assert config.enabled is True
        assert config.volume == 1.0
        assert config.timeout_seconds == 30
        assert config.supported_formats == [".mp3", ".wav", ".m4a", ".ogg"]

    def test_custom_values(self):
        """测试自定义值"""
        config = AudioPlayerConfig(enabled=False, volume=0.5, timeout_seconds=60, supported_formats=[".mp3", ".wav"])

        assert config.enabled is False
        assert config.volume == 0.5
        assert config.timeout_seconds == 60
        assert config.supported_formats == [".mp3", ".wav"]


class TestTTSConfig:
    """TTS配置测试"""

    def test_default_values(self):
        """测试默认值"""
        config = TTSConfig()

        assert config.enabled is True
        assert config.engine == "pyttsx3"
        assert config.language == "zh-CN"
        assert config.rate == 150
        assert config.volume == 1.0

    def test_custom_values(self):
        """测试自定义值"""
        config = TTSConfig(enabled=False, engine="azure", language="en-US", rate=200, volume=0.8)

        assert config.enabled is False
        assert config.engine == "azure"
        assert config.language == "en-US"
        assert config.rate == 200
        assert config.volume == 0.8


class TestAudioProperties:
    """音频属性测试"""

    def test_default_values(self):
        """测试默认值"""
        props = AudioProperties()

        assert props.enabled is True
        assert isinstance(props.player, AudioPlayerConfig)
        assert isinstance(props.tts, TTSConfig)

    def test_validation_success(self):
        """测试验证成功"""
        props = AudioProperties()
        # 默认值应该通过验证
        props.validate()  # 不应该抛出异常

    def test_validation_audio_volume_invalid(self):
        """测试音频音量验证失败"""
        props = AudioProperties()
        props.player.volume = 1.5  # 无效值

        with pytest.raises(ValueError, match="音频播放音量必须在 0.0 到 1.0 之间"):
            props.validate()

    def test_validation_tts_volume_invalid(self):
        """测试TTS音量验证失败"""
        props = AudioProperties()
        props.tts.volume = -0.1  # 无效值

        with pytest.raises(ValueError, match="TTS音量必须在 0.0 到 1.0 之间"):
            props.validate()

    def test_validation_tts_rate_invalid(self):
        """测试TTS语速验证失败"""
        props = AudioProperties()
        props.tts.rate = 400  # 无效值

        with pytest.raises(ValueError, match="TTS语速必须在 50 到 300 之间"):
            props.validate()

    def test_validation_timeout_invalid(self):
        """测试超时时间验证失败"""
        props = AudioProperties()
        props.player.timeout_seconds = 0  # 无效值

        with pytest.raises(ValueError, match="音频播放超时时间必须大于 0"):
            props.validate()
