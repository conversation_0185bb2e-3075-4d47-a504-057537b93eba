{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "context7-mcp": {"url": "https://mcp.context7.com/mcp"}, "deepwiki-mcp": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager@latest"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}, "promptx": {"command": "npx", "args": ["-y", "-f", "--registry", "https://registry.npmjs.org", "dpml-prompt@latest", "mcp-server"]}}}