#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: DefaultBeanFactory核心功能单元测试
"""

import unittest
import threading
import time
import contextlib
from typing import Optional, Any
from unittest.mock import Mock

from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.definition import BeanDefinition, BeanScope, BeanStatus
from miniboot.bean.registry import DefaultBeanDefinitionRegistry





class TestService:
    """测试用的服务类"""
    def __init__(self, name: str = "test"):
        self.name = name
        self.initialized = False

    def init(self):
        self.initialized = True

    def destroy(self):
        self.initialized = False


class TestDependentService:
    """测试用的依赖服务类"""
    def __init__(self, service: Optional[TestService] = None):
        self.service = service


class TestRepository:
    """测试用的仓储类"""
    def __init__(self):
        self.data = {}

    def save(self, key: str, value: Any):
        self.data[key] = value

    def find(self, key: str):
        return self.data.get(key)


class DefaultBeanFactoryTestCase(unittest.TestCase):
    """DefaultBeanFactory核心功能测试"""

    def setUp(self):
        """测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    def tearDown(self):
        """测试后清理"""
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()
        self.factory = None
        self.registry = None

    def test_factory_initialization(self):
        """测试工厂初始化"""
        # 测试默认初始化
        factory = DefaultBeanFactory()
        self.assertIsNotNone(factory._registry)
        self.assertIsNotNone(factory._cache_manager)
        self.assertIsNotNone(factory._scope_manager)
        self.assertIsNotNone(factory._dependency_graph)

        # 测试带注册表初始化
        registry = DefaultBeanDefinitionRegistry()
        factory = DefaultBeanFactory(registry)
        self.assertIs(factory._registry, registry)

    def test_bean_status_management(self):
        """测试Bean状态管理"""
        bean_name = "testBean"

        # 初始状态
        status = self.factory.get_bean_status(bean_name)
        self.assertEqual(status, BeanStatus.NOT_CREATED)

        # 设置状态
        self.factory.set_bean_status(bean_name, BeanStatus.CREATING)
        status = self.factory.get_bean_status(bean_name)
        self.assertEqual(status, BeanStatus.CREATING)

        # 状态转换
        self.factory.set_bean_status(bean_name, BeanStatus.CREATED)
        status = self.factory.get_bean_status(bean_name)
        self.assertEqual(status, BeanStatus.CREATED)

    def test_get_beans_by_status(self):
        """测试按状态获取Bean"""
        # 设置一些Bean状态（遵循状态转换规则）
        self.factory.set_bean_status("bean1", BeanStatus.CREATING)
        self.factory.set_bean_status("bean2", BeanStatus.CREATING)

        # bean3: NOT_CREATED -> CREATING -> CREATED
        self.factory.set_bean_status("bean3", BeanStatus.CREATING)
        self.factory.set_bean_status("bean3", BeanStatus.CREATED)

        # 获取指定状态的Bean
        creating_beans = self.factory.get_beans_by_status(BeanStatus.CREATING)
        created_beans = self.factory.get_beans_by_status(BeanStatus.CREATED)
        not_created_beans = self.factory.get_beans_by_status(BeanStatus.NOT_CREATED)

        self.assertEqual(len(creating_beans), 2)
        self.assertIn("bean1", creating_beans)
        self.assertIn("bean2", creating_beans)

        self.assertEqual(len(created_beans), 1)
        self.assertIn("bean3", created_beans)

        # 测试不存在的状态
        self.assertEqual(len(not_created_beans), 0)

    def test_contains_bean(self):
        """测试Bean存在检查"""
        # 注册Bean定义
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 检查Bean存在
        self.assertTrue(self.factory.contains_bean("testBean"))
        self.assertFalse(self.factory.contains_bean("nonExistentBean"))

    def test_contains_local_bean(self):
        """测试本地Bean存在检查"""
        # 注册Bean定义
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 检查本地Bean存在
        self.assertTrue(self.factory.contains_local_bean("testBean"))
        self.assertFalse(self.factory.contains_local_bean("nonExistentBean"))

    def test_get_bean_simple(self):
        """测试简单Bean获取"""
        # 注册Bean定义
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 获取Bean
        bean = self.factory.get_bean("testService")

        self.assertIsInstance(bean, TestService)
        self.assertEqual(bean.name, "test")

    def test_get_bean_with_type_check(self):
        """测试带类型检查的Bean获取"""
        # 注册Bean定义
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 正确类型
        bean = self.factory.get_bean("testService", TestService)
        self.assertIsInstance(bean, TestService)

        # 错误类型
        with self.assertRaises(TypeError):
            self.factory.get_bean("testService", TestRepository)

    def test_get_bean_not_found(self):
        """测试获取不存在的Bean"""
        with self.assertRaises(KeyError):
            self.factory.get_bean("nonExistentBean")

    def test_singleton_bean_caching(self):
        """测试单例Bean缓存"""
        # 注册单例Bean
        definition = BeanDefinition("singletonService", TestService, BeanScope.SINGLETON)
        self.registry.register("singletonService", definition)

        # 多次获取应该返回同一实例
        bean1 = self.factory.get_bean("singletonService")
        bean2 = self.factory.get_bean("singletonService")

        self.assertIs(bean1, bean2)

    def test_prototype_bean_creation(self):
        """测试原型Bean创建"""
        # 注册原型Bean
        definition = BeanDefinition("prototypeService", TestService, BeanScope.PROTOTYPE)
        self.registry.register("prototypeService", definition)

        # 多次获取应该返回不同实例
        bean1 = self.factory.get_bean("prototypeService")
        bean2 = self.factory.get_bean("prototypeService")

        self.assertIsNot(bean1, bean2)
        self.assertIsInstance(bean1, TestService)
        self.assertIsInstance(bean2, TestService)

    def test_get_type(self):
        """测试获取Bean类型"""
        # 注册Bean定义
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 获取类型
        bean_type = self.factory.get_type("testService")
        self.assertEqual(bean_type, TestService)

        # 不存在的Bean
        bean_type = self.factory.get_type("nonExistentBean")
        self.assertIsNone(bean_type)

    def test_is_singleton(self):
        """测试单例检查"""
        # 注册单例Bean
        singleton_def = BeanDefinition("singletonBean", TestService, BeanScope.SINGLETON)
        self.registry.register("singletonBean", singleton_def)

        # 注册原型Bean
        prototype_def = BeanDefinition("prototypeBean", TestService, BeanScope.PROTOTYPE)
        self.registry.register("prototypeBean", prototype_def)

        # 检查单例
        self.assertTrue(self.factory.is_singleton("singletonBean"))
        self.assertFalse(self.factory.is_singleton("prototypeBean"))
        self.assertFalse(self.factory.is_singleton("nonExistentBean"))

    def test_is_prototype(self):
        """测试原型检查"""
        # 注册单例Bean
        singleton_def = BeanDefinition("singletonBean", TestService, BeanScope.SINGLETON)
        self.registry.register("singletonBean", singleton_def)

        # 注册原型Bean
        prototype_def = BeanDefinition("prototypeBean", TestService, BeanScope.PROTOTYPE)
        self.registry.register("prototypeBean", prototype_def)

        # 检查原型
        self.assertFalse(self.factory.is_prototype("singletonBean"))
        self.assertTrue(self.factory.is_prototype("prototypeBean"))
        self.assertFalse(self.factory.is_prototype("nonExistentBean"))

    def test_get_bean_definition_names(self):
        """测试获取Bean定义名称"""
        # 注册一些Bean
        def1 = BeanDefinition("bean1", TestService)
        def2 = BeanDefinition("bean2", TestRepository)

        self.registry.register("bean1", def1)
        self.registry.register("bean2", def2)

        # 获取Bean名称
        names = self.factory.get_bean_definition_names()
        self.assertEqual(len(names), 2)
        self.assertIn("bean1", names)
        self.assertIn("bean2", names)

    def test_get_bean_definition_count(self):
        """测试获取Bean定义数量"""
        initial_count = self.factory.get_bean_definition_count()

        # 注册Bean
        definition = BeanDefinition("testBean", TestService)
        self.registry.register("testBean", definition)

        # 检查数量
        new_count = self.factory.get_bean_definition_count()
        self.assertEqual(new_count, initial_count + 1)

    def test_get_beans_of_type(self):
        """测试按类型获取Bean"""
        # 注册不同类型的Bean
        service_def = BeanDefinition("testService", TestService)
        repo_def = BeanDefinition("testRepository", TestRepository)

        self.registry.register("testService", service_def)
        self.registry.register("testRepository", repo_def)

        # 获取指定类型的Bean
        services = self.factory.get_beans_of_type(TestService)
        repositories = self.factory.get_beans_of_type(TestRepository)

        self.assertEqual(len(services), 1)
        self.assertIn("testService", services)
        self.assertIsInstance(services["testService"], TestService)

        self.assertEqual(len(repositories), 1)
        self.assertIn("testRepository", repositories)
        self.assertIsInstance(repositories["testRepository"], TestRepository)

    def test_destroy_singletons(self):
        """测试销毁单例Bean"""
        # 注册单例Bean
        definition = BeanDefinition("testService", TestService, BeanScope.SINGLETON)
        self.registry.register("testService", definition)

        # 创建Bean
        bean = self.factory.get_bean("testService")
        self.assertIsNotNone(bean)

        # 销毁单例
        self.factory.destroy_singletons()

        # 再次获取应该创建新实例
        new_bean = self.factory.get_bean("testService")
        self.assertIsNot(bean, new_bean)

    def test_performance_stats(self):
        """测试性能统计"""
        # 注册Bean
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        # 获取Bean（应该增加统计）
        self.factory.get_bean("testService")

        # 检查统计信息
        stats = self.factory.get_performance_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn("total_beans_created", stats)
        self.assertIn("cache_hits", stats)
        self.assertIn("cache_misses", stats)

    def test_thread_safety(self):
        """测试线程安全"""
        # 注册Bean
        definition = BeanDefinition("testService", TestService, BeanScope.SINGLETON)
        self.registry.register("testService", definition)

        results = []
        errors = []

        def get_bean_worker():
            try:
                bean = self.factory.get_bean("testService")
                results.append(bean)
            except Exception as e:
                errors.append(e)

        # 创建多个线程同时获取Bean
        threads = [threading.Thread(target=get_bean_worker) for _ in range(10)]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 检查结果
        self.assertEqual(len(errors), 0)
        self.assertEqual(len(results), 10)

        # 所有结果应该是同一个实例（单例）
        first_bean = results[0]
        for bean in results:
            self.assertIs(bean, first_bean)


class DefaultBeanFactoryAdvancedTestCase(unittest.TestCase):
    """DefaultBeanFactory高级功能测试"""

    def setUp(self):
        """测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    def tearDown(self):
        """测试后清理"""
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()

    def test_bean_post_processors(self):
        """测试Bean后置处理器"""
        # 创建模拟的后置处理器
        processor = Mock()
        processor.pre_process = Mock(side_effect=lambda bean, _name: bean)
        processor.post_process = Mock(side_effect=lambda bean, _name: bean)

        # 添加后置处理器
        self.factory.add_bean_post_processor(processor)

        # 检查处理器数量
        self.assertEqual(self.factory.get_bean_processor_count(), 1)

        # 注册并获取Bean
        definition = BeanDefinition("testService", TestService)
        self.registry.register("testService", definition)

        _bean = self.factory.get_bean("testService")

        # 验证后置处理器被调用
        processor.pre_process.assert_called_once()
        processor.post_process.assert_called_once()

    def test_parent_bean_factory(self):
        """测试父Bean工厂"""
        # 创建父工厂
        parent_registry = DefaultBeanDefinitionRegistry()
        parent_factory = DefaultBeanFactory(parent_registry)

        # 在父工厂中注册Bean
        parent_def = BeanDefinition("parentBean", TestService)
        parent_registry.register("parentBean", parent_def)

        # 设置父工厂
        self.factory.set_parent_bean_factory(parent_factory)

        # 检查父工厂
        self.assertIs(self.factory.get_parent_bean_factory(), parent_factory)

        # 从子工厂获取父工厂的Bean
        bean = self.factory.get_bean("parentBean")
        self.assertIsInstance(bean, TestService)

    def test_dependency_graph_integration(self):
        """测试依赖图集成"""
        # 注册有依赖关系的Bean
        service_def = BeanDefinition("serviceBean", TestService)
        dependent_def = BeanDefinition("dependentBean", TestDependentService)

        self.registry.register("serviceBean", service_def)
        self.registry.register("dependentBean", dependent_def)

        # 获取依赖图
        dependency_graph = self.factory._dependency_graph
        self.assertIsNotNone(dependency_graph)

    def test_batch_bean_creation(self):
        """测试批量Bean创建"""
        # 注册多个Bean
        beans_to_create = []
        for i in range(5):
            bean_name = f"bean{i}"
            definition = BeanDefinition(bean_name, TestService)
            self.registry.register(bean_name, definition)
            beans_to_create.append(bean_name)

        # 批量创建Bean
        created_beans = self.factory.preinstantiate_singletons(beans_to_create)

        # 验证创建结果
        self.assertEqual(len(created_beans), 5)
        for bean_name in beans_to_create:
            self.assertIn(bean_name, created_beans)
            self.assertIsInstance(created_beans[bean_name], TestService)

    def test_async_context_detection(self):
        """测试异步上下文检测"""
        # 测试异步检测器
        detector = self.factory._async_detector

        # 在同步环境中应该返回False
        self.assertFalse(detector.is_async_context_active())

        # 测试执行模式检测
        mode = detector.detect_execution_mode()
        self.assertEqual(mode, "sync")

    def test_cache_integration(self):
        """测试缓存集成"""
        # 注册单例Bean
        definition = BeanDefinition("cachedBean", TestService, BeanScope.SINGLETON)
        self.registry.register("cachedBean", definition)

        # 第一次获取（缓存未命中）
        bean1 = self.factory.get_bean("cachedBean")

        # 第二次获取（缓存命中）
        bean2 = self.factory.get_bean("cachedBean")

        # 应该是同一个实例
        self.assertIs(bean1, bean2)

        # 检查性能统计
        stats = self.factory.get_performance_stats()
        self.assertGreater(stats["cache_hits"], 0)

    def test_error_handling(self):
        """测试错误处理"""
        # 测试获取不存在的Bean
        with self.assertRaises(KeyError):
            self.factory.get_bean("nonExistentBean")

    def test_bean_lifecycle_callbacks(self):
        """测试Bean生命周期回调"""
        # 创建带生命周期方法的Bean类
        class LifecycleBean:
            def __init__(self):
                self.initialized = False
                self.destroyed = False

            def init(self):
                self.initialized = True

            def destroy(self):
                self.destroyed = True

        # 注册Bean
        definition = BeanDefinition("lifecycleBean", LifecycleBean,
                                   init_method_name="init",
                                   destroy_method_name="destroy")
        self.registry.register("lifecycleBean", definition)

        # 获取Bean
        bean = self.factory.get_bean("lifecycleBean")
        self.assertIsInstance(bean, LifecycleBean)

    def test_scope_integration(self):
        """测试作用域集成"""
        # 获取作用域管理器
        scope_manager = self.factory._scope_manager
        self.assertIsNotNone(scope_manager)

        # 测试不同作用域的Bean
        singleton_def = BeanDefinition("singletonBean", TestService, BeanScope.SINGLETON)
        prototype_def = BeanDefinition("prototypeBean", TestService, BeanScope.PROTOTYPE)

        self.registry.register("singletonBean", singleton_def)
        self.registry.register("prototypeBean", prototype_def)

        # 验证作用域行为
        self.assertTrue(self.factory.is_singleton("singletonBean"))
        self.assertTrue(self.factory.is_prototype("prototypeBean"))

    def test_concurrent_bean_creation(self):
        """测试并发Bean创建"""
        # 注册Bean
        definition = BeanDefinition("concurrentBean", TestService, BeanScope.SINGLETON)
        self.registry.register("concurrentBean", definition)

        results = []
        errors = []

        def create_bean_worker():
            try:
                bean = self.factory.get_bean("concurrentBean")
                results.append(id(bean))  # 记录对象ID
            except Exception as e:
                errors.append(e)

        # 创建多个线程并发创建Bean
        threads = [threading.Thread(target=create_bean_worker) for _ in range(20)]

        start_time = time.time()
        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()
        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0)
        self.assertEqual(len(results), 20)

        # 所有Bean应该是同一个实例（相同ID）
        unique_ids = set(results)
        self.assertEqual(len(unique_ids), 1)

        # 性能检查
        self.assertLess(end_time - start_time, 1.0)  # 应该在1秒内完成


if __name__ == '__main__':
    unittest.main()
