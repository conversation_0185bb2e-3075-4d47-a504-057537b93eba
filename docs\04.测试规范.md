# Mini-Boot 测试规范

## 1. 测试概述

Mini-Boot 框架采用多层次的测试策略，确保代码质量和系统稳定性。本文档详细说明了测试目录结构、测试规范和最佳实践。

## ⚠️ 重要规范

**相关文档**：

-   需求文档：#[[file:requirements.md]]
-   设计文档：#[[file:design.md]]
-   微观结构信号模块文档：#[[file:微观结构信号模块文档.md]]

## 📝 测试开发规范

### 强制要求

1. **测试文件位置**：所有测试文件必须放在 `mini-boot/tests/` 目录下
2. **一对一映射**：每个组件对应一个测试文件 `tests/unit/test_{component_name}.py`
3. **测试覆盖率**：单元测试覆盖率必须 > 80%
4. **任务完成清理**：每个任务完成后，删除所有临时文件和额外脚本

### 禁止行为

-   ❌ 在项目根目录创建测试脚本（如 `test_*.py`）
-   ❌ 创建任务完成报告文档（如 `TASK_X_COMPLETION_REPORT.md`）
-   ❌ 创建演示脚本（如 `demo_*.py`）
-   ❌ 在 tests 目录外创建任何测试相关文件

### 测试文件结构

```
mini-boot/tests/
├── unit/                    # 单元测试
│   ├── test_celery_manager.py    # ✅ 已完成
│   └── ...                      # 其他单元测试文件
├── integration/             # 集成测试
├── benchmark/               # 性能基准测试
├── mocks/                   # Mock测试工具
├── testutils/               # 测试工具类
└── async/                   # 异步测试
```

**测试框架限制**：

-   ✅ **只允许使用 unittest**：Python 标准库测试框架
-   ❌ **严禁使用 pytest**：包括 pytest 及其所有插件
-   ✅ **异步测试使用 unittest.IsolatedAsyncioTestCase**
-   ✅ **Mock 使用 unittest.mock**

**测试类命名规范**：

-   所有测试类必须以 `TestCase` 结尾
-   格式：`{模块名}{功能名}TestCase`
-   异步测试类：`Async{模块名}TestCase`

**运行规范**：

-   统一使用'uv run python'来运行测试案例

## 2. 测试目录结构

### 2.1 整体架构

```
tests/
├── benchmark/                        # 性能基准测试
├── integration/                      # 集成测试
├── mocks/                           # Mock测试工具
├── testutils/                       # 测试工具类
├── unit/                            # 单元测试
└── async/                           # 异步测试
```

### 2.2 详细目录说明

#### **benchmark/ - 性能基准测试**

```
benchmark/
├── startup_benchmark.py             # 启动性能测试
├── memory_benchmark.py              # 内存使用测试
└── throughput_benchmark.py          # 吞吐量测试
```

**功能职责**：

-   测试框架启动时间和资源消耗
-   监控内存使用情况和垃圾回收
-   评估并发处理能力和吞吐量
-   生成性能基准报告

#### **integration/ - 集成测试**

```
integration/
├── full_app_test.py                 # 完整应用集成测试
├── web_integration_test.py          # Web集成测试
└── database_integration_test.py     # 数据库集成测试
```

**功能职责**：

-   端到端的完整应用测试
-   Web 层与业务层的集成验证
-   数据库连接和事务处理测试
-   外部服务集成测试

#### **mocks/ - Mock 测试工具**

```
mocks/
├── mock_beans.py                    # Mock Bean定义
├── mock_services.py                 # Mock服务
└── mock_data.py                     # Mock数据
```

**功能职责**：

-   提供测试用的 Mock Bean 定义
-   模拟外部服务依赖
-   生成和管理测试数据
-   隔离外部依赖

#### **testutils/ - 测试工具类**

```
testutils/
├── test_context.py                  # 测试上下文工具
├── assertions.py                    # 自定义断言
└── fixtures.py                      # 测试夹具
```

**功能职责**：

-   提供测试专用的 ApplicationContext
-   实现框架特定的断言方法
-   管理测试夹具和数据准备
-   简化测试代码编写

#### **unit/ - 单元测试**

```
unit/
├── annotations_test.py              # 注解系统测试
├── bean_test.py                     # Bean管理测试
├── processor_test.py                # 处理器测试
├── errors_test.py                   # 错误处理测试
├── async_test.py                    # 异步功能测试
├── actuator_test.py                 # 监控功能测试
├── env_test.py                      # 环境配置测试
├── web_test.py                      # Web功能测试
├── events_test.py                   # 事件系统测试
├── schedule_test.py                 # 调度功能测试
└── utils_test.py                    # 工具类测试
```

**功能职责**：

-   测试各个模块的核心功能
-   验证 API 的正确性
-   确保边界条件处理
-   提供快速反馈

#### **async/ - 异步测试**

```
async/
├── async_bean_test.py               # 异步Bean测试
├── async_web_test.py                # 异步Web测试
├── async_events_test.py             # 异步事件测试
├── async_schedule_test.py           # 异步调度测试
└── async_actuator_test.py           # 异步监控测试
```

**功能职责**：

-   测试异步功能的正确性
-   验证异步上下文管理
-   测试并发安全性
-   确保异步性能

## 3. 测试策略

### 3.1 测试金字塔

```
        /\
       /  \
      /E2E \     <- integration/ (少量，高价值)
     /______\
    /        \
   / Service  \   <- integration/ (中量，集成验证)
  /____________\
 /              \
/     Unit       \ <- unit/ (大量，快速反馈)
/__________________\
```

### 3.2 测试覆盖率要求

| 测试类型 | 覆盖率要求 | 执行频率 |
| -------- | ---------- | -------- |
| 单元测试 | > 90%      | 每次提交 |
| 集成测试 | > 80%      | 每日构建 |
| 性能测试 | 关键路径   | 每周执行 |

### 3.3 测试分层策略

#### **第一层：单元测试 (unit/)**

-   **目标**: 快速反馈，高覆盖率
-   **范围**: 单个类或方法
-   **特点**: 快速执行，隔离依赖
-   **工具**: unittest

#### **第二层：集成测试 (integration/)**

-   **目标**: 验证模块间协作
-   **范围**: 多个组件的交互
-   **特点**: 真实环境，完整流程
-   **工具**: unittest 集成测试框架

#### **第三层：端到端测试 (e2e/)**

-   **目标**: 用户场景验证
-   **范围**: 完整应用流程
-   **特点**: 真实数据，外部依赖
-   **工具**: 端到端测试框架

## 4. 测试命名规范

### 4.1 文件命名

-   **单元测试**: `{模块名}_test.py`
-   **集成测试**: `{功能名}_integration_test.py`

-   **性能测试**: `{功能名}_benchmark.py`

### 4.2 测试方法命名

```python
def test_{功能描述}_{预期结果}():
    """测试方法的命名模式"""
    pass

# 示例
def test_bean_creation_success():
    """测试Bean创建成功"""
    pass

def test_circular_dependency_raises_exception():
    """测试循环依赖抛出异常"""
    pass
```

### 4.3 测试类命名

```python
class {模块名}{功能名}TestCase(unittest.TestCase):
    """测试类的命名模式"""
    pass

# 示例
class BeanFactoryTestCase(unittest.TestCase):
    """Bean工厂测试类"""
    pass

class WebControllerTestCase(unittest.TestCase):
    """Web控制器测试类"""
    pass

# 异步测试类
class AsyncBeanTestCase(unittest.IsolatedAsyncioTestCase):
    """异步Bean测试类"""
    pass
```

## 5. 测试最佳实践

### 5.1 测试原则

-   **FIRST 原则**: Fast, Independent, Repeatable, Self-Validating, Timely
-   **AAA 模式**: Arrange, Act, Assert
-   **单一职责**: 每个测试只验证一个功能点
-   **可读性**: 测试代码要清晰易懂

### 5.2 Mock 使用规范

```python
# 正确的Mock使用
@patch('miniboot.external_service.ExternalAPI')
def test_service_with_external_dependency(mock_api):
    # Arrange
    mock_api.get_data.return_value = {"status": "success"}
    service = MyService(mock_api)

    # Act
    result = service.process_data()

    # Assert
    assert result["status"] == "processed"
    mock_api.get_data.assert_called_once()
```

### 5.3 测试数据管理

```python
# 使用fixtures管理测试数据
@pytest.fixture
def sample_bean_definition():
    return BeanDefinition(
        name="testBean",
        bean_class=TestService,
        scope="singleton"
    )

def test_bean_registration(sample_bean_definition):
    registry = BeanRegistry()
    registry.register(sample_bean_definition)
    assert registry.contains("testBean")
```

## 6. 测试执行

### 6.1 本地测试执行

```bash
# 执行所有测试
python -m unittest discover tests

# 执行单元测试
python -m unittest discover tests/unit

# 执行集成测试
python -m unittest discover tests/integration

# 执行异步测试
python -m unittest discover tests/async

# 执行性能测试
python -m unittest discover tests/benchmark

# 使用自定义测试运行器
python test_runner.py

# 生成覆盖率报告
coverage run -m unittest discover tests
coverage html
```

### 6.2 CI/CD 集成

```yaml
# GitHub Actions示例
name: Tests
on: [push, pull_request]
jobs:
    test:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v2
            - name: Set up Python
              uses: actions/setup-python@v2
              with:
                  python-version: 3.9
            - name: Install dependencies
              run: pip install -r requirements-test.txt
            - name: Run tests
              run: |
                  coverage run -m unittest discover tests
                  coverage xml
            - name: Upload coverage
              uses: codecov/codecov-action@v1
```

## 7. 具体测试示例

### 7.1 单元测试示例

#### **Bean 管理测试 (bean_test.py)**

```python
import unittest
from unittest.mock import Mock, patch
from miniboot.bean import BeanFactory, BeanDefinition, BeanRegistry
from miniboot.errors import BeanCreationException, CircularDependencyException

class BeanFactoryTestCase(unittest.TestCase):
    def test_create_singleton_bean_success(self):
        """测试单例Bean创建成功"""
        # Arrange
        factory = BeanFactory()
        definition = BeanDefinition("testBean", TestService, "singleton")

        # Act
        bean1 = factory.create_bean(definition)
        bean2 = factory.create_bean(definition)

        # Assert
        self.assertIs(bean1, bean2)  # 单例模式
        self.assertIsInstance(bean1, TestService)

    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        # Arrange
        factory = BeanFactory()
        # 设置循环依赖的Bean定义

        # Act & Assert
        with self.assertRaises(CircularDependencyException) as context:
            factory.resolve_dependencies()

        self.assertIn("Circular dependency detected", str(context.exception))
```

#### **注解处理测试 (annotations_test.py)**

```python
import unittest
from miniboot.annotations import Component, Service, Autowired
from miniboot.annotations.scanner import ComponentScanner

class ComponentScannerTestCase(unittest.TestCase):
    def test_scan_components_in_package(self):
        """测试包扫描功能"""
        # Arrange
        scanner = ComponentScanner()

        # Act
        components = scanner.scan("test.package")

        # Assert
        self.assertGreater(len(components), 0)
        self.assertTrue(any(comp.name == "testService" for comp in components))

    def test_autowired_annotation_processing(self):
        """测试@Autowired注解处理"""
        # Arrange
        @Service
        class TestService:
            @Autowired
            def __init__(self, dependency: TestDependency):
                self.dependency = dependency

        scanner = ComponentScanner()

        # Act
        metadata = scanner.extract_metadata(TestService)

        # Assert
        self.assertTrue(metadata.has_autowired_constructor())
        self.assertIn("dependency", metadata.constructor_params)
```

### 7.2 集成测试示例

#### **Web 集成测试 (web_integration_test.py)**

```python
import unittest
from miniboot import MiniBootApplication
from miniboot.web import RestController, GetMapping
from miniboot.testutils import TestContext

class WebIntegrationTestCase(unittest.TestCase):
    def setUp(self):
        """创建测试应用上下文"""
        self.context = TestContext()
        self.context.scan("test.controllers")
        self.context.start()

    def tearDown(self):
        """清理测试上下文"""
        self.context.stop()

    def test_controller_registration_and_routing(self):
        """测试控制器注册和路由"""
        # Arrange
        client = self.context.test_client()

        # Act
        response = client.get("/api/test")

        # Assert
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["message"], "success")

    def test_dependency_injection_in_controller(self):
        """测试控制器中的依赖注入"""
        # Arrange
        controller = self.context.get_bean("testController")

        # Act & Assert
        self.assertIsNotNone(controller.service)
        self.assertIsInstance(controller.service, TestService)
```

### 7.3 异步测试示例

#### **异步 Bean 测试 (async_bean_test.py)**

```python
import unittest
import asyncio
from miniboot.async import AsyncExecutor
from miniboot.bean import BeanFactory
from miniboot.annotations import Async

class AsyncBeanTestCase(unittest.IsolatedAsyncioTestCase):
    async def test_async_bean_creation(self):
        """测试异步Bean创建"""
        # Arrange
        factory = BeanFactory()

        # Act
        bean = await factory.create_async_bean("asyncService", AsyncService)

        # Assert
        self.assertIsNotNone(bean)
        self.assertIsInstance(bean, AsyncService)

    async def test_async_method_execution(self):
        """测试异步方法执行"""
        # Arrange
        @Async
        async def async_operation():
            await asyncio.sleep(0.1)
            return "completed"

        # Act
        result = await async_operation()

        # Assert
        self.assertEqual(result, "completed")

    async def test_async_context_management(self):
        """测试异步上下文管理"""
        # Arrange
        context = AsyncApplicationContext()

        # Act
        await context.start()
        bean = await context.get_bean_async("asyncService")
        await context.stop()

        # Assert
        self.assertIsNotNone(bean)
```

### 7.4 性能测试示例

#### **启动性能测试 (startup_benchmark.py)**

```python
import time
import psutil
import unittest
from miniboot import MiniBootApplication

class StartupPerformanceTestCase(unittest.TestCase):
    def test_application_startup_time(self):
        """测试应用启动时间"""
        # Arrange
        start_time = time.time()

        # Act
        app = MiniBootApplication()
        app.run()
        startup_time = time.time() - start_time

        # Assert
        self.assertLess(startup_time, 2.0)  # 启动时间应小于2秒
        app.stop()

    def test_memory_usage_during_startup(self):
        """测试启动时内存使用"""
        # Arrange
        process = psutil.Process()
        initial_memory = process.memory_info().rss

        # Act
        app = MiniBootApplication()
        app.run()
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory

        # Assert
        self.assertLess(memory_increase, 50 * 1024 * 1024)  # 内存增长小于50MB
        app.stop()
```

## 8. 测试工具和框架

### 8.1 测试框架选择

-   **unittest**: Python 标准库测试框架（唯一允许的测试框架）
-   **unittest.mock**: Mock 对象创建
-   **coverage.py**: 代码覆盖率统计

**⚠️ 重要说明**：

-   **禁止使用 pytest**：项目统一使用 unittest 框架
-   **禁止使用 pytest 相关插件**：如 pytest-mock、pytest-asyncio 等
-   **所有测试必须继承 unittest.TestCase** 或 **unittest.IsolatedAsyncioTestCase**

### 8.2 测试工具配置

#### **unittest 配置**

```python
# test_runner.py - 统一测试运行器
import unittest
import sys
import os

def run_tests():
    """运行所有测试"""
    # 发现测试
    loader = unittest.TestLoader()
    start_dir = 'tests'
    suite = loader.discover(start_dir, pattern='*_test.py')

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # 返回结果
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
```

#### **coverage 配置 (.coveragerc)**

```ini
[run]
source = miniboot
omit =
    */tests/*
    */venv/*
    */__pycache__/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise AssertionError
    raise NotImplementedError
```

## 9. 持续集成

### 9.1 测试流水线

```yaml
stages:
    - lint
    - unit-test
    - integration-test
    - performance-test
    - coverage-report

unit-test:
    stage: unit-test
    script:
        - python -m unittest discover tests/unit -v
        - coverage run -m unittest discover tests/unit
    coverage: '/TOTAL.*\s+(\d+%)$/'

integration-test:
    stage: integration-test
    script:
        - python -m unittest discover tests/integration -v
    dependencies:
        - unit-test

performance-test:
    stage: performance-test
    script:
        - python -m unittest discover tests/benchmark -v
    only:
        - main
        - develop
```

### 9.2 质量门禁

-   **单元测试覆盖率**: ≥ 90%
-   **集成测试通过率**: 100%
-   **性能测试**: 不超过基准值的 10%
-   **代码质量**: SonarQube 评分 ≥ A

---

_本文档定义了 Mini-Boot 框架的完整测试规范，包括测试策略、最佳实践和具体示例，确保代码质量和系统稳定性。_
