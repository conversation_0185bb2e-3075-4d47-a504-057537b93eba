#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 缓存组件并发测试 - 验证缓存组件的线程安全性

测试各种缓存组件在高并发环境下的线程安全性和数据一致性。
"""

import random
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed

# 缓存组件
from miniboot.bean.proxy_components import AttributeInspectionComponent, MethodCacheComponent
from miniboot.events.components import EventDataComponent


class CacheComponentsConcurrencyTestCase(unittest.TestCase):
    """缓存组件并发测试用例"""

    def test_method_cache_component_concurrency(self):
        """测试方法缓存组件并发访问"""
        cache_component = MethodCacheComponent(cache_size=100)
        cache_component.initialize()

        results = []
        errors = []

        def concurrent_cache_operations(worker_id: int):
            """并发缓存操作函数"""
            try:
                worker_results = []
                for i in range(50):
                    key = f"method_{worker_id}_{i % 10}"  # 重复使用一些key
                    value = f"cached_method_{worker_id}_{i}"

                    # 写入缓存
                    cache_component.put(key, value)

                    # 读取缓存
                    cached_value = cache_component.get(key)
                    worker_results.append((key, value, cached_value))

                    # 随机读取其他key
                    if i > 10:
                        random_key = f"method_{worker_id}_{random.randint(0, i - 1) % 10}"
                        random_cached = cache_component.get(random_key)
                        worker_results.append((random_key, None, random_cached))

                results.extend(worker_results)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(concurrent_cache_operations, i) for i in range(8)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"缓存并发操作出现错误: {errors}")
        self.assertGreater(len(results), 0, "应该有缓存操作结果")

        # 验证缓存统计
        stats = cache_component.get_stats()
        self.assertGreater(stats["hits"], 0, "应该有缓存命中")
        self.assertGreater(stats["misses"], 0, "应该有缓存未命中")
        self.assertGreaterEqual(stats["size"], 0, "缓存大小应该>=0")
        self.assertLessEqual(stats["size"], 100, "缓存大小不应超过限制")

        # 验证数据一致性
        for key, expected_value, cached_value in results:
            if expected_value is not None:  # 写入操作
                # 由于并发写入，最后写入的值应该被缓存
                # 这里主要验证没有数据损坏
                self.assertIsNotNone(cached_value, f"Key {key} 应该有缓存值")

        # 验证性能
        self.assertLess(end_time - start_time, 2.0, "缓存并发操作应该在2秒内完成")

        # 清理
        cache_component.shutdown()

        print(f"✅ 方法缓存并发测试通过 - 耗时: {end_time - start_time:.3f}秒")
        print(f"   缓存统计: {stats}")

    def test_attribute_inspection_component_concurrency(self):
        """测试属性检查组件并发访问"""
        attr_component = AttributeInspectionComponent()
        attr_component.initialize()

        # 创建测试对象
        class TestObject:
            def __init__(self):
                self.value = "test_value"
                self.counter = 0

            def test_method(self):
                return "test_result"

        test_objects = [TestObject() for _ in range(10)]

        results = []
        errors = []

        def concurrent_attribute_operations(worker_id: int):
            """并发属性操作函数"""
            try:
                worker_results = []
                for i in range(30):
                    obj = test_objects[i % len(test_objects)]

                    # 检查属性存在
                    has_value = attr_component.has_attribute(obj, "value")
                    has_method = attr_component.has_attribute(obj, "test_method")
                    has_nonexistent = attr_component.has_attribute(obj, "nonexistent")

                    # 检查可调用性
                    is_value_callable = attr_component.is_callable(obj, "value")
                    is_method_callable = attr_component.is_callable(obj, "test_method")

                    # 获取属性值
                    value = attr_component.get_attribute(obj, "value")
                    method = attr_component.get_attribute(obj, "test_method")

                    worker_results.append(
                        {
                            "worker_id": worker_id,
                            "iteration": i,
                            "has_value": has_value,
                            "has_method": has_method,
                            "has_nonexistent": has_nonexistent,
                            "is_value_callable": is_value_callable,
                            "is_method_callable": is_method_callable,
                            "value": value,
                            "method_type": type(method).__name__,
                        }
                    )

                results.extend(worker_results)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=6) as executor:
            futures = [executor.submit(concurrent_attribute_operations, i) for i in range(6)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"属性检查并发操作出现错误: {errors}")
        self.assertEqual(len(results), 180, "应该有180个属性检查结果")

        # 验证数据一致性
        for result in results:
            self.assertTrue(result["has_value"], "应该检测到value属性")
            self.assertTrue(result["has_method"], "应该检测到test_method属性")
            self.assertFalse(result["has_nonexistent"], "不应该检测到不存在的属性")
            self.assertFalse(result["is_value_callable"], "value属性不应该可调用")
            self.assertTrue(result["is_method_callable"], "test_method应该可调用")
            self.assertEqual(result["value"], "test_value", "value属性值应该正确")
            self.assertEqual(result["method_type"], "method", "method类型应该正确")

        # 验证性能
        self.assertLess(end_time - start_time, 1.5, "属性检查并发操作应该在1.5秒内完成")

        # 清理
        attr_component.shutdown()

        print(f"✅ 属性检查并发测试通过 - 耗时: {end_time - start_time:.3f}秒")

    def test_event_data_component_concurrency(self):
        """测试事件数据组件并发访问"""
        data_component = EventDataComponent()
        data_component.initialize()

        results = []
        errors = []

        def concurrent_event_data_operations(worker_id: int):
            """并发事件数据操作函数"""
            try:
                worker_results = []
                for i in range(40):
                    event_id = f"event_{worker_id}_{i}"
                    event_data = {"worker_id": worker_id, "iteration": i, "timestamp": time.time(), "data": f"test_data_{worker_id}_{i}"}

                    # 存储事件数据
                    data_component.store_event_data(event_id, event_data)

                    # 检查数据存在
                    has_data = data_component.has_event_data(event_id)

                    # 获取事件数据
                    retrieved_data = data_component.get_event_data(event_id)

                    # 验证数据一致性
                    worker_results.append(
                        {
                            "event_id": event_id,
                            "stored_data": event_data,
                            "has_data": has_data,
                            "retrieved_data": retrieved_data,
                            "data_match": event_data == retrieved_data,
                        }
                    )

                    # 随机访问其他事件数据
                    if i > 5:
                        random_event_id = f"event_{worker_id}_{random.randint(0, i - 1)}"
                        random_data = data_component.get_event_data(random_event_id)
                        worker_results.append(
                            {
                                "event_id": random_event_id,
                                "stored_data": None,
                                "has_data": data_component.has_event_data(random_event_id),
                                "retrieved_data": random_data,
                                "data_match": None,
                            }
                        )

                results.extend(worker_results)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(concurrent_event_data_operations, i) for i in range(5)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"事件数据并发操作出现错误: {errors}")
        self.assertGreater(len(results), 0, "应该有事件数据操作结果")

        # 验证数据一致性
        store_operations = [r for r in results if r["stored_data"] is not None]
        for result in store_operations:
            self.assertTrue(result["has_data"], f"事件 {result['event_id']} 应该存在数据")
            self.assertTrue(result["data_match"], f"事件 {result['event_id']} 数据应该匹配")

        # 验证性能
        self.assertLess(end_time - start_time, 2.0, "事件数据并发操作应该在2秒内完成")

        # 清理
        data_component.shutdown()

        print(f"✅ 事件数据并发测试通过 - 耗时: {end_time - start_time:.3f}秒")

    def test_mixed_cache_operations_stress_test(self):
        """混合缓存操作压力测试"""
        # 创建多个缓存组件
        method_cache = MethodCacheComponent(cache_size=50)
        attr_cache = AttributeInspectionComponent()
        event_cache = EventDataComponent()

        # 初始化组件
        method_cache.initialize()
        attr_cache.initialize()
        event_cache.initialize()

        class StressTestObject:
            def __init__(self, obj_id: int):
                self.obj_id = obj_id
                self.value = f"value_{obj_id}"

            def get_value(self):
                return self.value

        test_objects = [StressTestObject(i) for i in range(20)]

        results = []
        errors = []

        def stress_test_worker(worker_id: int):
            """压力测试工作函数"""
            try:
                for i in range(100):
                    obj = test_objects[i % len(test_objects)]

                    # 方法缓存操作
                    cache_key = f"stress_{worker_id}_{i % 20}"
                    method_cache.put(cache_key, f"cached_value_{worker_id}_{i}")
                    cached_value = method_cache.get(cache_key)

                    # 属性检查操作
                    has_value = attr_cache.has_attribute(obj, "value")
                    is_callable = attr_cache.is_callable(obj, "get_value")

                    # 事件数据操作
                    event_id = f"stress_event_{worker_id}_{i}"
                    event_data = {"worker": worker_id, "iteration": i}
                    event_cache.store_event_data(event_id, event_data)
                    retrieved_event = event_cache.get_event_data(event_id)

                    results.append(
                        {
                            "worker_id": worker_id,
                            "iteration": i,
                            "cache_success": cached_value is not None,
                            "attr_success": has_value and is_callable,
                            "event_success": retrieved_event == event_data,
                        }
                    )

                    # 短暂休眠模拟真实负载
                    if i % 10 == 0:
                        time.sleep(0.001)

            except Exception as e:
                errors.append((worker_id, e))

        # 压力测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(stress_test_worker, i) for i in range(8)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"压力测试出现错误: {errors}")
        self.assertEqual(len(results), 800, "应该有800个压力测试结果")

        # 验证成功率
        cache_success_rate = sum(1 for r in results if r["cache_success"]) / len(results)
        attr_success_rate = sum(1 for r in results if r["attr_success"]) / len(results)
        event_success_rate = sum(1 for r in results if r["event_success"]) / len(results)

        self.assertGreater(cache_success_rate, 0.8, "缓存成功率应该大于80%")
        self.assertGreater(attr_success_rate, 0.95, "属性检查成功率应该大于95%")
        self.assertGreater(event_success_rate, 0.95, "事件数据成功率应该大于95%")

        # 验证性能
        self.assertLess(end_time - start_time, 5.0, "压力测试应该在5秒内完成")

        # 清理
        method_cache.shutdown()
        attr_cache.shutdown()
        event_cache.shutdown()

        print(f"✅ 混合缓存压力测试通过 - 耗时: {end_time - start_time:.3f}秒")
        print(f"   成功率 - 缓存: {cache_success_rate:.2%}, 属性: {attr_success_rate:.2%}, 事件: {event_success_rate:.2%}")


if __name__ == "__main__":
    unittest.main(verbosity=2)
