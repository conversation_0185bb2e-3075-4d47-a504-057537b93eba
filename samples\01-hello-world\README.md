# Mini-Boot Hello World 示例

## 📖 项目简介

这是 Mini-Boot 框架的 Hello World 基础示例，展示如何创建最简单的 Mini-Boot 应用。

## 🎯 学习目标

-   理解 Mini-Boot 的基本概念
-   学会创建最简单的 Mini-Boot 应用
-   掌握基本的应用启动流程

## 🏗️ 技术栈

-   **Mini-Boot**: 0.0.4+
-   **Python**: 3.8+

## 📁 项目结构

```
samples/01-hello-world/
├── README.md                 # 项目文档
├── main.py                  # 应用入口
├── requirements.txt          # Python依赖
└── resources/               # 配置文件目录
    └── application.yml      # 基础配置
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
python main.py
```

### 3. 预期输出

```
Hello World from Mini-Boot!
Mini-Boot application started successfully!
```

## � 代码解释

### 主应用类

```python
from miniboot.annotations import MiniBootApplication
from miniboot import run_application

@MiniBootApplication
class HelloWorldApp:
    """Hello World 应用主类"""

    def run(self):
        """应用启动方法 - 类似 Spring Boot 的 run 方法"""
        print("Hello World from Mini-Boot!")
        print("Mini-Boot application started successfully!")

if __name__ == "__main__":
    # 创建应用实例并启动 - 类似 SpringApplication.run()
    app = HelloWorldApp()
    app.run()
```

### 核心概念

-   **@MiniBootApplication**: Mini-Boot 应用的入口注解，类似于 Spring Boot 的 @SpringBootApplication
-   **应用类**: 使用 @MiniBootApplication 注解标记的应用入口类
-   **自动配置**: 框架会自动配置应用上下文和基础组件

## 🔗 相关资源

-   [下一个示例：依赖注入基础](../02-dependency-injection/)
-   [配置管理示例](../03-configuration/)
-   [事件系统示例](../04-events/)

---

**注意**: 这是最简单的 Mini-Boot 应用示例，更复杂的功能请参考其他示例。
