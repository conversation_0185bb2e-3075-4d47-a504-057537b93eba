#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置属性基类模块
"""

from abc import ABC
from dataclasses import asdict, dataclass
from typing import Any, Optional


@dataclass
class StarterProperties(ABC):
    """Starter配置属性基类

    为所有Starter配置属性提供通用功能.
    """

    enabled: bool = True  # 是否启用Starter

    def is_enabled(self) -> bool:
        """检查Starter是否启用

        Returns:
            bool: 是否启用
        """
        return self.enabled

    def get_prefix(self) -> Optional[str]:
        """获取配置前缀

        Returns:
            Optional[str]: 配置前缀
        """
        # 从@ConfigurationProperties注解中获取prefix
        if hasattr(self.__class__, "_configuration_prefix"):
            return self.__class__._configuration_prefix

        # 如果没有注解,尝试从类名推断
        class_name = self.__class__.__name__
        if class_name.endswith("Properties"):
            # 将驼峰命名转换为小写点分隔
            prefix = class_name[:-10]  # 去掉'Properties'后缀
            return self._camel_to_snake(prefix)

        return None

    def validate(self) -> None:
        """验证配置属性

        子类可以重写此方法来实现自定义验证.

        Raises:
            ValueError: 配置验证失败
        """
        # 基础验证:检查必需字段
        if not hasattr(self, "enabled"):
            raise ValueError("'enabled' property is required")

    def to_dict(self) -> dict[str, Any]:
        """转换为字典

        Returns:
            Dict[str, Any]: 属性字典
        """
        return asdict(self)

    def update_from_dict(self, data: dict[str, Any]) -> None:
        """从字典更新属性

        Args:
            data: 属性数据字典
        """
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def get_property_value(self, property_name: str, default_value: Any = None) -> Any:
        """获取属性值

        Args:
            property_name: 属性名称
            default_value: 默认值

        Returns:
            属性值
        """
        return getattr(self, property_name, default_value)

    def set_property_value(self, property_name: str, value: Any) -> None:
        """设置属性值

        Args:
            property_name: 属性名称
            value: 属性值
        """
        if hasattr(self, property_name):
            setattr(self, property_name, value)
        else:
            raise AttributeError(f"Property '{property_name}' does not exist")

    def get_non_default_properties(self) -> dict[str, Any]:
        """获取非默认值的属性

        Returns:
            非默认值的属性字典
        """
        # 创建默认实例进行比较
        default_instance = self.__class__()
        current_dict = self.to_dict()
        default_dict = default_instance.to_dict()

        non_default = {}
        for key, value in current_dict.items():
            if key not in default_dict or value != default_dict[key]:
                non_default[key] = value

        return non_default

    def merge_properties(self, other_properties: "StarterProperties") -> None:
        """合并其他属性对象的属性

        Args:
            other_properties: 其他属性对象
        """
        if not isinstance(other_properties, self.__class__):
            raise TypeError(f"Cannot merge properties of different types: {type(other_properties)}")

        other_dict = other_properties.to_dict()
        self.update_from_dict(other_dict)

    def _camel_to_snake(self, camel_str: str) -> str:
        """将驼峰命名转换为下划线命名

        Args:
            camel_str: 驼峰命名字符串

        Returns:
            下划线命名字符串
        """
        import re

        # 处理连续大写字母的情况,如HTTPClient -> http_client
        # 先处理连续大写字母
        s1 = re.sub("([A-Z]+)([A-Z][a-z])", r"\1_\2", camel_str)
        # 再处理普通的驼峰命名
        s2 = re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1)
        return s2.lower()

    def __str__(self) -> str:
        """字符串表示"""
        class_name = self.__class__.__name__
        properties = self.to_dict()
        return f"{class_name}({properties})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return self.__str__()
