#!/usr/bin/env python3
"""
pyttsx3中文语音播放器单元测试
测试跨平台文本转语音功能，支持连续播放
"""

import platform
import sys
import time
import unittest

import pyttsx3


class ChineseTTSPlayer:
    """中文TTS播放器"""

    def __init__(self):
        self.enabled = True

    def speak(self, text, async_mode=False):
        """播放中文文本"""
        _ = async_mode  # 标记参数已使用
        if not self.enabled or not text or not text.strip():
            return False

        print(f"🔊 开始播放: {text}")

        try:
            # 每次创建新的引擎实例避免冲突
            engine = pyttsx3.init()

            # 配置引擎
            voices = engine.getProperty("voices")
            for voice in voices:
                if voice.languages:
                    for lang in voice.languages:
                        if "zh" in str(lang).lower():
                            engine.setProperty("voice", voice.id)
                            print(f"使用中文语音: {voice.name}")
                            break

            # 设置语音参数
            engine.setProperty("rate", 150)  # 语速
            engine.setProperty("volume", 1.0)  # 音量：最大值1.0

            # 尝试设置额外的音量增强（如果支持）
            try:
                # 某些SAPI引擎支持额外的音量控制
                current_volume = engine.getProperty("volume")
                print(f"当前音量设置: {current_volume}")

                # 确保音量确实设置为1.0
                if current_volume < 1.0:
                    engine.setProperty("volume", 1.0)
                    print("强制设置音量为最大值")

            except Exception as vol_error:
                print(f"音量设置警告: {vol_error}")

            # 播放
            engine.say(text)
            engine.runAndWait()

            # 立即清理
            engine.stop()
            del engine

            print(f"✅ 播放完成: {text}")
            return True

        except Exception as e:
            print(f"❌ 播放失败: {e}")
            return False

    def set_enabled(self, enabled):
        """启用/禁用语音播放"""
        self.enabled = enabled


class TestChineseTTSPlayer(unittest.TestCase):
    """中文TTS播放器测试"""

    def test_pyttsx3_sequential_play(self):
        """测试pyttsx3连续播放"""
        print("\n" + "=" * 60)
        print("🎵 pyttsx3连续播放测试")
        print("=" * 60)

        player = ChineseTTSPlayer()

        test_texts = ["第一段：pyttsx3测试开始。", "第二段：这是第二段语音。", "第三段：这是第三段语音。", "第四段：pyttsx3测试结束。"]

        for i, text in enumerate(test_texts, 1):
            print(f"\n--- 播放第 {i} 段 ---")
            success = player.speak(text)
            self.assertTrue(success, f"第{i}段播放失败")

            if i < len(test_texts):
                print("等待0.2秒后播放下一段...")
                time.sleep(0.2)

        print("\n🎉 pyttsx3连续播放测试完成！")

    def test_simple_sequential(self):
        """简单连续播放测试 - 重点测试"""
        print("\n" + "=" * 60)
        print("🎵 简单连续播放测试（重点）")
        print("=" * 60)
        print("请仔细听，应该能听到所有4段语音！")

        test_texts = ["第一段语音测试。", "第二段语音测试。", "第三段语音测试。", "第四段语音测试。"]

        for i, text in enumerate(test_texts, 1):
            print(f"\n🔊 [{i}/4] 播放: {text}")

            try:
                # 创建独立的引擎实例
                engine = pyttsx3.init()

                # 配置语音参数
                engine.setProperty("rate", 150)
                engine.setProperty("volume", 1.0)

                # 验证音量设置
                actual_volume = engine.getProperty("volume")
                print(f"实际音量: {actual_volume}")

                # 如果系统不支持1.0，尝试其他方式
                if actual_volume < 1.0:
                    print(f"系统最大音量限制为: {actual_volume}")
                    # 尝试设置为系统支持的最大值
                    engine.setProperty("volume", actual_volume)

                # 播放
                engine.say(text)
                engine.runAndWait()

                # 立即清理
                engine.stop()
                del engine

                print(f"✅ [{i}/4] 播放完成")

                # 播放后短暂等待
                if i < len(test_texts):
                    print("⏳ 等待0.2秒后播放下一段...")
                    time.sleep(0.2)

            except Exception as e:
                print(f"❌ [{i}/4] 播放失败: {e}")
                self.fail(f"第{i}段播放失败: {e}")

        print("\n🎉 简单连续播放测试完成！")
        print("如果您听到了所有4段语音，说明问题已解决！")


if __name__ == "__main__":
    print("pyttsx3连续播放问题修复测试")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print("=" * 60)

    # 运行测试
    unittest.main(verbosity=2)
