#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 核心Bean作用域管理 - 只管理SINGLETON和PROTOTYPE作用域
"""

import contextlib
import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Callable, Optional

from miniboot.utils.singleton import SingletonMeta

from .definition import BeanScope


@dataclass
class BeanScopeContext:
    """Bean作用域上下文

    管理Bean作用域中的Bean实例和销毁回调。
    """

    scope_id: str
    scope_type: BeanScope
    creation_time: float = None

    def __post_init__(self):
        if self.creation_time is None:
            self.creation_time = time.time()

        self._beans: dict[str, Any] = {}
        self._destruction_callbacks: dict[str, Callable[[], None]] = {}
        self._lock = threading.RLock()

    def get_bean(self, bean_name: str) -> Optional[Any]:
        """获取Bean实例"""
        with self._lock:
            return self._beans.get(bean_name)

    def put_bean(self, bean_name: str, bean: Any, destruction_callback: Optional[Callable[[], None]] = None) -> None:
        """存储Bean实例"""
        with self._lock:
            self._beans[bean_name] = bean
            if destruction_callback:
                self._destruction_callbacks[bean_name] = destruction_callback

    def remove_bean(self, bean_name: str) -> Optional[Any]:
        """移除Bean实例"""
        with self._lock:
            bean = self._beans.pop(bean_name, None)
            callback = self._destruction_callbacks.pop(bean_name, None)
            if callback:
                with contextlib.suppress(Exception):
                    callback()
            return bean

    def get_bean_names(self) -> list[str]:
        """获取所有Bean名称"""
        with self._lock:
            return list(self._beans.keys())

    def destroy(self) -> None:
        """销毁作用域上下文"""
        with self._lock:
            # 执行所有销毁回调
            for callback in self._destruction_callbacks.values():
                with contextlib.suppress(Exception):
                    callback()

            self._beans.clear()
            self._destruction_callbacks.clear()


class BeanScopeManager(ABC):
    """Bean作用域管理器抽象基类

    定义Bean作用域管理器的标准接口。
    """

    @abstractmethod
    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[BeanScopeContext]:
        """获取作用域上下文"""
        pass

    @abstractmethod
    def create_scope_context(self, scope_id: str) -> BeanScopeContext:
        """创建作用域上下文"""
        pass

    @abstractmethod
    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁作用域上下文"""
        pass

    @abstractmethod
    def get_scope_type(self) -> BeanScope:
        """获取作用域类型"""
        pass

    @abstractmethod
    def cleanup_expired_contexts(self, _timeout_seconds: float = 3600) -> int:
        """清理过期的作用域上下文"""
        return 0


class SingletonScopeManager(BeanScopeManager):
    """单例作用域管理器

    管理单例作用域的Bean实例。
    整个应用只有一个作用域上下文。
    """

    def __init__(self):
        self._context: Optional[BeanScopeContext] = None
        self._lock = threading.RLock()

    def get_scope_context(self, _scope_id: Optional[str] = None) -> Optional[BeanScopeContext]:
        """获取单例作用域上下文"""
        with self._lock:
            return self._context

    def create_scope_context(self, scope_id: str) -> BeanScopeContext:
        """创建单例作用域上下文"""
        with self._lock:
            if self._context is None:
                self._context = BeanScopeContext(scope_id, BeanScope.SINGLETON)
            return self._context

    def destroy_scope_context(self, _scope_id: str) -> None:
        """销毁单例作用域上下文"""
        with self._lock:
            if self._context:
                self._context.destroy()
                self._context = None

    def get_scope_type(self) -> BeanScope:
        return BeanScope.SINGLETON

    def cleanup_expired_contexts(self, _timeout_seconds: float = 3600) -> int:
        """清理过期的作用域上下文"""
        # 单例作用域不需要清理过期上下文
        return 0


class PrototypeScopeManager(BeanScopeManager):
    """原型作用域管理器

    管理原型作用域的Bean实例。
    每次请求都创建新的实例，不进行缓存。
    """

    def __init__(self):
        self._lock = threading.RLock()

    def get_scope_context(self, _scope_id: Optional[str] = None) -> Optional[BeanScopeContext]:
        """原型作用域不缓存上下文，总是返回None"""
        return None

    def create_scope_context(self, scope_id: str) -> BeanScopeContext:
        """创建临时的原型作用域上下文"""
        return BeanScopeContext(scope_id, BeanScope.PROTOTYPE)

    def destroy_scope_context(self, _scope_id: str) -> None:
        """原型作用域不需要销毁上下文"""
        pass

    def get_scope_type(self) -> BeanScope:
        return BeanScope.PROTOTYPE

    def cleanup_expired_contexts(self, _timeout_seconds: float = 3600) -> int:
        """清理过期的作用域上下文"""
        # 原型作用域不缓存上下文，无需清理
        return 0


class BeanScopeRegistry(metaclass=SingletonMeta):
    """Bean作用域注册表 - 单例模式

    统一管理Bean作用域管理器（SINGLETON和PROTOTYPE）。
    """

    def __init__(self):
        """初始化Bean作用域注册表"""
        # 单例模式：只在首次创建时初始化
        if not hasattr(self, "_initialized"):
            self._managers: dict[BeanScope, BeanScopeManager] = {}
            self._lock = threading.RLock()

            # 注册默认的Bean作用域管理器
            self._register_default_managers()

            # 统计信息
            self._stats = {"total_contexts": 0, "active_contexts": 0, "cleanup_operations": 0}
            self._initialized = True

    def cleanup(self) -> None:
        """清理Bean作用域注册表，供单例重置时调用"""
        if hasattr(self, "_managers"):
            # 清理所有管理器中的过期上下文
            for manager in self._managers.values():
                if hasattr(manager, "cleanup_expired_contexts"):
                    with contextlib.suppress(Exception):
                        manager.cleanup_expired_contexts(0)  # 清理所有上下文

            # 重新初始化
            self._managers.clear()
            self._register_default_managers()

            # 重置统计信息
            self._stats = {"total_contexts": 0, "active_contexts": 0, "cleanup_operations": 0}

    def _register_default_managers(self) -> None:
        """注册默认的Bean作用域管理器"""
        self.register_scope_manager(BeanScope.SINGLETON, SingletonScopeManager())
        self.register_scope_manager(BeanScope.PROTOTYPE, PrototypeScopeManager())

    def register_scope_manager(self, scope_type: BeanScope, manager: BeanScopeManager) -> None:
        """注册Bean作用域管理器

        Args:
            scope_type: Bean作用域类型
            manager: Bean作用域管理器
        """
        with self._lock:
            self._managers[scope_type] = manager

    def get_scope_manager(self, scope_type: BeanScope) -> Optional[BeanScopeManager]:
        """获取Bean作用域管理器

        Args:
            scope_type: Bean作用域类型

        Returns:
            Optional[BeanScopeManager]: Bean作用域管理器，如果不存在则返回None
        """
        with self._lock:
            return self._managers.get(scope_type)

    def get_bean(self, scope_type: BeanScope, bean_name: str, scope_id: Optional[str] = None) -> Optional[Any]:
        """从指定Bean作用域获取Bean

        Args:
            scope_type: Bean作用域类型
            bean_name: Bean名称
            scope_id: 作用域标识符

        Returns:
            Optional[Any]: Bean实例，如果不存在则返回None
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return None

        context = manager.get_scope_context(scope_id)
        if not context:
            return None

        return context.get_bean(bean_name)

    def put_bean(
        self,
        scope_type: BeanScope,
        bean_name: str,
        bean: Any,
        scope_id: Optional[str] = None,
        destruction_callback: Optional[Callable[[], None]] = None,
    ) -> bool:
        """将Bean存储到指定Bean作用域

        Args:
            scope_type: Bean作用域类型
            bean_name: Bean名称
            bean: Bean实例
            scope_id: 作用域标识符
            destruction_callback: 销毁回调函数

        Returns:
            bool: 如果成功存储返回True，否则返回False
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return False

        context = manager.get_scope_context(scope_id)
        if not context and scope_id:
            context = manager.create_scope_context(scope_id)
            self._stats["total_contexts"] += 1
            self._stats["active_contexts"] += 1

        if context:
            context.put_bean(bean_name, bean, destruction_callback)
            return True

        return False

    def get_registry_stats(self) -> dict[str, Any]:
        """获取Bean作用域注册表统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return {
                "registered_scopes": [scope.value for scope in self._managers],
                "manager_count": len(self._managers),
                "stats": self._stats.copy(),
            }


class ScopeManager:
    """作用域管理器

    负责管理Bean工厂中的各种作用域，提供作用域注册和获取功能。
    从factory.py迁移而来，保持作用域管理的独立性。
    """

    def __init__(self):
        """初始化作用域管理器"""
        self._scopes: dict[str, Any] = {}

    def register_scope(self, scope_name: str, scope: Any) -> None:
        """注册作用域

        Args:
            scope_name: 作用域名称
            scope: 作用域实现
        """
        self._scopes[scope_name] = scope

    def get_scope(self, scope_name: str) -> Any:
        """获取作用域

        Args:
            scope_name: 作用域名称

        Returns:
            作用域实现，如果不存在则返回None
        """
        return self._scopes.get(scope_name)

    def has_scope(self, scope_name: str) -> bool:
        """检查是否存在指定作用域

        Args:
            scope_name: 作用域名称

        Returns:
            如果存在返回True，否则返回False
        """
        return scope_name in self._scopes

    def get_registered_scopes(self) -> list[str]:
        """获取所有已注册的作用域名称

        Returns:
            作用域名称列表
        """
        return list(self._scopes.keys())

    def clear_scopes(self) -> None:
        """清除所有注册的作用域"""
        self._scopes.clear()
        self._scopes.clear()
