#!/usr/bin/env python
"""
* @author: cz
* @description: Events 模块配置属性

提供 Events 模块的配置属性类,包括发布器配置、监听器配置、错误处理配置等.
"""

from dataclasses import dataclass, field
from typing import Any, List, Optional


@dataclass
class PublisherConfig:
    """事件发布器配置"""

    # 线程池配置
    thread_pool_size: int = 10
    thread_name_prefix: str = "event-publisher"

    # 队列配置
    queue_size: int = 1000
    queue_timeout: float = 30.0

    # 性能配置
    batch_size: int = 100
    batch_timeout: float = 1.0

    # 统计配置
    enable_stats: bool = True
    stats_interval: int = 60


@dataclass
class ListenerConfig:
    """事件监听器配置"""

    # 执行配置
    default_timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0

    # 异步配置
    async_enabled: bool = True
    async_timeout: float = 60.0

    # 条件配置
    enable_conditions: bool = True
    condition_timeout: float = 5.0


@dataclass
class ErrorHandlingConfig:
    """错误处理配置"""

    # 基础配置
    enabled: bool = True
    log_errors: bool = True

    # 重试配置
    enable_retry: bool = True
    max_retry_attempts: int = 3
    retry_delay: float = 1.0
    retry_backoff_factor: float = 2.0

    # 错误收集配置
    collect_errors: bool = True
    max_error_records: int = 1000
    error_retention_hours: int = 24

    # 恢复策略
    recovery_strategy: str = "continue"  # continue, stop, retry

    # 通知配置
    notify_on_error: bool = False
    notification_threshold: int = 10


@dataclass
class PerformanceConfig:
    """性能配置"""

    # 并发配置
    max_concurrent_events: int = 100
    max_concurrent_handlers: int = 50

    # 缓存配置
    enable_handler_cache: bool = True
    cache_size: int = 500
    cache_ttl: int = 300

    # 监控配置
    enable_metrics: bool = True
    metrics_interval: int = 30

    # 优化配置
    enable_fast_path: bool = True
    enable_batch_processing: bool = True


@dataclass
class SecurityConfig:
    """安全配置"""

    # 访问控制
    enable_access_control: bool = False
    allowed_event_types: List[str] = field(default_factory=list)
    blocked_event_types: List[str] = field(default_factory=list)

    # 执行安全
    enable_sandbox: bool = False
    sandbox_timeout: float = 10.0

    # 审计配置
    enable_audit: bool = False
    audit_sensitive_events: bool = True


@dataclass
class EventsProperties:
    """Events 框架配置属性类"""

    # 基础配置
    enabled: bool = True
    async_enabled: bool = True

    # 子配置对象
    publisher: PublisherConfig = field(default_factory=PublisherConfig)
    listener: ListenerConfig = field(default_factory=ListenerConfig)
    error_handling: ErrorHandlingConfig = field(default_factory=ErrorHandlingConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)

    @classmethod
    def from_environment(cls, environment) -> "EventsProperties":
        """从环境配置创建Events配置

        使用统一的框架配置加载方式,简洁高效
        """
        return cls(
            enabled=environment.get_property("miniboot.events.enabled", True),
            async_enabled=environment.get_property("miniboot.events.async-enabled", True),
            publisher=PublisherConfig(
                thread_pool_size=environment.get_property("miniboot.events.publisher.thread-pool-size", 10),
                thread_name_prefix=environment.get_property("miniboot.events.publisher.thread-name-prefix", "event-publisher"),
                queue_size=environment.get_property("miniboot.events.publisher.queue-size", 1000),
                queue_timeout=environment.get_property("miniboot.events.publisher.queue-timeout", 30.0),
                batch_size=environment.get_property("miniboot.events.publisher.batch-size", 100),
                batch_timeout=environment.get_property("miniboot.events.publisher.batch-timeout", 1.0),
                enable_stats=environment.get_property("miniboot.events.publisher.enable-stats", True),
                stats_interval=environment.get_property("miniboot.events.publisher.stats-interval", 60),
            ),
            listener=ListenerConfig(
                default_timeout=environment.get_property("miniboot.events.listener.default-timeout", 30.0),
                max_retries=environment.get_property("miniboot.events.listener.max-retries", 3),
                retry_delay=environment.get_property("miniboot.events.listener.retry-delay", 1.0),
                async_enabled=environment.get_property("miniboot.events.listener.async-enabled", True),
                async_timeout=environment.get_property("miniboot.events.listener.async-timeout", 60.0),
                enable_conditions=environment.get_property("miniboot.events.listener.enable-conditions", True),
                condition_timeout=environment.get_property("miniboot.events.listener.condition-timeout", 5.0),
            ),
            error_handling=ErrorHandlingConfig(
                enabled=environment.get_property("miniboot.events.error-handling.enabled", True),
                log_errors=environment.get_property("miniboot.events.error-handling.log-errors", True),
                enable_retry=environment.get_property("miniboot.events.error-handling.enable-retry", True),
                max_retry_attempts=environment.get_property("miniboot.events.error-handling.max-retry-attempts", 3),
                retry_delay=environment.get_property("miniboot.events.error-handling.retry-delay", 1.0),
                retry_backoff_factor=environment.get_property("miniboot.events.error-handling.retry-backoff-factor", 2.0),
                collect_errors=environment.get_property("miniboot.events.error-handling.collect-errors", True),
                max_error_records=environment.get_property("miniboot.events.error-handling.max-error-records", 1000),
                error_retention_hours=environment.get_property("miniboot.events.error-handling.error-retention-hours", 24),
                recovery_strategy=environment.get_property("miniboot.events.error-handling.recovery-strategy", "continue"),
                notify_on_error=environment.get_property("miniboot.events.error-handling.notify-on-error", False),
                notification_threshold=environment.get_property("miniboot.events.error-handling.notification-threshold", 10),
            ),
            performance=PerformanceConfig(
                max_concurrent_events=environment.get_property("miniboot.events.performance.max-concurrent-events", 100),
                max_concurrent_handlers=environment.get_property("miniboot.events.performance.max-concurrent-handlers", 50),
                enable_handler_cache=environment.get_property("miniboot.events.performance.enable-handler-cache", True),
                cache_size=environment.get_property("miniboot.events.performance.cache-size", 500),
                cache_ttl=environment.get_property("miniboot.events.performance.cache-ttl", 300),
                enable_metrics=environment.get_property("miniboot.events.performance.enable-metrics", True),
                metrics_interval=environment.get_property("miniboot.events.performance.metrics-interval", 30),
                enable_fast_path=environment.get_property("miniboot.events.performance.enable-fast-path", True),
                enable_batch_processing=environment.get_property("miniboot.events.performance.enable-batch-processing", True),
            ),
            security=SecurityConfig(
                enable_access_control=environment.get_property("miniboot.events.security.enable-access-control", False),
                allowed_event_types=cls._parse_list_property(environment.get_property("miniboot.events.security.allowed-event-types", "")),
                blocked_event_types=cls._parse_list_property(environment.get_property("miniboot.events.security.blocked-event-types", "")),
                enable_sandbox=environment.get_property("miniboot.events.security.enable-sandbox", False),
                sandbox_timeout=environment.get_property("miniboot.events.security.sandbox-timeout", 10.0),
                enable_audit=environment.get_property("miniboot.events.security.enable-audit", False),
                audit_sensitive_events=environment.get_property("miniboot.events.security.audit-sensitive-events", True),
            ),
        )

    @staticmethod
    def _parse_list_property(value: str) -> List[str]:
        """解析逗号分隔的列表属性"""
        if not value or value.strip() == "":
            return []
        return [item.strip() for item in value.split(",") if item.strip()]

    def is_enabled(self) -> bool:
        """检查Events模块是否启用"""
        return self.enabled

    def validate(self) -> None:
        """验证配置参数"""
        if self.publisher.thread_pool_size <= 0:
            raise ValueError("Publisher thread pool size must be positive")

        if self.publisher.queue_size <= 0:
            raise ValueError("Publisher queue size must be positive")

        if self.listener.default_timeout <= 0:
            raise ValueError("Listener default timeout must be positive")

        if self.listener.max_retries < 0:
            raise ValueError("Listener max retries cannot be negative")

        if self.error_handling.max_retry_attempts < 0:
            raise ValueError("Error handling max retry attempts cannot be negative")

        if self.performance.max_concurrent_events <= 0:
            raise ValueError("Max concurrent events must be positive")

        if self.performance.cache_size <= 0:
            raise ValueError("Performance cache size must be positive")

    def to_dict(self) -> dict[str, Any]:
        """将配置转换为字典格式"""
        return {
            "enabled": self.enabled,
            "async_enabled": self.async_enabled,
            "publisher": {
                "thread_pool_size": self.publisher.thread_pool_size,
                "thread_name_prefix": self.publisher.thread_name_prefix,
                "queue_size": self.publisher.queue_size,
                "queue_timeout": self.publisher.queue_timeout,
                "batch_size": self.publisher.batch_size,
                "batch_timeout": self.publisher.batch_timeout,
                "enable_stats": self.publisher.enable_stats,
                "stats_interval": self.publisher.stats_interval,
            },
            "listener": {
                "default_timeout": self.listener.default_timeout,
                "max_retries": self.listener.max_retries,
                "retry_delay": self.listener.retry_delay,
                "async_enabled": self.listener.async_enabled,
                "async_timeout": self.listener.async_timeout,
                "enable_conditions": self.listener.enable_conditions,
                "condition_timeout": self.listener.condition_timeout,
            },
            "error_handling": {
                "enabled": self.error_handling.enabled,
                "log_errors": self.error_handling.log_errors,
                "enable_retry": self.error_handling.enable_retry,
                "max_retry_attempts": self.error_handling.max_retry_attempts,
                "retry_delay": self.error_handling.retry_delay,
                "retry_backoff_factor": self.error_handling.retry_backoff_factor,
                "collect_errors": self.error_handling.collect_errors,
                "max_error_records": self.error_handling.max_error_records,
                "error_retention_hours": self.error_handling.error_retention_hours,
                "recovery_strategy": self.error_handling.recovery_strategy,
                "notify_on_error": self.error_handling.notify_on_error,
                "notification_threshold": self.error_handling.notification_threshold,
            },
            "performance": {
                "max_concurrent_events": self.performance.max_concurrent_events,
                "max_concurrent_handlers": self.performance.max_concurrent_handlers,
                "enable_handler_cache": self.performance.enable_handler_cache,
                "cache_size": self.performance.cache_size,
                "cache_ttl": self.performance.cache_ttl,
                "enable_metrics": self.performance.enable_metrics,
                "metrics_interval": self.performance.metrics_interval,
                "enable_fast_path": self.performance.enable_fast_path,
                "enable_batch_processing": self.performance.enable_batch_processing,
            },
            "security": {
                "enable_access_control": self.security.enable_access_control,
                "allowed_event_types": self.security.allowed_event_types,
                "blocked_event_types": self.security.blocked_event_types,
                "enable_sandbox": self.security.enable_sandbox,
                "sandbox_timeout": self.security.sandbox_timeout,
                "enable_audit": self.security.enable_audit,
                "audit_sensitive_events": self.security.audit_sensitive_events,
            },
        }
