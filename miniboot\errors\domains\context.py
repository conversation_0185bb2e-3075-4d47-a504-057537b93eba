#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Context相关异常类

应用上下文生命周期管理相关的异常。
"""

from ..base import (ApplicationError, BusinessError, SystemError,
                    ValidationError)


# 上下文启动相关异常 (ApplicationError)
class ContextStartupError(ApplicationError):
    """上下文启动错误 - 应用上下文启动过程中的错误"""
    max_attempts = 3
    base_delay = 2.0


class ContextInitializationError(ApplicationError):
    """上下文初始化错误 - 应用上下文初始化失败"""
    max_attempts = 2
    base_delay = 2.0


class ContextBootstrapError(ApplicationError):
    """上下文引导错误 - 应用上下文引导过程失败"""
    max_attempts = 2
    base_delay = 3.0


# 上下文关闭相关异常 (ApplicationError)
class ContextShutdownError(ApplicationError):
    """上下文关闭错误 - 应用上下文关闭过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


class ContextDestructionError(ApplicationError):
    """上下文销毁错误 - 应用上下文销毁过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


class ContextCleanupError(ApplicationError):
    """上下文清理错误 - 应用上下文清理过程中的错误"""
    max_attempts = 1
    base_delay = 0.5


# 上下文配置相关异常 (ApplicationError)
class ContextConfigurationError(ApplicationError):
    """上下文配置错误 - 应用上下文配置相关的错误"""
    retryable = False  # 配置错误重试无意义


class ContextConfigurationValidationError(ApplicationError):
    """上下文配置验证错误 - 应用上下文配置验证失败"""
    retryable = False


class ContextPropertiesError(ApplicationError):
    """上下文属性错误 - 应用上下文属性相关的错误"""
    retryable = False


# 上下文状态相关异常 (BusinessError)
class ContextStateError(BusinessError):
    """上下文状态错误 - 应用上下文状态相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ContextNotStartedError(BusinessError):
    """上下文未启动错误 - 应用上下文尚未启动"""
    retryable = False


class ContextAlreadyStartedError(BusinessError):
    """上下文已启动错误 - 应用上下文已经启动"""
    retryable = False


class ContextNotActiveError(BusinessError):
    """上下文未激活错误 - 应用上下文未激活"""
    retryable = False


class ContextClosedError(BusinessError):
    """上下文已关闭错误 - 应用上下文已经关闭"""
    retryable = False


# 上下文刷新相关异常 (BusinessError)
class ContextRefreshError(BusinessError):
    """上下文刷新错误 - 应用上下文刷新过程中的错误"""
    max_attempts = 3
    base_delay = 2.0


class ContextReloadError(BusinessError):
    """上下文重载错误 - 应用上下文重载过程中的错误"""
    max_attempts = 2
    base_delay = 3.0


# 上下文层次结构相关异常 (ValidationError)
class ContextHierarchyError(ValidationError):
    """上下文层次结构错误 - 应用上下文层次结构相关的错误"""
    # 继承ValidationError的属性：retryable = False


class ParentContextError(ValidationError):
    """父上下文错误 - 父上下文相关的错误"""
    # 继承ValidationError的属性：retryable = False


class ChildContextError(ValidationError):
    """子上下文错误 - 子上下文相关的错误"""
    # 继承ValidationError的属性：retryable = False


# 上下文事件相关异常 (BusinessError)
class ContextEventError(BusinessError):
    """上下文事件错误 - 应用上下文事件相关的错误"""
    max_attempts = 2
    base_delay = 0.5


class ContextEventPublishError(BusinessError):
    """上下文事件发布错误 - 应用上下文事件发布失败"""
    max_attempts = 3
    base_delay = 0.5
    strategy = "linear"


class ContextEventHandlerError(BusinessError):
    """上下文事件处理器错误 - 应用上下文事件处理器执行失败"""
    max_attempts = 2
    base_delay = 0.5


# 上下文资源相关异常 (ApplicationError)
class ContextResourceError(ApplicationError):
    """上下文资源错误 - 应用上下文资源相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ContextResourceLoadError(ApplicationError):
    """上下文资源加载错误 - 应用上下文资源加载失败"""
    max_attempts = 3
    base_delay = 1.0


class ContextResourceNotFoundError(ApplicationError):
    """上下文资源未找到错误 - 应用上下文资源未找到"""
    retryable = False


# 上下文环境相关异常 (ApplicationError)
class ContextEnvironmentError(ApplicationError):
    """上下文环境错误 - 应用上下文环境相关的错误"""
    retryable = False


class ContextProfileError(ApplicationError):
    """上下文配置文件错误 - 应用上下文配置文件相关的错误"""
    retryable = False


class ContextPropertySourceError(ApplicationError):
    """上下文属性源错误 - 应用上下文属性源相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class PropertyNotFoundError(ValidationError):
    """属性未找到错误 - 找不到指定的配置属性"""
    retryable = False


# 上下文处理器相关异常 (BusinessError)
class ContextProcessorError(BusinessError):
    """上下文处理器错误 - 应用上下文处理器相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ContextPostProcessorError(BusinessError):
    """上下文后处理器错误 - 应用上下文后处理器执行失败"""
    max_attempts = 2
    base_delay = 0.5


# 上下文安全相关异常 (SystemError)
class ContextSecurityError(SystemError):
    """上下文安全错误 - 应用上下文安全相关的错误"""
    # 继承SystemError的属性：retryable = False


class ContextPermissionError(SystemError):
    """上下文权限错误 - 应用上下文权限相关的错误"""
    # 继承SystemError的属性：retryable = False


# 上下文监控相关异常 (BusinessError)
class ContextMonitoringError(BusinessError):
    """上下文监控错误 - 应用上下文监控相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class ContextHealthCheckError(BusinessError):
    """上下文健康检查错误 - 应用上下文健康检查失败"""
    max_attempts = 3
    base_delay = 1.0
    strategy = "linear"
