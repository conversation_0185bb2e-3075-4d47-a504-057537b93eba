#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块边界情况和错误处理测试
"""

import contextlib
import threading
import time
import unittest

from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.errors import BeanCircularDependencyError


# ==================== 测试用Bean类 ====================

class ProblematicService:
    """有问题的服务类 - 用于测试错误情况"""
    def __init__(self, should_fail: bool = False):
        if should_fail:
            raise RuntimeError("Service initialization failed")
        self.initialized = False

    def init(self):
        """可能失败的初始化方法"""
        if hasattr(self, '_fail_init') and self._fail_init:
            raise RuntimeError("Initialization method failed")
        self.initialized = True

    def destroy(self):
        """可能失败的销毁方法"""
        if hasattr(self, '_fail_destroy') and self._fail_destroy:
            raise RuntimeError("Destroy method failed")


class SlowService:
    """慢速服务 - 用于测试超时情况"""
    def __init__(self, delay: float = 0.1):
        time.sleep(delay)
        self.created_at = time.time()


class MemoryIntensiveService:
    """内存密集型服务 - 用于测试资源限制"""
    def __init__(self, size: int = 1000):
        self.data = list(range(size))


# ==================== 边界情况和错误处理测试 ====================

class BeanFactoryEdgeCasesTestCase(unittest.TestCase):
    """Bean工厂边界情况测试"""

    def setUp(self):
        """测试前准备"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.factory = DefaultBeanFactory(self.registry)

    def tearDown(self):
        """测试后清理"""
        with contextlib.suppress(Exception):
            self.factory.destroy_singletons()

    def test_bean_creation_failure_handling(self):
        """测试Bean创建失败处理"""
        # 创建一个会在构造函数中失败的类
        class FailingService:
            def __init__(self):
                raise RuntimeError("Service initialization failed")

        # 注册会失败的Bean
        definition = BeanDefinition("problematicBean", FailingService)
        self.registry.register("problematicBean", definition)

        # 尝试创建应该失败
        with self.assertRaises(RuntimeError) as context:
            self.factory.get_bean("problematicBean")
        self.assertIn("Service initialization failed", str(context.exception))

    def test_bean_initialization_method_failure(self):
        """测试Bean初始化方法失败"""
        # 注册Bean
        definition = BeanDefinition("problematicBean", ProblematicService)
        definition.init_method_name = "init"
        self.registry.register("problematicBean", definition)

        # 创建Bean
        bean = self.factory.get_bean("problematicBean")

        # 模拟初始化失败
        bean._fail_init = True

        # 重新创建应该处理初始化失败
        self.registry.remove("problematicBean")
        definition2 = BeanDefinition("problematicBean2", ProblematicService)
        definition2.init_method_name = "init"
        self.registry.register("problematicBean2", definition2)

        bean2 = self.factory.get_bean("problematicBean2")
        bean2._fail_init = True

        # 验证Bean仍然被创建（初始化失败不影响Bean创建）
        self.assertIsInstance(bean2, ProblematicService)

    def test_bean_destroy_method_failure(self):
        """测试Bean销毁方法失败"""
        # 注册Bean
        definition = BeanDefinition("problematicBean", ProblematicService, BeanScope.SINGLETON)
        definition.destroy_method_name = "destroy"
        self.registry.register("problematicBean", definition)

        # 创建Bean
        bean = self.factory.get_bean("problematicBean")
        bean._fail_destroy = True

        # 销毁Bean应该处理失败但不抛出异常
        try:
            self.factory.destroy_singletons()
        except Exception as e:
            self.fail(f"destroy_singletons should handle destroy method failures: {e}")

    def test_concurrent_bean_creation_race_condition(self):
        """测试并发Bean创建竞态条件"""
        # 注册慢速Bean
        definition = BeanDefinition("slowBean", SlowService, BeanScope.SINGLETON)
        definition.add_arg(index=0, value=0.05)  # 50ms延迟
        self.registry.register("slowBean", definition)

        results = []
        errors = []

        def create_bean():
            try:
                bean = self.factory.get_bean("slowBean")
                results.append(bean)
            except Exception as e:
                errors.append(e)

        # 并发创建Bean
        threads = [threading.Thread(target=create_bean) for _ in range(5)]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 验证结果
        self.assertEqual(len(errors), 0)  # 不应该有错误
        self.assertEqual(len(results), 5)  # 应该有5个结果

        # 验证单例行为 - 所有Bean应该是同一个实例
        first_bean = results[0]
        for bean in results[1:]:
            self.assertIs(bean, first_bean)

    def test_memory_intensive_bean_creation(self):
        """测试内存密集型Bean创建"""
        # 注册内存密集型Bean（使用默认参数）
        definition = BeanDefinition("memoryBean", MemoryIntensiveService, BeanScope.PROTOTYPE)
        self.registry.register("memoryBean", definition)

        # 创建多个实例
        beans = []
        for _ in range(3):
            bean = self.factory.get_bean("memoryBean")
            beans.append(bean)
            self.assertIsInstance(bean, MemoryIntensiveService)
            self.assertEqual(len(bean.data), 1000)  # 默认大小

        # 验证原型行为 - 每个Bean都是不同实例
        for i in range(len(beans)):
            for j in range(i + 1, len(beans)):
                self.assertIsNot(beans[i], beans[j])

    def test_invalid_bean_definition_handling(self):
        """测试无效Bean定义处理"""
        # 测试空Bean名称
        with self.assertRaises((TypeError, ValueError)):
            BeanDefinition("", ProblematicService)

        # 测试None Bean名称
        with self.assertRaises((TypeError, ValueError)):
            BeanDefinition(None, ProblematicService)

        # 测试正常Bean定义但使用无效参数
        definition = BeanDefinition("testBean", ProblematicService)
        self.registry.register("testBean", definition)

        # 创建Bean应该成功
        bean = self.factory.get_bean("testBean")
        self.assertIsInstance(bean, ProblematicService)

    def test_circular_dependency_in_batch_creation(self):
        """测试批量创建中的循环依赖"""
        # 创建循环依赖的Bean定义
        class ServiceA:
            def __init__(self):
                pass

        class ServiceB:
            def __init__(self):
                pass

        # 注册Bean
        def_a = BeanDefinition("serviceA", ServiceA)
        def_a.add_dependency("serviceB")

        def_b = BeanDefinition("serviceB", ServiceB)
        def_b.add_dependency("serviceA")

        self.registry.register("serviceA", def_a)
        self.registry.register("serviceB", def_b)

        # 批量创建应该检测到循环依赖
        with self.assertRaises(BeanCircularDependencyError):
            self.factory.create_beans_in_dependency_order(["serviceA", "serviceB"])

    def test_empty_bean_list_batch_creation(self):
        """测试空Bean列表的批量创建"""
        # 空列表应该返回空字典
        result = self.factory.create_beans_in_dependency_order([])
        self.assertEqual(result, {})

        # None应该创建所有Bean
        result = self.factory.create_beans_in_dependency_order(None)
        self.assertIsInstance(result, dict)

    def test_nonexistent_bean_in_batch_creation(self):
        """测试批量创建中包含不存在的Bean"""
        # 注册一个存在的Bean
        definition = BeanDefinition("existingBean", ProblematicService)
        self.registry.register("existingBean", definition)

        # 批量创建包含不存在的Bean
        result = self.factory.create_beans_in_dependency_order(["existingBean", "nonExistentBean"])

        # 验证结果是字典类型
        self.assertIsInstance(result, dict)

        # 如果方法过滤掉不存在的Bean，结果可能为空或包含存在的Bean
        # 这取决于具体实现


if __name__ == '__main__':
    unittest.main()
