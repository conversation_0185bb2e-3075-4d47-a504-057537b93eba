#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Web作用域管理 - 提供Web相关的Bean作用域支持
"""

import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from ..utils.singleton import SingletonMeta


class WebBeanScope(Enum):
    """Web相关Bean作用域枚举

    定义Web应用中特有的Bean生命周期和实例化策略。
    """

    REQUEST = "request"  # 请求作用域：每个HTTP请求一个实例
    SESSION = "session"  # 会话作用域：每个HTTP会话一个实例
    APPLICATION = "application"  # 应用作用域：整个Web应用一个实例
    WEBSOCKET = "websocket"  # WebSocket作用域：每个WebSocket会话一个实例

    def __str__(self) -> str:
        return self.value

    def __repr__(self) -> str:
        return f"WebBeanScope.{self.name}"

    @classmethod
    def from_string(cls, value: str) -> "WebBeanScope":
        """从字符串创建WebBeanScope

        Args:
            value: 作用域字符串值

        Returns:
            WebBeanScope: 对应的Web作用域枚举

        Raises:
            ValueError: 如果值无效
        """
        value = value.lower().strip()
        for scope in cls:
            if scope.value == value:
                return scope
        raise ValueError(f"Invalid web scope value: {value}")


@dataclass
class WebScopeContext:
    """Web作用域上下文

    管理特定Web作用域中的Bean实例和销毁回调。
    """

    scope_id: str
    scope_type: WebBeanScope
    creation_time: float = None

    def __post_init__(self):
        if self.creation_time is None:
            self.creation_time = time.time()

        self._beans: Dict[str, Any] = {}
        self._destruction_callbacks: Dict[str, Callable[[], None]] = {}
        self._lock = threading.RLock()

    def get_bean(self, bean_name: str) -> Optional[Any]:
        """获取Bean实例"""
        with self._lock:
            return self._beans.get(bean_name)

    def put_bean(self, bean_name: str, bean: Any, destruction_callback: Optional[Callable[[], None]] = None) -> None:
        """存储Bean实例"""
        with self._lock:
            self._beans[bean_name] = bean
            if destruction_callback:
                self._destruction_callbacks[bean_name] = destruction_callback

    def remove_bean(self, bean_name: str) -> Optional[Any]:
        """移除Bean实例"""
        with self._lock:
            bean = self._beans.pop(bean_name, None)
            callback = self._destruction_callbacks.pop(bean_name, None)
            if callback:
                try:
                    callback()
                except Exception:
                    # 忽略销毁回调中的异常
                    pass
            return bean

    def get_bean_names(self) -> List[str]:
        """获取所有Bean名称"""
        with self._lock:
            return list(self._beans.keys())

    def destroy(self) -> None:
        """销毁作用域上下文"""
        with self._lock:
            # 执行所有销毁回调
            for callback in self._destruction_callbacks.values():
                try:
                    callback()
                except Exception:
                    # 忽略销毁回调中的异常
                    pass

            self._beans.clear()
            self._destruction_callbacks.clear()


class WebScopeManager(ABC):
    """Web作用域管理器抽象基类

    定义Web作用域管理器的标准接口。
    """

    @abstractmethod
    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[WebScopeContext]:
        """获取作用域上下文"""
        pass

    @abstractmethod
    def create_scope_context(self, scope_id: str) -> WebScopeContext:
        """创建作用域上下文"""
        pass

    @abstractmethod
    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁作用域上下文"""
        pass

    @abstractmethod
    def get_scope_type(self) -> WebBeanScope:
        """获取作用域类型"""
        pass

    def cleanup_expired_contexts(self, timeout_seconds: float = 3600) -> int:
        """清理过期的作用域上下文"""
        return 0


class RequestScopeManager(WebScopeManager):
    """请求作用域管理器

    管理HTTP请求作用域的Bean实例。
    """

    def __init__(self):
        self._contexts: Dict[str, WebScopeContext] = {}
        self._thread_local = threading.local()
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[WebScopeContext]:
        """获取请求作用域上下文"""
        if scope_id:
            with self._lock:
                return self._contexts.get(scope_id)
        else:
            # 使用线程本地存储
            return getattr(self._thread_local, "context", None)

    def create_scope_context(self, scope_id: str) -> WebScopeContext:
        """创建请求作用域上下文"""
        context = WebScopeContext(scope_id, WebBeanScope.REQUEST)

        with self._lock:
            self._contexts[scope_id] = context

        # 设置到线程本地存储
        self._thread_local.context = context

        return context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁请求作用域上下文"""
        with self._lock:
            context = self._contexts.pop(scope_id, None)
            if context:
                context.destroy()

        # 清理线程本地存储
        if hasattr(self._thread_local, "context"):
            if self._thread_local.context and self._thread_local.context.scope_id == scope_id:
                delattr(self._thread_local, "context")

    def get_scope_type(self) -> WebBeanScope:
        return WebBeanScope.REQUEST

    def cleanup_expired_contexts(self, timeout_seconds: float = 3600) -> int:
        """清理过期的请求上下文"""
        current_time = time.time()
        expired_contexts = []

        with self._lock:
            for scope_id, context in self._contexts.items():
                if current_time - context.creation_time > timeout_seconds:
                    expired_contexts.append(scope_id)

        for scope_id in expired_contexts:
            self.destroy_scope_context(scope_id)

        return len(expired_contexts)


class SessionScopeManager(WebScopeManager):
    """会话作用域管理器

    管理HTTP会话作用域的Bean实例。
    """

    def __init__(self):
        self._contexts: Dict[str, WebScopeContext] = {}
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[WebScopeContext]:
        """获取会话作用域上下文"""
        if not scope_id:
            return None

        with self._lock:
            return self._contexts.get(scope_id)

    def create_scope_context(self, scope_id: str) -> WebScopeContext:
        """创建会话作用域上下文"""
        context = WebScopeContext(scope_id, WebBeanScope.SESSION)

        with self._lock:
            self._contexts[scope_id] = context

        return context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁会话作用域上下文"""
        with self._lock:
            context = self._contexts.pop(scope_id, None)
            if context:
                context.destroy()

    def get_scope_type(self) -> WebBeanScope:
        return WebBeanScope.SESSION

    def cleanup_expired_contexts(self, timeout_seconds: float = 7200) -> int:
        """清理过期的会话上下文（默认2小时）"""
        current_time = time.time()
        expired_contexts = []

        with self._lock:
            for scope_id, context in self._contexts.items():
                if current_time - context.creation_time > timeout_seconds:
                    expired_contexts.append(scope_id)

        for scope_id in expired_contexts:
            self.destroy_scope_context(scope_id)

        return len(expired_contexts)


class ApplicationScopeManager(WebScopeManager):
    """应用作用域管理器

    管理应用级别作用域的Bean实例。
    """

    def __init__(self):
        self._context: Optional[WebScopeContext] = None
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[WebScopeContext]:
        """获取应用作用域上下文"""
        with self._lock:
            return self._context

    def create_scope_context(self, scope_id: str) -> WebScopeContext:
        """创建应用作用域上下文"""
        with self._lock:
            if self._context is None:
                self._context = WebScopeContext(scope_id, WebBeanScope.APPLICATION)
            return self._context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁应用作用域上下文"""
        with self._lock:
            if self._context:
                self._context.destroy()
                self._context = None

    def get_scope_type(self) -> WebBeanScope:
        return WebBeanScope.APPLICATION


class WebSocketScopeManager(WebScopeManager):
    """WebSocket作用域管理器

    管理WebSocket会话作用域的Bean实例。
    """

    def __init__(self):
        self._contexts: Dict[str, WebScopeContext] = {}
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[WebScopeContext]:
        """获取WebSocket作用域上下文"""
        if not scope_id:
            return None

        with self._lock:
            return self._contexts.get(scope_id)

    def create_scope_context(self, scope_id: str) -> WebScopeContext:
        """创建WebSocket作用域上下文"""
        context = WebScopeContext(scope_id, WebBeanScope.WEBSOCKET)

        with self._lock:
            self._contexts[scope_id] = context

        return context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁WebSocket作用域上下文"""
        with self._lock:
            context = self._contexts.pop(scope_id, None)
            if context:
                context.destroy()

    def get_scope_type(self) -> WebBeanScope:
        return WebBeanScope.WEBSOCKET

    def cleanup_expired_contexts(self, timeout_seconds: float = 1800) -> int:
        """清理过期的WebSocket上下文（默认30分钟）"""
        current_time = time.time()
        expired_contexts = []

        with self._lock:
            for scope_id, context in self._contexts.items():
                if current_time - context.creation_time > timeout_seconds:
                    expired_contexts.append(scope_id)

        for scope_id in expired_contexts:
            self.destroy_scope_context(scope_id)

        return len(expired_contexts)


class WebScopeRegistry(metaclass=SingletonMeta):
    """Web作用域注册表 - 单例模式

    统一管理所有Web相关的作用域管理器。
    提供Web作用域管理器的注册、获取和Bean作用域操作的统一接口。
    """

    def __init__(self):
        """初始化Web作用域注册表"""
        # 单例模式：只在首次创建时初始化
        if not hasattr(self, "_initialized"):
            self._managers: Dict[WebBeanScope, WebScopeManager] = {}
            self._lock = threading.RLock()

            # 注册默认的Web作用域管理器
            self._register_default_managers()

            # 统计信息
            self._stats = {"total_contexts": 0, "active_contexts": 0, "cleanup_operations": 0}
            self._initialized = True

    def cleanup(self) -> None:
        """清理Web作用域注册表，供单例重置时调用"""
        if hasattr(self, "_managers"):
            # 清理所有管理器中的过期上下文
            for manager in self._managers.values():
                if hasattr(manager, "cleanup_expired_contexts"):
                    try:
                        manager.cleanup_expired_contexts(0)  # 清理所有上下文
                    except Exception:
                        # 忽略清理过程中的异常
                        pass

            # 重新初始化
            self._managers.clear()
            self._register_default_managers()

            # 重置统计信息
            self._stats = {"total_contexts": 0, "active_contexts": 0, "cleanup_operations": 0}

    def _register_default_managers(self) -> None:
        """注册默认的Web作用域管理器"""
        self.register_scope_manager(WebBeanScope.REQUEST, RequestScopeManager())
        self.register_scope_manager(WebBeanScope.SESSION, SessionScopeManager())
        self.register_scope_manager(WebBeanScope.APPLICATION, ApplicationScopeManager())
        self.register_scope_manager(WebBeanScope.WEBSOCKET, WebSocketScopeManager())

    def register_scope_manager(self, scope_type: WebBeanScope, manager: WebScopeManager) -> None:
        """注册Web作用域管理器

        Args:
            scope_type: Web作用域类型
            manager: Web作用域管理器
        """
        with self._lock:
            self._managers[scope_type] = manager

    def get_scope_manager(self, scope_type: WebBeanScope) -> Optional[WebScopeManager]:
        """获取Web作用域管理器

        Args:
            scope_type: Web作用域类型

        Returns:
            Optional[WebScopeManager]: Web作用域管理器，如果不存在则返回None
        """
        with self._lock:
            return self._managers.get(scope_type)

    def get_bean(self, scope_type: WebBeanScope, bean_name: str, scope_id: Optional[str] = None) -> Optional[Any]:
        """从指定Web作用域获取Bean

        Args:
            scope_type: Web作用域类型
            bean_name: Bean名称
            scope_id: 作用域标识符

        Returns:
            Optional[Any]: Bean实例，如果不存在则返回None
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return None

        context = manager.get_scope_context(scope_id)
        if not context:
            return None

        return context.get_bean(bean_name)

    def put_bean(
        self,
        scope_type: WebBeanScope,
        bean_name: str,
        bean: Any,
        scope_id: Optional[str] = None,
        destruction_callback: Optional[Callable[[], None]] = None,
    ) -> bool:
        """将Bean存储到指定Web作用域

        Args:
            scope_type: Web作用域类型
            bean_name: Bean名称
            bean: Bean实例
            scope_id: 作用域标识符
            destruction_callback: 销毁回调函数

        Returns:
            bool: 如果成功存储返回True，否则返回False
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return False

        context = manager.get_scope_context(scope_id)
        if not context and scope_id:
            context = manager.create_scope_context(scope_id)
            self._stats["total_contexts"] += 1
            self._stats["active_contexts"] += 1

        if context:
            context.put_bean(bean_name, bean, destruction_callback)
            return True

        return False

    def remove_bean(self, scope_type: WebBeanScope, bean_name: str, scope_id: Optional[str] = None) -> Optional[Any]:
        """从指定Web作用域移除Bean

        Args:
            scope_type: Web作用域类型
            bean_name: Bean名称
            scope_id: 作用域标识符

        Returns:
            Optional[Any]: 被移除的Bean实例
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return None

        context = manager.get_scope_context(scope_id)
        if not context:
            return None

        return context.remove_bean(bean_name)

    def destroy_scope(self, scope_type: WebBeanScope, scope_id: str) -> None:
        """销毁指定Web作用域

        Args:
            scope_type: Web作用域类型
            scope_id: 作用域标识符
        """
        manager = self.get_scope_manager(scope_type)
        if manager:
            manager.destroy_scope_context(scope_id)
            self._stats["active_contexts"] -= 1

    def cleanup_expired_scopes(self, timeout_seconds: float = 3600) -> int:
        """清理所有过期的Web作用域

        Args:
            timeout_seconds: 超时时间（秒），默认1小时

        Returns:
            int: 清理的作用域数量
        """
        total_cleaned = 0

        with self._lock:
            for manager in self._managers.values():
                cleaned = manager.cleanup_expired_contexts(timeout_seconds)
                total_cleaned += cleaned
                self._stats["active_contexts"] -= cleaned

        self._stats["cleanup_operations"] += 1
        return total_cleaned

    def get_registry_stats(self) -> dict[str, Any]:
        """获取Web作用域注册表统计信息

        Returns:
            dict[str, Any]: 统计信息
        """
        with self._lock:
            manager_stats = {}
            for scope_type, manager in self._managers.items():
                if hasattr(manager, "get_stats"):
                    manager_stats[scope_type.value] = manager.get_stats()

            return {
                "registered_scopes": [scope.value for scope in self._managers.keys()],
                "manager_count": len(self._managers),
                "stats": self._stats.copy(),
                "manager_stats": manager_stats,
            }
