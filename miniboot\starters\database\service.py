#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Database服务实现
"""

import sqlite3
from typing import Any, Optional
from loguru import logger

from .properties import DatabaseProperties


class DatabaseService:
    """Database 演示实现"""

    def __init__(self, properties: DatabaseProperties):
        """初始化 Database 服务

        Args:
            properties: Database配置属性
        """
        self.properties = properties
        self._connection = None
        self._connected = False

    def is_enabled(self) -> bool:
        """检查服务是否启用

        Returns:
            是否启用
        """
        return self.properties.enabled

    def connect(self) -> None:
        """连接数据库"""
        if not self.is_enabled() or self._connected:
            return

        try:
            if self.properties.driver == "sqlite":
                # 从URL中提取数据库文件路径
                db_path = self.properties.url.replace("sqlite:///", "")
                self._connection = sqlite3.connect(db_path, check_same_thread=False)
                self._connection.row_factory = sqlite3.Row  # 支持字典式访问
                self._connected = True
                logger.info(f"Connected to SQLite database: {db_path}")
            else:
                logger.warning(f"Unsupported database driver: {self.properties.driver}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise

    def disconnect(self) -> None:
        """断开数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None
            self._connected = False
            logger.info("Database connection closed")

    def is_connected(self) -> bool:
        """检查是否已连接

        Returns:
            是否已连接
        """
        return self._connected and self._connection is not None

    def execute_query(self, sql: str, params: Optional[tuple] = None) -> list[dict[str, Any]]:
        """执行查询SQL

        Args:
            sql: SQL查询语句
            params: 查询参数

        Returns:
            查询结果列表
        """
        if not self.is_connected():
            self.connect()

        try:
            cursor = self._connection.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            # 将结果转换为字典列表
            columns = [description[0] for description in cursor.description] if cursor.description else []
            results = []
            for row in cursor.fetchall():
                if isinstance(row, sqlite3.Row):
                    results.append(dict(row))
                else:
                    results.append(dict(zip(columns, row)))

            logger.debug(f"Query executed: {sql}, Results: {len(results)} rows")
            return results

        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            raise

    def execute_update(self, sql: str, params: Optional[tuple] = None) -> int:
        """执行更新SQL

        Args:
            sql: SQL更新语句
            params: 更新参数

        Returns:
            影响的行数
        """
        if not self.is_connected():
            self.connect()

        try:
            cursor = self._connection.cursor()
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)

            self._connection.commit()
            affected_rows = cursor.rowcount

            logger.debug(f"Update executed: {sql}, Affected rows: {affected_rows}")
            return affected_rows

        except Exception as e:
            logger.error(f"Update execution failed: {e}")
            self._connection.rollback()
            raise

    def create_table(self, table_name: str, columns: dict[str, str]) -> None:
        """创建表

        Args:
            table_name: 表名
            columns: 列定义字典 {列名: 类型}
        """
        if not columns:
            raise ValueError("Columns cannot be empty")

        column_defs = []
        for col_name, col_type in columns.items():
            column_defs.append(f"{col_name} {col_type}")

        sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(column_defs)})"
        self.execute_update(sql)
        logger.info(f"Table created: {table_name}")

    def get_database_info(self) -> dict:
        """获取数据库信息

        Returns:
            数据库信息字典
        """
        if not self.is_enabled():
            return {}

        return {
            "service": "DatabaseService",
            "enabled": self.properties.enabled,
            "auto_connect": self.properties.auto_connect,
            "url": self.properties.url,
            "driver": self.properties.driver,
            "pool_size": self.properties.pool_size,
            "connected": self._connected,
            "message": "This is database info from DatabaseService",
        }
