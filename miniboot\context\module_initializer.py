#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 重构的模块初始化器

提供重构后的模块初始化器，具有更好的扩展性、可维护性和性能。

核心改进:
- 插件化架构：支持模块插件动态注册
- 异步优化：全面异步化，支持并发初始化
- 条件系统：更强大的条件评估系统
- 生命周期管理：完整的模块生命周期钩子
- 依赖解析：智能依赖图构建和循环检测
- 错误恢复：更好的错误处理和恢复机制
- 性能监控：内置性能指标收集
- 配置热更新：支持运行时配置更新
"""

import asyncio
import time
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Union

from loguru import logger

from ..env import Environment


class ModuleState(Enum):
    """模块状态枚举"""
    NOT_INITIALIZED = "not_initialized"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    FAILED = "failed"
    DISABLED = "disabled"


class ModulePriority(Enum):
    """模块优先级枚举"""
    CRITICAL = 0      # 关键模块（如配置、日志）
    HIGH = 25         # 高优先级（如安全、认证）
    NORMAL = 50       # 普通优先级（如业务模块）
    LOW = 75          # 低优先级（如监控、统计）
    BACKGROUND = 100  # 后台模块（如清理、维护）


@dataclass
class ModuleMetrics:
    """模块指标"""
    initialization_time: float = 0.0
    startup_time: float = 0.0
    total_runtime: float = 0.0
    restart_count: int = 0
    failure_count: int = 0
    last_failure_time: Optional[float] = None
    last_failure_reason: Optional[str] = None


@dataclass
class ModuleInfo:
    """模块信息（重构版）"""
    name: str
    description: str
    version: str = "1.0.0"

    # 配置相关
    config_key: str = ""
    config_schema: Optional[Dict[str, Any]] = None

    # 依赖关系
    dependencies: List[str] = field(default_factory=list)
    optional_dependencies: List[str] = field(default_factory=list)
    conflicts: List[str] = field(default_factory=list)

    # 优先级和标签
    priority: Union[int, ModulePriority] = ModulePriority.NORMAL
    tags: Set[str] = field(default_factory=set)

    # 生命周期钩子
    initializer: Optional[Callable] = None
    starter: Optional[Callable] = None
    stopper: Optional[Callable] = None
    health_checker: Optional[Callable] = None

    # 运行时状态
    state: ModuleState = ModuleState.NOT_INITIALIZED
    enabled: bool = True
    required: bool = False
    error_message: Optional[str] = None

    # 指标和元数据
    metrics: ModuleMetrics = field(default_factory=ModuleMetrics)
    metadata: Dict[str, Any] = field(default_factory=dict)


class ModuleCondition(ABC):
    """模块条件接口（重构版）"""

    @abstractmethod
    async def evaluate(self, environment: Environment, module: ModuleInfo) -> bool:
        """异步评估模块条件

        Args:
            environment: 环境配置
            module: 模块信息

        Returns:
            bool: 是否满足条件
        """
        pass

    @abstractmethod
    def get_condition_name(self) -> str:
        """获取条件名称"""
        pass


class ConfigBasedCondition(ModuleCondition):
    """基于配置的条件（重构版）"""

    async def evaluate(self, environment: Environment, module: ModuleInfo) -> bool:
        """基于配置键评估模块条件"""
        if not module.config_key:
            return True

        try:
            enabled = environment.get_property(module.config_key, module.enabled)
            logger.debug(f"Module {module.name} config condition: {module.config_key}={enabled}")
            return bool(enabled)
        except Exception as e:
            logger.warning(f"Failed to evaluate config condition for module {module.name}: {e}")
            return False

    def get_condition_name(self) -> str:
        return "ConfigBasedCondition"


class DependencyCondition(ModuleCondition):
    """依赖条件（重构版）"""

    def __init__(self, module_registry: Dict[str, ModuleInfo]):
        self.module_registry = module_registry

    async def evaluate(self, environment: Environment, module: ModuleInfo) -> bool:
        """检查依赖模块是否已初始化"""
        for dep_name in module.dependencies:
            dep_module = self.module_registry.get(dep_name)
            if not dep_module:
                logger.warning(f"Module {module.name} depends on non-existent module: {dep_name}")
                return False

            if dep_module.state not in [ModuleState.INITIALIZED, ModuleState.RUNNING]:
                logger.debug(f"Module {module.name} waiting for dependency: {dep_name}")
                return False

        return True

    def get_condition_name(self) -> str:
        return "DependencyCondition"


class ConflictCondition(ModuleCondition):
    """冲突条件"""

    def __init__(self, module_registry: Dict[str, ModuleInfo]):
        self.module_registry = module_registry

    async def evaluate(self, environment: Environment, module: ModuleInfo) -> bool:
        """检查是否存在冲突模块"""
        for conflict_name in module.conflicts:
            conflict_module = self.module_registry.get(conflict_name)
            if conflict_module and conflict_module.state in [ModuleState.INITIALIZED, ModuleState.RUNNING]:
                logger.warning(f"Module {module.name} conflicts with running module: {conflict_name}")
                return False

        return True

    def get_condition_name(self) -> str:
        return "ConflictCondition"


class ModuleLifecycleHook(ABC):
    """模块生命周期钩子接口"""

    @abstractmethod
    async def before_initialization(self, module: ModuleInfo, context: Any) -> None:
        """初始化前钩子"""
        pass

    @abstractmethod
    async def after_initialization(self, module: ModuleInfo, context: Any) -> None:
        """初始化后钩子"""
        pass

    @abstractmethod
    async def before_startup(self, module: ModuleInfo, context: Any) -> None:
        """启动前钩子"""
        pass

    @abstractmethod
    async def after_startup(self, module: ModuleInfo, context: Any) -> None:
        """启动后钩子"""
        pass

    @abstractmethod
    async def before_shutdown(self, module: ModuleInfo, context: Any) -> None:
        """关闭前钩子"""
        pass

    @abstractmethod
    async def after_shutdown(self, module: ModuleInfo, context: Any) -> None:
        """关闭后钩子"""
        pass


class DefaultLifecycleHook(ModuleLifecycleHook):
    """默认生命周期钩子实现"""

    async def before_initialization(self, module: ModuleInfo, context: Any) -> None:
        logger.debug(f"Before initialization: {module.name}")

    async def after_initialization(self, module: ModuleInfo, context: Any) -> None:
        logger.debug(f"After initialization: {module.name}")

    async def before_startup(self, module: ModuleInfo, context: Any) -> None:
        logger.debug(f"Before startup: {module.name}")

    async def after_startup(self, module: ModuleInfo, context: Any) -> None:
        logger.debug(f"After startup: {module.name}")

    async def before_shutdown(self, module: ModuleInfo, context: Any) -> None:
        logger.debug(f"Before shutdown: {module.name}")

    async def after_shutdown(self, module: ModuleInfo, context: Any) -> None:
        logger.debug(f"After shutdown: {module.name}")


class DependencyGraph:
    """依赖图管理器"""

    def __init__(self):
        self.graph: Dict[str, Set[str]] = defaultdict(set)
        self.reverse_graph: Dict[str, Set[str]] = defaultdict(set)

    def add_dependency(self, module: str, dependency: str) -> None:
        """添加依赖关系"""
        self.graph[module].add(dependency)
        self.reverse_graph[dependency].add(module)

    def remove_dependency(self, module: str, dependency: str) -> None:
        """移除依赖关系"""
        self.graph[module].discard(dependency)
        self.reverse_graph[dependency].discard(module)

    def get_dependencies(self, module: str) -> Set[str]:
        """获取模块的直接依赖"""
        return self.graph[module].copy()

    def get_dependents(self, module: str) -> Set[str]:
        """获取依赖此模块的模块"""
        return self.reverse_graph[module].copy()

    def has_circular_dependency(self) -> Optional[List[str]]:
        """检测循环依赖"""
        # 创建图的快照以避免迭代时修改
        graph_snapshot = {node: set(deps) for node, deps in self.graph.items()}

        visited = set()
        rec_stack = set()

        def dfs(node: str, path: List[str]) -> Optional[List[str]]:
            if node in rec_stack:
                # 找到循环，返回循环路径
                cycle_start = path.index(node)
                return path[cycle_start:] + [node]

            if node in visited:
                return None

            visited.add(node)
            rec_stack.add(node)
            path.append(node)

            # 使用快照而不是原始图
            for neighbor in graph_snapshot.get(node, set()):
                cycle = dfs(neighbor, path.copy())
                if cycle:
                    return cycle

            rec_stack.remove(node)
            return None

        # 使用快照的键
        for node in list(graph_snapshot.keys()):
            if node not in visited:
                cycle = dfs(node, [])
                if cycle:
                    return cycle

        return None

    def topological_sort(self) -> List[str]:
        """拓扑排序"""
        # 创建图的快照以避免迭代时修改
        graph_snapshot = {node: set(deps) for node, deps in self.graph.items()}

        in_degree = defaultdict(int)

        # 计算入度
        for node in graph_snapshot:
            for neighbor in graph_snapshot[node]:
                in_degree[neighbor] += 1

        # 获取所有节点
        all_nodes = set(graph_snapshot.keys()) | set().union(*graph_snapshot.values()) if graph_snapshot else set()

        # 初始化队列
        queue = deque([node for node in all_nodes if in_degree[node] == 0])
        result = []

        while queue:
            node = queue.popleft()
            result.append(node)

            # 使用快照
            for neighbor in graph_snapshot.get(node, set()):
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)

        return result


class ModuleInitializer:
    """重构的模块初始化器

    提供更强大、灵活和高性能的模块初始化功能。
    """

    def __init__(self, environment: Environment, context: Any = None):
        """初始化模块初始化器

        Args:
            environment: 环境配置
            context: 应用上下文
        """
        self.environment = environment
        self.context = context

        # 模块注册表
        self.modules: Dict[str, ModuleInfo] = {}
        self.dependency_graph = DependencyGraph()

        # 条件和钩子
        self.conditions: List[ModuleCondition] = []
        self.lifecycle_hooks: List[ModuleLifecycleHook] = []

        # 状态跟踪
        self.initialized_modules: Set[str] = set()
        self.running_modules: Set[str] = set()
        self.failed_modules: Set[str] = set()

        # 性能指标
        self.total_initialization_time = 0.0
        self.initialization_start_time = 0.0

        # 并发控制
        self.max_concurrent_initializations = environment.get_property(
            "miniboot.modules.max_concurrent", 5
        )
        self.initialization_semaphore = asyncio.Semaphore(self.max_concurrent_initializations)

        # 注册默认条件和钩子
        self._register_default_conditions()
        self._register_default_hooks()

        logger.debug("ModuleInitializer created")

    def _register_default_conditions(self) -> None:
        """注册默认条件"""
        self.conditions.extend([
            ConfigBasedCondition(),
            DependencyCondition(self.modules),
            ConflictCondition(self.modules)
        ])

    def _register_default_hooks(self) -> None:
        """注册默认钩子"""
        self.lifecycle_hooks.append(DefaultLifecycleHook())

    def register_module(self, module: ModuleInfo) -> None:
        """注册模块

        Args:
            module: 模块定义
        """
        if module.name in self.modules:
            logger.warning(f"Module {module.name} already registered, overwriting")

        # 标准化优先级
        if isinstance(module.priority, ModulePriority):
            module.priority = module.priority.value

        self.modules[module.name] = module

        # 构建依赖图
        for dep in module.dependencies:
            self.dependency_graph.add_dependency(module.name, dep)

        logger.debug(f"Registered module: {module.name} (priority: {module.priority})")

    def register_module_legacy(
        self,
        name: str,
        description: str,
        config_key: str,
        initializer: Callable,
        dependencies: Optional[List[str]] = None,
        priority: int = 100,
        required: bool = False,
    ) -> None:
        """注册模块（兼容旧接口）

        Args:
            name: 模块名称
            description: 模块描述
            config_key: 配置键
            initializer: 初始化函数
            dependencies: 依赖的模块名称列表
            priority: 初始化优先级
            required: 是否为必需模块
        """
        module = ModuleInfo(
            name=name,
            description=description,
            config_key=config_key,
            dependencies=dependencies or [],
            priority=priority,
            required=required,
            initializer=initializer,
        )
        self.register_module(module)

    def add_condition(self, condition: ModuleCondition) -> None:
        """添加条件"""
        self.conditions.append(condition)
        logger.debug(f"Added condition: {condition.get_condition_name()}")

    def add_lifecycle_hook(self, hook: ModuleLifecycleHook) -> None:
        """添加生命周期钩子"""
        self.lifecycle_hooks.append(hook)
        logger.debug(f"Added lifecycle hook: {hook.__class__.__name__}")

    async def initialize_modules(self) -> Dict[str, Any]:
        """初始化所有模块

        Returns:
            初始化结果摘要
        """
        logger.info("🚀 Starting module initialization...")
        self.initialization_start_time = time.time()

        try:
            # 1. 验证依赖图
            await self._validate_dependency_graph()

            # 2. 计算初始化顺序
            initialization_order = await self._calculate_initialization_order()

            # 3. 记录扫描结果
            await self._log_module_scan_results()

            # 4. 并发初始化模块
            await self._initialize_modules_concurrently(initialization_order)

            # 5. 记录初始化摘要
            result = await self._generate_initialization_summary()

            self.total_initialization_time = time.time() - self.initialization_start_time
            logger.info(f"✅ Module initialization completed in {self.total_initialization_time:.3f}s")

            return result

        except Exception as e:
            logger.error(f"❌ Module initialization failed: {e}")
            raise

    async def _validate_dependency_graph(self) -> None:
        """验证依赖图"""
        logger.debug("Validating dependency graph...")

        # 检查循环依赖
        cycle = self.dependency_graph.has_circular_dependency()
        if cycle:
            cycle_str = " -> ".join(cycle)
            raise RuntimeError(f"Circular dependency detected: {cycle_str}")

        # 检查依赖模块是否存在
        # 创建字典的副本以避免迭代时修改
        modules_copy = dict(self.modules)
        for module_name, module in modules_copy.items():
            for dep in module.dependencies:
                if dep not in self.modules:
                    raise RuntimeError(f"Module {module_name} depends on non-existent module: {dep}")

        logger.debug("Dependency graph validation passed")

    async def _calculate_initialization_order(self) -> List[str]:
        """计算初始化顺序"""
        logger.debug("Calculating initialization order...")

        # 拓扑排序获取基本顺序
        topo_order = self.dependency_graph.topological_sort()

        # 按优先级和拓扑顺序排序
        # 创建模块列表的副本以避免迭代时修改
        module_names = list(self.modules.keys())

        def sort_key(module_name: str) -> tuple:
            module = self.modules[module_name]
            topo_index = topo_order.index(module_name) if module_name in topo_order else len(topo_order)
            # 确保优先级是整数类型
            priority = module.priority.value if isinstance(module.priority, ModulePriority) else module.priority
            return (priority, topo_index)

        sorted_modules = sorted(module_names, key=sort_key)

        logger.debug(f"Initialization order: {sorted_modules}")
        return sorted_modules

    async def _log_module_scan_results(self) -> None:
        """记录模块扫描结果"""
        logger.info("📋 Module Configuration Scan Results:")

        # 创建模块列表的副本以避免迭代时修改
        modules_list = list(self.modules.values())
        for module in sorted(modules_list, key=lambda m: m.priority.value if isinstance(m.priority, ModulePriority) else m.priority):
            # 评估所有条件
            should_initialize = True
            condition_results = []

            for condition in self.conditions:
                try:
                    result = await condition.evaluate(self.environment, module)
                    condition_results.append(f"{condition.get_condition_name()}={result}")
                    if not result:
                        should_initialize = False
                except Exception as e:
                    condition_results.append(f"{condition.get_condition_name()}=ERROR({e})")
                    should_initialize = False

            status_icon = "✅" if should_initialize else "❌"
            conditions_str = ", ".join(condition_results)

            logger.info(f"  {status_icon} {module.name}: {conditions_str}")

            # 显示依赖信息
            if module.dependencies:
                logger.info(f"    Dependencies: {', '.join(module.dependencies)}")
            if module.optional_dependencies:
                logger.info(f"    Optional: {', '.join(module.optional_dependencies)}")

    async def _initialize_modules_concurrently(self, initialization_order: List[str]) -> None:
        """并发初始化模块"""
        logger.info(f"Initializing {len(initialization_order)} modules with max concurrency: {self.max_concurrent_initializations}")

        # 创建初始化任务
        tasks = []
        for module_name in initialization_order:
            task = asyncio.create_task(self._initialize_single_module(module_name))
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                module_name = initialization_order[i]
                logger.error(f"Module {module_name} initialization failed: {result}")
                self.failed_modules.add(module_name)

    async def _initialize_single_module(self, module_name: str) -> None:
        """初始化单个模块"""
        module = self.modules[module_name]

        async with self.initialization_semaphore:
            try:
                # 等待依赖模块完成初始化
                await self._wait_for_dependencies(module)

                # 检查所有条件
                should_initialize = await self._evaluate_all_conditions(module)
                if not should_initialize:
                    module.state = ModuleState.DISABLED
                    logger.info(f"🔒 Module disabled: {module.name}")
                    return

                # 开始初始化
                module.state = ModuleState.INITIALIZING
                start_time = time.time()

                logger.info(f"🔄 Initializing module: {module.name}")

                # 执行生命周期钩子：初始化前
                for hook in self.lifecycle_hooks:
                    await hook.before_initialization(module, self.context)

                # 执行初始化逻辑
                if module.initializer:
                    if asyncio.iscoroutinefunction(module.initializer):
                        await module.initializer()
                    else:
                        module.initializer()

                # 记录初始化时间
                module.metrics.initialization_time = time.time() - start_time

                # 标记为已初始化
                module.state = ModuleState.INITIALIZED
                self.initialized_modules.add(module.name)

                # 执行生命周期钩子：初始化后
                for hook in self.lifecycle_hooks:
                    await hook.after_initialization(module, self.context)

                logger.info(f"✅ Module initialized: {module.name} ({module.metrics.initialization_time:.3f}s)")

            except Exception as e:
                module.state = ModuleState.FAILED
                module.metrics.failure_count += 1
                module.metrics.last_failure_time = time.time()
                module.metrics.last_failure_reason = str(e)

                self.failed_modules.add(module.name)

                # 详细记录错误信息用于调试
                logger.error(f"❌ Module initialization failed: {module.name} - {e}")
                logger.debug(f"Error details for {module.name}: {e}", exc_info=True)

                # 记录模块的配置状态
                config_key = getattr(module, 'config_key', None)
                if config_key:
                    enabled = self.environment.get_property(config_key, True)
                    logger.debug(f"Module {module.name} config status: {config_key}={enabled}")

                # 如果是必需模块，抛出异常
                if module.required:
                    raise RuntimeError(f"Required module {module.name} failed to initialize: {e}") from e
                else:
                    # 非必需模块失败时，记录警告但继续
                    logger.warning(f"⚠️ Optional module {module.name} failed to initialize, continuing without it")

    async def _wait_for_dependencies(self, module: ModuleInfo) -> None:
        """等待依赖模块完成初始化"""
        max_wait_time = 30.0  # 最大等待时间（秒）
        check_interval = 0.1  # 检查间隔（秒）

        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            all_deps_ready = True

            for dep_name in module.dependencies:
                dep_module = self.modules.get(dep_name)
                if not dep_module or dep_module.state not in [ModuleState.INITIALIZED, ModuleState.RUNNING]:
                    all_deps_ready = False
                    break

            if all_deps_ready:
                return

            await asyncio.sleep(check_interval)

        # 超时处理
        unready_deps = []
        for dep_name in module.dependencies:
            dep_module = self.modules.get(dep_name)
            if not dep_module or dep_module.state not in [ModuleState.INITIALIZED, ModuleState.RUNNING]:
                unready_deps.append(dep_name)

        if unready_deps:
            raise RuntimeError(f"Module {module.name} timed out waiting for dependencies: {unready_deps}")

    async def _evaluate_all_conditions(self, module: ModuleInfo) -> bool:
        """评估所有条件"""
        for condition in self.conditions:
            try:
                if not await condition.evaluate(self.environment, module):
                    return False
            except Exception as e:
                logger.warning(f"Condition {condition.get_condition_name()} evaluation failed for module {module.name}: {e}")
                return False

        return True

    async def _generate_initialization_summary(self) -> Dict[str, Any]:
        """生成初始化摘要"""
        total_modules = len(self.modules)
        initialized_count = len(self.initialized_modules)
        failed_count = len(self.failed_modules)
        disabled_count = sum(1 for m in self.modules.values() if m.state == ModuleState.DISABLED)

        # 计算平均初始化时间
        # 创建模块列表的副本以避免迭代时修改
        modules_copy = dict(self.modules)
        total_init_time = sum(m.metrics.initialization_time for m in modules_copy.values()
                             if m.state == ModuleState.INITIALIZED)
        avg_init_time = total_init_time / initialized_count if initialized_count > 0 else 0

        summary = {
            "total_modules": total_modules,
            "initialized_modules": self.initialized_modules.copy(),
            "failed_modules": self.failed_modules.copy(),
            "disabled_modules": {name for name, m in modules_copy.items() if m.state == ModuleState.DISABLED},
            "statistics": {
                "initialized_count": initialized_count,
                "failed_count": failed_count,
                "disabled_count": disabled_count,
                "success_rate": initialized_count / total_modules if total_modules > 0 else 0,
                "total_initialization_time": self.total_initialization_time,
                "average_module_initialization_time": avg_init_time
            },
            "module_metrics": {
                name: {
                    "state": module.state.value,
                    "initialization_time": module.metrics.initialization_time,
                    "failure_count": module.metrics.failure_count,
                    "last_failure_reason": module.metrics.last_failure_reason
                }
                for name, module in modules_copy.items()
            }
        }

        logger.info("📊 Module Initialization Summary:")
        logger.info(f"  Total modules: {total_modules}")
        logger.info(f"  Initialized: {initialized_count}")
        logger.info(f"  Failed: {failed_count}")
        logger.info(f"  Disabled: {disabled_count}")
        logger.info(f"  Success rate: {summary['statistics']['success_rate']:.1%}")
        logger.info(f"  Total time: {self.total_initialization_time:.3f}s")
        logger.info(f"  Average module time: {avg_init_time:.3f}s")

        return summary

    async def start_module(self, module_name: str) -> bool:
        """启动模块

        Args:
            module_name: 模块名称

        Returns:
            bool: 是否启动成功
        """
        module = self.modules.get(module_name)
        if not module:
            logger.error(f"Module not found: {module_name}")
            return False

        if module.state != ModuleState.INITIALIZED:
            logger.error(f"Module {module_name} is not initialized (current state: {module.state.value})")
            return False

        try:
            module.state = ModuleState.STARTING
            start_time = time.time()

            logger.info(f"🚀 Starting module: {module.name}")

            # 执行生命周期钩子：启动前
            for hook in self.lifecycle_hooks:
                await hook.before_startup(module, self.context)

            # 执行启动逻辑
            if module.starter:
                if asyncio.iscoroutinefunction(module.starter):
                    await module.starter()
                else:
                    module.starter()

            # 记录启动时间
            module.metrics.startup_time = time.time() - start_time

            # 标记为运行中
            module.state = ModuleState.RUNNING
            self.running_modules.add(module.name)

            # 执行生命周期钩子：启动后
            for hook in self.lifecycle_hooks:
                await hook.after_startup(module, self.context)

            logger.info(f"✅ Module started: {module.name} ({module.metrics.startup_time:.3f}s)")
            return True

        except Exception as e:
            module.state = ModuleState.FAILED
            module.metrics.failure_count += 1
            module.metrics.last_failure_time = time.time()
            module.metrics.last_failure_reason = str(e)

            logger.error(f"❌ Module startup failed: {module.name} - {e}")
            return False

    async def stop_module(self, module_name: str) -> bool:
        """停止模块

        Args:
            module_name: 模块名称

        Returns:
            bool: 是否停止成功
        """
        module = self.modules.get(module_name)
        if not module:
            logger.error(f"Module not found: {module_name}")
            return False

        if module.state != ModuleState.RUNNING:
            logger.warning(f"Module {module_name} is not running (current state: {module.state.value})")
            return True

        try:
            module.state = ModuleState.STOPPING

            logger.info(f"🛑 Stopping module: {module.name}")

            # 执行生命周期钩子：关闭前
            for hook in self.lifecycle_hooks:
                await hook.before_shutdown(module, self.context)

            # 执行停止逻辑
            if module.stopper:
                if asyncio.iscoroutinefunction(module.stopper):
                    await module.stopper()
                else:
                    module.stopper()

            # 标记为已停止
            module.state = ModuleState.STOPPED
            self.running_modules.discard(module.name)

            # 执行生命周期钩子：关闭后
            for hook in self.lifecycle_hooks:
                await hook.after_shutdown(module, self.context)

            logger.info(f"✅ Module stopped: {module.name}")
            return True

        except Exception as e:
            module.state = ModuleState.FAILED
            module.metrics.failure_count += 1
            module.metrics.last_failure_time = time.time()
            module.metrics.last_failure_reason = str(e)

            logger.error(f"❌ Module shutdown failed: {module.name} - {e}")
            return False

    async def restart_module(self, module_name: str) -> bool:
        """重启模块

        Args:
            module_name: 模块名称

        Returns:
            bool: 是否重启成功
        """
        module = self.modules.get(module_name)
        if not module:
            logger.error(f"Module not found: {module_name}")
            return False

        logger.info(f"🔄 Restarting module: {module.name}")

        # 停止模块
        if module.state == ModuleState.RUNNING:
            if not await self.stop_module(module_name):
                return False

        # 启动模块
        if module.state in [ModuleState.STOPPED, ModuleState.INITIALIZED]:
            success = await self.start_module(module_name)
            if success:
                module.metrics.restart_count += 1
            return success

        return False

    async def check_module_health(self, module_name: str) -> Dict[str, Any]:
        """检查模块健康状态

        Args:
            module_name: 模块名称

        Returns:
            健康状态信息
        """
        module = self.modules.get(module_name)
        if not module:
            return {"status": "not_found", "message": f"Module {module_name} not found"}

        health_info = {
            "module_name": module.name,
            "state": module.state.value,
            "enabled": module.enabled,
            "required": module.required,
            "metrics": {
                "initialization_time": module.metrics.initialization_time,
                "startup_time": module.metrics.startup_time,
                "restart_count": module.metrics.restart_count,
                "failure_count": module.metrics.failure_count,
                "last_failure_time": module.metrics.last_failure_time,
                "last_failure_reason": module.metrics.last_failure_reason
            }
        }

        # 执行健康检查
        if module.health_checker:
            try:
                if asyncio.iscoroutinefunction(module.health_checker):
                    health_result = await module.health_checker()
                else:
                    health_result = module.health_checker()

                health_info["health_check"] = health_result
                health_info["status"] = "healthy" if health_result.get("healthy", True) else "unhealthy"
            except Exception as e:
                health_info["health_check"] = {"error": str(e)}
                health_info["status"] = "unhealthy"
        else:
            health_info["status"] = "healthy" if module.state == ModuleState.RUNNING else "unknown"

        return health_info

    def get_module_status(self) -> Dict[str, Any]:
        """获取所有模块状态"""
        # 创建字典的副本以避免迭代时修改
        modules_copy = dict(self.modules)
        module_names = list(self.modules.keys())

        return {
            "total_modules": len(modules_copy),
            "initialized_modules": self.initialized_modules.copy(),
            "running_modules": self.running_modules.copy(),
            "failed_modules": self.failed_modules.copy(),
            "module_states": {name: module.state.value for name, module in modules_copy.items()},
            "dependency_graph": {
                name: list(self.dependency_graph.get_dependencies(name))
                for name in module_names
            }
        }

    def get_module_state(self, name: str) -> Optional[ModuleState]:
        """获取模块状态

        Args:
            name: 模块名称

        Returns:
            Optional[ModuleState]: 模块状态,如果模块不存在返回None
        """
        module = self.modules.get(name)
        return module.state if module else None

    def is_module_initialized(self, name: str) -> bool:
        """检查模块是否已初始化

        Args:
            name: 模块名称

        Returns:
            bool: 是否已初始化
        """
        return name in self.initialized_modules

    def get_initialized_modules(self) -> Set[str]:
        """获取已初始化的模块列表

        Returns:
            Set[str]: 已初始化的模块名称集合
        """
        return self.initialized_modules.copy()

    def get_failed_modules(self) -> Set[str]:
        """获取初始化失败的模块列表

        Returns:
            Set[str]: 初始化失败的模块名称集合
        """
        return self.failed_modules.copy()

    def get_module_definition(self, module_name: str) -> Optional[ModuleInfo]:
        """获取模块定义"""
        return self.modules.get(module_name)

    def get_all_modules(self) -> Dict[str, ModuleInfo]:
        """获取所有模块定义"""
        return self.modules.copy()





class UnifiedModuleInitializer:
    """统一模块初始化器（重构版）

    负责管理所有模块的条件化初始化,包括Web、调度器、监控、异步等模块.
    提供统一的初始化入口和管理接口.
    """

    def __init__(self, context):
        """初始化统一模块初始化器

        Args:
            context: 应用上下文实例
        """
        self.context = context
        self.module_initializer = ModuleInitializer(context.get_environment(), context)

        # 模块实例存储
        self.web_application = None
        self.scheduler = None
        self.actuator_context = None
        self.async_integration = None

        # 注册所有模块
        self._register_all_modules()

        logger.debug("UnifiedModuleInitializer created")

    def _register_all_modules(self) -> None:
        """注册所有模块"""
        logger.debug("Registering all modules...")

        # 注册异步模块(优先级最高,其他模块可能依赖)
        async_module = ModuleInfo(
            name="async",
            description="异步处理模块",
            config_key="miniboot.async.enabled",
            initializer=self._initialize_async_module,
            priority=ModulePriority.CRITICAL,
            required=False,
            tags={"core", "async"}
        )
        self.module_initializer.register_module(async_module)

        # 注册事件模块(高优先级,其他模块可能依赖事件系统)
        events_module = ModuleInfo(
            name="events",
            description="事件系统模块",
            config_key="miniboot.events.enabled",
            initializer=self._initialize_events_module,
            priority=ModulePriority.HIGH,
            required=False,
            tags={"core", "events"}
        )
        self.module_initializer.register_module(events_module)
        # 注册Web模块
        web_module = ModuleInfo(
            name="web",
            description="Web框架模块",
            config_key="miniboot.web.enabled",
            initializer=self._initialize_web_module,
            starter=self._start_web_module,
            stopper=self._stop_web_module,
            health_checker=self._check_web_health,
            priority=ModulePriority.NORMAL,  # 保持正常优先级，通过注解控制自动配置顺序
            required=False,
            tags={"web", "http"}
        )
        self.module_initializer.register_module(web_module)

        # 注册调度器模块
        scheduler_module = ModuleInfo(
            name="scheduler",
            description="任务调度模块",
            config_key="miniboot.scheduler.enabled",
            initializer=self._initialize_scheduler_module,
            starter=self._start_scheduler_module,
            stopper=self._stop_scheduler_module,
            health_checker=self._check_scheduler_health,
            priority=ModulePriority.NORMAL,
            required=False,
            tags={"scheduler", "tasks"}
        )
        self.module_initializer.register_module(scheduler_module)

        # 移除硬编码的 Actuator 模块注册
        # 监控模块现在通过接口驱动的自动发现机制集成
        # 不再需要在核心框架中硬编码特定的 Starter 模块
        logger.debug("Monitoring modules will be auto-discovered through interface-driven integration")

        logger.debug("All modules registered")

    async def initialize_all_modules(self) -> Dict[str, Any]:
        """初始化所有模块

        Returns:
            Dict[str, Any]: 模块初始化结果
        """
        logger.info("🚀 Starting unified module initialization...")

        # 执行模块初始化
        result = await self.module_initializer.initialize_modules()

        return result

    async def start_all_modules(self) -> Dict[str, bool]:
        """启动所有已初始化的模块"""
        logger.info("🚀 Starting all initialized modules...")

        results = {}
        for module_name in self.module_initializer.get_initialized_modules():
            success = await self.module_initializer.start_module(module_name)
            results[module_name] = success

        return results

    async def stop_all_modules(self) -> Dict[str, bool]:
        """停止所有运行中的模块"""
        logger.info("🛑 Stopping all running modules...")

        results = {}
        for module_name in list(self.module_initializer.running_modules):
            success = await self.module_initializer.stop_module(module_name)
            results[module_name] = success

        return results

    async def restart_module(self, module_name: str) -> bool:
        """重启指定模块"""
        return await self.module_initializer.restart_module(module_name)

    async def check_module_health(self, module_name: str) -> Dict[str, Any]:
        """检查模块健康状态"""
        return await self.module_initializer.check_module_health(module_name)

    def get_module_status(self) -> Dict[str, Any]:
        """获取所有模块状态"""
        base_status = self.module_initializer.get_module_status()

        # 添加统一初始化器特有的状态信息
        base_status.update({
            "web_application_available": self.web_application is not None,
            "scheduler_available": self.scheduler is not None,
            "actuator_context_available": self.actuator_context is not None,
            "async_integration_available": self.async_integration is not None
        })

        return base_status

    # 新增的模块生命周期方法
    async def _start_web_module(self) -> None:
        """启动Web模块"""
        if self.web_application:
            logger.info("🌐 Starting Web application...")
            try:
                # 使用新的start方法启动Web应用
                success = await self.web_application.start()
                if success:
                    logger.info("✅ Web application started successfully")
                else:
                    logger.warning("⚠️ Web application failed to start")
            except Exception as e:
                logger.error(f"❌ Error starting Web application: {e}")
                # 不抛出异常，避免阻塞整个应用启动

    async def _stop_web_module(self) -> None:
        """停止Web模块"""
        if self.web_application:
            logger.info("🛑 Stopping Web application...")
            try:
                await self.web_application.stop()
                logger.info("✅ Web application stopped successfully")
            except Exception as e:
                logger.error(f"❌ Error stopping Web application: {e}")
                # 不抛出异常，避免阻塞应用关闭

    async def _check_web_health(self) -> Dict[str, Any]:
        """检查Web模块健康状态"""
        if not self.web_application:
            return {"healthy": False, "reason": "Web application not available"}

        return {"healthy": True, "port": getattr(self.web_application, 'port', 'unknown')}

    async def _start_scheduler_module(self) -> None:
        """启动调度器模块"""
        if self.scheduler:
            logger.info("⏰ Starting scheduler...")
            try:
                # 调用实际的调度器启动逻辑
                await self._start_scheduler()
                logger.info("✅ Scheduler module started successfully")
            except Exception as e:
                logger.error(f"❌ Error starting scheduler module: {e}")
                # 不抛出异常，避免阻塞整个应用启动

    async def _stop_scheduler_module(self) -> None:
        """停止调度器模块"""
        if self.scheduler:
            logger.info("🛑 Stopping scheduler...")
            try:
                self.scheduler.shutdown()
                logger.info("✅ Scheduler module stopped successfully")
            except Exception as e:
                logger.error(f"❌ Error stopping scheduler module: {e}")
                # 不抛出异常，避免阻塞应用关闭

    async def _check_scheduler_health(self) -> Dict[str, Any]:
        """检查调度器模块健康状态"""
        if not self.scheduler:
            return {"healthy": False, "reason": "Scheduler not available"}

        return {"healthy": True, "running_jobs": getattr(self.scheduler, 'running_jobs_count', 0)}

    # 移除硬编码的 Actuator 模块方法
    # 这些方法违反了 Spring Boot 的设计原则
    # 监控功能现在通过接口驱动的自动发现机制处理

    # ==================== 异步模块初始化 ====================

    async def _initialize_async_module(self) -> None:
        """初始化异步模块

        采用新的懒加载架构：
        - 只注册异步处理器到Bean工厂
        - 实际的线程池创建延迟到检测到异步注解时
        """
        logger.info("Initializing Async module...")

        try:
            # 导入异步处理器集成功能
            from ..processor.asyncs import AsyncBeanFactoryIntegration

            # 获取Bean工厂
            bean_factory = self.context.get_bean_factory()

            # 注册异步处理器到Bean工厂（懒加载模式）
            AsyncBeanFactoryIntegration.register_async_processors(bean_factory)
            self.async_integration = True

            # 记录异步模块状态
            await self._log_async_module_status()

            logger.info("✅ Async module initialized successfully (lazy loading mode)")

        except ImportError as e:
            logger.error(f"Failed to import async module: {e}")
            raise RuntimeError("Async module is not available") from e
        except Exception as e:
            logger.error(f"Failed to initialize async module: {e}")
            raise

    # ==================== 事件模块初始化 ====================

    async def _initialize_events_module(self) -> None:
        """初始化事件模块"""
        logger.info("Initializing Events module...")

        try:
            # 1. 创建事件配置
            events_properties = self._create_events_properties()

            # 2. 创建配置化的事件发布器
            await self._create_configured_event_publisher(events_properties)

            # 3. 注册事件处理器
            await self._register_event_processors()

            # 4. 扫描事件监听器
            await self._scan_event_listeners()

            logger.info("✅ Events module initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Events module: {e}")
            raise

    def _create_events_properties(self) -> Any:
        """创建事件属性配置"""
        try:
            from ..events.properties import EventsProperties

            # 使用统一的配置加载方式
            properties = EventsProperties.from_environment(self.context)

            return properties

        except ImportError as e:
            logger.error(f"Failed to import EventsProperties: {e}")
            raise RuntimeError("Events module is not available") from e
        except Exception as e:
            logger.error(f"Failed to create events properties: {e}")
            raise

    async def _create_configured_event_publisher(self, events_properties) -> None:
        """创建配置化的事件发布器"""
        logger.debug("Creating configured event publisher...")

        try:
            from ..events.publisher import ApplicationEventPublisher

            # 使用配置创建事件发布器
            event_publisher = ApplicationEventPublisher(config=events_properties)

            # 注册到上下文
            self.context.register_singleton("eventPublisher", event_publisher)
            self.context.register_singleton("eventsProperties", events_properties)

            # 更新应用上下文的事件发布器
            if hasattr(self.context, "_event_publisher"):
                self.context._event_publisher = event_publisher

            logger.debug("Configured event publisher created and registered")

        except ImportError as e:
            logger.error(f"Failed to import ApplicationEventPublisher: {e}")
            raise RuntimeError("Events module is not available") from e
        except Exception as e:
            logger.error(f"Failed to create configured event publisher: {e}")
            raise

    async def _register_event_processors(self) -> None:
        """注册事件处理器"""
        logger.debug("Registering event processors...")

        try:
            # 注册事件监听处理器
            from ..processor.event import EventListenerProcessor

            event_publisher = self.context.get_bean("eventPublisher")
            event_processor = EventListenerProcessor(event_publisher)

            self.context.register_bean_post_processor(event_processor)

            logger.debug("Event processors registered")

        except ImportError as e:
            logger.warning(f"Event processors not available: {e}")
        except Exception as e:
            logger.warning(f"Failed to register event processors: {e}")

    async def _scan_event_listeners(self) -> None:
        """扫描事件监听器"""
        logger.debug("Scanning event listeners...")

        try:
            # 这里可以添加事件监听器的自动扫描逻辑
            # 目前通过 EventListenerProcessor 在 Bean 后处理时自动处理
            logger.debug("Event listeners scanning completed")

        except Exception as e:
            logger.warning(f"Failed to scan event listeners: {e}")

    async def _log_async_module_status(self) -> None:
        """记录异步模块状态"""
        try:
            # 获取异步配置信息
            default_timeout = self.context.get_property("miniboot.async.default-timeout", 30.0)

            # 获取线程池配置
            thread_pools = []
            pool_configs = self.context.get_property("miniboot.async.thread-pools", {})
            if isinstance(pool_configs, dict):
                for pool_name, config in pool_configs.items():
                    if isinstance(config, dict) and config.get("enabled", True):
                        thread_pools.append(pool_name)

            if not thread_pools:
                thread_pools = ["default"]

            logger.info("🔄 Async Module: Enabled")
            logger.info(f"⏱️  Default Timeout: {default_timeout}s")
            logger.info(f"🏊 Thread Pools: {', '.join(thread_pools)}")

        except Exception as e:
            logger.debug(f"Failed to log async module status: {e}")

    # ==================== Web模块初始化 ====================

    async def _initialize_web_module(self) -> None:
        """初始化Web模块"""
        logger.info("Initializing Web module...")

        try:
            # 1. 验证Web配置
            await self._validate_web_configuration()

            # 2. 注册Web处理器
            await self._register_web_processor()

            # 3. 创建Web应用
            await self._create_web_application()

            # 4. 扫描和注册控制器
            await self._scan_web_controllers()

            # 5. 触发Actuator延迟初始化(如果有待处理的回调)
            await self._trigger_actuator_delayed_initialization()

            logger.info("✅ Web module initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Web module: {e}")
            raise

    async def _trigger_actuator_delayed_initialization(self) -> None:
        """触发Actuator延迟初始化"""
        logger.debug("Triggering delayed Actuator initialization...")

        # 执行预定义的回调
        if hasattr(self, "_actuator_delayed_callbacks"):
            for callback in self._actuator_delayed_callbacks:
                try:
                    await callback()
                except Exception as e:
                    logger.error(f"Failed to execute delayed Actuator callback: {e}")
            # 清除回调列表
            self._actuator_delayed_callbacks.clear()

        # 自动注册 Actuator 路由（如果有 ActuatorRouteRegistrar Bean）
        try:
            await self._auto_register_actuator_routes()
        except Exception as e:
            logger.error(f"Failed to auto-register Actuator routes: {e}")

    async def _auto_register_actuator_routes(self) -> None:
        """自动注册 Actuator 路由到 FastAPI 应用"""
        try:
            # 1. 检查是否有 ActuatorRouteRegistrar Bean
            try:
                route_registrar = self.context.get_bean("actuator_route_registrar")
                if not route_registrar:
                    logger.debug("ActuatorRouteRegistrar bean not found, skipping route registration")
                    return
            except Exception:
                logger.debug("ActuatorRouteRegistrar bean not available, skipping route registration")
                return

            # 2. 确保 FastAPI 应用实例可用
            web_app = None

            # 首先尝试从实例属性获取
            if hasattr(self, 'web_app') and self.web_app:
                web_app = self.web_app
                logger.debug("Got FastAPI app from instance attribute")
            else:
                # 尝试从 Bean 工厂获取 webApplication Bean
                try:
                    web_application_bean = self.context.get_bean("webApplication")
                    if web_application_bean:
                        logger.debug(f"webApplication Bean found: {type(web_application_bean)}")
                        if hasattr(web_application_bean, 'fastapi_app'):
                            web_app = web_application_bean.fastapi_app
                            logger.debug(f"Got FastAPI app from webApplication Bean: {web_app}")
                        else:
                            logger.debug("webApplication Bean has no fastapi_app attribute")
                    else:
                        logger.debug("webApplication Bean is None")
                except Exception as e:
                    logger.debug(f"Failed to get webApplication Bean: {e}")

            if not web_app:
                logger.warning("FastAPI app not available, cannot register Actuator routes")
                return

            # 设置 FastAPI 应用实例到路由注册器
            route_registrar.set_app(web_app)
            logger.debug("FastAPI app set to ActuatorRouteRegistrar")

            # 3. 执行路由注册
            logger.info("🚀 Starting automatic Actuator route registration...")
            success = route_registrar.register_routes()

            if success:
                # 获取注册统计信息
                registered_routes = route_registrar.get_registered_routes()
                route_count = len(registered_routes)

                logger.info(f"✅ Actuator routes registered successfully: {route_count} routes")

                # 记录已注册的路由
                for route_key, route_info in registered_routes.items():
                    logger.debug(f"  📍 {route_info.method} {route_info.full_path} -> {route_info.endpoint_id}")

            else:
                logger.error("❌ Failed to register Actuator routes")

        except Exception as e:
            logger.error(f"❌ Error during automatic Actuator route registration: {e}")

    async def _validate_web_configuration(self) -> None:
        """验证Web配置"""
        logger.debug("Validating Web configuration...")

        # 验证端口号
        port = self.context.get_property("miniboot.web.port", 8080)
        if not isinstance(port, int) or port <= 0 or port > 65535:
            raise ValueError(f"Invalid web port: {port}")

        # 验证主机地址
        host = self.context.get_property("miniboot.web.host", "0.0.0.0")
        if not isinstance(host, str) or not host.strip():
            raise ValueError(f"Invalid web host: {host}")

        logger.debug(f"Web configuration validated: {host}:{port}")

    async def _register_web_processor(self) -> None:
        """注册Web处理器"""
        logger.debug("Registering Web processor...")

        try:
            from ..processor.web import WebAnnotationProcessor

            web_processor = WebAnnotationProcessor()

            # 注册到Bean后置处理器注册表
            if hasattr(self.context, "_processor_registry"):
                self.context._processor_registry.register_processor(web_processor)
                logger.debug("Web processor registered")
            else:
                logger.warning("Processor registry not found")

        except ImportError as e:
            logger.error(f"Failed to import Web processor: {e}")
            raise RuntimeError("Web processor is not available") from e

    async def _create_web_application(self) -> None:
        """创建Web应用"""
        logger.debug("Creating Web application...")

        try:
            from ..web.application import WebApplication

            # 创建Web属性配置
            web_properties = self._create_web_properties()

            # 创建Web应用实例
            self.web_application = WebApplication(properties=web_properties)

            # 获取FastAPI应用实例(延迟初始化)
            self.web_app = self.web_application.get_app()

            # 注册到上下文
            self.context.register_singleton("webApplication", self.web_application)
            self.context.register_singleton("webApp", self.web_app)

            logger.debug("Web application created and registered")

        except ImportError as e:
            logger.error(f"Failed to import Web application: {e}")
            raise RuntimeError("Web application is not available") from e

    def _create_web_properties(self) -> Any:
        """创建Web属性配置"""
        try:
            from ..web.properties import WebProperties

            properties = WebProperties()

            # 基础配置
            properties.enabled = self.context.get_property("miniboot.web.enabled", True)
            properties.host = self.context.get_property("miniboot.web.host", "0.0.0.0")
            properties.port = self.context.get_property("miniboot.web.port", 8080)
            properties.title = self.context.get_property("miniboot.web.title", "Mini-Boot Application")
            properties.description = self.context.get_property("miniboot.web.description", "Mini-Boot Web Application")
            properties.version = self.context.get_property("miniboot.web.version", "1.0.0")
            properties.log_level = self.context.get_property("miniboot.web.log-level", "INFO")

            # CORS配置
            properties.cors.enabled = self.context.get_property("miniboot.web.cors.enabled", True)
            properties.cors.allowed_origins = self.context.get_property("miniboot.web.cors.allowed-origins", ["*"])
            properties.cors.allowed_methods = self.context.get_property(
                "miniboot.web.cors.allowed-methods", ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
            )
            properties.cors.allowed_headers = self.context.get_property("miniboot.web.cors.allowed-headers", ["*"])
            properties.cors.allow_credentials = self.context.get_property("miniboot.web.cors.allow-credentials", True)

            # 其他配置...
            properties.compression.enabled = self.context.get_property("miniboot.web.compression.enabled", True)
            properties.compression.min_size = self.context.get_property("miniboot.web.compression.min-size", 1024)

            properties.logging.enabled = self.context.get_property("miniboot.web.logging.enabled", True)
            properties.docs.enabled = self.context.get_property("miniboot.web.docs.enabled", True)

            # 静态文件配置
            properties.static.enabled = self.context.get_property("mini.web.static.enabled", True)
            properties.static.directory = self.context.get_property("mini.web.static.directory", "static")
            properties.static.mount_path = self.context.get_property("mini.web.static.mount-path", "/static")
            properties.static.html = self.context.get_property("mini.web.static.html", True)
            properties.static.check_dir = self.context.get_property("mini.web.static.check-dir", True)

            return properties

        except ImportError as e:
            logger.error(f"Failed to import WebProperties: {e}")
            raise RuntimeError("WebProperties is not available") from e

    async def _scan_web_controllers(self) -> None:
        """扫描Web控制器"""
        logger.debug("Scanning Web controllers...")

        try:
            from ..annotations.web import is_controller, is_rest_controller

            controller_count = 0
            bean_names = self.context.get_bean_names()

            for bean_name in bean_names:
                try:
                    bean = self.context.get_bean(bean_name)
                    bean_class = bean.__class__

                    if is_controller(bean_class) or is_rest_controller(bean_class):
                        controller_count += 1
                        logger.debug(f"Found controller: {bean_name}")

                except Exception as e:
                    logger.debug(f"Failed to check bean {bean_name}: {e}")

            logger.info(f"🎯 Scanned {controller_count} Web controllers")

        except ImportError as e:
            logger.warning(f"Web annotations not available: {e}")
        except Exception as e:
            logger.warning(f"Failed to scan Web controllers: {e}")

    # ==================== 调度器模块初始化 ====================

    async def _initialize_scheduler_module(self) -> None:
        """初始化调度器模块"""
        logger.info("Initializing Scheduler module...")

        try:
            # 1. 验证调度器配置
            await self._validate_scheduler_configuration()

            # 2. 注册调度器处理器
            await self._register_scheduler_processor()

            # 3. 创建调度器
            await self._create_scheduler()

            # 4. 扫描和注册任务
            await self._scan_scheduled_tasks()

            # 5. 启动调度器
            await self._start_scheduler()

            logger.info("✅ Scheduler module initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Scheduler module: {e}")
            raise

    async def _validate_scheduler_configuration(self) -> None:
        """验证调度器配置"""
        logger.debug("Validating Scheduler configuration...")

        # 验证最大工作线程数
        max_workers = self.context.get_property("miniboot.scheduler.concurrency.max-workers", 10)
        if not isinstance(max_workers, int) or max_workers <= 0:
            raise ValueError(f"Invalid scheduler max-workers: {max_workers}")

        # 验证时区设置
        timezone = self.context.get_property("miniboot.scheduler.timezone", "Asia/Shanghai")
        if not isinstance(timezone, str) or not timezone.strip():
            raise ValueError(f"Invalid scheduler timezone: {timezone}")

        logger.debug(f"Scheduler configuration validated: max_workers={max_workers}, timezone={timezone}")

    async def _register_scheduler_processor(self) -> None:
        """注册调度器处理器"""
        logger.debug("Registering Scheduler processor...")

        try:
            from ..processor.schedule import ScheduledAnnotationProcessor

            scheduler_processor = ScheduledAnnotationProcessor()

            # 注册到Bean后置处理器注册表
            if hasattr(self.context, "_processor_registry"):
                self.context._processor_registry.register_processor(scheduler_processor)
                logger.debug("Scheduler processor registered")
            else:
                logger.warning("Processor registry not found")

        except ImportError as e:
            logger.error(f"Failed to import Scheduler processor: {e}")
            raise RuntimeError("Scheduler processor is not available") from e

    async def _create_scheduler(self) -> None:
        """创建调度器"""
        logger.debug("Creating Scheduler...")

        try:
            from ..schedule.scheduler import MiniBootScheduler

            # 创建调度器属性配置
            scheduler_properties = self._create_scheduler_properties()

            # 创建调度器实例
            self.scheduler = MiniBootScheduler(properties=scheduler_properties)

            # 注册到上下文
            self.context.register_singleton("scheduler", self.scheduler)

            logger.debug("Scheduler created and registered")

        except ImportError as e:
            logger.error(f"Failed to import Scheduler: {e}")
            raise RuntimeError("Scheduler is not available") from e

    def _create_scheduler_properties(self) -> Any:
        """创建调度器属性配置"""
        try:
            from ..schedule.properties import SchedulerProperties

            # 使用统一的配置加载方式
            properties = SchedulerProperties.from_environment(self.context)

            return properties

        except ImportError as e:
            logger.error(f"Failed to import SchedulerProperties: {e}")
            raise RuntimeError("SchedulerProperties is not available") from e

    async def _scan_scheduled_tasks(self) -> None:
        """扫描调度任务"""
        logger.debug("Scanning scheduled tasks...")

        try:
            from ..annotations.schedule import has_methods

            task_count = 0
            bean_names = self.context.get_bean_names()

            for bean_name in bean_names:
                try:
                    bean = self.context.get_bean(bean_name)
                    bean_class = bean.__class__

                    if has_methods(bean_class):
                        task_count += 1
                        logger.debug(f"Found scheduled bean: {bean_name}")

                except Exception as e:
                    logger.debug(f"Failed to check bean {bean_name}: {e}")

            logger.info(f"⏰ Scanned {task_count} scheduled beans")

        except ImportError as e:
            logger.warning(f"Schedule annotations not available: {e}")
        except Exception as e:
            logger.warning(f"Failed to scan scheduled tasks: {e}")

    async def _start_scheduler(self) -> None:
        """启动调度器"""
        if not self.scheduler:
            logger.warning("Scheduler not created, cannot start")
            return

        logger.debug("Starting Scheduler...")

        try:
            # 检查是否自动启动
            auto_startup = self.context.get_property("miniboot.scheduler.auto-startup", True)
            if not auto_startup:
                logger.info("Scheduler auto-startup disabled")
                return

            # 启动调度器
            self.scheduler.start()

            # 创建任务管理器
            await self._create_task_manager()

            logger.info("🕐 Scheduler started successfully")

        except Exception as e:
            logger.error(f"Failed to start Scheduler: {e}")
            raise

    async def _create_task_manager(self) -> None:
        """创建任务管理器"""
        logger.debug("Creating Task Manager...")

        try:
            # 检查是否启用任务管理
            task_management_enabled = self.context.get_property("miniboot.scheduler.task-management.enabled", True)
            if not task_management_enabled:
                logger.debug("Task management disabled")
                return

            from ..schedule.manager import TaskManager

            task_manager = TaskManager(self.scheduler)
            self.context.register_singleton("taskManager", task_manager)

            logger.debug("Task Manager created and registered")

        except ImportError as e:
            logger.warning(f"Task Manager not available: {e}")
        except Exception as e:
            logger.warning(f"Failed to create Task Manager: {e}")

    # ==================== 监控模块初始化 ====================
    # 移除硬编码的 Actuator 初始化逻辑
    # 监控模块现在通过接口驱动的自动发现机制集成

    # 移除硬编码的 Actuator 集成逻辑

    # 移除所有硬编码的 Actuator 初始化方法
    # 这些方法违反了 Spring Boot 的设计原则

    # ==================== 模块管理方法 ====================
    # 旧的 stop_all_modules 方法已移除，使用新的统一模块管理系统

    def get_module_status(self) -> dict[str, Any]:
        """获取模块状态"""
        # 基于模块初始化器的状态来返回正确的模块状态
        initialized_modules = self.module_initializer.get_initialized_modules()

        return {
            "async": "async" in initialized_modules,
            "web": "web" in initialized_modules,
            "scheduler": "scheduler" in initialized_modules,
            "actuator": "actuator" in initialized_modules,
            "initialized_modules": initialized_modules,
            "failed_modules": self.module_initializer.get_failed_modules(),
        }
