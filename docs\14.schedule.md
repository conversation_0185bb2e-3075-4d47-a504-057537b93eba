# Mini-Boot 调度系统设计

## 1. 概述

Mini-Boot 调度系统是框架的重要组件之一，提供了定时任务调度功能。该系统基于 Python 的 `asyncio` 和 `APScheduler` 库，支持多种调度策略，包括 Cron 表达式、固定间隔、固定延迟和一次性任务。调度系统与 Bean 生命周期集成，通过注解驱动的方式，使开发者能够轻松地定义和管理定时任务。

调度系统的主要特点：

-   支持多种调度策略（Cron 表达式、固定速率、固定延迟、一次性任务）
-   提供任务并发控制和资源限制
-   支持任务状态监控和性能指标收集
-   提供任务持久化和故障恢复
-   支持健康检查和状态报告
-   充分利用 Python 的 async/await 特性

## 1.1 目录结构

```
miniboot/schedule/
├── __init__.py                     # 调度模块导出
├── scheduler.py                    # 核心调度器
├── task.py                         # 任务定义和接口
├── annotations.py                  # 调度注解
├── processor.py                    # 调度处理器
├── storage.py                      # 任务存储接口
├── metrics.py                      # 性能指标
├── health.py                       # 健康检查
├── properties.py                   # 调度配置
└── exceptions.py                   # 调度异常
```

## 2. 核心组件

### 2.1 调度器核心

```python
import asyncio
from typing import Dict, List, Optional, Callable, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
from apscheduler.executors.pool import ThreadPoolExecutor
from datetime import datetime, timedelta
import threading

class MiniBootScheduler:
    """Mini-Boot 调度器"""

    def __init__(self, properties: 'SchedulerProperties' = None):
        self.properties = properties or SchedulerProperties()
        self.scheduler = None
        self.tasks: Dict[str, 'ScheduledTask'] = {}
        self.metrics: Dict[str, 'TaskMetrics'] = {}
        self.health_monitor = None
        self._lock = threading.RLock()
        self._running = False

        self._initialize_scheduler()

    def _initialize_scheduler(self):
        """初始化调度器"""
        # 配置作业存储
        jobstores = {
            'default': MemoryJobStore()
        }

        if self.properties.storage.enabled:
            if self.properties.storage.driver == 'sqlite':
                jobstores['persistent'] = SQLAlchemyJobStore(
                    url=f'sqlite:///{self.properties.storage.dsn}'
                )

        # 配置执行器
        executors = {
            'default': AsyncIOExecutor(),
            'threadpool': ThreadPoolExecutor(
                max_workers=self.properties.concurrency.max_concurrent
            )
        }

        # 作业默认配置
        job_defaults = {
            'coalesce': True,  # 合并错过的执行
            'max_instances': self.properties.concurrency.max_task_instances,
            'misfire_grace_time': 30  # 错过执行的宽限时间
        }

        # 创建调度器
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone=self.properties.timezone
        )

        # 添加事件监听器
        self._setup_event_listeners()

    def _setup_event_listeners(self):
        """设置事件监听器"""
        from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR, EVENT_JOB_ADDED, EVENT_JOB_REMOVED

        self.scheduler.add_listener(self._on_job_executed, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self._on_job_error, EVENT_JOB_ERROR)
        self.scheduler.add_listener(self._on_job_added, EVENT_JOB_ADDED)
        self.scheduler.add_listener(self._on_job_removed, EVENT_JOB_REMOVED)

    async def start(self):
        """启动调度器"""
        if not self.properties.enabled:
            return

        with self._lock:
            if self._running:
                return

            self.scheduler.start()
            self._running = True

            # 启动健康监控
            if self.properties.health.enabled:
                await self._start_health_monitor()

    async def stop(self):
        """停止调度器"""
        with self._lock:
            if not self._running:
                return

            self.scheduler.shutdown(wait=True)
            self._running = False

            # 停止健康监控
            if self.health_monitor:
                await self.health_monitor.stop()

    def add_task(self, task: 'ScheduledTask') -> bool:
        """添加任务"""
        try:
            with self._lock:
                task_id = task.get_task_id()

                # 检查任务是否已存在
                if task_id in self.tasks:
                    return False

                # 创建任务包装器
                wrapper = self._create_task_wrapper(task)

                # 根据任务类型添加作业
                if task.get_task_type() == TaskType.CRON:
                    self.scheduler.add_job(
                        wrapper,
                        'cron',
                        **self._parse_cron_expression(task.get_schedule_spec()),
                        id=task_id,
                        replace_existing=True
                    )
                elif task.get_task_type() == TaskType.FIXED_RATE:
                    interval = self._parse_duration(task.get_schedule_spec())
                    self.scheduler.add_job(
                        wrapper,
                        'interval',
                        seconds=interval.total_seconds(),
                        id=task_id,
                        replace_existing=True
                    )
                elif task.get_task_type() == TaskType.FIXED_DELAY:
                    # 固定延迟需要特殊处理
                    self._add_fixed_delay_task(task, wrapper)
                elif task.get_task_type() == TaskType.ONE_TIME:
                    run_date = datetime.now() + timedelta(seconds=1)
                    self.scheduler.add_job(
                        wrapper,
                        'date',
                        run_date=run_date,
                        id=task_id,
                        replace_existing=True
                    )

                # 存储任务
                self.tasks[task_id] = task
                self.metrics[task_id] = TaskMetrics(task_id)

                return True

        except Exception as e:
            print(f"Error adding task {task.get_task_id()}: {e}")
            return False

    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        try:
            with self._lock:
                if task_id not in self.tasks:
                    return False

                # 从调度器中移除作业
                self.scheduler.remove_job(task_id)

                # 清理任务数据
                del self.tasks[task_id]
                del self.metrics[task_id]

                return True

        except Exception as e:
            print(f"Error removing task {task_id}: {e}")
            return False

    def _create_task_wrapper(self, task: 'ScheduledTask') -> Callable:
        """创建任务包装器"""
        async def wrapper():
            task_id = task.get_task_id()
            metrics = self.metrics.get(task_id)

            if not metrics:
                return

            start_time = datetime.now()

            try:
                # 更新指标
                metrics.total_executions += 1
                metrics.concurrent_executions += 1
                metrics.last_execution_time = start_time

                # 执行任务
                if asyncio.iscoroutinefunction(task.run):
                    await task.run()
                else:
                    # 在线程池中执行同步任务
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(None, task.run)

                # 更新成功指标
                metrics.success_count += 1

            except Exception as e:
                # 更新失败指标
                metrics.failure_count += 1
                print(f"Task {task_id} execution failed: {e}")

            finally:
                # 更新执行时间指标
                end_time = datetime.now()
                execution_time = end_time - start_time

                metrics.concurrent_executions -= 1
                metrics.total_execution_time += execution_time

                if metrics.total_executions > 0:
                    metrics.average_execution_time = metrics.total_execution_time / metrics.total_executions

                if execution_time > metrics.max_execution_time:
                    metrics.max_execution_time = execution_time

                if execution_time < metrics.min_execution_time or metrics.min_execution_time == timedelta():
                    metrics.min_execution_time = execution_time

        return wrapper
```

### 2.2 任务接口和类型

```python
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any
import uuid

class TaskType(Enum):
    """任务类型"""
    CRON = "cron"
    FIXED_RATE = "fixed_rate"
    FIXED_DELAY = "fixed_delay"
    ONE_TIME = "one_time"

class ScheduledTask(ABC):
    """定时任务接口"""

    @abstractmethod
    def get_task_id(self) -> str:
        """获取任务ID"""
        pass

    @abstractmethod
    async def run(self) -> None:
        """执行任务"""
        pass

    @abstractmethod
    def get_schedule_spec(self) -> str:
        """获取调度表达式"""
        pass

    @abstractmethod
    def get_task_type(self) -> TaskType:
        """获取任务类型"""
        pass

class MethodTask(ScheduledTask):
    """方法任务实现"""

    def __init__(self, bean_instance: Any, method: callable,
                 schedule_spec: str, task_type: TaskType,
                 task_id: str = None):
        self.bean_instance = bean_instance
        self.method = method
        self.schedule_spec = schedule_spec
        self.task_type = task_type
        self.task_id = task_id or f"{bean_instance.__class__.__name__}.{method.__name__}_{uuid.uuid4().hex[:8]}"

    def get_task_id(self) -> str:
        return self.task_id

    async def run(self) -> None:
        """执行方法任务"""
        try:
            if asyncio.iscoroutinefunction(self.method):
                await self.method(self.bean_instance)
            else:
                self.method(self.bean_instance)
        except Exception as e:
            raise RuntimeError(f"Task execution failed: {e}")

    def get_schedule_spec(self) -> str:
        return self.schedule_spec

    def get_task_type(self) -> TaskType:
        return self.task_type

class LambdaTask(ScheduledTask):
    """Lambda任务实现"""

    def __init__(self, func: callable, schedule_spec: str,
                 task_type: TaskType, task_id: str = None):
        self.func = func
        self.schedule_spec = schedule_spec
        self.task_type = task_type
        self.task_id = task_id or f"lambda_task_{uuid.uuid4().hex[:8]}"

    def get_task_id(self) -> str:
        return self.task_id

    async def run(self) -> None:
        """执行Lambda任务"""
        try:
            if asyncio.iscoroutinefunction(self.func):
                await self.func()
            else:
                self.func()
        except Exception as e:
            raise RuntimeError(f"Lambda task execution failed: {e}")

    def get_schedule_spec(self) -> str:
        return self.schedule_spec

    def get_task_type(self) -> TaskType:
        return self.task_type
```

### 2.3 调度注解

```python
from typing import Optional

def Scheduled(cron: Optional[str] = None,
             fixed_rate: Optional[str] = None,
             fixed_delay: Optional[str] = None,
             initial_delay: Optional[str] = None,
             zone: Optional[str] = None):
    """定时任务装饰器

    Args:
        cron: Cron表达式
        fixed_rate: 固定速率（如 "10s", "1m", "1h"）
        fixed_delay: 固定延迟（如 "10s", "1m", "1h"）
        initial_delay: 初始延迟（如 "5s", "30s"）
        zone: 时区
    """
    def decorator(func: callable) -> callable:
        # 标记为定时任务
        func.__scheduled__ = True
        func.__scheduled_cron__ = cron
        func.__scheduled_fixed_rate__ = fixed_rate
        func.__scheduled_fixed_delay__ = fixed_delay
        func.__scheduled_initial_delay__ = initial_delay
        func.__scheduled_zone__ = zone

        return func

    return decorator

def EnableScheduling(cls=None):
    """启用调度功能的类装饰器"""
    def decorator(cls):
        cls.__enable_scheduling__ = True
        return cls

    if cls is None:
        return decorator
    return decorator(cls)
```

### 2.4 调度配置

```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class ConcurrencyConfig:
    """并发配置"""
    max_concurrent: int = 10
    max_task_instances: int = 1
    allow_concurrent: bool = False

@dataclass
class StorageConfig:
    """存储配置"""
    enabled: bool = False
    driver: str = "memory"  # memory, sqlite, postgresql, mysql
    dsn: str = "scheduler.db"

@dataclass
class MetricsConfig:
    """指标配置"""
    enabled: bool = True

@dataclass
class HealthConfig:
    """健康检查配置"""
    enabled: bool = True
    check_interval: int = 30  # 秒
    memory_threshold: int = 1024 * 1024 * 1024  # 1GB
    cpu_threshold: float = 80.0  # 80%

@dataclass
class SchedulerProperties:
    """调度器配置"""
    enabled: bool = True
    timezone: str = "UTC"
    concurrency: ConcurrencyConfig = None
    storage: StorageConfig = None
    metrics: MetricsConfig = None
    health: HealthConfig = None

    def __post_init__(self):
        if self.concurrency is None:
            self.concurrency = ConcurrencyConfig()
        if self.storage is None:
            self.storage = StorageConfig()
        if self.metrics is None:
            self.metrics = MetricsConfig()
        if self.health is None:
            self.health = HealthConfig()

    @classmethod
    def from_environment(cls, environment) -> 'SchedulerProperties':
        """从环境配置创建调度器配置"""
        return cls(
            enabled=environment.get_property("mini.scheduler.enabled", True),
            timezone=environment.get_property("mini.scheduler.timezone", "UTC"),
            concurrency=ConcurrencyConfig(
                max_concurrent=environment.get_property("mini.scheduler.concurrency.max-concurrent", 10),
                max_task_instances=environment.get_property("mini.scheduler.concurrency.max-task-instances", 1),
                allow_concurrent=environment.get_property("mini.scheduler.concurrency.allow-concurrent", False)
            ),
            storage=StorageConfig(
                enabled=environment.get_property("mini.scheduler.storage.enabled", False),
                driver=environment.get_property("mini.scheduler.storage.driver", "memory"),
                dsn=environment.get_property("mini.scheduler.storage.dsn", "scheduler.db")
            ),
            metrics=MetricsConfig(
                enabled=environment.get_property("mini.scheduler.metrics.enabled", True)
            ),
            health=HealthConfig(
                enabled=environment.get_property("mini.scheduler.health.enabled", True),
                check_interval=environment.get_property("mini.scheduler.health.check-interval", 30),
                memory_threshold=environment.get_property("mini.scheduler.health.memory-threshold", 1024*1024*1024),
                cpu_threshold=environment.get_property("mini.scheduler.health.cpu-threshold", 80.0)
            )
        )
```

### 2.5 性能指标

```python
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List
import threading

@dataclass
class TaskMetrics:
    """任务指标"""
    task_id: str

    # 执行统计
    total_executions: int = 0
    success_count: int = 0
    failure_count: int = 0
    last_execution_time: Optional[datetime] = None
    next_execution_time: Optional[datetime] = None

    # 时间统计
    total_execution_time: timedelta = field(default_factory=lambda: timedelta())
    average_execution_time: timedelta = field(default_factory=lambda: timedelta())
    max_execution_time: timedelta = field(default_factory=lambda: timedelta())
    min_execution_time: timedelta = field(default_factory=lambda: timedelta())

    # 等待统计
    total_wait_time: timedelta = field(default_factory=lambda: timedelta())
    average_wait_time: timedelta = field(default_factory=lambda: timedelta())
    max_wait_time: timedelta = field(default_factory=lambda: timedelta())
    min_wait_time: timedelta = field(default_factory=lambda: timedelta())

    # 并发统计
    concurrent_executions: int = 0
    max_concurrent: int = 0

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_executions == 0:
            return 0.0
        return self.success_count / self.total_executions

    def get_failure_rate(self) -> float:
        """获取失败率"""
        if self.total_executions == 0:
            return 0.0
        return self.failure_count / self.total_executions

class MetricsCollector:
    """指标收集器"""

    def __init__(self):
        self.task_metrics: Dict[str, TaskMetrics] = {}
        self._lock = threading.RLock()

    def get_task_metrics(self, task_id: str) -> Optional[TaskMetrics]:
        """获取任务指标"""
        with self._lock:
            return self.task_metrics.get(task_id)

    def get_all_metrics(self) -> Dict[str, TaskMetrics]:
        """获取所有任务指标"""
        with self._lock:
            return self.task_metrics.copy()

    def update_execution_metrics(self, task_id: str, execution_time: timedelta, success: bool):
        """更新执行指标"""
        with self._lock:
            if task_id not in self.task_metrics:
                self.task_metrics[task_id] = TaskMetrics(task_id)

            metrics = self.task_metrics[task_id]
            metrics.total_executions += 1

            if success:
                metrics.success_count += 1
            else:
                metrics.failure_count += 1

            # 更新时间统计
            metrics.total_execution_time += execution_time
            metrics.average_execution_time = metrics.total_execution_time / metrics.total_executions

            if execution_time > metrics.max_execution_time:
                metrics.max_execution_time = execution_time

            if execution_time < metrics.min_execution_time or metrics.min_execution_time == timedelta():
                metrics.min_execution_time = execution_time
```

### 2.6 调度处理器

```python
import inspect
from typing import Any, List, Tuple
import re

class ScheduleProcessor:
    """调度处理器"""

    def __init__(self, scheduler: MiniBootScheduler):
        self.scheduler = scheduler
        self.registered_tasks: Dict[str, List[str]] = {}

    def process_bean(self, bean: Any, bean_name: str) -> None:
        """处理Bean中的定时任务"""
        bean_class = bean.__class__

        # 检查类是否启用了调度功能
        if not hasattr(bean_class, '__enable_scheduling__'):
            return

        # 扫描定时任务方法
        scheduled_methods = []
        for method_name, method in inspect.getmembers(bean_class, inspect.isfunction):
            if hasattr(method, '__scheduled__'):
                scheduled_methods.append((method_name, method))

        if scheduled_methods:
            self._register_scheduled_tasks(bean, bean_name, scheduled_methods)

    def _register_scheduled_tasks(self, bean: Any, bean_name: str,
                                 scheduled_methods: List[Tuple[str, callable]]) -> None:
        """注册定时任务"""
        registered_tasks = []

        for method_name, method in scheduled_methods:
            try:
                # 获取调度配置
                cron = getattr(method, '__scheduled_cron__', None)
                fixed_rate = getattr(method, '__scheduled_fixed_rate__', None)
                fixed_delay = getattr(method, '__scheduled_fixed_delay__', None)
                initial_delay = getattr(method, '__scheduled_initial_delay__', None)
                zone = getattr(method, '__scheduled_zone__', None)

                # 确定任务类型和调度表达式
                task_type, schedule_spec = self._determine_task_config(
                    cron, fixed_rate, fixed_delay
                )

                if not schedule_spec:
                    print(f"Warning: No valid schedule configuration for {bean_name}.{method_name}")
                    continue

                # 创建方法任务
                task = MethodTask(
                    bean_instance=bean,
                    method=method,
                    schedule_spec=schedule_spec,
                    task_type=task_type,
                    task_id=f"{bean_name}.{method_name}"
                )

                # 添加到调度器
                if self.scheduler.add_task(task):
                    registered_tasks.append(method_name)
                    print(f"Registered scheduled task: {bean_name}.{method_name} ({task_type.value})")

            except Exception as e:
                print(f"Error registering scheduled task {bean_name}.{method_name}: {e}")

        if registered_tasks:
            self.registered_tasks[bean_name] = registered_tasks

    def _determine_task_config(self, cron: str, fixed_rate: str,
                              fixed_delay: str) -> Tuple[TaskType, str]:
        """确定任务配置"""
        if cron:
            return TaskType.CRON, cron
        elif fixed_rate:
            return TaskType.FIXED_RATE, fixed_rate
        elif fixed_delay:
            return TaskType.FIXED_DELAY, fixed_delay
        else:
            return None, None

    def _parse_duration(self, duration_str: str) -> timedelta:
        """解析持续时间字符串"""
        # 支持格式: "10s", "5m", "1h", "2d"
        pattern = r'^(\d+)([smhd])$'
        match = re.match(pattern, duration_str.lower())

        if not match:
            raise ValueError(f"Invalid duration format: {duration_str}")

        value = int(match.group(1))
        unit = match.group(2)

        if unit == 's':
            return timedelta(seconds=value)
        elif unit == 'm':
            return timedelta(minutes=value)
        elif unit == 'h':
            return timedelta(hours=value)
        elif unit == 'd':
            return timedelta(days=value)
        else:
            raise ValueError(f"Unsupported time unit: {unit}")
```

## 3. 使用示例

### 3.1 使用注解定义定时任务

```python
from miniboot.annotations import Component
from miniboot.schedule import Scheduled, EnableScheduling
import asyncio

@Component
@EnableScheduling
class TaskService:
    """任务服务"""

    @Scheduled(cron="0 */5 * * * *")  # 每5分钟执行一次
    def perform_scheduled_task(self):
        """执行定时任务"""
        print(f"Scheduled task executed at {datetime.now()}")
        # 任务实现...

    @Scheduled(fixed_rate="10s")  # 固定间隔10秒执行
    async def perform_fixed_rate_task(self):
        """执行固定速率任务"""
        print(f"Fixed rate task executed at {datetime.now()}")
        await asyncio.sleep(2)  # 模拟异步操作
        print("Fixed rate task completed")

    @Scheduled(fixed_delay="1m")  # 上次执行完成后延迟1分钟再执行
    def perform_fixed_delay_task(self):
        """执行固定延迟任务"""
        print(f"Fixed delay task executed at {datetime.now()}")
        # 模拟耗时操作
        import time
        time.sleep(5)
        print("Fixed delay task completed")

    @Scheduled(cron="0 0 2 * * *")  # 每天凌晨2点执行
    async def daily_cleanup_task(self):
        """每日清理任务"""
        print("Starting daily cleanup...")
        # 清理临时文件
        await self._cleanup_temp_files()
        # 清理过期数据
        await self._cleanup_expired_data()
        print("Daily cleanup completed")

    async def _cleanup_temp_files(self):
        """清理临时文件"""
        await asyncio.sleep(1)  # 模拟清理操作
        print("Temp files cleaned")

    async def _cleanup_expired_data(self):
        """清理过期数据"""
        await asyncio.sleep(2)  # 模拟清理操作
        print("Expired data cleaned")

@Component
@EnableScheduling
class ReportService:
    """报告服务"""

    @Scheduled(cron="0 0 9 * * MON")  # 每周一上午9点执行
    def generate_weekly_report(self):
        """生成周报"""
        print("Generating weekly report...")
        # 生成报告逻辑
        self._collect_weekly_data()
        self._generate_report()
        self._send_report()
        print("Weekly report generated and sent")

    def _collect_weekly_data(self):
        """收集周数据"""
        print("Collecting weekly data...")

    def _generate_report(self):
        """生成报告"""
        print("Generating report...")

    def _send_report(self):
        """发送报告"""
        print("Sending report...")
```

### 3.2 手动创建和注册任务

```python
from miniboot.schedule import ScheduledTask, TaskType, LambdaTask

class CustomTask(ScheduledTask):
    """自定义任务"""

    def __init__(self, task_id: str):
        self.task_id = task_id

    def get_task_id(self) -> str:
        return self.task_id

    async def run(self) -> None:
        """执行任务"""
        print(f"Custom task {self.task_id} executed at {datetime.now()}")
        await asyncio.sleep(1)  # 模拟异步操作
        print(f"Custom task {self.task_id} completed")

    def get_schedule_spec(self) -> str:
        return "*/30 * * * * *"  # 每30秒执行一次

    def get_task_type(self) -> TaskType:
        return TaskType.CRON

async def register_custom_tasks(scheduler: MiniBootScheduler):
    """注册自定义任务"""

    # 注册自定义任务类
    custom_task = CustomTask("custom-task-1")
    scheduler.add_task(custom_task)

    # 注册Lambda任务
    def simple_task():
        print(f"Simple task executed at {datetime.now()}")

    lambda_task = LambdaTask(
        func=simple_task,
        schedule_spec="*/15 * * * * *",  # 每15秒执行一次
        task_type=TaskType.CRON,
        task_id="lambda-task-1"
    )
    scheduler.add_task(lambda_task)

    # 注册异步Lambda任务
    async def async_task():
        print(f"Async task started at {datetime.now()}")
        await asyncio.sleep(2)
        print(f"Async task completed at {datetime.now()}")

    async_lambda_task = LambdaTask(
        func=async_task,
        schedule_spec="1m",  # 每分钟执行一次
        task_type=TaskType.FIXED_RATE,
        task_id="async-lambda-task-1"
    )
    scheduler.add_task(async_lambda_task)
```

### 3.3 配置示例

```yaml
# application.yml
mini:
    scheduler:
        enabled: true
        timezone: "Asia/Shanghai"
        concurrency:
            max-concurrent: 20
            max-task-instances: 2
            allow-concurrent: true
        storage:
            enabled: true
            driver: "sqlite"
            dsn: "scheduler.db"
        metrics:
            enabled: true
        health:
            enabled: true
            check-interval: 30
            memory-threshold: ********** # 2GB
            cpu-threshold: 85.0
```

## 4. 与 Go 版本对比

Mini-Boot 调度系统在设计上借鉴了 Go 版本的核心理念，但针对 Python 语言特性进行了优化：

| 特性       | Go 版本             | Python 版本                 |
| ---------- | ------------------- | --------------------------- |
| 实现语言   | Go                  | Python                      |
| 底层调度器 | robfig/cron         | APScheduler                 |
| 任务存储   | 支持 SQLite 持久化  | 支持多种数据库持久化        |
| 健康检查   | 内置支持            | 内置支持                    |
| 性能指标   | 内置详细指标        | 内置详细指标                |
| 动态任务   | 支持运行时添加/删除 | 支持运行时添加/删除         |
| 分布式调度 | 不支持              | 可通过 APScheduler 集群实现 |
| 任务历史   | 支持执行历史记录    | 支持执行历史记录            |
| 异步支持   | goroutine           | async/await + 线程池        |
| 并发模型   | 基于 goroutine      | 基于 asyncio 事件循环       |
| 错误处理   | error 返回值        | Python 异常机制             |
| 配置方式   | YAML 配置文件       | YAML 配置文件 + dataclass   |

### 使用对比

**Go 版本:**

```go
// @Scheduled(cron="0 0/15 * * * ?") // 每15分钟执行一次
func (s *TaskService) PerformTask() error {
    // 任务实现...
    return nil
}
```

**Python 版本:**

```python
@Scheduled(cron="0 */15 * * * *")  # 每15分钟执行一次
async def perform_task(self):
    # 任务实现...
    pass
```

## 5. 性能优化和最佳实践

### 5.1 异步任务优化

```python
@Component
@EnableScheduling
class OptimizedTaskService:
    """优化的任务服务"""

    @Scheduled(fixed_rate="5s")
    async def io_intensive_task(self):
        """IO密集型任务 - 使用异步"""
        async with aiohttp.ClientSession() as session:
            async with session.get('http://api.example.com/data') as response:
                data = await response.json()
                await self._process_data(data)

    @Scheduled(fixed_rate="30s")
    def cpu_intensive_task(self):
        """CPU密集型任务 - 在线程池中执行"""
        # 这个方法会自动在线程池中执行，不会阻塞事件循环
        result = self._complex_calculation()
        self._save_result(result)

    async def _process_data(self, data):
        """处理数据"""
        await asyncio.sleep(0.1)  # 模拟异步处理

    def _complex_calculation(self):
        """复杂计算"""
        # CPU密集型计算
        return sum(i * i for i in range(100000))

    def _save_result(self, result):
        """保存结果"""
        print(f"Result saved: {result}")
```

### 5.2 错误处理和重试

```python
@Component
@EnableScheduling
class RobustTaskService:
    """健壮的任务服务"""

    @Scheduled(fixed_rate="1m")
    async def reliable_task(self):
        """可靠的任务"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                await self._execute_task()
                break  # 成功执行，退出重试循环
            except Exception as e:
                retry_count += 1
                if retry_count >= max_retries:
                    print(f"Task failed after {max_retries} retries: {e}")
                    # 可以发送告警或记录到错误日志
                    await self._handle_task_failure(e)
                else:
                    print(f"Task failed, retrying ({retry_count}/{max_retries}): {e}")
                    await asyncio.sleep(2 ** retry_count)  # 指数退避

    async def _execute_task(self):
        """执行任务"""
        # 可能失败的任务逻辑
        if random.random() < 0.3:  # 30%的失败率
            raise Exception("Random task failure")
        print("Task executed successfully")

    async def _handle_task_failure(self, error):
        """处理任务失败"""
        # 发送告警、记录日志等
        print(f"Handling task failure: {error}")
```

### 5.3 任务监控和指标

```python
@Component
@EnableScheduling
class MonitoredTaskService:
    """监控的任务服务"""

    def __init__(self):
        self.task_counter = 0
        self.success_counter = 0
        self.failure_counter = 0

    @Scheduled(fixed_rate="10s")
    async def monitored_task(self):
        """被监控的任务"""
        start_time = datetime.now()
        self.task_counter += 1

        try:
            await self._do_work()
            self.success_counter += 1

        except Exception as e:
            self.failure_counter += 1
            print(f"Task failed: {e}")

        finally:
            execution_time = datetime.now() - start_time
            print(f"Task {self.task_counter} completed in {execution_time.total_seconds():.2f}s")
            print(f"Success rate: {self.success_counter/self.task_counter:.2%}")

    async def _do_work(self):
        """执行工作"""
        await asyncio.sleep(random.uniform(0.5, 2.0))  # 模拟工作
        if random.random() < 0.1:  # 10%的失败率
            raise Exception("Work failed")
```

## 6. 总结

Mini-Boot 调度系统提供了一种灵活、可靠的定时任务调度机制，通过与 Bean 生命周期的集成，使定时任务的定义和管理变得简单直观。

### 核心特性

1. **完整的调度策略支持**

    - Cron 表达式调度
    - 固定速率调度
    - 固定延迟调度
    - 一次性任务调度

2. **Python 原生异步支持**

    - 充分利用 async/await 语法
    - 基于 asyncio 事件循环
    - 支持协程和线程池混合执行

3. **注解驱动的任务定义**

    - 简洁的@Scheduled 装饰器
    - 自动的 Bean 集成
    - 灵活的配置选项

4. **强大的监控和管理**
    - 详细的性能指标收集
    - 健康状态监控
    - 任务执行历史记录

### 设计优势

1. **易于使用**: 简单的注解 API，与框架深度集成
2. **高性能**: 基于 APScheduler 的高效调度引擎
3. **可扩展**: 支持自定义任务类型和存储后端
4. **可靠性**: 内置错误处理和重试机制
5. **可观测性**: 丰富的指标和监控能力

### 适用场景

1. **定期数据处理**: 数据同步、清理、备份等
2. **报告生成**: 定期生成业务报告
3. **系统维护**: 日志清理、缓存刷新等
4. **监控告警**: 定期检查系统状态
5. **业务流程**: 定时触发业务逻辑

通过调度系统，Mini-Boot 框架为 Python 应用提供了企业级的定时任务解决方案，特别适合需要可靠、高效定时任务处理的应用场景。该系统的设计借鉴了 Spring @Scheduled 的理念，但针对 Python 的特性进行了优化，提供了更好的异步支持和性能表现。

---

_本文档定义了 Mini-Boot 框架的调度系统设计，提供完整的定时任务调度功能。_
