#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mini-Boot框架重试策略实现

提供多种重试策略算法：
- FixedBackoff: 固定延迟
- ExponentialBackoff: 指数退避
- LinearBackoff: 线性退避
"""

import random
from abc import ABC, abstractmethod
from enum import Enum


class ExceptionAction(Enum):
    """异常处理动作枚举"""

    PROPAGATE = "propagate"  # 传播异常
    SUPPRESS = "suppress"    # 抑制异常
    LOG_AND_CONTINUE = "log_and_continue"  # 记录日志并继续
    RETRY = "retry"          # 重试
    FALLBACK = "fallback"    # 回退处理


class RetryStrategy(Enum):
    """重试策略枚举"""

    FIXED_BACKOFF = "fixed"
    EXPONENTIAL_BACKOFF = "exponential"
    LINEAR_BACKOFF = "linear"
    RANDOM_JITTER_BACKOFF = "random_jitter"


class BackoffStrategy(ABC):
    """回退策略基类"""

    @abstractmethod
    def calculate_delay(self, attempt: int, base_delay: float) -> float:
        """计算延迟时间

        Args:
            attempt: 当前尝试次数(从1开始)
            base_delay: 基础延迟时间

        Returns:
            延迟时间(秒)
        """
        pass


class FixedBackoff(BackoffStrategy):
    """固定延迟回退策略"""

    def calculate_delay(self, attempt: int, base_delay: float) -> float:
        """固定延迟，每次重试都使用相同的延迟时间"""
        return base_delay


class ExponentialBackoff(BackoffStrategy):
    """指数退避策略"""

    def __init__(self, multiplier: float = 2.0, max_delay: float = 60.0):
        """初始化指数退避策略

        Args:
            multiplier: 指数倍数，默认2.0
            max_delay: 最大延迟时间，默认60秒
        """
        self.multiplier = multiplier
        self.max_delay = max_delay

    def calculate_delay(self, attempt: int, base_delay: float) -> float:
        """指数增长延迟：base_delay * (multiplier ^ (attempt - 1))"""
        delay = base_delay * (self.multiplier ** (attempt - 1))
        return min(delay, self.max_delay)


class LinearBackoff(BackoffStrategy):
    """线性退避策略"""

    def __init__(self, increment: float = 1.0, max_delay: float = 30.0):
        """初始化线性退避策略

        Args:
            increment: 线性增量，默认1.0秒
            max_delay: 最大延迟时间，默认30秒
        """
        self.increment = increment
        self.max_delay = max_delay

    def calculate_delay(self, attempt: int, base_delay: float) -> float:
        """线性增长延迟：base_delay + (attempt - 1) * increment"""
        delay = base_delay + (attempt - 1) * self.increment
        return min(delay, self.max_delay)


class RandomJitterBackoff(BackoffStrategy):
    """随机抖动退避策略"""

    def __init__(self, jitter_factor: float = 0.1, max_delay: float = 30.0):
        """初始化随机抖动退避策略

        Args:
            jitter_factor: 抖动因子，默认0.1 (10%)
            max_delay: 最大延迟时间，默认30秒
        """
        self.jitter_factor = jitter_factor
        self.max_delay = max_delay

    def calculate_delay(self, attempt: int, base_delay: float) -> float:
        """指数增长 + 随机抖动"""
        base = base_delay * (2 ** (attempt - 1))
        jitter = base * self.jitter_factor * random.random()
        delay = base + jitter
        return min(delay, self.max_delay)
