#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 综合性能测试套件 - 全面的性能基准测试和回归检测

提供完整的性能测试框架，包括基准测试、回归检测、性能报告生成等功能。
"""

import gc
import json
import os
import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import asdict, dataclass
from datetime import datetime
from typing import Any
from unittest.mock import Mock

import psutil

from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.proxy import BeanProxy
# 性能监控
# 核心组件
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.bean.utils import DependencyGraph
# 事件系统
from miniboot.events.composite_event import CompositeEvent
from miniboot.events.event_types import (ApplicationStartedEvent,
                                         BeanCreatedEvent)


@dataclass
class PerformanceBenchmark:
    """性能基准数据"""

    test_name: str
    operations_count: int
    total_time: float
    avg_time_per_operation: float
    memory_usage_mb: float
    cpu_usage_percent: float
    throughput_ops_per_sec: float
    timestamp: str

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class PerformanceReport:
    """性能测试报告"""

    test_suite: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    total_duration: float
    benchmarks: list[PerformanceBenchmark]
    system_info: dict[str, Any]
    timestamp: str

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return {
            "test_suite": self.test_suite,
            "total_tests": self.total_tests,
            "passed_tests": self.passed_tests,
            "failed_tests": self.failed_tests,
            "total_duration": self.total_duration,
            "benchmarks": [b.to_dict() for b in self.benchmarks],
            "system_info": self.system_info,
            "timestamp": self.timestamp,
        }


class PerformanceTestBean:
    """性能测试用的Bean"""

    def __init__(self, name: str):
        self.name = name
        self.call_count = 0
        self.data = {"key": "value", "number": 42}
        self._lock = threading.Lock()

    def simple_method(self) -> str:
        """简单方法"""
        with self._lock:
            self.call_count += 1
            return f"result_{self.call_count}"

    def complex_method(self, param1: str, param2: int = 10) -> dict[str, Any]:
        """复杂方法"""
        with self._lock:
            self.call_count += 1
            # 模拟一些计算
            result = {"param1": param1, "param2": param2, "computed": param2 * 2, "call_count": self.call_count, "timestamp": time.time()}
            return result

    def get_data(self) -> dict[str, Any]:
        """获取数据"""
        return self.data.copy()


class ComprehensivePerformanceTestSuite(unittest.TestCase):
    """综合性能测试套件"""

    def setUp(self):
        """测试前置设置"""
        self.process = psutil.Process(os.getpid())
        self.benchmarks: list[PerformanceBenchmark] = []
        self.failed_tests = 0
        self.passed_tests = 0

        # 性能测试参数
        self.small_scale_ops = 1000
        self.medium_scale_ops = 5000
        self.large_scale_ops = 10000

        # 性能阈值（秒）
        self.performance_thresholds = {
            "bean_factory_get": 0.001,  # 1ms per operation
            "proxy_method_call": 0.002,  # 2ms per operation
            "event_creation": 0.001,  # 1ms per operation
            "cache_operation": 0.0005,  # 0.5ms per operation
        }

    def _measure_performance(self, test_name: str, operation_func, operations_count: int) -> PerformanceBenchmark:
        """测量性能的通用方法"""
        # 垃圾回收
        gc.collect()

        # 记录初始状态
        initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu = self.process.cpu_percent()

        # 执行测试
        start_time = time.time()
        operation_func()
        end_time = time.time()

        # 记录结束状态
        final_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = self.process.cpu_percent()

        # 计算指标
        total_time = end_time - start_time
        avg_time = total_time / operations_count
        memory_usage = final_memory - initial_memory
        cpu_usage = (initial_cpu + final_cpu) / 2
        throughput = operations_count / total_time if total_time > 0 else 0

        return PerformanceBenchmark(
            test_name=test_name,
            operations_count=operations_count,
            total_time=total_time,
            avg_time_per_operation=avg_time,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            throughput_ops_per_sec=throughput,
            timestamp=datetime.now().isoformat(),
        )

    def test_bean_factory_performance(self):
        """Bean工厂性能测试"""
        print("\n=== Bean工厂性能测试 ===")

        # 创建工厂和Bean
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = DefaultBeanFactory()

        # 注册Bean
        beans = {}
        for i in range(100):
            bean = PerformanceTestBean(f"bean_{i}")
            bean_name = f"bean_{i}"
            beans[bean_name] = bean

            # 注册Bean定义
            from miniboot.bean.definition import BeanDefinition, BeanScope

            bean_definition = BeanDefinition(bean_name=bean_name, bean_class=type(bean), scope=BeanScope.SINGLETON)
            registry.register_bean_definition(bean_name, bean_definition)

            # 直接注册Bean实例到工厂
            factory.register_singleton(bean_name, bean)

        def bean_factory_operations():
            """Bean工厂操作"""
            for _ in range(self.medium_scale_ops):
                bean_name = f"bean_{_ % 100}"
                bean = factory.get_bean(bean_name)
                if bean:
                    bean.simple_method()

        # 测量性能
        benchmark = self._measure_performance("bean_factory_get", bean_factory_operations, self.medium_scale_ops)

        self.benchmarks.append(benchmark)

        # 验证性能阈值
        threshold = self.performance_thresholds["bean_factory_get"]
        if benchmark.avg_time_per_operation <= threshold:
            self.passed_tests += 1
            print("✅ Bean工厂性能测试通过")
        else:
            self.failed_tests += 1
            print("❌ Bean工厂性能测试失败")

        print(f"   平均耗时: {benchmark.avg_time_per_operation * 1000:.3f}ms")
        print(f"   吞吐量: {benchmark.throughput_ops_per_sec:.0f} ops/sec")
        print(f"   内存使用: {benchmark.memory_usage_mb:.2f}MB")

        # 清理
        factory.shutdown()

    def test_proxy_performance(self):
        """代理性能测试"""
        print("\n=== 代理性能测试 ===")

        bean = PerformanceTestBean("proxyTestBean")
        proxy = CompositeProxy(sync_bean=bean, bean_name="performanceTestProxy")

        def proxy_operations():
            """代理操作"""
            for _ in range(self.medium_scale_ops):
                proxy.simple_method()
                if _ % 10 == 0:  # 偶尔调用复杂方法
                    proxy.complex_method("test", _ % 100)

        # 测量性能
        benchmark = self._measure_performance("proxy_method_call", proxy_operations, self.medium_scale_ops)

        self.benchmarks.append(benchmark)

        # 验证性能阈值
        threshold = self.performance_thresholds["proxy_method_call"]
        if benchmark.avg_time_per_operation <= threshold:
            self.passed_tests += 1
            print("✅ 代理性能测试通过")
        else:
            self.failed_tests += 1
            print("❌ 代理性能测试失败")

        print(f"   平均耗时: {benchmark.avg_time_per_operation * 1000:.3f}ms")
        print(f"   吞吐量: {benchmark.throughput_ops_per_sec:.0f} ops/sec")
        print(f"   内存使用: {benchmark.memory_usage_mb:.2f}MB")

        # 验证代理统计
        stats = proxy.get_stats()
        print(f"   代理访问次数: {stats['access_count']}")

        # 清理
        proxy.shutdown()

    def test_event_system_performance(self):
        """事件系统性能测试"""
        print("\n=== 事件系统性能测试 ===")

        events = []

        def event_operations():
            """事件操作"""
            for i in range(self.medium_scale_ops):
                if i % 3 == 0:
                    event = ApplicationStartedEvent(application=Mock(), startup_time=1.5)
                elif i % 3 == 1:
                    event = BeanCreatedEvent(bean_name=f"bean_{i}", bean_class="TestBean", source=Mock())
                else:
                    event = CompositeEvent(event_type=f"CustomEvent_{i}", source=Mock(), data={"index": i})

                # 操作事件
                event.set_data("processed", True)
                event.mark_processed()
                events.append(event)

        # 测量性能
        benchmark = self._measure_performance("event_creation", event_operations, self.medium_scale_ops)

        self.benchmarks.append(benchmark)

        # 验证性能阈值
        threshold = self.performance_thresholds["event_creation"]
        if benchmark.avg_time_per_operation <= threshold:
            self.passed_tests += 1
            print("✅ 事件系统性能测试通过")
        else:
            self.failed_tests += 1
            print("❌ 事件系统性能测试失败")

        print(f"   平均耗时: {benchmark.avg_time_per_operation * 1000:.3f}ms")
        print(f"   吞吐量: {benchmark.throughput_ops_per_sec:.0f} ops/sec")
        print(f"   内存使用: {benchmark.memory_usage_mb:.2f}MB")

        # 验证事件唯一性
        event_ids = [event.event_id for event in events]
        unique_ids = set(event_ids)
        self.assertEqual(len(unique_ids), len(event_ids), "所有事件ID应该唯一")

        # 清理事件
        for event in events:
            event.cleanup()

    def test_cache_performance(self):
        """缓存性能测试"""
        print("\n=== 缓存性能测试 ===")

        cache = MethodCacheComponent(cache_size=1000)
        cache.initialize()

        def cache_operations():
            """缓存操作"""
            for i in range(self.medium_scale_ops):
                key = f"cache_key_{i % 500}"  # 重复使用一些key以测试命中率
                value = f"cache_value_{i}"

                # 写入缓存
                cache.put(key, value)

                # 读取缓存
                cached_value = cache.get(key)

                # 验证数据
                if cached_value != value:
                    raise ValueError(f"缓存数据不一致: {cached_value} != {value}")

        # 测量性能
        benchmark = self._measure_performance("cache_operation", cache_operations, self.medium_scale_ops)

        self.benchmarks.append(benchmark)

        # 验证性能阈值
        threshold = self.performance_thresholds["cache_operation"]
        if benchmark.avg_time_per_operation <= threshold:
            self.passed_tests += 1
            print("✅ 缓存性能测试通过")
        else:
            self.failed_tests += 1
            print("❌ 缓存性能测试失败")

        print(f"   平均耗时: {benchmark.avg_time_per_operation * 1000:.3f}ms")
        print(f"   吞吐量: {benchmark.throughput_ops_per_sec:.0f} ops/sec")
        print(f"   内存使用: {benchmark.memory_usage_mb:.2f}MB")

        # 验证缓存统计
        stats = cache.get_stats()
        hit_rate = stats["hits"] / (stats["hits"] + stats["misses"]) if (stats["hits"] + stats["misses"]) > 0 else 0
        print(f"   缓存命中率: {hit_rate:.2%}")
        print(f"   缓存大小: {stats['size']}")

        # 清理
        cache.shutdown()

    def test_concurrent_performance(self):
        """并发性能测试"""
        print("\n=== 并发性能测试 ===")

        # 创建共享资源
        bean = PerformanceTestBean("concurrentBean")
        proxy = CompositeProxy(sync_bean=bean, bean_name="concurrentProxy")

        results = []

        def concurrent_worker(worker_id: int):
            """并发工作函数"""
            worker_start = time.time()
            for i in range(100):
                proxy.simple_method()
                if i % 10 == 0:
                    proxy.get_data()
            worker_end = time.time()
            return worker_end - worker_start

        def concurrent_operations():
            """并发操作"""
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(concurrent_worker, i) for i in range(10)]
                for future in as_completed(futures):
                    results.append(future.result())

        # 测量性能
        benchmark = self._measure_performance(
            "concurrent_operations",
            concurrent_operations,
            1000,  # 10 workers * 100 operations
        )

        self.benchmarks.append(benchmark)

        # 验证并发性能
        if benchmark.total_time <= 5.0:  # 5秒内完成
            self.passed_tests += 1
            print("✅ 并发性能测试通过")
        else:
            self.failed_tests += 1
            print("❌ 并发性能测试失败")

        print(f"   总耗时: {benchmark.total_time:.3f}秒")
        print(f"   平均worker耗时: {sum(results) / len(results):.3f}秒")
        print(f"   吞吐量: {benchmark.throughput_ops_per_sec:.0f} ops/sec")

        # 清理
        proxy.shutdown()

    def generate_performance_report(self) -> PerformanceReport:
        """生成性能测试报告"""
        system_info = {
            "cpu_count": psutil.cpu_count(),
            "memory_total_gb": psutil.virtual_memory().total / 1024 / 1024 / 1024,
            "python_version": f"{psutil.Process().exe}",
            "platform": os.name,
        }

        total_tests = self.passed_tests + self.failed_tests
        total_duration = sum(b.total_time for b in self.benchmarks)

        return PerformanceReport(
            test_suite="ComprehensivePerformanceTestSuite",
            total_tests=total_tests,
            passed_tests=self.passed_tests,
            failed_tests=self.failed_tests,
            total_duration=total_duration,
            benchmarks=self.benchmarks,
            system_info=system_info,
            timestamp=datetime.now().isoformat(),
        )

    def save_performance_report(self, report: PerformanceReport, filename: str = None):
        """保存性能测试报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"

        report_path = os.path.join("tests", "performance", "reports", filename)
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(report.to_dict(), f, indent=2, ensure_ascii=False)

        print(f"\n📊 性能报告已保存: {report_path}")
        return report_path


if __name__ == "__main__":
    # 创建测试实例
    test_instance = ComprehensivePerformanceTestSuite()

    # 运行所有测试方法
    test_methods = [
        test_instance.test_bean_factory_performance,
        test_instance.test_proxy_performance,
        test_instance.test_event_system_performance,
        test_instance.test_cache_performance,
        test_instance.test_concurrent_performance,
    ]

    print("🚀 开始综合性能测试套件")
    print("=" * 50)

    for test_method in test_methods:
        try:
            test_instance.setUp()
            test_method()
        except Exception as e:
            print(f"❌ {test_method.__name__} 失败: {e}")
            test_instance.failed_tests += 1

    # 生成和保存报告
    if test_instance.benchmarks:
        report = test_instance.generate_performance_report()
        report_path = test_instance.save_performance_report(report)

        print("\n" + "=" * 50)
        print("🎯 性能测试总结:")
        print(f"   总测试数: {report.total_tests}")
        print(f"   通过数: {report.passed_tests}")
        print(f"   失败数: {report.failed_tests}")
        print(f"   成功率: {(report.passed_tests / report.total_tests) * 100:.1f}%")
        print(f"   总耗时: {report.total_duration:.3f}秒")
        print(f"   报告路径: {report_path}")

        # 显示性能基准
        print("\n📊 性能基准:")
        for benchmark in report.benchmarks:
            print(f"   {benchmark.test_name}:")
            print(f"     - 平均耗时: {benchmark.avg_time_per_operation * 1000:.3f}ms")
            print(f"     - 吞吐量: {benchmark.throughput_ops_per_sec:.0f} ops/sec")
            print(f"     - 内存使用: {benchmark.memory_usage_mb:.2f}MB")
    else:
        print("❌ 没有收集到性能基准数据")
