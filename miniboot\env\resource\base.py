#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 抽象属性源加载器基类 - 消除配置加载逻辑重复
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, Union

from loguru import logger

from ..sources import MapPropertySource, PropertySource
from .loader import PropertySourceLoader, PropertySourceLoadError, ResourceLoader


class AbstractPropertySourceLoader(PropertySourceLoader, ABC):
    """抽象属性源加载器基类

    封装所有配置格式加载器的通用逻辑,消除重复代码.
    使用模板方法模式定义加载流程,子类只需实现格式特定的解析逻辑.

    通用功能:
    - 文件内容加载:统一的文件读取逻辑
    - 数据处理:通用的数据扁平化和验证
    - 属性源创建:标准的属性源创建流程
    - 异常处理:统一的错误处理和日志记录

    子类只需实现:
    - get_file_extensions(): 支持的文件扩展名
    - _parse_content(): 格式特定的内容解析
    """

    def load(self, name: str, resource_loader: ResourceLoader, location: Union[str, Path]) -> list[PropertySource]:
        """加载属性源 - 模板方法

        定义标准的加载流程:
        1. 验证输入参数
        2. 加载文件内容
        3. 解析内容(子类实现)
        4. 处理数据
        5. 创建属性源
        6. 统一异常处理

        Args:
            name: 属性源名称
            resource_loader: 资源加载器
            location: 资源位置

        Returns:
            属性源列表

        Raises:
            PropertySourceLoadError: 加载失败时抛出
        """
        try:
            # 1. 验证输入参数
            self._validate_inputs(name, resource_loader, location)

            # 2. 加载文件内容
            content = self._load_content(resource_loader, location)

            # 3. 解析内容(子类实现格式特定逻辑)
            data = self._parse_content(content, location)

            # 4. 处理数据(通用逻辑)
            processed_data = self._process_data(data, location)

            # 5. 创建属性源(通用逻辑)
            property_sources = self._create_property_sources(name, processed_data, location)

            # 6. 记录成功日志
            self._log_success(name, location, processed_data)

            return property_sources

        except Exception as e:
            # 统一异常处理
            self._handle_load_error(location, e)

    # ========== 抽象方法 - 子类必须实现 ==========

    @abstractmethod
    def _parse_content(self, content: str, location: Union[str, Path]) -> Any:
        """解析文件内容 - 子类实现格式特定逻辑

        Args:
            content: 文件内容字符串
            location: 文件位置(用于错误信息)

        Returns:
            解析后的数据对象

        Raises:
            Exception: 解析失败时抛出格式特定异常
        """
        pass

    # ========== 通用方法 - 统一实现 ==========

    def _validate_inputs(self, name: str, resource_loader: ResourceLoader, location: Union[str, Path]) -> None:
        """验证输入参数

        Args:
            name: 属性源名称
            resource_loader: 资源加载器
            location: 资源位置

        Raises:
            ValueError: 参数无效时抛出
        """
        if not name or not name.strip():
            raise ValueError("Property source name cannot be empty")

        if resource_loader is None:
            raise ValueError("Resource loader cannot be None")

        if not location:
            raise ValueError("Location cannot be empty")

    def _load_content(self, resource_loader: ResourceLoader, location: Union[str, Path]) -> str:
        """加载文件内容

        Args:
            resource_loader: 资源加载器
            location: 资源位置

        Returns:
            文件内容字符串

        Raises:
            PropertySourceLoadError: 文件加载失败时抛出
        """
        try:
            # 检查文件是否存在
            if not resource_loader.exists(location):
                raise PropertySourceLoadError(location, f"File not found: {location}")

            # 加载文件内容
            content = resource_loader.load(location)

            # 验证内容
            if content is None:
                logger.warning(f"File content is None: {location}")
                content = ""

            return content

        except PropertySourceLoadError:
            raise
        except Exception as e:
            raise PropertySourceLoadError(location, f"Failed to load file content: {e}") from e

    def _process_data(self, data: Any, location: Union[str, Path]) -> Dict[str, Any]:
        """处理解析后的数据

        Args:
            data: 解析后的数据对象
            location: 文件位置(用于日志)

        Returns:
            处理后的扁平化数据字典
        """
        # 处理空数据
        if data is None:
            logger.debug(f"Parsed data is None for {location}, using empty dict")
            data = {}

        # 确保数据是字典类型
        if not isinstance(data, dict):
            logger.warning(f"Parsed data is not a dict for {location}, type: {type(data)}")
            # 尝试转换为字典
            if hasattr(data, "__dict__"):
                data = data.__dict__
            else:
                data = {"value": data}

        # 扁平化嵌套字典
        flattened_data = self._flatten_dict(data)

        logger.debug(f"Processed {len(flattened_data)} properties from {location}")
        return flattened_data

    def _create_property_sources(self, name: str, data: Dict[str, Any], location: Union[str, Path]) -> list[PropertySource]:
        """创建属性源列表

        Args:
            name: 属性源名称
            data: 处理后的数据字典
            location: 文件位置(用于日志)

        Returns:
            属性源列表
        """
        # 创建映射属性源
        property_source = MapPropertySource(name, data)

        # 设置源信息(用于调试)
        if hasattr(property_source, "_location"):
            property_source._location = str(location)

        return [property_source]

    def _flatten_dict(self, data: Dict[str, Any], parent_key: str = "", separator: str = ".") -> Dict[str, Any]:
        """将嵌套字典扁平化为点分隔的键

        Args:
            data: 要扁平化的字典
            parent_key: 父级键名
            separator: 键分隔符

        Returns:
            扁平化后的字典
        """
        items = []

        for key, value in data.items():
            new_key = f"{parent_key}{separator}{key}" if parent_key else key

            if isinstance(value, dict):
                # 递归处理嵌套字典
                items.extend(self._flatten_dict(value, new_key, separator).items())
            elif isinstance(value, list):
                # 处理列表,将索引作为键
                for i, item in enumerate(value):
                    list_key = f"{new_key}[{i}]"
                    if isinstance(item, dict):
                        items.extend(self._flatten_dict(item, list_key, separator).items())
                    else:
                        items.append((list_key, item))
                # 同时保存整个列表
                items.append((new_key, value))
            else:
                # 普通值
                items.append((new_key, value))

        return dict(items)

    def _log_success(self, name: str, location: Union[str, Path], data: Dict[str, Any]) -> None:
        """记录成功加载日志

        Args:
            name: 属性源名称
            location: 文件位置
            data: 加载的数据
        """
        property_count = len(data)
        file_format = self._get_format_name()

        logger.debug(f"Successfully loaded {file_format} property source '{name}' from {location} with {property_count} properties")

    def _handle_load_error(self, location: Union[str, Path], error: Exception) -> None:
        """统一异常处理

        Args:
            location: 文件位置
            error: 原始异常

        Raises:
            PropertySourceLoadError: 包装后的异常
        """
        file_format = self._get_format_name()

        # 如果已经是PropertySourceLoadError,直接重新抛出
        if isinstance(error, PropertySourceLoadError):
            logger.error(f"Failed to load {file_format} property source from {location}: {error}")
            raise error

        # 包装其他异常
        error_msg = f"Failed to load {file_format} property source: {error}"
        logger.error(f"Error loading {file_format} from {location}: {error}")

        raise PropertySourceLoadError(location, error_msg) from error

    def _get_format_name(self) -> str:
        """获取格式名称(用于日志)

        Returns:
            格式名称字符串
        """
        extensions = self.get_file_extensions()
        if extensions:
            return extensions[0].lstrip(".")
        return self.__class__.__name__.replace("PropertySourceLoader", "").lower()

    def get_cache_info(self) -> Dict[str, Any]:
        """获取加载器缓存信息(可选实现)

        Returns:
            缓存统计信息
        """
        return {
            "loader_type": self.__class__.__name__,
            "supported_extensions": self.get_file_extensions(),
            "format_name": self._get_format_name(),
        }
