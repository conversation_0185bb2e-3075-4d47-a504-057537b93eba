"""
Web 装饰器模块

提供 Web 开发相关的装饰器,包括控制器装饰器、请求映射装饰器和参数绑定装饰器.

主要功能:
- Controller - 控制器装饰器
- RestController - REST控制器装饰器
- RequestMapping - 请求映射装饰器
- GetMapping, PostMapping, PutMapping, DeleteMapping - HTTP方法映射装饰器
- RequestParam - 请求参数装饰器
- PathVariable - 路径变量装饰器
- RequestBody - 请求体装饰器
- RequestHeader - 请求头装饰器
- ResponseBody - 响应体装饰器
- ResponseStatus - 响应状态装饰器
"""

import re
from typing import Any, Optional


def _extract_path_params(path: str) -> list[str]:
    """提取路径参数

    从路径字符串中提取路径参数名称.
    例如: "/users/{user_id}/posts/{post_id}" -> ["user_id", "post_id"]

    Args:
        path: 路径字符串

    Returns:
        路径参数名称列表
    """
    if not path:
        return []

    # 匹配 {param_name} 格式的路径参数
    pattern = r"\{([^}]+)\}"
    matches = re.findall(pattern, path)
    return matches


def Controller(path: str = ""):
    """控制器装饰器

    标记一个类为控制器,用于处理Web请求.

    Args:
        path: 控制器基础路径

    Returns:
        装饰器函数

    Example:
        @Controller("/api")
        class UserController:
            pass
    """

    def decorator(cls):
        cls.__controller__ = True
        cls.__request_mapping__ = path
        return cls

    return decorator


def RestController(path: str = ""):
    """REST控制器装饰器

    标记一个类为REST控制器,自动处理JSON响应.

    Args:
        path: 控制器基础路径

    Returns:
        装饰器函数

    Example:
        @RestController("/api/users")
        class UserController:
            pass
    """

    def decorator(cls):
        cls.__rest_controller__ = True
        cls.__request_mapping__ = path
        return cls

    return decorator


def RequestMapping(
    path: str = "", method: str = "GET", produces: Optional[str] = None, consumes: Optional[str] = None, response_model: Optional[type] = None
):
    """请求映射装饰器

    将方法映射到特定的HTTP请求.

    Args:
        path: 请求路径
        method: HTTP方法
        produces: 响应内容类型
        consumes: 请求内容类型
        response_model: 响应模型类型

    Returns:
        装饰器函数

    Example:
        @RequestMapping("/users", method="POST")
        def create_user(self, user_data):
            pass
    """

    def decorator(func):
        func.__route_info__ = {
            "path": path,
            "method": method.upper(),
            "produces": produces,
            "consumes": consumes,
            "response_model": response_model,
            "path_params": _extract_path_params(path),
        }
        return func

    return decorator


def GetMapping(path: str = "", produces: Optional[str] = None, response_model: Optional[type] = None):
    """GET请求映射装饰器

    Args:
        path: 请求路径
        produces: 响应内容类型
        response_model: 响应模型类型

    Returns:
        装饰器函数

    Example:
        @GetMapping("/users/{user_id}")
        def get_user(self, user_id: int):
            pass
    """
    return RequestMapping(path=path, method="GET", produces=produces, response_model=response_model)


def PostMapping(path: str = "", consumes: Optional[str] = None, response_model: Optional[type] = None):
    """POST请求映射装饰器

    Args:
        path: 请求路径
        consumes: 请求内容类型
        response_model: 响应模型类型

    Returns:
        装饰器函数

    Example:
        @PostMapping("/users")
        def create_user(self, user_data: dict):
            pass
    """
    return RequestMapping(path=path, method="POST", consumes=consumes, response_model=response_model)


def PutMapping(path: str = "", consumes: Optional[str] = None, response_model: Optional[type] = None):
    """PUT请求映射装饰器

    Args:
        path: 请求路径
        consumes: 请求内容类型
        response_model: 响应模型类型

    Returns:
        装饰器函数

    Example:
        @PutMapping("/users/{user_id}")
        def update_user(self, user_id: int, user_data: dict):
            pass
    """
    return RequestMapping(path=path, method="PUT", consumes=consumes, response_model=response_model)


def DeleteMapping(path: str = "", response_model: Optional[type] = None):
    """DELETE请求映射装饰器

    Args:
        path: 请求路径
        response_model: 响应模型类型

    Returns:
        装饰器函数

    Example:
        @DeleteMapping("/users/{user_id}")
        def delete_user(self, user_id: int):
            pass
    """
    return RequestMapping(path=path, method="DELETE", response_model=response_model)


def PatchMapping(path: str = "", consumes: Optional[str] = None, response_model: Optional[type] = None):
    """PATCH请求映射装饰器

    Args:
        path: 请求路径
        consumes: 请求内容类型
        response_model: 响应模型类型

    Returns:
        装饰器函数

    Example:
        @PatchMapping("/users/{user_id}")
        def patch_user(self, user_id: int, patch_data: dict):
            pass
    """
    return RequestMapping(path=path, method="PATCH", consumes=consumes, response_model=response_model)


# 检查函数
def is_controller(cls: type) -> bool:
    """检查类是否为控制器

    Args:
        cls: 要检查的类

    Returns:
        是否为控制器
    """
    return hasattr(cls, "__controller__") or hasattr(cls, "__rest_controller__")


def is_rest_controller(cls: type) -> bool:
    """检查类是否为REST控制器

    Args:
        cls: 要检查的类

    Returns:
        是否为REST控制器
    """
    return hasattr(cls, "__rest_controller__")


def controller_path(cls: type) -> str:
    """获取控制器路径

    Args:
        cls: 控制器类

    Returns:
        控制器路径
    """
    return getattr(cls, "__request_mapping__", "")


def has_route(method) -> bool:
    """检查方法是否有路由映射

    Args:
        method: 要检查的方法

    Returns:
        是否有路由映射
    """
    return hasattr(method, "__route_info__")


def route_info(method) -> Optional[dict[str, Any]]:
    """获取路由信息

    Args:
        method: 方法对象

    Returns:
        路由信息字典,如果没有则返回None
    """
    return getattr(method, "__route_info__", None)


# Web参数绑定装饰器
def RequestParam(name: Optional[str] = None, required: bool = True, default_value: Any = None, description: Optional[str] = None):
    """请求参数装饰器

    标记方法参数为请求参数(查询参数).

    Args:
        name: 参数名称,如果为None则使用方法参数名
        required: 是否必需
        default_value: 默认值
        description: 参数描述

    Returns:
        装饰器函数

    Example:
        @GetMapping("/users")
        def get_users(self, @RequestParam("page") page: int = 1):
            pass
    """

    def decorator(func):
        if not hasattr(func, "__request_params__"):
            func.__request_params__ = []

        func.__request_params__.append({
            "name": name,
            "required": required,
            "default_value": default_value,
            "description": description,
        })
        return func

    return decorator


def PathVariable(name: Optional[str] = None, description: Optional[str] = None):
    """路径变量装饰器

    标记方法参数为路径变量.

    Args:
        name: 变量名称,如果为None则使用方法参数名
        description: 变量描述

    Returns:
        装饰器函数

    Example:
        @GetMapping("/users/{user_id}")
        def get_user(self, @PathVariable("user_id") user_id: int):
            pass
    """

    def decorator(func):
        if not hasattr(func, "__path_variables__"):
            func.__path_variables__ = []

        func.__path_variables__.append({
            "name": name,
            "description": description,
        })
        return func

    return decorator


def RequestBody(description: Optional[str] = None, required: bool = True):
    """请求体装饰器

    标记方法参数为请求体.

    Args:
        description: 请求体描述
        required: 是否必需

    Returns:
        装饰器函数

    Example:
        @PostMapping("/users")
        def create_user(self, @RequestBody user_data: dict):
            pass
    """

    def decorator(func):
        func.__request_body__ = {
            "description": description,
            "required": required,
        }
        return func

    return decorator


def RequestHeader(name: Optional[str] = None, required: bool = True, default_value: Any = None, description: Optional[str] = None):
    """请求头装饰器

    标记方法参数为请求头.

    Args:
        name: 请求头名称,如果为None则使用方法参数名
        required: 是否必需
        default_value: 默认值
        description: 请求头描述

    Returns:
        装饰器函数

    Example:
        @GetMapping("/users")
        def get_users(self, @RequestHeader("Authorization") auth: str):
            pass
    """

    def decorator(func):
        if not hasattr(func, "__request_headers__"):
            func.__request_headers__ = []

        func.__request_headers__.append({
            "name": name,
            "required": required,
            "default_value": default_value,
            "description": description,
        })
        return func

    return decorator


def ResponseBody(description: Optional[str] = None):
    """响应体装饰器

    标记方法返回响应体.

    Args:
        description: 响应体描述

    Returns:
        装饰器函数

    Example:
        @GetMapping("/users")
        @ResponseBody("用户列表")
        def get_users(self):
            pass
    """

    def decorator(func):
        func.__response_body__ = {
            "description": description,
        }
        return func

    return decorator


def ResponseStatus(code: int, reason: Optional[str] = None):
    """响应状态装饰器

    设置方法的响应状态码.

    Args:
        code: HTTP状态码
        reason: 状态原因

    Returns:
        装饰器函数

    Example:
        @PostMapping("/users")
        @ResponseStatus(201, "Created")
        def create_user(self, user_data: dict):
            pass
    """

    def decorator(func):
        func.__response_status__ = {
            "code": code,
            "reason": reason,
        }
        return func

    return decorator


# 参数绑定检查函数
def has_params(method) -> bool:
    """检查方法是否有请求参数

    Args:
        method: 要检查的方法

    Returns:
        是否有请求参数
    """
    return hasattr(method, "__request_params__")


def request_params(method) -> list[dict[str, Any]]:
    """获取请求参数信息

    Args:
        method: 方法对象

    Returns:
        请求参数信息列表
    """
    return getattr(method, "__request_params__", [])


def has_path_vars(method) -> bool:
    """检查方法是否有路径变量

    Args:
        method: 要检查的方法

    Returns:
        是否有路径变量
    """
    return hasattr(method, "__path_variables__")


def path_variables(method) -> list[dict[str, Any]]:
    """获取路径变量信息

    Args:
        method: 方法对象

    Returns:
        路径变量信息列表
    """
    return getattr(method, "__path_variables__", [])


def has_body(method) -> bool:
    """检查方法是否有请求体

    Args:
        method: 要检查的方法

    Returns:
        是否有请求体
    """
    return hasattr(method, "__request_body__")


def request_body(method) -> Optional[dict[str, Any]]:
    """获取请求体信息

    Args:
        method: 方法对象

    Returns:
        请求体信息,如果没有则返回None
    """
    return getattr(method, "__request_body__", None)


def has_headers(method) -> bool:
    """检查方法是否有请求头

    Args:
        method: 要检查的方法

    Returns:
        是否有请求头
    """
    return hasattr(method, "__request_headers__")


def request_headers(method) -> list[dict[str, Any]]:
    """获取请求头信息

    Args:
        method: 方法对象

    Returns:
        请求头信息列表
    """
    return getattr(method, "__request_headers__", [])


def has_response(method) -> bool:
    """检查方法是否有响应体

    Args:
        method: 要检查的方法

    Returns:
        是否有响应体
    """
    return hasattr(method, "__response_body__")


def response_body(method) -> Optional[dict[str, Any]]:
    """获取响应体信息

    Args:
        method: 方法对象

    Returns:
        响应体信息,如果没有则返回None
    """
    return getattr(method, "__response_body__", None)


def has_status(method) -> bool:
    """检查方法是否有响应状态

    Args:
        method: 要检查的方法

    Returns:
        是否有响应状态
    """
    return hasattr(method, "__response_status__")


def response_status(method) -> Optional[dict[str, Any]]:
    """获取响应状态信息

    Args:
        method: 方法对象

    Returns:
        响应状态信息,如果没有则返回None
    """
    return getattr(method, "__response_status__", None)
