{"test_suite": "ComprehensivePerformanceTestSuite", "total_tests": 1, "passed_tests": 1, "failed_tests": 0, "total_duration": 0.005984067916870117, "benchmarks": [{"test_name": "concurrent_operations", "operations_count": 1000, "total_time": 0.005984067916870117, "avg_time_per_operation": 5.984067916870117e-06, "memory_usage_mb": 0.078125, "cpu_usage_percent": 0.0, "throughput_ops_per_sec": 167110.40280489263, "timestamp": "2025-07-18T10:57:16.256334"}], "system_info": {"cpu_count": 20, "memory_total_gb": 63.77785873413086, "python_version": "<bound method Process.exe of psutil.Process(pid=21036, name='python.exe', status='running', started='10:57:09')>", "platform": "nt"}, "timestamp": "2025-07-18T10:57:16.261321"}