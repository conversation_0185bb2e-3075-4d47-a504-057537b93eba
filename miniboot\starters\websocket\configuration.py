#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket Starter 自动配置类
"""

from typing import Optional

from loguru import logger

from miniboot.annotations import (Autowired, Bean, ConditionalOnMissingBean,
                                  ConditionalOnProperty, Configuration)
from miniboot.autoconfigure import AutoConfigurationMetadata
from miniboot.autoconfigure.base import StarterAutoConfiguration
from miniboot.context import ApplicationContext

from .compression import WebSocketMessageCompressor
from .handlers import (WebSocketAuthHandler, WebSocketConnectionHandler,
                       WebSocketMessageHandler)
from .health import WebSocketHealthIndicator
from .metrics import WebSocketMetrics
from .pool import PoolConfiguration, WebSocketConnectionPool
from .properties import WebSocketProperties
from .service import WebSocketService
from .session import WebSocketSessionManager


@Configuration
@ConditionalOnProperty(prefix="miniboot.starters.websocket", name="enabled", having_value="true", match_if_missing=True)
class WebSocketAutoConfiguration(StarterAutoConfiguration):
    """WebSocket Starter 自动配置类

    当 miniboot.starters.websocket.enabled=true 时自动配置 WebSocket 功能.
    提供完整的 WebSocket 实时通信支持,包括连接管理、消息处理、认证授权等功能.
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据

        Returns:
            AutoConfigurationMetadata: 配置元数据
        """
        return AutoConfigurationMetadata(
            name="websocket-auto-configuration",
            description="WebSocket 实时通信功能自动配置",
            priority=150,  # 中等优先级,在基础设施之后,业务功能之前
            auto_configure_after=["web-auto-configuration"],  # 在 Web 配置之后
        )

    def get_starter_name(self) -> str:
        """获取 Starter 名称

        Returns:
            str: Starter 名称
        """
        return "miniboot-starter-websocket"

    def get_starter_version(self) -> str:
        """获取 Starter 版本

        Returns:
            str: Starter 版本
        """
        return "1.0.0"

    def get_starter_description(self) -> str:
        """获取 Starter 描述

        Returns:
            str: Starter 描述
        """
        return "Mini-Boot WebSocket 实时通信功能 Starter,提供基于 FastAPI WebSocket 的企业级实时通信解决方案"

    @Bean
    @ConditionalOnMissingBean(WebSocketProperties)
    def websocket_properties(self) -> WebSocketProperties:
        """WebSocket 配置属性 Bean

        Returns:
            WebSocketProperties: WebSocket 配置属性实例
        """
        logger.debug("Creating WebSocketProperties bean")
        return WebSocketProperties()

    @Bean
    @ConditionalOnMissingBean(WebSocketSessionManager)
    def websocket_session_manager(self, @Autowired properties: WebSocketProperties) -> WebSocketSessionManager:
        """WebSocket 会话管理器 Bean

        Args:
            properties: WebSocket 配置属性

        Returns:
            WebSocketSessionManager: 会话管理器实例
        """
        try:
            logger.debug("Creating WebSocketSessionManager bean")
            return WebSocketSessionManager()
        except Exception as e:
            logger.error(f"Failed to create WebSocketSessionManager: {e}")
            # 创建一个默认的会话管理器
            return WebSocketSessionManager()

    @Bean
    @ConditionalOnMissingBean(WebSocketAuthHandler)
    def websocket_auth_handler(self, @Autowired properties: WebSocketProperties) -> WebSocketAuthHandler:
        """WebSocket 认证处理器 Bean

        Args:
            properties: WebSocket 配置属性

        Returns:
            WebSocketAuthHandler: 认证处理器实例
        """
        try:
            logger.debug("Creating WebSocketAuthHandler bean")
            return WebSocketAuthHandler(properties.security)
        except Exception as e:
            logger.error(f"Failed to create WebSocketAuthHandler: {e}")
            # 创建一个默认的安全配置
            from .properties import SecurityConfig
            default_security = SecurityConfig()
            return WebSocketAuthHandler(default_security)

    @Bean
    @ConditionalOnMissingBean(WebSocketConnectionHandler)
    def websocket_connection_handler(self) -> WebSocketConnectionHandler:
        """WebSocket 连接处理器 Bean

        Returns:
            WebSocketConnectionHandler: 连接处理器实例
        """
        logger.debug("Creating WebSocketConnectionHandler bean")
        return WebSocketConnectionHandler()

    @Bean
    @ConditionalOnMissingBean(WebSocketMessageHandler)
    def websocket_message_handler(self) -> WebSocketMessageHandler:
        """WebSocket 消息处理器 Bean

        Returns:
            WebSocketMessageHandler: 消息处理器实例
        """
        logger.debug("Creating WebSocketMessageHandler bean")
        return WebSocketMessageHandler()

    @Bean
    @ConditionalOnMissingBean(WebSocketMetrics)
    def websocket_metrics(self) -> WebSocketMetrics:
        """WebSocket 指标收集器 Bean

        Returns:
            WebSocketMetrics: 指标收集器实例
        """
        logger.debug("Creating WebSocketMetrics bean")
        return WebSocketMetrics()

    @Bean
    @ConditionalOnMissingBean(WebSocketHealthIndicator)
    def websocket_health_indicator(self, @Autowired(required=False) websocket_metrics: Optional[WebSocketMetrics] = None) -> WebSocketHealthIndicator:
        """WebSocket 健康检查指示器 Bean

        Args:
            websocket_metrics: WebSocket 指标收集器

        Returns:
            WebSocketHealthIndicator: 健康检查指示器实例
        """
        logger.debug("Creating WebSocketHealthIndicator bean")
        return WebSocketHealthIndicator(None, websocket_metrics)

    @Bean
    @ConditionalOnMissingBean(WebSocketConnectionPool)
    def websocket_connection_pool(self, @Autowired properties: WebSocketProperties) -> WebSocketConnectionPool:
        """WebSocket 连接池 Bean

        Args:
            properties: WebSocket 配置属性

        Returns:
            WebSocketConnectionPool: 连接池实例
        """
        try:
            logger.debug("Creating WebSocketConnectionPool bean")

            # 根据配置创建连接池配置
            pool_config = PoolConfiguration(
                min_connections=5,
                max_connections=properties.connection_limit.max_total_connections if properties.connection_limit.enabled else 50,
                max_idle_time_seconds=properties.server.idle_timeout,
                connection_timeout_seconds=properties.server.read_timeout,
                enable_connection_validation=True,
                enable_connection_recycling=True,
            )

            return WebSocketConnectionPool(pool_config)
        except Exception as e:
            logger.error(f"Failed to create WebSocketConnectionPool: {e}")
            # 创建一个默认的连接池配置
            default_config = PoolConfiguration()
            return WebSocketConnectionPool(default_config)

    @Bean
    @ConditionalOnMissingBean(WebSocketMessageCompressor)
    def websocket_message_compressor(self, @Autowired properties: WebSocketProperties) -> WebSocketMessageCompressor:
        """WebSocket 消息压缩器 Bean

        Args:
            properties: WebSocket 配置属性

        Returns:
            WebSocketMessageCompressor: 消息压缩器实例
        """
        try:
            logger.debug("Creating WebSocketMessageCompressor bean")

            compressor = WebSocketMessageCompressor()

            # 根据配置设置压缩器
            if properties.compression.enabled:
                compressor.configure(
                    default_level=properties.compression.level,
                    enable_adaptive_compression=True,
                    min_compress_size=1024,  # 1KB
                    max_compress_size=properties.max_message_size,
                )
            else:
                # 禁用压缩
                compressor.configure(
                    min_compress_size=properties.max_message_size + 1  # 永远不压缩
                )

            return compressor
        except Exception as e:
            logger.error(f"Failed to create WebSocketMessageCompressor: {e}")
            # 创建一个默认的压缩器
            return WebSocketMessageCompressor()

    @Bean
    @ConditionalOnMissingBean(WebSocketService)
    def websocket_service(
        self,
        @Autowired properties: WebSocketProperties,
        @Autowired(required=False) session_manager: Optional[WebSocketSessionManager] = None,
        @Autowired(required=False) auth_handler: Optional[WebSocketAuthHandler] = None,
        @Autowired(required=False) connection_handler: Optional[WebSocketConnectionHandler] = None,
        @Autowired(required=False) message_handler: Optional[WebSocketMessageHandler] = None,
        @Autowired(required=False) metrics: Optional[WebSocketMetrics] = None,
        @Autowired(required=False) connection_pool: Optional[WebSocketConnectionPool] = None,
        @Autowired(required=False) message_compressor: Optional[WebSocketMessageCompressor] = None,
    ) -> WebSocketService:
        """WebSocket 核心服务 Bean

        Args:
            properties: WebSocket 配置属性
            session_manager: 会话管理器
            auth_handler: 认证处理器
            connection_handler: 连接处理器
            message_handler: 消息处理器
            metrics: 指标收集器
            connection_pool: 连接池(可选)
            message_compressor: 消息压缩器(可选)

        Returns:
            WebSocketService: WebSocket 核心服务实例
        """
        try:
            logger.debug("Creating WebSocketService bean")

            # 创建服务实例，使用默认值处理可选依赖
            service = WebSocketService(
                properties,
                metrics or WebSocketMetrics(),
                connection_pool,
                message_compressor
            )

            # 注入依赖组件(如果可用)
            if session_manager:
                service.session_manager = session_manager
            if auth_handler:
                service.auth_handler = auth_handler
            if connection_handler:
                service.connection_handler = connection_handler
            if message_handler:
                service.message_handler = message_handler

            # 初始化组件
            service.initialize_components()

            return service
        except Exception as e:
            logger.error(f"Failed to create WebSocketService: {e}")
            # 创建一个最小化的服务实例
            service = WebSocketService(properties, WebSocketMetrics(), None, None)
            service.initialize_components()
            return service

    def _initialize_starter(self, context: ApplicationContext) -> None:
        """初始化 Starter 特定逻辑

        Args:
            context: 应用上下文
        """
        try:
            logger.info("Initializing WebSocket Starter...")

            # 获取 WebSocket 服务
            websocket_service = context.get_bean(WebSocketService)
            if not websocket_service:
                logger.warning("WebSocketService bean not found, skipping initialization")
                return

            # 获取配置属性
            properties = context.get_bean(WebSocketProperties)
            if not properties:
                logger.warning("WebSocketProperties bean not found, using default configuration")
                properties = WebSocketProperties()

            # 验证配置
            try:
                properties.validate()
                logger.debug("WebSocket configuration validated successfully")
            except Exception as e:
                logger.error(f"WebSocket configuration validation failed: {e}")
                raise

            # 初始化服务
            # 注意:这里不能直接调用 async 方法,需要在实际使用时初始化
            logger.debug("WebSocket service will be initialized on first use")

            # 注册 WebSocket 控制器(如果有的话)
            self._register_websocket_controllers(context, websocket_service)

            logger.info("WebSocket Starter initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize WebSocket Starter: {e}")
            raise

    def _register_websocket_controllers(self, context: ApplicationContext, websocket_service: WebSocketService) -> None:
        """注册 WebSocket 控制器

        Args:
            context: 应用上下文
            websocket_service: WebSocket 服务
        """
        try:
            from .annotations import (get_websocket_path,
                                      is_websocket_controller)

            # 获取所有 Bean
            bean_names = context.get_bean_names()
            controller_count = 0

            for bean_name in bean_names:
                try:
                    bean = context.get_bean(bean_name)
                    if bean and is_websocket_controller(bean.__class__):
                        # 获取 WebSocket 路径
                        path = get_websocket_path(bean.__class__)
                        if not path:
                            path = f"/{bean_name}"  # 使用 Bean 名称作为默认路径

                        # 注册控制器
                        websocket_service.register_controller(path, bean)
                        controller_count += 1

                        logger.info(f"Registered WebSocket controller: {bean.__class__.__name__} -> {path}")

                except Exception as e:
                    logger.warning(f"Failed to register WebSocket controller for bean {bean_name}: {e}")

            if controller_count > 0:
                logger.info(f"Registered {controller_count} WebSocket controllers")
            else:
                logger.debug("No WebSocket controllers found")

        except Exception as e:
            logger.error(f"Error registering WebSocket controllers: {e}")

    def get_configuration_properties_classes(self) -> list[type]:
        """获取配置属性类列表

        Returns:
            list[type]: 配置属性类列表
        """
        return [WebSocketProperties]

    def should_configure(self, context: ApplicationContext) -> bool:
        """判断是否应该执行配置

        Args:
            context: 应用上下文

        Returns:
            bool: 是否应该配置
        """
        # 调用父类的条件检查
        if not super().should_configure(context):
            return False

        # 检查是否有 FastAPI 支持(可选)
        try:
            import fastapi  # noqa: F401

            logger.debug("FastAPI is available for WebSocket support")
            return True
        except ImportError:
            logger.warning("FastAPI is not available, WebSocket functionality may be limited")
            return False
