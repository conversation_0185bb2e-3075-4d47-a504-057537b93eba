# Mini-Boot 示例项目规划文档

## 1. 概述

本文档规划了 mini-boot 框架的完整示例体系，旨在为用户提供从入门到精通的渐进式学习路径。所有示例都将放置在`samples/`目录下，每个示例都是独立可运行的完整应用。

## 2. 示例分类和学习路径

### 2.1 基础示例 (Foundation Examples)

**学习目标**: 掌握 mini-boot 的核心概念和基本用法
**适用人群**: 框架新手、Python 初学者

### 2.2 进阶示例 (Advanced Examples)

**学习目标**: 学习复杂场景下的框架使用和最佳实践
**适用人群**: 有一定经验的开发者

### 2.3 实战示例 (Real-world Examples)

**学习目标**: 构建完整的业务应用，展示企业级开发模式
**适用人群**: 准备在生产环境使用的开发者

## 3. 详细示例规划

### 3.1 基础示例

#### 3.1.1 Hello World (`samples/01-hello-world/`)

**优先级**: P0 (最高)
**预估工作量**: 0.5 天
**学习目标**:

-   理解 mini-boot 的基本概念
-   学会创建最简单的应用
-   掌握基本的配置和启动流程

**涵盖功能点**:

-   应用上下文创建
-   基本 Bean 定义和获取
-   简单配置文件使用

**目录结构**:

```
samples/01-hello-world/
├── README.md
├── main.py
├── resources/
│   └── application.yml
└── requirements.txt
```

#### 3.1.2 依赖注入基础 (`samples/02-dependency-injection/`)

**优先级**: P0
**预估工作量**: 1 天
**学习目标**:

-   掌握@Autowired 注解的使用
-   理解依赖注入的基本原理
-   学会处理循环依赖

**涵盖功能点**:

-   字段注入、构造函数注入、方法注入
-   按类型注入和按名称注入
-   可选依赖和默认值处理

#### 3.1.3 配置管理 (`samples/03-configuration/`)

**优先级**: P0
**预估工作量**: 1 天
**学习目标**:

-   掌握@Value 注解的使用
-   学会配置文件的组织和管理
-   理解配置优先级和环境切换

**涵盖功能点**:

-   属性注入和类型转换
-   配置文件加载（properties、yaml、json）
-   环境变量和命令行参数
-   Profile 机制

#### 3.1.4 事件系统基础 (`samples/04-events/`)

**优先级**: P1
**预估工作量**: 1 天
**学习目标**:

-   理解事件驱动编程模式
-   掌握事件发布和监听
-   学会异步事件处理

**涵盖功能点**:

-   事件定义和发布
-   事件监听器注册
-   同步和异步事件处理
-   事件过滤和条件监听

### 3.2 进阶示例

#### 3.2.1 自动配置开发 (`samples/05-auto-configuration/`)

**优先级**: P1
**预估工作量**: 2 天
**学习目标**:

-   学会开发自定义自动配置
-   理解条件注解的使用
-   掌握配置类的组织方式

**涵盖功能点**:

-   @ConditionalOnProperty 等条件注解
-   自动配置类的编写
-   配置属性绑定
-   自动配置的测试

#### 3.2.2 Bean 生命周期管理 (`samples/06-bean-lifecycle/`)

**优先级**: P1
**预估工作量**: 1.5 天
**学习目标**:

-   深入理解 Bean 的生命周期
-   掌握各种处理器的使用
-   学会自定义 Bean 处理逻辑

**涵盖功能点**:

-   BeanPostProcessor 的实现
-   初始化和销毁回调
-   作用域管理
-   代理和 AOP 基础

#### 3.2.3 Starters 使用和开发 (`samples/07-starters/`)

**优先级**: P1
**预估工作量**: 2 天
**学习目标**:

-   学会使用内置 starters
-   掌握自定义 starter 的开发
-   理解 starter 的自动配置机制

**涵盖功能点**:

-   使用内置 starters（web-starter、data-starter、audio-starter 等）
-   自定义 starter 开发（redis-starter 示例）
-   音频播放 starter 使用和配置
-   自动配置类编写
-   starter 配置属性绑定
-   条件注解使用
-   starter 打包和发布

**技术栈**:

-   mini-boot starters
-   mini-boot-starter-audio
-   自动配置机制
-   条件注解
-   配置属性绑定

#### 3.2.4 音频播放功能 (`samples/08-audio-starter/`)

**优先级**: P1
**预估工作量**: 1 天
**学习目标**:

-   学会使用音频播放 Starter
-   掌握 TTS 文本转语音功能
-   理解音频文件播放和管理
-   学会音频功能的配置和参数调整

**涵盖功能点**:

-   音频播放 Starter 集成和配置
-   TTS 文本转语音播放
-   音频文件播放（MP3、WAV 等格式）
-   混合播放序列（音频文件+文本语音）
-   语音参数动态调整（语速、音量）
-   通知服务场景应用
-   音频功能状态检查和控制

**技术栈**:

-   mini-boot-starter-audio
-   pyttsx3 (TTS 引擎)
-   pygame (音频播放，可选)
-   中文语音合成

**目录结构**:

```
samples/08-audio-starter/
├── README.md                 # 项目文档
├── main.py                  # 应用入口
├── requirements.txt          # Python依赖
├── resources/               # 配置文件目录
│   ├── application.yml
│   ├── application-dev.yml
│   └── application-prod.yml
├── audio_files/             # 示例音频文件
│   ├── notification.mp3
│   ├── success.wav
│   └── alert.ogg
└── examples/                # 功能演示脚本
    ├── basic_tts_demo.py
    ├── audio_player_demo.py
    ├── notification_service.py
    └── mixed_sequence_demo.py
```

**核心代码示例**:

```python
from miniboot import Application
from miniboot.annotations import Component, Autowired
from miniboot.starters.audio import AudioService, AudioItem

@Component
class NotificationService:
    """通知服务 - 演示音频播放功能"""

    @Autowired
    def set_audio_service(self, audio_service: AudioService):
        self.audio_service = audio_service

    async def play_welcome_message(self):
        """播放欢迎消息"""
        await self.audio_service.speak("欢迎使用Mini-Boot音频播放功能")

    async def play_notification_sequence(self):
        """播放通知序列"""
        sequence = [
            "audio_files/notification.mp3",  # 音频文件
            "您有新的消息",                    # 文本语音
            "请及时查看"                      # 文本语音
        ]
        await self.audio_service.play_sequence(sequence, interval=0.5)

    async def play_system_status(self):
        """播放系统状态"""
        # 使用AudioItem明确指定类型
        status_items = [
            AudioItem.file("audio_files/success.wav"),
            AudioItem.text("系统运行正常"),
            AudioItem.text("所有服务已启动"),
            AudioItem.text("准备接收请求")
        ]
        await self.audio_service.play_sequence(status_items, interval=0.3)

# 应用启动
async def main():
    app = Application()
    await app.start()

    notification_service = app.get_context().get_bean(NotificationService)

    # 演示各种音频功能
    await notification_service.play_welcome_message()
    await notification_service.play_notification_sequence()
    await notification_service.play_system_status()

    await app.stop()

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
```

**配置示例**:

```yaml
# application.yml
miniboot:
  audio:
    enabled: true
    player:
      enabled: true
      volume: 1.0
      timeout_seconds: 30
      supported_formats: ['.mp3', '.wav', '.m4a', '.ogg']
    tts:
      enabled: true
      engine: "pyttsx3"
      language: "zh-CN"
      rate: 150
      volume: 1.0

# application-dev.yml (开发环境)
miniboot:
  audio:
    tts:
      rate: 120  # 开发环境语速较慢，便于调试

# application-prod.yml (生产环境)
miniboot:
  audio:
    tts:
      rate: 180  # 生产环境语速较快
```

#### 3.2.5 多模块应用 (`samples/09-multi-module/`)

**优先级**: P2
**预估工作量**: 1.5 天
**学习目标**:

-   学会组织大型应用的模块结构
-   掌握模块间的依赖管理
-   理解包扫描和组件发现

**涵盖功能点**:

-   模块化设计
-   包扫描配置
-   模块间通信
-   配置文件组织

### 3.3 核心特性示例

#### 3.3.1 智能异步支持 (`samples/15-smart-async-context/`)

**优先级**: P0 (最高)
**预估工作量**: 2 天
**学习目标**:

-   掌握 SmartApplicationContext 的智能异步适配功能
-   理解 AsyncEnvironmentDetector 的环境检测机制
-   学会透明的同步/异步切换
-   掌握异步 Bean 生命周期管理

**涵盖功能点**:

-   智能异步环境检测和适配
-   透明的同步/异步 Bean 创建
-   异步事件处理和监听
-   异步任务执行和管理
-   混合同步/异步场景处理

**技术栈**:

-   SmartApplicationContext
-   AsyncEnvironmentDetector
-   @Async 注解
-   asyncio 协程
-   异步事件系统

**目录结构**:

```
samples/15-smart-async-context/
├── README.md                 # 项目文档
├── main.py                  # 应用入口
├── requirements.txt          # Python依赖
├── resources/               # 配置文件目录
│   ├── application.yml
│   ├── application-dev.yml
│   └── application-prod.yml
└── examples/                # 功能演示脚本
    ├── async_detection_demo.py
    ├── mixed_sync_async_demo.py
    └── performance_comparison.py
```

**技术栈**:

-   SmartApplicationContext
-   AsyncEnvironmentDetector
-   @Async 注解
-   asyncio 协程
-   异步事件系统

#### 3.3.2 三级缓存和循环依赖 (`samples/16-circular-dependency/`)

**优先级**: P0 (最高)
**预估工作量**: 2 天
**学习目标**:

-   深入理解 Bean 工厂的三级缓存机制
-   掌握循环依赖的创建和解决过程
-   学会 DependencyGraph 依赖分析
-   理解 Bean 创建状态跟踪

**涵盖功能点**:

-   三级缓存机制详解
-   循环依赖检测和解决
-   Bean 创建状态管理
-   依赖关系图分析
-   复杂依赖场景处理

**技术栈**:

-   DefaultBeanFactory
-   DependencyGraph
-   三级缓存机制
-   循环依赖解决算法

#### 3.3.3 注解系统全覆盖 (`samples/17-comprehensive-annotations/`)

**优先级**: P0 (最高)
**预估工作量**: 3 天
**学习目标**:

-   掌握 Mini-Boot 框架的 34 个注解系统
-   理解各类注解的使用场景和最佳实践
-   学会注解组合和高级用法
-   掌握自定义注解开发

**涵盖功能点**:

-   核心注解：@Component、@Service、@Repository、@Configuration
-   依赖注入：@Autowired、@Qualifier、@Primary、@Lazy
-   配置注解：@Value、@ConfigurationProperties、@Profile
-   Web 注解：@RestController、@RequestMapping、@ResponseBody
-   生命周期：@PostConstruct、@PreDestroy、@EventListener
-   定时任务：@Scheduled、@EnableScheduling
-   异步处理：@Async、@EnableAsync
-   条件注解：@ConditionalOnProperty、@ConditionalOnClass

**技术栈**:

-   34 个注解系统
-   注解处理器机制
-   元数据提取和解析
-   注解组合和继承

#### 3.3.4 多环境配置管理 (`samples/18-multi-environment-config/`)

**优先级**: P1
**预估工作量**: 2 天
**学习目标**:

-   掌握 Profile 机制的深度使用
-   理解配置优先级和覆盖策略
-   学会占位符解析和属性绑定
-   掌握配置热重载和动态更新

**涵盖功能点**:

-   Profile 激活和切换机制
-   配置文件分层和优先级
-   占位符解析和嵌套引用
-   环境变量和命令行参数
-   配置属性验证和类型转换
-   动态配置更新和热重载

**技术栈**:

-   StandardEnvironment
-   ConfigurationLoader
-   PropertyResolver
-   @Profile 注解
-   @ConfigurationProperties

#### 3.3.5 异步处理和线程池 (`samples/19-async-processing/`)

**优先级**: P1
**预估工作量**: 2 天
**学习目标**:

-   掌握 @Async 注解的高级用法
-   理解自定义线程池配置和管理
-   学会异步任务监控和异常处理
-   掌握异步执行器的优化策略

**涵盖功能点**:

-   @Async 注解和异步方法调用
-   自定义线程池配置
-   异步任务结果处理
-   异步异常处理和回调
-   线程池监控和性能调优
-   异步事务管理

**技术栈**:

-   @Async 注解
-   ThreadPoolTaskExecutor
-   AsyncExecutor
-   concurrent.futures
-   异步监控和统计

#### 3.3.6 事件系统高级用法 (`samples/20-advanced-events/`)

**优先级**: P1
**预估工作量**: 2 天
**学习目标**:

-   掌握事件继承和多态处理
-   理解条件事件监听和过滤
-   学会事件转换和链式处理
-   掌握分布式事件处理机制

**涵盖功能点**:

-   自定义事件类型和继承
-   条件事件监听器
-   事件过滤和转换
-   异步事件处理链
-   事件优先级和顺序
-   事件持久化和重放

#### 3.3.7 性能优化和监控 (`samples/21-performance-optimization/`)

**优先级**: P2
**预估工作量**: 2.5 天
**学习目标**:

-   掌握 Bean 缓存策略优化
-   理解内存使用监控和分析
-   学会启动时间优化技巧
-   掌握运行时性能分析方法

**涵盖功能点**:

-   Bean 创建性能优化
-   内存泄漏检测和预防
-   启动时间分析和优化
-   运行时性能监控
-   缓存策略和命中率优化
-   GC 调优和内存管理

**技术栈**:

-   Actuator 监控指标
-   psutil 系统监控
-   gc 垃圾回收分析
-   @Lazy 延迟初始化
-   性能分析工具

#### 3.3.8 自定义扩展开发 (`samples/22-custom-extensions/`)

**优先级**: P2
**预估工作量**: 3 天
**学习目标**:

-   掌握自定义 BeanPostProcessor 开发
-   理解自定义 PropertySource 实现
-   学会自定义 Actuator 端点开发
-   掌握自定义注解的设计和实现

**涵盖功能点**:

-   自定义 Bean 后置处理器
-   自定义配置源和属性解析器
-   自定义健康检查指示器
-   自定义监控端点
-   自定义注解处理器
-   框架扩展点和 SPI 机制

#### 3.3.9 测试策略和 Mock (`samples/23-testing-strategies/`)

**优先级**: P2
**预估工作量**: 2 天
**学习目标**:

-   掌握 Mini-Boot 应用的完整测试策略
-   理解单元测试和集成测试的最佳实践
-   学会 Mock 和 Stub 的使用技巧
-   掌握测试环境配置和数据准备

**涵盖功能点**:

-   单元测试设计和实现
-   集成测试和端到端测试
-   Mock 对象和测试替身
-   测试数据管理和清理
-   测试覆盖率分析
-   性能测试和压力测试

**技术栈**:

-   unittest 测试框架
-   unittest.mock Mock 库
-   测试配置和环境隔离
-   测试数据工厂模式

#### 3.3.10 错误处理和异常管理 (`samples/24-error-handling/`)

**优先级**: P2
**预估工作量**: 2 天
**学习目标**:

-   掌握企业级错误处理策略
-   理解全局异常处理器的设计
-   学会自定义异常体系的构建
-   掌握错误日志和监控集成

**涵盖功能点**:

-   全局异常处理器设计
-   自定义异常类型体系
-   错误日志记录和分析
-   异常监控和告警
-   优雅降级和容错机制
-   错误恢复和重试策略

**技术栈**:

-   Python 异常处理机制
-   日志记录和分析
-   监控集成和告警
-   容错和降级策略

### 3.4 实战示例

#### 3.4.1 Web 服务应用 (`samples/10-web-service/`)

**优先级**: P1
**预估工作量**: 3 天
**学习目标**:

-   构建完整的 Web API 服务
-   集成 HTTP 服务器
-   实现 RESTful 接口

**涵盖功能点**:

-   HTTP 服务器集成（如 Flask、FastAPI）
-   路由和控制器
-   请求处理和响应
-   中间件和过滤器
-   异常处理

**技术栈**:

-   mini-boot + FastAPI
-   数据验证和序列化
-   日志记录

#### 3.3.2 数据访问层 (`samples/11-data-access/`)

**优先级**: P1
**预估工作量**: 3 天
**学习目标**:

-   集成数据库访问
-   实现数据访问层模式
-   掌握事务管理

**涵盖功能点**:

-   数据库连接池管理
-   ORM 集成（SQLAlchemy）
-   Repository 模式实现
-   事务管理
-   数据库迁移

**技术栈**:

-   mini-boot + SQLAlchemy
-   PostgreSQL/MySQL
-   Alembic 迁移工具

#### 3.3.3 定时任务系统 (`samples/12-scheduled-tasks/`)

**优先级**: P2
**预估工作量**: 2 天
**学习目标**:

-   实现定时任务调度
-   掌握任务管理和监控
-   学会处理任务异常

**涵盖功能点**:

-   @Scheduled 注解实现
-   Cron 表达式支持
-   任务状态监控

#### 3.3.4 Actuator 监控端点 (`samples/13-actuator-endpoints/`)

**优先级**: P2
**预估工作量**: 1.5 天
**学习目标**:

-   学会使用 mini-boot 的 Actuator 功能
-   掌握应用监控端点的配置和使用
-   理解生产环境下的应用监控

**涵盖功能点**:

-   健康检查端点 (/actuator/health)
-   应用信息端点 (/actuator/info)
-   指标数据端点 (/actuator/metrics)
-   环境信息端点 (/actuator/env)
-   Bean 信息端点 (/actuator/beans)
-   自定义业务指标（计数器、计时器、仪表盘）
-   自定义健康检查指示器
-   自定义 Actuator 端点

**技术栈**:

-   mini-boot Actuator 模块
-   FastAPI 集成
-   JSON 数据格式
-   端点安全配置

#### 3.3.5 图书管理系统 (`samples/14-library-system/`)

**优先级**: P3
**预估工作量**: 6 天
**学习目标**:

-   构建企业级完整应用
-   综合运用所有框架特性
-   展示最佳实践和设计模式

**涵盖功能点**:

-   用户管理和认证（读者、管理员）
-   图书信息管理（增删改查）
-   借阅归还流程
-   图书搜索和分类
-   借阅记录统计
-   逾期提醒功能
-   缓存策略
-   数据报表生成

**技术栈**:

-   mini-boot + FastAPI
-   PostgreSQL + Redis
-   简单搜索功能
-   定时任务（逾期提醒）
-   监控指标集成

## 4. 示例依赖关系

```mermaid
graph TD
    A[01-hello-world] --> B[02-dependency-injection]
    A --> C[03-configuration]
    B --> D[04-events]
    C --> D
    D --> E[05-auto-configuration]
    B --> F[06-bean-lifecycle]
    E --> G[07-starters]
    F --> G
    G --> H[08-audio-starter]
    G --> I[09-multi-module]
    H --> I

    %% 核心特性示例
    B --> O[15-smart-async-context]
    F --> P[16-circular-dependency]
    E --> Q[17-comprehensive-annotations]
    C --> R[18-multi-environment-config]
    O --> S[19-async-processing]
    D --> T[20-advanced-events]
    P --> U[21-performance-optimization]
    Q --> V[22-custom-extensions]

    %% 实战示例
    I --> J[10-web-service]
    I --> K[11-data-access]
    I --> L[12-scheduled-tasks]
    J --> M[13-actuator-endpoints]
    K --> M
    S --> W[23-testing-strategies]
    T --> X[24-error-handling]

    %% 综合应用
    J --> N[14-library-system]
    K --> N
    L --> N
    M --> N
    U --> N
    V --> N
```

## 5. 任务进度列表

### 5.1 进度统计

-   **总任务数**: 24 个示例
-   **已完成**: 1 个 (4.2%)
-   **进行中**: 0 个 (0%)
-   **未开始**: 23 个 (95.8%)

### 5.2 详细进度跟踪

#### 基础示例 (Foundation Examples)

-   [x] **01-hello-world** - Hello World 基础示例
    -   状态: ✅ 已完成 | 优先级: P0 | 预估: 0.5 天 | 负责人: Python 系统架构师 | 完成日期: 2025-01-21
-   [ ] **02-dependency-injection** - 依赖注入基础
    -   状态: 📋 未开始 | 优先级: P0 | 预估: 1 天 | 负责人: - | 完成日期: -
-   [ ] **03-configuration** - 配置管理
    -   状态: 📋 未开始 | 优先级: P0 | 预估: 1 天 | 负责人: - | 完成日期: -
-   [ ] **04-events** - 事件系统基础
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 1 天 | 负责人: - | 完成日期: -

#### 进阶示例 (Advanced Examples)

-   [ ] **05-auto-configuration** - 自动配置开发
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **06-bean-lifecycle** - Bean 生命周期管理
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 1.5 天 | 负责人: - | 完成日期: -
-   [ ] **07-starters** - Starters 使用和开发
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **08-audio-starter** - 音频播放功能
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 1 天 | 负责人: - | 完成日期: -
-   [ ] **09-multi-module** - 多模块应用
    -   状态: 📋 未开始 | 优先级: P2 | 预估: 1.5 天 | 负责人: - | 完成日期: -

#### 核心特性示例 (Core Features Examples)

-   [ ] **15-smart-async-context** - 智能异步支持
    -   状态: 📋 未开始 | 优先级: P0 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **16-circular-dependency** - 三级缓存和循环依赖
    -   状态: 📋 未开始 | 优先级: P0 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **17-comprehensive-annotations** - 注解系统全覆盖
    -   状态: 📋 未开始 | 优先级: P0 | 预估: 3 天 | 负责人: - | 完成日期: -
-   [ ] **18-multi-environment-config** - 多环境配置管理
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **19-async-processing** - 异步处理和线程池
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **20-advanced-events** - 事件系统高级用法
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **21-performance-optimization** - 性能优化和监控
    -   状态: 📋 未开始 | 优先级: P2 | 预估: 2.5 天 | 负责人: - | 完成日期: -
-   [ ] **22-custom-extensions** - 自定义扩展开发
    -   状态: 📋 未开始 | 优先级: P2 | 预估: 3 天 | 负责人: - | 完成日期: -
-   [ ] **23-testing-strategies** - 测试策略和 Mock
    -   状态: 📋 未开始 | 优先级: P2 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **24-error-handling** - 错误处理和异常管理
    -   状态: 📋 未开始 | 优先级: P2 | 预估: 2 天 | 负责人: - | 完成日期: -

#### 实战示例 (Real-world Examples)

-   [ ] **10-web-service** - Web 服务应用
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 3 天 | 负责人: - | 完成日期: -
-   [ ] **11-data-access** - 数据访问层
    -   状态: 📋 未开始 | 优先级: P1 | 预估: 3 天 | 负责人: - | 完成日期: -
-   [ ] **12-scheduled-tasks** - 定时任务系统
    -   状态: 📋 未开始 | 优先级: P2 | 预估: 2 天 | 负责人: - | 完成日期: -
-   [ ] **13-actuator-endpoints** - Actuator 监控端点
    -   状态: 📋 未开始 | 优先级: P2 | 预估: 1.5 天 | 负责人: - | 完成日期: -
-   [ ] **14-library-system** - 图书管理系统
    -   状态: 📋 未开始 | 优先级: P3 | 预估: 6 天 | 负责人: - | 完成日期: -

### 5.3 进度更新说明

**更新频率**: 每完成一个示例后立即更新
**更新内容**:

-   修改任务状态 (未开始 → 进行中 → 已完成)
-   记录实际完成时间
-   更新负责人信息
-   记录遇到的问题和解决方案

**状态说明**:

-   ✅ **已完成**: 示例代码、文档、测试全部完成
-   🔄 **进行中**: 正在开发中
-   ⏸️ **暂停**: 因依赖或其他原因暂停
-   ❌ **取消**: 不再需要或合并到其他示例
-   📋 **未开始**: 尚未开始开发

**优先级说明**:

-   **P0**: 必须完成，框架核心功能
-   **P1**: 重要功能，建议完成
-   **P2**: 一般功能，时间允许时完成
-   **P3**: 可选功能，展示用途

## 6. 文档规范

### 6.1 README.md 结构

每个示例的 README.md 应包含：

1. 项目简介和学习目标
2. 功能特性列表
3. 技术栈说明
4. 快速开始指南
5. 详细代码解释
6. 配置说明
7. 运行和测试步骤
8. 常见问题解答
9. 相关资源链接

### 6.2 代码注释规范

-   所有类和方法都要有详细的文档字符串
-   关键业务逻辑要有行内注释
-   配置文件要有详细的参数说明
-   复杂算法要有实现思路说明

### 6.3 配置文件规范

-   使用有意义的配置项名称
-   提供默认值和示例值
-   添加配置项说明注释
-   支持多环境配置

## 7. 质量保证

### 7.1 代码质量

-   所有示例代码都要通过代码质量检查
-   遵循 Python PEP 8 编码规范
-   使用类型提示增强代码可读性
-   包含适当的错误处理

### 7.2 测试覆盖

-   每个示例都要包含基本的测试用例
-   测试覆盖率不低于 80%
-   包含集成测试和端到端测试
-   提供测试运行指南

### 7.3 文档质量

-   文档内容准确无误
-   代码示例可以直接运行
-   包含清晰的架构图和流程图
-   定期更新和维护

## 8. 后续维护

### 8.1 版本管理

-   示例代码与框架版本保持同步
-   及时修复已知问题
-   根据用户反馈优化示例

### 9.2 社区贡献

-   鼓励社区贡献新的示例
-   建立示例贡献指南
-   定期 review 和合并贡献

### 9.3 持续改进

-   收集用户使用反馈
-   分析常见问题和痛点
-   持续优化示例设计和文档质量

## 10. 技术实施细节

### 10.1 示例项目模板结构

#### 10.1.1 简单示例项目结构（基础和进阶示例）

```
samples/XX-simple-example/
├── README.md                 # 项目文档
├── main.py                  # 应用入口（包含所有代码）
├── requirements.txt          # Python依赖
└── resources/               # 配置文件目录
    ├── application.yml
    ├── application-dev.yml
    └── application-prod.yml
```

#### 10.1.2 复杂系统项目结构（实战示例）

```
samples/XX-complex-system/
├── README.md                 # 项目文档
├── main.py                  # 应用入口
├── requirements.txt          # Python依赖
├── resources/               # 配置文件目录
│   ├── application.yml
│   ├── application-dev.yml
│   └── application-prod.yml
├── src/                     # 源代码目录
│   ├── __init__.py
│   ├── models/              # 数据模型
│   ├── services/            # 业务服务
│   ├── controllers/         # 控制器
│   └── config/              # 配置类
├── tests/                   # 测试代码
│   ├── __init__.py
│   ├── unit/
│   └── integration/
└── docs/                    # 详细文档
    ├── architecture.md
    └── api.md
```

### 10.2 代码风格指南

#### 10.2.1 导入规范

```python
# 标准库导入
import os
import sys
from typing import Optional, List, Dict

# 第三方库导入
import requests
from sqlalchemy import Column, Integer, String

# mini-boot框架导入
from miniboot import Application
from miniboot.annotations import Autowired, Value
from miniboot.context import ApplicationContext

# 本地模块导入
from .models import User
from .services import UserService
```

#### 10.2.2 类和方法命名

```python
class UserService:
    """用户服务类

    提供用户相关的业务逻辑处理功能。
    """

    @Autowired
    def __init__(self, user_repository: 'UserRepository'):
        self.user_repository = user_repository

    def create_user(self, username: str, email: str) -> User:
        """创建新用户

        Args:
            username: 用户名
            email: 邮箱地址

        Returns:
            创建的用户对象

        Raises:
            ValueError: 当用户名或邮箱格式不正确时
        """
        # 实现逻辑...
        pass
```

### 10.3 配置管理最佳实践

#### 10.3.1 配置文件组织

```yaml
# application.yml - 基础配置
app:
    name: mini-boot-example
    version: 1.0.0

# 数据库配置
database:
    url: postgresql://localhost:5432/miniboot
    pool:
        size: 10
        timeout: 30

# 日志配置
logging:
    level: INFO
    file: logs/application.log

# 自定义业务配置
business:
    feature:
        enabled: true
    cache:
        ttl: 3600
```

#### 10.3.2 环境特定配置

```yaml
# application-dev.yml - 开发环境
database:
  url: postgresql://localhost:5432/miniboot_dev
logging:
  level: DEBUG

# application-prod.yml - 生产环境
database:
  url: ${DATABASE_URL}
logging:
  level: WARN
```

### 10.4 错误处理模式

#### 10.4.1 业务异常定义

```python
class BusinessException(Exception):
    """业务异常基类"""

    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.error_code = error_code

class UserNotFoundException(BusinessException):
    """用户未找到异常"""

    def __init__(self, user_id: int):
        super().__init__(f"User with id {user_id} not found", "USER_NOT_FOUND")
```

#### 10.4.2 全局异常处理

```python
from miniboot.events import EventListener

class GlobalExceptionHandler:
    """全局异常处理器"""

    @EventListener
    def handle_business_exception(self, event: BusinessException):
        """处理业务异常"""
        logger.error(f"Business error: {event.message}")
        # 记录日志、发送通知等
```

#### 10.4.3 自定义 Actuator 指标示例

```python
from miniboot.annotations import Component
from miniboot.actuator import Counter, Timer, Gauge
import time
import random

@Component
class CustomMetrics:
    """自定义业务指标"""

    def __init__(self):
        # 计数器 - 统计请求次数
        self.request_counter = Counter("business.requests.total",
                                     description="Total business requests")

        # 计时器 - 统计处理时间
        self.process_timer = Timer("business.process.duration",
                                 description="Business process duration")

        # 仪表盘 - 显示当前活跃用户数
        self.active_users_gauge = Gauge("business.users.active",
                                      description="Current active users")

    def process_request(self, request_type: str):
        """处理业务请求并记录指标"""
        # 增加请求计数
        self.request_counter.increment(tags={"type": request_type})

        # 记录处理时间
        with self.process_timer.time():
            # 模拟业务处理
            time.sleep(random.uniform(0.1, 0.5))

        # 更新活跃用户数（示例）
        self.active_users_gauge.set(random.randint(10, 100))

@Component
class CustomHealthIndicator:
    """自定义健康检查指示器"""

    def __init__(self):
        self.database_connected = True
        self.external_service_available = True

    def check_database_health(self) -> dict:
        """检查数据库健康状态"""
        return {
            "status": "UP" if self.database_connected else "DOWN",
            "details": {
                "connection_pool": "active",
                "response_time": "< 100ms"
            }
        }

    def check_external_service_health(self) -> dict:
        """检查外部服务健康状态"""
        return {
            "status": "UP" if self.external_service_available else "DOWN",
            "details": {
                "endpoint": "https://api.example.com",
                "last_check": "2024-01-01T12:00:00Z"
            }
        }
```

#### 10.4.4 Starters 使用和开发示例

##### 使用内置 Starters

```python
# requirements.txt
mini-boot-starter-web==0.0.4
mini-boot-starter-data==0.0.4
mini-boot-starter-audio==1.0.0

# main.py
from miniboot import Application
from miniboot.annotations import Component, Autowired
from miniboot.web import RestController, GetMapping
from miniboot.data import Repository
from miniboot.starters.audio import AudioService, AudioItem

@RestController("/api/users")
class UserController:
    """用户控制器 - 使用 web-starter"""

    @Autowired
    def __init__(self, user_service: 'UserService'):
        self.user_service = user_service

    @GetMapping("/")
    def get_users(self):
        return self.user_service.find_all()

@Repository
class UserRepository:
    """用户仓库 - 使用 data-starter"""

    def find_all(self):
        # 使用 data-starter 提供的数据访问功能
        return []

@Component
class NotificationService:
    """通知服务 - 使用 audio-starter"""

    @Autowired
    def __init__(self, audio_service: AudioService):
        self.audio_service = audio_service

    async def notify_user_created(self, username: str):
        """用户创建通知"""
        await self.audio_service.speak(f"新用户 {username} 已成功创建")

    async def play_system_alert(self):
        """系统警报"""
        sequence = [
            "sounds/alert.mp3",  # 警报音效
            "系统检测到异常",      # 语音提示
            "请立即检查"          # 语音提示
        ]
        await self.audio_service.play_sequence(sequence, interval=0.3)
```

##### 自定义 Starter 开发

```python
# redis-starter/setup.py
from setuptools import setup, find_packages

setup(
    name="mini-boot-starter-redis",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "mini-boot>=0.0.4",
        "redis>=4.0.0"
    ]
)

# redis-starter/miniboot_starter_redis/__init__.py
from .autoconfigure import RedisAutoConfiguration
from .properties import RedisProperties
from .template import RedisTemplate

__all__ = ['RedisAutoConfiguration', 'RedisProperties', 'RedisTemplate']

# redis-starter/miniboot_starter_redis/properties.py
from miniboot.annotations import ConfigurationProperties

@ConfigurationProperties(prefix="redis")
class RedisProperties:
    """Redis 配置属性"""

    def __init__(self):
        self.host = "localhost"
        self.port = 6379
        self.password = None
        self.database = 0
        self.timeout = 2000

# redis-starter/miniboot_starter_redis/template.py
import redis
from typing import Any, Optional

class RedisTemplate:
    """Redis 操作模板"""

    def __init__(self, connection_pool):
        self.redis_client = redis.Redis(connection_pool=connection_pool)

    def set(self, key: str, value: Any, ex: Optional[int] = None):
        """设置键值"""
        return self.redis_client.set(key, value, ex=ex)

    def get(self, key: str) -> Optional[str]:
        """获取值"""
        result = self.redis_client.get(key)
        return result.decode('utf-8') if result else None

    def delete(self, key: str) -> bool:
        """删除键"""
        return bool(self.redis_client.delete(key))

# redis-starter/miniboot_starter_redis/autoconfigure.py
from miniboot.annotations import Configuration, Bean, ConditionalOnProperty
from miniboot.context import ApplicationContext
import redis

from .properties import RedisProperties
from .template import RedisTemplate

@Configuration
@ConditionalOnProperty(name="redis.enabled", having_value="true", match_if_missing=True)
class RedisAutoConfiguration:
    """Redis 自动配置"""

    @Bean
    def redis_properties(self) -> RedisProperties:
        """Redis 配置属性 Bean"""
        return RedisProperties()

    @Bean
    def redis_connection_pool(self, redis_properties: RedisProperties) -> redis.ConnectionPool:
        """Redis 连接池 Bean"""
        return redis.ConnectionPool(
            host=redis_properties.host,
            port=redis_properties.port,
            password=redis_properties.password,
            db=redis_properties.database,
            socket_timeout=redis_properties.timeout / 1000
        )

    @Bean
    def redis_template(self, connection_pool: redis.ConnectionPool) -> RedisTemplate:
        """Redis 操作模板 Bean"""
        return RedisTemplate(connection_pool)
```

##### 使用自定义 Starter

```python
# requirements.txt
mini-boot-starter-redis==0.1.0
mini-boot-starter-audio==1.0.0

# resources/application.yml
redis:
  enabled: true
  host: localhost
  port: 6379
  database: 0

miniboot:
  audio:
    enabled: true
    player:
      enabled: true
      volume: 0.8
    tts:
      enabled: true
      language: "zh-CN"
      rate: 160

# main.py
from miniboot import Application
from miniboot.annotations import Autowired, Component
from miniboot_starter_redis import RedisTemplate
from miniboot.starters.audio import AudioService

@Component
class CacheService:
    """缓存服务 - 使用自定义 redis-starter"""

    @Autowired
    def __init__(self, redis_template: RedisTemplate):
        self.redis_template = redis_template

    def cache_user(self, user_id: str, user_data: dict):
        """缓存用户数据"""
        self.redis_template.set(f"user:{user_id}", str(user_data), ex=3600)

    def get_cached_user(self, user_id: str) -> str:
        """获取缓存的用户数据"""
        return self.redis_template.get(f"user:{user_id}")

@Component
class SystemService:
    """系统服务 - 集成多个 starters"""

    @Autowired
    def __init__(self, redis_template: RedisTemplate, audio_service: AudioService):
        self.redis_template = redis_template
        self.audio_service = audio_service

    async def handle_cache_miss(self, key: str):
        """处理缓存未命中"""
        # 记录到 Redis
        self.redis_template.set(f"cache_miss:{key}", "1", ex=300)

        # 语音提醒
        await self.audio_service.speak(f"缓存键 {key} 未命中")

    async def system_startup_notification(self):
        """系统启动通知"""
        # 检查 Redis 连接
        try:
            self.redis_template.set("health_check", "ok", ex=10)
            await self.audio_service.speak("Redis 连接正常")
        except Exception:
            await self.audio_service.speak("Redis 连接异常")

        await self.audio_service.speak("系统启动完成")
```

### 10.5 测试策略

#### 10.5.1 单元测试示例

```python
import unittest
from unittest.mock import Mock, patch

from miniboot.context import ApplicationContext
from src.services import UserService

class TestUserService(unittest.TestCase):
    """用户服务测试类"""

    def setUp(self):
        """测试前置设置"""
        self.context = ApplicationContext()
        self.user_repository = Mock()
        self.user_service = UserService(self.user_repository)

    def test_create_user_success(self):
        """测试创建用户成功场景"""
        # Given
        username = "testuser"
        email = "<EMAIL>"

        # When
        result = self.user_service.create_user(username, email)

        # Then
        self.assertIsNotNone(result)
        self.user_repository.save.assert_called_once()
```

#### 10.5.2 集成测试示例

```python
import unittest
from miniboot import Application

class TestUserServiceIntegration(unittest.TestCase):
    """用户服务集成测试"""

    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        cls.app = Application()
        cls.app.run()
        cls.context = cls.app.get_context()

    def test_user_creation_flow(self):
        """测试用户创建完整流程"""
        user_service = self.context.get_bean(UserService)

        # 执行完整的用户创建流程
        user = user_service.create_user("integration_test", "<EMAIL>")

        # 验证结果
        self.assertIsNotNone(user.id)
        self.assertEqual(user.username, "integration_test")
```

## 11. 学习资源和参考

### 11.1 推荐学习顺序

1. **Python 基础** (如果需要)

    - 面向对象编程
    - 装饰器和元类
    - 异步编程基础

2. **依赖注入概念**

    - 控制反转(IoC)原理
    - 依赖注入模式
    - 服务定位器模式

3. **框架核心概念**

    - 应用上下文
    - Bean 生命周期
    - 事件驱动架构

4. **企业级开发模式**
    - 分层架构
    - 领域驱动设计
    - 微服务架构

### 11.2 外部资源链接

-   [Spring Boot 官方文档](https://spring.io/projects/spring-boot) - 设计理念参考
-   [Python 类型提示](https://docs.python.org/3/library/typing.html) - 类型系统
-   [依赖注入模式](https://martinfowler.com/articles/injection.html) - 理论基础
-   [十二要素应用](https://12factor.net/zh_cn/) - 应用设计原则

---

**注意**: 本规划文档将根据实际开发进度和用户反馈进行动态调整。优先级标记：P0(必须)、P1(重要)、P2(一般)、P3(可选)。
