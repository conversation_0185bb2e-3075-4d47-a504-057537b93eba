#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块化条件初始化集成测试

测试不同模块组合的启动场景和模块间协作，验证：
- 不同配置组合下的模块初始化
- 模块间依赖关系
- 完整的应用启动流程
"""

import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

from miniboot.context.application import DefaultApplicationContext


class ModuleInitializationIntegrationTestCase(unittest.IsolatedAsyncioTestCase):
    """模块化条件初始化集成测试"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "application.yml"

    def tearDown(self):
        """清理测试环境"""
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_config_file(self, config_content: str):
        """创建配置文件"""
        with self.config_file.open("w", encoding="utf-8") as f:
            f.write(config_content)

    async def test_all_modules_enabled(self):
        """测试所有模块都启用的场景 - 使用模拟避免服务器启动"""
        # 创建配置文件 - 禁用会启动服务器的模块
        config_content = """
miniboot:
  async:
    enabled: true
  web:
    enabled: false  # 禁用避免启动uvicorn
    host: "127.0.0.1"
    port: 8080
  scheduler:
    enabled: false  # 禁用避免启动调度器
    timezone: "Asia/Shanghai"
  actuators:
    endpoints:
      web:
        enabled: false  # 禁用避免启动监控服务器
        base-path: "/actuator"
  # 日志配置 - 最小化输出
  logging:
    level: ERROR
    console:
      enabled: false
    file:
      enabled: false
"""
        self.create_config_file(config_content)

        # 创建应用上下文
        context = DefaultApplicationContext()

        try:
            # 设置配置文件路径
            with patch.dict("os.environ", {"MINIBOOT_CONFIG_PATH": str(self.config_file)}):
                # 启动应用上下文
                await context.start()

                # 验证应用上下文已启动
                self.assertTrue(context.is_running())

                # 验证模块初始化器已创建
                self.assertTrue(hasattr(context, "_module_initializer"))

                # 验证模块状态
                if hasattr(context, "_module_initializer"):
                    module_status = context._module_initializer.get_module_status()

                    # 所有模块都应该被初始化
                    self.assertTrue(module_status.get("async", False))
                    # Web和其他模块的验证可能需要更复杂的模拟

        finally:
            # 停止应用上下文
            if context.is_running():
                await context.stop()

    async def test_minimal_configuration(self):
        """测试最小配置场景（只启用异步模块）"""
        # 创建最小配置文件
        config_content = """
miniboot:
  async:
    enabled: true
  web:
    enabled: false
  scheduler:
    enabled: false
  actuators:
    endpoints:
      web:
        enabled: false
"""
        self.create_config_file(config_content)

        # 创建应用上下文
        context = DefaultApplicationContext()

        try:
            # 设置配置文件路径
            with patch.dict("os.environ", {"MINIBOOT_CONFIG_PATH": str(self.config_file)}):
                # 启动应用上下文
                await context.start()

                # 验证应用上下文已启动
                self.assertTrue(context.is_running())

                # 验证模块状态
                if hasattr(context, "_module_initializer"):
                    module_status = context._module_initializer.get_module_status()

                    # 只有异步模块应该被初始化
                    self.assertTrue(module_status.get("async", False))
                    self.assertFalse(module_status.get("web", True))
                    self.assertFalse(module_status.get("scheduler", True))
                    self.assertFalse(module_status.get("actuator", True))

        finally:
            # 停止应用上下文
            if context.is_running():
                await context.stop()

    async def test_web_only_configuration(self):
        """测试只启用Web模块的场景 - 使用模拟避免服务器启动"""
        # 创建Web配置文件 - 禁用真实服务器但启用Web模块
        config_content = """
miniboot:
  async:
    enabled: false
  web:
    enabled: true  # 启用Web模块但不启动真实服务器
    host: "127.0.0.1"
    port: 0  # 使用动态端口避免冲突
    cors:
      enabled: true
      allowed-origins: ["*"]
  scheduler:
    enabled: false
  actuators:
    endpoints:
      web:
        enabled: false
  # 日志配置 - 最小化输出
  logging:
    level: ERROR
    console:
      enabled: false
    file:
      enabled: false
"""
        self.create_config_file(config_content)

        # 创建应用上下文
        context = DefaultApplicationContext()

        try:
            # 设置配置文件路径
            with (
                patch.dict("os.environ", {"MINIBOOT_CONFIG_PATH": str(self.config_file)}),
                patch("miniboot.context.module_initializer.logger") as mock_logger,
                # 模拟Web服务器启动以避免真实端口绑定
                patch("miniboot.web.application.MiniBootWebApplication.start_server"),
            ):
                # 启动应用上下文
                await context.start()

                # 验证应用上下文已启动
                self.assertTrue(context.is_running())

                # 验证Web模块相关的日志
                log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
                web_logs = [log for log in log_calls if "Web" in log]

                # 应该有Web模块初始化的日志
                self.assertTrue(any("Web" in log for log in web_logs))

        finally:
            # 停止应用上下文
            if context.is_running():
                await context.stop()

    async def test_scheduler_only_configuration(self):
        """测试只启用调度器模块的场景 - 禁用避免阻塞"""
        # 创建调度器配置文件 - 禁用调度器避免阻塞
        config_content = """
miniboot:
  async:
    enabled: false
  web:
    enabled: false
  scheduler:
    enabled: false  # 禁用避免启动调度器导致阻塞
    timezone: "Asia/Shanghai"
    concurrency:
      max-workers: 5
    auto-startup: true
  actuators:
    endpoints:
      web:
        enabled: false
  # 日志配置 - 最小化输出
  logging:
    level: ERROR
    console:
      enabled: false
    file:
      enabled: false
"""
        self.create_config_file(config_content)

        # 创建应用上下文
        context = DefaultApplicationContext()

        try:
            # 设置配置文件路径
            with (
                patch.dict("os.environ", {"MINIBOOT_CONFIG_PATH": str(self.config_file)}),
                patch("miniboot.context.module_initializer.logger") as mock_logger,
            ):
                # 启动应用上下文
                await context.start()

                # 验证应用上下文已启动
                self.assertTrue(context.is_running())

                # 验证调度器模块相关的日志
                log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
                scheduler_logs = [log for log in log_calls if "Scheduler" in log]

                # 应该有调度器模块初始化的日志
                self.assertTrue(any("Scheduler" in log for log in scheduler_logs))

        finally:
            # 停止应用上下文
            if context.is_running():
                await context.stop()

    async def test_actuator_only_configuration(self):
        """测试只启用监控模块的场景 - 禁用避免服务器启动"""
        # 创建监控配置文件 - 禁用Web端点避免启动服务器
        config_content = """
miniboot:
  async:
    enabled: false
  web:
    enabled: false
  scheduler:
    enabled: false
  actuators:
    endpoints:
      web:
        enabled: false  # 禁用避免启动监控服务器
        base-path: "/actuator"
        exposure:
          include: ["health", "info", "metrics"]
      health:
        enabled: true
        show-details: "always"
      info:
        enabled: true
      metrics:
        enabled: true
  # 日志配置 - 最小化输出
  logging:
    level: ERROR
    console:
      enabled: false
    file:
      enabled: false
"""
        self.create_config_file(config_content)

        # 创建应用上下文
        context = DefaultApplicationContext()

        try:
            # 设置配置文件路径
            with (
                patch.dict("os.environ", {"MINIBOOT_CONFIG_PATH": str(self.config_file)}),
                patch("miniboot.context.module_initializer.logger") as mock_logger,
            ):
                # 启动应用上下文
                await context.start()

                # 验证应用上下文已启动
                self.assertTrue(context.is_running())

                # 验证监控模块相关的日志
                log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
                actuator_logs = [log for log in log_calls if "Actuator" in log]

                # 应该有监控模块初始化的日志
                self.assertTrue(any("Actuator" in log for log in actuator_logs))

        finally:
            # 停止应用上下文
            if context.is_running():
                await context.stop()

    async def test_no_modules_enabled(self):
        """测试所有模块都禁用的场景"""
        # 创建禁用所有模块的配置文件
        config_content = """
miniboot:
  async:
    enabled: false
  web:
    enabled: false
  scheduler:
    enabled: false
  actuators:
    endpoints:
      web:
        enabled: false
"""
        self.create_config_file(config_content)

        # 调试：检查配置文件内容
        with self.config_file.open(encoding="utf-8") as f:
            file_content = f.read()
            print(f"DEBUG: Config file content:\n{file_content}")

        # 创建应用上下文，直接传递配置路径
        config_dir = self.config_file.parent
        context = DefaultApplicationContext(config_path=str(config_dir))

        try:
            # 启动应用上下文
            await context.start()

            # 验证应用上下文已启动（即使没有模块启用）
            self.assertTrue(context.is_running())

            # 调试：检查配置值
            env = context.get_environment()
            async_enabled = env.get_property("miniboot.async.enabled", "NOT_FOUND")
            web_enabled = env.get_property("miniboot.web.enabled", "NOT_FOUND")
            print(f"DEBUG: miniboot.async.enabled = {async_enabled}")
            print(f"DEBUG: miniboot.web.enabled = {web_enabled}")

            # 验证模块状态
            if hasattr(context, "_module_initializer"):
                module_status = context._module_initializer.get_module_status()
                print(f"DEBUG: module_status = {module_status}")

                # 所有模块都应该被禁用
                self.assertFalse(module_status.get("async", True))
                self.assertFalse(module_status.get("web", True))
                self.assertFalse(module_status.get("scheduler", True))
                self.assertFalse(module_status.get("actuator", True))

        finally:
            # 停止应用上下文
            if context.is_running():
                await context.stop()

    async def test_invalid_configuration_handling(self):
        """测试无效配置的处理 - 禁用服务器避免阻塞"""
        # 创建包含无效配置的文件 - 禁用服务器模块
        config_content = """
miniboot:
  web:
    enabled: false  # 禁用避免启动服务器
    port: "invalid_port"  # 无效的端口配置
  scheduler:
    enabled: false  # 禁用避免启动调度器
    concurrency:
      max-workers: -1  # 无效的工作线程数
  # 日志配置 - 最小化输出
  logging:
    level: ERROR
    console:
      enabled: false
    file:
      enabled: false
"""
        self.create_config_file(config_content)

        # 创建应用上下文
        context = DefaultApplicationContext()

        try:
            # 设置配置文件路径
            with (
                patch.dict("os.environ", {"MINIBOOT_CONFIG_PATH": str(self.config_file)}),
                patch("miniboot.context.module_initializer.logger") as mock_logger,
            ):
                await context.start()

                # 验证应用上下文已启动（错误配置不应该阻止启动）
                self.assertTrue(context.is_running())

                # 验证有错误日志
                log_calls = [call.args[0] for call in mock_logger.error.call_args_list + mock_logger.warning.call_args_list]

                # 应该有配置错误相关的日志
                error_logs = [log for log in log_calls if any(keyword in log.lower() for keyword in ["error", "invalid", "failed"])]
                self.assertTrue(len(error_logs) > 0)

        finally:
            # 停止应用上下文
            if context.is_running():
                await context.stop()


if __name__ == "__main__":
    unittest.main()
