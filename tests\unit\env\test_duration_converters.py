"""
Mini-Boot 转换系统单元测试

测试时间间隔、大小、百分比转换器的功能。
"""

import unittest
from miniboot.env.convert import (
    DefaultConversionService, 
    ConversionError,
    StrToDurationConverter,
    StrToSizeConverter, 
    StrToPercentageConverter,
    StrToFloatCompositeConverter
)


class TestDurationConverter(unittest.TestCase):
    """时间间隔转换器测试"""
    
    def setUp(self):
        self.converter = StrToDurationConverter()
    
    def test_can_convert(self):
        """测试转换器类型检查"""
        self.assertTrue(self.converter.can_convert(str, float))
        self.assertFalse(self.converter.can_convert(int, float))
        self.assertFalse(self.converter.can_convert(str, int))
    
    def test_duration_format_detection(self):
        """测试时间格式检测"""
        # 有效格式
        self.assertTrue(self.converter._is_duration_format("30s"))
        self.assertTrue(self.converter._is_duration_format("5m"))
        self.assertTrue(self.converter._is_duration_format("1h"))
        self.assertTrue(self.converter._is_duration_format("2d"))
        self.assertTrue(self.converter._is_duration_format("30"))  # 默认秒
        self.assertTrue(self.converter._is_duration_format("1.5h"))
        
        # 无效格式
        self.assertFalse(self.converter._is_duration_format("50%"))  # 百分比
        self.assertFalse(self.converter._is_duration_format("1KB"))  # 大小
        self.assertFalse(self.converter._is_duration_format("invalid"))
        self.assertFalse(self.converter._is_duration_format(""))
    
    def test_duration_conversion(self):
        """测试时间间隔转换"""
        test_cases = [
            ("30s", 30.0),
            ("5m", 300.0),
            ("1h", 3600.0),
            ("2d", 172800.0),
            ("30", 30.0),  # 默认秒
            ("1.5h", 5400.0),  # 小数
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input=input_str):
                result = self.converter.convert(input_str, float)
                self.assertAlmostEqual(result, expected, places=1)
    
    def test_invalid_duration_conversion(self):
        """测试无效时间间隔转换"""
        invalid_cases = ["invalid", "30x", ""]
        
        for invalid_input in invalid_cases:
            with self.subTest(input=invalid_input):
                with self.assertRaises(ConversionError):
                    self.converter.convert(invalid_input, float)


class TestSizeConverter(unittest.TestCase):
    """大小转换器测试"""
    
    def setUp(self):
        self.converter = StrToSizeConverter()
    
    def test_can_convert(self):
        """测试转换器类型检查"""
        self.assertTrue(self.converter.can_convert(str, int))
        self.assertFalse(self.converter.can_convert(int, int))
        self.assertFalse(self.converter.can_convert(str, float))
    
    def test_size_format_detection(self):
        """测试大小格式检测"""
        # 有效格式
        self.assertTrue(self.converter._is_size_format("1024"))
        self.assertTrue(self.converter._is_size_format("1KB"))
        self.assertTrue(self.converter._is_size_format("1MB"))
        self.assertTrue(self.converter._is_size_format("1GB"))
        self.assertTrue(self.converter._is_size_format("512b"))
        
        # 无效格式
        self.assertFalse(self.converter._is_size_format("50%"))  # 百分比
        self.assertFalse(self.converter._is_size_format("30s"))  # 时间
        self.assertFalse(self.converter._is_size_format("invalid"))
        self.assertFalse(self.converter._is_size_format(""))
    
    def test_size_conversion(self):
        """测试大小转换"""
        test_cases = [
            ("1024", 1024),
            ("1KB", 1024),
            ("1MB", 1024 * 1024),
            ("1GB", 1024 * 1024 * 1024),
            ("512b", 512),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input=input_str):
                result = self.converter.convert(input_str, int)
                self.assertEqual(result, expected)
    
    def test_invalid_size_conversion(self):
        """测试无效大小转换"""
        invalid_cases = ["invalid", "30x", ""]
        
        for invalid_input in invalid_cases:
            with self.subTest(input=invalid_input):
                with self.assertRaises(ConversionError):
                    self.converter.convert(invalid_input, int)


class TestPercentageConverter(unittest.TestCase):
    """百分比转换器测试"""
    
    def setUp(self):
        self.converter = StrToPercentageConverter()
    
    def test_can_convert(self):
        """测试转换器类型检查"""
        self.assertTrue(self.converter.can_convert(str, float))
        self.assertFalse(self.converter.can_convert(int, float))
        self.assertFalse(self.converter.can_convert(str, int))
    
    def test_percentage_format_detection(self):
        """测试百分比格式检测"""
        # 有效格式
        self.assertTrue(self.converter._is_percentage_format("50%"))
        self.assertTrue(self.converter._is_percentage_format("100%"))
        self.assertTrue(self.converter._is_percentage_format("0%"))
        self.assertTrue(self.converter._is_percentage_format("75.5%"))
        self.assertTrue(self.converter._is_percentage_format("0.5"))  # 小数形式
        self.assertTrue(self.converter._is_percentage_format("1.0"))
        
        # 无效格式
        self.assertFalse(self.converter._is_percentage_format("30s"))  # 时间
        self.assertFalse(self.converter._is_percentage_format("1KB"))  # 大小
        self.assertFalse(self.converter._is_percentage_format("invalid"))
        self.assertFalse(self.converter._is_percentage_format(""))
        self.assertFalse(self.converter._is_percentage_format("45.67"))  # 超出 0-1 范围
    
    def test_percentage_conversion(self):
        """测试百分比转换"""
        test_cases = [
            ("50%", 0.5),
            ("100%", 1.0),
            ("0%", 0.0),
            ("75.5%", 0.755),
            ("0.5", 0.5),  # 小数形式
            ("1.0", 1.0),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input=input_str):
                result = self.converter.convert(input_str, float)
                self.assertAlmostEqual(result, expected, places=3)
    
    def test_invalid_percentage_conversion(self):
        """测试无效百分比转换"""
        invalid_cases = ["150%", "-10%", "invalid", ""]
        
        for invalid_input in invalid_cases:
            with self.subTest(input=invalid_input):
                with self.assertRaises(ConversionError):
                    self.converter.convert(invalid_input, float)


class TestCompositeConverter(unittest.TestCase):
    """复合转换器测试"""
    
    def setUp(self):
        self.converter = StrToFloatCompositeConverter()
    
    def test_can_convert(self):
        """测试转换器类型检查"""
        self.assertTrue(self.converter.can_convert(str, float))
        self.assertFalse(self.converter.can_convert(int, float))
        self.assertFalse(self.converter.can_convert(str, int))
    
    def test_composite_conversion(self):
        """测试复合转换功能"""
        test_cases = [
            # 时间间隔
            ("30s", 30.0),
            ("5m", 300.0),
            ("1h", 3600.0),
            
            # 百分比
            ("50%", 0.5),
            ("100%", 1.0),
            ("0.5", 0.5),
            
            # 普通数字
            ("45.67", 45.67),
            ("123", 123.0),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input=input_str):
                result = self.converter.convert(input_str, float)
                self.assertAlmostEqual(result, expected, places=3)
    
    def test_invalid_composite_conversion(self):
        """测试无效复合转换"""
        invalid_cases = ["invalid", "30x", ""]
        
        for invalid_input in invalid_cases:
            with self.subTest(input=invalid_input):
                with self.assertRaises(ConversionError):
                    self.converter.convert(invalid_input, float)


class TestDefaultConversionService(unittest.TestCase):
    """默认转换服务测试"""
    
    def setUp(self):
        self.service = DefaultConversionService()
    
    def test_duration_conversion_integration(self):
        """测试时间间隔转换集成"""
        test_cases = [
            ("30s", 30.0),
            ("5m", 300.0),
            ("1h", 3600.0),
            ("2d", 172800.0),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input=input_str):
                result = self.service.convert(input_str, float)
                self.assertAlmostEqual(result, expected, places=1)
    
    def test_size_conversion_integration(self):
        """测试大小转换集成"""
        test_cases = [
            ("1024", 1024),
            ("1KB", 1024),
            ("1MB", 1024 * 1024),
            ("1GB", 1024 * 1024 * 1024),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input=input_str):
                result = self.service.convert(input_str, int)
                self.assertEqual(result, expected)
    
    def test_percentage_conversion_integration(self):
        """测试百分比转换集成"""
        test_cases = [
            ("50%", 0.5),
            ("100%", 1.0),
            ("0%", 0.0),
            ("75.5%", 0.755),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input=input_str):
                result = self.service.convert(input_str, float)
                self.assertAlmostEqual(result, expected, places=3)
    
    def test_existing_converters_compatibility(self):
        """测试现有转换器兼容性"""
        test_cases = [
            ("true", bool, True),
            ("false", bool, False),
            ("123", int, 123),
            ("45.67", float, 45.67),
            ("a,b,c", list, ["a", "b", "c"]),
        ]
        
        for input_str, target_type, expected in test_cases:
            with self.subTest(input=input_str, target=target_type.__name__):
                result = self.service.convert(input_str, target_type)
                self.assertEqual(result, expected)


if __name__ == '__main__':
    unittest.main()
