"""
应用上下文模块

提供 Mini-Boot 框架的应用上下文管理功能.

主要功能:
- ApplicationContext - 应用上下文接口
- DefaultApplicationContext - 默认应用上下文实现
- Bean 生命周期管理
- 环境配置集成
- 事件发布与订阅
- 异步执行支持
"""

# 导入具体的Bean相关异常类型,避免别名混乱
from ..errors import \
    BeanCircularDependencyError as CircularDependencyError  # 保持向后兼容的别名
from ..errors import BeanNotFoundError  # 保持向后兼容的别名
from ..errors import BeanCreationError
from ..errors import \
    BeanDefinitionError as BeanDefinitionStoreError  # 保持向后兼容的别名
from ..errors import \
    ContextConfigurationError as ApplicationContextError  # 保持向后兼容的别名
from ..errors import ContextConfigurationError as ConfigurationRefreshError
from ..errors import ContextShutdownError, ContextStartupError
from ..errors import ContextStateError as IllegalStateError  # 保持向后兼容的别名
from .application import (ApplicationContext, ApplicationContextState,
                          DefaultApplicationContext)
from .auto_context import (ContextManager, auto_context, create_application,
                           get_default_context, get_global_context_manager,
                           set_default_context, with_context)
from .runtime_detector import (  # AsyncEnvironmentDetector is for backward compatibility
    AsyncEnvironmentDetector, RuntimeDetector)

# Smart context functionality is now integrated into DefaultApplicationContext

__all__ = [
    # 核心接口和实现
    "ApplicationContext",
    "DefaultApplicationContext",  # Now includes smart features by default
    # 运行时环境检测
    "RuntimeDetector",
    "AsyncEnvironmentDetector",  # Backward compatibility alias
    # 自动上下文装饰器
    "auto_context",
    "with_context",
    "create_application",
    "ContextManager",
    "get_global_context_manager",
    "set_default_context",
    "get_default_context",
    # 异常类
    "ApplicationContextError",
    "BeanCreationError",
    "BeanDefinitionStoreError",
    "BeanNotFoundError",
    "CircularReferenceError",
    "ConfigurationRefreshError",
    "ContextStartupError",
    "ContextShutdownError",
    "IllegalStateError",
]

__version__ = "1.0.0"
