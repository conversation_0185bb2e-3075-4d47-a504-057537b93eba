# 测试配置文件 - 禁用Web模块
# 用于单元测试，避免端口冲突

# 应用基础配置
app:
    name: "mini-boot-test"
    version: "1.0.0-test"

# 环境配置
spring:
    profiles:
        active: test

# 禁用Web模块以避免端口冲突
miniboot:
    web:
        enabled: false
        host: "127.0.0.1"
        port: 0 # 使用动态端口避免冲突

    # 异步配置
    async:
        enabled: true
        timeout: 30.0

    # 调度器配置
    scheduler:
        enabled: false # 在测试中禁用调度器避免冲突

    # Actuator配置 - 完全禁用以避免端口冲突
    actuators:
        endpoints:
            enabled-by-default: false
            web:
                enabled: false # 在测试中禁用以避免端口冲突
                base-path: "/actuator"
                exposure:
                    include: []
                    exclude: ["*"]
        server:
            port: 0 # 使用动态端口避免冲突
            address: "127.0.0.1"

# 日志配置
logging:
    level:
        root: ERROR # 减少测试输出
        miniboot: WARN
