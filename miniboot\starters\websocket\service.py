#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 核心服务
"""

from typing import Any, Optional

from fastapi import WebSocket
from fastapi.websockets import WebSocketDisconnect
from loguru import logger

from .compression import WebSocketMessageCompressor
from .exceptions import (WebSocketAuthenticationException,
                         WebSocketServiceException)
from .handlers import (WebSocketAuthHandler, WebSocketConnectionHandler,
                       WebSocketMessageHandler)
from .metrics import WebSocketMetrics
from .pool import WebSocketConnectionPool
from .properties import WebSocketProperties
from .session import WebSocketSession, WebSocketSessionManager


class WebSocketService:
    """WebSocket 核心服务

    提供 WebSocket 连接管理、消息处理和会话管理的核心功能.
    这是 WebSocket Starter 的核心服务类,负责协调所有组件的工作.
    """

    def __init__(
        self,
        properties: WebSocketProperties,
        metrics: Optional[WebSocketMetrics] = None,
        connection_pool: Optional[WebSocketConnectionPool] = None,
        message_compressor: Optional[WebSocketMessageCompressor] = None,
    ):
        """初始化 WebSocket 服务

        Args:
            properties: WebSocket 配置属性
            metrics: WebSocket 指标收集器(可选)
            connection_pool: WebSocket 连接池(可选)
            message_compressor: WebSocket 消息压缩器(可选)
        """
        self.properties = properties

        # 初始化核心组件 - 延迟创建以避免循环依赖
        self.session_manager = None
        self.connection_handler = None
        self.message_handler = None
        self.auth_handler = None
        self.metrics = metrics or WebSocketMetrics()

        # 性能优化组件
        self.connection_pool = connection_pool
        self.message_compressor = message_compressor

        # 控制器注册表
        self._controllers: dict[str, Any] = {}

        # 服务状态
        self._initialized = False
        self._running = False

    def initialize_components(self):
        """初始化组件 - 在依赖注入完成后调用"""
        if self._initialized:
            return

        try:
            # 创建默认组件（如果没有通过依赖注入提供）
            if self.session_manager is None:
                self.session_manager = WebSocketSessionManager()
            if self.connection_handler is None:
                self.connection_handler = WebSocketConnectionHandler()
            if self.message_handler is None:
                self.message_handler = WebSocketMessageHandler()
            if self.auth_handler is None:
                from .properties import SecurityConfig
                self.auth_handler = WebSocketAuthHandler(self.properties.security)

            self._initialized = True
        except Exception as e:
            logger.error(f"Failed to initialize WebSocket components: {e}")
            # 创建最小化的组件
            if self.session_manager is None:
                self.session_manager = WebSocketSessionManager()
            self._initialized = True

        logger.debug("WebSocketService initialized")

    async def initialize(self) -> None:
        """初始化 WebSocket 服务

        Raises:
            WebSocketServiceException: 初始化失败时抛出
        """
        if self._initialized:
            logger.warning("WebSocketService is already initialized")
            return

        try:
            logger.info("Initializing WebSocket service...")

            # 验证配置
            self.properties.validate()

            # 初始化组件(如果需要)
            # 这里可以添加其他初始化逻辑

            self._initialized = True
            logger.info("WebSocket service initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize WebSocket service: {e}")
            raise WebSocketServiceException(f"Service initialization failed: {e}", cause=e)

    def register_controller(self, path: str, controller: Any) -> None:
        """注册 WebSocket 控制器

        Args:
            path: WebSocket 路径
            controller: 控制器实例

        Raises:
            WebSocketServiceException: 注册失败时抛出
        """
        try:
            if not path.startswith("/"):
                path = "/" + path

            # 检查路径是否已被注册
            if path in self._controllers:
                logger.warning(f"WebSocket path {path} is already registered, overriding")

            # 注册控制器
            self._controllers[path] = controller

            # 在处理器中注册控制器
            self.connection_handler.register_controller(controller)
            self.message_handler.register_controller(controller)

            logger.info(f"Registered WebSocket controller for path: {path}")

        except Exception as e:
            logger.error(f"Failed to register controller for path {path}: {e}")
            raise WebSocketServiceException(f"Controller registration failed: {e}", cause=e)

    def unregister_controller(self, path: str) -> Optional[Any]:
        """注销 WebSocket 控制器

        Args:
            path: WebSocket 路径

        Returns:
            Optional[Any]: 被注销的控制器,如果不存在返回 None
        """
        if not path.startswith("/"):
            path = "/" + path

        controller = self._controllers.pop(path, None)
        if controller:
            logger.info(f"Unregistered WebSocket controller for path: {path}")

        return controller

    def get_controller(self, path: str) -> Optional[Any]:
        """获取 WebSocket 控制器

        Args:
            path: WebSocket 路径

        Returns:
            Optional[Any]: 控制器实例,如果不存在返回 None
        """
        if not path.startswith("/"):
            path = "/" + path

        return self._controllers.get(path)

    def get_registered_paths(self) -> list[str]:
        """获取所有已注册的路径

        Returns:
            list[str]: 已注册的路径列表
        """
        return list(self._controllers.keys())

    async def handle_websocket(self, websocket: WebSocket, path: str) -> None:
        """处理 WebSocket 连接

        这是处理 WebSocket 连接的主要入口点.

        Args:
            websocket: WebSocket 连接对象
            path: WebSocket 路径

        Raises:
            WebSocketConnectionException: 连接处理失败时抛出
        """
        if not self._initialized:
            await self.initialize()

        session: Optional[WebSocketSession] = None

        try:
            logger.info(f"Handling WebSocket connection for path: {path}")

            # 记录连接尝试
            session_id = str(id(websocket))  # 临时会话ID
            self.metrics.record_connection_attempt(session_id)

            # 1. 检查路径是否有对应的控制器
            controller = self.get_controller(path)
            if not controller:
                logger.warning(f"No controller found for WebSocket path: {path}")
                self.metrics.record_connection_failure(session_id, Exception("Path not found"))
                await websocket.close(code=4004, reason="Path not found")
                return

            # 2. 认证检查
            user_id = None
            if self.properties.security.auth.enabled:
                try:
                    user_id = await self.auth_handler.authenticate(websocket)
                    if not user_id:
                        logger.warning("WebSocket authentication failed")
                        await websocket.close(code=4001, reason="Authentication failed")
                        return
                except WebSocketAuthenticationException as e:
                    logger.warning(f"WebSocket authentication error: {e}")
                    self.metrics.record_connection_failure(session_id, e)
                    await websocket.close(code=4001, reason=str(e))
                    return

            # 3. 检查连接限制
            if not await self._check_connection_limits(user_id):
                logger.warning("WebSocket connection limit exceeded")
                self.metrics.record_connection_failure(session_id, Exception("Connection limit exceeded"))
                await websocket.close(code=1008, reason="Connection limit exceeded")
                return

            # 4. 接受连接
            await websocket.accept()

            # 5. 创建会话
            session = await self.session_manager.create_session(websocket, user_id)
            logger.info(f"WebSocket session created: {session.get_id()}")

            # 记录连接成功
            self.metrics.record_connection_success(session.get_id())
            if user_id:
                self.metrics.record_connection_attempt(session.get_id(), user_id)

            # 6. 处理连接建立事件
            await self.connection_handler.handle_connect(session)

            # 7. 进入消息循环
            await self._message_loop(session)

        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected normally for path: {path}")

        except Exception as e:
            logger.error(f"Error handling WebSocket connection for path {path}: {e}")

            # 处理错误事件
            if session:
                await self.connection_handler.handle_error(session, e)

            # 关闭连接
            try:
                if not websocket.client_state.DISCONNECTED:
                    await websocket.close(code=1011, reason="Internal server error")
            except Exception:
                pass  # 忽略关闭时的异常

        finally:
            # 清理会话
            if session:
                try:
                    # 记录断开连接
                    self.metrics.record_disconnection(session.get_id(), session.get_user_id())

                    # 处理断开连接事件
                    await self.connection_handler.handle_disconnect(session)

                    # 移除会话
                    await self.session_manager.remove_session(session.get_id())
                    logger.info(f"WebSocket session cleaned up: {session.get_id()}")

                except Exception as e:
                    logger.error(f"Error cleaning up session {session.get_id()}: {e}")
                    self.metrics.record_error("session_cleanup_error", str(e))

    async def _check_connection_limits(self, user_id: Optional[str]) -> bool:
        """检查连接限制

        Args:
            user_id: 用户 ID

        Returns:
            bool: 是否允许连接
        """
        if not self.properties.connection_limit.enabled:
            return True

        # 检查总连接数限制
        total_connections = await self.session_manager.get_session_count()
        if total_connections >= self.properties.connection_limit.max_total_connections:
            logger.warning(f"Total connection limit exceeded: {total_connections}")
            return False

        # 检查单用户连接数限制
        if user_id:
            user_sessions = await self.session_manager.get_user_sessions(user_id)
            if len(user_sessions) >= self.properties.connection_limit.max_connections_per_user:
                logger.warning(f"User connection limit exceeded for user {user_id}: {len(user_sessions)}")
                return False

        return True

    async def _message_loop(self, session: WebSocketSession) -> None:
        """消息循环处理

        Args:
            session: WebSocket 会话
        """
        try:
            while session.is_active():
                try:
                    # 接收消息
                    message = await session.websocket.receive()

                    # 处理不同类型的消息
                    if message["type"] == "websocket.receive":
                        if "text" in message:
                            await self.message_handler.handle_text_message(session, message["text"])
                        elif "bytes" in message:
                            await self.message_handler.handle_binary_message(session, message["bytes"])

                    elif message["type"] == "websocket.disconnect":
                        logger.info(f"WebSocket disconnect message received for session: {session.get_id()}")
                        break

                except WebSocketDisconnect:
                    logger.info(f"WebSocket disconnected for session: {session.get_id()}")
                    break

                except Exception as e:
                    logger.error(f"Error in message loop for session {session.get_id()}: {e}")
                    await self.connection_handler.handle_error(session, e)
                    break

        except Exception as e:
            logger.error(f"Fatal error in message loop for session {session.get_id()}: {e}")
            await self.connection_handler.handle_error(session, e)

    async def broadcast_message(self, message: Any, exclude_sessions: Optional[set[str]] = None) -> int:
        """广播消息给所有活跃会话

        Args:
            message: 要广播的消息
            exclude_sessions: 要排除的会话 ID 集合

        Returns:
            int: 成功发送的会话数量
        """
        return await self.session_manager.broadcast(message, exclude_sessions)

    async def send_to_user(self, user_id: str, message: Any) -> int:
        """发送消息给指定用户的所有会话

        Args:
            user_id: 用户 ID
            message: 要发送的消息

        Returns:
            int: 成功发送的会话数量
        """
        return await self.session_manager.send_to_user(user_id, message)

    async def get_service_status(self) -> dict:
        """获取服务状态

        Returns:
            dict: 服务状态信息
        """
        active_sessions = await self.session_manager.get_session_count()

        return {
            "initialized": self._initialized,
            "running": self._running,
            "active_sessions": active_sessions,
            "registered_controllers": len(self._controllers),
            "registered_paths": self.get_registered_paths(),
            "connection_limits": {
                "enabled": self.properties.connection_limit.enabled,
                "max_total": self.properties.connection_limit.max_total_connections,
                "max_per_user": self.properties.connection_limit.max_connections_per_user,
            },
            "security": {"auth_enabled": self.properties.security.auth.enabled, "jwt_enabled": self.properties.security.jwt.enabled},
        }

    async def cleanup_inactive_sessions(self) -> int:
        """清理不活跃的会话

        Returns:
            int: 清理的会话数量
        """
        from datetime import timedelta

        max_idle_time = timedelta(seconds=self.properties.server.idle_timeout)
        return await self.session_manager.cleanup_inactive_sessions(max_idle_time)

    async def shutdown(self) -> None:
        """关闭 WebSocket 服务"""
        try:
            logger.info("Shutting down WebSocket service...")

            # 关闭所有会话
            await self.session_manager.close_all_sessions(code=1001, reason="Service shutdown")

            # 清空控制器
            self._controllers.clear()

            # 清空处理器
            self.connection_handler.clear_handlers()
            self.message_handler.clear_handlers()

            self._running = False
            self._initialized = False

            logger.info("WebSocket service shutdown completed")

        except Exception as e:
            logger.error(f"Error during WebSocket service shutdown: {e}")
            raise WebSocketServiceException(f"Service shutdown failed: {e}", cause=e)
