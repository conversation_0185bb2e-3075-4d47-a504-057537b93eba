#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Scheduler autoconfigure simple unit tests - basic testing for Scheduler module auto-configuration
"""

import unittest
from unittest.mock import MagicMock, patch

from miniboot.starters.actuator.autoconfigure.scheduler import (
    SchedulerMetrics, SchedulerMetricsAutoConfiguration,
    SchedulerMetricsCollector)


class SchedulerMetricsTestCase(unittest.TestCase):
    """Scheduler metrics data class unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.metrics = SchedulerMetrics()

    def test_metrics_initialization(self) -> None:
        """Test Scheduler metrics initialization"""
        self.assertEqual(self.metrics.total_tasks, 0)
        self.assertEqual(self.metrics.completed_tasks, 0)
        self.assertEqual(self.metrics.failed_tasks, 0)
        self.assertEqual(self.metrics.total_execution_time, 0.0)

    def test_calculate_derived_metrics_with_data(self) -> None:
        """Test calculating derived metrics with actual data"""
        # Set up test data
        self.metrics.total_tasks = 100
        self.metrics.completed_tasks = 95
        self.metrics.failed_tasks = 5
        self.metrics.total_execution_time = 50.0

        # Calculate derived metrics
        self.metrics.calculate_derived_metrics()

        # Verify calculations exist (actual calculation logic may vary)
        self.assertIsNotNone(self.metrics)

    def test_calculate_derived_metrics_with_zero_data(self) -> None:
        """Test calculating derived metrics with zero data"""
        # All values are zero by default
        self.metrics.calculate_derived_metrics()

        # Should not raise division by zero errors
        self.assertIsNotNone(self.metrics)


class SchedulerMetricsCollectorTestCase(unittest.TestCase):
    """Scheduler metrics collector unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.collector = SchedulerMetricsCollector()

    def test_collector_initialization(self) -> None:
        """Test Scheduler metrics collector initialization"""
        self.assertIsInstance(self.collector._metrics, SchedulerMetrics)

    def test_get_collector_name(self) -> None:
        """Test getting collector name"""
        name = self.collector.get_collector_name()
        self.assertEqual(name, "SchedulerMetricsCollector")

    def test_get_supported_metrics(self) -> None:
        """Test getting supported metrics list"""
        metrics = self.collector.get_supported_metrics()

        self.assertIsInstance(metrics, list)
        self.assertGreater(len(metrics), 0)

    def test_is_available(self) -> None:
        """Test checking collector availability"""
        # Scheduler metrics collector should always be available
        self.assertTrue(self.collector.is_available())

    def test_collect_metrics_basic(self) -> None:
        """Test basic metrics collection"""
        # Should not raise exception even with no registered components
        metrics_data = self.collector.collect_metrics()

        # Should return a list of MetricsData
        self.assertIsInstance(metrics_data, list)

    def test_reset_metrics(self) -> None:
        """Test resetting metrics"""
        # Set some initial data
        self.collector._metrics.total_tasks = 50
        self.collector._metrics.completed_tasks = 45

        # Reset metrics
        self.collector.reset_metrics()

        # Verify reset
        self.assertEqual(self.collector._metrics.total_tasks, 0)
        self.assertEqual(self.collector._metrics.completed_tasks, 0)


class SchedulerMetricsAutoConfigurationTestCase(unittest.TestCase):
    """Scheduler metrics auto-configuration unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.config = SchedulerMetricsAutoConfiguration()

    def test_configuration_initialization(self) -> None:
        """Test auto-configuration initialization"""
        self.assertIsInstance(self.config, SchedulerMetricsAutoConfiguration)

    def test_get_metadata(self) -> None:
        """Test getting configuration metadata"""
        metadata = self.config.get_metadata()

        self.assertEqual(metadata.name, "scheduler-metrics-auto-configuration")
        self.assertIn("异步调度模块指标采集自动配置", metadata.description)
        self.assertEqual(metadata.priority, 200)
        self.assertIn("actuator-starter-auto-configuration", metadata.auto_configure_after)

    def test_scheduler_metrics_collector_creation(self) -> None:
        """Test Scheduler metrics collector Bean creation"""
        collector = self.config.scheduler_metrics_collector()

        self.assertIsInstance(collector, SchedulerMetricsCollector)
        self.assertEqual(collector.get_collector_name(), "SchedulerMetricsCollector")
        self.assertTrue(collector.is_available())

    @patch('miniboot.starters.actuator.autoconfigure.scheduler.logger')
    def test_bean_creation_logging(self, mock_logger) -> None:
        """Test logging during Bean creation"""
        self.config.scheduler_metrics_collector()

        # Verify debug log was called
        mock_logger.debug.assert_called_with("Created SchedulerMetricsCollector bean")


if __name__ == "__main__":
    unittest.main()
