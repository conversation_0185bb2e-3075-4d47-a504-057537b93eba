#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket Starter 异常定义
"""

from typing import Optional


class WebSocketException(Exception):
    """WebSocket 异常基类

    所有 WebSocket 相关异常的基类,提供统一的异常处理机制.
    """

    def __init__(self, message: str, code: int = 1000, cause: Optional[Exception] = None):
        """初始化 WebSocket 异常

        Args:
            message: 异常消息
            code: WebSocket 关闭代码
            cause: 引起异常的原因
        """
        super().__init__(message)
        self.message = message
        self.code = code
        self.cause = cause

    def __str__(self) -> str:
        """返回异常的字符串表示"""
        if self.cause:
            return f"{self.message} (caused by: {self.cause})"
        return self.message


class WebSocketConnectionException(WebSocketException):
    """WebSocket 连接异常

    当 WebSocket 连接建立、维护或关闭过程中发生错误时抛出.
    """

    def __init__(self, message: str = "WebSocket connection error", cause: Optional[Exception] = None):
        super().__init__(message, 1006, cause)


class WebSocketAuthenticationException(WebSocketException):
    """WebSocket 认证异常

    当 WebSocket 连接认证失败时抛出.
    """

    def __init__(self, message: str = "WebSocket authentication failed", cause: Optional[Exception] = None):
        super().__init__(message, 4001, cause)


class WebSocketAuthorizationException(WebSocketException):
    """WebSocket 授权异常

    当 WebSocket 连接授权失败时抛出.
    """

    def __init__(self, message: str = "WebSocket authorization failed", cause: Optional[Exception] = None):
        super().__init__(message, 4003, cause)


class WebSocketMessageException(WebSocketException):
    """WebSocket 消息异常

    当 WebSocket 消息处理过程中发生错误时抛出.
    """

    def __init__(self, message: str = "WebSocket message error", cause: Optional[Exception] = None):
        super().__init__(message, 1003, cause)


class WebSocketSessionException(WebSocketException):
    """WebSocket 会话异常

    当 WebSocket 会话管理过程中发生错误时抛出.
    """

    def __init__(self, message: str = "WebSocket session error", cause: Optional[Exception] = None):
        super().__init__(message, 1000, cause)


class WebSocketControllerException(WebSocketException):
    """WebSocket 控制器异常

    当 WebSocket 控制器处理过程中发生错误时抛出.
    """

    def __init__(self, message: str = "WebSocket controller error", cause: Optional[Exception] = None):
        super().__init__(message, 1011, cause)


class WebSocketConfigurationException(WebSocketException):
    """WebSocket 配置异常

    当 WebSocket 配置无效或配置过程中发生错误时抛出.
    """

    def __init__(self, message: str = "WebSocket configuration error", cause: Optional[Exception] = None):
        super().__init__(message, 1011, cause)


class WebSocketServiceException(WebSocketException):
    """WebSocket 服务异常

    当 WebSocket 服务运行过程中发生错误时抛出.
    """

    def __init__(self, message: str = "WebSocket service error", cause: Optional[Exception] = None):
        super().__init__(message, 1011, cause)


class WebSocketTimeoutException(WebSocketException):
    """WebSocket 超时异常

    当 WebSocket 操作超时时抛出.
    """

    def __init__(self, message: str = "WebSocket operation timeout", cause: Optional[Exception] = None):
        super().__init__(message, 1000, cause)


class WebSocketLimitExceededException(WebSocketException):
    """WebSocket 限制超出异常

    当超出 WebSocket 连接限制时抛出.
    """

    def __init__(self, message: str = "WebSocket connection limit exceeded", cause: Optional[Exception] = None):
        super().__init__(message, 1008, cause)
