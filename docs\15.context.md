# Mini-Boot 应用上下文设计

## 1. 概述

应用上下文（ApplicationContext）是 Mini-Boot 框架的核心组件，负责管理整个应用的生命周期和资源。它是连接各个模块的中枢，提供了 Bean 容器管理、环境配置、事件发布、异步执行和调度系统等核心功能。应用上下文的设计借鉴了 Spring 框架的理念，但针对 Python 语言的特性进行了优化和调整。

应用上下文的主要特点：

-   提供完整的 IoC 容器功能，管理 Bean 的注册、创建和生命周期
-   集成环境配置系统，支持多环境配置和属性解析
-   内置事件发布与订阅机制，支持同步和异步事件处理
-   支持 Bean 的条件化创建和排序初始化
-   集成异步执行、调度系统和监控功能
-   提供统一的应用启动和关闭流程
-   充分利用 Python 的 async/await 特性

## 1.1 目录结构

```
miniboot/context/
├── __init__.py                     # 上下文模块导出
├── application.py                  # 应用上下文接口和实现
├── lifecycle.py                    # 生命周期管理
├── registry.py                     # Bean注册表
├── factory.py                      # Bean工厂
├── scanner.py                      # 类型扫描器
├── conditions.py                   # 条件化Bean创建
└── exceptions.py                   # 上下文异常
```

## 2. 核心组件

### 2.1 ApplicationContext 接口

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, TypeVar
import asyncio
from contextlib import asynccontextmanager

T = TypeVar('T')

class ApplicationContext(ABC):
    """应用上下文接口"""

    @abstractmethod
    async def start(self) -> None:
        """启动应用上下文"""
        pass

    @abstractmethod
    async def stop(self) -> None:
        """停止应用上下文"""
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查应用上下文是否处于运行状态"""
        pass

    @abstractmethod
    def register_type(self, cls: Type[T], name: Optional[str] = None) -> None:
        """注册Bean类型到容器中"""
        pass

    @abstractmethod
    def get_bean(self, name: str) -> Any:
        """根据名称获取Bean"""
        pass

    @abstractmethod
    def get_bean_by_type(self, cls: Type[T]) -> Optional[T]:
        """根据类型获取Bean"""
        pass

    @abstractmethod
    def get_beans_by_type(self, cls: Type[T]) -> Dict[str, T]:
        """根据类型获取所有Bean"""
        pass

    @abstractmethod
    def contains_bean(self, name: str) -> bool:
        """检查是否包含指定名称的Bean"""
        pass

    @abstractmethod
    def get_property(self, key: str, default: Any = None) -> Any:
        """获取配置属性"""
        pass

    @abstractmethod
    def publish_event(self, event: Any) -> None:
        """发布事件"""
        pass

    @abstractmethod
    async def publish_event_async(self, event: Any) -> None:
        """异步发布事件"""
        pass
```

### 2.2 DefaultApplicationContext 实现

```python
import asyncio
import threading
from typing import Dict, List, Any, Type, Optional, Set
from dataclasses import dataclass, field
import inspect
import logging

@dataclass
class BeanDefinition:
    """Bean定义"""
    name: str
    bean_type: Type
    scope: str = "singleton"
    lazy: bool = False
    order: int = 0
    conditions: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)

class DefaultApplicationContext(ApplicationContext):
    """默认应用上下文实现"""

    def __init__(self, config_path: Optional[str] = None):
        self._running = False
        self._lock = threading.RLock()

        # 核心组件
        self.environment = None
        self.bean_factory = None
        self.bean_registry = None
        self.bean_post_processors = []
        self.type_scanner = None
        self.async_executor = None
        self.scheduler = None
        self.event_publisher = None
        self.actuator = None

        # Bean管理
        self.bean_types: Dict[str, Type] = {}
        self.bean_definitions: Dict[str, BeanDefinition] = {}
        self.singleton_beans: Dict[str, Any] = {}
        self.bean_creation_order: List[str] = []

        # 初始化组件
        self._initialize_components(config_path)

    def _initialize_components(self, config_path: Optional[str] = None):
        """初始化核心组件"""
        # 1. 创建环境配置
        from miniboot.env import StandardEnvironment
        self.environment = StandardEnvironment()
        if config_path:
            self.environment.load_config(config_path)

        # 2. 创建Bean工厂和注册表（使用简化API）
        from miniboot.bean import create_bean_factory, create_bean_registry
        self.bean_registry = create_bean_registry()
        self.bean_factory = create_bean_factory()

        # 3. 创建类型扫描器
        from miniboot.annotations import TypeScanner
        self.type_scanner = TypeScanner(self.environment)

        # 4. 创建事件发布器
        from miniboot.event import ApplicationEventPublisher
        self.event_publisher = ApplicationEventPublisher()

        # 5. 创建异步执行器（如果启用）
        if self.environment.get_property("mini.async.enabled", False):
            from miniboot.async_executor import AsyncExecutor
            self.async_executor = AsyncExecutor()

        # 6. 创建调度器（如果启用）
        if self.environment.get_property("mini.scheduler.enabled", False):
            from miniboot.schedule import MiniBootScheduler, SchedulerProperties
            scheduler_props = SchedulerProperties.from_environment(self.environment)
            self.scheduler = MiniBootScheduler(scheduler_props)

        # 7. 创建监控组件（如果启用）
        if self.environment.get_property("mini.actuator.enabled", False):
            from miniboot.actuator import ActuatorContext
            self.actuator = ActuatorContext()

    async def start(self) -> None:
        """启动应用上下文"""
        if self._running:
            return

        with self._lock:
            if self._running:
                return

            try:
                # 1. 启动核心组件
                await self._start_components()

                # 2. 扫描包，收集Bean类型
                await self._scan_packages()

                # 3. 排序Bean类型
                self._sort_bean_types()

                # 4. 创建Bean定义
                self._create_bean_definitions()

                # 5. 注册Bean后置处理器
                self._register_bean_post_processors()

                # 6. 初始化所有单例Bean
                await self._initialize_singleton_beans()

                # 7. 发布应用启动事件
                from miniboot.event import ApplicationStartedEvent
                await self.publish_event_async(ApplicationStartedEvent(self))

                # 8. 打印Banner
                await self._print_banner()

                self._running = True
                logging.info("Application context started successfully")

            except Exception as e:
                logging.error(f"Failed to start application context: {e}")
                await self.stop()
                raise

    async def stop(self) -> None:
        """停止应用上下文"""
        if not self._running:
            return

        with self._lock:
            if not self._running:
                return

            try:
                # 1. 发布应用停止事件
                from miniboot.event import ApplicationStoppedEvent
                await self.publish_event_async(ApplicationStoppedEvent(self))

                # 2. 销毁所有Bean
                await self._destroy_beans()

                # 3. 停止核心组件
                await self._stop_components()

                self._running = False
                logging.info("Application context stopped successfully")

            except Exception as e:
                logging.error(f"Error stopping application context: {e}")

    def is_running(self) -> bool:
        """检查应用上下文是否处于运行状态"""
        return self._running

    def register_type(self, cls: Type, name: Optional[str] = None) -> None:
        """注册Bean类型到容器中"""
        with self._lock:
            if name is None:
                name = self._get_default_bean_name(cls)

            if name in self.bean_types:
                logging.warning(f"Bean type {name} already registered, skipping")
                return

            self.bean_types[name] = cls
            logging.debug(f"Registered bean type: {name} -> {cls}")

    def get_bean(self, name: str) -> Any:
        """根据名称获取Bean"""
        if name in self.singleton_beans:
            return self.singleton_beans[name]

        if name in self.bean_definitions:
            return self._create_bean(name)

        raise ValueError(f"No bean named '{name}' found")

    def get_bean_by_type(self, cls: Type[T]) -> Optional[T]:
        """根据类型获取Bean"""
        beans = self.get_beans_by_type(cls)
        if not beans:
            return None

        if len(beans) == 1:
            return next(iter(beans.values()))

        # 如果有多个Bean，尝试找到主要的Bean
        for name, bean in beans.items():
            if hasattr(bean.__class__, '__primary__'):
                return bean

        raise ValueError(f"Multiple beans of type {cls} found, but no primary bean defined")

    def get_beans_by_type(self, cls: Type[T]) -> Dict[str, T]:
        """根据类型获取所有Bean"""
        result = {}

        for name, bean in self.singleton_beans.items():
            if isinstance(bean, cls):
                result[name] = bean

        return result

    def contains_bean(self, name: str) -> bool:
        """检查是否包含指定名称的Bean"""
        return name in self.singleton_beans or name in self.bean_definitions

    def get_property(self, key: str, default: Any = None) -> Any:
        """获取配置属性"""
        return self.environment.get_property(key, default)

    def publish_event(self, event: Any) -> None:
        """发布事件"""
        if self.event_publisher:
            self.event_publisher.publish_event(event)

    async def publish_event_async(self, event: Any) -> None:
        """异步发布事件"""
        if self.event_publisher:
            await self.event_publisher.publish_event_async(event)
```

## 3. 生命周期管理

### 3.1 组件启动流程

```python
async def _start_components(self) -> None:
    """启动核心组件"""
    # 1. 启动异步执行器
    if self.async_executor:
        await self.async_executor.start()

    # 2. 启动调度器
    if self.scheduler:
        await self.scheduler.start()

    # 3. 启动监控组件
    if self.actuator:
        await self.actuator.start()

async def _stop_components(self) -> None:
    """停止核心组件"""
    # 1. 停止监控组件
    if self.actuator:
        await self.actuator.stop()

    # 2. 停止调度器
    if self.scheduler:
        await self.scheduler.stop()

    # 3. 停止异步执行器
    if self.async_executor:
        await self.async_executor.stop()
```

### 3.2 包扫描和类型收集

```python
async def _scan_packages(self) -> None:
    """扫描包，收集Bean类型"""
    # 获取扫描包路径
    scan_packages = self.environment.get_property("mini.scan.packages", [""])

    for package in scan_packages:
        await self.type_scanner.scan_package(package)

    # 收集扫描到的类型
    scanned_types = self.type_scanner.get_scanned_types()

    for cls in scanned_types:
        # 检查是否有@Component等注解
        if self._is_component_class(cls):
            self.register_type(cls)

def _is_component_class(self, cls: Type) -> bool:
    """检查是否是组件类"""
    # 检查@Component、@Service、@Repository、@Controller等注解
    return (hasattr(cls, '__component__') or
            hasattr(cls, '__service__') or
            hasattr(cls, '__repository__') or
            hasattr(cls, '__controller__') or
            hasattr(cls, '__configuration__'))
```

### 3.3 Bean 类型排序

```python
def _sort_bean_types(self) -> None:
    """根据@Order注解对Bean类型进行排序"""
    bean_orders = []

    for name, cls in self.bean_types.items():
        order = getattr(cls, '__order__', 0)
        bean_orders.append((name, cls, order))

    # 按order排序
    bean_orders.sort(key=lambda x: x[2])

    # 重建排序后的bean_types
    sorted_bean_types = {}
    for name, cls, _ in bean_orders:
        sorted_bean_types[name] = cls

    self.bean_types = sorted_bean_types
    logging.debug(f"Sorted {len(self.bean_types)} bean types by order")
```

### 3.4 Bean 定义创建

```python
def _create_bean_definitions(self) -> None:
    """创建Bean定义"""
    for name, cls in self.bean_types.items():
        # 检查条件注解
        if not self._check_bean_conditions(cls):
            logging.debug(f"Skipping bean {name} due to unmet conditions")
            continue

        # 创建Bean定义
        bean_def = BeanDefinition(
            name=name,
            bean_type=cls,
            scope=getattr(cls, '__scope__', 'singleton'),
            lazy=getattr(cls, '__lazy__', False),
            order=getattr(cls, '__order__', 0)
        )

        # 分析依赖关系
        bean_def.dependencies = self._analyze_dependencies(cls)

        self.bean_definitions[name] = bean_def
        logging.debug(f"Created bean definition: {name}")

def _check_bean_conditions(self, cls: Type) -> bool:
    """检查Bean创建条件"""
    # 检查@ConditionalOnProperty
    if hasattr(cls, '__conditional_on_property__'):
        condition = getattr(cls, '__conditional_on_property__')
        property_name = condition.get('name')
        having_value = condition.get('having_value')
        match_if_missing = condition.get('match_if_missing', False)

        property_value = self.environment.get_property(property_name)

        if property_value is None:
            return match_if_missing

        if having_value is not None:
            return str(property_value) == str(having_value)

    # 检查@ConditionalOnClass
    if hasattr(cls, '__conditional_on_class__'):
        class_names = getattr(cls, '__conditional_on_class__')
        for class_name in class_names:
            try:
                __import__(class_name)
            except ImportError:
                return False

    return True

def _analyze_dependencies(self, cls: Type) -> List[str]:
    """分析Bean的依赖关系"""
    dependencies = []

    # 分析构造函数参数
    if hasattr(cls, '__init__'):
        sig = inspect.signature(cls.__init__)
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue

            if param.annotation != inspect.Parameter.empty:
                # 根据类型查找Bean名称
                dep_name = self._find_bean_name_by_type(param.annotation)
                if dep_name:
                    dependencies.append(dep_name)

    # 分析@Autowired字段
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name, None)
        if hasattr(attr, '__autowired__'):
            # 根据类型查找Bean名称
            if hasattr(attr, '__autowired_type__'):
                dep_type = getattr(attr, '__autowired_type__')
                dep_name = self._find_bean_name_by_type(dep_type)
                if dep_name:
                    dependencies.append(dep_name)

    return dependencies

def _find_bean_name_by_type(self, target_type: Type) -> Optional[str]:
    """根据类型查找Bean名称"""
    for name, cls in self.bean_types.items():
        if cls == target_type or issubclass(cls, target_type):
            return name
    return None
```

### 3.5 Bean 后置处理器注册

```python
def _register_bean_post_processors(self) -> None:
    """注册Bean后置处理器"""
    from miniboot.processor import (
        ConfigurationPropertiesProcessor,
        ValueAnnotationProcessor,
        AutowiredAnnotationProcessor,
        LifecycleAnnotationProcessor,
        EventListenerProcessor,
        AsyncAnnotationProcessor,
        ScheduleProcessor
    )

    # 注册配置属性处理器
    self.bean_post_processors.append(
        ConfigurationPropertiesProcessor(self.environment)
    )

    # 注册值注入处理器
    self.bean_post_processors.append(
        ValueAnnotationProcessor(self.environment)
    )

    # 注册自动装配处理器
    self.bean_post_processors.append(
        AutowiredAnnotationProcessor(self)
    )

    # 注册生命周期处理器
    self.bean_post_processors.append(
        LifecycleAnnotationProcessor()
    )

    # 注册事件监听处理器
    if self.event_publisher:
        self.bean_post_processors.append(
            EventListenerProcessor(self.event_publisher)
        )

    # 注册异步处理器
    if self.async_executor:
        self.bean_post_processors.append(
            AsyncAnnotationProcessor(self.async_executor)
        )

    # 注册调度处理器
    if self.scheduler:
        self.bean_post_processors.append(
            ScheduleProcessor(self.scheduler)
        )

    # 按优先级排序
    self.bean_post_processors.sort(key=lambda p: p.get_order())

    logging.debug(f"Registered {len(self.bean_post_processors)} bean post processors")
```

## 4. Bean 创建和管理

### 4.1 单例 Bean 初始化

```python
async def _initialize_singleton_beans(self) -> None:
    """初始化所有单例Bean"""
    # 按依赖关系排序Bean定义
    sorted_definitions = self._sort_beans_by_dependencies()

    for bean_def in sorted_definitions:
        if bean_def.scope == 'singleton' and not bean_def.lazy:
            await self._create_and_register_bean(bean_def)

def _sort_beans_by_dependencies(self) -> List[BeanDefinition]:
    """按依赖关系排序Bean定义"""
    # 使用拓扑排序算法
    from collections import defaultdict, deque

    # 构建依赖图
    graph = defaultdict(list)
    in_degree = defaultdict(int)

    for bean_def in self.bean_definitions.values():
        in_degree[bean_def.name] = 0

    for bean_def in self.bean_definitions.values():
        for dep in bean_def.dependencies:
            if dep in self.bean_definitions:
                graph[dep].append(bean_def.name)
                in_degree[bean_def.name] += 1

    # 拓扑排序
    queue = deque([name for name, degree in in_degree.items() if degree == 0])
    sorted_names = []

    while queue:
        current = queue.popleft()
        sorted_names.append(current)

        for neighbor in graph[current]:
            in_degree[neighbor] -= 1
            if in_degree[neighbor] == 0:
                queue.append(neighbor)

    # 检查循环依赖
    if len(sorted_names) != len(self.bean_definitions):
        remaining = set(self.bean_definitions.keys()) - set(sorted_names)
        raise ValueError(f"Circular dependency detected among beans: {remaining}")

    # 返回排序后的Bean定义
    return [self.bean_definitions[name] for name in sorted_names]

async def _create_and_register_bean(self, bean_def: BeanDefinition) -> Any:
    """创建并注册Bean"""
    if bean_def.name in self.singleton_beans:
        return self.singleton_beans[bean_def.name]

    try:
        # 创建Bean实例
        bean_instance = await self._create_bean_instance(bean_def)

        # 应用Bean后置处理器（初始化前）
        for processor in self.bean_post_processors:
            bean_instance = processor.post_process_before_initialization(
                bean_instance, bean_def.name
            )

        # 应用Bean后置处理器（初始化后）
        for processor in self.bean_post_processors:
            bean_instance = processor.post_process_after_initialization(
                bean_instance, bean_def.name
            )

        # 注册到单例容器
        self.singleton_beans[bean_def.name] = bean_instance
        self.bean_creation_order.append(bean_def.name)

        logging.debug(f"Created and registered singleton bean: {bean_def.name}")
        return bean_instance

    except Exception as e:
        logging.error(f"Failed to create bean {bean_def.name}: {e}")
        raise

async def _create_bean_instance(self, bean_def: BeanDefinition) -> Any:
    """创建Bean实例"""
    cls = bean_def.bean_type

    # 获取构造函数参数
    constructor_args = await self._resolve_constructor_dependencies(bean_def)

    # 创建实例
    if asyncio.iscoroutinefunction(cls.__init__):
        instance = await cls(*constructor_args)
    else:
        instance = cls(*constructor_args)

    return instance

async def _resolve_constructor_dependencies(self, bean_def: BeanDefinition) -> List[Any]:
    """解析构造函数依赖"""
    args = []
    cls = bean_def.bean_type

    if hasattr(cls, '__init__'):
        sig = inspect.signature(cls.__init__)
        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue

            if param.annotation != inspect.Parameter.empty:
                # 根据类型查找依赖Bean
                dep_bean = self.get_bean_by_type(param.annotation)
                if dep_bean is not None:
                    args.append(dep_bean)
                elif param.default == inspect.Parameter.empty:
                    raise ValueError(f"Cannot resolve dependency {param.annotation} for bean {bean_def.name}")

    return args

def _create_bean(self, name: str) -> Any:
    """创建Bean（用于非单例Bean）"""
    if name not in self.bean_definitions:
        raise ValueError(f"No bean definition found for '{name}'")

    bean_def = self.bean_definitions[name]

    # 对于原型Bean，每次都创建新实例
    if bean_def.scope == 'prototype':
        return asyncio.run(self._create_bean_instance(bean_def))

    # 对于单例Bean，如果还未创建则创建
    if name not in self.singleton_beans:
        return asyncio.run(self._create_and_register_bean(bean_def))

    return self.singleton_beans[name]
```

### 4.2 Bean 销毁流程

```python
async def _destroy_beans(self) -> None:
    """销毁所有Bean"""
    # 按创建顺序的逆序销毁Bean
    for bean_name in reversed(self.bean_creation_order):
        if bean_name in self.singleton_beans:
            await self._destroy_bean(bean_name)

async def _destroy_bean(self, bean_name: str) -> None:
    """销毁单个Bean"""
    try:
        bean_instance = self.singleton_beans.get(bean_name)
        if bean_instance is None:
            return

        # 调用PreDestroy方法
        if hasattr(bean_instance, '__pre_destroy_methods__'):
            pre_destroy_methods = getattr(bean_instance, '__pre_destroy_methods__')
            for method in pre_destroy_methods:
                try:
                    if asyncio.iscoroutinefunction(method):
                        await method(bean_instance)
                    else:
                        method(bean_instance)
                except Exception as e:
                    logging.error(f"Error calling PreDestroy method on bean {bean_name}: {e}")

        # 从容器中移除
        del self.singleton_beans[bean_name]
        logging.debug(f"Destroyed bean: {bean_name}")

    except Exception as e:
        logging.error(f"Error destroying bean {bean_name}: {e}")
```

### 4.3 工具方法

```python
def _get_default_bean_name(self, cls: Type) -> str:
    """获取默认Bean名称"""
    # 检查是否有自定义名称
    if hasattr(cls, '__component_name__'):
        return getattr(cls, '__component_name__')

    # 使用类名，首字母小写
    class_name = cls.__name__
    return class_name[0].lower() + class_name[1:] if class_name else class_name

async def _print_banner(self) -> None:
    """打印Banner"""
    banner_enabled = self.environment.get_property("mini.banner.enabled", True)
    if not banner_enabled:
        return

    banner_text = self.environment.get_property("mini.banner.text", None)
    if banner_text:
        print(banner_text)
    else:
        # 默认Banner
        print("""
  __  __ _       _       ____              _
 |  \/  (_)     (_)     |  _ \            | |
 | \  / |_ _ __  _ ______| |_) | ___   ___ | |_
 | |\/| | | '_ \| |______|  _ < / _ \ / _ \| __|
 | |  | | | | | | |      | |_) | (_) | (_) | |_
 |_|  |_|_|_| |_|_|      |____/ \___/ \___/ \__|

        """)

    # 打印版本信息
    version = self.environment.get_property("mini.version", "1.0.0")
    print(f"Mini-Boot v{version}")
    print(f"Started in {asyncio.get_event_loop().time():.3f} seconds")
```

## 5. 使用示例

### 5.1 基本使用

```python
import asyncio
from miniboot.context import DefaultApplicationContext
from miniboot.annotations import Component, Autowired, Value

@Component
class DatabaseService:
    def __init__(self):
        self.connection = None

    @Value("${database.url}")
    def set_database_url(self, url: str):
        self.database_url = url

    async def connect(self):
        print(f"Connecting to database: {self.database_url}")
        # 模拟数据库连接
        self.connection = "connected"

@Component
class UserService:
    def __init__(self):
        self.db_service = None

    @Autowired
    def set_database_service(self, db_service: DatabaseService):
        self.db_service = db_service

    async def get_user(self, user_id: int):
        if self.db_service.connection:
            return f"User {user_id} from database"
        return None

async def main():
    # 创建应用上下文
    context = DefaultApplicationContext("config/application.yml")

    try:
        # 启动上下文
        await context.start()

        # 获取Bean
        user_service = context.get_bean("userService")
        user = await user_service.get_user(123)
        print(f"Retrieved: {user}")

    finally:
        # 停止上下文
        await context.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

### 5.2 条件化 Bean 创建

```python
from miniboot.annotations import Component, ConditionalOnProperty, ConditionalOnClass

@Component
@ConditionalOnProperty(name="redis.enabled", having_value="true")
class RedisService:
    """只有当redis.enabled=true时才创建"""

    def __init__(self):
        self.client = None

    async def connect(self):
        print("Connecting to Redis...")
        self.client = "redis_client"

@Component
@ConditionalOnClass("aiohttp.ClientSession")
class HttpService:
    """只有当aiohttp可用时才创建"""

    async def get(self, url: str):
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                return await response.text()
```

### 5.3 事件驱动示例

```python
from miniboot.annotations import Component, EventListener, EnableEvent
from miniboot.event import ApplicationEvent

class UserRegisteredEvent(ApplicationEvent):
    def __init__(self, username: str):
        super().__init__()
        self.username = username

@Component
@EnableEvent
class EmailService:
    @EventListener
    async def on_user_registered(self, event: UserRegisteredEvent):
        print(f"Sending welcome email to {event.username}")
        # 发送邮件逻辑

@Component
@EnableEvent
class AuditService:
    @EventListener
    def on_user_registered(self, event: UserRegisteredEvent):
        print(f"Logging user registration: {event.username}")
        # 审计日志逻辑

@Component
class UserController:
    def __init__(self, context: DefaultApplicationContext):
        self.context = context

    async def register_user(self, username: str):
        # 用户注册逻辑
        print(f"Registering user: {username}")

        # 发布事件
        event = UserRegisteredEvent(username)
        await self.context.publish_event_async(event)
```

### 5.4 定时任务示例

```python
from miniboot.annotations import Component, Scheduled, EnableScheduling

@Component
@EnableScheduling
class TaskService:
    @Scheduled(cron="0 */5 * * * *")  # 每5分钟执行
    async def cleanup_temp_files(self):
        print("Cleaning up temporary files...")
        # 清理逻辑

    @Scheduled(fixed_rate="30s")  # 每30秒执行
    def health_check(self):
        print("Performing health check...")
        # 健康检查逻辑
```

### 5.5 异步处理示例

```python
from miniboot.annotations import Component, Async, EnableAsync

@Component
@EnableAsync
class NotificationService:
    @Async(pool="email-pool")
    async def send_email(self, to: str, subject: str, content: str):
        print(f"Sending email to {to}: {subject}")
        # 模拟发送邮件
        await asyncio.sleep(2)
        print(f"Email sent to {to}")

    @Async(pool="sms-pool")
    def send_sms(self, phone: str, message: str):
        print(f"Sending SMS to {phone}: {message}")
        # 模拟发送短信
        time.sleep(1)
        print(f"SMS sent to {phone}")
```

## 6. 与 Go 版本对比

### 6.1 相似之处

-   都是 IoC 容器的核心组件，管理 Bean 的生命周期
-   都提供环境配置和属性解析功能
-   都支持事件发布与订阅机制
-   都支持 Bean 的条件化创建和排序初始化
-   都集成了多种功能模块（如调度、异步等）

### 6.2 差异之处

| 特性       | Go 版本                | Python 版本               |
| ---------- | ---------------------- | ------------------------- |
| 实现语言   | Go                     | Python                    |
| 并发模型   | goroutine              | async/await + 线程池      |
| 配置方式   | YAML 配置文件          | YAML 配置文件 + dataclass |
| 注解处理   | 基于反射实现           | 基于装饰器和属性标记      |
| 依赖注入   | 字段注入为主           | 构造函数注入、字段注入    |
| AOP 支持   | 有限支持（通过拦截器） | 动态代理和装饰器          |
| 上下文层次 | 单一上下文             | 单一上下文                |
| 类型系统   | 静态类型 + 反射        | 动态类型 + 类型注解       |
| 错误处理   | error 返回值           | Python 异常机制           |
| 生命周期   | 同步启动/停止          | 异步启动/停止             |

### 6.3 使用对比

**Go 版本:**

```go
// 创建应用上下文
ctx, err := ioc.NewApplicationContext()
if err != nil {
    panic(err)
}

// 启动上下文
if err := ctx.Start(context.Background()); err != nil {
    panic(err)
}

// 获取Bean
service, err := ctx.GetBean("userService")
if err != nil {
    panic(err)
}

// 停止上下文
defer ctx.Stop(context.Background())
```

**Python 版本:**

```python
# 创建应用上下文
context = DefaultApplicationContext("config/application.yml")

try:
    # 启动上下文
    await context.start()

    # 获取Bean
    service = context.get_bean("userService")

finally:
    # 停止上下文
    await context.stop()
```

## 7. 总结

Mini-Boot 的应用上下文设计提供了一个功能完整的 IoC 容器实现，它集成了环境配置、Bean 管理、事件系统、异步执行和调度功能等多个模块，为 Python 应用提供了类似 Spring 的开发体验。

### 核心特性

1. **完整的 IoC 容器功能**

    - Bean 的注册、创建和生命周期管理
    - 依赖注入和循环依赖检测
    - 单例和原型 Bean 支持

2. **Python 原生异步支持**

    - 基于 async/await 的异步启动和停止
    - 异步 Bean 创建和初始化
    - 与 asyncio 生态系统深度集成

3. **条件化 Bean 创建**

    - @ConditionalOnProperty 条件注解
    - @ConditionalOnClass 条件注解
    - 灵活的 Bean 创建控制

4. **事件驱动架构**
    - 内置事件发布与订阅机制
    - 支持同步和异步事件处理
    - 应用生命周期事件

### 设计优势

1. **模块化设计**: 各个组件职责清晰，易于扩展和维护
2. **异步优先**: 充分利用 Python 的异步编程特性
3. **类型安全**: 基于类型注解的依赖注入
4. **配置驱动**: 外部化配置和条件化 Bean 创建
5. **事件解耦**: 通过事件系统实现组件间的松耦合

### 适用场景

1. **企业级应用**: 复杂的业务逻辑和依赖关系管理
2. **微服务架构**: 服务间的异步通信和事件处理
3. **Web 应用**: 基于 IoC 的 Web 服务开发
4. **后台服务**: 定时任务和异步处理服务
5. **数据处理**: 事件驱动的数据处理管道

通过应用上下文，Mini-Boot 框架为 Python 应用提供了强大的 IoC 容器功能，简化了复杂应用的开发和维护。该设计借鉴了 Spring 的理念，但针对 Python 的特性进行了优化，提供了更好的异步支持和开发体验。

---

_本文档定义了 Mini-Boot 框架的应用上下文设计，提供完整的 IoC 容器功能。_
