#!/usr/bin/env python
"""
* @author: cz
* @description: Mini-Boot启动横幅模块

启动横幅模块提供应用启动时的信息展示功能,包括:
- 框架版本信息
- 应用信息
- 运行环境信息
- 自定义横幅内容
- 条件控制显示
"""

from .banner import Banner
from .printer import ConsoleBannerPrinter, FileBannerPrinter, LogBannerPrinter
from .properties import BannerConfig, BannerMode
from .resource import BannerResourceLoader, DefaultBannerResource

__all__ = [
    # 核心类
    "Banner",
    "BannerConfig",
    "BannerMode",
    # 打印器
    "ConsoleBannerPrinter",
    "FileBannerPrinter",
    "LogBannerPrinter",
    # 资源加载
    "BannerResourceLoader",
    "DefaultBannerResource",
]

# 版本信息
__version__ = "0.0.4"
