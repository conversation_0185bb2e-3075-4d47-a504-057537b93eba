"""
工具类模块

提供 Mini-Boot 框架的通用工具类和辅助功能.

主要功能:
- 类工具 (ClassUtils) - 类加载、反射等工具
- 反射工具 (ReflectionUtils) - 反射操作工具
- 字符串工具 (StringUtils) - 字符串处理工具
- 集合工具 (CollectionUtils) - 集合操作工具
- 文件工具 (FileUtils) - 文件操作工具
- 单例工具 (SingletonMeta, SingletonFactory) - 线程安全的单例模式工具
- 缓存工具 (BaseCache, WeakReferenceCache, LRUCache, TTLCache) - 统一缓存基础设施
- 异步装饰器 (timeout, retry, cached, concurrent_limit) - 统一异步编程装饰器
"""

# 导入缓存工具
from .cache import (BaseCache, CacheStats, LRUCache, TTLCache,
                    WeakReferenceCache)
# 导入异步装饰器
from .decorators import cached, concurrent_limit, retry, robust_async, timeout
# 导入单例工具
from .singleton import SingletonFactory, SingletonMeta, singleton_class

# 导出的公共接口
__all__ = [
    # 单例工具
    "SingletonMeta",
    "SingletonFactory",
    "singleton_class",
    # 缓存工具
    "BaseCache",
    "WeakReferenceCache",
    "LRUCache",
    "TTLCache",
    "CacheStats",
    # 异步装饰器
    "timeout",
    "retry",
    "cached",
    "concurrent_limit",
    "robust_async",
]
