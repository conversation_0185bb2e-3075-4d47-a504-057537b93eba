"""
Task Scheduler Properties

任务调度配置属性类，定义所有可配置的调度参数。
"""

from dataclasses import dataclass
from typing import Optional

from miniboot.annotations import ConfigurationProperties


@ConfigurationProperties(prefix="miniboot.starters.web.task-scheduler")
@dataclass
class TaskSchedulerProperties:
    """任务调度配置属性
    
    配置前缀: miniboot.starters.web.task-scheduler
    
    示例配置:
    ```yaml
    miniboot:
      starters:
        web:
          task-scheduler:
            enabled: true
            intelligent-scheduling: true
            max-concurrent-tasks: 100
            task-timeout: 30.0
    ```
    """
    
    # ==================== 基础配置 ====================
    enabled: bool = True
    """是否启用任务调度器"""
    
    intelligent_scheduling: bool = True
    """是否启用智能调度（基于负载和优先级）"""
    
    # ==================== 并发控制 ====================
    max_concurrent_tasks: int = 100
    """最大并发任务数量"""
    
    worker_threads: int = 4
    """工作线程数量，用于执行阻塞任务"""
    
    # ==================== 任务配置 ====================
    task_timeout: float = 30.0
    """任务执行超时时间(秒)"""
    
    default_priority: int = 5
    """默认任务优先级 (1-10，数字越大优先级越高)"""
    
    # ==================== 队列配置 ====================
    queue_size: int = 1000
    """任务队列最大大小"""
    
    priority_levels: int = 10
    """优先级等级数量 (1-priority_levels)"""
    
    # ==================== 调度策略 ====================
    scheduling_interval: float = 0.1
    """调度器检查间隔(秒)"""
    
    load_balancing_enabled: bool = True
    """是否启用负载均衡"""
    
    adaptive_concurrency: bool = False
    """是否启用自适应并发控制"""
    
    # ==================== 性能监控 ====================
    enable_metrics: bool = True
    """是否启用性能指标收集"""
    
    metrics_collection_interval: float = 5.0
    """指标收集间隔(秒)"""
    
    task_history_size: int = 1000
    """保留的任务历史记录数量"""
    
    # ==================== 重试机制 ====================
    enable_retry: bool = True
    """是否启用任务重试"""
    
    max_retry_attempts: int = 3
    """最大重试次数"""
    
    retry_delay: float = 1.0
    """重试延迟时间(秒)"""
    
    exponential_backoff: bool = True
    """是否使用指数退避重试策略"""
    
    # ==================== 高级配置 ====================
    enable_task_persistence: bool = False
    """是否启用任务持久化（重启后恢复）"""
    
    cleanup_interval: float = 300.0
    """清理过期任务的间隔(秒)"""
    
    task_result_ttl: float = 3600.0
    """任务结果保留时间(秒)"""
    
    enable_distributed_scheduling: bool = False
    """是否启用分布式调度（多实例协调）"""
    
    def validate(self) -> None:
        """验证配置参数的合理性"""
        if self.max_concurrent_tasks <= 0:
            raise ValueError("max_concurrent_tasks must be positive")
            
        if self.worker_threads <= 0:
            raise ValueError("worker_threads must be positive")
            
        if self.task_timeout <= 0:
            raise ValueError("task_timeout must be positive")
            
        if self.default_priority < 1 or self.default_priority > 10:
            raise ValueError("default_priority must be between 1 and 10")
            
        if self.queue_size <= 0:
            raise ValueError("queue_size must be positive")
            
        if self.priority_levels < 1 or self.priority_levels > 20:
            raise ValueError("priority_levels must be between 1 and 20")
            
        if self.scheduling_interval <= 0:
            raise ValueError("scheduling_interval must be positive")
            
        if self.max_retry_attempts < 0:
            raise ValueError("max_retry_attempts must be non-negative")
            
        if self.retry_delay < 0:
            raise ValueError("retry_delay must be non-negative")
    
    def get_effective_concurrency(self, current_load: Optional[float] = None) -> int:
        """获取有效的并发数量
        
        Args:
            current_load: 当前系统负载 (0.0-1.0)
            
        Returns:
            有效的并发任务数量
        """
        if not self.adaptive_concurrency or current_load is None:
            return self.max_concurrent_tasks
            
        # 根据负载动态调整并发数：负载越高，并发越少
        load_factor = 1.0 - current_load
        adjusted_concurrency = int(self.max_concurrent_tasks * load_factor)
        
        # 确保至少有1个并发任务
        return max(1, min(self.max_concurrent_tasks, adjusted_concurrency))
    
    def get_retry_delay(self, attempt: int) -> float:
        """获取重试延迟时间
        
        Args:
            attempt: 当前重试次数 (从1开始)
            
        Returns:
            重试延迟时间(秒)
        """
        if not self.exponential_backoff:
            return self.retry_delay
            
        # 指数退避：delay * (2 ^ (attempt - 1))
        return self.retry_delay * (2 ** (attempt - 1))