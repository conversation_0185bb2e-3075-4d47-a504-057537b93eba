#!/usr/bin/env python
"""
* @author: cz
* @description: 属性解析器测试
"""

import unittest

from miniboot.env.resolver import PropertyResolver


class MockPropertyResolver(PropertyResolver):
    """模拟属性解析器实现"""

    def __init__(self, properties: dict = None):
        self._properties = properties or {}

    def get_property(self, key: str, default=None):
        return self._properties.get(key, default)

    def contains_property(self, key: str) -> bool:
        return key in self._properties


class PropertyResolverTestCase(unittest.TestCase):
    """属性解析器测试"""

    def setUp(self):
        self.properties = {
            "string.value": "hello",
            "int.value": "123",
            "float.value": "45.67",
            "bool.true": "true",
            "bool.false": "false",
            "list.value": "a,b,c",
            "placeholder.simple": "Hello ${name}!",
            "placeholder.default": "Value: ${missing:default}",
            "placeholder.nested": "${string.value} World",
            "name": "Mini-Boot",
        }
        self.resolver = MockPropertyResolver(self.properties)

    def test_get_property(self):
        """测试获取属性值"""
        self.assertEqual("hello", self.resolver.get_property("string.value"))
        self.assertEqual("123", self.resolver.get_property("int.value"))
        self.assertIsNone(self.resolver.get_property("missing.key"))
        self.assertEqual("default", self.resolver.get_property("missing.key", "default"))

    def test_contains_property(self):
        """测试检查属性是否存在"""
        self.assertTrue(self.resolver.contains_property("string.value"))
        self.assertFalse(self.resolver.contains_property("missing.key"))

    def test_get_property_as_string(self):
        """测试获取字符串类型属性"""
        result = self.resolver.get_property_as("string.value", str)
        self.assertEqual("hello", result)
        self.assertIsInstance(result, str)

    def test_get_property_as_int(self):
        """测试获取整数类型属性"""
        result = self.resolver.get_property_as("int.value", int)
        self.assertEqual(123, result)
        self.assertIsInstance(result, int)

    def test_get_property_as_float(self):
        """测试获取浮点数类型属性"""
        result = self.resolver.get_property_as("float.value", float)
        self.assertEqual(45.67, result)
        self.assertIsInstance(result, float)

    def test_get_property_as_bool(self):
        """测试获取布尔类型属性"""
        true_result = self.resolver.get_property_as("bool.true", bool)
        false_result = self.resolver.get_property_as("bool.false", bool)

        self.assertTrue(true_result)
        self.assertFalse(false_result)
        self.assertIsInstance(true_result, bool)
        self.assertIsInstance(false_result, bool)

    def test_get_property_as_list(self):
        """测试获取列表类型属性"""
        result = self.resolver.get_property_as("list.value", list)
        self.assertEqual(["a", "b", "c"], result)
        self.assertIsInstance(result, list)

    def test_get_property_as_with_default(self):
        """测试获取属性时使用默认值"""
        result = self.resolver.get_property_as("missing.key", int, 999)
        self.assertEqual(999, result)

    def test_get_property_as_same_type(self):
        """测试获取相同类型的属性"""
        # 如果值已经是目标类型,应该直接返回
        resolver = MockPropertyResolver({"test": 123})
        result = resolver.get_property_as("test", int)
        self.assertEqual(123, result)

    def test_resolve_placeholders_simple(self):
        """测试简单占位符解析"""
        result = self.resolver.resolve_placeholders("Hello ${name}!")
        self.assertEqual("Hello Mini-Boot!", result)

    def test_resolve_placeholders_with_default(self):
        """测试带默认值的占位符解析"""
        result = self.resolver.resolve_placeholders("Value: ${missing:default}")
        self.assertEqual("Value: default", result)

    def test_resolve_placeholders_nested(self):
        """测试嵌套占位符解析"""
        result = self.resolver.resolve_placeholders("${string.value} World")
        self.assertEqual("hello World", result)

    def test_resolve_placeholders_multiple(self):
        """测试多个占位符解析"""
        text = "${string.value} ${name} ${int.value}"
        result = self.resolver.resolve_placeholders(text)
        self.assertEqual("hello Mini-Boot 123", result)

    def test_resolve_placeholders_missing_no_default(self):
        """测试缺失属性且无默认值的占位符"""
        result = self.resolver.resolve_placeholders("Value: ${missing.key}")
        self.assertEqual("Value: ${missing.key}", result)  # 保持原样

    def test_resolve_placeholders_no_placeholders(self):
        """测试无占位符的文本"""
        text = "No placeholders here"
        result = self.resolver.resolve_placeholders(text)
        self.assertEqual(text, result)

    def test_resolve_required_placeholders_success(self):
        """测试必需占位符解析成功"""
        result = self.resolver.resolve_required_placeholders("Hello ${name}!")
        self.assertEqual("Hello Mini-Boot!", result)

    def test_resolve_required_placeholders_with_default(self):
        """测试必需占位符解析带默认值"""
        result = self.resolver.resolve_required_placeholders("Value: ${missing:default}")
        self.assertEqual("Value: default", result)

    def test_resolve_required_placeholders_missing(self):
        """测试必需占位符解析失败"""
        with self.assertRaises(ValueError) as context:
            self.resolver.resolve_required_placeholders("Value: ${missing.key}")

        self.assertIn("missing.key", str(context.exception))

    def test_type_conversion_error(self):
        """测试类型转换错误"""
        resolver = MockPropertyResolver({"invalid.int": "not_a_number"})

        with self.assertRaises(ValueError):
            resolver.get_property_as("invalid.int", int)


if __name__ == "__main__":
    unittest.main()
