#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Context autoconfigure simple unit tests - basic testing for Context module auto-configuration
"""

import unittest
from unittest.mock import MagicMock, patch

from miniboot.starters.actuator.autoconfigure.context import (ContextMetrics,
                                                               ContextMetricsAutoConfiguration,
                                                               ContextMetricsCollector)


class ContextMetricsTestCase(unittest.TestCase):
    """Context metrics data class unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.metrics = ContextMetrics()

    def test_metrics_initialization(self) -> None:
        """Test Context metrics initialization"""
        self.assertEqual(self.metrics.total_startup_time, 0.0)
        self.assertEqual(self.metrics.startup_count, 0)
        self.assertEqual(self.metrics.startup_failures, 0)
        self.assertEqual(len(self.metrics.startup_sequence_times), 0)
        self.assertEqual(self.metrics.total_modules, 0)

    def test_calculate_derived_metrics_with_data(self) -> None:
        """Test calculating derived metrics with actual data"""
        # Set up test data
        self.metrics.startup_count = 3
        self.metrics.startup_failures = 1
        self.metrics.total_modules = 10
        self.metrics.initialized_modules = 8
        self.metrics.failed_modules = 2
        
        # Calculate derived metrics
        self.metrics.calculate_derived_metrics()
        
        # Verify calculations exist (actual calculation logic may vary)
        self.assertIsNotNone(self.metrics)

    def test_calculate_derived_metrics_with_zero_data(self) -> None:
        """Test calculating derived metrics with zero data"""
        # All values are zero by default
        self.metrics.calculate_derived_metrics()
        
        # Should not raise division by zero errors
        self.assertIsNotNone(self.metrics)


class ContextMetricsCollectorTestCase(unittest.TestCase):
    """Context metrics collector unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.collector = ContextMetricsCollector()

    def test_collector_initialization(self) -> None:
        """Test Context metrics collector initialization"""
        self.assertIsInstance(self.collector._metrics, ContextMetrics)

    def test_get_collector_name(self) -> None:
        """Test getting collector name"""
        name = self.collector.get_collector_name()
        self.assertEqual(name, "context-metrics-collector")

    def test_get_supported_metrics(self) -> None:
        """Test getting supported metrics list"""
        metrics = self.collector.get_supported_metrics()
        
        self.assertIsInstance(metrics, list)
        self.assertGreater(len(metrics), 0)

    def test_is_available(self) -> None:
        """Test checking collector availability"""
        # Context metrics collector should always be available
        self.assertTrue(self.collector.is_available())

    def test_collect_metrics_basic(self) -> None:
        """Test basic metrics collection"""
        # Should not raise exception even with no registered components
        metrics_data = self.collector.collect_metrics()
        
        # Should return a list of MetricsData
        self.assertIsInstance(metrics_data, list)

    def test_reset_metrics(self) -> None:
        """Test resetting metrics"""
        # Set some initial data
        self.collector._metrics.startup_count = 5
        self.collector._metrics.total_startup_time = 10.0
        
        # Reset metrics
        self.collector.reset_metrics()
        
        # Verify reset
        self.assertEqual(self.collector._metrics.startup_count, 0)
        self.assertEqual(self.collector._metrics.total_startup_time, 0.0)


class ContextMetricsAutoConfigurationTestCase(unittest.TestCase):
    """Context metrics auto-configuration unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.config = ContextMetricsAutoConfiguration()

    def test_configuration_initialization(self) -> None:
        """Test auto-configuration initialization"""
        self.assertIsInstance(self.config, ContextMetricsAutoConfiguration)

    def test_get_metadata(self) -> None:
        """Test getting configuration metadata"""
        metadata = self.config.get_metadata()
        
        self.assertEqual(metadata.name, "context-metrics-auto-configuration")
        self.assertIn("应用上下文模块指标采集自动配置", metadata.description)
        self.assertEqual(metadata.priority, 200)
        self.assertIn("actuator-starter-auto-configuration", metadata.auto_configure_after)

    def test_context_metrics_collector_creation(self) -> None:
        """Test Context metrics collector Bean creation"""
        collector = self.config.context_metrics_collector()
        
        self.assertIsInstance(collector, ContextMetricsCollector)
        self.assertEqual(collector.get_collector_name(), "context-metrics-collector")
        self.assertTrue(collector.is_available())

    @patch('miniboot.starters.actuator.autoconfigure.context.logger')
    def test_bean_creation_logging(self, mock_logger) -> None:
        """Test logging during Bean creation"""
        self.config.context_metrics_collector()
        
        # Verify debug log was called
        mock_logger.debug.assert_called_with("Created ContextMetricsCollector bean")


if __name__ == "__main__":
    unittest.main()
