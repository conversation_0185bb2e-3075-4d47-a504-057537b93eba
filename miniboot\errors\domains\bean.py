#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean相关异常类

扁平化的Bean异常类设计，直接继承对应的基础分类。
"""

from ..base import ApplicationError, BusinessError, ValidationError


# Bean创建相关异常 (BusinessError)
class BeanCreationError(BusinessError):
    """Bean创建错误 - 在Bean实例化过程中发生的错误"""
    max_attempts = 5  # Bean创建可以多重试几次
    base_delay = 0.5
    strategy = "exponential"


class BeanInitializationError(BusinessError):
    """Bean初始化错误 - 在Bean初始化过程中发生的错误"""
    max_attempts = 3
    base_delay = 1.0


class BeanDestructionError(BusinessError):
    """Bean销毁错误 - 在Bean销毁过程中发生的错误"""
    max_attempts = 2
    base_delay = 0.5


# Bean查找相关异常 (BusinessError)
class BeanNotFoundError(BusinessError):
    """Bean未找到错误 - 找不到指定的Bean定义"""
    retryable = False  # 找不到就是找不到，重试无意义


class MultipleBeanFoundError(BusinessError):
    """多个Bean找到错误 - 期望唯一Bean但找到多个"""
    retryable = False  # 多个Bean的问题重试解决不了


class BeanCurrentlyInCreationError(BusinessError):
    """Bean正在创建中错误 - Bean当前正在创建过程中"""
    max_attempts = 5  # 可能是并发问题，可以重试
    base_delay = 0.1
    strategy = "linear"


# Bean依赖相关异常 (BusinessError)
class BeanCircularDependencyError(BusinessError):
    """Bean循环依赖错误 - 检测到Bean之间的循环依赖"""
    retryable = False  # 循环依赖重试也解决不了


class UnsatisfiedDependencyError(BusinessError):
    """依赖不满足错误 - Bean的依赖无法满足"""
    max_attempts = 3
    base_delay = 1.0


class DependencyInjectionError(BusinessError):
    """依赖注入错误 - 在依赖注入过程中发生的错误"""
    max_attempts = 3
    base_delay = 0.5


# Bean类型相关异常 (ValidationError)
class BeanTypeMismatchError(ValidationError):
    """Bean类型不匹配错误 - Bean的实际类型与期望类型不匹配"""
    # 继承ValidationError的属性：retryable = False


class BeanNotOfRequiredTypeError(ValidationError):
    """Bean不是所需类型错误 - Bean不是所需的类型"""
    # 继承ValidationError的属性：retryable = False


class BeanInstantiationError(ValidationError):
    """Bean实例化错误 - Bean无法实例化（通常是构造函数问题）"""
    # 继承ValidationError的属性：retryable = False


# Bean定义相关异常 (ApplicationError)
class BeanDefinitionError(ApplicationError):
    """Bean定义错误 - Bean定义本身有问题"""
    retryable = False  # 定义错误重试无意义


class BeanDefinitionValidationError(ApplicationError):
    """Bean定义验证错误 - Bean定义验证失败"""
    retryable = False


class BeanDefinitionStoreError(ApplicationError):
    """Bean定义存储错误 - Bean定义存储过程中的错误"""
    max_attempts = 2
    base_delay = 1.0


class BeanDefinitionOverrideError(ApplicationError):
    """Bean定义覆盖错误 - Bean定义被意外覆盖"""
    retryable = False


# Bean工厂相关异常 (ApplicationError)
class BeanFactoryError(ApplicationError):
    """Bean工厂错误 - Bean工厂操作相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class BeanFactoryNotInitializedError(ApplicationError):
    """Bean工厂未初始化错误 - Bean工厂尚未初始化"""
    retryable = False


class BeanPostProcessorError(BusinessError):
    """Bean后处理器错误 - Bean后处理器执行过程中的错误"""
    max_attempts = 3
    base_delay = 0.5


# Bean作用域相关异常 (ValidationError)
class BeanScopeError(ValidationError):
    """Bean作用域错误 - Bean作用域相关的错误"""
    # 继承ValidationError的属性：retryable = False


class InvalidBeanScopeError(ValidationError):
    """无效Bean作用域错误 - 指定的Bean作用域无效"""
    # 继承ValidationError的属性：retryable = False


# Bean属性相关异常 (BusinessError)
class BeanPropertyError(BusinessError):
    """Bean属性错误 - Bean属性设置相关的错误"""
    max_attempts = 2
    base_delay = 0.5


class BeanPropertyAccessError(BusinessError):
    """Bean属性访问错误 - Bean属性访问失败"""
    max_attempts = 2
    base_delay = 0.5


# Bean方法相关异常 (BusinessError)
class BeanMethodInvocationError(BusinessError):
    """Bean方法调用错误 - Bean方法调用失败"""
    max_attempts = 3
    base_delay = 0.5


class BeanMethodNotFoundError(ValidationError):
    """Bean方法未找到错误 - 指定的Bean方法不存在"""
    # 继承ValidationError的属性：retryable = False


# Bean配置相关异常 (ApplicationError)
class BeanConfigurationError(ApplicationError):
    """Bean配置错误 - Bean配置相关的错误"""
    retryable = False


class BeanConfigurationPropertiesError(ApplicationError):
    """Bean配置属性错误 - Bean配置属性相关的错误"""
    retryable = False


# Bean生命周期相关异常 (BusinessError)
class BeanLifecycleError(BusinessError):
    """Bean生命周期错误 - Bean生命周期管理相关的错误"""
    max_attempts = 2
    base_delay = 1.0


class BeanDisposalError(BusinessError):
    """Bean销毁错误 - Bean销毁过程中的错误"""
    max_attempts = 2
    base_delay = 0.5
