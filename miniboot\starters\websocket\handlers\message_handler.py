#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 消息处理器
"""

import json
from typing import Any, Callable, Union

from loguru import logger

from ..annotations import get_websocket_handlers
from ..exceptions import WebSocketMessageException
from ..session import WebSocketSession


class WebSocketMessageHandler:
    """WebSocket 消息处理器

    负责处理不同类型的 WebSocket 消息,包括文本、二进制和 JSON 消息.
    """

    def __init__(self):
        """初始化消息处理器"""
        self._message_handlers: dict[str, list[Callable]] = {"text": [], "binary": [], "json": [], "any": []}
        self._send_to_handlers: list[Callable] = []

    def register_controller(self, controller_instance: Any) -> None:
        """注册 WebSocket 控制器

        Args:
            controller_instance: 控制器实例
        """
        handlers = get_websocket_handlers(controller_instance)

        # 注册消息处理器
        for handler in handlers["message"]:
            message_type = getattr(handler, "__websocket_message_type__", "any")
            if message_type not in self._message_handlers:
                self._message_handlers[message_type] = []
            self._message_handlers[message_type].append(handler)

        # 注册发送目标处理器
        self._send_to_handlers.extend(handlers["send_to"])

        logger.debug(f"Registered WebSocket controller: {controller_instance.__class__.__name__}")

    async def handle_text_message(self, session: WebSocketSession, message: str) -> None:
        """处理文本消息

        Args:
            session: WebSocket 会话
            message: 文本消息
        """
        try:
            # 处理文本消息处理器
            await self._invoke_handlers(self._message_handlers["text"], session, message)

            # 处理通用消息处理器
            await self._invoke_handlers(self._message_handlers["any"], session, message)

        except Exception as e:
            logger.error(f"Error handling text message: {e}")
            raise WebSocketMessageException(f"Failed to handle text message: {e}", cause=e)

    async def handle_binary_message(self, session: WebSocketSession, data: bytes) -> None:
        """处理二进制消息

        Args:
            session: WebSocket 会话
            data: 二进制数据
        """
        try:
            # 处理二进制消息处理器
            await self._invoke_handlers(self._message_handlers["binary"], session, data)

            # 处理通用消息处理器
            await self._invoke_handlers(self._message_handlers["any"], session, data)

        except Exception as e:
            logger.error(f"Error handling binary message: {e}")
            raise WebSocketMessageException(f"Failed to handle binary message: {e}", cause=e)

    async def handle_json_message(self, session: WebSocketSession, data: Any) -> None:
        """处理 JSON 消息

        Args:
            session: WebSocket 会话
            data: JSON 数据
        """
        try:
            # 处理 JSON 消息处理器
            await self._invoke_handlers(self._message_handlers["json"], session, data)

            # 处理通用消息处理器
            await self._invoke_handlers(self._message_handlers["any"], session, data)

        except Exception as e:
            logger.error(f"Error handling JSON message: {e}")
            raise WebSocketMessageException(f"Failed to handle JSON message: {e}", cause=e)

    async def handle_message(self, session: WebSocketSession, message: Union[str, bytes, dict]) -> None:
        """处理通用消息

        Args:
            session: WebSocket 会话
            message: 消息内容
        """
        try:
            if isinstance(message, str):
                # 尝试解析为 JSON
                try:
                    json_data = json.loads(message)
                    await self.handle_json_message(session, json_data)
                except (json.JSONDecodeError, TypeError):
                    # 不是有效的 JSON,作为文本处理
                    await self.handle_text_message(session, message)

            elif isinstance(message, bytes):
                await self.handle_binary_message(session, message)

            elif isinstance(message, (dict, list)):
                await self.handle_json_message(session, message)

            else:
                # 其他类型转换为字符串处理
                await self.handle_text_message(session, str(message))

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            raise WebSocketMessageException(f"Failed to handle message: {e}", cause=e)

    async def _invoke_handlers(self, handlers: list[Callable], session: WebSocketSession, message: Any) -> None:
        """调用消息处理器

        Args:
            handlers: 处理器列表
            session: WebSocket 会话
            message: 消息内容
        """
        for handler in handlers:
            try:
                # 调用处理器方法
                result = await handler(session, message)

                # 处理发送目标
                if hasattr(handler, "__websocket_send_to__"):
                    target = handler.__websocket_send_target__
                    await self._handle_send_to(target, session, result)

            except Exception as e:
                logger.error(f"Error in message handler {handler.__name__}: {e}")
                # 继续处理其他处理器,不中断整个流程

    async def _handle_send_to(self, target: str, session: WebSocketSession, message: Any) -> None:
        """处理发送目标

        Args:
            target: 发送目标
            session: WebSocket 会话
            message: 要发送的消息
        """
        if message is None:
            return

        try:
            if target == "sender":
                # 发送给发送者
                await session.send_json(message)

            elif target == "all":
                # 广播给所有用户(需要会话管理器支持)
                # 这里需要从上下文获取会话管理器
                pass  # 在 WebSocketService 中实现

            elif target == "user":
                # 发送给特定用户(需要从消息中提取用户 ID)
                if isinstance(message, dict) and "user_id" in message:
                    # 这里需要从上下文获取会话管理器
                    pass  # 在 WebSocketService 中实现

            elif target == "room":
                # 发送给房间内的用户(需要房间管理功能)
                if isinstance(message, dict) and "room_id" in message:
                    # 这里需要实现房间管理功能
                    pass  # 在 WebSocketService 中实现

            else:
                logger.warning(f"Unknown send target: {target}")

        except Exception as e:
            logger.error(f"Error handling send target {target}: {e}")

    def get_registered_handlers(self) -> dict[str, int]:
        """获取已注册的处理器统计

        Returns:
            Dict[str, int]: 处理器类型和数量的映射
        """
        return {message_type: len(handlers) for message_type, handlers in self._message_handlers.items() if handlers}

    def clear_handlers(self) -> None:
        """清空所有处理器"""
        for handlers in self._message_handlers.values():
            handlers.clear()
        self._send_to_handlers.clear()

        logger.debug("Cleared all WebSocket message handlers")


class MessageRouter:
    """消息路由器

    根据消息内容和路径进行更精确的消息路由.
    """

    def __init__(self):
        """初始化消息路由器"""
        self._routes: dict[str, list[Callable]] = {}

    def register_route(self, path: str, handler: Callable) -> None:
        """注册消息路由

        Args:
            path: 消息路径
            handler: 处理器方法
        """
        if path not in self._routes:
            self._routes[path] = []
        self._routes[path].append(handler)

    async def route_message(self, path: str, session: WebSocketSession, message: Any) -> bool:
        """路由消息

        Args:
            path: 消息路径
            session: WebSocket 会话
            message: 消息内容

        Returns:
            bool: 是否找到匹配的路由
        """
        handlers = self._routes.get(path, [])

        if not handlers:
            return False

        for handler in handlers:
            try:
                await handler(session, message)
            except Exception as e:
                logger.error(f"Error in route handler for {path}: {e}")

        return True

    def get_routes(self) -> list[str]:
        """获取所有注册的路由

        Returns:
            List[str]: 路由路径列表
        """
        return list(self._routes.keys())


class MessageFilter:
    """消息过滤器

    用于过滤和预处理 WebSocket 消息.
    """

    def __init__(self):
        """初始化消息过滤器"""
        self._filters: list[Callable] = []

    def add_filter(self, filter_func: Callable) -> None:
        """添加过滤器

        Args:
            filter_func: 过滤器函数,返回 True 表示通过,False 表示拒绝
        """
        self._filters.append(filter_func)

    async def filter_message(self, session: WebSocketSession, message: Any) -> bool:
        """过滤消息

        Args:
            session: WebSocket 会话
            message: 消息内容

        Returns:
            bool: 是否通过过滤
        """
        for filter_func in self._filters:
            try:
                if not await filter_func(session, message):
                    return False
            except Exception as e:
                logger.error(f"Error in message filter: {e}")
                return False

        return True

    def clear_filters(self) -> None:
        """清空所有过滤器"""
        self._filters.clear()
