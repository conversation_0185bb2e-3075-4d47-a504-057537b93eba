#!/usr/bin/env python
"""
异步编程工具函数 - 简化版

提供核心的异步处理工具函数,简化异步编程.
"""

import asyncio
import functools
import threading
import time
from collections.abc import Awaitable
from contextlib import asynccontextmanager
from typing import Any, Callable, Optional, TypeVar

from loguru import logger

# 类型变量
F = TypeVar("F", bound=Callable[..., Any])

from .pool import ThreadPoolManager

T = TypeVar("T")


def async_exception_handler(component_name: str = "", log_level: str = "ERROR", reraise: bool = True):
    """通用异步组件异常处理装饰器

    Args:
        component_name: 组件名称，用于日志记录
        log_level: 日志级别，默认为ERROR
        reraise: 是否重新抛出异常，默认为True

    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 获取组件名称和记录日志
                comp_name = component_name or (args[0]._component_name if hasattr(args[0], '_component_name') else args[0].__class__.__name__)
                log_msg = f"Error in {comp_name}.{func.__name__}: {e}"
                if log_level.upper() == "ERROR":
                    logger.error(log_msg)
                # 是否重新抛出异常
                if reraise:
                    raise
                return None
        return wrapper
    return decorator


async def run_sync(func: Callable[..., T], *args, pool_name: Optional[str] = None, **kwargs) -> T:
    """
    将同步函数在线程池中异步执行

    Args:
        func: 要执行的同步函数
        *args: 函数位置参数
        pool_name: 线程池名称,None表示使用默认池
        **kwargs: 函数关键字参数

    Returns:
        函数执行结果

    Example:
        # 在线程池中执行CPU密集型任务
        result = await run_sync(expensive_calculation, 1000000)

        # 使用指定线程池
        result = await run_sync(io_task, pool_name="io-pool")
    """
    manager = ThreadPoolManager()

    future = manager.submit(pool_name, func, *args, **kwargs) if pool_name else manager.submit_default(func, *args, **kwargs)

    # 将Future转换为awaitable
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, lambda: future.result())


async def gather(limit: int, *awaitables: Awaitable[T]) -> list[T]:
    """
    并发控制的gather函数,限制同时执行的协程数量

    Args:
        limit: 最大并发数
        *awaitables: 要执行的协程

    Returns:
        所有协程的执行结果列表

    Example:
        # 限制最多同时执行5个协程
        results = await gather(5,
            fetch_data(1), fetch_data(2), fetch_data(3), ...)
    """
    if not awaitables:
        return []

    if limit <= 0:
        raise ValueError("并发限制必须大于0")

    # 创建信号量控制并发
    semaphore = asyncio.Semaphore(limit)

    async def _run_with_semaphore(awaitable: Awaitable[T]) -> T:
        async with semaphore:
            return await awaitable

    # 包装所有协程
    wrapped_awaitables = [_run_with_semaphore(aw) for aw in awaitables]

    # 并发执行
    return await asyncio.gather(*wrapped_awaitables)


def timeout(timeout_seconds: float):
    """
    超时包装装饰器,为异步函数添加超时控制

    Args:
        timeout_seconds: 超时时间(秒)

    Returns:
        装饰器函数

    Example:
        @timeout_wrapper(5.0)
        async def slow_function():
            await asyncio.sleep(10)  # 会在5秒后超时
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError as err:
                raise asyncio.TimeoutError(f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)") from err

        return wrapper

    return decorator


async def retry(
    func: Callable[..., Awaitable[T]],
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,),
    *args,
    **kwargs,
) -> T:
    """
    异步重试机制

    Args:
        func: 要重试的异步函数
        max_retries: 最大重试次数
        delay: 初始延迟时间(秒)
        backoff_factor: 退避因子(每次重试延迟时间的倍数)
        exceptions: 需要重试的异常类型
        *args: 函数位置参数
        **kwargs: 函数关键字参数

    Returns:
        函数执行结果

    Raises:
        最后一次执行的异常

    Example:
        # 重试网络请求
        result = await retry_async(
            fetch_data,
            max_retries=5,
            delay=0.5,
            exceptions=(aiohttp.ClientError,),
            url="https://api.example.com"
        )
    """
    last_exception = None
    current_delay = delay

    for attempt in range(max_retries + 1):
        try:
            return await func(*args, **kwargs)
        except exceptions as e:
            last_exception = e

            if attempt == max_retries:
                # 最后一次尝试失败,抛出异常
                break

            # 等待后重试
            await asyncio.sleep(current_delay)
            current_delay *= backoff_factor

    # 抛出最后一次的异常
    raise last_exception


class AsyncBatch:
    """异步批处理工具类"""

    def __init__(self, batch_size: int = 10, concurrency: int = 5):
        """
        初始化批处理器

        Args:
            batch_size: 每批处理的项目数量
            concurrency: 并发批次数量
        """
        self.batch_size = batch_size
        self.concurrency = concurrency

    async def process(self, items: list[Any], processor: Callable[[Any], Awaitable[T]]) -> list[T]:
        """
        批量处理项目

        Args:
            items: 要处理的项目列表
            processor: 处理函数

        Returns:
            处理结果列表
        """
        if not items:
            return []

        # 分批
        batches = [items[i : i + self.batch_size] for i in range(0, len(items), self.batch_size)]

        # 并发处理批次
        async def process_batch(batch: list[Any]) -> list[T]:
            return await gather(self.concurrency, *[processor(item) for item in batch])

        # 处理所有批次
        batch_results = await gather(self.concurrency, *[process_batch(batch) for batch in batches])

        # 展平结果
        results = []
        for batch_result in batch_results:
            results.extend(batch_result)

        return results


@asynccontextmanager
async def timer():
    """
    异步计时器上下文管理器

    Example:
        async with async_timer() as timer:
            await some_async_operation()
        print(f"操作耗时: {timer.elapsed:.2f}秒")
    """

    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.elapsed = 0.0

    timer = Timer()
    timer.start_time = time.time()

    try:
        yield timer
    finally:
        timer.end_time = time.time()
        timer.elapsed = timer.end_time - timer.start_time


async def map(func: Callable[[T], Awaitable[Any]], items: list[T], concurrency: int = 10) -> list[Any]:
    """
    异步映射函数,类似于内置的map但支持异步

    Args:
        func: 映射函数
        items: 要映射的项目列表
        concurrency: 并发数

    Returns:
        映射结果列表
    """
    if not items:
        return []

    return await gather(concurrency, *[func(item) for item in items])


async def filter(predicate: Callable[[T], Awaitable[bool]], items: list[T], concurrency: int = 10) -> list[T]:
    """
    异步过滤函数,类似于内置的filter但支持异步

    Args:
        predicate: 过滤谓词函数
        items: 要过滤的项目列表
        concurrency: 并发数

    Returns:
        过滤后的项目列表
    """
    if not items:
        return []

    # 并发执行谓词函数
    results = await gather(concurrency, *[predicate(item) for item in items])

    # 根据结果过滤项目
    return [item for item, keep in zip(items, results) if keep]
