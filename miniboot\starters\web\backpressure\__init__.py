"""
Backpressure Control Starter

背压控制 Starter 组件，提供智能负载管理和流量控制功能。

主要功能:
- 智能背压检测：基于系统负载和响应时间的背压检测
- 流量控制：自适应限流和熔断机制
- 负载均衡：请求分发和负载平衡
- 性能监控：实时性能指标收集和分析
- 自动恢复：故障自动检测和恢复机制

使用方式:
```yaml
miniboot:
  starters:
    web:
      backpressure:
        enabled: true
        circuit-breaker-enabled: true
        rate-limiting-enabled: true
        max-requests-per-second: 1000
```
"""

from .controller import BackpressureController
from .properties import BackpressureProperties

__all__ = [
    "BackpressureController",
    "BackpressureProperties",
]