#!/usr/bin/env python
"""
* @author: cz
* @description: 事件相关注解

实现事件监听和发布相关的注解,包括@EventListener、@AsyncEventListener、@EventPublisher等.
"""

import inspect
from typing import Any, Callable, Optional, Union

from .metadata import EventListenerMetadata


def EventListener(
    func: Optional[Callable] = None, *, event_type: Optional[type] = None, condition: Optional[str] = None, order: int = 0, async_exec: bool = False
) -> Union[Callable, Callable[[Callable], Callable]]:
    """事件监听器注解

    标记方法为事件监听器,当指定类型的事件发布时会自动调用该方法.

    Args:
        func: 被装饰的函数(当不带参数使用时)
        event_type: 监听的事件类型,如果为None则从方法参数推断
        condition: 事件处理条件表达式,支持SpEL表达式
        order: 监听器执行顺序,数值越小优先级越高
        async_exec: 是否异步执行,默认为False

    Returns:
        装饰器函数或装饰后的函数

    Example:
        @EventListener
        def handle_user_created(self, event: UserCreatedEvent):
            pass

        @EventListener(condition="event.user.age >= 18", order=1)
        def handle_adult_user(self, event: UserCreatedEvent):
            pass
    """

    def decorator(f: Callable) -> Callable:
        # 如果没有指定事件类型,尝试从方法参数推断
        inferred_event_type = event_type
        if inferred_event_type is None:
            sig = inspect.signature(f)
            params = list(sig.parameters.values())
            # 跳过self参数,查找事件参数
            for param in params[1:] if len(params) > 1 else params:
                if param.annotation != inspect.Parameter.empty:
                    inferred_event_type = param.annotation
                    break

        # 创建元数据
        metadata = EventListenerMetadata(method=f.__name__, event_type=inferred_event_type, condition=condition, order=order, async_exec=async_exec)

        # 设置注解标记
        f.__is_event_listener__ = True
        f.__event_listener_metadata__ = metadata
        f.__event_type__ = inferred_event_type
        f.__event_condition__ = condition
        f.__event_order__ = order
        f.__event_async__ = async_exec

        return f

    # 如果func不为None,说明是不带参数的装饰器使用方式
    if func is not None:
        return decorator(func)

    # 否则返回装饰器函数
    return decorator


def AsyncEventListener(
    func: Optional[Callable] = None, *, event_type: Optional[type] = None, condition: Optional[str] = None, order: int = 0
) -> Union[Callable, Callable[[Callable], Callable]]:
    """异步事件监听器注解

    标记方法为异步事件监听器,事件处理将在异步环境中执行.
    这是@EventListener(async_exec=True)的便捷形式.

    Args:
        func: 被装饰的函数(当不带参数使用时)
        event_type: 监听的事件类型,如果为None则从方法参数推断
        condition: 事件处理条件表达式
        order: 监听器执行顺序

    Returns:
        装饰器函数或装饰后的函数

    Example:
        @AsyncEventListener
        async def handle_user_created_async(self, event: UserCreatedEvent):
            await self.send_welcome_email(event.user)
    """
    if func is not None:
        return EventListener(func, event_type=event_type, condition=condition, order=order, async_exec=True)

    def decorator(f: Callable) -> Callable:
        return EventListener(f, event_type=event_type, condition=condition, order=order, async_exec=True)

    return decorator


def EventPublisher(func: Callable) -> Callable:
    """事件发布器注入注解

    标记字段或方法参数需要注入事件发布器.

    Returns:
        装饰器函数

    Example:
        class UserService:
            @EventPublisher
            def set_event_publisher(self, publisher):
                self.event_publisher = publisher

            def create_user(self, user_data):
                user = User(**user_data)
                # 发布用户创建事件
                self.event_publisher.publish(UserCreatedEvent(user))
    """
    # 设置注解标记
    func.__is_event_publisher__ = True
    func.__event_publisher_inject__ = True

    return func


# 工具函数


def is_event_listener(method: Callable) -> bool:
    """检查方法是否为事件监听器

    Args:
        method: 要检查的方法

    Returns:
        如果是事件监听器返回True,否则返回False
    """
    return getattr(method, "__is_event_listener__", False)


def needs_publisher(method: Callable) -> bool:
    """检查方法是否需要事件发布器注入

    Args:
        method: 要检查的方法

    Returns:
        如果需要事件发布器注入返回True,否则返回False
    """
    return getattr(method, "__is_event_publisher__", False)


def listener_metadata(method: Callable) -> Optional[EventListenerMetadata]:
    """获取事件监听器元数据

    Args:
        method: 事件监听器方法

    Returns:
        事件监听器元数据,如果不是事件监听器则返回None
    """
    return getattr(method, "__event_listener_metadata__", None)


def event_type(method: Callable) -> Optional[type]:
    """获取事件监听器监听的事件类型

    Args:
        method: 事件监听器方法

    Returns:
        事件类型,如果没有指定则返回None
    """
    return getattr(method, "__event_type__", None)


def event_condition(method: Callable) -> Optional[str]:
    """获取事件监听器的条件表达式

    Args:
        method: 事件监听器方法

    Returns:
        条件表达式,如果没有指定则返回None
    """
    return getattr(method, "__event_condition__", None)


def event_order(method: Callable) -> int:
    """获取事件监听器的执行顺序

    Args:
        method: 事件监听器方法

    Returns:
        执行顺序,默认为0
    """
    return getattr(method, "__event_order__", 0)


def is_async_listener(method: Callable) -> bool:
    """检查事件监听器是否异步执行

    Args:
        method: 事件监听器方法

    Returns:
        如果异步执行返回True,否则返回False
    """
    return getattr(method, "__event_async__", False)


def _find_annotated_methods(cls: type, predicate_func: Callable[[Any], bool]) -> list[str]:
    """通用的注解方法查找函数

    Args:
        cls: 要查找的类
        predicate_func: 判断方法是否符合条件的函数

    Returns:
        符合条件的方法名称列表
    """
    methods = []

    # 遍历类的所有方法
    for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
        if predicate_func(method):
            methods.append(name)

    # 也检查实例方法
    for name in dir(cls):
        if not name.startswith("_"):  # 跳过私有方法
            try:
                attr = getattr(cls, name)
                if callable(attr) and predicate_func(attr) and name not in methods:
                    methods.append(name)
            except (AttributeError, TypeError):
                # 忽略无法访问的属性
                continue

    return methods


def listeners(cls: type) -> list[str]:
    """查找类中的事件监听器方法

    Args:
        cls: 要查找的类

    Returns:
        事件监听器方法名称列表
    """
    return _find_annotated_methods(cls, is_event_listener)


def injects(cls: type) -> list[str]:
    """查找类中需要事件发布器注入的方法

    Args:
        cls: 要查找的类

    Returns:
        需要事件发布器注入的方法名称列表
    """
    return _find_annotated_methods(cls, needs_publisher)


def has_listeners(cls: type) -> bool:
    """检查类是否有事件监听器方法

    Args:
        cls: 要检查的类

    Returns:
        如果有事件监听器返回True,否则返回False
    """
    return len(listeners(cls)) > 0


def has_injects(cls: type) -> bool:
    """检查类是否有需要事件发布器注入的方法

    Args:
        cls: 要检查的类

    Returns:
        如果有事件发布器注入需求返回True,否则返回False
    """
    return len(injects(cls)) > 0


def listener_info(cls: type) -> dict[str, dict[str, Any]]:
    """获取类中所有事件监听器的信息

    Args:
        cls: 要查询的类

    Returns:
        事件监听器信息字典,格式为 {方法名: {event_type, condition, order, async_exec, metadata}}
    """
    info = {}

    for method_name in listeners(cls):
        try:
            method = getattr(cls, method_name)
            info[method_name] = {
                "event_type": event_type(method),
                "condition": event_condition(method),
                "order": event_order(method),
                "async_exec": is_async_listener(method),
                "metadata": listener_metadata(method),
            }
        except (AttributeError, TypeError):
            continue

    return info
