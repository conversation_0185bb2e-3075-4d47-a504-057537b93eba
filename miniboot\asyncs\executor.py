#!/usr/bin/env python
"""
异步执行引擎

定义AsyncExecutor接口和ThreadPoolAsyncExecutor实现,提供统一的异步执行抽象.
"""

import asyncio
import os
import threading
from abc import ABC, abstractmethod
from concurrent.futures import Future
from dataclasses import dataclass, field
from typing import Any, Callable, Optional

from loguru import logger

from ..errors import ExceptionAction, exception_handler
from .metrics import AsyncTaskMetricsCollector, ConfigurablePerformanceMonitor


@dataclass
class UnifiedAsyncExecutorConfig:
    """统一的异步执行器配置类

    整合了 AsyncExecutorProperties 和 AsyncOptimizationConfig 的所有配置项
    """
    # 基础执行器配置
    core_size: int = 2
    max_size: int = 10
    queue_capacity: int = 100
    keep_alive: int = 60
    thread_name_prefix: str = "async-executor"
    allow_core_thread_timeout: bool = True

    # 优化功能配置
    enable_optimization: bool = True
    dynamic_adjustment_enabled: bool = True
    load_threshold_high: float = 0.8
    load_threshold_low: float = 0.3
    adjustment_interval: float = 30.0
    enable_strategy_caching: bool = True
    enable_batch_processing: bool = True

    # 智能调度配置
    intelligent_scheduling: bool = True
    strategy_selection: bool = True
    performance_monitoring: bool = True

    # 性能监控配置
    enable_detailed_history: bool = False
    metrics_cache_size: int = 1000
    batch_update_interval: float = 5.0

    @classmethod
    def from_properties(cls, executor_properties=None, async_optimization_config=None):
        """从现有配置对象创建统一配置

        Args:
            executor_properties: AsyncExecutorProperties 实例
            async_optimization_config: AsyncOptimizationConfig 实例

        Returns:
            UnifiedAsyncExecutorConfig 实例
        """
        config = cls()

        # 从 executor_properties 复制配置
        if executor_properties:
            config.core_size = getattr(executor_properties, 'core_size', config.core_size)
            config.max_size = getattr(executor_properties, 'max_size', config.max_size)
            config.queue_capacity = getattr(executor_properties, 'queue_capacity', config.queue_capacity)
            config.keep_alive = getattr(executor_properties, 'keep_alive', config.keep_alive)
            config.thread_name_prefix = getattr(executor_properties, 'thread_name_prefix', config.thread_name_prefix)
            config.allow_core_thread_timeout = getattr(executor_properties, 'allow_core_thread_timeout', config.allow_core_thread_timeout)
            config.dynamic_adjustment_enabled = getattr(executor_properties, 'dynamic_adjustment_enabled', config.dynamic_adjustment_enabled)
            config.load_threshold_high = getattr(executor_properties, 'load_threshold_high', config.load_threshold_high)
            config.load_threshold_low = getattr(executor_properties, 'load_threshold_low', config.load_threshold_low)
            config.adjustment_interval = getattr(executor_properties, 'adjustment_interval', config.adjustment_interval)
            config.enable_strategy_caching = getattr(executor_properties, 'enable_strategy_caching', config.enable_strategy_caching)
            config.enable_batch_processing = getattr(executor_properties, 'enable_batch_processing', config.enable_batch_processing)

        # 从 async_optimization_config 复制配置
        if async_optimization_config:
            config.intelligent_scheduling = getattr(async_optimization_config, 'intelligent_scheduling', config.intelligent_scheduling)
            config.strategy_selection = getattr(async_optimization_config, 'strategy_selection', config.strategy_selection)
            config.performance_monitoring = getattr(async_optimization_config, 'performance_monitoring', config.performance_monitoring)

        return config





class AsyncExecutor(ABC):
    """异步执行器抽象接口

    定义异步执行器的标准接口,支持同步和异步方法的执行.
    """

    @abstractmethod
    @exception_handler(
        module_name="asyncs",
        component_name="executor",
        action=ExceptionAction.PROPAGATE,
        log_level="ERROR"
    )
    def execute(self, method: Callable, *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """执行方法

        Args:
            method: 要执行的方法
            *args: 位置参数
            timeout: 超时时间(秒)
            **kwargs: 关键字参数

        Returns:
            执行结果
        """
        pass

    @abstractmethod
    def execute_sync(self, method: Callable, *args, timeout: Optional[float] = None, **kwargs) -> Future:
        """执行同步方法

        Args:
            method: 同步方法
            *args: 位置参数
            timeout: 超时时间(秒)
            **kwargs: 关键字参数

        Returns:
            Future对象
        """
        pass

    @abstractmethod
    def execute_async(self, method: Callable, *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """执行异步方法

        Args:
            method: 异步方法
            *args: 位置参数
            timeout: 超时时间(秒)
            **kwargs: 关键字参数

        Returns:
            执行结果
        """
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """检查执行器是否可用

        Returns:
            是否可用
        """
        pass

    @abstractmethod
    def shutdown(self, wait: bool = True, timeout: Optional[float] = None) -> None:
        """关闭执行器

        Args:
            wait: 是否等待任务完成
            timeout: 等待超时时间
        """
        pass

    @abstractmethod
    def get_stats(self) -> dict:
        """获取执行器统计信息

        Returns:
            统计信息字典
        """
        pass


class ThreadPoolAsyncExecutor(AsyncExecutor):
    """基于线程池的异步执行器实现

    使用线程池来执行同步和异步方法,提供完整的异步执行能力.
    """

    def __init__(self, thread_pool, name: str = "default", enable_optimization: bool = True,
                 executor_properties=None, async_optimization_config=None):
        """初始化执行器（整合了优化功能）

        Args:
            thread_pool: 线程池对象
            name: 执行器名称
            enable_optimization: 是否启用智能优化功能
            executor_properties: 异步执行器配置属性（统一配置）
            async_optimization_config: 异步优化配置（将整合到 executor_properties 中）
        """
        self._thread_pool = thread_pool
        self._name = name
        self._shutdown = False
        self._lock = threading.RLock()

        # 合并统计信息（整合了 OptimizationStats）
        self._stats = {
            "total_executions": 0,
            "sync_executions": 0,
            "async_executions": 0,
            "failed_executions": 0,
            "active_tasks": 0,
            "load_balanced_executions": 0,
            "priority_executions": 0,
            "shed_requests": 0,
            # 整合的优化统计
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "failed_optimizations": 0,
            "total_optimization_time": 0.0,
            "average_optimization_time": 0.0,
            "performance_improvement": 0.0,
            "resource_utilization": 0.0,
            "dynamic_adjustments": 0,
            "strategy_selections": 0,
            "performance_measurements": 0
        }
        self._metrics_collector = AsyncTaskMetricsCollector()
        self._performance_monitor = ConfigurablePerformanceMonitor()

        # 整合的智能优化功能（替代独立的 AsyncExecutorOptimizer）
        self._enable_optimization = enable_optimization
        self._optimization_running = False

        # 优化组件（直接整合，不再使用独立的 AsyncExecutorOptimizer）
        self.dynamic_adjuster = None
        self.task_scheduler = None
        self.strategy_selector = None

        if self._enable_optimization:
            try:
                from .properties import AsyncExecutorProperties

                # 创建统一的配置对象
                self.executor_properties = executor_properties or AsyncExecutorProperties()

                # 如果提供了 async_optimization_config，将其整合到 executor_properties 中
                if async_optimization_config:
                    self.executor_properties.update_from_optimization_config(async_optimization_config)

                # 初始化优化组件
                self._init_optimization_components()

                logger.debug(f"Integrated optimization enabled for executor '{self._name}'")

            except Exception as e:
                logger.warning(f"Failed to initialize optimization for executor '{self._name}': {e}")
                self._enable_optimization = False

    def _init_optimization_components(self):
        """初始化优化组件（整合了 AsyncExecutorOptimizer 的逻辑）"""
        try:
            # 1. 动态池调整器
            if self.executor_properties.dynamic_adjustment_enabled:
                from .adjuster import AdjustmentPolicy, DynamicPoolAdjuster
                from .pool import ThreadPoolManager

                adjustment_policy = AdjustmentPolicy(
                    high_load_threshold=self.executor_properties.load_threshold_high,
                    low_load_threshold=self.executor_properties.load_threshold_low,
                    min_adjustment_interval=int(self.executor_properties.adjustment_interval),
                )
                self.dynamic_adjuster = DynamicPoolAdjuster(
                    manager=ThreadPoolManager(),
                    policy=adjustment_policy
                )

            # 2. 智能任务调度器
            if self.executor_properties.intelligent_scheduling:
                from ..web.scheduling.scheduler import TaskScheduler
                self.task_scheduler = TaskScheduler(config=self.executor_properties)

            # 3. 策略选择器
            if self.executor_properties.strategy_selection:
                from ..web.scheduling.selector import StrategySelector
                self.strategy_selector = StrategySelector(config=self.executor_properties)

            logger.debug("Optimization components initialized successfully")

        except Exception as e:
            logger.warning(f"Failed to initialize some optimization components: {e}")

    def start_optimization(self):
        """启动优化组件"""
        if not self._enable_optimization:
            return

        with self._lock:
            if self._optimization_running:
                return

            self._optimization_running = True

            # 启动各个组件
            if self.dynamic_adjuster:
                self.dynamic_adjuster.start()
                logger.debug("Dynamic pool adjuster started")

            if self.task_scheduler:
                self.task_scheduler.start()
                logger.debug("Task scheduler started")

            logger.info(f"Optimization started for executor '{self._name}'")

    def stop_optimization(self):
        """停止优化组件"""
        with self._lock:
            if not self._optimization_running:
                return

            self._optimization_running = False

            # 停止各个组件
            if self.dynamic_adjuster:
                self.dynamic_adjuster.stop()
                logger.debug("Dynamic pool adjuster stopped")

            if self.task_scheduler:
                self.task_scheduler.stop()
                logger.debug("Task scheduler stopped")

            logger.info(f"Optimization stopped for executor '{self._name}'")

    def _optimize_execution(self, method: Callable, args: tuple, kwargs: dict, timeout: Optional[float] = None) -> Any:
        """整合的优化执行逻辑（替代 AsyncExecutorOptimizer.optimize_execution）"""
        import time

        start_time = time.time()
        success = False

        try:
            # 1. 策略选择
            execution_strategy = self._select_execution_strategy(method, args, kwargs)

            # 2. 执行方法
            result = self._execute_with_strategy(method, execution_strategy, args, kwargs, timeout)

            # 3. 更新统计
            with self._lock:
                self._stats["strategy_selections"] += 1
                self._stats["performance_measurements"] += 1
                self._stats["total_optimizations"] += 1
                self._stats["successful_optimizations"] += 1

            success = True
            return result

        except Exception as e:
            with self._lock:
                self._stats["total_optimizations"] += 1
                self._stats["failed_optimizations"] += 1
            logger.warning(f"Optimized execution failed, falling back to simple execution: {e}")
            # 回退到简单执行
            return self._simple_execute(method, args, kwargs, timeout)

        finally:
            duration = time.time() - start_time
            with self._lock:
                self._stats["total_optimization_time"] += duration
                if self._stats["total_optimizations"] > 0:
                    self._stats["average_optimization_time"] = (
                        self._stats["total_optimization_time"] / self._stats["total_optimizations"]
                    )

    def _select_execution_strategy(self, method: Callable, args: tuple, kwargs: dict) -> str:
        """选择执行策略（整合了 AsyncExecutorOptimizer 的逻辑）"""
        if not self.strategy_selector:
            return "default"

        try:
            # 构建任务请求（简化版）
            import time

            from ..web.scheduling.scheduler import TaskRequest

            task_request = TaskRequest(
                function=method,
                args=args,
                kwargs=kwargs,
                task_id=f"opt_{id(method)}_{time.time()}"
            )

            # 分析任务特征（简化版）
            characteristics = {
                "function_name": method.__name__,
                "arg_count": len(args),
                "kwarg_count": len(kwargs),
                "is_coroutine": hasattr(method, "__code__") and method.__code__.co_flags & 0x80,
            }

            # 选择策略
            import asyncio
            if asyncio.iscoroutinefunction(self.strategy_selector.select_strategy):
                # 如果是异步方法，在线程池中执行
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        lambda: asyncio.run(self.strategy_selector.select_strategy(task_request, characteristics))
                    )
                    decision = future.result(timeout=1.0)
            else:
                decision = self.strategy_selector.select_strategy(task_request, characteristics)

            return decision.strategy.value if hasattr(decision, 'strategy') else "default"

        except Exception as e:
            logger.debug(f"Strategy selection failed, using default: {e}")
            return "default"

    def _execute_with_strategy(self, method: Callable, strategy: str, args: tuple, kwargs: dict, timeout: Optional[float] = None) -> Any:
        """根据策略执行方法（整合了 AsyncExecutorOptimizer 的逻辑）"""
        try:
            if strategy == "async" and not hasattr(method, "__code__"):
                # 异步策略但方法不是协程，回退到默认
                strategy = "default"

            # 简化的执行逻辑
            return self._simple_execute(method, args, kwargs, timeout)

        except Exception as e:
            logger.warning(f"Execution with strategy '{strategy}' failed: {e}")
            # 回退到直接执行
            return self._simple_execute(method, args, kwargs, timeout)

    def _simple_execute(self, method: Callable, args: tuple, kwargs: dict, timeout: Optional[float] = None) -> Any:
        """简化的方法执行

        Args:
            method: 要执行的方法
            args: 位置参数
            kwargs: 关键字参数
            timeout: 超时时间

        Returns:
            执行结果
        """
        import asyncio
        import inspect

        # 检查是否为协程函数
        if inspect.iscoroutinefunction(method):
            # 异步方法：创建协程并在线程池中运行
            coro = method(*args, **kwargs)

            def run_coro():
                return asyncio.run(coro)

            future = self._thread_pool.submit(run_coro)
        else:
            # 同步方法：直接在线程池中执行
            future = self._thread_pool.submit(method, *args, **kwargs)

        # 处理超时
        if timeout:
            try:
                return future.result(timeout=timeout)
            except Exception as e:
                future.cancel()
                raise e
        else:
            return future



    def execute(self, method: Callable, *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """执行方法(自动判断同步/异步)

        Args:
            method: 要执行的方法
            *args: 位置参数
            timeout: 超时时间(秒)
            **kwargs: 关键字参数

        Returns:
            执行结果
        """
        if self._shutdown:
            raise RuntimeError(f"Executor '{self._name}' has been shut down")

        method_name = getattr(method, "__name__", str(method))

        # 整合的智能优化执行路径
        if self._enable_optimization and self._optimization_running:
            # 使用整合的优化逻辑执行
            try:
                with self._performance_monitor.measure_execution(self._name, method_name):
                    # 只在启用详细指标时使用传统的指标收集器
                    task_id = None
                    if self._performance_monitor.config.enable_detailed_history:
                        task_id = self._metrics_collector.start_task(
                            method_name=method_name, executor_name=self._name, thread_name=threading.current_thread().name
                        )

                    with self._lock:
                        self._stats["active_tasks"] += 1

                    try:
                        # 使用整合的优化逻辑执行方法
                        result = self._optimize_execution(method, args, kwargs, timeout)

                        # 标记任务成功完成(仅在启用详细历史时)
                        if task_id:
                            self._metrics_collector.complete_task(task_id, success=True)
                        return result

                    except Exception as e:
                        with self._lock:
                            self._stats["failed_executions"] += 1

                        # 标记任务失败(仅在启用详细历史时)
                        if task_id:
                            self._metrics_collector.complete_task(task_id, success=False, error_message=str(e))
                        raise
                    finally:
                        with self._lock:
                            self._stats["active_tasks"] -= 1

            except Exception as e:
                # 如果优化器执行失败，回退到传统执行路径
                logger.debug(f"Optimizer execution failed, falling back to traditional path: {e}")
                # 继续执行传统路径

        # 传统执行路径（向后兼容）
        with self._performance_monitor.measure_execution(self._name, method_name):
            # 只在启用详细指标时使用传统的指标收集器
            task_id = None
            if self._performance_monitor.config.enable_detailed_history:
                task_id = self._metrics_collector.start_task(
                    method_name=method_name, executor_name=self._name, thread_name=threading.current_thread().name
                )

            # 使用优化的执行路由器
            with self._lock:
                self._stats["active_tasks"] += 1

            try:
                # 使用简化的执行方法
                result = self._simple_execute(method, args, kwargs, timeout)

                # 标记任务成功完成(仅在启用详细历史时)
                if task_id:
                    self._metrics_collector.complete_task(task_id, success=True)
                return result

            except Exception as e:
                with self._lock:
                    self._stats["failed_executions"] += 1

                # 标记任务失败(仅在启用详细历史时)
                if task_id:
                    self._metrics_collector.complete_task(task_id, success=False, error_message=str(e))
                # 异常处理由装饰器统一处理
                raise
            finally:
                with self._lock:
                    self._stats["active_tasks"] -= 1

    def execute_sync(self, method: Callable, *args, timeout: Optional[float] = None, **kwargs) -> Future:
        """执行同步方法

        Args:
            method: 同步方法
            *args: 位置参数
            timeout: 超时时间(秒)
            **kwargs: 关键字参数

        Returns:
            Future对象
        """
        if self._shutdown:
            raise RuntimeError(f"Executor '{self._name}' has been shut down")

        with self._lock:
            self._stats["total_executions"] += 1
            self._stats["sync_executions"] += 1
            self._stats["active_tasks"] += 1

        try:

            def task():
                try:
                    logger.debug(f"Executing sync method {method.__name__} in executor '{self._name}'")
                    return method(*args, **kwargs)
                except Exception as e:
                    logger.error(f"Sync method {method.__name__} failed: {e}")
                    raise

            # 提交到线程池
            future = self._thread_pool.submit(task)

            # 如果有超时,包装Future
            if timeout:
                return self._wrap_future_with_timeout(future, timeout)

            return future
        finally:
            with self._lock:
                self._stats["active_tasks"] -= 1

    def execute_async(self, method: Callable, *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """执行异步方法

        Args:
            method: 异步方法
            *args: 位置参数
            timeout: 超时时间(秒)
            **kwargs: 关键字参数

        Returns:
            执行结果
        """
        if self._shutdown:
            raise RuntimeError(f"Executor '{self._name}' has been shut down")

        with self._lock:
            self._stats["total_executions"] += 1
            self._stats["async_executions"] += 1
            self._stats["active_tasks"] += 1

        try:
            # 创建协程
            coro = method(*args, **kwargs)
            logger.debug(f"Executing async method {method.__name__} in executor '{self._name}'")

            # 检查是否在事件循环中
            try:
                loop = asyncio.get_running_loop()
                # 在事件循环中,使用线程池执行
                return loop.run_in_executor(self._thread_pool._executor, self._run_coroutine_in_thread, coro, timeout)
            except RuntimeError:
                # 没有运行的事件循环,直接运行
                return asyncio.run(self._run_with_timeout(coro, timeout))

        except Exception as e:
            logger.error(f"Async method {method.__name__} failed: {e}")
            raise
        finally:
            with self._lock:
                self._stats["active_tasks"] -= 1

    def _run_coroutine_in_thread(self, coro, timeout: Optional[float]) -> Any:
        """在线程中运行协程

        Args:
            coro: 协程对象
            timeout: 超时时间

        Returns:
            执行结果
        """
        try:
            return asyncio.run(self._run_with_timeout(coro, timeout))
        except Exception as e:
            logger.error(f"Coroutine execution in thread failed: {e}")
            raise

    async def _run_with_timeout(self, coro, timeout: Optional[float]) -> Any:
        """带超时运行协程

        Args:
            coro: 协程对象
            timeout: 超时时间

        Returns:
            执行结果
        """
        if timeout:
            return await asyncio.wait_for(coro, timeout=timeout)
        else:
            return await coro

    def _wrap_future_with_timeout(self, future: Future, timeout: float) -> Future:
        """为Future添加超时处理

        Args:
            future: 原始Future
            timeout: 超时时间

        Returns:
            包装后的Future
        """

        def timeout_handler():
            if not future.done():
                future.cancel()
                logger.warning(f"Task timed out after {timeout} seconds in executor '{self._name}'")

        # 设置超时定时器
        timer = threading.Timer(timeout, timeout_handler)
        timer.start()

        # 添加完成回调来取消定时器
        def cleanup_timer(_):
            timer.cancel()

        future.add_done_callback(cleanup_timer)

        return future

    def is_available(self) -> bool:
        """检查执行器是否可用

        Returns:
            是否可用
        """
        return not self._shutdown and self._thread_pool and not self._thread_pool._shutdown

    def shutdown(self, wait: bool = True, timeout: Optional[float] = None) -> None:
        """关闭执行器

        Args:
            wait: 是否等待任务完成
            timeout: 等待超时时间
        """
        with self._lock:
            if self._shutdown:
                return

            self._shutdown = True
            logger.info(f"Shutting down executor '{self._name}'")

        # 关闭整合的优化组件
        if self._enable_optimization:
            try:
                self.stop_optimization()
                logger.debug(f"Integrated optimization stopped for executor '{self._name}'")
            except Exception as e:
                logger.warning(f"Failed to stop optimization for executor '{self._name}': {e}")

        if self._thread_pool:
            try:
                # ThreadPoolExecutor.shutdown()不接受timeout参数
                self._thread_pool.shutdown(wait=wait)
                logger.debug(f"Executor '{self._name}' shut down successfully")
            except Exception as e:
                logger.error(f"Failed to shutdown executor '{self._name}': {e}")

    def get_stats(self) -> dict:
        """获取执行器统计信息

        Returns:
            统计信息字典
        """
        with self._lock:
            stats = self._stats.copy()

        # 添加线程池统计信息
        if self._thread_pool and hasattr(self._thread_pool, "get_metrics"):
            pool_metrics = self._thread_pool.get_metrics()
            stats.update(
                {
                    "pool_active_threads": pool_metrics.active_count,
                    "pool_task_count": pool_metrics.task_count,
                    "pool_completed_tasks": pool_metrics.completed_task_count,
                    "pool_queue_size": pool_metrics.queue_size,
                }
            )

        stats.update({"name": self._name, "available": self.is_available(), "shutdown": self._shutdown})

        # 执行路由器已简化，无需额外统计

        # 添加整合的优化统计
        if self._enable_optimization:
            stats.update({
                "optimization_enabled": True,
                "optimization_running": self._optimization_running,
                "success_rate": (
                    self._stats["successful_optimizations"] / self._stats["total_optimizations"]
                    if self._stats["total_optimizations"] > 0 else 0.0
                ),
                "dynamic_adjuster_enabled": self.dynamic_adjuster is not None,
                "task_scheduler_enabled": self.task_scheduler is not None,
                "strategy_selector_enabled": self.strategy_selector is not None,
            })
        else:
            stats["optimization_enabled"] = False

        return stats

    @property
    def name(self) -> str:
        """获取执行器名称"""
        return self._name

    @property
    def thread_pool(self):
        """获取底层线程池"""
        return self._thread_pool

    def is_optimization_enabled(self) -> bool:
        """检查是否启用了智能优化功能

        Returns:
            是否启用优化
        """
        return self._enable_optimization

    def get_optimization_stats(self) -> dict:
        """获取整合的优化统计信息

        Returns:
            优化统计信息字典
        """
        if not self._enable_optimization:
            return {"enabled": False, "message": "Optimization not enabled"}

        with self._lock:
            stats = {
                "enabled": self._enable_optimization,
                "running": self._optimization_running,
                "total_optimizations": self._stats["total_optimizations"],
                "success_rate": (
                    self._stats["successful_optimizations"] / self._stats["total_optimizations"]
                    if self._stats["total_optimizations"] > 0 else 0.0
                ),
                "average_optimization_time": self._stats["average_optimization_time"],
                "performance_improvement": self._stats["performance_improvement"],
                "resource_utilization": self._stats["resource_utilization"],
                "dynamic_adjustments": self._stats["dynamic_adjustments"],
                "strategy_selections": self._stats["strategy_selections"],
                "performance_measurements": self._stats["performance_measurements"],
            }

            # 添加组件状态
            stats.update({
                "dynamic_adjuster_enabled": self.dynamic_adjuster is not None,
                "task_scheduler_enabled": self.task_scheduler is not None,
                "strategy_selector_enabled": self.strategy_selector is not None,
            })

            return stats

    def configure_optimization(self, **config_updates):
        """配置整合的优化参数

        Args:
            **config_updates: 配置更新项
        """
        with self._lock:
            # 更新启用状态
            if "enabled" in config_updates:
                self._enable_optimization = config_updates["enabled"]
                logger.info(f"Optimization {'enabled' if self._enable_optimization else 'disabled'}")

            # 更新组件配置
            if "dynamic_adjustment" in config_updates and self.dynamic_adjuster:
                # 更新动态调整配置
                adj_config = config_updates["dynamic_adjustment"]
                if "enabled" in adj_config:
                    if adj_config["enabled"] and not getattr(self.dynamic_adjuster, '_running', False):
                        self.dynamic_adjuster.start()
                    elif not adj_config["enabled"] and getattr(self.dynamic_adjuster, '_running', False):
                        self.dynamic_adjuster.stop()

            logger.debug(f"Optimization configuration updated: {config_updates}")

    def get_component_status(self) -> dict:
        """获取优化组件状态"""
        return {
            "dynamic_adjuster": self.dynamic_adjuster is not None and getattr(self.dynamic_adjuster, '_running', False),
            "task_scheduler": self.task_scheduler is not None and getattr(self.task_scheduler, '_state', None) and self.task_scheduler._state.name != "STOPPED",
            "strategy_selector": self.strategy_selector is not None,
            "performance_monitor": self._performance_monitor is not None,
        }
