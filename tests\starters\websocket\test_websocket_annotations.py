#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 注解系统测试
"""

import unittest

from miniboot.starters.websocket.annotations import (
    WebSocketController,
    WebSocketOnConnect,
    WebSocketOnDisconnect,
    WebSocketOnMessage,
    WebSocketOnError,
    WebSocketSendTo,
    WebSocketMessageMapping,
    WebSocketEventListener,
    is_websocket_controller,
    get_websocket_path,
    get_websocket_handlers,
)
from miniboot.starters.websocket.session import WebSocketSession


class WebSocketAnnotationsTestCase(unittest.TestCase):
    """WebSocket 注解系统测试用例"""

    def test_websocket_controller_annotation(self):
        """测试 WebSocket 控制器注解"""

        @WebSocketController("/test")
        class TestController:
            pass

        # 验证注解属性
        self.assertTrue(hasattr(TestController, "__websocket_controller__"))
        self.assertTrue(TestController.__websocket_controller__)
        self.assertEqual(TestController.__websocket_path__, "/test")

        # 验证辅助函数
        self.assertTrue(is_websocket_controller(TestController))
        self.assertEqual(get_websocket_path(TestController), "/test")

    def test_websocket_controller_annotation_without_path(self):
        """测试没有路径的 WebSocket 控制器注解"""

        @WebSocketController()
        class TestController:
            pass

        self.assertTrue(is_websocket_controller(TestController))
        self.assertEqual(get_websocket_path(TestController), "")

    def test_websocket_on_connect_annotation(self):
        """测试连接建立事件注解"""

        class TestController:
            @WebSocketOnConnect()
            async def on_connect(self, session: WebSocketSession):
                pass

        method = TestController.on_connect
        self.assertTrue(hasattr(method, "__websocket_on_connect__"))
        self.assertTrue(method.__websocket_on_connect__)

    def test_websocket_on_disconnect_annotation(self):
        """测试连接断开事件注解"""

        class TestController:
            @WebSocketOnDisconnect()
            async def on_disconnect(self, session: WebSocketSession):
                pass

        method = TestController.on_disconnect
        self.assertTrue(hasattr(method, "__websocket_on_disconnect__"))
        self.assertTrue(method.__websocket_on_disconnect__)

    def test_websocket_on_message_annotation(self):
        """测试消息处理注解"""

        class TestController:
            @WebSocketOnMessage("text")
            async def on_text_message(self, session: WebSocketSession, message: str):
                pass

            @WebSocketOnMessage("json")
            async def on_json_message(self, session: WebSocketSession, message: dict):
                pass

            @WebSocketOnMessage()  # 默认为 "any"
            async def on_any_message(self, session: WebSocketSession, message):
                pass

        # 验证文本消息处理器
        text_method = TestController.on_text_message
        self.assertTrue(hasattr(text_method, "__websocket_on_message__"))
        self.assertTrue(text_method.__websocket_on_message__)
        self.assertEqual(text_method.__websocket_message_type__, "text")

        # 验证 JSON 消息处理器
        json_method = TestController.on_json_message
        self.assertTrue(hasattr(json_method, "__websocket_on_message__"))
        self.assertTrue(json_method.__websocket_on_message__)
        self.assertEqual(json_method.__websocket_message_type__, "json")

        # 验证通用消息处理器
        any_method = TestController.on_any_message
        self.assertTrue(hasattr(any_method, "__websocket_on_message__"))
        self.assertTrue(any_method.__websocket_on_message__)
        self.assertEqual(any_method.__websocket_message_type__, "any")

    def test_websocket_on_error_annotation(self):
        """测试错误处理注解"""

        class TestController:
            @WebSocketOnError()
            async def on_error(self, session: WebSocketSession, error: Exception):
                pass

        method = TestController.on_error
        self.assertTrue(hasattr(method, "__websocket_on_error__"))
        self.assertTrue(method.__websocket_on_error__)

    def test_websocket_send_to_annotation(self):
        """测试消息发送注解"""

        class TestController:
            @WebSocketSendTo("all")
            async def broadcast_message(self, message: str) -> str:
                return f"Broadcast: {message}"

            @WebSocketSendTo("sender")
            async def echo_message(self, message: str) -> str:
                return f"Echo: {message}"

        # 验证广播消息方法
        broadcast_method = TestController.broadcast_message
        self.assertTrue(hasattr(broadcast_method, "__websocket_send_to__"))
        self.assertTrue(broadcast_method.__websocket_send_to__)
        self.assertEqual(broadcast_method.__websocket_send_target__, "all")

        # 验证回显消息方法
        echo_method = TestController.echo_message
        self.assertTrue(hasattr(echo_method, "__websocket_send_to__"))
        self.assertTrue(echo_method.__websocket_send_to__)
        self.assertEqual(echo_method.__websocket_send_target__, "sender")

    def test_websocket_message_mapping_annotation(self):
        """测试消息映射注解"""

        class TestController:
            @WebSocketMessageMapping("/chat/send", "json")
            async def handle_chat_message(self, session: WebSocketSession, message: dict):
                pass

        method = TestController.handle_chat_message
        self.assertTrue(hasattr(method, "__websocket_message_mapping__"))
        self.assertTrue(method.__websocket_message_mapping__)
        self.assertEqual(method.__websocket_message_path__, "/chat/send")
        self.assertEqual(method.__websocket_message_type__, "json")

    def test_websocket_event_listener_annotation(self):
        """测试事件监听注解"""

        class TestController:
            @WebSocketEventListener("user_join")
            async def on_user_join(self, session: WebSocketSession, event_data: dict):
                pass

        method = TestController.on_user_join
        self.assertTrue(hasattr(method, "__websocket_event_listener__"))
        self.assertTrue(method.__websocket_event_listener__)
        self.assertEqual(method.__websocket_event_type__, "user_join")

    def test_get_websocket_handlers(self):
        """测试获取 WebSocket 处理器"""

        @WebSocketController("/test")
        class TestController:
            @WebSocketOnConnect()
            async def on_connect(self, session: WebSocketSession):
                pass

            @WebSocketOnDisconnect()
            async def on_disconnect(self, session: WebSocketSession):
                pass

            @WebSocketOnMessage("text")
            async def on_text_message(self, session: WebSocketSession, message: str):
                pass

            @WebSocketOnMessage("json")
            async def on_json_message(self, session: WebSocketSession, message: dict):
                pass

            @WebSocketOnError()
            async def on_error(self, session: WebSocketSession, error: Exception):
                pass

            @WebSocketSendTo("all")
            async def broadcast_message(self, message: str) -> str:
                return f"Broadcast: {message}"

            # 普通方法，不应该被识别为处理器
            async def normal_method(self):
                pass

        controller = TestController()
        handlers = get_websocket_handlers(controller)

        # 验证处理器数量
        self.assertEqual(len(handlers["connect"]), 1)
        self.assertEqual(len(handlers["disconnect"]), 1)
        self.assertEqual(len(handlers["message"]), 2)  # text 和 json
        self.assertEqual(len(handlers["error"]), 1)
        self.assertEqual(len(handlers["send_to"]), 1)

        # 验证处理器方法
        self.assertEqual(handlers["connect"][0].__name__, "on_connect")
        self.assertEqual(handlers["disconnect"][0].__name__, "on_disconnect")
        self.assertIn("on_text_message", [h.__name__ for h in handlers["message"]])
        self.assertIn("on_json_message", [h.__name__ for h in handlers["message"]])
        self.assertEqual(handlers["error"][0].__name__, "on_error")
        self.assertEqual(handlers["send_to"][0].__name__, "broadcast_message")

    def test_non_websocket_controller(self):
        """测试非 WebSocket 控制器"""

        class NormalClass:
            def normal_method(self):
                pass

        self.assertFalse(is_websocket_controller(NormalClass))
        self.assertIsNone(get_websocket_path(NormalClass))

        instance = NormalClass()
        handlers = get_websocket_handlers(instance)

        # 所有处理器列表应该为空
        for handler_list in handlers.values():
            self.assertEqual(len(handler_list), 0)

    def test_complex_controller(self):
        """测试复杂的控制器"""

        @WebSocketController("/chat")
        class ChatController:
            @WebSocketOnConnect()
            async def on_connect(self, session: WebSocketSession):
                await session.send_text("Welcome to chat!")

            @WebSocketOnDisconnect()
            async def on_disconnect(self, session: WebSocketSession):
                print(f"User {session.get_user_id()} left the chat")

            @WebSocketOnMessage("text")
            async def on_text_message(self, session: WebSocketSession, message: str):
                if message.startswith("/"):
                    await self.handle_command(session, message)
                else:
                    await self.broadcast_chat_message(session, message)

            @WebSocketOnMessage("json")
            async def on_json_message(self, session: WebSocketSession, message: dict):
                message_type = message.get("type")
                if message_type == "ping":
                    await session.send_json({"type": "pong"})
                elif message_type == "chat":
                    await self.broadcast_chat_message(session, message.get("content", ""))

            @WebSocketOnError()
            async def on_error(self, session: WebSocketSession, error: Exception):
                print(f"Error in session {session.get_id()}: {error}")
                await session.send_json({"type": "error", "message": "An error occurred"})

            @WebSocketSendTo("all")
            async def broadcast_chat_message(self, session: WebSocketSession, message: str) -> dict:
                return {"type": "chat", "user_id": session.get_user_id(), "message": message, "timestamp": "2024-01-01T00:00:00Z"}

            async def handle_command(self, session: WebSocketSession, command: str):
                # 私有方法，不应该被识别为处理器
                pass

        # 验证控制器注解
        self.assertTrue(is_websocket_controller(ChatController))
        self.assertEqual(get_websocket_path(ChatController), "/chat")

        # 验证处理器
        controller = ChatController()
        handlers = get_websocket_handlers(controller)

        self.assertEqual(len(handlers["connect"]), 1)
        self.assertEqual(len(handlers["disconnect"]), 1)
        self.assertEqual(len(handlers["message"]), 2)
        self.assertEqual(len(handlers["error"]), 1)
        self.assertEqual(len(handlers["send_to"]), 1)


if __name__ == "__main__":
    unittest.main()
