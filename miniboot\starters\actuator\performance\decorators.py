#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 性能监控装饰器 - 便捷的性能指标收集

提供多种性能监控装饰器,支持:
- 方法级性能监控
- 端点性能监控
- 自定义指标收集
- 异步和同步方法支持
- 条件性能监控
- 性能阈值告警
"""

import asyncio
import functools
import time
from typing import Callable, Optional, Union

from loguru import logger

from .metrics import get_metrics


def monitor(
    metric_name: Optional[str] = None,
    record_errors: bool = True,
    custom_tags: Optional[dict[str, str]] = None,
    threshold_warning: Optional[float] = None,
    threshold_error: Optional[float] = None,
):
    """性能监控装饰器

    Args:
        metric_name: 指标名称,默认使用函数名
        record_errors: 是否记录错误
        custom_tags: 自定义标签
        threshold_warning: 警告阈值(秒)
        threshold_error: 错误阈值(秒)
    """
    import time as time_module  # 避免命名冲突

    def decorator(func: Callable) -> Callable:
        # 确定指标名称
        name = metric_name or f"{func.__module__}.{func.__qualname__}"
        metrics = get_metrics(name)

        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                request_id = metrics.record_request_start()
                start_time = time_module.time()
                error = None

                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = e
                    raise
                finally:
                    end_time = time_module.time()
                    response_time = end_time - start_time

                    # 记录请求结束
                    if record_errors:
                        metrics.record_request_end(request_id, response_time, error)
                    else:
                        metrics.record_request_end(request_id, response_time)

                    # 记录自定义标签
                    if custom_tags:
                        for tag_name, tag_value in custom_tags.items():
                            metrics.record_custom_metric(f"tag_{tag_name}", 1, {"value": tag_value})

                    # 检查阈值
                    _check_thresholds(name, response_time, threshold_warning, threshold_error)

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                request_id = metrics.record_request_start()
                start_time = time_module.time()
                error = None

                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = e
                    raise
                finally:
                    end_time = time_module.time()
                    response_time = end_time - start_time

                    # 记录请求结束
                    if record_errors:
                        metrics.record_request_end(request_id, response_time, error)
                    else:
                        metrics.record_request_end(request_id, response_time)

                    # 记录自定义标签
                    if custom_tags:
                        for tag_name, tag_value in custom_tags.items():
                            metrics.record_custom_metric(f"tag_{tag_name}", 1, {"value": tag_value})

                    # 检查阈值
                    _check_thresholds(name, response_time, threshold_warning, threshold_error)

            return sync_wrapper

    return decorator


def perf(endpoint_name: Optional[str] = None, include_args: bool = False, sample_rate: float = 1.0):
    """端点性能监控装饰器

    Args:
        endpoint_name: 端点名称
        include_args: 是否包含参数信息
        sample_rate: 采样率(0.0-1.0)
    """
    import time as time_module  # 避免命名冲突

    def decorator(func: Callable) -> Callable:
        name = endpoint_name or f"endpoint_{func.__name__}"
        metrics = get_metrics(name)

        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # 采样检查
                if sample_rate < 1.0:
                    import random

                    if random.random() > sample_rate:
                        return await func(*args, **kwargs)

                request_id = metrics.record_request_start()
                start_time = time_module.time()
                error = None

                try:
                    result = await func(*args, **kwargs)

                    # 记录成功指标
                    metrics.record_custom_metric("success_count", 1)

                    return result
                except Exception as e:
                    error = e
                    # 记录失败指标
                    metrics.record_custom_metric("failure_count", 1)
                    raise
                finally:
                    end_time = time_module.time()
                    response_time = end_time - start_time

                    metrics.record_request_end(request_id, response_time, error)

                    # 记录参数信息
                    if include_args and args:
                        metrics.record_custom_metric("args_count", len(args))
                    if include_args and kwargs:
                        metrics.record_custom_metric("kwargs_count", len(kwargs))

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # 采样检查
                if sample_rate < 1.0:
                    import random

                    if random.random() > sample_rate:
                        return func(*args, **kwargs)

                request_id = metrics.record_request_start()
                start_time = time_module.time()
                error = None

                try:
                    result = func(*args, **kwargs)

                    # 记录成功指标
                    metrics.record_custom_metric("success_count", 1)

                    return result
                except Exception as e:
                    error = e
                    # 记录失败指标
                    metrics.record_custom_metric("failure_count", 1)
                    raise
                finally:
                    end_time = time_module.time()
                    response_time = end_time - start_time

                    metrics.record_request_end(request_id, response_time, error)

                    # 记录参数信息
                    if include_args and args:
                        metrics.record_custom_metric("args_count", len(args))
                    if include_args and kwargs:
                        metrics.record_custom_metric("kwargs_count", len(kwargs))

            return sync_wrapper

    return decorator


def timing(metric_name: Optional[str] = None):
    """时间测量装饰器

    Args:
        metric_name: 指标名称
    """
    import time as time_module  # 避免命名冲突

    def decorator(func: Callable) -> Callable:
        name = metric_name or f"time_{func.__name__}"
        metrics = get_metrics(name)

        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time_module.time()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    end_time = time_module.time()
                    execution_time = end_time - start_time
                    metrics.record_custom_metric("execution_time", execution_time)

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time_module.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time_module.time()
                    execution_time = end_time - start_time
                    metrics.record_custom_metric("execution_time", execution_time)

            return sync_wrapper

    return decorator


def count(metric_name: Optional[str] = None):
    """调用计数装饰器

    Args:
        metric_name: 指标名称
    """

    def decorator(func: Callable) -> Callable:
        name = metric_name or f"calls_{func.__name__}"
        metrics = get_metrics(name)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            metrics.record_custom_metric("call_count", 1)
            return func(*args, **kwargs)

        return wrapper

    return decorator


def conditional(condition: Callable[..., bool], metric_name: Optional[str] = None):
    """条件性能监控装饰器

    Args:
        condition: 条件函数,返回True时才监控
        metric_name: 指标名称
    """

    def decorator(func: Callable) -> Callable:
        name = metric_name or f"conditional_{func.__name__}"
        metrics = get_metrics(name)

        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                if not condition(*args, **kwargs):
                    return await func(*args, **kwargs)

                request_id = metrics.record_request_start()
                start_time = time.time()
                error = None

                try:
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = e
                    raise
                finally:
                    end_time = time.time()
                    response_time = end_time - start_time
                    metrics.record_request_end(request_id, response_time, error)

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                if not condition(*args, **kwargs):
                    return func(*args, **kwargs)

                request_id = metrics.record_request_start()
                start_time = time.time()
                error = None

                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    error = e
                    raise
                finally:
                    end_time = time.time()
                    response_time = end_time - start_time
                    metrics.record_request_end(request_id, response_time, error)

            return sync_wrapper

    return decorator


class PerformanceContext:
    """性能监控上下文管理器"""

    def __init__(self, metric_name: str, record_custom_metrics: bool = True):
        """初始化性能监控上下文

        Args:
            metric_name: 指标名称
            record_custom_metrics: 是否记录自定义指标
        """
        self.metric_name = metric_name
        self.record_custom_metrics = record_custom_metrics
        self.metrics = get_metrics(metric_name)
        self.request_id: Optional[str] = None
        self.start_time: Optional[float] = None

    def __enter__(self):
        """进入上下文"""
        self.request_id = self.metrics.record_request_start()
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        if self.start_time and self.request_id:
            end_time = time.time()
            response_time = end_time - self.start_time

            error = exc_val if exc_type else None
            self.metrics.record_request_end(self.request_id, response_time, error)

            if self.record_custom_metrics:
                if error:
                    self.metrics.record_custom_metric("context_errors", 1)
                else:
                    self.metrics.record_custom_metric("context_success", 1)

    async def __aenter__(self):
        """异步进入上下文"""
        return self.__enter__()

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步退出上下文"""
        return self.__exit__(exc_type, exc_val, exc_tb)

    def record_metric(self, name: str, value: Union[int, float], tags: Optional[dict[str, str]] = None) -> None:
        """记录自定义指标

        Args:
            name: 指标名称
            value: 指标值
            tags: 标签
        """
        self.metrics.record_custom_metric(name, value, tags)


def _check_thresholds(name: str, response_time: float, warning_threshold: Optional[float], error_threshold: Optional[float]) -> None:
    """检查性能阈值

    Args:
        name: 指标名称
        response_time: 响应时间
        warning_threshold: 警告阈值
        error_threshold: 错误阈值
    """
    if error_threshold and response_time > error_threshold:
        logger.error(f"Performance ERROR: {name} took {response_time:.3f}s (threshold: {error_threshold}s)")
    elif warning_threshold and response_time > warning_threshold:
        logger.warning(f"Performance WARNING: {name} took {response_time:.3f}s (threshold: {warning_threshold}s)")
