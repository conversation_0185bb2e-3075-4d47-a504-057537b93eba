#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 消息压缩测试
"""

import json
import unittest

from miniboot.starters.websocket.compression import (
    BrotliCompressionStrategy,
    CompressionAlgorithm,
    CompressionResult,
    DecompressionResult,
    DeflateCompressionStrategy,
    GzipCompressionStrategy,
    LzmaCompressionStrategy,
    WebSocketMessageCompressor,
)
from miniboot.starters.websocket.exceptions import WebSocketMessageException


class CompressionStrategyTestCase(unittest.TestCase):
    """压缩策略测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.test_data = "Hello, World! This is a test message for compression."
        self.test_bytes = self.test_data.encode("utf-8")

        self.gzip_strategy = GzipCompressionStrategy()
        self.deflate_strategy = DeflateCompressionStrategy()
        self.lzma_strategy = LzmaCompressionStrategy()
        self.brotli_strategy = BrotliCompressionStrategy()

    def test_gzip_compression_strategy(self):
        """测试 Gzip 压缩策略"""
        # 使用更长的测试数据确保压缩效果
        long_test_data = self.test_data * 10
        long_test_bytes = long_test_data.encode("utf-8")

        # 测试字符串压缩
        compressed = self.gzip_strategy.compress(long_test_data)
        self.assertIsInstance(compressed, bytes)
        self.assertLess(len(compressed), len(long_test_bytes))

        # 测试解压
        decompressed = self.gzip_strategy.decompress(compressed)
        self.assertEqual(decompressed, long_test_bytes)

        # 测试算法标识
        self.assertEqual(self.gzip_strategy.get_algorithm(), CompressionAlgorithm.GZIP)

    def test_deflate_compression_strategy(self):
        """测试 Deflate 压缩策略"""
        # 使用更长的测试数据确保压缩效果
        long_test_data = self.test_data * 10
        long_test_bytes = long_test_data.encode("utf-8")

        # 测试字节压缩
        compressed = self.deflate_strategy.compress(long_test_bytes)
        self.assertIsInstance(compressed, bytes)
        self.assertLess(len(compressed), len(long_test_bytes))

        # 测试解压
        decompressed = self.deflate_strategy.decompress(compressed)
        self.assertEqual(decompressed, long_test_bytes)

        # 测试算法标识
        self.assertEqual(self.deflate_strategy.get_algorithm(), CompressionAlgorithm.DEFLATE)

    def test_lzma_compression_strategy(self):
        """测试 LZMA 压缩策略"""
        # 测试压缩
        compressed = self.lzma_strategy.compress(self.test_data)
        self.assertIsInstance(compressed, bytes)

        # 测试解压
        decompressed = self.lzma_strategy.decompress(compressed)
        self.assertEqual(decompressed, self.test_bytes)

        # 测试算法标识
        self.assertEqual(self.lzma_strategy.get_algorithm(), CompressionAlgorithm.LZMA)

    def test_brotli_compression_strategy(self):
        """测试 Brotli 压缩策略"""
        # 测试压缩
        compressed = self.brotli_strategy.compress(self.test_data)
        self.assertIsInstance(compressed, bytes)

        # 测试解压
        decompressed = self.brotli_strategy.decompress(compressed)
        self.assertEqual(decompressed, self.test_bytes)

        # 测试算法标识
        self.assertEqual(self.brotli_strategy.get_algorithm(), CompressionAlgorithm.BROTLI)

    def test_compression_levels(self):
        """测试不同压缩级别"""
        # 测试不同压缩级别
        compressed_1 = self.gzip_strategy.compress(self.test_data, level=1)
        compressed_9 = self.gzip_strategy.compress(self.test_data, level=9)

        # 高压缩级别通常产生更小的文件
        self.assertLessEqual(len(compressed_9), len(compressed_1))

        # 两种压缩级别的结果都应该能正确解压
        decompressed_1 = self.gzip_strategy.decompress(compressed_1)
        decompressed_9 = self.gzip_strategy.decompress(compressed_9)

        self.assertEqual(decompressed_1, self.test_bytes)
        self.assertEqual(decompressed_9, self.test_bytes)


class WebSocketMessageCompressorTestCase(unittest.TestCase):
    """WebSocket 消息压缩器测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.compressor = WebSocketMessageCompressor()
        self.test_string = "Hello, World! This is a test message for compression. " * 50  # 重复以确保压缩效果
        self.test_dict = {
            "message": "Hello, World!",
            "data": [1, 2, 3, 4, 5] * 100,  # 重复数据
            "metadata": {"timestamp": "2023-01-01T00:00:00Z", "version": "1.0.0"},
        }

    def test_compressor_initialization(self):
        """测试压缩器初始化"""
        self.assertEqual(self.compressor.default_algorithm, CompressionAlgorithm.GZIP)
        self.assertEqual(self.compressor.default_level, 6)
        self.assertEqual(self.compressor.min_compress_size, 1024)
        self.assertTrue(self.compressor.enable_adaptive_compression)

        # 检查支持的算法
        supported_algorithms = self.compressor.get_supported_algorithms()
        self.assertIn(CompressionAlgorithm.GZIP, supported_algorithms)
        self.assertIn(CompressionAlgorithm.DEFLATE, supported_algorithms)
        self.assertIn(CompressionAlgorithm.LZMA, supported_algorithms)
        self.assertIn(CompressionAlgorithm.BROTLI, supported_algorithms)

    def test_compress_string_message(self):
        """测试压缩字符串消息"""
        result = self.compressor.compress_message(self.test_string)

        self.assertIsInstance(result, CompressionResult)
        self.assertGreater(result.original_size, 0)
        self.assertGreater(result.compressed_size, 0)
        self.assertLess(result.compression_ratio, 1.0)  # 应该有压缩效果
        self.assertIn(result.algorithm, [CompressionAlgorithm.GZIP, CompressionAlgorithm.NONE])

    def test_compress_dict_message(self):
        """测试压缩字典消息"""
        result = self.compressor.compress_message(self.test_dict)

        self.assertIsInstance(result, CompressionResult)
        self.assertGreater(result.original_size, 0)
        self.assertGreater(result.compressed_size, 0)

        # 验证原始数据是 JSON 序列化的
        original_json = json.dumps(self.test_dict, ensure_ascii=False, separators=(",", ":"))
        self.assertEqual(result.original_size, len(original_json.encode("utf-8")))

    def test_compress_bytes_message(self):
        """测试压缩字节消息"""
        test_bytes = self.test_string.encode("utf-8")
        result = self.compressor.compress_message(test_bytes)

        self.assertIsInstance(result, CompressionResult)
        self.assertEqual(result.original_size, len(test_bytes))

    def test_compress_with_specific_algorithm(self):
        """测试使用特定算法压缩"""
        result = self.compressor.compress_message(self.test_string, algorithm=CompressionAlgorithm.DEFLATE)

        if result.algorithm != CompressionAlgorithm.NONE:
            self.assertEqual(result.algorithm, CompressionAlgorithm.DEFLATE)

    def test_compress_with_specific_level(self):
        """测试使用特定压缩级别"""
        result_low = self.compressor.compress_message(self.test_string, level=1)
        result_high = self.compressor.compress_message(self.test_string, level=9)

        # 如果都进行了压缩，高级别应该压缩比更好
        if result_low.algorithm != CompressionAlgorithm.NONE and result_high.algorithm != CompressionAlgorithm.NONE:
            self.assertLessEqual(result_high.compression_ratio, result_low.compression_ratio)

    def test_decompress_message(self):
        """测试解压消息"""
        # 先压缩
        compress_result = self.compressor.compress_message(self.test_string)

        # 再解压
        decompress_result = self.compressor.decompress_message(compress_result.compressed_data, compress_result.algorithm)

        self.assertIsInstance(decompress_result, DecompressionResult)
        self.assertEqual(decompress_result.algorithm, compress_result.algorithm)

        # 验证解压后的数据
        if compress_result.algorithm == CompressionAlgorithm.NONE:
            self.assertEqual(decompress_result.decompressed_data, self.test_string)
        else:
            self.assertEqual(decompress_result.decompressed_data, self.test_string)

    def test_decompress_no_compression(self):
        """测试解压未压缩的消息"""
        test_data = self.test_string.encode("utf-8")

        result = self.compressor.decompress_message(test_data, CompressionAlgorithm.NONE)

        self.assertEqual(result.decompressed_data, self.test_string)
        self.assertEqual(result.algorithm, CompressionAlgorithm.NONE)
        self.assertEqual(result.original_size, result.compressed_size)

    def test_decompress_return_bytes(self):
        """测试解压返回字节格式"""
        compress_result = self.compressor.compress_message(self.test_string)

        decompress_result = self.compressor.decompress_message(compress_result.compressed_data, compress_result.algorithm, return_string=False)

        if compress_result.algorithm == CompressionAlgorithm.NONE:
            self.assertIsInstance(decompress_result.decompressed_data, bytes)
        else:
            self.assertIsInstance(decompress_result.decompressed_data, bytes)
            self.assertEqual(decompress_result.decompressed_data, self.test_string.encode("utf-8"))

    def test_small_message_no_compression(self):
        """测试小消息不压缩"""
        small_message = "Hello"  # 小于 min_compress_size

        result = self.compressor.compress_message(small_message)

        self.assertEqual(result.algorithm, CompressionAlgorithm.NONE)
        self.assertEqual(result.compression_ratio, 1.0)

    def test_adaptive_compression(self):
        """测试自适应压缩"""
        # 创建一个压缩效果不好的数据（随机数据）
        import random
        import string

        random_data = "".join(random.choices(string.ascii_letters + string.digits, k=2000))

        # 启用自适应压缩
        self.compressor.enable_adaptive_compression = True
        self.compressor.compression_ratio_threshold = 0.9

        result = self.compressor.compress_message(random_data)

        # 如果压缩效果不好，应该返回原始数据
        if result.compression_ratio > 0.9:
            self.assertEqual(result.algorithm, CompressionAlgorithm.NONE)

    def test_compression_stats(self):
        """测试压缩统计信息"""
        # 执行一些压缩操作
        self.compressor.compress_message(self.test_string)
        self.compressor.compress_message(self.test_dict)

        stats = self.compressor.get_compression_stats()

        self.assertIn("total_compressions", stats)
        self.assertIn("total_decompressions", stats)
        self.assertIn("bytes_before_compression", stats)
        self.assertIn("bytes_after_compression", stats)
        self.assertIn("average_compression_ratio", stats)
        self.assertIn("algorithm_usage", stats)

        self.assertGreaterEqual(stats["total_compressions"], 2)

    def test_reset_stats(self):
        """测试重置统计信息"""
        # 执行一些操作
        self.compressor.compress_message(self.test_string)

        # 重置统计
        self.compressor.reset_stats()

        stats = self.compressor.get_compression_stats()
        self.assertEqual(stats["total_compressions"], 0)
        self.assertEqual(stats["total_decompressions"], 0)

    def test_compressor_configuration(self):
        """测试压缩器配置"""
        # 测试配置更新
        self.compressor.configure(
            default_algorithm=CompressionAlgorithm.DEFLATE, default_level=8, min_compress_size=2048, enable_adaptive_compression=False
        )

        self.assertEqual(self.compressor.default_algorithm, CompressionAlgorithm.DEFLATE)
        self.assertEqual(self.compressor.default_level, 8)
        self.assertEqual(self.compressor.min_compress_size, 2048)
        self.assertFalse(self.compressor.enable_adaptive_compression)

    def test_compression_error_handling(self):
        """测试压缩错误处理"""
        # 测试无效的压缩数据解压
        with self.assertRaises(WebSocketMessageException):
            self.compressor.decompress_message(b"invalid compressed data", CompressionAlgorithm.GZIP)

    def test_compression_result_properties(self):
        """测试压缩结果属性"""
        result = self.compressor.compress_message(self.test_string)

        # 测试节省空间计算
        expected_saved = result.original_size - result.compressed_size
        self.assertEqual(result.space_saved, expected_saved)

        # 测试节省空间百分比
        if result.original_size > 0:
            expected_percentage = (expected_saved / result.original_size) * 100
            self.assertEqual(result.space_saved_percentage, expected_percentage)

    def test_algorithm_selection(self):
        """测试算法选择策略"""
        # 小数据应该选择 GZIP
        small_data = "x" * 5000  # 5KB
        result_small = self.compressor.compress_message(small_data)

        # 中等数据应该选择 DEFLATE
        medium_data = "x" * 50000  # 50KB
        result_medium = self.compressor.compress_message(medium_data)

        # 大数据应该选择 LZMA
        large_data = "x" * 500000  # 500KB
        result_large = self.compressor.compress_message(large_data)

        # 验证算法选择（如果进行了压缩）
        if result_small.algorithm != CompressionAlgorithm.NONE:
            self.assertEqual(result_small.algorithm, CompressionAlgorithm.GZIP)

        if result_medium.algorithm != CompressionAlgorithm.NONE:
            self.assertEqual(result_medium.algorithm, CompressionAlgorithm.DEFLATE)

        if result_large.algorithm != CompressionAlgorithm.NONE:
            self.assertEqual(result_large.algorithm, CompressionAlgorithm.LZMA)

    def test_round_trip_compression(self):
        """测试往返压缩（压缩后解压应该得到原始数据）"""
        test_cases = [
            self.test_string,
            self.test_dict,
            self.test_string.encode("utf-8"),
            {"unicode": "测试中文字符", "emoji": "🚀🎉"},
            list(range(1000)),  # 数字列表
        ]

        for test_data in test_cases:
            with self.subTest(data_type=type(test_data).__name__):
                # 压缩
                compress_result = self.compressor.compress_message(test_data)

                # 解压
                decompress_result = self.compressor.decompress_message(compress_result.compressed_data, compress_result.algorithm)

                # 验证数据一致性
                if isinstance(test_data, (dict, list)):
                    # 对于字典和列表，比较 JSON 序列化后的结果
                    # 注意：压缩器内部使用相同的序列化参数
                    original_json = json.dumps(test_data, ensure_ascii=False, separators=(",", ":"))
                    # 解压后的数据应该与原始 JSON 字符串一致
                    if isinstance(decompress_result.decompressed_data, str):
                        # 解析两个 JSON 字符串并比较对象
                        original_obj = json.loads(original_json)
                        decompressed_obj = json.loads(decompress_result.decompressed_data)
                        self.assertEqual(decompressed_obj, original_obj)
                    else:
                        self.assertEqual(decompress_result.decompressed_data, original_json)
                elif isinstance(test_data, bytes):
                    # 对于字节数据，需要以字节格式解压
                    decompress_result_bytes = self.compressor.decompress_message(
                        compress_result.compressed_data, compress_result.algorithm, return_string=False
                    )
                    self.assertEqual(decompress_result_bytes.decompressed_data, test_data)
                else:
                    # 对于字符串数据
                    self.assertEqual(decompress_result.decompressed_data, test_data)


if __name__ == "__main__":
    unittest.main()
