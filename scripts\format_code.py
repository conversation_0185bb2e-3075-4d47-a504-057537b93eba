#!/usr/bin/env python3
"""
代码格式化和质量检查工具

用于自动格式化代码并修复常见的代码质量问题.
"""

import argparse
import subprocess
import sys
from pathlib import Path


class CodeFormatter:
    """代码格式化器"""

    def __init__(self, project_root: Path):
        self.project_root = project_root

    def log(self, message: str, level: str = "INFO") -> None:
        """输出日志"""
        print(f"[{level}] {message}")

    def run_command(self, cmd: list, check: bool = True) -> subprocess.CompletedProcess:
        """执行命令"""
        self.log(f"执行命令: {' '.join(cmd)}")
        return subprocess.run(
            cmd,
            check=check,
            cwd=self.project_root,
            capture_output=True,
            text=True,
            encoding="utf-8",
            errors="replace",
        )

    def format_code(self) -> bool:
        """格式化代码"""
        self.log("开始格式化代码...")

        try:
            # 使用 ruff 格式化代码
            result = self.run_command(["uv", "run", "ruff", "format", "."])
            if result.returncode == 0:
                self.log("✅ 代码格式化完成")
                if result.stdout.strip():
                    self.log(f"格式化输出: {result.stdout.strip()}")
            else:
                self.log(f"❌ 代码格式化失败: {result.stderr}", "ERROR")
                return False

        except subprocess.CalledProcessError as e:
            self.log(f"❌ 代码格式化失败: {e}", "ERROR")
            return False

        return True

    def fix_imports(self) -> bool:
        """修复导入排序"""
        self.log("修复导入排序...")

        try:
            # 使用 ruff 修复导入
            result = self.run_command(["uv", "run", "ruff", "check", "--fix", "--select", "I", "."])
            if result.returncode == 0:
                self.log("✅ 导入排序修复完成")
            else:
                self.log(f"⚠️  导入排序修复警告: {result.stderr}", "WARN")

        except subprocess.CalledProcessError as e:
            self.log(f"❌ 导入排序修复失败: {e}", "ERROR")
            return False

        return True

    def fix_code_issues(self) -> bool:
        """修复代码问题"""
        self.log("修复代码问题...")

        try:
            # 使用 ruff 自动修复问题
            result = self.run_command(["uv", "run", "ruff", "check", "--fix", "."])
            if result.returncode == 0:
                self.log("✅ 代码问题修复完成")
                if result.stdout.strip():
                    self.log(f"修复输出: {result.stdout.strip()}")
            else:
                self.log(f"⚠️  部分代码问题无法自动修复: {result.stderr}", "WARN")
                if result.stdout.strip():
                    self.log(f"修复输出: {result.stdout.strip()}")

        except subprocess.CalledProcessError as e:
            self.log(f"❌ 代码问题修复失败: {e}", "ERROR")
            return False

        return True

    def check_code_quality(self) -> bool:
        """检查代码质量"""
        self.log("检查代码质量...")

        try:
            # 运行代码质量检查
            result = self.run_command(["uv", "run", "tests/test_runner.py", "--quality"])
            if result.returncode == 0:
                self.log("✅ 代码质量检查通过")
                return True
            self.log(f"❌ 代码质量检查失败: {result.stderr}", "ERROR")
            if result.stdout.strip():
                self.log(f"检查输出: {result.stdout.strip()}")
            return False

        except subprocess.CalledProcessError as e:
            self.log(f"❌ 代码质量检查失败: {e}", "ERROR")
            return False

    def format_all(self, check_only: bool = False) -> bool:
        """执行完整的代码格式化流程"""
        self.log("🚀 开始代码格式化和质量检查...")

        if check_only:
            # 仅检查,不修复
            return self.check_code_quality()

        # 1. 格式化代码
        if not self.format_code():
            return False

        # 2. 修复导入排序
        if not self.fix_imports():
            return False

        # 3. 修复代码问题
        if not self.fix_code_issues():
            return False

        # 4. 最终质量检查
        if not self.check_code_quality():
            self.log("⚠️  代码质量检查未通过,请手动修复剩余问题", "WARN")
            return False

        self.log("🎉 代码格式化和质量检查完成!")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Mini-Boot 代码格式化工具")
    parser.add_argument("--check", action="store_true", help="仅检查代码质量,不进行修复")
    parser.add_argument("--format-only", action="store_true", help="仅格式化代码")
    parser.add_argument("--fix-only", action="store_true", help="仅修复代码问题")

    args = parser.parse_args()

    project_root = Path(__file__).parent.parent
    formatter = CodeFormatter(project_root)

    try:
        if args.check:
            success = formatter.check_code_quality()
        elif args.format_only:
            success = formatter.format_code()
        elif args.fix_only:
            success = formatter.fix_code_issues()
        else:
            success = formatter.format_all()

        sys.exit(0 if success else 1)

    except (OSError, RuntimeError) as e:
        formatter.log(f"❌ 操作失败: {e}", "ERROR")
        sys.exit(1)


if __name__ == "__main__":
    main()
