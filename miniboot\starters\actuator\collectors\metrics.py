#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 应用指标收集器 - 高性能异步实现

提供高性能的异步应用指标收集,支持并发执行和智能缓存.

核心特性:
- 异步应用指标收集
- HTTP请求统计
- 数据库连接池监控
- 缓存使用统计
- 任务执行监控
- 智能缓存和性能优化
"""

import asyncio
import gc
import sys
import threading
import time
from collections import defaultdict
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from miniboot.utils import timeout


class AppMetricsCollector:
    """异步应用指标收集器

    提供高性能的异步应用指标收集,支持并发执行和智能缓存.
    """

    def __init__(self, timeout: float = 5.0, cache_ttl: float = 30.0):
        """初始化异步应用指标收集器

        Args:
            timeout: 收集超时时间(秒)
            cache_ttl: 缓存TTL(秒)
        """
        self.timeout = timeout
        self.cache_ttl = cache_ttl
        self._start_time = time.time()
        self._request_stats = defaultdict(int)
        self._task_stats = defaultdict(int)
        self._error_stats = defaultdict(int)

    @timeout(5.0)
    async def collect_all(self) -> Dict[str, Any]:
        """收集所有应用指标"""
        try:
            loop = asyncio.get_event_loop()

            # 并发收集不同类型的应用指标
            runtime_task = loop.run_in_executor(None, self._collect_runtime_metrics)
            memory_task = loop.run_in_executor(None, self._collect_memory_metrics)
            threading_task = loop.run_in_executor(None, self._collect_threading_metrics)
            gc_task = loop.run_in_executor(None, self._collect_gc_metrics)
            asyncio_task = loop.run_in_executor(None, self._collect_asyncio_metrics)
            application_task = loop.run_in_executor(None, self._collect_application_metrics)

            # 等待所有任务完成
            runtime_metrics, memory_metrics, threading_metrics, gc_metrics, asyncio_metrics, app_metrics = await asyncio.gather(
                runtime_task, memory_task, threading_task, gc_task, asyncio_task, application_task, return_exceptions=True
            )

            return {
                "timestamp": datetime.now().isoformat(),
                "runtime": runtime_metrics if not isinstance(runtime_metrics, Exception) else {"error": str(runtime_metrics)},
                "memory": memory_metrics if not isinstance(memory_metrics, Exception) else {"error": str(memory_metrics)},
                "threading": threading_metrics if not isinstance(threading_metrics, Exception) else {"error": str(threading_metrics)},
                "gc": gc_metrics if not isinstance(gc_metrics, Exception) else {"error": str(gc_metrics)},
                "asyncio": asyncio_metrics if not isinstance(asyncio_metrics, Exception) else {"error": str(asyncio_metrics)},
                "application": app_metrics if not isinstance(app_metrics, Exception) else {"error": str(app_metrics)},
            }

        except Exception as e:
            logger.error(f"Application metrics collection failed: {e}")
            return {"timestamp": datetime.now().isoformat(), "error": str(e)}

    def _collect_runtime_metrics(self) -> Dict[str, Any]:
        """收集运行时指标"""
        try:
            import platform

            # Python运行时信息
            runtime_info = {
                "python_version": platform.python_version(),
                "python_implementation": platform.python_implementation(),
                "python_compiler": platform.python_compiler(),
                "python_build": platform.python_build(),
                "python_branch": platform.python_branch(),
                "python_revision": platform.python_revision(),
            }

            # 系统路径信息
            path_info = {
                "executable": sys.executable,
                "prefix": sys.prefix,
                "exec_prefix": sys.exec_prefix,
                "base_prefix": getattr(sys, "base_prefix", sys.prefix),
                "base_exec_prefix": getattr(sys, "base_exec_prefix", sys.exec_prefix),
                "path_count": len(sys.path),
                "modules_count": len(sys.modules),
            }

            # 运行时统计
            uptime = time.time() - self._start_time
            runtime_stats = {
                "uptime_seconds": uptime,
                "uptime_formatted": self._format_uptime(uptime),
                "start_time": self._start_time,
                "current_time": time.time(),
            }

            return {"info": runtime_info, "paths": path_info, "stats": runtime_stats}

        except Exception as e:
            return {"error": f"Failed to collect runtime metrics: {e}"}

    def _collect_memory_metrics(self) -> Dict[str, Any]:
        """收集内存指标"""
        try:
            # 获取对象统计
            objects = gc.get_objects()
            object_types = defaultdict(int)

            # 统计对象类型
            for obj in objects[:1000]:  # 限制检查数量以提高性能
                obj_type = type(obj).__name__
                object_types[obj_type] += 1

            # 内存使用统计
            memory_stats = {
                "total_objects": len(objects),
                "object_types_sampled": dict(object_types),
                "reference_cycles": len(gc.garbage),
                "gc_enabled": gc.isenabled(),
            }

            # 尝试获取内存使用信息
            try:
                import psutil

                process = psutil.Process()
                memory_info = process.memory_info()
                memory_stats.update({"rss": memory_info.rss, "vms": memory_info.vms, "memory_percent": process.memory_percent()})
            except ImportError:
                pass

            return memory_stats

        except Exception as e:
            return {"error": f"Failed to collect memory metrics: {e}"}

    def _collect_threading_metrics(self) -> Dict[str, Any]:
        """收集线程指标"""
        try:
            # 线程统计
            all_threads = threading.enumerate()

            thread_stats = {
                "total_threads": len(all_threads),
                "daemon_threads": sum(1 for t in all_threads if t.daemon),
                "non_daemon_threads": sum(1 for t in all_threads if not t.daemon),
                "alive_threads": sum(1 for t in all_threads if t.is_alive()),
                "main_thread_alive": threading.main_thread().is_alive(),
                "active_count": threading.active_count(),
            }

            # 线程详情
            thread_details = []
            for thread in all_threads:
                thread_info = {"id": thread.ident, "name": thread.name, "daemon": thread.daemon, "alive": thread.is_alive()}
                thread_details.append(thread_info)

            return {"statistics": thread_stats, "threads": thread_details}

        except Exception as e:
            return {"error": f"Failed to collect threading metrics: {e}"}

    def _collect_gc_metrics(self) -> Dict[str, Any]:
        """收集垃圾回收指标"""
        try:
            # GC统计
            gc_stats = {"enabled": gc.isenabled(), "counts": gc.get_count(), "thresholds": gc.get_threshold(), "garbage_count": len(gc.garbage)}

            # 尝试获取详细GC统计
            if hasattr(gc, "get_stats"):
                gc_stats["detailed_stats"] = gc.get_stats()

            # 执行一次GC并统计
            collected_before = sum(gc.get_count())
            collected = gc.collect()
            collected_after = sum(gc.get_count())

            gc_stats["collection_result"] = {
                "objects_before": collected_before,
                "objects_after": collected_after,
                "objects_collected": collected,
                "collection_time": datetime.now().isoformat(),
            }

            return gc_stats

        except Exception as e:
            return {"error": f"Failed to collect GC metrics: {e}"}

    def _collect_asyncio_metrics(self) -> Dict[str, Any]:
        """收集asyncio指标"""
        try:
            # 获取当前事件循环
            try:
                loop = asyncio.get_running_loop()
                loop_running = True
            except RuntimeError:
                loop = None
                loop_running = False

            asyncio_stats = {"loop_running": loop_running, "loop_type": type(loop).__name__ if loop else None}

            if loop:
                # 获取任务信息
                all_tasks = asyncio.all_tasks(loop)
                asyncio_stats.update(
                    {
                        "total_tasks": len(all_tasks),
                        "pending_tasks": len([t for t in all_tasks if not t.done()]),
                        "completed_tasks": len([t for t in all_tasks if t.done()]),
                        "cancelled_tasks": len([t for t in all_tasks if t.cancelled()]),
                    }
                )

                # 任务详情
                task_details = []
                for task in list(all_tasks)[:10]:  # 限制显示前10个任务
                    task_info = {"name": getattr(task, "_name", "unnamed"), "done": task.done(), "cancelled": task.cancelled()}
                    if hasattr(task, "_coro"):
                        task_info["coro"] = str(task._coro)
                    task_details.append(task_info)

                asyncio_stats["task_details"] = task_details

            return asyncio_stats

        except Exception as e:
            return {"error": f"Failed to collect asyncio metrics: {e}"}

    def _collect_application_metrics(self) -> Dict[str, Any]:
        """收集应用指标"""
        try:
            # 应用统计
            app_stats = {
                "request_stats": dict(self._request_stats),
                "task_stats": dict(self._task_stats),
                "error_stats": dict(self._error_stats),
                "uptime": time.time() - self._start_time,
            }

            # 性能指标
            performance_stats = {
                "total_requests": sum(self._request_stats.values()),
                "total_tasks": sum(self._task_stats.values()),
                "total_errors": sum(self._error_stats.values()),
                "error_rate": sum(self._error_stats.values()) / max(sum(self._request_stats.values()), 1) * 100,
            }

            # 健康指标
            health_stats = {"status": "healthy" if performance_stats["error_rate"] < 5 else "degraded", "last_collection": datetime.now().isoformat()}

            return {"statistics": app_stats, "performance": performance_stats, "health": health_stats}

        except Exception as e:
            return {"error": f"Failed to collect application metrics: {e}"}

    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if days > 0:
            return f"{days}d {hours}h {minutes}m {secs}s"
        elif hours > 0:
            return f"{hours}h {minutes}m {secs}s"
        elif minutes > 0:
            return f"{minutes}m {secs}s"
        else:
            return f"{secs}s"

    async def collect_specific(self, metric_type: str) -> Dict[str, Any]:
        """收集特定类型的指标"""
        try:
            loop = asyncio.get_event_loop()

            if metric_type == "runtime":
                result = await loop.run_in_executor(None, self._collect_runtime_metrics)
            elif metric_type == "memory":
                result = await loop.run_in_executor(None, self._collect_memory_metrics)
            elif metric_type == "threading":
                result = await loop.run_in_executor(None, self._collect_threading_metrics)
            elif metric_type == "gc":
                result = await loop.run_in_executor(None, self._collect_gc_metrics)
            elif metric_type == "asyncio":
                result = await loop.run_in_executor(None, self._collect_asyncio_metrics)
            elif metric_type == "application":
                result = await loop.run_in_executor(None, self._collect_application_metrics)
            else:
                raise ValueError(f"Unknown metric type: {metric_type}")

            return {"timestamp": datetime.now().isoformat(), "metric_type": metric_type, "data": result}

        except Exception as e:
            logger.error(f"Specific metric collection failed for {metric_type}: {e}")
            return {"timestamp": datetime.now().isoformat(), "metric_type": metric_type, "error": str(e)}

    def record_request(self, method: str, status_code: int = 200) -> None:
        """记录请求统计"""
        key = f"{method}_{status_code}"
        self._request_stats[key] += 1

        if status_code >= 400:
            self._error_stats[f"http_{status_code}"] += 1

    def record_task(self, task_name: str, success: bool = True) -> None:
        """记录任务统计"""
        key = f"{task_name}_{'success' if success else 'failure'}"
        self._task_stats[key] += 1

        if not success:
            self._error_stats[f"task_{task_name}"] += 1

    def record_error(self, error_type: str) -> None:
        """记录错误统计"""
        self._error_stats[error_type] += 1

    def get_supported_metrics(self) -> List[str]:
        """获取支持的指标类型"""
        return ["runtime", "memory", "threading", "gc", "asyncio", "application"]

    def reset_stats(self) -> None:
        """重置统计信息"""
        self._request_stats.clear()
        self._task_stats.clear()
        self._error_stats.clear()
        self._start_time = time.time()
