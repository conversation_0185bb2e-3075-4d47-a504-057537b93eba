{"version": "0.2.0", "configurations": [{"name": "Python: Current File", "type": "python", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Mini-Boot Application", "type": "python", "request": "launch", "program": "${workspaceFolder}/examples/basic/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Run Tests", "type": "python", "request": "launch", "module": "pytest", "args": ["tests/", "-v"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Debug Tests", "type": "python", "request": "launch", "module": "pytest", "args": ["${file}", "-v", "-s"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}]}