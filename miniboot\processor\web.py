#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web注解处理器

处理Web相关的注解,包括控制器注册、路由映射等功能.

主要功能:
- WebAnnotationProcessor - Web注解处理器
- 控制器自动扫描和注册
- 路由信息提取和映射
- 参数绑定处理
"""

from typing import TYPE_CHECKING, Any, Optional

from loguru import logger

from ..annotations.web import (controller_path, has_route, is_controller,
                               is_rest_controller, route_info)
from .base import BeanPostProcessor

# 避免循环导入,使用TYPE_CHECKING
if TYPE_CHECKING:
    from ..web.application import WebApplication


class WebAnnotationProcessor(BeanPostProcessor):
    """Web注解处理器

    处理@Controller、@RestController等Web相关注解,
    自动注册控制器到Web应用中.
    """

    def __init__(self, web_application: Optional["WebApplication"] = None):
        """初始化Web注解处理器

        Args:
            web_application: Web应用实例,如果为None则延迟获取
        """
        super().__init__()
        self.web_application = web_application
        self.registered_controllers: dict[str, Any] = {}
        self.route_mappings: list[dict[str, Any]] = []

    def get_order(self) -> int:
        """获取处理器的执行顺序

        Returns:
            执行顺序，Web处理器使用较低的优先级
        """
        return 200  # Web处理器在其他核心处理器之后执行

    def supports(self, bean: Any, _: str) -> bool:
        """检查是否支持处理该Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            是否支持处理
        """
        return is_controller(bean.__class__)

    def can_process(self, bean_class: type, _: Any) -> bool:
        """检查是否可以处理指定的Bean类和实例

        Args:
            bean_class: Bean类
            bean_instance: Bean实例

        Returns:
            是否可以处理
        """
        return is_controller(bean_class)

    def post_process_before_initialization(self, bean: Any, _: str) -> Any:
        """在Bean初始化前处理Web注解

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化后处理Web注解

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        if not self.supports(bean, bean_name):
            return bean

        try:
            # 注册控制器
            self._register_controller(bean.__class__, bean, bean_name)

            # 提取路由信息
            self._extract_route_mappings(bean.__class__, bean, bean_name)

            logger.info(f"Processed web controller: {bean_name}")

        except Exception as e:
            logger.error(f"Failed to process web controller {bean_name}: {e}")

        return bean

    def _register_controller(self, bean_class: type, bean_instance: Any, bean_name: str):
        """注册控制器

        Args:
            bean_class: Bean类
            bean_instance: Bean实例
            bean_name: Bean名称
        """
        # 记录控制器信息
        controller_info = {
            "class": bean_class,
            "instance": bean_instance,
            "name": bean_name,
            "path": controller_path(bean_class),
            "is_rest": is_rest_controller(bean_class),
        }

        self.registered_controllers[bean_name] = controller_info

        # 注意：不在这里立即注册到WebApplication，因为这是同步方法
        # 实际注册会在set_web_application时进行
        logger.debug(f"Controller {bean_name} recorded, will register when WebApplication is set")

    def _extract_route_mappings(self, bean_class: type, _: Any, bean_name: str):
        """提取路由映射信息

        Args:
            bean_class: Bean类
            bean_instance: Bean实例
            bean_name: Bean名称
        """
        controller_path_value = controller_path(bean_class)

        # 扫描控制器方法
        for method_name in dir(bean_class):
            if method_name.startswith("_"):
                continue

            method = getattr(bean_class, method_name)

            if not callable(method) or not has_route(method):
                continue

            route_info_value = route_info(method)
            if not route_info_value:
                continue

            # 构建完整的路由信息
            full_route_info = {
                "controller_name": bean_name,
                "controller_path": controller_path_value,
                "method_name": method_name,
                "method": method,
                "route_info": route_info_value,
                "full_path": self._build_full_path(controller_path_value, route_info_value["path"]),
            }

            self.route_mappings.append(full_route_info)

            logger.debug(f"Extracted route mapping: {full_route_info['full_path']} -> {bean_name}.{method_name}")

    def _build_full_path(self, controller_path: str, method_path: str) -> str:
        """构建完整路径

        Args:
            controller_path: 控制器路径
            method_path: 方法路径

        Returns:
            完整路径
        """
        full_path = controller_path + method_path

        # 确保路径以/开头
        if not full_path.startswith("/"):
            full_path = "/" + full_path

        # 清理重复的斜杠
        while "//" in full_path:
            full_path = full_path.replace("//", "/")

        return full_path

    async def set_web_application(self, web_application: "WebApplication"):
        """设置Web应用实例

        Args:
            web_application: Web应用实例
        """
        self.web_application = web_application

        # 注册所有已发现的控制器到Web应用
        for controller_name, controller_info in self.registered_controllers.items():
            if controller_info["instance"]:
                await web_application.register_controller(controller_info["instance"], controller_name)
                logger.info(f"Registered existing controller: {controller_name}")

    def get_registered_controllers(self) -> dict[str, Any]:
        """获取已注册的控制器

        Returns:
            控制器字典
        """
        return self.registered_controllers.copy()

    def get_route_mappings(self) -> list[dict[str, Any]]:
        """获取路由映射信息

        Returns:
            路由映射列表
        """
        return self.route_mappings.copy()

    def get_controller_info(self, controller_name: str) -> Optional[dict[str, Any]]:
        """获取控制器信息

        Args:
            controller_name: 控制器名称

        Returns:
            控制器信息,如果不存在则返回None
        """
        return self.registered_controllers.get(controller_name)

    def routes(self, controller_name: str) -> list[dict[str, Any]]:
        """获取指定控制器的路由

        Args:
            controller_name: 控制器名称

        Returns:
            路由列表
        """
        return [route for route in self.route_mappings if route["controller_name"] == controller_name]

    def clear(self):
        """清理所有注册信息"""
        self.registered_controllers.clear()
        self.route_mappings.clear()
        logger.info("Cleared all web controller registrations")

    def get_summary(self) -> dict[str, Any]:
        """获取处理器摘要信息

        Returns:
            摘要信息
        """
        return {
            "processor_type": "WebAnnotationProcessor",
            "controllers_count": len(self.registered_controllers),
            "routes_count": len(self.route_mappings),
            "has_web_application": self.web_application is not None,
            "controllers": list(self.registered_controllers.keys()),
            "routes": [
                {
                    "path": route["full_path"],
                    "method": route["route_info"]["method"],
                    "controller": route["controller_name"],
                    "handler": route["method_name"],
                }
                for route in self.route_mappings
            ],
        }
