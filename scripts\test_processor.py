#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean处理器模块测试运行脚本
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True, encoding="utf-8")
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)


def run_processor_tests(args):
    """运行Bean处理器模块测试"""

    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent

    print("🚀 开始运行Bean处理器模块测试...")
    print(f"📁 项目根目录: {project_root}")

    # 构建测试命令
    cmd_parts = ["uv", "run", "python", "-m", "pytest", "tests/unit/processor/"]

    # 添加覆盖率选项
    if args.coverage:
        cmd_parts.extend(["--cov=miniboot.processor", "--cov-report=term-missing"])

        if args.html:
            cmd_parts.append("--cov-report=html")

        if args.xml:
            cmd_parts.append("--cov-report=xml")

    # 添加详细输出选项
    if args.verbose:
        cmd_parts.append("-v")

    # 添加失败时停止选项
    if args.fail_fast:
        cmd_parts.append("-x")

    # 添加特定测试文件
    if args.test_file:
        cmd_parts[-1] = f"tests/unit/processor/{args.test_file}"

    # 添加特定测试方法
    if args.test_method:
        if args.test_file:
            cmd_parts[-1] += f"::{args.test_method}"
        else:
            print("❌ 错误: 指定测试方法时必须同时指定测试文件")
            return False

    # 添加覆盖率阈值
    if args.coverage and args.min_coverage:
        cmd_parts.append(f"--cov-fail-under={args.min_coverage}")

    # 运行测试
    cmd = " ".join(cmd_parts)
    print(f"🔧 执行命令: {cmd}")
    print("=" * 80)

    success, stdout, stderr = run_command(cmd, cwd=project_root)

    # 输出结果
    if stdout:
        print(stdout)
    if stderr:
        print("错误输出:", stderr, file=sys.stderr)

    # 显示结果摘要
    print("=" * 80)
    if success:
        print("✅ 测试执行成功!")

        if args.coverage and args.html:
            html_report = project_root / "htmlcov" / "index.html"
            if html_report.exists():
                print(f"📊 HTML覆盖率报告: {html_report}")

        if args.coverage and args.xml:
            xml_report = project_root / "coverage.xml"
            if xml_report.exists():
                print(f"📄 XML覆盖率报告: {xml_report}")

        # 显示覆盖率报告位置
        coverage_report = project_root / "tests" / "unit" / "processor" / "COVERAGE_REPORT.md"
        if coverage_report.exists():
            print(f"📋 覆盖率总结报告: {coverage_report}")

    else:
        print("❌ 测试执行失败!")
        return False

    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Bean处理器模块测试运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python scripts/test_processor.py                    # 运行所有测试
  python scripts/test_processor.py --coverage         # 运行测试并生成覆盖率报告
  python scripts/test_processor.py --coverage --html  # 生成HTML覆盖率报告
  python scripts/test_processor.py --test-file test_manager.py  # 运行特定测试文件
  python scripts/test_processor.py --test-file test_manager.py --test-method test_register_processor  # 运行特定测试方法
  python scripts/test_processor.py --coverage --min-coverage 80  # 设置最低覆盖率要求
        """,
    )

    parser.add_argument("--coverage", action="store_true", help="生成测试覆盖率报告")

    parser.add_argument("--html", action="store_true", help="生成HTML格式的覆盖率报告")

    parser.add_argument("--xml", action="store_true", help="生成XML格式的覆盖率报告")

    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出测试信息")

    parser.add_argument("--fail-fast", "-x", action="store_true", help="遇到第一个失败测试时停止")

    parser.add_argument("--test-file", help="运行特定的测试文件 (例如: test_manager.py)")

    parser.add_argument("--test-method", help="运行特定的测试方法 (需要同时指定 --test-file)")

    parser.add_argument("--min-coverage", type=int, help="设置最低覆盖率要求 (百分比)")

    args = parser.parse_args()

    # 验证参数
    if args.html and not args.coverage:
        print("❌ 错误: --html 选项需要同时使用 --coverage")
        return 1

    if args.xml and not args.coverage:
        print("❌ 错误: --xml 选项需要同时使用 --coverage")
        return 1

    if args.min_coverage and not args.coverage:
        print("❌ 错误: --min-coverage 选项需要同时使用 --coverage")
        return 1

    # 运行测试
    success = run_processor_tests(args)
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
