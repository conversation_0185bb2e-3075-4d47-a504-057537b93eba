# Mini-Boot WebSocket Starter 技术方案

## 1. 概述

Mini-Boot WebSocket Starter 是一个基于 FastAPI WebSocket 的实时通信扩展模块，提供注解驱动的 WebSocket 开发体验。该 Starter 遵循 Mini-Boot 框架的设计理念，提供开箱即用的 WebSocket 功能。

### 1.1 核心特性

-   **🔌 WebSocket 控制器**：通过 `@WebSocketController` 注解定义 WebSocket 控制器
-   **📨 消息处理**：通过 `@WebSocketOnMessage` 注解处理不同类型的消息
-   **🔗 连接管理**：通过 `@WebSocketOnConnect` 和 `@WebSocketOnDisconnect` 注解处理连接生命周期
-   **❌ 错误处理**：通过 `@WebSocketOnError` 注解处理 WebSocket 错误
-   **📡 消息广播**：通过 `@WebSocketSendTo` 注解定义消息发送目标
-   **🔐 认证支持**：集成 JWT 认证机制
-   **📊 会话管理**：完整的 WebSocket 会话生命周期管理
-   **⚡ 性能优化**：支持消息压缩、连接池和异步处理

### 1.2 技术验证

相关的技术验证将基于：

-   FastAPI WebSocket 支持
-   Python asyncio 异步编程
-   现有的 WebSocketScopeManager

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[WebSocketAutoConfiguration] --> B[WebSocketProperties]
    A --> C[WebSocketService]
    A --> D[WebSocketSessionManager]

    B --> E[ServerConfig]
    B --> F[SecurityConfig]
    B --> G[CompressionConfig]

    C --> H[WebSocketController]
    C --> I[MessageHandler]
    C --> J[ConnectionManager]

    D --> K[WebSocketSession]
    D --> L[SessionRegistry]

    M[用户应用] --> C
    N[application.yml] --> B

    C --> O[OnConnect]
    C --> P[OnMessage]
    C --> Q[OnDisconnect]
    C --> R[OnError]
```

### 2.2 模块划分

```
miniboot/starters/websocket/
├── META-INF/
│   └── mini.factories          # 自动配置注册文件
├── __init__.py                 # 模块导出
├── properties.py               # WebSocket配置属性
├── service.py                  # WebSocket核心服务
├── session.py                  # 会话管理
├── annotations.py              # WebSocket注解
├── handlers/
│   ├── __init__.py
│   ├── message_handler.py      # 消息处理器
│   ├── connection_handler.py   # 连接处理器
│   └── auth_handler.py         # 认证处理器
├── managers/
│   ├── __init__.py
│   ├── session_manager.py      # 会话管理器
│   └── connection_manager.py   # 连接管理器
├── exceptions.py               # WebSocket相关异常
└── configuration.py            # 自动配置类
```

## 3. 核心组件设计

### 3.1 配置属性类

```python
@ConfigurationProperties(prefix="miniboot.starters.websocket")
@dataclass
class WebSocketProperties(StarterProperties):
    """WebSocket配置属性"""

    # 基础配置
    enabled: bool = True
    path: str = "/ws"
    max_message_size: int = 512 * 1024  # 512KB

    # 服务器配置
    server: ServerConfig = field(default_factory=ServerConfig)

    # 安全配置
    security: SecurityConfig = field(default_factory=SecurityConfig)

    # 压缩配置
    compression: CompressionConfig = field(default_factory=CompressionConfig)

    # 超时配置
    timeout: TimeoutConfig = field(default_factory=TimeoutConfig)

    # 连接限制配置
    connection_limit: ConnectionLimitConfig = field(default_factory=ConnectionLimitConfig)

@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = "0.0.0.0"
    port: int = 8080
    read_timeout: int = 10
    write_timeout: int = 10
    idle_timeout: int = 60

@dataclass
class SecurityConfig:
    """安全配置"""
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    auth: AuthConfig = field(default_factory=AuthConfig)
    jwt: JWTConfig = field(default_factory=JWTConfig)

@dataclass
class AuthConfig:
    """认证配置"""
    enabled: bool = False
    token_header: str = "Authorization"
    token_query_param: str = "token"
    token_cookie: str = "auth_token"

@dataclass
class JWTConfig:
    """JWT配置"""
    enabled: bool = False
    secret_key: str = "mini-boot-websocket-jwt-secret-key"
    issuer: str = "mini-boot-websocket"
    expiration_time: int = 3600

@dataclass
class CompressionConfig:
    """压缩配置"""
    enabled: bool = False
    level: int = 6

@dataclass
class TimeoutConfig:
    """超时配置"""
    enabled: bool = False
    duration: int = 5
    ping_interval: int = 54
    pong_timeout: int = 60

@dataclass
class ConnectionLimitConfig:
    """连接限制配置"""
    enabled: bool = False
    max_connections_per_user: int = 5
    max_total_connections: int = 1000
```

### 3.2 WebSocket 注解系统

```python
def WebSocketController(path: str = ""):
    """WebSocket控制器装饰器

    标记一个类为WebSocket控制器，用于处理WebSocket连接。

    Args:
        path: WebSocket路径

    Example:
        @WebSocketController("/chat")
        class ChatController:
            pass
    """
    def decorator(cls):
        cls.__websocket_controller__ = True
        cls.__websocket_path__ = path
        return cls
    return decorator

def WebSocketOnConnect():
    """WebSocket连接建立事件装饰器

    标记方法处理WebSocket连接建立事件。

    Example:
        @WebSocketOnConnect()
        async def on_connect(self, session: WebSocketSession):
            pass
    """
    def decorator(func):
        func.__websocket_on_connect__ = True
        return func
    return decorator

def WebSocketOnDisconnect():
    """WebSocket连接断开事件装饰器

    标记方法处理WebSocket连接断开事件。

    Example:
        @WebSocketOnDisconnect()
        async def on_disconnect(self, session: WebSocketSession):
            pass
    """
    def decorator(func):
        func.__websocket_on_disconnect__ = True
        return func
    return decorator

def WebSocketOnMessage(message_type: str = "any"):
    """WebSocket消息处理装饰器

    标记方法处理WebSocket消息。

    Args:
        message_type: 消息类型 ("text", "binary", "json", "any")

    Example:
        @WebSocketOnMessage("text")
        async def on_text_message(self, session: WebSocketSession, message: str):
            pass
    """
    def decorator(func):
        func.__websocket_on_message__ = True
        func.__websocket_message_type__ = message_type
        return func
    return decorator

def WebSocketOnError():
    """WebSocket错误处理装饰器

    标记方法处理WebSocket错误。

    Example:
        @WebSocketOnError()
        async def on_error(self, session: WebSocketSession, error: Exception):
            pass
    """
    def decorator(func):
        func.__websocket_on_error__ = True
        return func
    return decorator

def WebSocketSendTo(target: str):
    """WebSocket消息发送装饰器

    标记方法的返回值发送给指定目标。

    Args:
        target: 发送目标 ("all", "sender", "user", "room")

    Example:
        @WebSocketSendTo("all")
        async def broadcast_message(self, message: str) -> str:
            return message
    """
    def decorator(func):
        func.__websocket_send_to__ = True
        func.__websocket_send_target__ = target
        return func
    return decorator
```

### 3.3 WebSocket 会话管理

```python
class WebSocketSession:
    """WebSocket会话类

    封装WebSocket连接的会话信息和操作方法。
    """

    def __init__(self, websocket: WebSocket, session_id: str):
        self.websocket = websocket
        self.session_id = session_id
        self.user_id: Optional[str] = None
        self.attributes: Dict[str, Any] = {}
        self.created_at = datetime.now()
        self.last_activity = datetime.now()

    def get_id(self) -> str:
        """获取会话ID"""
        return self.session_id

    def get_user_id(self) -> Optional[str]:
        """获取用户ID"""
        return self.user_id

    def set_user_id(self, user_id: str) -> None:
        """设置用户ID"""
        self.user_id = user_id

    def get_attribute(self, key: str) -> Any:
        """获取会话属性"""
        return self.attributes.get(key)

    def set_attribute(self, key: str, value: Any) -> None:
        """设置会话属性"""
        self.attributes[key] = value

    async def send_text(self, message: str) -> None:
        """发送文本消息"""
        await self.websocket.send_text(message)
        self.last_activity = datetime.now()

    async def send_bytes(self, data: bytes) -> None:
        """发送二进制消息"""
        await self.websocket.send_bytes(data)
        self.last_activity = datetime.now()

    async def send_json(self, data: Any) -> None:
        """发送JSON消息"""
        await self.websocket.send_json(data)
        self.last_activity = datetime.now()

    async def close(self, code: int = 1000, reason: str = "") -> None:
        """关闭连接"""
        await self.websocket.close(code, reason)

    def is_active(self) -> bool:
        """检查连接是否活跃"""
        return self.websocket.client_state == WebSocketState.CONNECTED

    def get_duration(self) -> timedelta:
        """获取会话持续时间"""
        return datetime.now() - self.created_at
```

### 3.4 WebSocket 服务

```python
class WebSocketService:
    """WebSocket核心服务

    提供WebSocket连接管理、消息处理和会话管理功能。
    """

    def __init__(self, properties: WebSocketProperties):
        self.properties = properties
        self.session_manager = WebSocketSessionManager()
        self.connection_manager = WebSocketConnectionManager()
        self.message_handler = WebSocketMessageHandler()
        self.auth_handler = WebSocketAuthHandler(properties.security)
        self._controllers: Dict[str, Any] = {}

    async def initialize(self) -> None:
        """初始化WebSocket服务"""
        await self.session_manager.initialize()
        await self.connection_manager.initialize()

    def register_controller(self, path: str, controller: Any) -> None:
        """注册WebSocket控制器"""
        self._controllers[path] = controller

    async def handle_websocket(self, websocket: WebSocket, path: str) -> None:
        """处理WebSocket连接"""
        # 认证检查
        if self.properties.security.auth.enabled:
            user_id = await self.auth_handler.authenticate(websocket)
            if not user_id:
                await websocket.close(code=4001, reason="Authentication failed")
                return
        else:
            user_id = None

        # 创建会话
        session = await self.session_manager.create_session(websocket, user_id)

        try:
            # 获取控制器
            controller = self._controllers.get(path)
            if not controller:
                await websocket.close(code=4004, reason="Controller not found")
                return

            # 处理连接建立
            await self._handle_connect(controller, session)

            # 消息循环
            async for message in websocket.iter_text():
                try:
                    await self._handle_message(controller, session, message)
                except Exception as e:
                    await self._handle_error(controller, session, e)

        except WebSocketDisconnect:
            # 处理连接断开
            await self._handle_disconnect(controller, session)
        except Exception as e:
            # 处理其他错误
            await self._handle_error(controller, session, e)
        finally:
            # 清理会话
            await self.session_manager.remove_session(session.get_id())

    async def _handle_connect(self, controller: Any, session: WebSocketSession) -> None:
        """处理连接建立事件"""
        for method_name in dir(controller):
            method = getattr(controller, method_name)
            if hasattr(method, '__websocket_on_connect__'):
                await method(session)

    async def _handle_disconnect(self, controller: Any, session: WebSocketSession) -> None:
        """处理连接断开事件"""
        for method_name in dir(controller):
            method = getattr(controller, method_name)
            if hasattr(method, '__websocket_on_disconnect__'):
                await method(session)

    async def _handle_message(self, controller: Any, session: WebSocketSession, message: str) -> None:
        """处理消息事件"""
        for method_name in dir(controller):
            method = getattr(controller, method_name)
            if hasattr(method, '__websocket_on_message__'):
                message_type = getattr(method, '__websocket_message_type__', 'any')
                if message_type in ['text', 'any']:
                    result = await method(session, message)

                    # 处理发送目标
                    if hasattr(method, '__websocket_send_to__'):
                        target = getattr(method, '__websocket_send_target__')
                        await self._send_to_target(target, session, result)

    async def _handle_error(self, controller: Any, session: WebSocketSession, error: Exception) -> None:
        """处理错误事件"""
        for method_name in dir(controller):
            method = getattr(controller, method_name)
            if hasattr(method, '__websocket_on_error__'):
                await method(session, error)

    async def _send_to_target(self, target: str, session: WebSocketSession, message: Any) -> None:
        """发送消息到指定目标"""
        if target == "sender":
            await session.send_json(message)
        elif target == "all":
            await self.session_manager.broadcast(message)
        elif target == "user":
            # 发送给特定用户的所有会话
            if isinstance(message, dict) and "user_id" in message:
                user_id = message["user_id"]
                await self.session_manager.send_to_user(user_id, message)
```

## 4. 配置管理

### 4.1 默认配置

```yaml
miniboot:
    starters:
        websocket:
            enabled: true
            path: "/ws"
            max_message_size: 524288 # 512KB

            # 服务器配置
            server:
                host: "0.0.0.0"
                port: 8080
                read_timeout: 10
                write_timeout: 10
                idle_timeout: 60

            # 安全配置
            security:
                allowed_origins: ["*"]
                auth:
                    enabled: false
                    token_header: "Authorization"
                    token_query_param: "token"
                    token_cookie: "auth_token"
                jwt:
                    enabled: false
                    secret_key: "mini-boot-websocket-jwt-secret-key"
                    issuer: "mini-boot-websocket"
                    expiration_time: 3600

            # 压缩配置
            compression:
                enabled: false
                level: 6

            # 超时配置
            timeout:
                enabled: false
                duration: 5
                ping_interval: 54
                pong_timeout: 60

            # 连接限制配置
            connection_limit:
                enabled: false
                max_connections_per_user: 5
                max_total_connections: 1000
```

### 4.2 环境特定配置

```yaml
# application-dev.yml (开发环境)
miniboot:
  starters:
    websocket:
      timeout:
        enabled: true
        duration: 30  # 开发环境超时时间较长
      connection_limit:
        max_total_connections: 100  # 开发环境连接数限制较小

# application-prod.yml (生产环境)
miniboot:
  starters:
    websocket:
      security:
        auth:
          enabled: true  # 生产环境启用认证
        jwt:
          enabled: true
          secret_key: "${JWT_SECRET_KEY}"  # 从环境变量获取
      compression:
        enabled: true  # 生产环境启用压缩
      connection_limit:
        enabled: true
        max_total_connections: 10000
```

## 5. 使用指南

### 5.1 基本使用

```python
from miniboot.annotations import Component
from miniboot.starters.websocket import (
    WebSocketController, WebSocketOnConnect, WebSocketOnDisconnect,
    WebSocketOnMessage, WebSocketOnError, WebSocketSendTo,
    WebSocketSession
)

@Component
@WebSocketController("/chat")
class ChatController:
    """聊天控制器"""

    def __init__(self):
        self.sessions: Dict[str, WebSocketSession] = {}

    @WebSocketOnConnect()
    async def on_connect(self, session: WebSocketSession):
        """处理连接建立"""
        self.sessions[session.get_id()] = session
        await session.send_text("Welcome to the chat room!")

        # 广播用户加入消息
        await self.broadcast_message(f"User {session.get_id()} joined the chat")

    @WebSocketOnMessage("text")
    async def on_text_message(self, session: WebSocketSession, message: str):
        """处理文本消息"""
        # 广播消息给所有用户
        await self.broadcast_message(f"User {session.get_id()}: {message}")

    @WebSocketOnMessage("json")
    async def on_json_message(self, session: WebSocketSession, message: dict):
        """处理JSON消息"""
        message_type = message.get("type")
        content = message.get("content")

        if message_type == "private":
            # 处理私聊消息
            target_user = message.get("target_user")
            await self.send_private_message(session, target_user, content)
        else:
            # 广播普通消息
            await self.broadcast_message(f"User {session.get_id()}: {content}")

    @WebSocketOnDisconnect()
    async def on_disconnect(self, session: WebSocketSession):
        """处理连接断开"""
        if session.get_id() in self.sessions:
            del self.sessions[session.get_id()]

        # 广播用户离开消息
        await self.broadcast_message(f"User {session.get_id()} left the chat")

    @WebSocketOnError()
    async def on_error(self, session: WebSocketSession, error: Exception):
        """处理错误"""
        print(f"Error occurred for session {session.get_id()}: {error}")

    @WebSocketSendTo("all")
    async def broadcast_message(self, message: str) -> str:
        """广播消息给所有用户"""
        return message

    async def send_private_message(self, sender: WebSocketSession, target_user_id: str, message: str):
        """发送私聊消息"""
        for session in self.sessions.values():
            if session.get_user_id() == target_user_id:
                await session.send_json({
                    "type": "private",
                    "from": sender.get_user_id() or sender.get_id(),
                    "message": message
                })
                break
```

### 5.2 认证集成

```python
@Component
@WebSocketController("/secure-chat")
class SecureChatController:
    """安全聊天控制器"""

    @WebSocketOnConnect()
    async def on_connect(self, session: WebSocketSession):
        """处理认证用户连接"""
        user_id = session.get_user_id()
        if not user_id:
            await session.close(code=4001, reason="Authentication required")
            return

        await session.send_text(f"Welcome, {user_id}!")

    @WebSocketOnMessage("text")
    async def on_authenticated_message(self, session: WebSocketSession, message: str):
        """处理认证用户的消息"""
        user_id = session.get_user_id()
        await self.broadcast_message(f"{user_id}: {message}")
```

## 6. 自动配置集成

### 6.1 自动配置类

```python
@Configuration
@ConditionalOnProperty(name="miniboot.starters.websocket.enabled", having_value="true", match_if_missing=True)
class WebSocketAutoConfiguration:
    """WebSocket自动配置类"""

    @Bean
    @ConditionalOnMissingBean
    def websocket_properties(self) -> WebSocketProperties:
        """WebSocket配置属性Bean"""
        return WebSocketProperties()

    @Bean
    @ConditionalOnMissingBean
    def websocket_session_manager(self, properties: WebSocketProperties) -> WebSocketSessionManager:
        """WebSocket会话管理器Bean"""
        return WebSocketSessionManager(properties)

    @Bean
    @ConditionalOnMissingBean
    def websocket_connection_manager(self, properties: WebSocketProperties) -> WebSocketConnectionManager:
        """WebSocket连接管理器Bean"""
        return WebSocketConnectionManager(properties)

    @Bean
    @ConditionalOnMissingBean
    def websocket_auth_handler(self, properties: WebSocketProperties) -> WebSocketAuthHandler:
        """WebSocket认证处理器Bean"""
        return WebSocketAuthHandler(properties.security)

    @Bean
    @ConditionalOnMissingBean
    def websocket_service(
        self,
        properties: WebSocketProperties,
        session_manager: WebSocketSessionManager,
        connection_manager: WebSocketConnectionManager,
        auth_handler: WebSocketAuthHandler
    ) -> WebSocketService:
        """WebSocket服务Bean"""
        return WebSocketService(properties, session_manager, connection_manager, auth_handler)

    @Bean
    @ConditionalOnMissingBean
    def websocket_annotation_processor(self, websocket_service: WebSocketService) -> WebSocketAnnotationProcessor:
        """WebSocket注解处理器Bean"""
        return WebSocketAnnotationProcessor(websocket_service)
```

### 6.2 META-INF 配置

```ini
# META-INF/mini.factories
miniboot.starters.websocket.configuration.WebSocketAutoConfiguration
```

## 7. 异常处理系统

### 7.1 异常定义

```python
class WebSocketException(Exception):
    """WebSocket异常基类"""

    def __init__(self, message: str, code: int = 1000):
        super().__init__(message)
        self.message = message
        self.code = code

class WebSocketConnectionException(WebSocketException):
    """WebSocket连接异常"""

    def __init__(self, message: str = "WebSocket connection error"):
        super().__init__(message, 1006)

class WebSocketAuthenticationException(WebSocketException):
    """WebSocket认证异常"""

    def __init__(self, message: str = "WebSocket authentication failed"):
        super().__init__(message, 4001)

class WebSocketMessageException(WebSocketException):
    """WebSocket消息异常"""

    def __init__(self, message: str = "WebSocket message error"):
        super().__init__(message, 1003)

class WebSocketSessionException(WebSocketException):
    """WebSocket会话异常"""

    def __init__(self, message: str = "WebSocket session error"):
        super().__init__(message, 1000)

class WebSocketControllerException(WebSocketException):
    """WebSocket控制器异常"""

    def __init__(self, message: str = "WebSocket controller error"):
        super().__init__(message, 1011)
```

### 7.2 全局异常处理

```python
class WebSocketExceptionHandler:
    """WebSocket全局异常处理器"""

    def __init__(self, properties: WebSocketProperties):
        self.properties = properties

    async def handle_exception(self, session: WebSocketSession, exception: Exception) -> None:
        """处理异常"""
        if isinstance(exception, WebSocketException):
            await self._handle_websocket_exception(session, exception)
        else:
            await self._handle_general_exception(session, exception)

    async def _handle_websocket_exception(self, session: WebSocketSession, exception: WebSocketException) -> None:
        """处理WebSocket异常"""
        try:
            await session.send_json({
                "type": "error",
                "code": exception.code,
                "message": exception.message
            })
        except Exception:
            # 如果发送错误消息失败，直接关闭连接
            await session.close(exception.code, exception.message)

    async def _handle_general_exception(self, session: WebSocketSession, exception: Exception) -> None:
        """处理一般异常"""
        try:
            await session.send_json({
                "type": "error",
                "code": 1011,
                "message": "Internal server error"
            })
        except Exception:
            await session.close(1011, "Internal server error")
```

## 8. 监控和指标

### 8.1 WebSocket 指标收集

```python
@Component
class WebSocketMetrics:
    """WebSocket指标收集器"""

    def __init__(self):
        # 连接指标
        self.active_connections = Counter("websocket.connections.active",
                                        description="Active WebSocket connections")
        self.total_connections = Counter("websocket.connections.total",
                                       description="Total WebSocket connections")

        # 消息指标
        self.messages_sent = Counter("websocket.messages.sent",
                                   description="Total messages sent")
        self.messages_received = Counter("websocket.messages.received",
                                       description="Total messages received")

        # 错误指标
        self.connection_errors = Counter("websocket.errors.connection",
                                       description="WebSocket connection errors")
        self.message_errors = Counter("websocket.errors.message",
                                    description="WebSocket message errors")

        # 会话指标
        self.session_duration = Timer("websocket.session.duration",
                                    description="WebSocket session duration")

    def record_connection(self, path: str):
        """记录连接建立"""
        self.active_connections.increment(tags={"path": path})
        self.total_connections.increment(tags={"path": path})

    def record_disconnection(self, path: str):
        """记录连接断开"""
        self.active_connections.decrement(tags={"path": path})

    def record_message_sent(self, path: str, message_type: str):
        """记录消息发送"""
        self.messages_sent.increment(tags={"path": path, "type": message_type})

    def record_message_received(self, path: str, message_type: str):
        """记录消息接收"""
        self.messages_received.increment(tags={"path": path, "type": message_type})

    def record_error(self, path: str, error_type: str):
        """记录错误"""
        if error_type == "connection":
            self.connection_errors.increment(tags={"path": path})
        elif error_type == "message":
            self.message_errors.increment(tags={"path": path})
```

### 8.2 健康检查

```python
@Component
class WebSocketHealthIndicator:
    """WebSocket健康检查指示器"""

    def __init__(self, session_manager: WebSocketSessionManager):
        self.session_manager = session_manager

    def check_health(self) -> dict:
        """检查WebSocket健康状态"""
        active_sessions = self.session_manager.get_active_session_count()
        total_sessions = self.session_manager.get_total_session_count()

        status = "UP" if active_sessions >= 0 else "DOWN"

        return {
            "status": status,
            "details": {
                "active_connections": active_sessions,
                "total_connections": total_sessions,
                "uptime": self._get_uptime(),
                "memory_usage": self._get_memory_usage()
            }
        }

    def _get_uptime(self) -> str:
        """获取运行时间"""
        # 实现运行时间计算
        return "1h 23m 45s"

    def _get_memory_usage(self) -> dict:
        """获取内存使用情况"""
        # 实现内存使用情况统计
        return {
            "used": "128MB",
            "total": "512MB",
            "percentage": 25
        }
```

## 9. 性能优化

### 9.1 连接池管理

```python
class WebSocketConnectionPool:
    """WebSocket连接池"""

    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.connections: Dict[str, WebSocketSession] = {}
        self.lock = asyncio.Lock()

    async def add_connection(self, session: WebSocketSession) -> bool:
        """添加连接到池中"""
        async with self.lock:
            if len(self.connections) >= self.max_size:
                return False

            self.connections[session.get_id()] = session
            return True

    async def remove_connection(self, session_id: str) -> None:
        """从池中移除连接"""
        async with self.lock:
            self.connections.pop(session_id, None)

    async def get_connection(self, session_id: str) -> Optional[WebSocketSession]:
        """获取连接"""
        return self.connections.get(session_id)

    async def broadcast(self, message: Any) -> None:
        """广播消息给所有连接"""
        async with self.lock:
            tasks = []
            for session in self.connections.values():
                if session.is_active():
                    tasks.append(session.send_json(message))

            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
```

### 9.2 消息压缩

```python
class WebSocketMessageCompressor:
    """WebSocket消息压缩器"""

    def __init__(self, enabled: bool = False, level: int = 6):
        self.enabled = enabled
        self.level = level

    def compress(self, data: bytes) -> bytes:
        """压缩数据"""
        if not self.enabled:
            return data

        import zlib
        return zlib.compress(data, self.level)

    def decompress(self, data: bytes) -> bytes:
        """解压数据"""
        if not self.enabled:
            return data

        import zlib
        return zlib.decompress(data)
```

## 10. 测试支持

### 10.1 WebSocket 测试工具

```python
class WebSocketTestHelper:
    """WebSocket测试辅助工具"""

    @staticmethod
    def create_mock_websocket():
        """创建模拟WebSocket连接"""
        return MockWebSocket()

    @staticmethod
    def create_test_session(session_id: str = "test-session", user_id: str = None):
        """创建测试会话"""
        from miniboot.starters.websocket import WebSocketSession

        mock_websocket = WebSocketTestHelper.create_mock_websocket()
        session = WebSocketSession(mock_websocket, session_id)

        if user_id:
            session.set_user_id(user_id)

        return session, mock_websocket

    @staticmethod
    def assert_message_sent(mock_websocket, message_type: str, expected_content=None):
        """断言消息已发送"""
        sent_messages = mock_websocket.sent_messages

        # 查找指定类型的消息
        matching_messages = [msg for msg in sent_messages if msg["type"] == message_type]

        if not matching_messages:
            raise AssertionError(f"No {message_type} message was sent")

        if expected_content is not None:
            last_message = matching_messages[-1]
            if expected_content not in str(last_message["data"]):
                raise AssertionError(f"Expected content '{expected_content}' not found in message: {last_message['data']}")

    @staticmethod
    def assert_no_messages_sent(mock_websocket):
        """断言没有消息被发送"""
        if mock_websocket.sent_messages:
            raise AssertionError(f"Expected no messages, but {len(mock_websocket.sent_messages)} messages were sent")

class WebSocketServiceTestHelper:
    """WebSocket服务测试辅助工具"""

    @staticmethod
    def create_test_service():
        """创建测试WebSocket服务"""
        from miniboot.starters.websocket import WebSocketService, WebSocketProperties

        properties = WebSocketProperties()
        properties.enabled = True
        properties.path = "/test"

        return WebSocketService(properties)

    @staticmethod
    def create_test_controller():
        """创建测试控制器"""
        from miniboot.starters.websocket.annotations import (
            WebSocketController, WebSocketOnConnect, WebSocketOnMessage
        )

        @WebSocketController("/test")
        class TestController:
            def __init__(self):
                self.events = []

            @WebSocketOnConnect()
            async def on_connect(self, session):
                self.events.append(("connect", session.get_id()))
                await session.send_text("Welcome!")

            @WebSocketOnMessage("text")
            async def on_message(self, session, message):
                self.events.append(("message", message))
                await session.send_text(f"Received: {message}")

        return TestController()
```

### 10.2 测试示例

```python
import unittest
import asyncio
from unittest.mock import Mock, AsyncMock
from miniboot import Application
from miniboot.annotations import Component
from miniboot.starters.websocket import (
    WebSocketSession, WebSocketService, WebSocketProperties
)

class WebSocketServiceTest(unittest.TestCase):
    """WebSocket服务测试"""

    def setUp(self):
        """测试前置设置"""
        self.properties = WebSocketProperties()
        self.websocket_service = WebSocketService(self.properties)

    def test_websocket_properties_validation(self):
        """测试WebSocket配置属性验证"""
        # 测试默认配置
        self.assertTrue(self.properties.enabled)
        self.assertEqual(self.properties.path, "/ws")
        self.assertEqual(self.properties.max_message_size, 524288)

        # 测试配置验证
        try:
            self.properties.validate()
        except Exception as e:
            self.fail(f"配置验证失败: {e}")

    def test_websocket_session_creation(self):
        """测试WebSocket会话创建"""
        # 模拟WebSocket连接
        mock_websocket = Mock()
        mock_websocket.client_state = "CONNECTED"

        # 创建会话
        session = WebSocketSession(mock_websocket, "test-session-id")

        # 验证会话属性
        self.assertEqual(session.get_id(), "test-session-id")
        self.assertIsNone(session.get_user_id())
        self.assertTrue(session.is_active())

        # 测试会话属性设置
        session.set_user_id("test-user")
        self.assertEqual(session.get_user_id(), "test-user")

        session.set_attribute("test_key", "test_value")
        self.assertEqual(session.get_attribute("test_key"), "test_value")

    def test_websocket_controller_registration(self):
        """测试WebSocket控制器注册"""
        # 模拟控制器
        mock_controller = Mock()
        mock_controller.__websocket_controller__ = True
        mock_controller.__websocket_path__ = "/test"

        # 注册控制器
        self.websocket_service.register_controller("/test", mock_controller)

        # 验证控制器已注册
        self.assertIn("/test", self.websocket_service._controllers)
        self.assertEqual(self.websocket_service._controllers["/test"], mock_controller)

class WebSocketAnnotationTest(unittest.TestCase):
    """WebSocket注解测试"""

    def test_websocket_controller_annotation(self):
        """测试WebSocket控制器注解"""
        from miniboot.starters.websocket.annotations import WebSocketController

        @WebSocketController("/test")
        class TestController:
            pass

        # 验证注解属性
        self.assertTrue(hasattr(TestController, '__websocket_controller__'))
        self.assertTrue(TestController.__websocket_controller__)
        self.assertEqual(TestController.__websocket_path__, "/test")

    def test_websocket_message_annotation(self):
        """测试WebSocket消息注解"""
        from miniboot.starters.websocket.annotations import WebSocketOnMessage

        class TestController:
            @WebSocketOnMessage("text")
            def handle_text_message(self, session, message):
                pass

            @WebSocketOnMessage("json")
            def handle_json_message(self, session, message):
                pass

        # 验证注解属性
        text_handler = TestController.handle_text_message
        self.assertTrue(hasattr(text_handler, '__websocket_on_message__'))
        self.assertEqual(text_handler.__websocket_message_type__, "text")

        json_handler = TestController.handle_json_message
        self.assertTrue(hasattr(json_handler, '__websocket_on_message__'))
        self.assertEqual(json_handler.__websocket_message_type__, "json")

class WebSocketExceptionTest(unittest.TestCase):
    """WebSocket异常测试"""

    def test_websocket_exceptions(self):
        """测试WebSocket异常"""
        from miniboot.starters.websocket.exceptions import (
            WebSocketException, WebSocketConnectionException,
            WebSocketAuthenticationException, WebSocketMessageException
        )

        # 测试基础异常
        base_exception = WebSocketException("Test error", 1000)
        self.assertEqual(base_exception.message, "Test error")
        self.assertEqual(base_exception.code, 1000)

        # 测试连接异常
        conn_exception = WebSocketConnectionException()
        self.assertEqual(conn_exception.code, 1006)

        # 测试认证异常
        auth_exception = WebSocketAuthenticationException()
        self.assertEqual(auth_exception.code, 4001)

        # 测试消息异常
        msg_exception = WebSocketMessageException()
        self.assertEqual(msg_exception.code, 1003)

# 运行测试
if __name__ == "__main__":
    unittest.main()
```

### 10.3 模拟测试工具

```python
class MockWebSocket:
    """模拟WebSocket连接"""

    def __init__(self):
        self.client_state = "CONNECTED"
        self.sent_messages = []
        self.received_messages = []

    async def send_text(self, message: str):
        """模拟发送文本消息"""
        self.sent_messages.append({"type": "text", "data": message})

    async def send_json(self, data: dict):
        """模拟发送JSON消息"""
        self.sent_messages.append({"type": "json", "data": data})

    async def send_bytes(self, data: bytes):
        """模拟发送二进制消息"""
        self.sent_messages.append({"type": "bytes", "data": data})

    async def close(self, code: int = 1000, reason: str = ""):
        """模拟关闭连接"""
        self.client_state = "DISCONNECTED"

    def add_received_message(self, message_type: str, data):
        """添加接收到的消息（用于测试）"""
        self.received_messages.append({"type": message_type, "data": data})

class WebSocketControllerTest(unittest.TestCase):
    """WebSocket控制器功能测试"""

    def setUp(self):
        """测试前置设置"""
        from miniboot.starters.websocket import WebSocketSession

        # 创建模拟WebSocket和会话
        self.mock_websocket = MockWebSocket()
        self.session = WebSocketSession(self.mock_websocket, "test-session")

        # 创建测试控制器
        self.controller = self.create_test_controller()

    def create_test_controller(self):
        """创建测试控制器"""
        from miniboot.starters.websocket.annotations import (
            WebSocketController, WebSocketOnConnect, WebSocketOnMessage,
            WebSocketOnDisconnect, WebSocketOnError
        )

        @WebSocketController("/test")
        class TestController:
            def __init__(self):
                self.connected_sessions = []
                self.received_messages = []
                self.errors = []

            @WebSocketOnConnect()
            async def on_connect(self, session):
                self.connected_sessions.append(session)
                await session.send_text("Connected successfully")

            @WebSocketOnMessage("text")
            async def on_text_message(self, session, message):
                self.received_messages.append(message)
                await session.send_text(f"Echo: {message}")

            @WebSocketOnMessage("json")
            async def on_json_message(self, session, message):
                self.received_messages.append(message)
                await session.send_json({"echo": message})

            @WebSocketOnDisconnect()
            async def on_disconnect(self, session):
                if session in self.connected_sessions:
                    self.connected_sessions.remove(session)

            @WebSocketOnError()
            async def on_error(self, session, error):
                self.errors.append(error)

        return TestController()

    def test_controller_connect_handler(self):
        """测试连接处理器"""
        async def run_test():
            await self.controller.on_connect(self.session)

            # 验证会话已添加
            self.assertIn(self.session, self.controller.connected_sessions)

            # 验证发送了连接消息
            self.assertEqual(len(self.mock_websocket.sent_messages), 1)
            sent_message = self.mock_websocket.sent_messages[0]
            self.assertEqual(sent_message["type"], "text")
            self.assertEqual(sent_message["data"], "Connected successfully")

        asyncio.run(run_test())

    def test_controller_text_message_handler(self):
        """测试文本消息处理器"""
        async def run_test():
            test_message = "Hello, WebSocket!"
            await self.controller.on_text_message(self.session, test_message)

            # 验证消息已记录
            self.assertIn(test_message, self.controller.received_messages)

            # 验证发送了回复
            self.assertEqual(len(self.mock_websocket.sent_messages), 1)
            sent_message = self.mock_websocket.sent_messages[0]
            self.assertEqual(sent_message["type"], "text")
            self.assertEqual(sent_message["data"], f"Echo: {test_message}")

        asyncio.run(run_test())

    def test_controller_json_message_handler(self):
        """测试JSON消息处理器"""
        async def run_test():
            test_message = {"type": "greeting", "content": "Hello JSON!"}
            await self.controller.on_json_message(self.session, test_message)

            # 验证消息已记录
            self.assertIn(test_message, self.controller.received_messages)

            # 验证发送了JSON回复
            self.assertEqual(len(self.mock_websocket.sent_messages), 1)
            sent_message = self.mock_websocket.sent_messages[0]
            self.assertEqual(sent_message["type"], "json")
            self.assertEqual(sent_message["data"], {"echo": test_message})

        asyncio.run(run_test())

    def test_controller_disconnect_handler(self):
        """测试断开连接处理器"""
        async def run_test():
            # 先连接
            await self.controller.on_connect(self.session)
            self.assertIn(self.session, self.controller.connected_sessions)

            # 然后断开
            await self.controller.on_disconnect(self.session)
            self.assertNotIn(self.session, self.controller.connected_sessions)

        asyncio.run(run_test())

    def test_controller_error_handler(self):
        """测试错误处理器"""
        async def run_test():
            test_error = Exception("Test error")
            await self.controller.on_error(self.session, test_error)

            # 验证错误已记录
            self.assertIn(test_error, self.controller.errors)

        asyncio.run(run_test())

# 运行控制器测试
if __name__ == "__main__":
    unittest.main()
```

### 10.3 模拟测试工具

```python
class MockWebSocket:
    """模拟WebSocket连接"""

    def __init__(self):
        self.client_state = "CONNECTED"
        self.sent_messages = []
        self.received_messages = []

    async def send_text(self, message: str):
        """模拟发送文本消息"""
        self.sent_messages.append({"type": "text", "data": message})

    async def send_json(self, data: dict):
        """模拟发送JSON消息"""
        self.sent_messages.append({"type": "json", "data": data})

    async def send_bytes(self, data: bytes):
        """模拟发送二进制消息"""
        self.sent_messages.append({"type": "bytes", "data": data})

    async def close(self, code: int = 1000, reason: str = ""):
        """模拟关闭连接"""
        self.client_state = "DISCONNECTED"

    def add_received_message(self, message_type: str, data):
        """添加接收到的消息（用于测试）"""
        self.received_messages.append({"type": message_type, "data": data})

class WebSocketTestHelper:
    """WebSocket测试辅助工具"""

    @staticmethod
    def create_mock_websocket():
        """创建模拟WebSocket连接"""
        return MockWebSocket()

    @staticmethod
    def create_test_session(session_id: str = "test-session", user_id: str = None):
        """创建测试会话"""
        from miniboot.starters.websocket import WebSocketSession

        mock_websocket = WebSocketTestHelper.create_mock_websocket()
        session = WebSocketSession(mock_websocket, session_id)

        if user_id:
            session.set_user_id(user_id)

        return session, mock_websocket

    @staticmethod
    def assert_message_sent(mock_websocket, message_type: str, expected_content=None):
        """断言消息已发送"""
        sent_messages = mock_websocket.sent_messages

        # 查找指定类型的消息
        matching_messages = [msg for msg in sent_messages if msg["type"] == message_type]

        if not matching_messages:
            raise AssertionError(f"No {message_type} message was sent")

        if expected_content is not None:
            last_message = matching_messages[-1]
            if expected_content not in str(last_message["data"]):
                raise AssertionError(f"Expected content '{expected_content}' not found in message: {last_message['data']}")

    @staticmethod
    def assert_no_messages_sent(mock_websocket):
        """断言没有消息被发送"""
        if mock_websocket.sent_messages:
            raise AssertionError(f"Expected no messages, but {len(mock_websocket.sent_messages)} messages were sent")

# 运行测试
if __name__ == "__main__":
    unittest.main()
```

## 11. 总结

Mini-Boot WebSocket Starter 提供了完整的实时通信解决方案，具有以下特点：

### 11.1 核心优势

-   **🚀 开箱即用**：简单配置即可启用 WebSocket 功能
-   **📝 注解驱动**：基于注解的编程模型，简化开发
-   **🔧 高度可配置**：丰富的配置选项满足不同需求
-   **🔐 安全可靠**：内置认证和授权机制
-   **📊 监控完善**：完整的指标收集和健康检查
-   **⚡ 性能优化**：连接池、消息压缩等优化特性
-   **🧪 测试友好**：提供完整的测试工具和示例

### 11.2 适用场景

-   **💬 实时聊天**：聊天室、即时通讯应用
-   **📊 实时数据**：股票行情、监控数据推送
-   **🎮 在线游戏**：多人在线游戏、实时对战
-   **📢 通知推送**：系统通知、消息推送
-   **🔄 协同编辑**：在线文档、协同工具

### 11.3 技术特色

-   **异步处理**：基于 Python asyncio 的高性能异步处理
-   **框架集成**：与 Mini-Boot 框架深度集成，享受依赖注入等特性
-   **扩展性强**：模块化设计，易于扩展和定制
-   **生产就绪**：完善的错误处理、监控和运维支持

---

_本文档定义了 Mini-Boot 框架的 WebSocket Starter 完整技术方案，为开发者提供企业级的实时通信解决方案。_
