"""
Backpressure Auto Configuration

背压控制自动配置类，提供 BackpressureController 的自动配置和 Bean 注册。
"""

from typing import Optional

from miniboot.annotations import Bean
from miniboot.autoconfigure import (AutoConfiguration,
                                    AutoConfigurationMetadata,
                                    ConditionalOnProperty)

from ..controller import BackpressureController
from ..properties import BackpressureProperties


@ConditionalOnProperty(name="miniboot.starters.web.backpressure.enabled", match_if_missing=True)
class BackpressureAutoConfiguration(AutoConfiguration):
    """背压控制自动配置
    
    当满足以下条件时自动配置 BackpressureController：
    1. 配置属性 miniboot.starters.web.backpressure.enabled 为 true（默认为 true）
    
    提供的 Bean：
    - backpressure_properties: BackpressureProperties 配置属性
    - backpressure_controller: BackpressureController 背压控制器实例
    """
    
    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取自动配置元数据
        
        Returns:
            自动配置元数据
        """
        return AutoConfigurationMetadata(
            name="backpressure-auto-configuration",
            description="Web 背压控制自动配置，提供智能负载管理和流量控制功能",
            priority=500,  # 高优先级，在其他组件之后启动
        )
    
    @Bean
    def backpressure_properties(self) -> BackpressureProperties:
        """创建背压控制配置属性 Bean
        
        Returns:
            BackpressureProperties 实例
        """
        properties = BackpressureProperties()
        
        # 验证配置参数
        properties.validate()
        
        return properties
    
    @Bean
    def backpressure_controller(self, properties: BackpressureProperties) -> BackpressureController:
        """创建背压控制器 Bean
        
        Args:
            properties: 背压控制配置属性（自动注入）
            
        Returns:
            BackpressureController 实例
        """
        # 创建 BackpressureController 实例
        controller = BackpressureController(properties)
        
        # 容器会自动调用 @PostConstruct 方法初始化控制器
        
        return controller