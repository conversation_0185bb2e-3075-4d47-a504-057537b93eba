#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置合并器 - 实现属性级别的配置合并策略
"""

import threading
from copy import deepcopy
from typing import Any

from ..utils.singleton import SingletonMeta
from .priority import ConfigurationMergeStrategy, get_priority


class ConfigurationMerger(metaclass=SingletonMeta):
    """配置合并器

    实现不同合并策略的配置属性合并功能,支持覆盖、合并、追加等策略.
    """

    # 类级别的初始化锁
    _init_lock = threading.RLock()

    def __init__(self):
        """初始化配置合并器"""
        if not hasattr(self, '_initialized'):
            with self._init_lock:
                # 双重检查：防止竞态条件
                if not hasattr(self, '_initialized'):
                    self._priority_manager = get_priority()
                    # 实例级别的操作锁
                    self._operation_lock = threading.RLock()
                    self._initialized = True

    def merge_configs(self, high_priority_config: dict[str, Any], low_priority_config: dict[str, Any]) -> dict[str, Any]:
        """合并两个配置对象

        Args:
            high_priority_config: 高优先级配置
            low_priority_config: 低优先级配置

        Returns:
            合并后的配置
        """
        with self._operation_lock:
            if not high_priority_config:
                return deepcopy(low_priority_config) if low_priority_config else {}

            if not low_priority_config:
                return deepcopy(high_priority_config)

            # 深拷贝高优先级配置作为基础
            result = deepcopy(high_priority_config)

            # 递归合并低优先级配置(只合并高优先级中不存在的键)
            self._merge_missing_keys(result, low_priority_config, "")

            return result

    def _merge_missing_keys(self, target: dict[str, Any], source: dict[str, Any], prefix: str) -> None:
        """合并缺失的键(只添加目标中不存在的键)

        Args:
            target: 目标配置(会被修改)
            source: 源配置
            prefix: 当前属性路径前缀
        """
        for key, value in source.items():
            current_path = f"{prefix}.{key}" if prefix else key

            if key not in target:
                # 目标中不存在该键,直接设置
                target[key] = deepcopy(value)
            elif isinstance(target[key], dict) and isinstance(value, dict):
                # 两者都是字典,递归合并
                self._merge_missing_keys(target[key], value, current_path)
            else:
                # 目标中已存在该键且不是字典,根据策略决定是否合并
                merge_strategy = self._priority_manager.get_merge_strategy(current_path)
                if merge_strategy in [ConfigurationMergeStrategy.APPEND, ConfigurationMergeStrategy.PREPEND]:
                    target[key] = self._merge_values(target[key], value, merge_strategy, current_path)

    def _merge_values(self, target_value: Any, source_value: Any, strategy: str, property_path: str) -> Any:
        """合并两个值

        Args:
            target_value: 目标值
            source_value: 源值
            strategy: 合并策略
            property_path: 属性路径

        Returns:
            合并后的值
        """
        if strategy == ConfigurationMergeStrategy.OVERRIDE:
            return deepcopy(source_value)

        elif strategy == ConfigurationMergeStrategy.MERGE:
            return self._merge_objects(target_value, source_value, property_path)

        elif strategy == ConfigurationMergeStrategy.APPEND:
            return self._append_values(target_value, source_value)

        elif strategy == ConfigurationMergeStrategy.PREPEND:
            return self._prepend_values(target_value, source_value)

        else:
            # 默认覆盖策略
            return deepcopy(source_value)

    def _merge_objects(self, target: Any, source: Any, property_path: str) -> Any:
        """合并对象

        Args:
            target: 目标对象
            source: 源对象
            property_path: 属性路径

        Returns:
            合并后的对象
        """
        # 如果两者都是字典,递归合并
        if isinstance(target, dict) and isinstance(source, dict):
            result = deepcopy(target)
            self._merge_recursive(result, source, property_path)
            return result

        # 如果两者都是列表,合并列表
        elif isinstance(target, list) and isinstance(source, list):
            return self._merge_lists(target, source)

        # 其他情况使用覆盖策略
        else:
            return deepcopy(source)

    def _merge_lists(self, target_list: list[Any], source_list: list[Any]) -> list[Any]:
        """合并列表

        Args:
            target_list: 目标列表
            source_list: 源列表

        Returns:
            合并后的列表
        """
        # 对于列表,默认使用追加策略
        result = deepcopy(target_list)
        for item in source_list:
            if item not in result:
                result.append(deepcopy(item))
        return result

    def _append_values(self, target: Any, source: Any) -> Any:
        """追加值

        Args:
            target: 目标值
            source: 源值

        Returns:
            追加后的值
        """
        # 如果目标不是列表,转换为列表
        target = ([target] if target is not None else []) if not isinstance(target, list) else deepcopy(target)

        # 如果源是列表,追加所有元素
        if isinstance(source, list):
            for item in source:
                if item not in target:
                    target.append(deepcopy(item))
        else:
            # 如果源不是列表,直接追加
            if source not in target:
                target.append(deepcopy(source))

        return target

    def _prepend_values(self, target: Any, source: Any) -> Any:
        """前置插入值

        Args:
            target: 目标值
            source: 源值

        Returns:
            前置插入后的值
        """
        # 如果目标不是列表,转换为列表
        target = ([target] if target is not None else []) if not isinstance(target, list) else deepcopy(target)

        # 如果源是列表,前置插入所有元素
        if isinstance(source, list):
            for item in reversed(source):  # 反向插入保持顺序
                if item not in target:
                    target.insert(0, deepcopy(item))
        else:
            # 如果源不是列表,直接前置插入
            if source not in target:
                target.insert(0, deepcopy(source))

        return target

    def merge_property_sources(self, property_sources_data: list[dict[str, Any]]) -> dict[str, Any]:
        """合并多个属性源的数据

        Args:
            property_sources_data: 属性源数据列表,按优先级排序(高优先级在前)

        Returns:
            合并后的配置数据
        """
        if not property_sources_data:
            return {}

        # 从最低优先级开始合并
        result = {}
        for config_data in reversed(property_sources_data):
            result = self.merge_configurations(config_data, result)

        return result

    def validate_merge_result(self, merged_config: dict[str, Any]) -> bool:
        """验证合并结果

        Args:
            merged_config: 合并后的配置

        Returns:
            True if validation passes
        """
        try:
            # 基本验证:确保配置是有效的字典
            if not isinstance(merged_config, dict):
                return False

            # 验证必需的配置项
            required_keys = ["miniboot"]
            return all(key in merged_config for key in required_keys)

        except Exception:
            return False
