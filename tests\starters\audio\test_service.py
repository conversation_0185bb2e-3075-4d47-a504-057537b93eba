#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频服务测试
"""

import pytest
from unittest.mock import patch
from miniboot.starters.audio.service import AudioService
from miniboot.starters.audio.properties import AudioProperties


class TestAudioService:
    """音频服务测试"""

    @pytest.fixture
    def audio_properties(self):
        """创建音频配置属性"""
        return AudioProperties()

    @pytest.fixture
    def audio_service(self, audio_properties):
        """创建音频服务"""
        return AudioService(audio_properties)

    def test_initialization(self, audio_service):
        """测试初始化"""
        assert audio_service.properties is not None
        assert audio_service.audio_player is None
        assert audio_service.tts_player is None
        assert audio_service._initialized is False

    @pytest.mark.asyncio
    async def test_initialize_with_enabled_features(self, audio_service):
        """测试启用功能的初始化"""
        with patch("miniboot.starters.audio.service.MP3Player") as mock_mp3, patch("miniboot.starters.audio.service.ChineseTTSPlayer") as mock_tts:
            await audio_service.initialize()

            assert audio_service._initialized is True
            mock_mp3.assert_called_once()
            mock_tts.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_with_disabled_player(self, audio_service):
        """测试禁用音频播放器的初始化"""
        audio_service.properties.player.enabled = False

        with patch("miniboot.starters.audio.service.ChineseTTSPlayer") as mock_tts:
            await audio_service.initialize()

            assert audio_service.audio_player is None
            mock_tts.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_with_disabled_tts(self, audio_service):
        """测试禁用TTS的初始化"""
        audio_service.properties.tts.enabled = False

        with patch("miniboot.starters.audio.service.MP3Player") as mock_mp3:
            await audio_service.initialize()

            assert audio_service.tts_player is None
            mock_mp3.assert_called_once()

    @pytest.mark.asyncio
    async def test_play_file_disabled(self, audio_service):
        """测试禁用音频播放时的文件播放"""
        audio_service.properties.player.enabled = False

        result = await audio_service.play_file("test.mp3")
        assert result is False

    @pytest.mark.asyncio
    async def test_speak_disabled(self, audio_service):
        """测试禁用TTS时的语音播放"""
        audio_service.properties.tts.enabled = False

        result = await audio_service.speak("测试文本")
        assert result is False

    def test_is_audio_enabled(self, audio_service):
        """测试音频启用状态检查"""
        assert audio_service.is_audio_enabled() is True

        audio_service.properties.player.enabled = False
        assert audio_service.is_audio_enabled() is False

    def test_is_tts_enabled(self, audio_service):
        """测试TTS启用状态检查"""
        assert audio_service.is_tts_enabled() is True

        audio_service.properties.tts.enabled = False
        assert audio_service.is_tts_enabled() is False

    def test_set_audio_volume(self, audio_service):
        """测试设置音频音量"""
        audio_service.set_audio_volume(0.5)
        assert audio_service.properties.player.volume == 0.5

        # 测试无效音量
        audio_service.set_audio_volume(1.5)
        assert audio_service.properties.player.volume == 0.5  # 不应该改变

    def test_set_tts_properties(self, audio_service):
        """测试设置TTS属性"""
        audio_service.set_tts_properties(rate=200, volume=0.8)
        assert audio_service.properties.tts.rate == 200
        assert audio_service.properties.tts.volume == 0.8

    def test_is_audio_file(self, audio_service):
        """测试音频文件识别"""
        # 测试音频文件扩展名
        assert audio_service._is_audio_file("test.mp3") is True
        assert audio_service._is_audio_file("test.wav") is True
        assert audio_service._is_audio_file("test.txt") is False

        # 测试无效路径
        assert audio_service._is_audio_file("") is False
        assert audio_service._is_audio_file("invalid path with spaces") is False
