#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Monitor Starter配置属性
"""

from dataclasses import dataclass

from miniboot.annotations import ConfigurationProperties
from miniboot.autoconfigure.properties import StarterProperties


@ConfigurationProperties(prefix="miniboot.starters.monitor")
@dataclass
class MonitorProperties(StarterProperties):
    """Monitor Starter配置属性

    用于配置监控功能的基本参数.
    """

    # 基础配置
    enabled: bool = True  # 是否启用监控功能
    auto_start: bool = True  # 是否自动启动监控
    collect_interval: int = 60  # 数据收集间隔(秒)
    metrics_enabled: bool = True  # 是否启用指标收集
    health_enabled: bool = True  # 是否启用健康检查
