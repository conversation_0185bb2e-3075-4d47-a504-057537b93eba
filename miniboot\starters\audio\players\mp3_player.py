#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: MP3音频播放器实现
"""

import time
from pathlib import Path

from ..exceptions import AudioPlayerException, AudioFileException


class MP3Player:
    """MP3音频播放器

    基于pygame实现的音频文件播放器,支持多种音频格式.
    """

    def __init__(self, volume: float = 1.0, timeout_seconds: int = 30):
        self.volume = volume
        self.timeout_seconds = timeout_seconds
        self.enabled = True
        self._pygame = None

    def _init_pygame(self):
        """延迟初始化pygame"""
        if self._pygame is None:
            try:
                import pygame

                pygame.mixer.init()
                self._pygame = pygame
            except ImportError:
                raise AudioPlayerException("pygame库未安装,无法播放音频文件")
            except Exception as e:
                raise AudioPlayerException(f"初始化pygame失败: {e}")

    def play(self, file_path: str) -> bool:
        """播放音频文件

        Args:
            file_path: 音频文件路径

        Returns:
            bool: 播放是否成功
        """
        if not self.enabled:
            return False

        try:
            # 验证文件
            if not self._validate_file(file_path):
                return False

            # 初始化pygame
            self._init_pygame()

            # 加载并播放音频
            self._pygame.mixer.music.load(file_path)
            self._pygame.mixer.music.set_volume(self.volume)
            self._pygame.mixer.music.play()

            # 等待播放完成
            start_time = time.time()
            while self._pygame.mixer.music.get_busy():
                if time.time() - start_time > self.timeout_seconds:
                    self._pygame.mixer.music.stop()
                    raise AudioPlayerException(f"音频播放超时 ({self.timeout_seconds}秒)")
                time.sleep(0.1)

            return True

        except AudioPlayerException:
            raise
        except Exception as e:
            raise AudioPlayerException(f"播放音频文件失败: {e}")

    def play_multiple(self, file_paths: list[str], interval: float = 0.1) -> bool:
        """连续播放多个音频文件

        Args:
            file_paths: 音频文件路径列表
            interval: 播放间隔(秒)

        Returns:
            bool: 是否全部播放成功
        """
        if not self.enabled:
            return False

        success_count = 0
        total_count = len(file_paths)

        for i, file_path in enumerate(file_paths, 1):
            if self.play(file_path):
                success_count += 1

            # 播放间隔
            if i < total_count and interval > 0:
                time.sleep(interval)

        return success_count == total_count

    def stop(self) -> None:
        """停止播放"""
        if self._pygame:
            self._pygame.mixer.music.stop()

    def set_volume(self, volume: float) -> None:
        """设置音量

        Args:
            volume: 音量值 (0.0-1.0)
        """
        if 0.0 <= volume <= 1.0:
            self.volume = volume
            if self._pygame:
                self._pygame.mixer.music.set_volume(volume)

    def _validate_file(self, file_path: str) -> bool:
        """验证音频文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 文件是否有效
        """
        if not file_path or not file_path.strip():
            raise AudioFileException("音频文件路径不能为空")

        file_path_obj = Path(file_path)

        if not file_path_obj.exists():
            raise AudioFileException(f"音频文件不存在: {file_path}")

        if not file_path_obj.is_file():
            raise AudioFileException(f"路径不是文件: {file_path}")

        # 检查文件扩展名
        supported_formats = [".mp3", ".wav", ".m4a", ".ogg"]
        if file_path_obj.suffix.lower() not in supported_formats:
            raise AudioFileException(f"不支持的音频格式: {file_path_obj.suffix}")

        return True
