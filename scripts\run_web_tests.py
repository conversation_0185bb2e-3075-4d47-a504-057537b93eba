#!/usr/bin/env python
"""
Web模块测试执行脚本

执行Web模块的所有测试并生成详细的测试报告和覆盖率报告.

使用方法:
    python scripts/run_web_tests.py
    python scripts/run_web_tests.py --coverage
    python scripts/run_web_tests.py --performance
    python scripts/run_web_tests.py --all
"""

import argparse
import os
import subprocess
import sys
import time
from pathlib import Path


def setup_environment():
    """设置测试环境"""
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))

    # 设置环境变量
    os.environ["PYTHONPATH"] = str(project_root)

    return project_root


def run_basic_tests(project_root):
    """运行基础测试"""
    print("🧪 运行Web模块基础测试...")
    print("=" * 60)

    test_dir = project_root / "tests" / "web"

    # 运行pytest
    cmd = [sys.executable, "-m", "pytest", str(test_dir), "-v", "--tb=short", "--durations=10"]

    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)

        print("STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        return result.returncode == 0

    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False


def run_coverage_tests(project_root):
    """运行覆盖率测试"""
    print("\n📊 运行Web模块覆盖率测试...")
    print("=" * 60)

    test_dir = project_root / "tests" / "web"

    # 运行pytest with coverage
    cmd = [
        sys.executable,
        "-m",
        "pytest",
        str(test_dir),
        "--cov=miniboot.web",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-report=xml",
        "--cov-fail-under=85",  # 要求覆盖率至少85%
        "-v",
    ]

    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)

        print("STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        # 检查HTML报告
        html_report = project_root / "htmlcov" / "index.html"
        if html_report.exists():
            print(f"\n📄 HTML覆盖率报告: {html_report}")

        return result.returncode == 0

    except Exception as e:
        print(f"❌ 运行覆盖率测试失败: {e}")
        return False


def run_performance_tests(project_root):
    """运行性能测试"""
    print("\n⚡ 运行Web模块性能测试...")
    print("=" * 60)

    test_dir = project_root / "tests" / "web"

    # 运行特定的性能测试
    cmd = [
        sys.executable,
        "-m",
        "pytest",
        str(test_dir / "test_integration.py::TestWebIntegration::test_performance_baseline"),
        str(test_dir / "test_integration.py::TestWebIntegration::test_concurrent_requests"),
        "-v",
        "--tb=short",
    ]

    try:
        result = subprocess.run(cmd, cwd=project_root, capture_output=True, text=True)

        print("STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        return result.returncode == 0

    except Exception as e:
        print(f"❌ 运行性能测试失败: {e}")
        return False


def run_unittest_tests(project_root):
    """运行unittest测试"""
    print("\n🔬 运行Web模块unittest测试...")
    print("=" * 60)

    test_dir = project_root / "tests" / "web"

    # 切换到测试目录
    original_cwd = Path.cwd()
    os.chdir(test_dir)

    try:
        # 运行unittest discover
        cmd = [sys.executable, "-m", "unittest", "discover", "-s", ".", "-p", "test_*.py", "-v"]

        result = subprocess.run(cmd, capture_output=True, text=True)

        print("STDOUT:")
        print(result.stdout)

        if result.stderr:
            print("STDERR:")
            print(result.stderr)

        return result.returncode == 0

    except Exception as e:
        print(f"❌ 运行unittest测试失败: {e}")
        return False

    finally:
        os.chdir(original_cwd)


def check_test_dependencies():
    """检查测试依赖"""
    print("🔍 检查测试依赖...")

    required_packages = ["pytest", "pytest-cov", "fastapi", "uvicorn"]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")

    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("安装命令:")
        print(f"  pip install {' '.join(missing_packages)}")
        return False

    print("✅ 所有依赖包已安装")
    return True


def generate_test_summary(results):
    """生成测试摘要"""
    print("\n" + "=" * 60)
    print("📋 Web模块测试摘要")
    print("=" * 60)

    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    failed_tests = total_tests - passed_tests

    print(f"总测试套件: {total_tests}")
    print(f"通过套件: {passed_tests} ✅")
    print(f"失败套件: {failed_tests} ❌")

    if failed_tests == 0:
        print("\n🎉 所有测试套件通过!")
        success_rate = 100.0
    else:
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n⚠️  成功率: {success_rate:.1f}%")

    print("\n详细结果:")
    for test_name, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {test_name}")

    print("\n" + "=" * 60)

    return failed_tests == 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Web模块测试执行脚本")
    parser.add_argument("--coverage", action="store_true", help="运行覆盖率测试")
    parser.add_argument("--performance", action="store_true", help="运行性能测试")
    parser.add_argument("--unittest", action="store_true", help="运行unittest测试")
    parser.add_argument("--all", action="store_true", help="运行所有测试")
    parser.add_argument("--check-deps", action="store_true", help="检查测试依赖")

    args = parser.parse_args()

    # 设置环境
    project_root = setup_environment()

    print("🚀 Mini-Boot Web模块测试执行器")
    print("=" * 60)
    print(f"项目根目录: {project_root}")
    print(f"Python版本: {sys.version}")
    print()

    # 检查依赖
    if args.check_deps or args.all:
        if not check_test_dependencies():
            sys.exit(1)
        print()

    # 记录测试结果
    results = {}
    start_time = time.time()

    # 运行测试
    if args.all:
        # 运行所有测试
        results["基础测试"] = run_basic_tests(project_root)
        results["覆盖率测试"] = run_coverage_tests(project_root)
        results["性能测试"] = run_performance_tests(project_root)
        results["unittest测试"] = run_unittest_tests(project_root)
    else:
        # 根据参数运行特定测试
        if args.coverage:
            results["覆盖率测试"] = run_coverage_tests(project_root)
        elif args.performance:
            results["性能测试"] = run_performance_tests(project_root)
        elif args.unittest:
            results["unittest测试"] = run_unittest_tests(project_root)
        else:
            results["基础测试"] = run_basic_tests(project_root)

    # 计算总时间
    end_time = time.time()
    total_time = end_time - start_time

    print(f"\n⏱️  总执行时间: {total_time:.2f} 秒")

    # 生成摘要
    success = generate_test_summary(results)

    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
