#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Actuator 缓存模块

提供 Actuator 的缓存管理功能,包括:
- 高级端点缓存策略
- 缓存装饰器
- 缓存性能监控
- 多种缓存算法支持
"""

# 高级缓存功能
from .cache import AdvancedEndpointCache, CacheManager, CacheStrategy
# 缓存装饰器
from .decorators import (CacheableEndpoint, adaptive_cache, advanced_cache,
                         cache_evict, cache_put, conditional_cache, fifo_cache,
                         lfu_cache, lru_cache, ttl_cache)
# 缓存端点
from .endpoint import CacheEndpoint

__all__ = [
    # 高级缓存功能
    "AdvancedEndpointCache",
    "CacheManager",
    "CacheStrategy",
    # 缓存装饰器
    "CacheableEndpoint",
    "adaptive_cache",
    "advanced_cache",
    "cache_evict",
    "cache_put",
    "conditional_cache",
    "fifo_cache",
    "lfu_cache",
    "lru_cache",
    "ttl_cache",
    # 缓存端点
    "CacheEndpoint",
]
