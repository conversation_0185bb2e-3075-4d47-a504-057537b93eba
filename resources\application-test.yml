# Mini-Boot 测试环境配置

miniboot:
    # 测试环境应用配置
    application:
        name: mini-boot-app-test

    # 禁用 Web 模块以避免阻塞
    web:
        enabled: false

    # 禁用 Scheduler 模块以避免后台任务
    scheduler:
        enabled: false

    # 保持核心模块启用
    async:
        enabled: true

    events:
        enabled: true

    # 测试服务器配置
    server:
        port: 8081

    # 测试数据库配置
    datasource:
        url: "sqlite:///:memory:"
        driver: sqlite3

    # 测试日志配置
    logging:
        level: DEBUG # 测试环境使用DEBUG级别

        # 控制台输出（测试环境启用）
        console:
            enabled: true
            level: INFO # 测试环境控制台显示INFO级别
            colorize: false # 测试环境关闭彩色输出（避免CI/CD干扰）

        # 文件输出（测试环境可选）
        file:
            enabled: true
            path: logs/test.log
            level: DEBUG
            rotation: "50 MB" # 测试环境小的轮转大小
            retention: "1 day" # 测试环境短的保留时间
            compression: null # 测试环境不压缩
