#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: PerformanceMonitor 单元测试

测试 Actuator 性能监控器的核心功能，包括：
- 监控器初始化和配置
- 监控生命周期管理
- 性能数据收集和分析
- 状态查询和报告
- 异常处理和错误恢复
"""

import asyncio
import time
import unittest
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, Mock, patch

from miniboot.starters.actuator.performance.metrics import PerformanceAnalyzer
from miniboot.starters.actuator.performance.monitor import (
    PerformanceCollector, PerformanceMetric, PerformanceMonitor,
    PerformanceSnapshot)
from tests.utils.asyncio_patch import suppress_asyncio_warnings


class TestPerformanceMetric(unittest.TestCase):
    """PerformanceMetric 数据结构测试"""

    def test_metric_creation(self):
        """测试性能指标创建"""
        metric = PerformanceMetric(
            name="cpu_usage",
            value=75.5,
            timestamp=time.time(),
            unit="%",
            category="system",
            tags={"host": "server1"}
        )

        self.assertEqual(metric.name, "cpu_usage")
        self.assertEqual(metric.value, 75.5)
        self.assertEqual(metric.unit, "%")
        self.assertEqual(metric.category, "system")
        self.assertEqual(metric.tags["host"], "server1")

    def test_metric_defaults(self):
        """测试性能指标默认值"""
        metric = PerformanceMetric(
            name="test_metric",
            value=100.0,
            timestamp=time.time()
        )

        self.assertEqual(metric.unit, "")
        self.assertEqual(metric.category, "general")
        self.assertEqual(metric.tags, {})


class TestPerformanceSnapshot(unittest.TestCase):
    """PerformanceSnapshot 数据结构测试"""

    def test_snapshot_creation(self):
        """测试性能快照创建"""
        timestamp = time.time()
        snapshot = PerformanceSnapshot(
            timestamp=timestamp,
            integration_metrics={"integration_time": 0.5},
            endpoint_metrics={"endpoint1": {"avg_response_time": 0.1}},
            system_metrics={"cpu_percent": 50.0, "memory_percent": 60.0},
            performance_score=85.0
        )

        self.assertEqual(snapshot.timestamp, timestamp)
        self.assertEqual(snapshot.integration_metrics["integration_time"], 0.5)
        self.assertEqual(snapshot.endpoint_metrics["endpoint1"]["avg_response_time"], 0.1)
        self.assertEqual(snapshot.system_metrics["cpu_percent"], 50.0)
        self.assertEqual(snapshot.performance_score, 85.0)

    def test_snapshot_default_score(self):
        """测试性能快照默认分数"""
        snapshot = PerformanceSnapshot(
            timestamp=time.time(),
            integration_metrics={},
            endpoint_metrics={},
            system_metrics={}
        )

        self.assertEqual(snapshot.performance_score, 0.0)


class TestPerformanceCollector(unittest.TestCase):
    """PerformanceCollector 测试"""

    def setUp(self):
        """测试前准备"""
        self.collector = PerformanceCollector()

    def test_collector_initialization(self):
        """测试收集器初始化"""
        self.assertTrue(self.collector._enabled)
        self.assertEqual(len(self.collector._metrics), 0)
        self.assertEqual(len(self.collector._snapshots), 0)

    def test_record_metric(self):
        """测试记录性能指标"""
        metric = PerformanceMetric(
            name="test_metric",
            value=100.0,
            timestamp=time.time()
        )

        self.collector.record_metric(metric)

        metrics = self.collector.get_metrics("test_metric")
        self.assertEqual(len(metrics), 1)
        self.assertEqual(metrics[0].value, 100.0)

    def test_record_snapshot(self):
        """测试记录性能快照"""
        snapshot = PerformanceSnapshot(
            timestamp=time.time(),
            integration_metrics={},
            endpoint_metrics={},
            system_metrics={},
            performance_score=75.0
        )

        self.collector.record_snapshot(snapshot)

        latest = self.collector.get_latest_snapshot()
        self.assertIsNotNone(latest)
        self.assertEqual(latest.performance_score, 75.0)

    def test_get_snapshots_with_limit(self):
        """测试获取快照列表（带限制）"""
        # 添加多个快照
        for i in range(10):
            snapshot = PerformanceSnapshot(
                timestamp=time.time() + i,
                integration_metrics={},
                endpoint_metrics={},
                system_metrics={},
                performance_score=float(i * 10)
            )
            self.collector.record_snapshot(snapshot)

        # 测试限制功能
        snapshots = self.collector.get_snapshots(limit=5)
        self.assertEqual(len(snapshots), 5)

        # 应该返回最新的5个
        self.assertEqual(snapshots[-1].performance_score, 90.0)

    def test_collector_disable_enable(self):
        """测试收集器启用/禁用"""
        # 禁用收集器
        self.collector.disable()

        metric = PerformanceMetric("test", 100.0, time.time())
        self.collector.record_metric(metric)

        # 禁用时不应记录
        self.assertEqual(len(self.collector.get_metrics("test")), 0)

        # 重新启用
        self.collector.enable()
        self.collector.record_metric(metric)

        # 启用后应该记录
        self.assertEqual(len(self.collector.get_metrics("test")), 1)

    def test_clear_data(self):
        """测试清除数据"""
        # 添加一些数据
        metric = PerformanceMetric("test", 100.0, time.time())
        self.collector.record_metric(metric)

        snapshot = PerformanceSnapshot(
            timestamp=time.time(),
            integration_metrics={},
            endpoint_metrics={},
            system_metrics={}
        )
        self.collector.record_snapshot(snapshot)

        # 清除数据
        self.collector.clear()

        # 验证数据已清除
        self.assertEqual(len(self.collector.get_metrics("test")), 0)
        self.assertIsNone(self.collector.get_latest_snapshot())


class TestPerformanceMonitor(unittest.IsolatedAsyncioTestCase):
    """PerformanceMonitor 主要功能测试"""

    def setUp(self):
        """测试前准备"""
        self.monitor = PerformanceMonitor(
            collection_interval=0.1,  # 快速测试
            analysis_interval=0.2
        )

    def test_monitor_initialization(self):
        """测试监控器初始化"""
        self.assertEqual(self.monitor.collection_interval, 0.1)
        self.assertEqual(self.monitor.analysis_interval, 0.2)
        self.assertIsInstance(self.monitor._data_collector, PerformanceCollector)
        self.assertIsInstance(self.monitor._metrics_analyzer, PerformanceAnalyzer)
        self.assertFalse(self.monitor._is_running)
        self.assertIsNone(self.monitor._data_collection_task)
        self.assertIsNone(self.monitor._metrics_analysis_task)

    def test_configure_targets(self):
        """测试配置监控目标"""
        mock_context = Mock()
        mock_integration = Mock()

        self.monitor.configure_targets(
            actuator_context=mock_context,
            integration=mock_integration
        )

        self.assertEqual(self.monitor._actuator_context, mock_context)
        self.assertEqual(self.monitor._integration, mock_integration)

    def test_is_running_status(self):
        """测试运行状态检查"""
        self.assertFalse(self.monitor.is_running())

        self.monitor._is_running = True
        self.assertTrue(self.monitor.is_running())

    @suppress_asyncio_warnings
    async def test_start_monitoring(self):
        """测试启动监控"""
        with patch.object(self.monitor, '_data_collection_loop') as mock_collection, \
             patch.object(self.monitor, '_metrics_analysis_loop') as mock_analysis:

            mock_collection.return_value = AsyncMock()
            mock_analysis.return_value = AsyncMock()

            await self.monitor.start()

            self.assertTrue(self.monitor.is_running())
            self.assertIsNotNone(self.monitor._data_collection_task)
            self.assertIsNotNone(self.monitor._metrics_analysis_task)

    @suppress_asyncio_warnings
    async def test_start_monitoring_already_running(self):
        """测试重复启动监控"""
        self.monitor._is_running = True

        with patch('loguru.logger.warning') as mock_logger:
            await self.monitor.start()
            mock_logger.assert_called_with("Performance monitoring already started")

    @suppress_asyncio_warnings
    async def test_stop_monitoring(self):
        """测试停止监控"""
        # 先启动监控
        with patch.object(self.monitor, '_data_collection_loop') as mock_collection, \
             patch.object(self.monitor, '_metrics_analysis_loop') as mock_analysis:

            mock_collection.return_value = AsyncMock()
            mock_analysis.return_value = AsyncMock()

            await self.monitor.start()

            # 然后停止监控
            await self.monitor.stop()

            self.assertFalse(self.monitor.is_running())

    @suppress_asyncio_warnings
    async def test_stop_monitoring_not_running(self):
        """测试停止未运行的监控"""
        # 直接停止未运行的监控应该不会出错
        await self.monitor.stop()
        self.assertFalse(self.monitor.is_running())


    def test_get_status_no_data(self):
        """测试获取状态（无数据）"""
        status = self.monitor.get_status()

        self.assertEqual(status["status"], "no_data")
        self.assertFalse(status["monitoring"])

    def test_get_status_with_data(self):
        """测试获取状态（有数据）"""
        # 添加测试快照
        snapshot = PerformanceSnapshot(
            timestamp=time.time(),
            integration_metrics={},
            endpoint_metrics={"endpoint1": {}, "endpoint2": {}},
            system_metrics={},
            performance_score=85.0
        )
        self.monitor._data_collector.record_snapshot(snapshot)

        # Mock 分析器
        with patch.object(self.monitor._metrics_analyzer, 'analyze_performance') as mock_analyze:
            mock_analyze.return_value = {
                "status": "healthy",
                "issues": [],
                "recommendations": []
            }

            status = self.monitor.get_status()

            self.assertIn("latest_snapshot", status)
            self.assertEqual(status["latest_snapshot"]["performance_score"], 85.0)
            self.assertEqual(status["latest_snapshot"]["endpoint_count"], 2)
            self.assertIn("analysis", status)
            self.assertEqual(status["collection_interval"], 0.1)
            self.assertEqual(status["analysis_interval"], 0.2)

    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    def test_collect_system_metrics(self, mock_memory, mock_cpu):
        """测试收集系统指标"""
        # Mock psutil 返回值
        mock_cpu.return_value = 75.5
        mock_memory.return_value = Mock(percent=60.0, used=1024*1024*512)  # 512MB

        metrics = self.monitor._collect_system_metrics()

        self.assertEqual(metrics["cpu_percent"], 75.5)
        self.assertEqual(metrics["memory_percent"], 60.0)
        self.assertEqual(metrics["memory_used"], 1024*1024*512)

    def test_collect_system_metrics_no_psutil(self):
        """测试收集系统指标（无psutil）"""
        with patch('builtins.__import__', side_effect=ImportError):
            metrics = self.monitor._collect_system_metrics()

            # 应该返回模拟数据
            self.assertIn("cpu_percent", metrics)
            self.assertIn("memory_percent", metrics)
            self.assertIn("memory_used", metrics)

    def test_calculate_overall_score(self):
        """测试计算整体性能分数"""
        integration_metrics = {"integration_time": 0.5}
        endpoint_metrics = {
            "endpoint1": {"avg_response_time": 0.05},
            "endpoint2": {"avg_response_time": 0.08}
        }
        system_metrics = {"cpu_percent": 50.0, "memory_percent": 40.0}

        score = self.monitor._calculate_overall_score(
            integration_metrics, endpoint_metrics, system_metrics
        )

        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 100.0)

    def test_calculate_overall_score_high_resource_usage(self):
        """测试计算整体性能分数（高资源使用）"""
        integration_metrics = {"integration_time": 2.0}  # 高集成时间
        endpoint_metrics = {
            "endpoint1": {"avg_response_time": 0.5}  # 高响应时间
        }
        system_metrics = {"cpu_percent": 90.0, "memory_percent": 95.0}  # 高资源使用

        score = self.monitor._calculate_overall_score(
            integration_metrics, endpoint_metrics, system_metrics
        )

        # 高资源使用应该导致较低分数
        self.assertLess(score, 50.0)

    def test_collect_endpoint_metrics(self):
        """测试收集端点指标"""
        # Mock actuator context
        mock_context = Mock()
        mock_endpoint_registry = Mock()
        mock_context.endpoint_registry = mock_endpoint_registry

        # Mock 端点
        mock_endpoint1 = Mock()
        mock_endpoint1.enabled = True
        mock_endpoint1.sensitive = False
        mock_endpoint1.operations.return_value = ["GET", "POST"]

        mock_endpoint2 = Mock()
        mock_endpoint2.enabled = True
        mock_endpoint2.sensitive = True
        mock_endpoint2.operations.return_value = ["GET"]

        # Mock get_enabled_endpoints 返回字典
        mock_endpoint_registry.get_enabled_endpoints.return_value = {
            "health": mock_endpoint1,
            "info": mock_endpoint2
        }

        self.monitor._actuator_context = mock_context

        metrics = self.monitor._collect_endpoint_metrics()

        self.assertIn("health", metrics)
        self.assertIn("info", metrics)
        self.assertEqual(metrics["health"]["enabled"], True)
        self.assertEqual(metrics["health"]["sensitive"], False)
        self.assertEqual(metrics["health"]["operations_count"], 2)
        self.assertEqual(metrics["info"]["enabled"], True)
        self.assertEqual(metrics["info"]["sensitive"], True)
        self.assertEqual(metrics["info"]["operations_count"], 1)

    def test_collect_endpoint_metrics_no_context(self):
        """测试收集端点指标（无上下文）"""
        self.monitor._actuator_context = None

        metrics = self.monitor._collect_endpoint_metrics()

        self.assertEqual(metrics, {})

    @suppress_asyncio_warnings
    async def test_collect_all_metrics(self):
        """测试收集所有指标"""
        # Mock 集成组件
        mock_integration = Mock()
        mock_integration.get_performance_metrics.return_value = {
            "integration_time": 0.3,
            "success_rate": 0.98
        }
        self.monitor._integration = mock_integration

        # Mock 系统指标收集
        with patch.object(self.monitor, '_collect_system_metrics') as mock_system, \
             patch.object(self.monitor, '_collect_endpoint_metrics') as mock_endpoint, \
             patch.object(self.monitor, '_calculate_overall_score') as mock_score:

            mock_system.return_value = {"cpu_percent": 45.0}
            mock_endpoint.return_value = {"health": {"avg_response_time": 0.02}}
            mock_score.return_value = 88.5

            await self.monitor._collect_all_metrics()

            # 验证数据收集器被调用
            latest_snapshot = self.monitor._data_collector.get_latest_snapshot()
            self.assertIsNotNone(latest_snapshot)
            self.assertEqual(latest_snapshot.performance_score, 88.5)
            self.assertEqual(latest_snapshot.integration_metrics["integration_time"], 0.3)

    @suppress_asyncio_warnings
    async def test_analyze_metrics(self):
        """测试分析指标"""
        # 添加测试快照
        snapshot = PerformanceSnapshot(
            timestamp=time.time(),
            integration_metrics={},
            endpoint_metrics={},
            system_metrics={},
            performance_score=75.0
        )
        self.monitor._data_collector.record_snapshot(snapshot)

        # Mock 分析器
        with patch.object(self.monitor._metrics_analyzer, 'analyze_performance') as mock_analyze:
            mock_analyze.return_value = {
                "status": "warning",
                "issues": [{"message": "High CPU usage detected"}],
                "recommendations": ["Consider scaling up resources"]
            }

            with patch('loguru.logger.warning') as mock_warning, \
                 patch('loguru.logger.info') as mock_info:

                await self.monitor._analyze_metrics()

                # 验证日志记录
                mock_warning.assert_called()
                mock_info.assert_called()

    @suppress_asyncio_warnings
    async def test_analyze_metrics_no_data(self):
        """测试分析指标（无数据）"""
        # 确保没有快照数据
        self.monitor._data_collector.clear()

        # 分析应该直接返回，不会出错
        await self.monitor._analyze_metrics()


class TestPerformanceMonitorErrorHandling(unittest.IsolatedAsyncioTestCase):
    """PerformanceMonitor 异常处理测试"""

    def setUp(self):
        """测试前准备"""
        self.monitor = PerformanceMonitor(
            collection_interval=0.05,
            analysis_interval=0.1
        )

    @suppress_asyncio_warnings
    async def test_data_collection_loop_exception_handling(self):
        """测试数据收集循环异常处理"""
        # Mock 收集方法抛出异常
        with patch.object(self.monitor, '_collect_all_metrics', side_effect=Exception("Collection error")):
            # 启动监控
            self.monitor._is_running = True

            # 创建收集任务
            task = asyncio.create_task(self.monitor._data_collection_loop())

            # 等待一小段时间让异常发生
            await asyncio.sleep(0.02)

            # 停止监控
            self.monitor._is_running = False

            # 等待任务完成
            await task

    @suppress_asyncio_warnings
    async def test_metrics_analysis_loop_exception_handling(self):
        """测试指标分析循环异常处理"""
        # Mock 分析方法抛出异常
        with patch.object(self.monitor, '_analyze_metrics', side_effect=Exception("Analysis error")):
            # 启动监控
            self.monitor._is_running = True

            # 创建分析任务
            task = asyncio.create_task(self.monitor._metrics_analysis_loop())

            # 等待一小段时间让异常发生
            await asyncio.sleep(0.02)

            # 停止监控
            self.monitor._is_running = False

            # 等待任务完成
            await task

    def test_collect_endpoint_metrics_exception_handling(self):
        """测试收集端点指标异常处理"""
        # Mock context 抛出异常
        mock_context = Mock()
        mock_endpoint_registry = Mock()
        mock_context.endpoint_registry = mock_endpoint_registry
        mock_endpoint_registry.get_enabled_endpoints.side_effect = Exception("Registry error")

        self.monitor._actuator_context = mock_context

        # 应该返回空字典而不是抛出异常
        metrics = self.monitor._collect_endpoint_metrics()
        self.assertEqual(metrics, {})

    def test_collect_endpoint_metrics_endpoint_exception(self):
        """测试端点指标收集异常处理"""
        # Mock context 和端点
        mock_context = Mock()
        mock_endpoint_registry = Mock()
        mock_context.endpoint_registry = mock_endpoint_registry

        # Mock 端点抛出异常
        mock_endpoint = Mock()
        mock_endpoint.enabled = True
        mock_endpoint.sensitive = False
        mock_endpoint.operations.side_effect = Exception("Endpoint error")

        mock_endpoint_registry.get_enabled_endpoints.return_value = {
            "problematic_endpoint": mock_endpoint
        }

        self.monitor._actuator_context = mock_context

        # 应该跳过有问题的端点或返回空字典
        metrics = self.monitor._collect_endpoint_metrics()
        # 由于异常处理，应该返回空字典
        self.assertEqual(metrics, {})

    @suppress_asyncio_warnings
    async def test_task_cancellation_handling(self):
        """测试任务取消处理"""
        # 启动监控
        await self.monitor.start()

        # 获取任务引用
        collection_task = self.monitor._data_collection_task
        analysis_task = self.monitor._metrics_analysis_task

        # 手动取消任务
        collection_task.cancel()
        analysis_task.cancel()

        # 停止监控应该正常处理已取消的任务
        await self.monitor.stop()

        self.assertFalse(self.monitor.is_running())


class TestPerformanceMonitorEdgeCases(unittest.TestCase):
    """PerformanceMonitor 边界情况测试"""

    def setUp(self):
        """测试前准备"""
        self.monitor = PerformanceMonitor()

    def test_zero_intervals(self):
        """测试零间隔配置"""
        monitor = PerformanceMonitor(
            collection_interval=0.0,
            analysis_interval=0.0
        )

        self.assertEqual(monitor.collection_interval, 0.0)
        self.assertEqual(monitor.analysis_interval, 0.0)

    def test_negative_intervals(self):
        """测试负间隔配置"""
        monitor = PerformanceMonitor(
            collection_interval=-1.0,
            analysis_interval=-2.0
        )

        # 应该接受负值（虽然在实际使用中可能不合理）
        self.assertEqual(monitor.collection_interval, -1.0)
        self.assertEqual(monitor.analysis_interval, -2.0)

    def test_very_large_intervals(self):
        """测试非常大的间隔配置"""
        monitor = PerformanceMonitor(
            collection_interval=86400.0,  # 1天
            analysis_interval=604800.0   # 1周
        )

        self.assertEqual(monitor.collection_interval, 86400.0)
        self.assertEqual(monitor.analysis_interval, 604800.0)

    def test_calculate_score_edge_cases(self):
        """测试分数计算边界情况"""
        # 空指标
        score = self.monitor._calculate_overall_score({}, {}, {})
        self.assertEqual(score, 100.0)

        # 极端值
        extreme_metrics = {
            "integration_time": 1000.0,  # 极高集成时间
        }
        extreme_endpoints = {
            "slow_endpoint": {"avg_response_time": 10.0}  # 极慢响应
        }
        extreme_system = {
            "cpu_percent": 100.0,
            "memory_percent": 100.0
        }

        score = self.monitor._calculate_overall_score(
            extreme_metrics, extreme_endpoints, extreme_system
        )

        # 分数应该被限制在0-100范围内
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 100.0)

    def test_configure_targets_none_values(self):
        """测试配置目标为None值"""
        self.monitor.configure_targets(
            actuator_context=None,
            integration=None
        )

        self.assertIsNone(self.monitor._actuator_context)
        self.assertIsNone(self.monitor._integration)

    def test_configure_targets_partial_values(self):
        """测试部分配置目标"""
        mock_context = Mock()

        # 只配置context
        self.monitor.configure_targets(actuator_context=mock_context)

        self.assertEqual(self.monitor._actuator_context, mock_context)
        self.assertIsNone(self.monitor._integration)

        # 只配置integration
        mock_integration = Mock()
        self.monitor.configure_targets(integration=mock_integration)

        self.assertEqual(self.monitor._integration, mock_integration)
        # actuator_context 应该被重置为None（因为没有传入）
        self.assertIsNone(self.monitor._actuator_context)


class TestPerformanceMonitorIntegration(unittest.IsolatedAsyncioTestCase):
    """PerformanceMonitor 集成测试"""

    def setUp(self):
        """测试前准备"""
        self.monitor = PerformanceMonitor(
            collection_interval=0.05,  # 非常快的间隔用于测试
            analysis_interval=0.1
        )

    @suppress_asyncio_warnings
    async def test_full_monitoring_cycle(self):
        """测试完整监控周期"""
        # Mock 依赖组件
        mock_integration = Mock()
        mock_integration.get_performance_metrics.return_value = {
            "integration_time": 0.2,
            "success_rate": 0.99
        }

        mock_context = Mock()
        mock_endpoint_registry = Mock()
        mock_context.endpoint_registry = mock_endpoint_registry
        mock_endpoint_registry.get_enabled_endpoints.return_value = {}

        self.monitor.configure_targets(
            actuator_context=mock_context,
            integration=mock_integration
        )

        # 启动监控
        await self.monitor.start()

        # 等待一些数据收集
        await asyncio.sleep(0.15)

        # 检查状态
        status = self.monitor.get_status()
        self.assertTrue(self.monitor.is_running())

        # 停止监控
        await self.monitor.stop()
        self.assertFalse(self.monitor.is_running())

    @suppress_asyncio_warnings
    async def test_monitoring_with_real_data_flow(self):
        """测试真实数据流的监控"""
        # 配置真实的数据收集
        with patch.object(self.monitor, '_collect_system_metrics') as mock_system:
            mock_system.return_value = {
                "cpu_percent": 45.0,
                "memory_percent": 60.0,
                "memory_used": 1024*1024*256
            }

            # 启动监控
            await self.monitor.start()

            # 等待数据收集
            await asyncio.sleep(0.12)

            # 检查是否有数据被收集
            latest_snapshot = self.monitor._data_collector.get_latest_snapshot()
            if latest_snapshot:  # 可能由于时间问题没有收集到数据
                self.assertIsNotNone(latest_snapshot.system_metrics)
                self.assertIn("cpu_percent", latest_snapshot.system_metrics)

            # 停止监控
            await self.monitor.stop()

    @suppress_asyncio_warnings
    async def test_concurrent_start_stop_operations(self):
        """测试并发启动停止操作"""
        # 同时启动多次
        tasks = []
        for _ in range(3):
            tasks.append(asyncio.create_task(self.monitor.start()))

        await asyncio.gather(*tasks)

        # 应该只启动一次
        self.assertTrue(self.monitor.is_running())

        # 同时停止多次
        tasks = []
        for _ in range(3):
            tasks.append(asyncio.create_task(self.monitor.stop()))

        await asyncio.gather(*tasks)

        # 应该正常停止
        self.assertFalse(self.monitor.is_running())


if __name__ == "__main__":
    unittest.main()
