#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 并发测试增强验证 - 验证并发测试增强的效果

验证新增的并发测试功能和线程安全修复。
"""

import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import Mock

# 组合式代理
from miniboot.bean.composite_proxy import CompositeProxy

# 缓存组件
from miniboot.bean.proxy_components import MethodCacheComponent

# 组合式事件
from miniboot.events.composite_event import CompositeEvent
from miniboot.events.event_types import ApplicationStartedEvent, BeanCreatedEvent


class ConcurrencyEnhancementsTestCase(unittest.TestCase):
    """并发测试增强验证用例"""

    def test_thread_safe_event_id_generation(self):
        """测试线程安全的事件ID生成"""
        event_ids = []
        errors = []

        def create_events_worker(worker_id: int):
            """并发创建事件的工作函数"""
            try:
                worker_ids = []
                for i in range(50):
                    event = CompositeEvent(event_type=f"TestEvent_{worker_id}_{i}", source=Mock())
                    worker_ids.append(event.event_id)
                    event.cleanup()

                event_ids.extend(worker_ids)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(create_events_worker, i) for i in range(10)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"事件ID生成出现错误: {errors}")
        self.assertEqual(len(event_ids), 500, "应该生成500个事件ID")

        # 验证ID唯一性
        unique_ids = set(event_ids)
        self.assertEqual(len(unique_ids), len(event_ids), "所有事件ID应该唯一")

        # 验证性能
        self.assertLess(end_time - start_time, 3.0, "事件ID生成应该在3秒内完成")

        print(f"✅ 线程安全事件ID生成测试通过 - 耗时: {end_time - start_time:.3f}秒")
        print(f"   生成了{len(unique_ids)}个唯一ID")

    def test_proxy_concurrent_access(self):
        """测试代理并发访问"""

        class TestBean:
            def __init__(self):
                self.call_count = 0
                self._lock = threading.Lock()

            def thread_safe_method(self):
                with self._lock:
                    self.call_count += 1
                    return f"result_{self.call_count}"

        test_bean = TestBean()
        proxy = CompositeProxy(sync_bean=test_bean, bean_name="concurrentTestProxy")

        results = []
        errors = []

        def proxy_access_worker(worker_id: int):
            """代理访问工作函数"""
            try:
                worker_results = []
                for i in range(20):
                    result = proxy.thread_safe_method()
                    worker_results.append((worker_id, i, result))

                results.extend(worker_results)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(proxy_access_worker, i) for i in range(8)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"代理并发访问出现错误: {errors}")
        self.assertEqual(len(results), 160, "应该有160个访问结果")
        self.assertEqual(test_bean.call_count, 160, "Bean方法应该被调用160次")

        # 验证代理统计
        stats = proxy.get_stats()
        self.assertGreater(stats["access_count"], 0, "代理访问计数应该大于0")

        # 验证性能
        self.assertLess(end_time - start_time, 2.0, "代理并发访问应该在2秒内完成")

        # 清理
        proxy.shutdown()

        print(f"✅ 代理并发访问测试通过 - 耗时: {end_time - start_time:.3f}秒")

    def test_cache_component_thread_safety(self):
        """测试缓存组件线程安全"""
        cache = MethodCacheComponent(cache_size=50)
        cache.initialize()

        results = []
        errors = []

        def cache_worker(worker_id: int):
            """缓存操作工作函数"""
            try:
                for i in range(30):
                    key = f"cache_key_{worker_id}_{i % 10}"  # 重复使用一些key
                    value = f"cache_value_{worker_id}_{i}"

                    # 写入缓存
                    cache.put(key, value)

                    # 读取缓存
                    cached_value = cache.get(key)

                    results.append((worker_id, i, key, value, cached_value))
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=6) as executor:
            futures = [executor.submit(cache_worker, i) for i in range(6)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"缓存并发操作出现错误: {errors}")
        self.assertEqual(len(results), 180, "应该有180个缓存操作结果")

        # 验证缓存统计
        stats = cache.get_stats()
        self.assertGreaterEqual(stats["hits"], 0, "缓存命中数应该>=0")
        self.assertGreaterEqual(stats["misses"], 0, "缓存未命中数应该>=0")
        self.assertLessEqual(stats["size"], 50, "缓存大小不应超过限制")

        # 验证总访问次数
        total_accesses = stats["hits"] + stats["misses"]
        self.assertGreater(total_accesses, 0, "应该有缓存访问")

        # 验证性能
        self.assertLess(end_time - start_time, 1.5, "缓存并发操作应该在1.5秒内完成")

        # 清理
        cache.shutdown()

        print(f"✅ 缓存组件线程安全测试通过 - 耗时: {end_time - start_time:.3f}秒")
        print(f"   缓存统计: {stats}")

    def test_mixed_event_operations(self):
        """测试混合事件操作"""
        events = []
        errors = []

        def mixed_event_worker(worker_id: int):
            """混合事件操作工作函数"""
            try:
                worker_events = []
                for i in range(15):
                    # 创建不同类型的事件
                    if i % 3 == 0:
                        event = ApplicationStartedEvent(application=Mock(), startup_time=1.5)
                    elif i % 3 == 1:
                        event = BeanCreatedEvent(bean_name=f"bean_{worker_id}_{i}", bean_class="TestBean", source=Mock())
                    else:
                        event = CompositeEvent(event_type=f"CustomEvent_{worker_id}_{i}", source=Mock(), data={"worker": worker_id, "iteration": i})

                    # 操作事件
                    event.set_data("processed_by", worker_id)
                    event.mark_processed()

                    worker_events.append(event)

                events.extend(worker_events)
            except Exception as e:
                errors.append((worker_id, e))

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(mixed_event_worker, i) for i in range(5)]

            for future in as_completed(futures):
                future.result()

        end_time = time.time()

        # 验证结果
        self.assertEqual(len(errors), 0, f"混合事件操作出现错误: {errors}")
        self.assertEqual(len(events), 75, "应该创建75个事件")

        # 验证事件状态
        for event in events:
            self.assertTrue(event.is_processed(), "所有事件应该被标记为已处理")
            self.assertIsNotNone(event.get_data("processed_by"), "事件应该有处理者信息")

        # 验证事件ID唯一性
        event_ids = [event.event_id for event in events]
        unique_ids = set(event_ids)
        self.assertEqual(len(unique_ids), len(event_ids), "所有事件ID应该唯一")

        # 验证性能
        self.assertLess(end_time - start_time, 2.0, "混合事件操作应该在2秒内完成")

        # 清理事件
        for event in events:
            event.cleanup()

        print(f"✅ 混合事件操作测试通过 - 耗时: {end_time - start_time:.3f}秒")

    def test_stress_test_summary(self):
        """并发测试增强总结"""
        print("\n=== 并发测试增强总结 ===")

        # 运行所有测试方法
        test_methods = [
            self.test_thread_safe_event_id_generation,
            self.test_proxy_concurrent_access,
            self.test_cache_component_thread_safety,
            self.test_mixed_event_operations,
        ]

        passed_tests = 0
        total_tests = len(test_methods)

        for test_method in test_methods:
            try:
                test_method()
                passed_tests += 1
            except Exception as e:
                print(f"❌ {test_method.__name__} 失败: {e}")

        success_rate = (passed_tests / total_tests) * 100

        print("\n📊 并发测试增强结果:")
        print(f"   通过测试: {passed_tests}/{total_tests}")
        print(f"   成功率: {success_rate:.1f}%")

        if success_rate >= 100:
            print("🎉 所有并发测试增强验证通过！")
        elif success_rate >= 80:
            print("✅ 大部分并发测试增强验证通过")
        else:
            print("⚠️ 并发测试增强需要进一步改进")

        # 验证成功率
        self.assertGreaterEqual(success_rate, 80, "并发测试成功率应该至少80%")


if __name__ == "__main__":
    unittest.main(verbosity=2)
