# Mini-Boot 发布脚本

## 概述

这个目录包含了 Mini-Boot 框架的自动化发布脚本，支持版本管理、质量检查、构建打包和发布到私有 PyPI 仓库。

## 脚本说明

### 1. publish.py - 主发布脚本
自动化发布流程，包括质量检查、构建和发布。

### 2. version.py - 版本管理工具
管理项目版本号，支持语义化版本控制。

### 3. .env.example - 配置示例
包含发布所需的环境变量配置示例。

## 使用方法

### 环境准备

1. 复制配置文件：
```bash
cp scripts/.env.example scripts/.env
```

2. 编辑 `.env` 文件，填入实际的 Nexus 仓库信息：
```bash
NEXUS_URL=http://your-nexus-server:8081/repository/pypi-private/
NEXUS_USERNAME=your_username
NEXUS_PASSWORD=your_password
```

3. 加载环境变量：
```bash
# Windows PowerShell
Get-Content scripts/.env | ForEach-Object { $env:($_.Split('=')[0]) = $_.Split('=')[1] }

# Linux/Mac
source scripts/.env
```

### 版本管理

```bash
# 查看当前版本
uv run scripts/version.py --show

# 递增补丁版本 (1.0.0 -> 1.0.1)
uv run scripts/version.py --bump patch

# 递增次要版本 (1.0.0 -> 1.1.0)
uv run scripts/version.py --bump minor

# 递增主要版本 (1.0.0 -> 2.0.0)
uv run scripts/version.py --bump major

# 设置指定版本
uv run scripts/version.py --set 1.2.3
```

### 发布流程

```bash
# 标准发布流程
uv run scripts/publish.py

# 试运行（不执行实际操作）
uv run scripts/publish.py --dry-run

# 跳过测试（不推荐）
uv run scripts/publish.py --skip-tests
```

## 发布流程说明

1. **环境检查**：验证 uv 工具和项目结构
2. **代码质量检查**：运行测试套件和代码质量检查
3. **清理构建目录**：删除旧的构建文件
4. **构建包**：使用 `uv build` 构建分发包
5. **发布到私有仓库**：使用 `uv publish` 发布到 Nexus

## 注意事项

- 发布前确保所有测试通过
- 确保版本号符合语义化版本规范
- 发布到生产环境前建议先在测试环境验证
- 保护好 Nexus 仓库的访问凭据

## 故障排除

### 常见问题

1. **认证失败**：检查 NEXUS_USERNAME 和 NEXUS_PASSWORD 环境变量
2. **网络连接失败**：检查 NEXUS_URL 是否正确，网络是否可达
3. **版本冲突**：确保新版本号大于当前已发布的版本
4. **测试失败**：修复测试问题后再发布，或使用 --skip-tests（不推荐）

### 手动发布

如果自动发布失败，可以手动执行：

```bash
# 1. 构建包
uv build

# 2. 手动发布
uv publish --repository-url $NEXUS_URL --username $NEXUS_USERNAME --password $NEXUS_PASSWORD
```
