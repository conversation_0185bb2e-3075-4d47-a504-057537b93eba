#!/usr/bin/env python
"""
* @author: cz
* @description: 类型描述符测试
"""

import unittest
from dataclasses import dataclass

from miniboot.env.convert import TypeDescriptor


@dataclass
class TestClass:
    """测试用数据类"""

    name: str = "default"
    age: int = 0
    tags: list[str] = None
    metadata: dict[str, int] = None


class TypeDescriptorTestCase(unittest.TestCase):
    """类型描述符测试"""

    def test_create_from_basic_type(self):
        """测试从基本类型创建"""
        descriptor = TypeDescriptor.of(str)

        self.assertEqual(str, descriptor.type)
        self.assertIsNone(descriptor.origin)
        self.assertEqual((), descriptor.args)
        self.assertFalse(descriptor.is_generic())

    def test_create_from_generic_type(self):
        """测试从泛型类型创建"""
        descriptor = TypeDescriptor.of(list[str])

        self.assertEqual(list[str], descriptor.type)
        self.assertEqual(list, descriptor.origin)
        self.assertEqual((str,), descriptor.args)
        self.assertTrue(descriptor.is_generic())

    def test_create_from_value(self):
        """测试从值创建"""
        descriptor = TypeDescriptor.of_value("hello")

        self.assertEqual(str, descriptor.type)
        self.assertFalse(descriptor.is_generic())

    def test_create_from_field_class(self):
        """测试从类字段创建"""
        descriptor = TypeDescriptor.of_field(TestClass, "name")

        self.assertEqual(str, descriptor.type)
        self.assertFalse(descriptor.is_generic())

    def test_create_from_field_generic(self):
        """测试从泛型字段创建"""
        descriptor = TypeDescriptor.of_field(TestClass, "tags")

        self.assertEqual(list[str], descriptor.type)
        self.assertEqual(list, descriptor.origin)
        self.assertEqual((str,), descriptor.args)
        self.assertTrue(descriptor.is_generic())

    def test_create_from_field_instance(self):
        """测试从实例字段创建"""
        instance = TestClass(name="test", age=25)
        descriptor = TypeDescriptor.of_field(instance, "name")

        self.assertEqual(str, descriptor.type)

    def test_create_from_nonexistent_field(self):
        """测试从不存在的字段创建"""
        with self.assertRaises(AttributeError):
            TypeDescriptor.of_field(TestClass, "nonexistent")

    def test_is_collection(self):
        """测试集合类型检查"""
        # 泛型集合
        self.assertTrue(TypeDescriptor.of(list[str]).is_collection())
        self.assertTrue(TypeDescriptor.of(set[int]).is_collection())
        self.assertTrue(TypeDescriptor.of(tuple[str, ...]).is_collection())

        # 原始集合类型
        self.assertTrue(TypeDescriptor.of(list).is_collection())
        self.assertTrue(TypeDescriptor.of(set).is_collection())
        self.assertTrue(TypeDescriptor.of(tuple).is_collection())

        # 非集合类型
        self.assertFalse(TypeDescriptor.of(str).is_collection())
        self.assertFalse(TypeDescriptor.of(dict[str, int]).is_collection())

    def test_is_mapping(self):
        """测试映射类型检查"""
        # 泛型映射
        self.assertTrue(TypeDescriptor.of(dict[str, int]).is_mapping())

        # 原始映射类型
        self.assertTrue(TypeDescriptor.of(dict).is_mapping())

        # 非映射类型
        self.assertFalse(TypeDescriptor.of(str).is_mapping())
        self.assertFalse(TypeDescriptor.of(list[str]).is_mapping())

    def test_get_element_type(self):
        """测试获取集合元素类型"""
        descriptor = TypeDescriptor.of(list[str])
        element_type = descriptor.get_element_type()

        self.assertIsNotNone(element_type)
        self.assertEqual(str, element_type.type)

    def test_get_element_type_non_collection(self):
        """测试非集合类型获取元素类型"""
        descriptor = TypeDescriptor.of(str)
        element_type = descriptor.get_element_type()

        self.assertIsNone(element_type)

    def test_get_key_value_types(self):
        """测试获取映射键值类型"""
        descriptor = TypeDescriptor.of(dict[str, int])

        key_type = descriptor.get_key_type()
        value_type = descriptor.get_value_type()

        self.assertIsNotNone(key_type)
        self.assertEqual(str, key_type.type)

        self.assertIsNotNone(value_type)
        self.assertEqual(int, value_type.type)

    def test_get_key_value_types_non_mapping(self):
        """测试非映射类型获取键值类型"""
        descriptor = TypeDescriptor.of(str)

        self.assertIsNone(descriptor.get_key_type())
        self.assertIsNone(descriptor.get_value_type())

    def test_get_raw_type(self):
        """测试获取原始类型"""
        # 泛型类型
        generic_descriptor = TypeDescriptor.of(list[str])
        self.assertEqual(list, generic_descriptor.get_raw_type())

        # 基本类型
        basic_descriptor = TypeDescriptor.of(str)
        self.assertEqual(str, basic_descriptor.get_raw_type())

    def test_is_assignable_from_same_type(self):
        """测试相同类型的赋值兼容性"""
        descriptor1 = TypeDescriptor.of(str)
        descriptor2 = TypeDescriptor.of(str)

        self.assertTrue(descriptor1.is_assignable_from(descriptor2))

    def test_is_assignable_from_inheritance(self):
        """测试继承关系的赋值兼容性"""

        # 创建一个继承关系的例子
        class Parent:
            pass

        class Child(Parent):
            pass

        parent_descriptor = TypeDescriptor.of(Parent)
        child_descriptor = TypeDescriptor.of(Child)

        # Parent 可以接受 Child 的赋值
        self.assertTrue(parent_descriptor.is_assignable_from(child_descriptor))
        # Child 不能接受 Parent 的赋值
        self.assertFalse(child_descriptor.is_assignable_from(parent_descriptor))

    def test_is_assignable_from_different_types(self):
        """测试不同类型的赋值兼容性"""
        str_descriptor = TypeDescriptor.of(str)
        int_descriptor = TypeDescriptor.of(int)

        self.assertFalse(str_descriptor.is_assignable_from(int_descriptor))

    def test_string_representation(self):
        """测试字符串表示"""
        # 基本类型
        basic_descriptor = TypeDescriptor.of(str)
        self.assertEqual("str", str(basic_descriptor))

        # 泛型类型
        generic_descriptor = TypeDescriptor.of(list[str])
        self.assertEqual("list[str]", str(generic_descriptor))

        # 复杂泛型类型
        complex_descriptor = TypeDescriptor.of(dict[str, int])
        self.assertEqual("dict[str, int]", str(complex_descriptor))

    def test_repr_representation(self):
        """测试详细字符串表示"""
        descriptor = TypeDescriptor.of(list[str])
        repr_str = repr(descriptor)

        self.assertIn("TypeDescriptor", repr_str)
        self.assertIn("type=", repr_str)
        self.assertIn("origin=", repr_str)
        self.assertIn("args=", repr_str)

    def test_frozen_dataclass(self):
        """测试类型描述符是不可变的"""
        descriptor = TypeDescriptor.of(str)

        # 尝试修改应该抛出异常
        with self.assertRaises((AttributeError, TypeError)):
            descriptor.type = int

    def test_complex_nested_generics(self):
        """测试复杂嵌套泛型"""
        descriptor = TypeDescriptor.of(dict[str, list[int]])

        self.assertTrue(descriptor.is_mapping())
        self.assertEqual(dict, descriptor.origin)

        key_type = descriptor.get_key_type()
        self.assertEqual(str, key_type.type)

        value_type = descriptor.get_value_type()
        self.assertEqual(list[int], value_type.type)
        self.assertTrue(value_type.is_collection())


if __name__ == "__main__":
    unittest.main()
