#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频数据模型测试
"""

from miniboot.starters.audio.models import AudioItem, AudioItemType


class TestAudioItemType:
    """音频项目类型测试"""

    def test_enum_values(self):
        """测试枚举值"""
        assert AudioItemType.FILE.value == "file"
        assert AudioItemType.TEXT.value == "text"


class TestAudioItem:
    """音频项目测试"""

    def test_create_file_item(self):
        """测试创建文件项目"""
        item = AudioItem.file("test.mp3")

        assert item.type == AudioItemType.FILE
        assert item.content == "test.mp3"
        assert item.options is None

    def test_create_file_item_with_options(self):
        """测试创建带选项的文件项目"""
        options = {"volume": 0.8, "loop": True}
        item = AudioItem.file("test.mp3", **options)

        assert item.type == AudioItemType.FILE
        assert item.content == "test.mp3"
        assert item.options == options

    def test_create_text_item(self):
        """测试创建文本项目"""
        item = AudioItem.text("测试文本")

        assert item.type == AudioItemType.TEXT
        assert item.content == "测试文本"
        assert item.options is None

    def test_create_text_item_with_options(self):
        """测试创建带选项的文本项目"""
        options = {"rate": 200, "volume": 0.9}
        item = AudioItem.text("测试文本", **options)

        assert item.type == AudioItemType.TEXT
        assert item.content == "测试文本"
        assert item.options == options

    def test_direct_creation(self):
        """测试直接创建"""
        item = AudioItem(AudioItemType.FILE, "test.wav", {"volume": 0.5})

        assert item.type == AudioItemType.FILE
        assert item.content == "test.wav"
        assert item.options == {"volume": 0.5}
