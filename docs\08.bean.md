# Mini-Boot Bean 模块设计

## 概述

Bean 模块是 Mini-Boot 框架的核心组件，负责实现控制反转(IoC)和依赖注入(DI)功能。该模块提供了类似 Spring 框架的 Bean 容器管理能力，包括 Bean 的定义、注册、创建、生命周期管理和依赖注入等核心功能。

## 核心组件

### 1. Bean 定义 (bean_definition.py)

`BeanDefinition` 类定义了 Bean 的元数据，包括 Bean 的名称、类型、作用域、初始化和销毁方法等信息：

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, Type, Optional, Callable
from enum import Enum

class BeanScope(Enum):
    """Bean作用域枚举"""
    SINGLETON = "singleton"
    PROTOTYPE = "prototype"

class BeanDefinition:
    """Bean定义类"""

    def __init__(self, bean_name: str, bean_class: Type, scope: BeanScope = BeanScope.SINGLETON):
        self.bean_name = bean_name
        self.bean_class = bean_class
        self.scope = scope
        self.init_method_name: Optional[str] = None
        self.destroy_method_name: Optional[str] = None
        self.property_values: Dict[str, Any] = {}
        self.constructor_args: list = []
        self.lazy_init: bool = False
        self.primary: bool = False
        self.depends_on: list = []

    @property
    def is_singleton(self) -> bool:
        """是否为单例"""
        return self.scope == BeanScope.SINGLETON

    @property
    def is_prototype(self) -> bool:
        """是否为原型"""
        return self.scope == BeanScope.PROTOTYPE

    def set_init_method(self, method_name: str) -> None:
        """设置初始化方法"""
        self.init_method_name = method_name

    def set_destroy_method(self, method_name: str) -> None:
        """设置销毁方法"""
        self.destroy_method_name = method_name

    def add_property_value(self, name: str, value: Any) -> None:
        """添加属性值"""
        self.property_values[name] = value

    def get_property_value(self, name: str) -> Any:
        """获取属性值"""
        return self.property_values.get(name)

    def add_constructor_arg(self, value: Any) -> None:
        """添加构造函数参数"""
        self.constructor_args.append(value)

    def __repr__(self) -> str:
        return f"BeanDefinition(name={self.bean_name}, class={self.bean_class.__name__}, scope={self.scope.value})"
```

### 2. Bean 注册表 (bean_registry.py)

`BeanDefinitionRegistry` 接口定义了 Bean 定义的注册和管理功能：

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Type, Optional
from threading import RLock
from .bean_definition import BeanDefinition

class BeanDefinitionRegistry(ABC):
    """Bean定义注册表接口"""

    @abstractmethod
    def register_bean_definition(self, name: str, bean_definition: BeanDefinition) -> None:
        """注册Bean定义"""
        pass

    @abstractmethod
    def remove_bean_definition(self, name: str) -> None:
        """移除Bean定义"""
        pass

    @abstractmethod
    def get_bean_definition(self, name: str) -> BeanDefinition:
        """获取Bean定义"""
        pass

    @abstractmethod
    def contains_bean_definition(self, name: str) -> bool:
        """检查是否包含Bean定义"""
        pass

    @abstractmethod
    def get_bean_definition_names(self) -> List[str]:
        """获取所有Bean定义名称"""
        pass

    @abstractmethod
    def get_bean_definition_count(self) -> int:
        """获取Bean定义数量"""
        pass

    @abstractmethod
    def find_bean_definitions_by_type(self, bean_type: Type) -> List[BeanDefinition]:
        """根据类型查找Bean定义"""
        pass

class DefaultBeanDefinitionRegistry(BeanDefinitionRegistry):
    """默认Bean定义注册表实现"""

    def __init__(self):
        self._bean_definitions: Dict[str, BeanDefinition] = {}
        self._lock = RLock()

    def register_bean_definition(self, name: str, bean_definition: BeanDefinition) -> None:
        """注册Bean定义"""
        with self._lock:
            if name in self._bean_definitions:
                raise ValueError(f"Bean definition with name '{name}' already exists")
            self._bean_definitions[name] = bean_definition

    def remove_bean_definition(self, name: str) -> None:
        """移除Bean定义"""
        with self._lock:
            if name not in self._bean_definitions:
                raise ValueError(f"Bean definition with name '{name}' not found")
            del self._bean_definitions[name]

    def get_bean_definition(self, name: str) -> BeanDefinition:
        """获取Bean定义"""
        with self._lock:
            if name not in self._bean_definitions:
                raise ValueError(f"Bean definition with name '{name}' not found")
            return self._bean_definitions[name]

    def contains_bean_definition(self, name: str) -> bool:
        """检查是否包含Bean定义"""
        with self._lock:
            return name in self._bean_definitions

    def get_bean_definition_names(self) -> List[str]:
        """获取所有Bean定义名称"""
        with self._lock:
            return list(self._bean_definitions.keys())

    def get_bean_definition_count(self) -> int:
        """获取Bean定义数量"""
        with self._lock:
            return len(self._bean_definitions)

    def find_bean_definitions_by_type(self, bean_type: Type) -> List[BeanDefinition]:
        """根据类型查找Bean定义"""
        with self._lock:
            result = []
            for bean_def in self._bean_definitions.values():
                if issubclass(bean_def.bean_class, bean_type):
                    result.append(bean_def)
            return result
```

### 3. 依赖管理 (dependency_graph.py)

`DependencyGraph` 类用于管理 Bean 之间的依赖关系：

```python
from typing import Dict, Set, List
from threading import RLock
import inspect
from .errors import CircularDependencyError

class DependencyGraph:
    """依赖关系图"""

    def __init__(self):
        self._dependencies: Dict[str, Set[str]] = {}
        self._creating: Set[str] = set()
        self._created: Set[str] = set()
        self._lock = RLock()

    def add_dependency(self, bean_name: str, dependency_name: str) -> None:
        """添加依赖关系"""
        with self._lock:
            if bean_name not in self._dependencies:
                self._dependencies[bean_name] = set()
            self._dependencies[bean_name].add(dependency_name)

    def check_circular_dependency(self, bean_name: str, path: List[str] = None) -> None:
        """检查循环依赖"""
        if path is None:
            path = []

        if bean_name in path:
            cycle_path = path[path.index(bean_name):] + [bean_name]
            raise CircularDependencyError(f"Circular dependency detected: {' -> '.join(cycle_path)}")

        path.append(bean_name)
        dependencies = self._dependencies.get(bean_name, set())

        for dep in dependencies:
            self.check_circular_dependency(dep, path.copy())

    def get_dependencies(self, bean_name: str) -> Set[str]:
        """获取Bean的所有依赖"""
        with self._lock:
            return self._dependencies.get(bean_name, set()).copy()

    def analyze_dependencies(self, bean_name: str, bean_class: type) -> None:
        """分析Bean的依赖关系"""
        # 分析构造函数依赖
        self._analyze_constructor_dependencies(bean_name, bean_class)

        # 分析字段依赖
        self._analyze_field_dependencies(bean_name, bean_class)

    def _analyze_constructor_dependencies(self, bean_name: str, bean_class: type) -> None:
        """分析构造函数依赖"""
        try:
            signature = inspect.signature(bean_class.__init__)
            for param_name, param in signature.parameters.items():
                if param_name == 'self':
                    continue

                # 如果参数有类型注解，添加依赖
                if param.annotation != inspect.Parameter.empty:
                    dep_name = self._get_dependency_name(param.annotation)
                    if dep_name:
                        self.add_dependency(bean_name, dep_name)
        except (ValueError, TypeError):
            pass

    def _analyze_field_dependencies(self, bean_name: str, bean_class: type) -> None:
        """分析字段依赖"""
        # 获取类的所有注解
        annotations = getattr(bean_class, '__annotations__', {})

        for field_name, field_type in annotations.items():
            # 检查是否有依赖注入标记
            if hasattr(bean_class, field_name):
                field_value = getattr(bean_class, field_name)
                if hasattr(field_value, '__inject__'):
                    dep_name = self._get_dependency_name(field_type)
                    if dep_name:
                        self.add_dependency(bean_name, dep_name)

    def _get_dependency_name(self, dependency_type: type) -> str:
        """获取依赖名称"""
        if hasattr(dependency_type, '__name__'):
            return dependency_type.__name__.lower()
        return str(dependency_type).lower()

    def mark_creating(self, bean_name: str) -> None:
        """标记Bean正在创建"""
        with self._lock:
            self._creating.add(bean_name)

    def mark_created(self, bean_name: str) -> None:
        """标记Bean创建完成"""
        with self._lock:
            self._creating.discard(bean_name)
            self._created.add(bean_name)

    def is_creating(self, bean_name: str) -> bool:
        """检查Bean是否正在创建"""
        with self._lock:
            return bean_name in self._creating

    def is_created(self, bean_name: str) -> bool:
        """检查Bean是否已创建"""
        with self._lock:
            return bean_name in self._created
```

### 4. Bean 工厂 (bean_factory.py)

`BeanFactory` 接口定义了获取和管理 Bean 实例的方法：

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, Type, Optional, List
from threading import RLock
from .bean_definition import BeanDefinition, BeanScope
from .bean_registry import BeanDefinitionRegistry
from .dependency_graph import DependencyGraph
from .errors import BeanCreationError, NoSuchBeanDefinitionError

class BeanFactory(ABC):
    """Bean工厂接口"""

    @abstractmethod
    def get_bean(self, name: str) -> Any:
        """获取Bean实例"""
        pass

    @abstractmethod
    def get_bean_of_type(self, name: str, bean_type: Type) -> Any:
        """获取指定类型的Bean实例"""
        pass

    @abstractmethod
    def get_beans_of_type(self, bean_type: Type) -> Dict[str, Any]:
        """获取指定类型的所有Bean实例"""
        pass

    @abstractmethod
    def contains_bean(self, name: str) -> bool:
        """检查是否包含Bean"""
        pass

    @abstractmethod
    def is_singleton(self, name: str) -> bool:
        """检查Bean是否为单例"""
        pass

    @abstractmethod
    def is_prototype(self, name: str) -> bool:
        """检查Bean是否为原型"""
        pass

    @abstractmethod
    def get_type(self, name: str) -> Type:
        """获取Bean类型"""
        pass

class DefaultBeanFactory(BeanFactory):
    """默认Bean工厂实现"""

    def __init__(self, registry: BeanDefinitionRegistry):
        self.registry = registry
        self.dependency_graph = DependencyGraph()

        # 三级缓存机制
        self._singleton_objects: Dict[str, Any] = {}  # 一级缓存：完全初始化的Bean
        self._early_singleton_objects: Dict[str, Any] = {}  # 二级缓存：原始Bean对象
        self._singleton_factories: Dict[str, callable] = {}  # 三级缓存：Bean工厂

        # 原型Bean对象池
        self._prototype_pool: Dict[str, List[Any]] = {}

        # 线程安全锁
        self._lock = RLock()

        # Bean后置处理器
        self._bean_post_processors: List['BeanPostProcessor'] = []

    def get_bean(self, name: str) -> Any:
        """获取Bean实例"""
        if not self.registry.contains_bean_definition(name):
            raise NoSuchBeanDefinitionError(f"No bean definition found for name: {name}")

        bean_definition = self.registry.get_bean_definition(name)

        if bean_definition.is_singleton:
            return self._get_singleton_bean(name, bean_definition)
        else:
            return self._create_prototype_bean(name, bean_definition)

    def _get_singleton_bean(self, name: str, bean_definition: BeanDefinition) -> Any:
        """获取单例Bean"""
        with self._lock:
            # 一级缓存检查
            if name in self._singleton_objects:
                return self._singleton_objects[name]

            # 二级缓存检查
            if name in self._early_singleton_objects:
                return self._early_singleton_objects[name]

            # 三级缓存检查
            if name in self._singleton_factories:
                bean = self._singleton_factories[name]()
                self._early_singleton_objects[name] = bean
                del self._singleton_factories[name]
                return bean

            # 创建新的单例Bean
            return self._create_singleton_bean(name, bean_definition)

    def _create_singleton_bean(self, name: str, bean_definition: BeanDefinition) -> Any:
        """创建单例Bean"""
        try:
            # 检查循环依赖
            self.dependency_graph.check_circular_dependency(name)

            # 标记正在创建
            self.dependency_graph.mark_creating(name)

            # 创建Bean实例
            bean = self._instantiate_bean(bean_definition)

            # 放入三级缓存
            self._singleton_factories[name] = lambda: bean

            # 注入依赖
            self._inject_dependencies(bean, bean_definition)

            # 初始化Bean
            self._initialize_bean(bean, bean_definition)

            # 应用后置处理器
            bean = self._apply_bean_post_processors_after_initialization(bean, name)

            # 放入一级缓存
            self._singleton_objects[name] = bean

            # 清理二级和三级缓存
            self._early_singleton_objects.pop(name, None)
            self._singleton_factories.pop(name, None)

            # 标记创建完成
            self.dependency_graph.mark_created(name)

            return bean

        except Exception as e:
            # 清理缓存
            self._early_singleton_objects.pop(name, None)
            self._singleton_factories.pop(name, None)
            raise BeanCreationError(f"Failed to create bean '{name}': {str(e)}")

    def _create_prototype_bean(self, name: str, bean_definition: BeanDefinition) -> Any:
        """创建原型Bean"""
        try:
            # 创建Bean实例
            bean = self._instantiate_bean(bean_definition)

            # 注入依赖
            self._inject_dependencies(bean, bean_definition)

            # 初始化Bean
            self._initialize_bean(bean, bean_definition)

            # 应用后置处理器
            bean = self._apply_bean_post_processors_after_initialization(bean, name)

            return bean

        except Exception as e:
            raise BeanCreationError(f"Failed to create prototype bean '{name}': {str(e)}")

    def _instantiate_bean(self, bean_definition: BeanDefinition) -> Any:
        """实例化Bean"""
        try:
            # 准备构造函数参数
            constructor_args = []
            for arg in bean_definition.constructor_args:
                if isinstance(arg, str) and self.registry.contains_bean_definition(arg):
                    constructor_args.append(self.get_bean(arg))
                else:
                    constructor_args.append(arg)

            # 创建实例
            if constructor_args:
                return bean_definition.bean_class(*constructor_args)
            else:
                return bean_definition.bean_class()

        except Exception as e:
            raise BeanCreationError(f"Failed to instantiate bean: {str(e)}")

    def _inject_dependencies(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """注入依赖"""
        # 注入属性值
        for prop_name, prop_value in bean_definition.property_values.items():
            if hasattr(bean, prop_name):
                if isinstance(prop_value, str) and self.registry.contains_bean_definition(prop_value):
                    # 如果属性值是Bean名称，注入Bean实例
                    setattr(bean, prop_name, self.get_bean(prop_value))
                else:
                    setattr(bean, prop_name, prop_value)

        # 分析并注入字段依赖
        self._inject_field_dependencies(bean)

    def _inject_field_dependencies(self, bean: Any) -> None:
        """注入字段依赖"""
        bean_class = bean.__class__
        annotations = getattr(bean_class, '__annotations__', {})

        for field_name, field_type in annotations.items():
            if hasattr(bean, field_name):
                field_value = getattr(bean, field_name)
                # 检查是否需要依赖注入
                if hasattr(field_value, '__inject__') or field_value is None:
                    # 查找对应的Bean
                    dependency_name = self._get_dependency_name(field_type)
                    if self.registry.contains_bean_definition(dependency_name):
                        setattr(bean, field_name, self.get_bean(dependency_name))

    def _get_dependency_name(self, dependency_type: Type) -> str:
        """获取依赖名称"""
        if hasattr(dependency_type, '__name__'):
            return dependency_type.__name__.lower()
        return str(dependency_type).lower()

    def _initialize_bean(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """初始化Bean"""
        # 执行初始化方法
        if bean_definition.init_method_name:
            init_method = getattr(bean, bean_definition.init_method_name, None)
            if init_method and callable(init_method):
                init_method()

        # 如果Bean实现了InitializingBean接口
        if hasattr(bean, 'after_properties_set'):
            bean.after_properties_set()

    def _apply_bean_post_processors_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """应用Bean后置处理器"""
        result = bean
        for processor in self._bean_post_processors:
            result = processor.post_process_after_initialization(result, bean_name)
        return result

    def get_bean_of_type(self, name: str, bean_type: Type) -> Any:
        """获取指定类型的Bean实例"""
        bean = self.get_bean(name)
        if not isinstance(bean, bean_type):
            raise BeanCreationError(f"Bean '{name}' is not of type {bean_type}")
        return bean

    def get_beans_of_type(self, bean_type: Type) -> Dict[str, Any]:
        """获取指定类型的所有Bean实例"""
        result = {}
        bean_definitions = self.registry.find_bean_definitions_by_type(bean_type)

        for bean_def in bean_definitions:
            bean = self.get_bean(bean_def.bean_name)
            result[bean_def.bean_name] = bean

        return result

    def contains_bean(self, name: str) -> bool:
        """检查是否包含Bean"""
        return self.registry.contains_bean_definition(name)

    def is_singleton(self, name: str) -> bool:
        """检查Bean是否为单例"""
        if not self.registry.contains_bean_definition(name):
            return False
        bean_definition = self.registry.get_bean_definition(name)
        return bean_definition.is_singleton

    def is_prototype(self, name: str) -> bool:
        """检查Bean是否为原型"""
        if not self.registry.contains_bean_definition(name):
            return False
        bean_definition = self.registry.get_bean_definition(name)
        return bean_definition.is_prototype

    def get_type(self, name: str) -> Type:
        """获取Bean类型"""
        if not self.registry.contains_bean_definition(name):
            raise NoSuchBeanDefinitionError(f"No bean definition found for name: {name}")
        bean_definition = self.registry.get_bean_definition(name)
        return bean_definition.bean_class

    def add_bean_post_processor(self, processor: 'BeanPostProcessor') -> None:
        """添加Bean后置处理器"""
        self._bean_post_processors.append(processor)

    def destroy_singletons(self) -> None:
        """销毁所有单例Bean"""
        with self._lock:
            for name, bean in self._singleton_objects.items():
                try:
                    self._destroy_bean(bean, name)
                except Exception as e:
                    print(f"Error destroying bean '{name}': {e}")

            self._singleton_objects.clear()
            self._early_singleton_objects.clear()
            self._singleton_factories.clear()

    def _destroy_bean(self, bean: Any, bean_name: str) -> None:
        """销毁Bean"""
        bean_definition = self.registry.get_bean_definition(bean_name)

        # 执行销毁方法
        if bean_definition.destroy_method_name:
            destroy_method = getattr(bean, bean_definition.destroy_method_name, None)
            if destroy_method and callable(destroy_method):
                destroy_method()

        # 如果Bean实现了DisposableBean接口
        if hasattr(bean, 'destroy'):
            bean.destroy()
```

### 5. 生命周期管理 (lifecycle.py)

Bean 模块提供了丰富的生命周期接口，用于在 Bean 的不同阶段执行自定义逻辑：

```python
from abc import ABC, abstractmethod
from typing import Any

class BeanPostProcessor(ABC):
    """Bean后置处理器接口"""

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化前执行处理"""
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化后执行处理"""
        return bean

class InitializingBean(ABC):
    """初始化Bean接口"""

    @abstractmethod
    def after_properties_set(self) -> None:
        """在属性设置后执行初始化"""
        pass

class DisposableBean(ABC):
    """可销毁Bean接口"""

    @abstractmethod
    def destroy(self) -> None:
        """在Bean销毁前执行清理"""
        pass

class BeanNameAware(ABC):
    """Bean名称感知接口"""

    @abstractmethod
    def set_bean_name(self, name: str) -> None:
        """设置Bean名称"""
        pass

class BeanFactoryAware(ABC):
    """Bean工厂感知接口"""

    @abstractmethod
    def set_bean_factory(self, bean_factory: 'BeanFactory') -> None:
        """设置Bean工厂"""
        pass

class Lifecycle(ABC):
    """生命周期接口"""

    @abstractmethod
    def start(self) -> None:
        """启动Bean"""
        pass

    @abstractmethod
    def stop(self) -> None:
        """停止Bean"""
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查Bean是否正在运行"""
        pass
```

### 6. 错误处理 (errors.py)

Bean 模块定义了多种错误类型，用于处理 Bean 管理过程中可能出现的异常情况：

```python
class BeanException(Exception):
    """Bean异常基类"""
    pass

class BeanDefinitionError(BeanException):
    """Bean定义错误"""
    pass

class NoSuchBeanDefinitionError(BeanException):
    """找不到Bean定义错误"""
    pass

class CircularDependencyError(BeanException):
    """循环依赖错误"""
    pass

class BeanCreationError(BeanException):
    """Bean创建错误"""
    pass

class BeanInstantiationError(BeanException):
    """Bean实例化错误"""
    pass

class BeanInitializationError(BeanException):
    """Bean初始化错误"""
    pass
```

### 7. 依赖注入装饰器 (decorators.py)

提供便捷的装饰器用于依赖注入：

```python
from typing import Any, Type, Optional
from functools import wraps

class Inject:
    """依赖注入标记类"""

    def __init__(self, name: Optional[str] = None):
        self.name = name
        self.__inject__ = True

def inject(name: Optional[str] = None):
    """依赖注入装饰器"""
    return Inject(name)

def component(name: Optional[str] = None, scope: str = "singleton"):
    """组件装饰器"""
    def decorator(cls):
        cls.__component__ = True
        cls.__component_name__ = name or cls.__name__.lower()
        cls.__component_scope__ = scope
        return cls
    return decorator

def service(name: Optional[str] = None):
    """服务装饰器"""
    return component(name, "singleton")

def repository(name: Optional[str] = None):
    """仓库装饰器"""
    return component(name, "singleton")

def controller(name: Optional[str] = None):
    """控制器装饰器"""
    return component(name, "singleton")

# 使用示例
@service("userService")
class UserService:
    def __init__(self):
        self.user_repository: UserRepository = inject()

    def get_user(self, user_id: int):
        return self.user_repository.find_by_id(user_id)

@repository("userRepository")
class UserRepository:
    def find_by_id(self, user_id: int):
        # 数据库查询逻辑
        return {"id": user_id, "name": "User"}
```

## 依赖注入机制

Bean 模块通过反射机制和类型注解实现依赖注入，主要支持以下注入方式：

1. **字段注入**: 通过类型注解和 `inject()` 标记注入依赖
2. **构造函数注入**: 通过构造函数参数类型注解注入依赖
3. **属性注入**: 通过 `add_property_value` 方法设置属性值

### 字段注入示例

```python
@service()
class OrderService:
    def __init__(self):
        # 字段注入
        self.user_service: UserService = inject()
        self.payment_service: PaymentService = inject("paymentService")

    def create_order(self, user_id: int, amount: float):
        user = self.user_service.get_user(user_id)
        payment = self.payment_service.process_payment(amount)
        return {"user": user, "payment": payment}
```

### 构造函数注入示例

```python
@service()
class NotificationService:
    def __init__(self, email_service: EmailService, sms_service: SmsService):
        # 构造函数注入
        self.email_service = email_service
        self.sms_service = sms_service

    def send_notification(self, message: str):
        self.email_service.send(message)
        self.sms_service.send(message)
```

## Bean 创建流程

1. **获取 Bean 定义**: 从注册表中获取 Bean 定义
2. **检查循环依赖**: 使用依赖图检测是否存在循环依赖
3. **创建 Bean 实例**: 根据 Bean 定义创建实例
4. **注入依赖**: 注入 Bean 的依赖
5. **初始化 Bean**: 执行 Bean 的初始化方法
6. **应用后置处理器**: 应用 `BeanPostProcessor` 进行处理

对于单例 Bean，创建后会缓存在一级缓存中，下次获取时直接返回缓存的实例。对于原型 Bean，每次获取都会创建一个新的实例。

## 循环依赖解决方案

Bean 模块使用三级缓存机制解决循环依赖问题：

1. **一级缓存 (singleton_objects)**: 存储完全初始化好的 Bean
2. **二级缓存 (early_singleton_objects)**: 存储原始的 Bean 对象，尚未填充属性
3. **三级缓存 (singleton_factories)**: 存储 Bean 的工厂对象，用于解决循环依赖

### 循环依赖解决流程

1. 当 Bean A 依赖 Bean B，而 Bean B 又依赖 Bean A 时，会产生循环依赖
2. 创建 Bean A 时，先创建原始对象，并放入三级缓存
3. 注入 Bean A 的依赖时，需要创建 Bean B
4. 创建 Bean B 时，注入依赖需要 Bean A，此时从三级缓存中获取 Bean A 的原始对象
5. Bean B 创建完成后，继续完成 Bean A 的创建

这种机制只能解决单例 Bean 的循环依赖，对于原型 Bean 的循环依赖，会直接报错。

## 使用示例

### 基本使用

```python
from miniboot.bean import (
    BeanDefinition, BeanScope, DefaultBeanDefinitionRegistry,
    DefaultBeanFactory, component, service, inject
)

# 1. 定义Bean类
@service("userService")
class UserService:
    def __init__(self):
        self.user_repository: UserRepository = inject()

    def get_user(self, user_id: int):
        return self.user_repository.find_by_id(user_id)

@service("userRepository")
class UserRepository:
    def find_by_id(self, user_id: int):
        return {"id": user_id, "name": f"User{user_id}"}

# 2. 创建Bean容器（使用简化API）
from miniboot.bean import create_bean_factory, create_bean_registry
registry = create_bean_registry()
factory = create_bean_factory()

# 3. 注册Bean定义
user_service_def = BeanDefinition("userService", UserService, BeanScope.SINGLETON)
user_repo_def = BeanDefinition("userRepository", UserRepository, BeanScope.SINGLETON)

registry.register_bean_definition("userService", user_service_def)
registry.register_bean_definition("userRepository", user_repo_def)

# 4. 获取Bean实例
user_service = factory.get_bean("userService")
user = user_service.get_user(1)
print(user)  # {'id': 1, 'name': 'User1'}
```

### 高级使用

```python
from miniboot.bean import BeanPostProcessor, InitializingBean, DisposableBean

# 自定义Bean后置处理器
class LoggingBeanPostProcessor(BeanPostProcessor):
    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        print(f"Bean '{bean_name}' initialized: {type(bean).__name__}")
        return bean

# 实现生命周期接口的Bean
@service("databaseService")
class DatabaseService(InitializingBean, DisposableBean):
    def __init__(self):
        self.connection = None

    def after_properties_set(self) -> None:
        """初始化数据库连接"""
        self.connection = "database_connection"
        print("Database connection established")

    def destroy(self) -> None:
        """关闭数据库连接"""
        if self.connection:
            self.connection = None
            print("Database connection closed")

    def query(self, sql: str):
        return f"Result for: {sql}"

# 使用Bean容器（使用简化API）
from miniboot.bean import create_bean_factory, create_bean_registry
registry = create_bean_registry()
factory = create_bean_factory()

# 添加Bean后置处理器
factory.add_bean_post_processor(LoggingBeanPostProcessor())

# 注册Bean定义
db_service_def = BeanDefinition("databaseService", DatabaseService, BeanScope.SINGLETON)
registry.register_bean_definition("databaseService", db_service_def)

# 获取Bean（会触发初始化）
db_service = factory.get_bean("databaseService")
result = db_service.query("SELECT * FROM users")

# 销毁Bean容器（会触发销毁方法）
factory.destroy_singletons()
```

### 与环境模块集成

```python
from miniboot.env import StandardEnvironment
from miniboot.bean import ApplicationContext

# 创建应用上下文
env = StandardEnvironment()
context = ApplicationContext(env)

# 扫描并注册组件
context.scan("com.example.services")

# 获取Bean
user_service = context.get_bean("userService")
order_service = context.get_bean("orderService")

# 启动应用上下文
context.start()

# 关闭应用上下文
context.close()
```

## 与 Go 版本对比

| 特性       | Go 版本            | Python 版本       |
| ---------- | ------------------ | ----------------- |
| Bean 定义  | interface + struct | class + dataclass |
| 依赖注入   | 反射 + 标签        | 类型注解 + 装饰器 |
| 生命周期   | interface 实现     | ABC 接口实现      |
| 循环依赖   | 三级缓存           | 三级缓存          |
| 线程安全   | sync.RWMutex       | threading.RLock   |
| 错误处理   | error 返回值       | Exception 抛出    |
| 作用域     | 常量定义           | Enum 枚举         |
| 后置处理器 | interface 实现     | ABC 接口实现      |

## 总结

Bean 模块是 Mini-Boot 框架的核心，提供了完整的 IoC 容器功能，包括：

### 核心特性

1. **Bean 定义管理**: 支持 Bean 的注册、查找和管理
2. **依赖注入**: 支持字段注入、构造函数注入和属性注入
3. **生命周期管理**: 提供完整的 Bean 生命周期接口
4. **循环依赖解决**: 使用三级缓存机制解决单例 Bean 的循环依赖
5. **多种作用域**: 支持单例和原型两种作用域
6. **线程安全**: 使用锁机制确保多线程环境下的安全性
7. **扩展性**: 支持 Bean 后置处理器和自定义扩展

### 设计优势

1. **类型安全**: 充分利用 Python 的类型注解系统
2. **装饰器支持**: 提供便捷的装饰器简化 Bean 定义
3. **接口设计**: 清晰的接口分离，易于扩展和测试
4. **错误处理**: 完善的异常体系，便于问题定位
5. **性能优化**: 三级缓存和分段锁提高并发性能

通过这些功能，用户可以实现控制反转，将对象的创建和依赖管理交给容器处理，从而降低代码的耦合度，提高可维护性和可测试性。

---

_本文档定义了 Mini-Boot 框架的 Bean 模块设计，提供完整的 IoC 容器功能。_
