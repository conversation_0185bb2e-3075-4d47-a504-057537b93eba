#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean处理器模块 - 提供Bean生命周期处理和增强功能

Bean 处理器模块

提供 Mini-Boot 框架的 Bean 后置处理器,用于在 Bean 初始化前后执行自定义逻辑.

主要功能:
- 基础处理器接口 (BeanPostProcessor)
- 处理器注册表 (BeanPostProcessorRegistry)
- 自动装配处理器 (@Autowired 注解处理)
- 配置类处理器 (@Configuration 和 @Bean 注解处理)
- 事件处理器 (@EventListener 注解处理)
- 生命周期处理器 (Bean 生命周期管理)
- 调度处理器 (@Scheduled 注解处理)
- 值注入处理器 (@Value 注解处理)
- Web 处理器 (@Controller、@RequestMapping 等注解处理)
"""

# 异步处理器
from .asyncs import AsyncAnnotationBeanPostProcessor, AsyncBeanFactoryIntegration, AsyncLifecycleBeanPostProcessor

# 自动装配处理器
from .autowired import AutowiredAnnotationProcessor

# 基础接口和抽象基类
from .base import AbstractAnnotationProcessor, BeanPostProcessor, OrderedBeanPostProcessor, ProcessorOrder

# 配置属性处理器
from .configuration import ConfigurationPropertiesProcessor

# 处理器装饰器
from .decorators import (
    cache_aware_processor,
    lightweight_processor_handler,
    processor_exception_handler,
    processor_method_handler,
    std_processor,
    supports_check_processor,
)

# 事件监听处理器
from .event import EventListenerProcessor

# 生命周期处理器
from .lifecycle import LifecycleAnnotationProcessor

# 处理器管理器
from .manager import BeanPostProcessorManager, ProcessorConfig, ProcessorMetrics, ProcessorState

# Profile 处理器
from .profile import ProfileBeanPostProcessor, ProfileConditionEvaluator, create_profile_condition_evaluator, create_profile_processor

# 处理器注册表
from .registry import BeanPostProcessorRegistry

# 定时任务处理器
from .schedule import ScheduledAnnotationProcessor

# 作用域处理器
from .scope import CoreScopeBeanPostProcessor, ScopedBeanFactory, create_scope_processor, create_scoped_bean_factory

# 值注入处理器
from .value import ValueAnnotationProcessor
from .web import WebAnnotationProcessor

__all__ = [
    # ========== 核心处理器接口 ==========
    "BeanPostProcessor",
    # ========== 处理器基类和管理器 ==========
    "OrderedBeanPostProcessor",
    "AbstractAnnotationProcessor",
    # ========== 常量 ==========
    "ProcessorOrder",
    # ========== 注册表和管理器 ==========
    "BeanPostProcessorRegistry",
    "BeanPostProcessorManager",
    "ProcessorConfig",
    "ProcessorMetrics",
    "ProcessorState",
    # ========== 具体处理器 ==========
    "AsyncAnnotationBeanPostProcessor",
    "AsyncLifecycleBeanPostProcessor",
    "AsyncBeanFactoryIntegration",
    "AutowiredAnnotationProcessor",
    "ValueAnnotationProcessor",
    "LifecycleAnnotationProcessor",
    "ConfigurationPropertiesProcessor",
    "ProfileBeanPostProcessor",
    "ProfileConditionEvaluator",
    "create_profile_processor",
    "create_profile_condition_evaluator",
    "CoreScopeBeanPostProcessor",
    "ScopedBeanFactory",
    "create_scope_processor",
    "create_scoped_bean_factory",
    "EventListenerProcessor",
    "ScheduledAnnotationProcessor",
    "WebAnnotationProcessor",
    # ========== 处理器装饰器（从 decorators.py 移动）==========
    "processor_exception_handler",
    "lightweight_processor_handler",
    "processor_method_handler",
    "cache_aware_processor",
    "supports_check_processor",
    "std_processor",
]
