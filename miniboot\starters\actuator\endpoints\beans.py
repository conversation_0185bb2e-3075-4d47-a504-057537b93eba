#!/usr/bin/env python
"""
* @author: cz
* @description: Bean 信息端点实现

提供 Bean 定义、实例、依赖关系等信息的监控端点.
类似于 Spring Boot Actuator 的 /beans 端点.
"""

from datetime import datetime
from typing import TYPE_CHECKING, Any, Optional

from loguru import logger

from miniboot.bean.definition import BeanDefinition
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.monitoring.interfaces import EndpointInfo, EndpointProvider

from .base import BaseEndpoint, EndpointOperation, OperationType


if TYPE_CHECKING:
    from miniboot.context.application import ApplicationContext


class BeansEndpoint(BaseEndpoint, EndpointProvider):
    """Bean 信息端点

    提供应用中所有 Bean 的详细信息,包括:
    - Bean 定义信息
    - Bean 实例状态
    - Bean 依赖关系
    - Bean 作用域信息
    """

    def __init__(self, application_context: Optional["ApplicationContext"] = None):
        """初始化 Bean 信息端点

        Args:
            application_context: 应用上下文,用于获取 Bean 信息
        """
        super().__init__("beans", enabled=True, sensitive=False)
        self.application_context = application_context

    def _create_operations(self) -> list[EndpointOperation]:
        """创建操作列表"""
        return [EndpointOperation(operation_type=OperationType.READ, method="GET", handler=self.get)]

    def invoke(self, operation_type: OperationType, **_kwargs) -> Any:
        """执行端点操作"""
        if operation_type == OperationType.READ:
            return self.get()
        raise ValueError(f"Unsupported operation: {operation_type}")

    def get(self) -> dict[str, Any]:
        """获取所有 Bean 信息 - 简化版本"""
        try:
            if not self.application_context:
                return {"error": "Application context not available", "timestamp": datetime.now().isoformat()}

            bean_factory = getattr(self.application_context, "_bean_factory", None)
            if not bean_factory:
                return {"error": "Bean factory not available", "timestamp": datetime.now().isoformat()}

            # 直接从工厂获取信息，不做复杂采集
            return {
                "timestamp": datetime.now().isoformat(),
                "contexts": {
                    "application": {
                        "beans": bean_factory.get_bean_info(),  # 核心：直接查询
                        "parentId": None
                    }
                },
                "statistics": bean_factory.get_simple_stats(),  # 简单统计
                "dependencies": bean_factory.get_dependency_info()  # 依赖信息
            }

        except Exception as e:
            logger.error(f"Failed to get beans info: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}

    # 注意：_collect 和 _build 方法已被简化，现在直接使用 bean_factory.get_bean_info()

    # 注意：复杂的 _build 方法已被简化，现在直接使用 bean_factory.get_bean_info()

    # 注意：复杂的辅助方法已被移除，现在直接使用 bean_factory.get_bean_info() 获取所有信息

    # 注意：_stats 方法已被移除，现在直接使用 bean_factory.get_simple_stats()

    def _stats_legacy(self, bean_registry: DefaultBeanDefinitionRegistry, bean_factory: DefaultBeanFactory) -> dict[str, Any]:
        """收集统计信息

        Args:
            bean_registry: Bean 注册表
            bean_factory: Bean 工厂

        Returns:
            统计信息字典
        """
        try:
            bean_names = bean_registry.get_bean_definition_names()
            total_beans = len(bean_names)

            # 按作用域统计
            scope_stats = {}
            singleton_count = 0
            prototype_count = 0
            web_scope_count = 0

            # 按类型统计
            type_stats = {}

            # 实例统计
            created_instances = 0
            singleton_objects = getattr(bean_factory, "_singleton_objects", {})

            for bean_name in bean_names:
                try:
                    bean_definition = bean_registry.get_bean_definition(bean_name)

                    # 作用域统计
                    scope = bean_definition.scope
                    if scope:
                        scope_name = scope.value
                        scope_stats[scope_name] = scope_stats.get(scope_name, 0) + 1

                        if scope.is_singleton():
                            singleton_count += 1
                        elif scope.is_prototype():
                            prototype_count += 1
                        elif scope.is_web_scope():
                            web_scope_count += 1

                    # 类型统计
                    if bean_definition.bean_class:
                        class_name = bean_definition.bean_class.__name__
                        type_stats[class_name] = type_stats.get(class_name, 0) + 1

                    # 实例统计
                    if bean_name in singleton_objects:
                        created_instances += 1

                except Exception as e:
                    logger.debug(f"Failed to collect stats for bean '{bean_name}': {e}")

            return {
                "totalBeans": total_beans,
                "createdInstances": created_instances,
                "scopeStatistics": {"singleton": singleton_count, "prototype": prototype_count, "webScope": web_scope_count, "details": scope_stats},
                "typeStatistics": type_stats,
                "memoryUsage": {
                    "singletonCacheSize": len(singleton_objects),
                    "earlySingletonCacheSize": len(getattr(bean_factory, "_early_singleton_objects", {})),
                    "singletonFactoryCacheSize": len(getattr(bean_factory, "_singleton_factories", {})),
                },
            }

        except Exception as e:
            logger.error(f"Failed to collect statistics: {e}")
            return {"error": str(e)}

    def set_context(self, application_context: "ApplicationContext") -> None:
        """设置应用上下文

        Args:
            application_context: 应用上下文
        """
        self.application_context = application_context

    # ==================== EndpointProvider 接口实现 ====================

    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息 - EndpointProvider 接口实现"""
        return EndpointInfo(name="beans", path="/beans", methods=["GET"], description="Application beans endpoint", enabled=True, sensitive=False)

    async def handle_request(self, _request: Any) -> Any:
        """处理请求 - EndpointProvider 接口实现"""
        return self.invoke(OperationType.READ)

    def is_enabled(self) -> bool:
        """检查端点是否启用 - EndpointProvider 接口实现"""
        return self.enabled


# 便利函数
def create_beans_endpoint(application_context: Optional["ApplicationContext"] = None) -> BeansEndpoint:
    """创建 Bean 信息端点

    Args:
        application_context: 应用上下文

    Returns:
        Bean 信息端点实例
    """
    return BeansEndpoint(application_context)
