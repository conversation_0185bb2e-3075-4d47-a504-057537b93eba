#!/usr/bin/env python
"""
Web框架性能测试用例

测试背压控制和智能调度的性能提升效果，包括吞吐量、延迟、资源使用等指标。
"""

import pytest
import asyncio
import time
import statistics
import psutil
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from unittest.mock import Mock, patch
from typing import List, Dict, Any

from miniboot.web.application import WebApplication
from miniboot.web.properties import WebProperties, BackpressureConfig, AsyncOptimizationConfig, BackpressureStrategy
from miniboot.web.scheduling.smart_scheduler import SmartTaskScheduler, TaskRequest


class PerformanceMetrics:
    """性能指标收集器"""

    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.response_times = []
        self.memory_usage = []
        self.cpu_usage = []
        self.error_count = 0
        self.success_count = 0

    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.response_times = []
        self.memory_usage = []
        self.cpu_usage = []
        self.error_count = 0
        self.success_count = 0

    def record_response_time(self, response_time: float):
        """记录响应时间"""
        self.response_times.append(response_time)

    def record_success(self):
        """记录成功"""
        self.success_count += 1

    def record_error(self):
        """记录错误"""
        self.error_count += 1

    def record_system_metrics(self):
        """记录系统指标"""
        process = psutil.Process()
        self.memory_usage.append(process.memory_info().rss / 1024 / 1024)  # MB
        self.cpu_usage.append(process.cpu_percent())

    def stop_monitoring(self):
        """停止监控"""
        self.end_time = time.time()

    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        total_time = self.end_time - self.start_time if self.end_time else 0
        total_requests = self.success_count + self.error_count

        return {
            "total_time": total_time,
            "total_requests": total_requests,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / max(1, total_requests) * 100,
            "throughput": total_requests / max(0.001, total_time),  # requests/second
            "avg_response_time": statistics.mean(self.response_times) if self.response_times else 0,
            "p95_response_time": statistics.quantiles(self.response_times, n=20)[18] if len(self.response_times) > 20 else 0,
            "p99_response_time": statistics.quantiles(self.response_times, n=100)[98] if len(self.response_times) > 100 else 0,
            "avg_memory_usage": statistics.mean(self.memory_usage) if self.memory_usage else 0,
            "max_memory_usage": max(self.memory_usage) if self.memory_usage else 0,
            "avg_cpu_usage": statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
            "max_cpu_usage": max(self.cpu_usage) if self.cpu_usage else 0,
        }


class TestWebApplicationPerformance:
    """Web应用性能测试"""

    def test_application_initialization_performance(self):
        """测试应用初始化性能"""
        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 测试多次初始化
        for _ in range(10):
            start_time = time.time()

            properties = WebProperties()
            app = WebApplication(properties)

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)
            metrics.record_success()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证性能指标
        assert summary["avg_response_time"] < 0.1  # 平均初始化时间小于100ms
        assert summary["success_rate"] == 100.0

    @pytest.mark.asyncio
    async def test_application_async_initialization_performance(self):
        """测试应用异步初始化性能"""
        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 测试多次异步初始化
        for _ in range(10):
            start_time = time.time()

            properties = WebProperties()
            app = WebApplication(properties)
            await app.initialize()

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)
            metrics.record_success()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证性能指标
        assert summary["avg_response_time"] < 0.5  # 平均异步初始化时间小于500ms
        assert summary["success_rate"] == 100.0

    def test_status_monitoring_performance(self):
        """测试状态监控性能"""
        properties = WebProperties()
        app = WebApplication(properties)

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 大量状态查询
        for _ in range(1000):
            start_time = time.time()

            status = app.get_status()

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)

            if isinstance(status, dict) and len(status) > 0:
                metrics.record_success()
            else:
                metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证性能指标
        assert summary["avg_response_time"] < 0.001  # 平均响应时间小于1ms
        assert summary["throughput"] > 1000  # 吞吐量大于1000 req/s
        assert summary["success_rate"] == 100.0


class TestBackpressurePerformance:
    """背压控制性能测试"""

    @pytest.mark.asyncio
    async def test_backpressure_controller_performance(self):
        """测试背压控制器性能"""
        config = BackpressureConfig(enabled=True, strategy=BackpressureStrategy.SIMPLE, max_concurrent_requests=100)

        # Mock背压控制器
        with patch("miniboot.web.backpressure.controller.ConfigurableBackpressureController") as MockController:
            mock_controller = Mock()
            mock_controller.should_protect_request = AsyncMock()
            MockController.return_value = mock_controller

            controller = MockController(config)

            metrics = PerformanceMetrics()
            metrics.start_monitoring()

            # 模拟大量请求
            for i in range(1000):
                start_time = time.time()

                request_context = {"method": "GET", "url": f"/test/{i}", "timestamp": time.time()}

                await controller.should_protect_request(request_context)

                end_time = time.time()
                metrics.record_response_time(end_time - start_time)
                metrics.record_success()

                # 每100次记录系统指标
                if i % 100 == 0:
                    metrics.record_system_metrics()

            metrics.stop_monitoring()
            summary = metrics.get_summary()

            # 验证性能指标
            assert summary["avg_response_time"] < 0.001  # 平均决策时间小于1ms
            assert summary["throughput"] > 1000  # 吞吐量大于1000 decisions/s

    def test_backpressure_strategy_comparison(self):
        """测试不同背压策略的性能对比"""
        strategies = [BackpressureStrategy.NONE, BackpressureStrategy.SIMPLE, BackpressureStrategy.ADAPTIVE]

        results = {}

        for strategy in strategies:
            config = BackpressureConfig(enabled=True, strategy=strategy, max_concurrent_requests=50)

            metrics = PerformanceMetrics()
            metrics.start_monitoring()

            # 模拟请求处理
            for _ in range(500):
                start_time = time.time()

                # 模拟策略决策时间
                if strategy == BackpressureStrategy.NONE:
                    time.sleep(0.0001)  # 无策略，最快
                elif strategy == BackpressureStrategy.SIMPLE:
                    time.sleep(0.0002)  # 简单策略
                else:  # ADAPTIVE
                    time.sleep(0.0005)  # 自适应策略，稍慢但更智能

                end_time = time.time()
                metrics.record_response_time(end_time - start_time)
                metrics.record_success()

            metrics.stop_monitoring()
            results[strategy.value] = metrics.get_summary()

        # 验证策略性能差异
        assert results["none"]["avg_response_time"] < results["simple"]["avg_response_time"]
        assert results["simple"]["avg_response_time"] < results["adaptive"]["avg_response_time"]

        # 所有策略都应该有良好的吞吐量
        for strategy_result in results.values():
            assert strategy_result["throughput"] > 100


class TestSmartSchedulingPerformance:
    """智能调度性能测试"""

    @pytest.mark.asyncio
    async def test_smart_scheduler_performance(self):
        """测试智能调度器性能"""
        config = AsyncOptimizationConfig(enabled=True, intelligent_scheduling=True, max_workers=10, batch_size=50)

        scheduler = SmartTaskScheduler(config)
        await scheduler.start()

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 创建不同类型的任务
        async def light_task(value):
            return value * 2

        async def medium_task(value):
            await asyncio.sleep(0.001)
            return value**2

        def heavy_task(value):
            return sum(range(value))

        tasks = []
        task_types = [light_task, medium_task, heavy_task]

        # 创建大量任务
        for i in range(300):
            task_func = task_types[i % 3]
            task_request = TaskRequest(task_id=f"perf-task-{i}", function=task_func, args=(i % 100,), metadata={"type": task_func.__name__})

            start_time = time.time()
            task = scheduler.schedule_task(task_request)
            tasks.append((task, start_time))

        # 等待所有任务完成
        for task, start_time in tasks:
            try:
                await task
                end_time = time.time()
                metrics.record_response_time(end_time - start_time)
                metrics.record_success()
            except Exception:
                metrics.record_error()

        await scheduler.stop()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证性能指标
        assert summary["success_rate"] > 95.0  # 成功率大于95%
        assert summary["avg_response_time"] < 0.1  # 平均任务时间小于100ms
        assert summary["throughput"] > 100  # 吞吐量大于100 tasks/s

    @pytest.mark.asyncio
    async def test_batch_processing_performance(self):
        """测试批处理性能"""
        config = AsyncOptimizationConfig(enabled=True, adaptive_batching=True, batch_size=20)

        scheduler = SmartTaskScheduler(config)
        await scheduler.start()

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 创建批处理任务
        async def batch_task(values):
            # 模拟批处理操作
            await asyncio.sleep(0.01)
            return [v * 2 for v in values]

        # 测试批处理性能
        batch_tasks = []
        for i in range(10):
            values = list(range(i * 10, (i + 1) * 10))
            task_request = TaskRequest(task_id=f"batch-{i}", function=batch_task, args=(values,), metadata={"batch_eligible": True})

            start_time = time.time()
            task = scheduler.schedule_task(task_request)
            batch_tasks.append((task, start_time))

        # 等待批处理完成
        for task, start_time in batch_tasks:
            try:
                result = await task
                end_time = time.time()
                metrics.record_response_time(end_time - start_time)
                metrics.record_success()
                assert len(result) == 10  # 验证批处理结果
            except Exception:
                metrics.record_error()

        await scheduler.stop()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证批处理性能
        assert summary["success_rate"] == 100.0
        assert summary["avg_response_time"] < 0.5  # 批处理平均时间小于500ms


class TestConcurrentPerformance:
    """并发性能测试"""

    @pytest.mark.asyncio
    async def test_concurrent_request_handling(self):
        """测试并发请求处理性能"""
        properties = WebProperties()
        properties.async_optimization.enabled = True
        properties.async_optimization.max_workers = 20

        app = WebApplication(properties)
        await app.initialize()

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 创建并发任务
        async def concurrent_task(task_id):
            start_time = time.time()

            # 模拟请求处理
            await asyncio.sleep(0.01)
            status = app.get_status()

            end_time = time.time()
            return end_time - start_time, len(status)

        # 并发执行任务
        concurrent_tasks = []
        for i in range(100):
            task = concurrent_task(i)
            concurrent_tasks.append(task)

        # 等待所有并发任务完成
        results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)

        # 统计结果
        for result in results:
            if isinstance(result, tuple):
                response_time, status_count = result
                metrics.record_response_time(response_time)
                if status_count > 0:
                    metrics.record_success()
                else:
                    metrics.record_error()
            else:
                metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证并发性能
        assert summary["success_rate"] > 95.0
        assert summary["throughput"] > 50  # 并发吞吐量大于50 req/s

    def test_thread_pool_performance(self):
        """测试线程池性能"""

        def cpu_intensive_task(n):
            """CPU密集型任务"""
            return sum(range(n))

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 使用线程池执行CPU密集型任务
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = []

            for i in range(50):
                start_time = time.time()
                future = executor.submit(cpu_intensive_task, 1000)
                futures.append((future, start_time))

            # 等待所有任务完成
            for future, start_time in futures:
                try:
                    result = future.result(timeout=5.0)
                    end_time = time.time()
                    metrics.record_response_time(end_time - start_time)
                    if result == sum(range(1000)):
                        metrics.record_success()
                    else:
                        metrics.record_error()
                except Exception:
                    metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证线程池性能
        assert summary["success_rate"] == 100.0
        assert summary["avg_response_time"] < 1.0  # 平均任务时间小于1秒


class TestMemoryPerformance:
    """内存性能测试"""

    def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        properties = WebProperties()
        app = WebApplication(properties)

        # 记录初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 创建大量对象模拟负载
        objects = []
        for i in range(1000):
            # 创建应用状态快照
            status = app.get_status()
            objects.append(status)

            # 每100次检查内存使用
            if i % 100 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_increase = current_memory - initial_memory

                # 内存增长应该是合理的（小于100MB）
                assert memory_increase < 100

        # 清理对象
        objects.clear()

        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory

        # 总内存增长应该是合理的
        assert total_increase < 50  # 小于50MB


if __name__ == "__main__":
    pytest.main([__file__])
