"""
Mini-Boot Framework - Lightweight Python Web Framework

Mini-Boot is a lightweight Python web framework inspired by Spring Boot design principles,
providing IoC container, dependency injection, auto-configuration and other enterprise features.

Key Features:
- IoC container and dependency injection
- Annotation-based configuration
- Auto-configuration mechanism
- Web framework integration
- Monitoring and health checks
- Asynchronous processing
- Event system
- Scheduled tasks
"""

__version__ = "0.0.4"
__author__ = "Mini-Boot Team"
__email__ = "<EMAIL>"

# 智能异步支持(现在集成在DefaultApplicationContext中)
from .context import AsyncEnvironmentDetector, DefaultApplicationContext, RuntimeDetector, auto_context, create_application, with_context
from .runner import MiniBootRunner, run_application

# Note: Other imports will be added after module implementation
# Currently export core functionality and version info

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    # 智能异步支持(现在集成在DefaultApplicationContext中)
    "MiniBootRunner",
    "create_application",
    "run_application",
    "DefaultApplicationContext",  # 现在包含智能功能
    "RuntimeDetector",
    "AsyncEnvironmentDetector",  # Backward compatibility alias
    "auto_context",
    "with_context",
]
