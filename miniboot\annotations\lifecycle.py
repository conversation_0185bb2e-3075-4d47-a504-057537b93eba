#!/usr/bin/env python
"""
* @author: cz
* @description: 生命周期注解实现

实现Mini-Boot框架的Bean生命周期管理注解,包括@PostConstruct、@PreDestroy等.
这些注解用于标记Bean的初始化和销毁方法,支持Bean生命周期的精确控制.
"""

import inspect
from typing import Callable, Optional, Union

from .metadata import OrderMetadata, PostConstructMetadata, PreDestroyMetadata


def PostConstruct(  # noqa: N802
    func: Optional[Callable] = None,
) -> Union[Callable, Callable[[Callable], Callable]]:
    """初始化方法注解装饰器

    标记一个方法在Bean实例化和依赖注入完成后执行.
    该方法会在Bean的所有依赖注入完成后自动调用.

    Args:
        func: 被装饰的方法

    Returns:
        装饰后的方法或装饰器函数

    Examples:
        @Component
        class UserService:
            @PostConstruct
            def init(self):
                print("UserService initialized")
                self.setup_resources()
    """

    def decorator(method: Callable) -> Callable:
        # 创建初始化方法元数据
        metadata = PostConstructMetadata(method=method.__name__)

        # 存储元数据
        method.__post_construct_metadata__ = metadata
        method.__is_post_construct__ = True
        method.__post_construct_method__ = method.__name__

        return method

    # 支持无参数调用
    if func is not None:
        return decorator(func)

    return decorator


def PreDestroy(  # noqa: N802
    func: Optional[Callable] = None,
) -> Union[Callable, Callable[[Callable], Callable]]:
    """销毁方法注解装饰器

    标记一个方法在Bean销毁前执行.
    该方法会在容器关闭或Bean被销毁前自动调用,用于清理资源.

    Args:
        func: 被装饰的方法

    Returns:
        装饰后的方法或装饰器函数

    Examples:
        @Component
        class DatabaseService:
            @PreDestroy
            def cleanup(self):
                print("DatabaseService cleanup")
                self.close_connections()
    """

    def decorator(method: Callable) -> Callable:
        # 创建销毁方法元数据
        metadata = PreDestroyMetadata(method=method.__name__)

        # 存储元数据
        method.__pre_destroy_metadata__ = metadata
        method.__is_pre_destroy__ = True
        method.__pre_destroy_method__ = method.__name__

        return method

    # 支持无参数调用
    if func is not None:
        return decorator(func)

    return decorator


def Order(  # noqa: N802
    value: int = 0,
) -> Callable[[Union[type, Callable]], Union[type, Callable]]:
    """顺序注解装饰器

    指定Bean的加载顺序或方法的执行顺序.
    数值越小优先级越高,默认为0.

    Args:
        value: 顺序值,数值越小优先级越高

    Returns:
        装饰器函数

    Examples:
        @Component
        @Order(1)
        class HighPriorityService:
            pass

        @Component
        @Order(10)
        class LowPriorityService:
            pass

        class EventHandler:
            @Order(1)
            @EventListener
            def handle_first(self, event):
                pass

            @Order(2)
            @EventListener
            def handle_second(self, event):
                pass
    """

    def decorator(target: Union[type, Callable]) -> Union[type, Callable]:
        # 创建顺序元数据
        metadata = OrderMetadata(value=value)

        # 存储元数据
        target.__order_metadata__ = metadata
        target.__is_ordered__ = True
        target.__order_value__ = value

        return target

    return decorator


# 工具函数
def is_post_construct(method: Callable) -> bool:
    """检查方法是否有@PostConstruct注解

    Args:
        method: 要检查的方法

    Returns:
        如果有@PostConstruct注解返回True,否则返回False
    """
    return hasattr(method, "__is_post_construct__") and method.__is_post_construct__


def is_pre_destroy(method: Callable) -> bool:
    """检查方法是否有@PreDestroy注解

    Args:
        method: 要检查的方法

    Returns:
        如果有@PreDestroy注解返回True,否则返回False
    """
    return hasattr(method, "__is_pre_destroy__") and method.__is_pre_destroy__


def get_post_construct_metadata(method: Callable) -> Optional[PostConstructMetadata]:
    """获取@PostConstruct注解元数据

    Args:
        method: 要获取元数据的方法

    Returns:
        PostConstruct元数据,如果没有则返回None
    """
    return getattr(method, "__post_construct_metadata__", None)


def get_pre_destroy_metadata(method: Callable) -> Optional[PreDestroyMetadata]:
    """获取@PreDestroy注解元数据

    Args:
        method: 要获取元数据的方法

    Returns:
        PreDestroy元数据,如果没有则返回None
    """
    return getattr(method, "__pre_destroy_metadata__", None)


def get_post_construct_method_name(method: Callable) -> Optional[str]:
    """获取@PostConstruct方法名

    Args:
        method: 要获取方法名的方法

    Returns:
        方法名,如果没有@PostConstruct注解则返回None
    """
    return getattr(method, "__post_construct_method__", None)


def get_pre_destroy_method_name(method: Callable) -> Optional[str]:
    """获取@PreDestroy方法名

    Args:
        method: 要获取方法名的方法

    Returns:
        方法名,如果没有@PreDestroy注解则返回None
    """
    return getattr(method, "__pre_destroy_method__", None)


def is_ordered(target: Union[type, Callable]) -> bool:
    """检查类或方法是否有@Order注解

    Args:
        target: 要检查的类或方法

    Returns:
        如果有@Order注解返回True,否则返回False
    """
    return hasattr(target, "__is_ordered__") and target.__is_ordered__


def get_order_metadata(target: Union[type, Callable]) -> Optional[OrderMetadata]:
    """获取@Order注解元数据

    Args:
        target: 要获取元数据的类或方法

    Returns:
        Order元数据,如果没有则返回None
    """
    return getattr(target, "__order_metadata__", None)


def get_order_value(target: Union[type, Callable]) -> int:
    """获取@Order注解的顺序值

    Args:
        target: 要获取顺序值的类或方法

    Returns:
        顺序值,如果没有@Order注解则返回0
    """
    return getattr(target, "__order_value__", 0)


def find_lifecycle_methods(cls: type) -> tuple[list[str], list[str]]:
    """查找类中的生命周期方法

    Args:
        cls: 要查找的类

    Returns:
        包含(post_construct_methods, pre_destroy_methods)的元组
    """
    post_construct_methods = []
    pre_destroy_methods = []

    # 遍历类的所有方法
    for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
        if is_post_construct(method):
            post_construct_methods.append(name)
        elif is_pre_destroy(method):
            pre_destroy_methods.append(name)

    # 也检查实例方法
    for name in dir(cls):
        if not name.startswith("_"):  # 跳过私有方法
            try:
                attr = getattr(cls, name)
                if callable(attr):
                    if is_post_construct(attr):
                        if name not in post_construct_methods:
                            post_construct_methods.append(name)
                    elif is_pre_destroy(attr) and name not in pre_destroy_methods:
                        pre_destroy_methods.append(name)
            except (AttributeError, TypeError):
                # 忽略无法访问的属性
                continue

    return post_construct_methods, pre_destroy_methods


def has_lifecycle_methods(cls: type) -> bool:
    """检查类是否有生命周期方法

    Args:
        cls: 要检查的类

    Returns:
        如果有生命周期方法返回True,否则返回False
    """
    post_construct_methods, pre_destroy_methods = find_lifecycle_methods(cls)
    return len(post_construct_methods) > 0 or len(pre_destroy_methods) > 0



def sort_by_order(*targets: Union[type, Callable]) -> list[Union[type, Callable]]:
    """根据@Order注解对目标进行排序

    Args:
        *targets: 要排序的类或方法列表

    Returns:
        按顺序值排序的列表,顺序值小的在前
    """
    return sorted(targets, key=get_order_value)


def sort_instances_by_order(*instances: object) -> list[object]:
    """根据实例类的@Order注解对实例进行排序

    Args:
        *instances: 要排序的实例列表

    Returns:
        按顺序值排序的实例列表,顺序值小的在前
    """
    return sorted(instances, key=lambda instance: get_order_value(instance.__class__))


def get_ordered_methods(cls: type, method_filter: Optional[Callable[[Callable], bool]] = None) -> list[tuple[str, Callable]]:
    """获取类中按@Order注解排序的方法

    Args:
        cls: 要查找的类
        method_filter: 方法过滤器函数,用于筛选特定的方法

    Returns:
        按顺序值排序的(方法名, 方法)元组列表
    """
    methods = []

    # 遍历类的所有方法
    for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
        if method_filter is None or method_filter(method):
            methods.append((name, method))

    # 也检查实例方法
    for name in dir(cls):
        if not name.startswith("_"):  # 跳过私有方法
            try:
                attr = getattr(cls, name)
                if callable(attr) and (name, attr) not in methods and (method_filter is None or method_filter(attr)):
                    methods.append((name, attr))
            except (AttributeError, TypeError):
                # 忽略无法访问的属性
                continue

    # 按Order注解排序
    return sorted(methods, key=lambda item: get_order_value(item[1]))
