#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 线程转储端点

提供线程转储端点,支持线程信息收集.
"""

import threading
from datetime import datetime
from typing import Any, Dict

from miniboot.monitoring.interfaces import EndpointInfo, EndpointProvider

from .base import Endpoint, EndpointOperation, OperationType


class ThreadCollector:
    """线程收集器"""

    def collect_thread_dump(self) -> Dict[str, Any]:
        """收集线程转储信息"""
        import threading
        import time

        threads = []
        for thread in threading.enumerate():
            thread_info = {"id": thread.ident, "name": thread.name, "daemon": thread.daemon, "alive": thread.is_alive()}
            threads.append(thread_info)

        return {"timestamp": datetime.now().isoformat(), "total_threads": len(threads), "thread_dumps": threads}


class ThreadDumpEndpoint(Endpoint, EndpointProvider):
    """线程转储端点"""

    def __init__(self):
        super().__init__(endpoint_id="threaddump", enabled=True, sensitive=False)
        self.collector = ThreadCollector()

    def operations(self) -> list[EndpointOperation]:
        """返回端点支持的操作"""
        return [
            EndpointOperation(
                operation_type=OperationType.READ,
                method="GET",
                path=""
            )
        ]

    def invoke(self, operation_type: OperationType, **kwargs) -> Any:
        """执行端点操作"""
        if operation_type == OperationType.READ:
            return self.threaddump_sync()
        else:
            raise ValueError(f"Unsupported operation type: {operation_type}")

    def threaddump_sync(self) -> Dict[str, Any]:
        """同步版本的线程转储方法"""
        return self.collector.collect_thread_dump()

    async def threaddump(self) -> Dict[str, Any]:
        """获取线程转储"""
        return self.collector.collect_thread_dump()

    # ==================== EndpointProvider 接口实现 ====================

    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息 - EndpointProvider 接口实现"""
        return EndpointInfo(
            name="threaddump",
            path="/threaddump",
            methods=["GET"],
            description="Thread dump endpoint",
            enabled=True,
            sensitive=False
        )

    async def handle_request(self, request: Any) -> Any:
        """处理请求 - EndpointProvider 接口实现"""
        return await self.threaddump()

    def is_enabled(self) -> bool:
        """检查端点是否启用 - EndpointProvider 接口实现"""
        return self.enabled
