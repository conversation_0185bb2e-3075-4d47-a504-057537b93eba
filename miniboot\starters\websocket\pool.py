#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 连接池管理
"""

import asyncio
import time
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Optional
from weakref import WeakSet

from loguru import logger

from miniboot.annotations import Component

from .exceptions import WebSocketConnectionException
from .session import WebSocketSession
import contextlib


class ConnectionState(Enum):
    """连接状态枚举"""

    IDLE = "IDLE"
    ACTIVE = "ACTIVE"
    CLOSED = "CLOSED"
    ERROR = "ERROR"


@dataclass
class PooledConnection:
    """池化连接对象"""

    session: WebSocketSession
    created_at: datetime = field(default_factory=datetime.now)
    last_used: datetime = field(default_factory=datetime.now)
    use_count: int = 0
    state: ConnectionState = ConnectionState.IDLE

    def mark_used(self) -> None:
        """标记连接被使用"""
        self.last_used = datetime.now()
        self.use_count += 1
        self.state = ConnectionState.ACTIVE

    def mark_idle(self) -> None:
        """标记连接为空闲"""
        self.last_used = datetime.now()
        self.state = ConnectionState.IDLE

    def mark_closed(self) -> None:
        """标记连接已关闭"""
        self.state = ConnectionState.CLOSED

    def mark_error(self) -> None:
        """标记连接出错"""
        self.state = ConnectionState.ERROR

    def is_expired(self, max_idle_time: timedelta) -> bool:
        """检查连接是否过期

        Args:
            max_idle_time: 最大空闲时间

        Returns:
            bool: 是否过期
        """
        return datetime.now() - self.last_used > max_idle_time

    def is_available(self) -> bool:
        """检查连接是否可用

        Returns:
            bool: 是否可用
        """
        return self.state == ConnectionState.IDLE and self.session.is_active()


@dataclass
class PoolConfiguration:
    """连接池配置"""

    min_connections: int = 5
    max_connections: int = 50
    max_idle_time_seconds: int = 300  # 5分钟
    max_connection_age_seconds: int = 3600  # 1小时
    connection_timeout_seconds: int = 30
    validation_interval_seconds: int = 60
    enable_connection_validation: bool = True
    enable_connection_recycling: bool = True


@Component
class WebSocketConnectionPool:
    """WebSocket 连接池

    提供高效的 WebSocket 连接管理,支持连接复用、自动清理、
    负载均衡等企业级功能.
    """

    def __init__(self, config: Optional[PoolConfiguration] = None):
        """初始化连接池

        Args:
            config: 连接池配置
        """
        self.config = config or PoolConfiguration()

        # 连接池存储
        self._idle_connections: deque[PooledConnection] = deque()
        self._active_connections: dict[str, PooledConnection] = {}
        self._all_connections: WeakSet[PooledConnection] = WeakSet()

        # 同步锁
        self._lock = asyncio.Lock()

        # 统计信息
        self._stats = {
            "created_connections": 0,
            "destroyed_connections": 0,
            "borrowed_connections": 0,
            "returned_connections": 0,
            "validation_failures": 0,
            "pool_hits": 0,
            "pool_misses": 0,
        }

        # 后台任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._validation_task: Optional[asyncio.Task] = None
        self._running = False

        logger.debug("WebSocketConnectionPool initialized")

    async def start(self) -> None:
        """启动连接池"""
        if self._running:
            return

        self._running = True

        # 启动后台清理任务
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())

        # 启动连接验证任务
        if self.config.enable_connection_validation:
            self._validation_task = asyncio.create_task(self._validation_loop())

        logger.info("WebSocketConnectionPool started")

    async def stop(self) -> None:
        """停止连接池"""
        if not self._running:
            return

        self._running = False

        # 取消后台任务
        if self._cleanup_task:
            self._cleanup_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._cleanup_task

        if self._validation_task:
            self._validation_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._validation_task

        # 关闭所有连接
        await self._close_all_connections()

        logger.info("WebSocketConnectionPool stopped")

    async def get_connection(self, timeout: Optional[float] = None) -> PooledConnection:
        """获取连接

        Args:
            timeout: 获取超时时间(秒)

        Returns:
            PooledConnection: 池化连接对象

        Raises:
            WebSocketConnectionException: 获取连接失败
        """
        timeout = timeout or self.config.connection_timeout_seconds
        start_time = time.time()

        while time.time() - start_time < timeout:
            async with self._lock:
                # 尝试从空闲连接中获取
                connection = await self._get_idle_connection()
                if connection:
                    self._stats["pool_hits"] += 1
                    self._stats["borrowed_connections"] += 1
                    return connection

                # 检查是否可以创建新连接
                if self._can_create_connection():
                    connection = await self._create_connection()
                    if connection:
                        self._stats["pool_misses"] += 1
                        self._stats["borrowed_connections"] += 1
                        return connection

            # 等待一段时间后重试
            await asyncio.sleep(0.1)

        raise WebSocketConnectionException(f"Failed to get connection within {timeout} seconds")

    async def return_connection(self, connection: PooledConnection) -> None:
        """归还连接

        Args:
            connection: 要归还的连接
        """
        async with self._lock:
            session_id = connection.session.get_id()

            # 从活跃连接中移除
            if session_id in self._active_connections:
                del self._active_connections[session_id]

            # 检查连接是否仍然有效
            if connection.is_available():
                connection.mark_idle()
                self._idle_connections.append(connection)
                self._stats["returned_connections"] += 1
                logger.debug(f"Connection {session_id} returned to pool")
            else:
                # 连接无效,销毁它
                await self._destroy_connection(connection)
                logger.debug(f"Invalid connection {session_id} destroyed")

    async def remove_connection(self, connection: PooledConnection) -> None:
        """移除连接

        Args:
            connection: 要移除的连接
        """
        async with self._lock:
            session_id = connection.session.get_id()

            # 从各个集合中移除
            if session_id in self._active_connections:
                del self._active_connections[session_id]

            # 从空闲连接中移除
            try:
                self._idle_connections.remove(connection)
            except ValueError:
                pass  # 连接不在空闲队列中

            # 销毁连接
            await self._destroy_connection(connection)

    async def _get_idle_connection(self) -> Optional[PooledConnection]:
        """从空闲连接中获取可用连接

        Returns:
            Optional[PooledConnection]: 可用连接或 None
        """
        while self._idle_connections:
            connection = self._idle_connections.popleft()

            # 检查连接是否仍然有效
            if connection.is_available():
                # 验证连接
                if await self._validate_connection(connection):
                    connection.mark_used()
                    self._active_connections[connection.session.get_id()] = connection
                    return connection
                else:
                    # 连接验证失败,销毁它
                    await self._destroy_connection(connection)
            else:
                # 连接无效,销毁它
                await self._destroy_connection(connection)

        return None

    def _can_create_connection(self) -> bool:
        """检查是否可以创建新连接

        Returns:
            bool: 是否可以创建
        """
        total_connections = len(self._active_connections) + len(self._idle_connections)
        return total_connections < self.config.max_connections

    async def _create_connection(self) -> Optional[PooledConnection]:
        """创建新连接

        Returns:
            Optional[PooledConnection]: 新创建的连接或 None
        """
        try:
            # 这里需要实际的 WebSocket 连接创建逻辑
            # 由于这是连接池,实际的连接创建应该由外部提供
            # 这里返回 None 表示无法直接创建连接
            logger.warning("Connection pool cannot create connections directly")
            return None

        except Exception as e:
            logger.error(f"Failed to create connection: {e}")
            return None

    async def _validate_connection(self, connection: PooledConnection) -> bool:
        """验证连接有效性

        Args:
            connection: 要验证的连接

        Returns:
            bool: 连接是否有效
        """
        try:
            # 检查连接是否活跃
            if not connection.session.is_active():
                return False

            # 检查连接是否过期
            max_idle_time = timedelta(seconds=self.config.max_idle_time_seconds)
            if connection.is_expired(max_idle_time):
                return False

            # 检查连接年龄
            max_age = timedelta(seconds=self.config.max_connection_age_seconds)
            if datetime.now() - connection.created_at > max_age:
                return False

            # 可以添加更多验证逻辑,如发送 ping 消息等

            return True

        except Exception as e:
            logger.error(f"Connection validation failed: {e}")
            self._stats["validation_failures"] += 1
            return False

    async def _destroy_connection(self, connection: PooledConnection) -> None:
        """销毁连接

        Args:
            connection: 要销毁的连接
        """
        try:
            connection.mark_closed()

            # 关闭 WebSocket 连接
            if connection.session.is_active():
                await connection.session.close()

            self._stats["destroyed_connections"] += 1
            logger.debug(f"Connection {connection.session.get_id()} destroyed")

        except Exception as e:
            logger.error(f"Error destroying connection: {e}")

    async def _cleanup_loop(self) -> None:
        """清理循环"""
        while self._running:
            try:
                await self._cleanup_expired_connections()
                await asyncio.sleep(self.config.validation_interval_seconds)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(5)  # 出错时等待5秒

    async def _validation_loop(self) -> None:
        """验证循环"""
        while self._running:
            try:
                await self._validate_idle_connections()
                await asyncio.sleep(self.config.validation_interval_seconds)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in validation loop: {e}")
                await asyncio.sleep(5)  # 出错时等待5秒

    async def _cleanup_expired_connections(self) -> None:
        """清理过期连接"""
        async with self._lock:
            max_idle_time = timedelta(seconds=self.config.max_idle_time_seconds)
            expired_connections = []

            # 检查空闲连接
            for connection in list(self._idle_connections):
                if connection.is_expired(max_idle_time):
                    expired_connections.append(connection)
                    self._idle_connections.remove(connection)

            # 销毁过期连接
            for connection in expired_connections:
                await self._destroy_connection(connection)

            if expired_connections:
                logger.info(f"Cleaned up {len(expired_connections)} expired connections")

    async def _validate_idle_connections(self) -> None:
        """验证空闲连接"""
        async with self._lock:
            invalid_connections = []

            # 验证所有空闲连接
            for connection in list(self._idle_connections):
                if not await self._validate_connection(connection):
                    invalid_connections.append(connection)
                    self._idle_connections.remove(connection)

            # 销毁无效连接
            for connection in invalid_connections:
                await self._destroy_connection(connection)

            if invalid_connections:
                logger.info(f"Removed {len(invalid_connections)} invalid connections")

    async def _close_all_connections(self) -> None:
        """关闭所有连接"""
        async with self._lock:
            # 关闭空闲连接
            while self._idle_connections:
                connection = self._idle_connections.popleft()
                await self._destroy_connection(connection)

            # 关闭活跃连接
            for connection in list(self._active_connections.values()):
                await self._destroy_connection(connection)

            self._active_connections.clear()

    def get_pool_stats(self) -> dict[str, Any]:
        """获取连接池统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "idle_connections": len(self._idle_connections),
            "active_connections": len(self._active_connections),
            "total_connections": len(self._idle_connections) + len(self._active_connections),
            "max_connections": self.config.max_connections,
            "min_connections": self.config.min_connections,
            "pool_utilization": (len(self._active_connections) / self.config.max_connections) * 100,
            "statistics": self._stats.copy(),
            "configuration": {
                "max_idle_time_seconds": self.config.max_idle_time_seconds,
                "max_connection_age_seconds": self.config.max_connection_age_seconds,
                "validation_enabled": self.config.enable_connection_validation,
                "recycling_enabled": self.config.enable_connection_recycling,
            },
        }

    async def warm_up(self, target_connections: Optional[int] = None) -> None:
        """预热连接池

        Args:
            target_connections: 目标连接数,默认为最小连接数
        """
        target = target_connections or self.config.min_connections
        current_total = len(self._idle_connections) + len(self._active_connections)

        if current_total >= target:
            return

        logger.info(f"Warming up connection pool to {target} connections")

        # 注意:实际的预热需要外部提供连接创建逻辑
        # 这里只是记录预热请求
        logger.warning("Connection pool warm-up requires external connection creation")

    def is_healthy(self) -> bool:
        """检查连接池是否健康

        Returns:
            bool: 连接池是否健康
        """
        total_connections = len(self._idle_connections) + len(self._active_connections)

        # 检查基本健康指标
        if total_connections == 0 and self.config.min_connections > 0:
            return False

        if total_connections > self.config.max_connections:
            return False

        # 检查错误率
        total_operations = self._stats["borrowed_connections"] + self._stats["returned_connections"]
        if total_operations > 0:
            error_rate = self._stats["validation_failures"] / total_operations
            if error_rate > 0.1:  # 错误率超过10%
                return False

        return True
