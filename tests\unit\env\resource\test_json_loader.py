#!/usr/bin/env python
"""
* @author: cz
* @description: JSON 属性源加载器测试
"""

import tempfile
import unittest
from pathlib import Path

from miniboot.env.resource.json_loader import JsonPropertySourceLoader
from miniboot.env.resource.loader import DefaultResourceLoader


class JsonPropertySourceLoaderTestCase(unittest.TestCase):
    """JSON 属性源加载器测试"""

    def setUp(self):
        self.loader = JsonPropertySourceLoader()
        self.resource_loader = DefaultResourceLoader()
        self.temp_dir = Path(tempfile.mkdtemp())

    def tearDown(self):
        # 清理临时文件
        for file in self.temp_dir.rglob("*"):
            if file.is_file():
                file.unlink()
        self.temp_dir.rmdir()

    def test_get_file_extensions(self):
        """测试获取支持的文件扩展名"""
        extensions = self.loader.get_file_extensions()
        self.assertEqual([".json"], extensions)

    def test_load_simple_json(self):
        """测试加载简单JSON文件"""
        json_content = """
        {
            "app": {
                "name": "test-app",
                "version": "1.0.0"
            },
            "server": {
                "port": 8080,
                "debug": true
            }
        }
        """

        # 创建临时JSON文件
        json_file = self.temp_dir / "test.json"
        json_file.write_text(json_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("test", self.resource_loader, json_file)

        self.assertEqual(1, len(property_sources))

        source = property_sources[0]
        self.assertEqual("test", source.name)

        # 验证展平后的属性
        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual("1.0.0", source.get_property("app.version"))
        self.assertEqual(8080, source.get_property("server.port"))
        self.assertEqual(True, source.get_property("server.debug"))

    def test_load_json_with_array(self):
        """测试加载包含数组的JSON文件"""
        json_content = """
        {
            "database": {
                "hosts": ["host1", "host2", "host3"],
                "config": {
                    "timeout": 30,
                    "retries": 3
                }
            },
            "features": [
                {"name": "auth", "enabled": true},
                {"name": "logging", "enabled": false}
            ]
        }
        """

        # 创建临时JSON文件
        json_file = self.temp_dir / "array_test.json"
        json_file.write_text(json_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("array_test", self.resource_loader, json_file)

        source = property_sources[0]

        # 验证数组属性
        self.assertEqual(["host1", "host2", "host3"], source.get_property("database.hosts"))
        self.assertEqual("host1", source.get_property("database.hosts[0]"))
        self.assertEqual("host2", source.get_property("database.hosts[1]"))
        self.assertEqual("host3", source.get_property("database.hosts[2]"))

        # 验证嵌套对象数组
        self.assertEqual("auth", source.get_property("features[0].name"))
        self.assertEqual(True, source.get_property("features[0].enabled"))
        self.assertEqual("logging", source.get_property("features[1].name"))
        self.assertEqual(False, source.get_property("features[1].enabled"))

    def test_load_empty_json(self):
        """测试加载空JSON文件"""
        json_content = "{}"

        # 创建临时JSON文件
        json_file = self.temp_dir / "empty.json"
        json_file.write_text(json_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("empty", self.resource_loader, json_file)

        self.assertEqual(1, len(property_sources))
        source = property_sources[0]
        self.assertEqual("empty", source.name)
        self.assertEqual({}, source._properties)

    def test_load_invalid_json(self):
        """测试加载无效JSON文件"""
        invalid_json = '{"invalid": json content}'

        # 创建临时JSON文件
        json_file = self.temp_dir / "invalid.json"
        json_file.write_text(invalid_json, encoding="utf-8")

        # 应该抛出异常
        from miniboot.env.resource.loader import PropertySourceLoadError

        with self.assertRaises(PropertySourceLoadError):
            self.loader.load("invalid", self.resource_loader, json_file)

    def test_load_json_with_null_values(self):
        """测试加载包含null值的JSON文件"""
        json_content = """
        {
            "app": {
                "name": "test-app",
                "description": null,
                "version": "1.0.0"
            }
        }
        """

        # 创建临时JSON文件
        json_file = self.temp_dir / "null_test.json"
        json_file.write_text(json_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("null_test", self.resource_loader, json_file)

        source = property_sources[0]
        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertIsNone(source.get_property("app.description"))
        self.assertEqual("1.0.0", source.get_property("app.version"))

    def test_flatten_dict_complex(self):
        """测试复杂字典展平功能"""
        complex_data = {
            "level1": {"level2": {"level3": "deep_value", "array": [1, 2, {"nested": "value"}]}, "simple": "simple_value"},
            "root_array": ["a", "b", "c"],
        }

        flattened = self.loader._flatten_dict(complex_data)

        # 验证展平结果
        self.assertEqual("deep_value", flattened["level1.level2.level3"])
        self.assertEqual("simple_value", flattened["level1.simple"])
        self.assertEqual([1, 2, {"nested": "value"}], flattened["level1.level2.array"])
        self.assertEqual(1, flattened["level1.level2.array[0]"])
        self.assertEqual(2, flattened["level1.level2.array[1]"])
        self.assertEqual("value", flattened["level1.level2.array[2].nested"])
        self.assertEqual(["a", "b", "c"], flattened["root_array"])
        self.assertEqual("a", flattened["root_array[0]"])


if __name__ == "__main__":
    unittest.main()
