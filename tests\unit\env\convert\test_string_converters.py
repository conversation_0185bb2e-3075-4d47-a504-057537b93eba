#!/usr/bin/env python
"""
* @author: cz
* @description: 字符串转换器测试
"""

import unittest

from miniboot.env.convert import ConversionError
from miniboot.env.convert.strings import StringToBooleanConverter, StringToFloatConverter, StringToIntConverter, StringToListConverter


class StringToBooleanConverterTestCase(unittest.TestCase):
    """字符串到布尔值转换器测试"""

    def setUp(self):
        self.converter = StringToBooleanConverter()

    def test_convert_true_values(self):
        """测试转换为 True 的值"""
        true_values = ["true", "True", "TRUE", "yes", "YES", "1", "on", "enabled"]
        for value in true_values:
            with self.subTest(value=value):
                self.assertTrue(self.converter.convert(value, bool))

    def test_convert_false_values(self):
        """测试转换为 False 的值"""
        false_values = ["false", "False", "FALSE", "no", "NO", "0", "off", "disabled"]
        for value in false_values:
            with self.subTest(value=value):
                self.assertFalse(self.converter.convert(value, bool))

    def test_convert_invalid_values(self):
        """测试无效值转换"""
        invalid_values = ["invalid", "maybe", "2", "unknown"]
        for value in invalid_values:
            with self.subTest(value=value), self.assertRaises(ConversionError):
                self.converter.convert(value, bool)

    def test_can_convert(self):
        """测试转换能力检查"""
        self.assertTrue(self.converter.can_convert(str, bool))
        self.assertFalse(self.converter.can_convert(int, bool))
        self.assertFalse(self.converter.can_convert(str, int))


class StringToIntConverterTestCase(unittest.TestCase):
    """字符串到整数转换器测试"""

    def setUp(self):
        self.converter = StringToIntConverter()

    def test_convert_decimal(self):
        """测试十进制转换"""
        self.assertEqual(123, self.converter.convert("123", int))
        self.assertEqual(-456, self.converter.convert("-456", int))
        self.assertEqual(0, self.converter.convert("0", int))

    def test_convert_hexadecimal(self):
        """测试十六进制转换"""
        self.assertEqual(16, self.converter.convert("0x10", int))
        self.assertEqual(255, self.converter.convert("0xFF", int))
        self.assertEqual(255, self.converter.convert("0xff", int))

    def test_convert_binary(self):
        """测试二进制转换"""
        self.assertEqual(5, self.converter.convert("0b101", int))
        self.assertEqual(7, self.converter.convert("0B111", int))

    def test_convert_octal(self):
        """测试八进制转换"""
        self.assertEqual(8, self.converter.convert("0o10", int))
        self.assertEqual(64, self.converter.convert("0O100", int))

    def test_convert_invalid_values(self):
        """测试无效值转换"""
        invalid_values = ["invalid", "12.34", "abc", ""]
        for value in invalid_values:
            with self.subTest(value=value), self.assertRaises(ConversionError):
                self.converter.convert(value, int)

    def test_can_convert(self):
        """测试转换能力检查"""
        self.assertTrue(self.converter.can_convert(str, int))
        self.assertFalse(self.converter.can_convert(int, str))
        self.assertFalse(self.converter.can_convert(str, float))


class StringToFloatConverterTestCase(unittest.TestCase):
    """字符串到浮点数转换器测试"""

    def setUp(self):
        self.converter = StringToFloatConverter()

    def test_convert_normal_float(self):
        """测试普通浮点数转换"""
        self.assertEqual(123.45, self.converter.convert("123.45", float))
        self.assertEqual(-67.89, self.converter.convert("-67.89", float))
        self.assertEqual(0.0, self.converter.convert("0.0", float))

    def test_convert_scientific_notation(self):
        """测试科学计数法转换"""
        self.assertEqual(1.23e-4, self.converter.convert("1.23e-4", float))
        self.assertEqual(1.5e10, self.converter.convert("1.5E10", float))

    def test_convert_integer_string(self):
        """测试整数字符串转换"""
        self.assertEqual(123.0, self.converter.convert("123", float))
        self.assertEqual(-456.0, self.converter.convert("-456", float))

    def test_convert_invalid_values(self):
        """测试无效值转换"""
        invalid_values = ["invalid", "abc", ""]
        for value in invalid_values:
            with self.subTest(value=value), self.assertRaises(ConversionError):
                self.converter.convert(value, float)

    def test_can_convert(self):
        """测试转换能力检查"""
        self.assertTrue(self.converter.can_convert(str, float))
        self.assertFalse(self.converter.can_convert(float, str))
        self.assertFalse(self.converter.can_convert(str, int))


class StringToListConverterTestCase(unittest.TestCase):
    """字符串到列表转换器测试"""

    def setUp(self):
        self.converter = StringToListConverter()

    def test_convert_comma_separated(self):
        """测试逗号分隔的字符串转换"""
        self.assertEqual(["a", "b", "c"], self.converter.convert("a,b,c", list))
        self.assertEqual(["1", "2", "3"], self.converter.convert("1, 2, 3", list))
        self.assertEqual(["item"], self.converter.convert("item", list))

    def test_convert_empty_string(self):
        """测试空字符串转换"""
        self.assertEqual([], self.converter.convert("", list))
        self.assertEqual([], self.converter.convert("   ", list))

    def test_convert_with_spaces(self):
        """测试包含空格的字符串转换"""
        self.assertEqual(["hello world", "test"], self.converter.convert("hello world, test", list))
        self.assertEqual(["a", "b", "c"], self.converter.convert(" a , b , c ", list))

    def test_can_convert(self):
        """测试转换能力检查"""
        self.assertTrue(self.converter.can_convert(str, list))
        self.assertFalse(self.converter.can_convert(list, str))
        self.assertFalse(self.converter.can_convert(str, dict))


if __name__ == "__main__":
    unittest.main()
