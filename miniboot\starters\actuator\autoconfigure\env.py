#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
环境配置模块指标采集自动配置

实现环境配置模块的指标采集功能，包括：
- 配置源统计：各配置源（文件、环境变量等）加载状态
- Profile 管理：当前激活的 Profile、切换统计
- 属性解析性能：配置属性解析耗时、缓存命中率
- 配置绑定统计：@ConfigurationProperties 绑定性能
- 环境变量监控：环境变量变更检测

配置条件：
- starters.actuator.metrics.core-modules.env=true (默认启用)
- 依赖 ActuatorStarterAutoConfiguration 已配置

使用示例：
    # application.yml
    starters:
        actuator:
            metrics:
                core-modules:
                    env: true  # 启用环境配置指标采集
"""

import threading
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Set

from loguru import logger

from miniboot.annotations.conditional import (ConditionalOnBean,
                                              ConditionalOnProperty)
from miniboot.annotations.base import Bean, Configuration
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.monitoring.interfaces import MetricsCollector, MetricsData


@dataclass
class EnvMetrics:
    """环境配置指标数据"""

    # 配置源统计
    total_property_sources: int = 0
    active_property_sources: int = 0
    property_source_types: Dict[str, int] = field(default_factory=dict)
    property_source_priorities: Dict[str, int] = field(default_factory=dict)

    # 配置文件加载统计
    total_config_files: int = 0
    loaded_config_files: int = 0
    failed_config_files: int = 0
    config_load_time: float = 0.0
    config_file_sizes: Dict[str, int] = field(default_factory=dict)

    # Profile 管理统计
    active_profiles: List[str] = field(default_factory=list)
    default_profiles: List[str] = field(default_factory=list)
    profile_switches: int = 0
    profile_specific_configs: int = 0

    # 属性解析性能
    total_property_requests: int = 0
    property_cache_hits: int = 0
    property_cache_misses: int = 0
    property_resolution_time: float = 0.0
    placeholder_resolutions: int = 0

    # 配置绑定统计
    configuration_properties_bindings: int = 0
    binding_successes: int = 0
    binding_failures: int = 0
    binding_time: float = 0.0

    # 环境变量监控
    environment_variables_count: int = 0
    environment_variable_overrides: int = 0
    system_property_overrides: int = 0
    command_line_overrides: int = 0

    # 配置刷新性能
    refresh_count: int = 0
    refresh_time: float = 0.0
    refresh_failures: int = 0
    last_refresh_time: Optional[float] = None

    # 错误统计
    configuration_errors: int = 0
    property_resolution_errors: int = 0
    binding_errors: int = 0

    def calculate_derived_metrics(self):
        """计算派生指标"""
        # 计算配置文件加载成功率
        if self.total_config_files > 0:
            self.config_load_success_rate = self.loaded_config_files / self.total_config_files
        else:
            self.config_load_success_rate = 0.0

        # 计算属性缓存命中率
        total_requests = self.property_cache_hits + self.property_cache_misses
        if total_requests > 0:
            self.property_cache_hit_rate = self.property_cache_hits / total_requests
        else:
            self.property_cache_hit_rate = 0.0

        # 计算配置绑定成功率
        total_bindings = self.binding_successes + self.binding_failures
        if total_bindings > 0:
            self.binding_success_rate = self.binding_successes / total_bindings
        else:
            self.binding_success_rate = 0.0

        # 计算平均配置加载时间
        if self.loaded_config_files > 0:
            self.avg_config_load_time = self.config_load_time / self.loaded_config_files
        else:
            self.avg_config_load_time = 0.0


class EnvMetricsCollector(MetricsCollector):
    """环境配置指标采集器

    负责收集环境配置模块的各种性能指标和状态信息。
    通过监控配置加载、属性解析、Profile管理等过程，
    提供详细的环境配置性能分析数据。
    """

    def __init__(self):
        """初始化环境配置指标采集器"""
        self._metrics = EnvMetrics()
        self._lock = threading.RLock()
        self._start_time = time.time()

        # 监控的环境实例
        self._monitored_environments: List[Any] = []

        # 监控的配置加载器
        self._monitored_config_loaders: List[Any] = []

        # 监控的属性源管理器
        self._monitored_property_sources: List[Any] = []

        # 监控的配置绑定器
        self._monitored_binders: List[Any] = []

        logger.info("EnvMetricsCollector initialized")

    def register_environment(self, environment: Any) -> None:
        """注册要监控的环境实例

        Args:
            environment: 环境实例 (StandardEnvironment)
        """
        with self._lock:
            if environment not in self._monitored_environments:
                self._monitored_environments.append(environment)
                logger.debug(f"Registered environment for monitoring: {environment}")

    def register_config_loader(self, loader: Any) -> None:
        """注册要监控的配置加载器

        Args:
            loader: 配置加载器实例 (ConfigurationLoader)
        """
        with self._lock:
            if loader not in self._monitored_config_loaders:
                self._monitored_config_loaders.append(loader)
                logger.debug(f"Registered config loader for monitoring: {loader}")

    def register_property_sources(self, property_sources: Any) -> None:
        """注册要监控的属性源管理器

        Args:
            property_sources: 属性源管理器实例 (MutablePropertySources)
        """
        with self._lock:
            if property_sources not in self._monitored_property_sources:
                self._monitored_property_sources.append(property_sources)
                logger.debug(f"Registered property sources for monitoring: {property_sources}")

    def register_binder(self, binder: Any) -> None:
        """注册要监控的配置绑定器

        Args:
            binder: 配置绑定器实例 (Binder)
        """
        with self._lock:
            if binder not in self._monitored_binders:
                self._monitored_binders.append(binder)
                logger.debug(f"Registered binder for monitoring: {binder}")

    async def collect_metrics_async(self) -> EnvMetrics:
        """收集当前的环境配置指标

        Returns:
            EnvMetrics: 当前的指标数据
        """
        import asyncio

        # 使用同步锁保护的内部函数
        def _collect_with_lock():
            with self._lock:
                # 重置指标
                metrics = EnvMetrics()

                # 收集环境指标
                for environment in self._monitored_environments:
                    self._collect_environment_metrics(environment, metrics)

                # 收集配置加载器指标
                for loader in self._monitored_config_loaders:
                    self._collect_config_loader_metrics(loader, metrics)

                # 收集属性源指标
                for property_sources in self._monitored_property_sources:
                    self._collect_property_sources_metrics(property_sources, metrics)

                # 收集绑定器指标
                for binder in self._monitored_binders:
                    self._collect_binder_metrics(binder, metrics)

                # 计算派生指标
                metrics.calculate_derived_metrics()

                # 更新内部指标
                self._metrics = metrics

                logger.debug(f"Collected env metrics: {metrics}")
                return metrics

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _collect_with_lock)

    def _collect_environment_metrics(self, environment: Any, metrics: EnvMetrics) -> None:
        """收集环境指标"""
        try:
            # 获取激活的Profile
            if hasattr(environment, 'get_active_profiles'):
                metrics.active_profiles = environment.get_active_profiles()

            # 获取默认Profile
            if hasattr(environment, 'get_default_profiles'):
                metrics.default_profiles = environment.get_default_profiles()

            # 获取属性源信息
            if hasattr(environment, 'get_property_sources'):
                property_sources = environment.get_property_sources()
                if hasattr(property_sources, '_property_sources'):
                    metrics.total_property_sources = len(property_sources._property_sources)
                    metrics.active_property_sources = len([ps for ps in property_sources._property_sources if ps])

                    # 统计属性源类型
                    for ps in property_sources._property_sources:
                        if ps:
                            ps_type = type(ps).__name__
                            metrics.property_source_types[ps_type] = metrics.property_source_types.get(ps_type, 0) + 1
                            if hasattr(ps, 'priority'):
                                metrics.property_source_priorities[ps.name] = ps.priority

            # 统计环境变量
            import os
            metrics.environment_variables_count = len(os.environ)

        except Exception as e:
            logger.debug(f"Failed to collect environment metrics: {e}")
            metrics.configuration_errors += 1

    def _collect_config_loader_metrics(self, loader: Any, metrics: EnvMetrics) -> None:
        """收集配置加载器指标"""
        try:
            # 获取加载统计信息
            if hasattr(loader, '_load_stats'):
                stats = loader._load_stats
                metrics.total_config_files += stats.get('total_files', 0)
                metrics.loaded_config_files += stats.get('loaded_files', 0)
                metrics.failed_config_files += stats.get('failed_files', 0)
                metrics.config_load_time += stats.get('load_time', 0.0)

            # 获取缓存统计
            if hasattr(loader, '_cache_stats'):
                cache_stats = loader._cache_stats
                metrics.property_cache_hits += cache_stats.get('hits', 0)
                metrics.property_cache_misses += cache_stats.get('misses', 0)

        except Exception as e:
            logger.debug(f"Failed to collect config loader metrics: {e}")

    def _collect_property_sources_metrics(self, property_sources: Any, metrics: EnvMetrics) -> None:
        """收集属性源指标"""
        try:
            if hasattr(property_sources, '_property_sources'):
                # 统计不同类型的属性源
                for ps in property_sources._property_sources:
                    if ps:
                        ps_type = type(ps).__name__
                        ps_name = getattr(ps, 'name', '')
                        if 'Profile' in ps_type or 'profile' in ps_name.lower():
                            metrics.profile_specific_configs += 1

                        # 统计覆盖情况
                        if 'Environment' in ps_type:
                            metrics.environment_variable_overrides += 1
                        elif 'System' in ps_type:
                            metrics.system_property_overrides += 1
                        elif 'CommandLine' in ps_type:
                            metrics.command_line_overrides += 1

        except Exception as e:
            logger.debug(f"Failed to collect property sources metrics: {e}")

    def _collect_binder_metrics(self, binder: Any, metrics: EnvMetrics) -> None:
        """收集绑定器指标"""
        try:
            # 获取绑定统计
            if hasattr(binder, '_binding_stats'):
                binding_stats = binder._binding_stats
                metrics.configuration_properties_bindings += binding_stats.get('total_bindings', 0)
                metrics.binding_successes += binding_stats.get('successful_bindings', 0)
                metrics.binding_failures += binding_stats.get('failed_bindings', 0)
                metrics.binding_time += binding_stats.get('binding_time', 0.0)

        except Exception as e:
            logger.debug(f"Failed to collect binder metrics: {e}")
            metrics.binding_errors += 1

    async def get_metrics_dict(self) -> Dict[str, Any]:
        """获取指标的字典表示

        Returns:
            Dict[str, Any]: 指标字典
        """
        metrics = await self.collect_metrics_async()
        return {
            'configuration_sources': {
                'total_property_sources': metrics.total_property_sources,
                'active_property_sources': metrics.active_property_sources,
                'property_source_types': metrics.property_source_types,
                'property_source_priorities': metrics.property_source_priorities,
            },
            'configuration_loading': {
                'total_config_files': metrics.total_config_files,
                'loaded_config_files': metrics.loaded_config_files,
                'failed_config_files': metrics.failed_config_files,
                'config_load_time': metrics.config_load_time,
                'config_load_success_rate': getattr(metrics, 'config_load_success_rate', 0.0),
                'avg_config_load_time': getattr(metrics, 'avg_config_load_time', 0.0),
            },
            'profile_management': {
                'active_profiles': metrics.active_profiles,
                'default_profiles': metrics.default_profiles,
                'profile_switches': metrics.profile_switches,
                'profile_specific_configs': metrics.profile_specific_configs,
            },
            'property_resolution': {
                'total_property_requests': metrics.total_property_requests,
                'property_cache_hits': metrics.property_cache_hits,
                'property_cache_misses': metrics.property_cache_misses,
                'property_cache_hit_rate': getattr(metrics, 'property_cache_hit_rate', 0.0),
                'property_resolution_time': metrics.property_resolution_time,
                'placeholder_resolutions': metrics.placeholder_resolutions,
            },
            'configuration_binding': {
                'configuration_properties_bindings': metrics.configuration_properties_bindings,
                'binding_successes': metrics.binding_successes,
                'binding_failures': metrics.binding_failures,
                'binding_success_rate': getattr(metrics, 'binding_success_rate', 0.0),
                'binding_time': metrics.binding_time,
            },
            'environment_monitoring': {
                'environment_variables_count': metrics.environment_variables_count,
                'environment_variable_overrides': metrics.environment_variable_overrides,
                'system_property_overrides': metrics.system_property_overrides,
                'command_line_overrides': metrics.command_line_overrides,
            },
            'configuration_refresh': {
                'refresh_count': metrics.refresh_count,
                'refresh_time': metrics.refresh_time,
                'refresh_failures': metrics.refresh_failures,
                'last_refresh_time': metrics.last_refresh_time,
            },
            'error_statistics': {
                'configuration_errors': metrics.configuration_errors,
                'property_resolution_errors': metrics.property_resolution_errors,
                'binding_errors': metrics.binding_errors,
            }
        }

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self._lock:
            self._metrics = EnvMetrics()
            self._start_time = time.time()
            logger.info("Env metrics reset")

    # MetricsCollector 接口实现方法
    def collect_metrics(self) -> List[MetricsData]:
        """收集指标数据 - MetricsCollector 接口实现"""
        import asyncio
        import time

        # 简化的方法：直接调用异步方法并处理事件循环
        try:
            # 检查是否有运行中的事件循环
            loop = asyncio.get_running_loop()
            # 如果有，我们需要在新线程中运行
            import concurrent.futures

            def run_in_thread():
                return asyncio.run(self.collect_metrics_async())

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                metrics = future.result(timeout=5.0)

        except RuntimeError:
            # 没有运行中的事件循环，直接运行
            metrics = asyncio.run(self.collect_metrics_async())
        except Exception as e:
            # 如果还是失败，使用同步回退
            import logging
            logging.warning(f"Failed to collect env metrics async: {e}, using fallback")
            metrics = self._get_fallback_metrics()

        # Convert EnvMetrics to MetricsData list
        metrics_data = []
        metrics_data.append(MetricsData(
            name="env.config_files_loaded",
            value=metrics.loaded_config_files,
            unit="count",
            tags={"module": "env"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="env.config_files_failed",
            value=metrics.failed_config_files,
            unit="count",
            tags={"module": "env"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="env.total_properties",
            value=metrics.total_property_requests,
            unit="count",
            tags={"module": "env"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="env.active_profiles",
            value=len(metrics.active_profiles),
            unit="count",
            tags={"module": "env"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="env.property_cache_hits",
            value=metrics.property_cache_hits,
            unit="count",
            tags={"module": "env"},
            timestamp=time.time()
        ))
        metrics_data.append(MetricsData(
            name="env.property_cache_misses",
            value=metrics.property_cache_misses,
            unit="count",
            tags={"module": "env"},
            timestamp=time.time()
        ))
        return metrics_data

    def get_collector_name(self) -> str:
        """获取收集器名称 - MetricsCollector 接口实现"""
        return "EnvMetricsCollector"

    def get_supported_metrics(self) -> List[str]:
        """获取支持的指标列表 - MetricsCollector 接口实现"""
        return [
            "env.config_files_loaded",
            "env.config_files_failed",
            "env.total_properties",
            "env.active_profiles",
            "env.property_cache_hits",
            "env.property_cache_misses"
        ]

    def is_available(self) -> bool:
        """检查收集器是否可用 - MetricsCollector 接口实现"""
        # Env 指标收集器总是可用的，即使没有监控特定环境
        # 它可以提供基本的环境统计信息
        return True


@ConditionalOnProperty(name="miniboot.starters.actuator.metrics.core-modules.env", match_if_missing=True)
@ConditionalOnBean(name="actuator_context")
class EnvMetricsAutoConfiguration(AutoConfiguration):
    """环境配置模块指标采集自动配置类

    当满足以下条件时自动配置环境配置指标采集：
    1. starters.actuator.metrics.core-modules.env=true (默认启用)
    2. ActuatorStarterAutoConfiguration 已配置

    注册的 Bean：
    - env_metrics_collector: 环境配置指标采集器
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="env-metrics-auto-configuration",
            description="环境配置模块指标采集自动配置",
            priority=200,  # 中等优先级
            auto_configure_after=["actuator-starter-auto-configuration"],
        )

    @Bean
    def env_metrics_collector(self) -> EnvMetricsCollector:
        """创建环境配置指标采集器 Bean

        Returns:
            EnvMetricsCollector: 环境配置指标采集器实例
        """
        collector = EnvMetricsCollector()
        logger.debug("Created EnvMetricsCollector bean")
        return collector
