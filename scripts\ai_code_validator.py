#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: AI代码生成验证器 - 防止生成有缺陷的代码
"""

import ast
import sys
from pathlib import Path
from typing import Dict, List, Set, Optional, Any
import importlib.util
import inspect
from loguru import logger


class CodeAnalyzer:
    """代码分析器 - 分析现有代码结构"""
    
    def __init__(self, project_root: Optional[Path] = None):
        """初始化代码分析器
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = project_root or Path(__file__).parent.parent
        
    def analyze_class(self, class_name: str, module_path: str) -> Dict[str, Any]:
        """分析指定类的完整结构
        
        Args:
            class_name: 类名
            module_path: 模块路径
            
        Returns:
            类的完整结构信息
        """
        try:
            # 读取模块文件
            module_file = self.project_root / module_path
            if not module_file.exists():
                return {"error": f"Module file not found: {module_path}"}
            
            # 解析AST
            with open(module_file, 'r', encoding='utf-8') as f:
                tree = ast.parse(f.read())
            
            # 查找目标类
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef) and node.name == class_name:
                    return self._extract_class_info(node)
            
            return {"error": f"Class {class_name} not found in {module_path}"}
            
        except Exception as e:
            return {"error": f"Failed to analyze class: {e}"}
    
    def _extract_class_info(self, class_node: ast.ClassDef) -> Dict[str, Any]:
        """提取类的详细信息
        
        Args:
            class_node: AST类节点
            
        Returns:
            类的详细信息
        """
        methods = []
        properties = []
        
        for node in class_node.body:
            if isinstance(node, ast.FunctionDef):
                method_info = {
                    "name": node.name,
                    "args": [arg.arg for arg in node.args.args],
                    "returns": ast.unparse(node.returns) if node.returns else None,
                    "is_async": isinstance(node, ast.AsyncFunctionDef),
                    "is_private": node.name.startswith('_'),
                    "docstring": ast.get_docstring(node)
                }
                methods.append(method_info)
            elif isinstance(node, ast.AnnAssign) and isinstance(node.target, ast.Name):
                properties.append({
                    "name": node.target.id,
                    "type": ast.unparse(node.annotation) if node.annotation else None
                })
        
        return {
            "name": class_node.name,
            "methods": methods,
            "properties": properties,
            "base_classes": [ast.unparse(base) for base in class_node.bases],
            "docstring": ast.get_docstring(class_node)
        }


class InterfaceChecker:
    """接口完整性检查器"""
    
    def __init__(self, analyzer: CodeAnalyzer):
        """初始化接口检查器
        
        Args:
            analyzer: 代码分析器实例
        """
        self.analyzer = analyzer
    
    def check_class_completeness(self, class_name: str, module_path: str) -> Dict[str, Any]:
        """检查类的接口完整性
        
        Args:
            class_name: 类名
            module_path: 模块路径
            
        Returns:
            完整性检查结果
        """
        class_info = self.analyzer.analyze_class(class_name, module_path)
        
        if "error" in class_info:
            return class_info
        
        issues = []
        warnings = []
        
        # 检查方法完整性
        for method in class_info["methods"]:
            # 检查公共方法是否有文档字符串
            if not method["is_private"] and not method["docstring"]:
                issues.append(f"Public method '{method['name']}' missing docstring")
            
            # 检查方法是否有返回类型注解
            if not method["returns"] and method["name"] != "__init__":
                warnings.append(f"Method '{method['name']}' missing return type annotation")
        
        return {
            "class_name": class_name,
            "total_methods": len(class_info["methods"]),
            "public_methods": len([m for m in class_info["methods"] if not m["is_private"]]),
            "issues": issues,
            "warnings": warnings,
            "is_complete": len(issues) == 0
        }


class DependencyChecker:
    """依赖关系检查器"""
    
    def __init__(self, project_root: Optional[Path] = None):
        """初始化依赖检查器
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = project_root or Path(__file__).parent.parent
    
    def verify_method_calls(self, file_path: str) -> Dict[str, Any]:
        """验证文件中所有方法调用的有效性
        
        Args:
            file_path: 要检查的文件路径
            
        Returns:
            验证结果
        """
        try:
            file_full_path = self.project_root / file_path
            if not file_full_path.exists():
                return {"error": f"File not found: {file_path}"}
            
            with open(file_full_path, 'r', encoding='utf-8') as f:
                tree = ast.parse(f.read())
            
            method_calls = []
            missing_methods = []
            
            # 收集所有方法调用
            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Attribute):
                        method_calls.append({
                            "object": ast.unparse(node.func.value) if hasattr(ast, 'unparse') else "unknown",
                            "method": node.func.attr,
                            "line": node.lineno
                        })
            
            return {
                "file_path": file_path,
                "total_method_calls": len(method_calls),
                "method_calls": method_calls,
                "missing_methods": missing_methods,
                "is_valid": len(missing_methods) == 0
            }
            
        except Exception as e:
            return {"error": f"Failed to verify method calls: {e}"}


class AICodeValidator:
    """AI代码生成验证器主类"""
    
    def __init__(self):
        """初始化验证器"""
        self.analyzer = CodeAnalyzer()
        self.interface_checker = InterfaceChecker(self.analyzer)
        self.dependency_checker = DependencyChecker()
    
    def pre_generation_check(self, target_module: str) -> Dict[str, Any]:
        """代码生成前检查
        
        Args:
            target_module: 目标模块名
            
        Returns:
            检查结果
        """
        logger.info(f"🔍 执行代码生成前检查: {target_module}")
        
        results = {
            "module": target_module,
            "timestamp": str(Path(__file__).stat().st_mtime),
            "checks": {},
            "overall_status": "unknown"
        }
        
        # 这里可以添加更多的预检查逻辑
        results["checks"]["module_exists"] = {"status": "pass", "message": "Module analysis completed"}
        results["overall_status"] = "ready_for_generation"
        
        return results
    
    def post_generation_check(self, generated_file: str) -> Dict[str, Any]:
        """代码生成后检查
        
        Args:
            generated_file: 生成的文件路径
            
        Returns:
            检查结果
        """
        logger.info(f"🔍 执行代码生成后检查: {generated_file}")
        
        results = {
            "file": generated_file,
            "timestamp": str(Path(__file__).stat().st_mtime),
            "checks": {},
            "overall_status": "unknown"
        }
        
        # 依赖关系检查
        dependency_result = self.dependency_checker.verify_method_calls(generated_file)
        results["checks"]["dependencies"] = dependency_result
        
        # 综合评估
        if dependency_result.get("is_valid", False):
            results["overall_status"] = "validation_passed"
        else:
            results["overall_status"] = "validation_failed"
        
        return results


def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法:")
        print("  uv run python scripts/ai_code_validator.py --pre-check module_name")
        print("  uv run python scripts/ai_code_validator.py --post-check file_path")
        sys.exit(1)
    
    command = sys.argv[1]
    target = sys.argv[2]
    
    validator = AICodeValidator()
    
    if command == "--pre-check":
        result = validator.pre_generation_check(target)
        print(f"📋 代码生成前检查结果:")
        print(f"  模块: {result['module']}")
        print(f"  状态: {result['overall_status']}")
        
        if result['overall_status'] != "ready_for_generation":
            print("❌ 检查失败，不允许生成代码")
            sys.exit(1)
        else:
            print("✅ 检查通过，可以生成代码")
    
    elif command == "--post-check":
        result = validator.post_generation_check(target)
        print(f"📋 代码生成后检查结果:")
        print(f"  文件: {result['file']}")
        print(f"  状态: {result['overall_status']}")
        
        if result['overall_status'] != "validation_passed":
            print("❌ 验证失败，生成的代码存在问题")
            sys.exit(1)
        else:
            print("✅ 验证通过，代码质量良好")
    
    else:
        print(f"❌ 未知命令: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
