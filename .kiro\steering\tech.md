# 技术栈和构建系统

## 核心技术

-   **Python**: 3.9+ (支持 3.9, 3.10, 3.11, 3.12)
-   **包管理器**: uv (现代 Python 包管理器)
-   **构建系统**: Hatchling
-   **Web 框架**: FastAPI 集成
-   **异步运行时**: asyncio 配合 uvicorn
-   **任务调度**: APScheduler 配合 croniter
-   **配置管理**: PyYAML 用于 YAML 配置文件
-   **日志系统**: Loguru 结构化日志
-   **HTTP 客户端**: httpx 异步 HTTP 请求
-   **系统监控**: psutil 系统指标收集

## 开发工具

-   **代码质量**: Ruff (代码检查、格式化、导入排序)
-   **测试框架**: pytest 配合 pytest-asyncio 和 pytest-cov
-   **类型检查**: mypy 严格配置
-   **覆盖率**: coverage.py 生成 HTML 报告

## 配置文件

-   `pyproject.toml` - 项目元数据、依赖和工具配置
-   `ruff.toml` - 代码质量规则 (行长度: 150, 目标: py39)
-   `pytest.ini` - 测试配置和覆盖率报告
-   `uv.lock` - 依赖锁定文件

## 常用命令

### 开发环境设置

```bash
# 安装依赖
uv sync

# 安装开发依赖
uv sync --group dev
```

### 代码质量

```bash
# 格式化代码
uv run ruff format .

# 检查代码风格
uv run ruff check .

# 修复可自动修复的问题
uv run ruff check --fix .

# 运行类型检查
uv run mypy miniboot/
```

### 测试

```bash
# 运行所有测试
uv run pytest

# 运行带覆盖率的测试
uv run pytest --cov=miniboot --cov-report=html

# 运行特定标记的测试
uv run pytest -m "not slow"
uv run pytest -m integration
```

### 构建和发布

```bash
# 构建包
uv build

# 运行质量检查和测试
uv run python tests/test_runner.py

# 版本管理
uv run scripts/version.py --show
uv run scripts/version.py --bump patch
```

## 代码质量标准

-   **行长度**: 150 字符 (在 ruff.toml 中配置)
-   **导入风格**: isort 配置 known-first-party = ["miniboot"]
-   **引号风格**: 优先使用双引号
-   **编码**: UTF-8 显式声明
-   **文件头**: 必需的 shebang 和编码声明
-   **中文注释**: 允许但必须使用英文标点符号
