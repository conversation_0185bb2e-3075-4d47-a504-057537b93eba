#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 优化配置加载器测试
"""

import tempfile
import unittest
from pathlib import Path

from miniboot.env import (
    ConfigurationLoader,
    ConfigurationPriority,
    MutablePropertySources,
)


class OptimizedConfigurationLoaderTestCase(unittest.TestCase):
    """优化配置加载器测试"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)

        # 创建测试配置文件
        self._create_test_config_files()

    def tearDown(self):
        """清理测试环境"""
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def _create_test_config_files(self):
        """创建测试配置文件"""
        # 用户配置目录
        user_config_dir = self.temp_path / "user"
        user_config_dir.mkdir(exist_ok=True)

        # 框架配置目录
        framework_config_dir = self.temp_path / "miniboot" / "resources"
        framework_config_dir.mkdir(parents=True, exist_ok=True)

        # 用户默认配置
        user_config = user_config_dir / "application.yml"
        user_config.write_text("""
miniboot:
  application:
    name: user-app
    version: 1.0.0
  server:
    port: 8080
""")

        # 用户Profile配置
        user_profile_config = user_config_dir / "application-dev.yml"
        user_profile_config.write_text("""
miniboot:
  application:
    name: user-dev-app
  server:
    port: 8081
""")

        # 框架默认配置
        framework_config = framework_config_dir / "application.yml"
        framework_config.write_text("""
miniboot:
  application:
    name: framework-app
    description: Framework default
  server:
    port: 8000
    timeout: 30
""")

        # 框架Profile配置
        framework_profile_config = framework_config_dir / "application-dev.yml"
        framework_profile_config.write_text("""
miniboot:
  application:
    description: Framework dev
  server:
    timeout: 60
""")

    def test_optimized_loading_mechanism(self):
        """测试优化的加载机制"""
        # 创建配置加载器
        search_paths = [str(self.temp_path / "user"), str(self.temp_path / "miniboot" / "resources")]
        loader = ConfigurationLoader(search_paths)

        # 创建属性源管理器
        property_sources = MutablePropertySources()

        # 加载配置
        result = loader.load_configuration(property_sources, {"dev"})

        # 验证加载结果
        self.assertIsNotNone(result)
        self.assertGreater(result.total_sources, 0)
        self.assertGreaterEqual(result.load_time_ms, 0)

        # 验证配置优先级
        sources = property_sources._property_sources
        priorities = [source.priority for source in sources]

        # 验证优先级是降序排列
        for i in range(len(priorities) - 1):
            self.assertGreaterEqual(priorities[i], priorities[i + 1])

    def test_framework_base_configuration(self):
        """测试框架配置作为基础"""
        search_paths = [str(self.temp_path / "user"), str(self.temp_path / "miniboot" / "resources")]
        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        # 加载配置
        result = loader.load_configuration(property_sources, set())

        # 验证框架配置被加载
        self.assertGreater(len(result.loaded_files), 0)

        # 验证层级统计
        self.assertIn("user", result.layer_statistics)
        self.assertIn("framework", result.layer_statistics)

    def test_user_configuration_override(self):
        """测试用户配置覆盖框架配置"""
        search_paths = [str(self.temp_path / "user"), str(self.temp_path / "miniboot" / "resources")]
        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        # 加载配置
        loader.load_configuration(property_sources, {"dev"})

        # 验证用户配置优先级更高
        user_sources = [s for s in property_sources._property_sources if s.priority >= ConfigurationPriority.USER_DEFAULT]
        framework_sources = [s for s in property_sources._property_sources if s.priority < ConfigurationPriority.USER_DEFAULT]

        self.assertGreater(len(user_sources), 0)
        self.assertGreater(len(framework_sources), 0)

    def test_configuration_caching(self):
        """测试配置文件发现缓存"""
        search_paths = [str(self.temp_path / "user")]
        loader = ConfigurationLoader(search_paths, enable_cache=True)

        # 第一次加载
        property_sources1 = MutablePropertySources()
        result1 = loader.load_configuration(property_sources1, set())

        # 第二次加载（应该使用缓存）
        property_sources2 = MutablePropertySources()
        result2 = loader.load_configuration(property_sources2, set())

        # 验证缓存工作
        self.assertEqual(len(result1.loaded_files), len(result2.loaded_files))

        # 清除缓存
        loader.clear_cache()

        # 第三次加载
        property_sources3 = MutablePropertySources()
        result3 = loader.load_configuration(property_sources3, set())

        self.assertEqual(len(result1.loaded_files), len(result3.loaded_files))

    def test_load_statistics(self):
        """测试加载统计功能"""
        search_paths = [str(self.temp_path / "user")]
        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        # 加载配置
        result = loader.load_configuration(property_sources, {"dev"})

        # 验证统计信息
        self.assertIsInstance(result.load_time_ms, float)
        self.assertIsInstance(result.total_sources, int)
        self.assertIsInstance(result.layer_statistics, dict)
        self.assertIsInstance(result.loaded_files, list)
        self.assertIsInstance(result.failed_files, list)

    def test_discover_config_files(self):
        """测试配置文件发现功能"""
        search_paths = [str(self.temp_path / "user")]
        loader = ConfigurationLoader(search_paths)

        # 发现配置文件
        config_files = loader.discover_config_files({"dev"})

        # 验证发现的文件
        self.assertGreater(len(config_files), 0)

        # 验证文件存在
        for config_file in config_files:
            self.assertTrue(config_file.exists())

    def test_cache_ttl_setting(self):
        """测试缓存TTL设置"""
        loader = ConfigurationLoader(enable_cache=True)

        # 设置缓存TTL
        loader.set_cache_ttl(600)  # 10分钟

        # 验证设置生效
        self.assertEqual(loader._cache_ttl_seconds, 600)


if __name__ == "__main__":
    unittest.main()
