# Mini-Boot Starter 模块设计

## 1. 概述

Mini-Boot Starter 模块是框架的扩展机制，提供了类似 Spring Boot Starter 的自动配置功能。通过 Starter 模块，开发者可以快速集成第三方库和服务，实现开箱即用的功能。Starter 模块基于自动配置、条件化 Bean 创建和配置属性绑定等核心特性，为框架提供了强大的扩展能力。

Starter 模块的主要特点：

-   基于自动配置机制，实现零配置或最少配置
-   支持条件化 Bean 创建，根据环境动态启用功能
-   提供配置属性绑定，支持外部化配置
-   遵循约定优于配置的设计原则
-   支持插件化扩展，易于第三方集成
-   与 Mini-Boot 框架深度集成

## 1.1 设计原理

Starter 模块基于以下核心原理：

1. **自动配置（AutoConfiguration）**: 通过`@Configuration`类自动配置 Bean
2. **条件化创建（Conditional）**: 基于条件注解控制 Bean 的创建
3. **配置属性（Properties）**: 通过`@ConfigurationProperties`绑定外部配置
4. **工厂机制（Factories）**: 通过`META-INF/mini.factories`文件注册自动配置类
5. **依赖注入（DI）**: 与 IoC 容器深度集成，支持依赖注入

## 1.2 目录结构

```
miniboot/starter/
├── __init__.py                     # Starter模块导出
├── autoconfigure.py                # 自动配置基类
├── conditional.py                  # 条件注解
├── properties.py                   # 配置属性基类
├── factory.py                      # 工厂加载器
├── registry.py                     # Starter注册表
└── utils.py                        # 工具类

# Starter示例结构
starter-example/
├── META-INF/
│   └── mini.factories             # 自动配置注册文件
├── __init__.py                    # 包导出
├── properties.py                  # 配置属性
├── service.py                     # 服务实现
└── configuration.py               # 自动配置类
```

## 2. 核心组件

### 2.1 自动配置基类

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type
from miniboot.annotations import Configuration, Bean
from miniboot.conditional import ConditionalOnProperty, ConditionalOnClass

class AutoConfiguration(ABC):
    """自动配置基类"""

    def get_order(self) -> int:
        """获取配置优先级"""
        return 0

    def get_conditions(self) -> List[str]:
        """获取生效条件"""
        return []

    def before_configuration(self) -> None:
        """配置前回调"""
        pass

    def after_configuration(self) -> None:
        """配置后回调"""
        pass

class StarterAutoConfiguration(AutoConfiguration):
    """Starter自动配置基类"""

    def __init__(self):
        self.properties = None
        self.services = {}

    def configure_properties(self, properties: Any) -> None:
        """配置属性"""
        self.properties = properties

    def register_service(self, name: str, service: Any) -> None:
        """注册服务"""
        self.services[name] = service

    def get_service(self, name: str) -> Optional[Any]:
        """获取服务"""
        return self.services.get(name)
```

### 2.2 配置属性基类

```python
from dataclasses import dataclass
from typing import Any, Dict, Optional
from miniboot.annotations import ConfigurationProperties

@dataclass
class StarterProperties:
    """Starter配置属性基类"""
    enabled: bool = True

    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self.enabled

    def validate(self) -> bool:
        """验证配置"""
        return True

    def get_property(self, key: str, default: Any = None) -> Any:
        """获取属性值"""
        return getattr(self, key, default)

    def set_property(self, key: str, value: Any) -> None:
        """设置属性值"""
        setattr(self, key, value)

# 示例：Redis Starter配置
@ConfigurationProperties(prefix="mini.redis")
@dataclass
class RedisProperties(StarterProperties):
    """Redis配置属性"""
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    database: int = 0
    timeout: int = 5000
    pool_size: int = 10

    def validate(self) -> bool:
        """验证Redis配置"""
        if not self.host:
            return False
        if self.port <= 0 or self.port > 65535:
            return False
        return True
```

### 2.3 条件注解扩展

```python
from typing import List, Optional, Callable, Any
import importlib

def ConditionalOnStarter(starter_name: str):
    """条件：当指定Starter存在时"""
    def decorator(cls):
        cls.__conditional_on_starter__ = starter_name
        return cls
    return decorator

def ConditionalOnBean(bean_type: type):
    """条件：当指定Bean存在时"""
    def decorator(cls):
        cls.__conditional_on_bean__ = bean_type
        return cls
    return decorator

def ConditionalOnMissingBean(bean_type: type):
    """条件：当指定Bean不存在时"""
    def decorator(cls):
        cls.__conditional_on_missing_bean__ = bean_type
        return cls
    return decorator

def ConditionalOnResource(resource_path: str):
    """条件：当指定资源存在时"""
    def decorator(cls):
        cls.__conditional_on_resource__ = resource_path
        return cls
    return decorator

class ConditionalEvaluator:
    """条件评估器"""

    def __init__(self, context):
        self.context = context

    def evaluate(self, cls: type) -> bool:
        """评估条件是否满足"""
        # 检查@ConditionalOnProperty
        if hasattr(cls, '__conditional_on_property__'):
            if not self._check_property_condition(cls):
                return False

        # 检查@ConditionalOnClass
        if hasattr(cls, '__conditional_on_class__'):
            if not self._check_class_condition(cls):
                return False

        # 检查@ConditionalOnBean
        if hasattr(cls, '__conditional_on_bean__'):
            if not self._check_bean_condition(cls):
                return False

        # 检查@ConditionalOnMissingBean
        if hasattr(cls, '__conditional_on_missing_bean__'):
            if not self._check_missing_bean_condition(cls):
                return False

        # 检查@ConditionalOnStarter
        if hasattr(cls, '__conditional_on_starter__'):
            if not self._check_starter_condition(cls):
                return False

        # 检查@ConditionalOnResource
        if hasattr(cls, '__conditional_on_resource__'):
            if not self._check_resource_condition(cls):
                return False

        return True

    def _check_property_condition(self, cls: type) -> bool:
        """检查属性条件"""
        condition = getattr(cls, '__conditional_on_property__')
        property_name = condition.get('name')
        having_value = condition.get('having_value')
        match_if_missing = condition.get('match_if_missing', False)

        property_value = self.context.environment.get_property(property_name)

        if property_value is None:
            return match_if_missing

        if having_value is not None:
            return str(property_value) == str(having_value)

        return True

    def _check_class_condition(self, cls: type) -> bool:
        """检查类存在条件"""
        class_names = getattr(cls, '__conditional_on_class__')
        for class_name in class_names:
            try:
                importlib.import_module(class_name)
            except ImportError:
                return False
        return True

    def _check_bean_condition(self, cls: type) -> bool:
        """检查Bean存在条件"""
        bean_type = getattr(cls, '__conditional_on_bean__')
        return self.context.get_bean_by_type(bean_type) is not None

    def _check_missing_bean_condition(self, cls: type) -> bool:
        """检查Bean不存在条件"""
        bean_type = getattr(cls, '__conditional_on_missing_bean__')
        return self.context.get_bean_by_type(bean_type) is None

    def _check_starter_condition(self, cls: type) -> bool:
        """检查Starter存在条件"""
        starter_name = getattr(cls, '__conditional_on_starter__')
        try:
            importlib.import_module(f"miniboot.starter.{starter_name}")
            return True
        except ImportError:
            return False

    def _check_resource_condition(self, cls: type) -> bool:
        """检查资源存在条件"""
        resource_path = getattr(cls, '__conditional_on_resource__')
        import os
        return os.path.exists(resource_path)
```

### 2.4 工厂加载器

```python
import os
import importlib
from typing import Dict, List, Type, Any
from pathlib import Path

class StarterFactory:
    """Starter工厂加载器"""

    FACTORIES_FILE = "META-INF/mini.factories"

    def __init__(self):
        self.auto_configurations: Dict[str, Type] = {}
        self.loaded_starters: List[str] = []

    def load_factories(self, package_paths: List[str] = None) -> None:
        """加载工厂配置"""
        if package_paths is None:
            package_paths = self._discover_starter_packages()

        for package_path in package_paths:
            self._load_package_factories(package_path)

    def _discover_starter_packages(self) -> List[str]:
        """发现Starter包"""
        starter_packages = []

        # 扫描miniboot.starter包下的所有子包
        try:
            import miniboot.starter
            starter_base_path = Path(miniboot.starter.__file__).parent

            for item in starter_base_path.iterdir():
                if item.is_dir() and not item.name.startswith('_'):
                    starter_packages.append(f"miniboot.starter.{item.name}")
        except ImportError:
            pass

        # 扫描第三方starter包
        try:
            import pkg_resources
            for entry_point in pkg_resources.iter_entry_points('miniboot_starters'):
                starter_packages.append(entry_point.module_name)
        except ImportError:
            pass

        return starter_packages

    def _load_package_factories(self, package_path: str) -> None:
        """加载包的工厂配置"""
        try:
            # 导入包
            package = importlib.import_module(package_path)
            package_dir = Path(package.__file__).parent

            # 查找META-INF/mini.factories文件
            factories_file = package_dir / self.FACTORIES_FILE

            if factories_file.exists():
                self._parse_factories_file(factories_file, package_path)
                self.loaded_starters.append(package_path)

        except ImportError as e:
            print(f"Failed to load starter package {package_path}: {e}")

    def _parse_factories_file(self, factories_file: Path, package_path: str) -> None:
        """解析工厂配置文件"""
        try:
            with open(factories_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip()

                            if key == 'mini.ioc.AutoConfiguration':
                                self._register_auto_configuration(value, package_path)

        except Exception as e:
            print(f"Failed to parse factories file {factories_file}: {e}")

    def _register_auto_configuration(self, class_path: str, package_path: str) -> None:
        """注册自动配置类"""
        try:
            # 导入自动配置类
            module_path, class_name = class_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            config_class = getattr(module, class_name)

            self.auto_configurations[package_path] = config_class
            print(f"Registered auto configuration: {class_path}")

        except Exception as e:
            print(f"Failed to register auto configuration {class_path}: {e}")

    def get_auto_configurations(self) -> List[Type]:
        """获取所有自动配置类"""
        return list(self.auto_configurations.values())

    def get_loaded_starters(self) -> List[str]:
        """获取已加载的Starter列表"""
        return self.loaded_starters.copy()

class StarterRegistry:
    """Starter注册表"""

    def __init__(self):
        self.factory = StarterFactory()
        self.registered_starters: Dict[str, Any] = {}
        self.conditional_evaluator = None

    def initialize(self, context) -> None:
        """初始化注册表"""
        self.conditional_evaluator = ConditionalEvaluator(context)

        # 加载所有Starter工厂
        self.factory.load_factories()

        # 注册自动配置类
        self._register_auto_configurations(context)

    def _register_auto_configurations(self, context) -> None:
        """注册自动配置类"""
        auto_configs = self.factory.get_auto_configurations()

        for config_class in auto_configs:
            # 评估条件
            if self.conditional_evaluator.evaluate(config_class):
                # 注册到IoC容器
                context.register_type(config_class)

                starter_name = self._extract_starter_name(config_class)
                self.registered_starters[starter_name] = config_class

                print(f"Registered starter: {starter_name}")
            else:
                print(f"Skipped starter due to unmet conditions: {config_class.__name__}")

    def _extract_starter_name(self, config_class: Type) -> str:
        """提取Starter名称"""
        class_name = config_class.__name__
        if class_name.endswith('Configuration'):
            return class_name[:-13].lower()  # 移除'Configuration'后缀
        return class_name.lower()

    def get_registered_starters(self) -> Dict[str, Any]:
        """获取已注册的Starter"""
        return self.registered_starters.copy()

    def is_starter_registered(self, starter_name: str) -> bool:
        """检查Starter是否已注册"""
        return starter_name in self.registered_starters
```

## 3. Starter 示例

### 3.1 Redis Starter

基于 mock starter 的模式，我们来看一个完整的 Redis Starter 示例：

#### 3.1.1 目录结构

```
miniboot/starter/redis/
├── META-INF/
│   └── mini.factories
├── __init__.py
├── properties.py
├── service.py
└── configuration.py
```

#### 3.1.2 META-INF/mini.factories

```properties
# Redis Starter自动配置
mini.ioc.AutoConfiguration=miniboot.starter.redis.configuration.RedisConfiguration
```

#### 3.1.3 properties.py

```python
"""Redis配置属性模块"""

from dataclasses import dataclass
from typing import Optional
from miniboot.starter import StarterProperties
from miniboot.annotations import ConfigurationProperties

@ConfigurationProperties(prefix="mini.redis")
@dataclass
class RedisProperties(StarterProperties):
    """Redis配置属性"""
    host: str = "localhost"
    port: int = 6379
    password: Optional[str] = None
    database: int = 0
    timeout: int = 5000
    pool_size: int = 10
    ssl: bool = False

    def validate(self) -> bool:
        """验证Redis配置"""
        if not self.host:
            return False
        if self.port <= 0 or self.port > 65535:
            return False
        if self.database < 0:
            return False
        return True

    def get_connection_url(self) -> str:
        """获取连接URL"""
        protocol = "rediss" if self.ssl else "redis"
        auth = f":{self.password}@" if self.password else ""
        return f"{protocol}://{auth}{self.host}:{self.port}/{self.database}"
```

#### 3.1.4 service.py

```python
"""Redis服务模块"""

import asyncio
from typing import Any, Optional, Dict, List
from miniboot.starter.redis.properties import RedisProperties

try:
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

class RedisService:
    """Redis服务实现"""

    def __init__(self, properties: RedisProperties):
        """初始化Redis服务

        Args:
            properties: Redis配置属性
        """
        self.properties = properties
        self.redis = None
        self._connected = False

    async def connect(self) -> None:
        """连接Redis"""
        if not REDIS_AVAILABLE:
            raise RuntimeError("aioredis is not installed")

        if not self.properties.validate():
            raise ValueError("Invalid Redis configuration")

        try:
            connection_url = self.properties.get_connection_url()
            self.redis = await aioredis.from_url(
                connection_url,
                socket_timeout=self.properties.timeout / 1000,
                max_connections=self.properties.pool_size
            )
            self._connected = True
            print(f"Connected to Redis: {self.properties.host}:{self.properties.port}")

        except Exception as e:
            raise RuntimeError(f"Failed to connect to Redis: {e}")

    async def disconnect(self) -> None:
        """断开Redis连接"""
        if self.redis:
            await self.redis.close()
            self._connected = False
            print("Disconnected from Redis")

    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._connected

    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置键值"""
        if not self._connected:
            await self.connect()

        try:
            if expire:
                await self.redis.setex(key, expire, value)
            else:
                await self.redis.set(key, value)
            return True
        except Exception as e:
            print(f"Redis set error: {e}")
            return False

    async def get(self, key: str) -> Optional[str]:
        """获取值"""
        if not self._connected:
            await self.connect()

        try:
            value = await self.redis.get(key)
            return value.decode('utf-8') if value else None
        except Exception as e:
            print(f"Redis get error: {e}")
            return None

    async def delete(self, key: str) -> bool:
        """删除键"""
        if not self._connected:
            await self.connect()

        try:
            result = await self.redis.delete(key)
            return result > 0
        except Exception as e:
            print(f"Redis delete error: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self._connected:
            await self.connect()

        try:
            result = await self.redis.exists(key)
            return result > 0
        except Exception as e:
            print(f"Redis exists error: {e}")
            return False

    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键列表"""
        if not self._connected:
            await self.connect()

        try:
            keys = await self.redis.keys(pattern)
            return [key.decode('utf-8') for key in keys]
        except Exception as e:
            print(f"Redis keys error: {e}")
            return []
```

#### 3.1.5 configuration.py

```python
"""Redis自动配置模块"""

from miniboot.annotations import Configuration, Bean
from miniboot.conditional import ConditionalOnProperty, ConditionalOnClass, ConditionalOnMissingBean
from miniboot.starter.redis.properties import RedisProperties
from miniboot.starter.redis.service import RedisService

@Configuration
@ConditionalOnClass(["aioredis"])
@ConditionalOnProperty(prefix="mini.redis", name="enabled", having_value="true", match_if_missing=True)
class RedisConfiguration:
    """Redis自动配置类"""

    @Bean
    @ConditionalOnMissingBean(RedisProperties)
    def redis_properties(self) -> RedisProperties:
        """创建Redis配置属性Bean"""
        return RedisProperties()

    @Bean
    @ConditionalOnMissingBean(RedisService)
    def redis_service(self, redis_properties: RedisProperties) -> RedisService:
        """创建Redis服务Bean"""
        return RedisService(redis_properties)
```

#### 3.1.6 **init**.py

```python
"""Redis Starter包导出"""

from .properties import RedisProperties
from .service import RedisService
from .configuration import RedisConfiguration

__all__ = [
    'RedisProperties',
    'RedisService',
    'RedisConfiguration'
]
```

### 3.2 Database Starter 示例

```python
# miniboot/starter/database/properties.py
@ConfigurationProperties(prefix="mini.database")
@dataclass
class DatabaseProperties(StarterProperties):
    """数据库配置属性"""
    driver: str = "sqlite"
    host: str = "localhost"
    port: int = 5432
    database: str = "miniboot"
    username: str = "root"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20

    def get_connection_url(self) -> str:
        """获取数据库连接URL"""
        if self.driver == "sqlite":
            return f"sqlite:///{self.database}.db"
        elif self.driver == "postgresql":
            return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        elif self.driver == "mysql":
            return f"mysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
        else:
            raise ValueError(f"Unsupported database driver: {self.driver}")

# miniboot/starter/database/service.py
class DatabaseService:
    """数据库服务"""

    def __init__(self, properties: DatabaseProperties):
        self.properties = properties
        self.engine = None
        self.session_factory = None

    async def initialize(self):
        """初始化数据库连接"""
        try:
            from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker

            connection_url = self.properties.get_connection_url()
            self.engine = create_async_engine(
                connection_url,
                pool_size=self.properties.pool_size,
                max_overflow=self.properties.max_overflow
            )

            self.session_factory = async_sessionmaker(self.engine)
            print(f"Database initialized: {self.properties.driver}")

        except ImportError:
            raise RuntimeError("SQLAlchemy is not installed")

    async def get_session(self):
        """获取数据库会话"""
        if not self.session_factory:
            await self.initialize()
        return self.session_factory()

# miniboot/starter/database/configuration.py
@Configuration
@ConditionalOnClass(["sqlalchemy"])
@ConditionalOnProperty(prefix="mini.database", name="enabled", having_value="true", match_if_missing=True)
class DatabaseConfiguration:
    """数据库自动配置"""

    @Bean
    def database_properties(self) -> DatabaseProperties:
        return DatabaseProperties()

    @Bean
    def database_service(self, database_properties: DatabaseProperties) -> DatabaseService:
        return DatabaseService(database_properties)
```

## 4. 使用指南

### 4.1 使用现有 Starter

```python
# application.yml
mini:
  redis:
    enabled: true
    host: "localhost"
    port: 6379
    password: "mypassword"
    database: 0
    pool_size: 20

# 在应用中使用
from miniboot.context import DefaultApplicationContext
from miniboot.annotations import Component, Autowired
from miniboot.starter.redis import RedisService

@Component
class UserService:

    @Autowired
    def set_redis_service(self, redis_service: RedisService):
        self.redis_service = redis_service

    async def cache_user(self, user_id: str, user_data: dict):
        """缓存用户数据"""
        import json
        await self.redis_service.set(
            f"user:{user_id}",
            json.dumps(user_data),
            expire=3600
        )

    async def get_cached_user(self, user_id: str) -> dict:
        """获取缓存的用户数据"""
        import json
        data = await self.redis_service.get(f"user:{user_id}")
        return json.loads(data) if data else None

async def main():
    context = DefaultApplicationContext("config/application.yml")
    await context.start()

    user_service = context.get_bean("userService")

    # 使用Redis缓存
    await user_service.cache_user("123", {"name": "Alice", "age": 30})
    user = await user_service.get_cached_user("123")
    print(f"Cached user: {user}")

    await context.stop()
```

### 4.2 创建自定义 Starter

#### 4.2.1 创建 Starter 项目结构

```bash
my-custom-starter/
├── setup.py
├── miniboot/
│   └── starter/
│       └── mycustom/
│           ├── META-INF/
│           │   └── mini.factories
│           ├── __init__.py
│           ├── properties.py
│           ├── service.py
│           └── configuration.py
```

#### 4.2.2 实现配置属性

```python
# properties.py
@ConfigurationProperties(prefix="mini.mycustom")
@dataclass
class MyCustomProperties(StarterProperties):
    """自定义Starter配置"""
    api_key: str = ""
    timeout: int = 30
    retry_count: int = 3

    def validate(self) -> bool:
        return bool(self.api_key)
```

#### 4.2.3 实现服务类

```python
# service.py
class MyCustomService:
    """自定义服务"""

    def __init__(self, properties: MyCustomProperties):
        self.properties = properties

    async def call_api(self, endpoint: str) -> dict:
        """调用API"""
        # 实现API调用逻辑
        pass
```

#### 4.2.4 实现自动配置

```python
# configuration.py
@Configuration
@ConditionalOnProperty(prefix="mini.mycustom", name="enabled", having_value="true")
class MyCustomConfiguration:
    """自定义自动配置"""

    @Bean
    def my_custom_properties(self) -> MyCustomProperties:
        return MyCustomProperties()

    @Bean
    def my_custom_service(self, properties: MyCustomProperties) -> MyCustomService:
        return MyCustomService(properties)
```

#### 4.2.5 配置工厂文件

```properties
# META-INF/mini.factories
mini.ioc.AutoConfiguration=miniboot.starter.mycustom.configuration.MyCustomConfiguration
```

#### 4.2.6 配置 setup.py

```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="miniboot-starter-mycustom",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "miniboot>=1.0.0",
        # 其他依赖
    ],
    entry_points={
        'miniboot_starters': [
            'mycustom = miniboot.starter.mycustom',
        ],
    },
    package_data={
        'miniboot.starter.mycustom': ['META-INF/*'],
    },
    include_package_data=True,
)
```

## 5. 最佳实践

### 5.1 Starter 设计原则

1. **单一职责**: 每个 Starter 只负责一个特定功能或服务的集成
2. **约定优于配置**: 提供合理的默认配置，减少用户配置负担
3. **条件化创建**: 使用条件注解确保 Starter 只在合适的环境下生效
4. **向后兼容**: 保持 API 的稳定性，避免破坏性变更
5. **文档完善**: 提供清晰的使用文档和配置说明

### 5.2 配置属性设计

```python
@ConfigurationProperties(prefix="mini.myservice")
@dataclass
class MyServiceProperties(StarterProperties):
    """服务配置属性"""

    # 使用合理的默认值
    timeout: int = 30
    retry_count: int = 3

    # 提供验证方法
    def validate(self) -> bool:
        if self.timeout <= 0:
            return False
        if self.retry_count < 0:
            return False
        return True

    # 提供便利方法
    def get_timeout_ms(self) -> int:
        return self.timeout * 1000
```

### 5.3 条件注解使用

```python
@Configuration
# 检查必要的依赖库
@ConditionalOnClass(["requests", "aiohttp"])
# 检查配置属性
@ConditionalOnProperty(prefix="mini.http", name="enabled", having_value="true", match_if_missing=True)
# 检查Bean不存在，避免重复创建
@ConditionalOnMissingBean(HttpService)
class HttpConfiguration:
    """HTTP客户端自动配置"""

    @Bean
    def http_service(self, properties: HttpProperties) -> HttpService:
        return HttpService(properties)
```

### 5.4 错误处理

```python
class MyService:
    def __init__(self, properties: MyServiceProperties):
        self.properties = properties

        # 在初始化时验证配置
        if not properties.validate():
            raise ValueError("Invalid service configuration")

    async def connect(self):
        """连接服务"""
        try:
            # 连接逻辑
            pass
        except Exception as e:
            # 提供清晰的错误信息
            raise RuntimeError(f"Failed to connect to service: {e}")
```

### 5.5 测试策略

```python
# test_starter.py
import pytest
from miniboot.context import DefaultApplicationContext
from miniboot.starter.myservice import MyService, MyServiceProperties

class TestMyServiceStarter:

    @pytest.fixture
    async def context(self):
        """创建测试上下文"""
        context = DefaultApplicationContext()
        await context.start()
        yield context
        await context.stop()

    async def test_service_auto_configuration(self, context):
        """测试服务自动配置"""
        # 设置测试配置
        context.environment.set_property("mini.myservice.enabled", "true")
        context.environment.set_property("mini.myservice.timeout", "60")

        # 获取自动配置的服务
        service = context.get_bean_by_type(MyService)
        assert service is not None
        assert service.properties.timeout == 60

    async def test_conditional_creation(self, context):
        """测试条件化创建"""
        # 禁用服务
        context.environment.set_property("mini.myservice.enabled", "false")

        # 服务不应该被创建
        service = context.get_bean_by_type(MyService)
        assert service is None
```

## 6. 与 Spring Boot Starter 对比

### 6.1 相似之处

-   都基于自动配置机制
-   都使用条件注解控制 Bean 创建
-   都支持配置属性绑定
-   都遵循约定优于配置原则
-   都提供开箱即用的功能

### 6.2 差异之处

| 特性     | Spring Boot Starter      | Mini-Boot Starter        |
| -------- | ------------------------ | ------------------------ |
| 配置文件 | spring.factories         | mini.factories           |
| 条件注解 | @ConditionalOnClass 等   | @ConditionalOnClass 等   |
| 配置属性 | @ConfigurationProperties | @ConfigurationProperties |
| 自动配置 | @AutoConfiguration       | @Configuration           |
| 依赖注入 | @Autowired               | @Autowired               |
| 异步支持 | 有限支持                 | 原生 async/await         |
| 包管理   | Maven/Gradle             | pip/poetry               |
| 生态系统 | 庞大的 Starter 生态      | 新兴的 Starter 生态      |

### 6.3 使用对比

**Spring Boot:**

```java
// application.yml
spring:
  redis:
    host: localhost
    port: 6379

// Java代码
@Service
public class UserService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public void cacheUser(String userId, User user) {
        redisTemplate.opsForValue().set("user:" + userId, user);
    }
}
```

**Mini-Boot:**

```python
# application.yml
mini:
  redis:
    host: localhost
    port: 6379

# Python代码
@Service
class UserService:
    @Autowired
    def set_redis_service(self, redis_service: RedisService):
        self.redis_service = redis_service

    async def cache_user(self, user_id: str, user: dict):
        await self.redis_service.set(f"user:{user_id}", json.dumps(user))
```

## 7. 总结

Mini-Boot Starter 模块提供了一种强大而灵活的扩展机制，使开发者能够快速集成第三方库和服务。通过自动配置、条件化 Bean 创建和配置属性绑定等特性，Starter 模块实现了真正的开箱即用体验。

### 核心特性

1. **自动配置机制**

    - 基于@Configuration 类的自动配置
    - META-INF/mini.factories 工厂注册
    - 与 IoC 容器深度集成

2. **条件化 Bean 创建**

    - @ConditionalOnProperty 属性条件
    - @ConditionalOnClass 类存在条件
    - @ConditionalOnBean Bean 存在条件
    - 灵活的条件组合

3. **配置属性绑定**

    - @ConfigurationProperties 外部化配置
    - 类型安全的配置属性
    - 配置验证和默认值

4. **扩展机制**
    - 插件化的 Starter 架构
    - 第三方 Starter 支持
    - 标准化的开发模式

### 设计优势

1. **开箱即用**: 最少配置或零配置即可使用
2. **类型安全**: 基于 Python 类型注解的配置绑定
3. **条件智能**: 根据环境自动启用或禁用功能
4. **易于扩展**: 标准化的 Starter 开发模式
5. **生态友好**: 支持第三方 Starter 开发和分发

### 适用场景

1. **第三方库集成**: Redis、数据库、消息队列等
2. **云服务集成**: AWS、阿里云等云服务
3. **监控和日志**: Prometheus、ELK 等监控方案
4. **安全认证**: JWT、OAuth 等认证方案
5. **业务组件**: 支付、短信等业务服务

### 发展方向

1. **丰富 Starter 生态**: 开发更多常用的 Starter
2. **提升开发体验**: 改进开发工具和文档
3. **性能优化**: 优化自动配置的加载性能
4. **社区建设**: 建立 Starter 开发和分享社区

通过 Starter 模块，Mini-Boot 框架为 Python 开发者提供了类似 Spring Boot 的开发体验，大大简化了第三方库的集成和配置工作。这种设计不仅提高了开发效率，也促进了框架生态系统的发展。

---

_本文档定义了 Mini-Boot 框架的 Starter 模块设计，提供自动配置和扩展机制。_
