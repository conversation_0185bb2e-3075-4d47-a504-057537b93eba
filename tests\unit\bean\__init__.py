#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean模块单元测试套件
"""

import unittest
import sys


def load_tests(loader, tests, pattern):
    """加载Bean模块的所有测试"""
    suite = unittest.TestSuite()

    # 导入所有测试模块
    from . import test_base
    from . import test_definition
    from . import test_factory
    from . import test_factory_async
    from . import test_factory_sync
    from . import test_registry
    from . import test_cache
    from . import test_graph
    from . import test_scopes

    # 添加测试用例
    suite.addTests(loader.loadTestsFromModule(test_base))
    suite.addTests(loader.loadTestsFromModule(test_definition))
    suite.addTests(loader.loadTestsFromModule(test_factory))
    suite.addTests(loader.loadTestsFromModule(test_factory_async))
    suite.addTests(loader.loadTestsFromModule(test_factory_sync))
    suite.addTests(loader.loadTestsFromModule(test_registry))
    suite.addTests(loader.loadTestsFromModule(test_cache))
    suite.addTests(loader.loadTestsFromModule(test_graph))
    suite.addTests(loader.loadTestsFromModule(test_scopes))

    return suite


def run_all_tests():
    """运行所有Bean模块测试"""
    loader = unittest.TestLoader()
    suite = load_tests(loader, None, None)

    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
