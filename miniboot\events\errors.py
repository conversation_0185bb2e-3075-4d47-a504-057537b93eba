#!/usr/bin/env python
"""
* @author: cz
* @description: 事件错误处理器

实现事件系统的错误处理和恢复机制,提供错误收集、报告、
恢复策略和降级处理等功能.
"""

import threading
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any

from loguru import logger

from ..errors import EventHandlerError
from ..errors import EventHandlerExecutionError as AsyncEventExecutionError
from ..errors import EventPublishError
from ..errors import EventPublishError as EventException


class ErrorSeverity(Enum):
    """错误严重程度"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RecoveryStrategy(Enum):
    """恢复策略"""

    IGNORE = "ignore"  # 忽略错误,继续执行
    RETRY = "retry"  # 重试执行
    FALLBACK = "fallback"  # 使用降级处理
    STOP = "stop"  # 停止处理


@dataclass
class ErrorRecord:
    """错误记录"""

    error: Exception
    severity: ErrorSeverity
    context: dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    recovery_attempted: bool = False
    recovery_successful: bool = False
    retry_count: int = 0

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return {
            "error_type": type(self.error).__name__,
            "error_message": str(self.error),
            "severity": self.severity.value,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "recovery_attempted": self.recovery_attempted,
            "recovery_successful": self.recovery_successful,
            "retry_count": self.retry_count,
        }


class ErrorHandler(ABC):
    """错误处理器抽象基类"""

    @abstractmethod
    def can_handle(self, error: Exception) -> bool:
        """检查是否可以处理指定错误

        Args:
            error: 错误对象

        Returns:
            如果可以处理返回True,否则返回False
        """
        pass

    @abstractmethod
    def handle_error(self, error: Exception, context: dict[str, Any]) -> RecoveryStrategy:
        """处理错误

        Args:
            error: 错误对象
            context: 错误上下文

        Returns:
            恢复策略
        """
        pass

    @abstractmethod
    def get_severity(self, error: Exception) -> ErrorSeverity:
        """获取错误严重程度

        Args:
            error: 错误对象

        Returns:
            错误严重程度
        """
        pass


class DefaultEventErrorHandler(ErrorHandler):
    """默认事件错误处理器"""

    def __init__(self, max_retries: int = 3):
        """初始化默认错误处理器

        Args:
            max_retries: 最大重试次数
        """
        self.max_retries = max_retries

    def can_handle(self, error: Exception) -> bool:
        """检查是否可以处理指定错误"""
        return isinstance(error, EventException)

    def handle_error(self, error: Exception, context: dict[str, Any]) -> RecoveryStrategy:
        """处理错误"""
        severity = self.get_severity(error)
        retry_count = context.get("retry_count", 0)

        # 记录错误
        logger.error(f"Event error occurred: {error}")

        # 根据错误类型和严重程度决定恢复策略
        if isinstance(error, EventHandlerError):
            if retry_count < self.max_retries and severity != ErrorSeverity.CRITICAL:
                return RecoveryStrategy.RETRY
            else:
                return RecoveryStrategy.IGNORE  # 忽略单个处理器错误,继续其他处理器

        elif isinstance(error, EventPublishError):
            if severity == ErrorSeverity.CRITICAL:
                return RecoveryStrategy.STOP
            else:
                return RecoveryStrategy.FALLBACK

        elif isinstance(error, AsyncEventExecutionError):
            if retry_count < self.max_retries:
                return RecoveryStrategy.RETRY
            else:
                return RecoveryStrategy.IGNORE

        else:
            # 其他类型的错误
            if severity == ErrorSeverity.CRITICAL:
                return RecoveryStrategy.STOP
            else:
                return RecoveryStrategy.IGNORE

    def get_severity(self, error: Exception) -> ErrorSeverity:
        """获取错误严重程度"""
        if isinstance(error, EventHandlerError):
            # 单个处理器错误通常不是致命的
            return ErrorSeverity.MEDIUM

        elif isinstance(error, EventPublishError):
            # 发布错误可能影响多个处理器
            failed_handlers = getattr(error, "failed_handlers", [])
            if len(failed_handlers) > 5:  # 如果失败的处理器太多
                return ErrorSeverity.HIGH
            else:
                return ErrorSeverity.MEDIUM

        elif isinstance(error, AsyncEventExecutionError):
            # 异步执行错误
            execution_time = getattr(error, "execution_time", 0)
            if execution_time and execution_time > 30:  # 执行时间过长
                return ErrorSeverity.HIGH
            else:
                return ErrorSeverity.MEDIUM

        else:
            # 其他未知错误
            return ErrorSeverity.HIGH


class EventErrorCollector:
    """事件错误收集器

    收集、存储和分析事件系统中发生的错误,提供错误统计和报告功能.
    """

    def __init__(self, max_records: int = 1000, retention_hours: int = 24):
        """初始化错误收集器

        Args:
            max_records: 最大错误记录数
            retention_hours: 错误记录保留时间(小时)
        """
        super().__init__()  # 初始化异常处理混入
        self.max_records = max_records
        self.retention_hours = retention_hours

        # 错误记录存储
        self._records: deque = deque(maxlen=max_records)
        self._error_counts: dict[str, int] = defaultdict(int)
        self._severity_counts: dict[ErrorSeverity, int] = defaultdict(int)

        # 线程安全锁
        self._lock = threading.RLock()

        # 错误处理器列表
        self._error_handlers: list[ErrorHandler] = []
        self._default_handler = DefaultEventErrorHandler()

    def add_error_handler(self, handler: ErrorHandler) -> None:
        """添加错误处理器

        Args:
            handler: 错误处理器
        """
        with self._lock:
            self._error_handlers.append(handler)

    def remove_error_handler(self, handler: ErrorHandler) -> bool:
        """移除错误处理器

        Args:
            handler: 错误处理器

        Returns:
            如果成功移除返回True,否则返回False
        """
        with self._lock:
            try:
                self._error_handlers.remove(handler)
                return True
            except ValueError:
                return False

    def collect_error(self, error: Exception, context: dict[str, Any]) -> RecoveryStrategy:
        """收集错误并决定恢复策略

        Args:
            error: 错误对象
            context: 错误上下文

        Returns:
            恢复策略
        """
        with self._lock:
            # 查找合适的错误处理器
            handler = self._find_error_handler(error)

            # 获取错误严重程度和恢复策略
            severity = handler.get_severity(error)
            recovery_strategy = handler.handle_error(error, context)

            # 创建错误记录
            record = ErrorRecord(error=error, severity=severity, context=context.copy(), retry_count=context.get("retry_count", 0))

            # 存储错误记录
            self._records.append(record)

            # 更新统计信息
            error_type = type(error).__name__
            self._error_counts[error_type] += 1
            self._severity_counts[severity] += 1

            # 清理过期记录
            self._cleanup_expired_records()

            return recovery_strategy

    def _find_error_handler(self, error: Exception) -> ErrorHandler:
        """查找合适的错误处理器

        Args:
            error: 错误对象

        Returns:
            错误处理器
        """
        for handler in self._error_handlers:
            if handler.can_handle(error):
                return handler

        # 如果没有找到合适的处理器,使用默认处理器
        return self._default_handler

    def _cleanup_expired_records(self) -> None:
        """清理过期的错误记录"""
        if not self._records:
            return

        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)

        # 从队列前端移除过期记录
        while self._records and self._records[0].timestamp < cutoff_time:
            expired_record = self._records.popleft()

            # 更新统计信息
            error_type = type(expired_record.error).__name__
            self._error_counts[error_type] = max(0, self._error_counts[error_type] - 1)
            self._severity_counts[expired_record.severity] = max(0, self._severity_counts[expired_record.severity] - 1)

    def get_error_statistics(self) -> dict[str, Any]:
        """获取错误统计信息

        Returns:
            错误统计信息字典
        """
        with self._lock:
            self._cleanup_expired_records()

            return {
                "total_errors": len(self._records),
                "error_counts_by_type": dict(self._error_counts),
                "error_counts_by_severity": {severity.value: count for severity, count in self._severity_counts.items()},
                "recent_errors": [
                    record.to_dict()
                    for record in list(self._records)[-10:]  # 最近10个错误
                ],
                "retention_hours": self.retention_hours,
                "max_records": self.max_records,
            }

    def get_error_trends(self, hours: int = 24) -> dict[str, Any]:
        """获取错误趋势分析

        Args:
            hours: 分析时间范围(小时)

        Returns:
            错误趋势分析结果
        """
        with self._lock:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_records = [record for record in self._records if record.timestamp >= cutoff_time]

            if not recent_records:
                return {"period_hours": hours, "total_errors": 0, "error_rate": 0.0, "most_common_errors": [], "severity_distribution": {}}

            # 计算错误率(每小时错误数)
            error_rate = len(recent_records) / hours

            # 统计最常见的错误类型
            type_counts = defaultdict(int)
            severity_counts = defaultdict(int)

            for record in recent_records:
                error_type = type(record.error).__name__
                type_counts[error_type] += 1
                severity_counts[record.severity] += 1

            # 排序获取最常见的错误
            most_common_errors = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:5]

            return {
                "period_hours": hours,
                "total_errors": len(recent_records),
                "error_rate": round(error_rate, 2),
                "most_common_errors": most_common_errors,
                "severity_distribution": {severity.value: count for severity, count in severity_counts.items()},
            }

    def clear_records(self) -> int:
        """清空所有错误记录

        Returns:
            清空的记录数量
        """
        with self._lock:
            count = len(self._records)
            self._records.clear()
            self._error_counts.clear()
            self._severity_counts.clear()
            return count
