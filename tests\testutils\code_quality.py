"""
代码质量检查工具

提供代码质量检查功能,确保代码没有编译警告和格式问题.
"""

import subprocess
from pathlib import Path
from typing import Optional

from tqdm import tqdm


class CodeQualityChecker:
    """代码质量检查器"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.miniboot_dir = project_root / "miniboot"
        self.tests_dir = project_root / "tests"

    def check_with_ruff(self, file_path: Path) -> list[str]:
        """使用 ruff 检查代码质量"""
        warnings = []
        try:
            # 使用 ruff 检查代码
            result = subprocess.run(
                [
                    "uv",
                    "run",
                    "ruff",
                    "check",
                    str(file_path),
                ],
                capture_output=True,
                text=True,
                encoding="utf-8",
                timeout=30,
                check=False,
            )

            if result.stdout:
                for line in result.stdout.strip().split("\n"):
                    # 过滤掉 "All checks passed!" 消息
                    if line.strip() and "All checks passed!" not in line:
                        warnings.append(f"ruff: {line}")

        except (subprocess.TimeoutExpired, FileNotFoundError):
            # ruff 不可用,跳过
            pass
        except (OSError, subprocess.SubprocessError) as e:
            warnings.append(f"ruff检查错误 {file_path}: {e}")

        return warnings

    def check_chinese_punctuation(self, file_path: Path) -> list[str]:
        """检查中文标点符号"""
        warnings = []

        # 跳过检查代码质量检查脚本本身
        if file_path.name == "code_quality.py":
            return warnings

        try:
            with file_path.open(encoding="utf-8") as f:
                lines = f.readlines()

            # 定义需要检查的中文标点符号
            chinese_punctuation = {
                "，": ",",  # 中文逗号 -> 英文逗号
                "。": ".",  # 中文句号 -> 英文句号
                "；": ";",  # 中文分号 -> 英文分号
                "：": ":",  # 中文冒号 -> 英文冒号
                "（": "(",  # 中文左括号 -> 英文左括号
                "）": ")",  # 中文右括号 -> 英文右括号
                "【": "[",  # 中文左方括号 -> 英文左方括号
                "】": "]",  # 中文右方括号 -> 英文右方括号
                "！": "!",  # 中文感叹号 -> 英文感叹号
                "？": "?",  # 中文问号 -> 英文问号
                """: '"',  # 中文左双引号 -> 英文双引号
                """: '"',  # 中文右双引号 -> 英文双引号
                "'": "'",  # 中文左单引号 -> 英文单引号
                "'": "'",  # 中文右单引号 -> 英文单引号
            }

            for line_no, line in enumerate(lines, 1):
                # 跳过非注释行(简单检查,只检查包含中文的行)
                if not any("\u4e00" <= char <= "\u9fff" for char in line):
                    continue

                # 跳过中文标点符号定义行(避免检查工具检查自己)
                if "->" in line and ("中文" in line or "english_punct" in line):
                    continue

                # 检查是否包含中文标点符号
                for chinese_punct, english_punct in chinese_punctuation.items():
                    if chinese_punct in line:
                        warnings.append(f"中文标点符号 {file_path}:{line_no}: 发现中文标点 '{chinese_punct}', 应使用英文标点 '{english_punct}'")

        except (OSError, UnicodeDecodeError) as e:
            warnings.append(f"中文标点检查错误 {file_path}: {e}")

        return warnings

    def check_file(self, file_path: Path) -> dict[str, list[str]]:
        """检查单个文件"""
        results = {
            "ruff_issues": [],
            "chinese_punctuation": [],
        }

        if file_path.suffix == ".py":
            results["ruff_issues"] = self.check_with_ruff(file_path)
            results["chinese_punctuation"] = self.check_chinese_punctuation(file_path)

        return results

    def check_directory(self, directory: Path, show_progress: bool = True) -> dict[str, dict[str, list[str]]]:
        """检查目录下的所有 Python 文件"""
        results = {}

        # 收集所有需要检查的Python文件
        python_files = [py_file for py_file in directory.rglob("*.py") if not self._should_skip_file(py_file)]

        # 使用tqdm显示进度条
        if show_progress and python_files:
            file_iterator = tqdm(python_files, desc=f"🔍 检查 {directory.name}", unit="文件", leave=False)
        else:
            file_iterator = python_files

        for py_file in file_iterator:
            if show_progress and hasattr(file_iterator, "set_postfix_str"):
                # 更新进度条描述显示当前文件
                file_iterator.set_postfix_str(f"📄 {py_file.name}")

            file_results = self.check_file(py_file)
            if any(file_results.values()):
                results[str(py_file.relative_to(self.project_root))] = file_results

        return results

    def check_project_with_ruff(self, show_progress: bool = True) -> list[str]:
        """使用 Ruff 检查整个项目"""
        # 收集所有 Python 文件
        all_files = []

        # 收集 miniboot 目录的文件
        miniboot_files = list(self.miniboot_dir.rglob("*.py"))
        all_files.extend([f for f in miniboot_files if not self._should_skip_file(f)])

        # 收集 tests 目录的文件
        tests_files = list(self.tests_dir.rglob("*.py"))
        all_files.extend([f for f in tests_files if not self._should_skip_file(f)])

        if show_progress:
            print(f"📁 发现 {len(all_files)} 个 Python 文件")

        if not all_files:
            return []

        # 使用批量 Ruff 检查
        return self._check_with_ruff_batch(all_files, show_progress)

    def _check_with_ruff_batch(self, file_paths: list[Path], show_progress: bool = True) -> list[str]:
        """批量使用 Ruff 检查代码质量"""
        issues = []

        try:
            if show_progress:
                print("🔍 运行 Ruff 代码质量检查...")

            # 批量检查所有文件
            result = subprocess.run(
                [
                    "uv",
                    "run",
                    "ruff",
                    "check",
                    "--output-format=json",
                ]
                + [str(path) for path in file_paths],
                capture_output=True,
                text=True,
                encoding="utf-8",
                timeout=120,
                check=False,
            )

            if result.returncode != 0 and result.stdout.strip():
                # 解析 JSON 输出
                import json

                try:
                    ruff_issues = json.loads(result.stdout)

                    if show_progress:
                        print(f"⚠️  发现 {len(ruff_issues)} 个代码质量问题")

                    for issue in ruff_issues:
                        file_path = Path(issue["filename"])
                        relative_path = file_path.relative_to(self.project_root)
                        message = f"{relative_path}:{issue['location']['row']}:{issue['location']['column']} {issue['code']}: {issue['message']}"
                        issues.append(message)

                except json.JSONDecodeError:
                    # 如果 JSON 解析失败，回退到文本解析
                    for line in result.stdout.strip().split("\n"):
                        if line.strip():
                            issues.append(line.strip())
            else:
                if show_progress:
                    print("✅ 没有发现代码质量问题")

        except subprocess.TimeoutExpired:
            issues.append("Ruff 检查超时")
        except Exception as e:
            issues.append(f"Ruff 检查失败: {e}")

        return issues

    def check_project(self, show_progress: bool = True) -> dict[str, dict[str, list[str]]]:
        """检查整个项目 (保留向后兼容性)"""
        results = {}

        if show_progress:
            print("🚀 开始代码质量检查...")

        # 检查 miniboot 目录
        if show_progress:
            print("📦 检查 miniboot 目录...")
        miniboot_results = self.check_directory(self.miniboot_dir, show_progress)
        results.update(miniboot_results)

        # 检查 tests 目录
        if show_progress:
            print("🧪 检查 tests 目录...")
        tests_results = self.check_directory(self.tests_dir, show_progress)
        results.update(tests_results)

        if show_progress:
            total_files = len(miniboot_results) + len(tests_results)
            if total_files == 0:
                print("✅ 代码质量检查完成, 没有发现问题!")
            else:
                print(f"⚠️  代码质量检查完成, 发现 {total_files} 个文件有问题")

        return results

    def _should_skip_file(self, file_path: Path) -> bool:
        """判断是否应该跳过文件"""
        skip_patterns = [
            "__pycache__",
            ".venv",
            "venv",
            ".git",
            "htmlcov",
            "build",
            "dist",
            "*.egg-info",
        ]

        return any(pattern in str(file_path) for pattern in skip_patterns)


def format_quality_report(results: dict[str, dict[str, list[str]]]) -> str:
    """格式化质量报告"""
    if not results:
        return "✅ 代码质量检查通过:没有发现问题"

    report = ["❌ 代码质量检查失败:发现以下问题", ""]

    total_issues = 0
    for file_path, file_results in results.items():
        file_issues = sum(len(issues) for issues in file_results.values())
        if file_issues > 0:
            report.append(f"📁 {file_path} ({file_issues} 个问题):")

            for category, issues in file_results.items():
                if issues:
                    category_name = {
                        "ruff_issues": "Ruff 代码质量问题",
                        "chinese_punctuation": "中文标点符号问题",
                    }.get(category, category)

                    report.append(f"  {category_name}:")
                    for issue in issues:
                        report.append(f"    - {issue}")

            report.append("")
            total_issues += file_issues

    report.append(f"总计: {total_issues} 个问题需要修复")
    return "\n".join(report)


def check_code_quality(project_root: Optional[Path] = None) -> bool:
    """
    检查代码质量

    Returns:
        True 如果没有问题,False 如果有问题
    """
    if project_root is None:
        project_root = Path(__file__).parent.parent.parent

    checker = CodeQualityChecker(project_root)
    results = checker.check_project()

    report = format_quality_report(results)
    print(report)

    return len(results) == 0
