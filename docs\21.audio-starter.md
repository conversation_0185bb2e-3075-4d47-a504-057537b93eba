# Mini-Boot 音频播放 Starter 技术方案

## 1. 概述

Mini-Boot 音频播放 Starter 是一个集成音频文件播放和文本转语音（TTS）功能的扩展模块。该 Starter 遵循 Mini-Boot 框架的设计理念，提供开箱即用的音频播放能力。

### 1.1 核心特性

- **🎵 音频文件播放**：支持 MP3、WAV、M4A、OGG 等常见音频格式
- **🗣️ 文本转语音**：支持中文 TTS，自动选择合适的中文语音
- **🔄 统一服务接口**：单一服务处理所有音频相关功能
- **📝 混合播放序列**：支持音频文件和文本语音的混合播放
- **⚙️ 灵活配置**：支持音量、语速、超时等参数配置
- **🔧 条件化启用**：可分别启用/禁用音频播放和 TTS 功能

### 1.2 技术验证

相关的技术验证代码已在 `tests/utils` 目录下完成：
- `test_mp3_player.py`：音频文件播放功能验证
- `test_pyttsx3_tts.py`：文本转语音功能验证

## 2. 架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[AudioAutoConfiguration] --> B[AudioProperties]
    A --> C[AudioService]
    
    B --> D[AudioPlayerConfig]
    B --> E[TTSConfig]
    
    C --> F[MP3Player]
    C --> G[ChineseTTSPlayer]
    
    H[用户应用] --> C
    I[application.yml] --> B
    
    C --> J[play_file]
    C --> K[speak]
    C --> L[play_sequence]
```

### 2.2 模块划分

```
miniboot/starters/audio/
├── META-INF/
│   └── mini.factories          # 自动配置注册文件
├── __init__.py                 # 模块导出
├── properties.py               # 音频配置属性
├── service.py                  # 统一的音频服务
├── players/
│   ├── __init__.py
│   ├── mp3_player.py          # MP3播放器实现
│   └── tts_player.py          # TTS播放器实现
├── exceptions.py               # 音频相关异常
└── configuration.py            # 自动配置类
```

## 3. 核心组件设计

### 3.1 配置属性类

```python
@ConfigurationProperties(prefix="miniboot.audio")
@dataclass
class AudioProperties(StarterProperties):
    """音频播放配置属性"""
    
    # 基础配置
    enabled: bool = True
    
    # 音频播放配置
    player: AudioPlayerConfig = field(default_factory=AudioPlayerConfig)
    
    # TTS配置
    tts: TTSConfig = field(default_factory=TTSConfig)

@dataclass
class AudioPlayerConfig:
    """音频播放器配置"""
    enabled: bool = True
    volume: float = 1.0
    timeout_seconds: int = 30
    supported_formats: List[str] = field(default_factory=lambda: ['.mp3', '.wav', '.m4a', '.ogg'])
    
@dataclass  
class TTSConfig:
    """TTS配置"""
    enabled: bool = True
    engine: str = "pyttsx3"
    language: str = "zh-CN"
    rate: int = 150
    volume: float = 1.0
```

### 3.2 统一音频服务

```python
class AudioService:
    """统一的音频服务"""
    
    def __init__(self, properties: AudioProperties):
        self.properties = properties
        self.audio_player = None
        self.tts_player = None
        self._initialized = False
        
    # ==================== 音频文件播放 ====================
    async def play_file(self, file_path: str, **kwargs) -> bool:
        """播放音频文件"""
        
    async def play_files(self, file_paths: List[str], interval: float = 0.1) -> bool:
        """连续播放多个音频文件"""
        
    # ==================== 文本转语音 ====================
    async def speak(self, text: str, **kwargs) -> bool:
        """播放文本语音"""
        
    async def speak_texts(self, texts: List[str], interval: float = 0.2) -> bool:
        """连续播放多段文本语音"""
        
    # ==================== 混合播放 ====================
    async def play_sequence(self, items: List[Union[str, AudioItem]], interval: float = 0.1) -> bool:
        """播放混合序列（音频文件 + 文本语音）"""
```

### 3.3 自动配置类

```python
@ConditionalOnProperty(prefix="miniboot.audio", name="enabled", having_value="true", match_if_missing=True)
class AudioAutoConfiguration(StarterAutoConfiguration):
    """音频播放自动配置类"""
    
    def get_starter_name(self) -> str:
        return "miniboot-starter-audio"
        
    def get_starter_version(self) -> str:
        return "1.0.0"
        
    def get_starter_description(self) -> str:
        return "Mini-Boot 音频播放功能Starter，支持音频文件播放和TTS"
        
    @Bean
    def audio_properties(self) -> AudioProperties:
        """创建音频配置属性Bean"""
        return AudioProperties()
        
    @Bean
    def audio_service(self, audio_properties: AudioProperties) -> AudioService:
        """创建统一的音频服务Bean"""
        return AudioService(audio_properties)
```

## 4. 配置管理

### 4.1 默认配置

```yaml
miniboot:
  audio:
    enabled: true
    
    # 音频播放配置
    player:
      enabled: true
      volume: 1.0
      timeout_seconds: 30
      supported_formats: ['.mp3', '.wav', '.m4a', '.ogg']
      
    # TTS配置
    tts:
      enabled: true
      engine: "pyttsx3"
      language: "zh-CN"
      rate: 150
      volume: 1.0
```

### 4.2 环境特定配置

```yaml
# application-dev.yml (开发环境)
miniboot:
  audio:
    tts:
      rate: 120  # 开发环境语速较慢，便于调试
      
# application-prod.yml (生产环境)
miniboot:
  audio:
    player:
      timeout_seconds: 10  # 生产环境超时时间更短
    tts:
      rate: 180  # 生产环境语速较快
```

## 5. 使用指南

### 5.1 基本使用

```python
from miniboot.context import DefaultApplicationContext
from miniboot.annotations import Component, Autowired
from miniboot.starters.audio import AudioService

@Component
class NotificationService:
    """通知服务示例"""
    
    @Autowired
    def set_audio_service(self, audio_service: AudioService):
        self.audio_service = audio_service
        
    async def play_notification_sound(self, sound_file: str):
        """播放通知音效"""
        await self.audio_service.play_file(sound_file)
        
    async def speak_notification(self, message: str):
        """语音播报通知"""
        await self.audio_service.speak(message)
```

### 5.2 混合播放序列

```python
async def play_completion_sequence(self):
    """播放完成序列"""
    # 方式1：自动识别类型
    sequence = [
        "sounds/completion.mp3",  # 自动识别为音频文件
        "任务已完成",              # 自动识别为文本
        "请查看结果"
    ]
    await self.audio_service.play_sequence(sequence, interval=0.2)
    
    # 方式2：使用AudioItem明确指定类型
    from miniboot.starters.audio import AudioItem
    
    sequence = [
        AudioItem.file("sounds/completion.mp3"),
        AudioItem.text("任务已完成"),
        AudioItem.text("请查看结果")
    ]
    await self.audio_service.play_sequence(sequence, interval=0.2)
```

### 5.3 批量播放

```python
async def play_multiple_notifications(self):
    """播放多个通知"""
    # 连续播放多个音频文件
    sound_files = [
        "sounds/beep1.mp3",
        "sounds/beep2.mp3", 
        "sounds/beep3.mp3"
    ]
    await self.audio_service.play_files(sound_files, interval=0.1)
    
    # 连续播放多段文本
    messages = [
        "第一条通知",
        "第二条通知",
        "第三条通知"
    ]
    await self.audio_service.speak_texts(messages, interval=0.3)
```

## 6. 依赖注入和生命周期

### 6.1 Bean注册

AudioService 通过自动配置类注册为 Spring Bean，支持：
- 依赖注入：通过 `@Autowired` 注解自动装配
- 生命周期管理：与应用上下文同步启动和关闭
- 条件化创建：根据配置属性决定是否创建

### 6.2 条件注解

```python
# 主服务条件
@ConditionalOnProperty(prefix="miniboot.audio", name="enabled", having_value="true", match_if_missing=True)

# 音频播放条件
@ConditionalOnProperty(prefix="miniboot.audio.player", name="enabled", having_value="true", match_if_missing=True)

# TTS条件
@ConditionalOnProperty(prefix="miniboot.audio.tts", name="enabled", having_value="true", match_if_missing=True)
```

## 7. 错误处理

### 7.1 异常层次结构

```python
class AudioException(Exception):
    """音频相关异常基类"""
    
class AudioPlayerException(AudioException):
    """音频播放异常"""
    
class TTSException(AudioException):
    """TTS异常"""
    
class AudioFileException(AudioException):
    """音频文件异常"""
```

### 7.2 错误处理策略

- **文件不存在**：返回 False，记录警告日志
- **格式不支持**：抛出 AudioFileException
- **播放失败**：返回 False，记录错误日志
- **TTS引擎异常**：返回 False，记录错误日志

## 8. 扩展性设计

### 8.1 插件化音频引擎

```python
class AudioEngine(ABC):
    """音频引擎抽象基类"""
    
    @abstractmethod
    async def play(self, file_path: str) -> bool:
        """播放音频文件"""
        
class TTSEngine(ABC):
    """TTS引擎抽象基类"""
    
    @abstractmethod
    async def speak(self, text: str) -> bool:
        """播放文本语音"""
```

### 8.2 引擎注册机制

支持注册自定义的音频引擎和TTS引擎，便于扩展新的播放能力。

---

_本文档定义了 Mini-Boot 框架的音频播放 Starter 技术方案，提供音频文件播放和文本转语音功能。_
