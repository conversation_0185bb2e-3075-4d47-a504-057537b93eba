#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 自动装配处理器 - 实现@Autowired注解的依赖注入处理

自动装配处理器负责处理@Autowired注解标记的字段和方法,
实现自动依赖注入功能.支持字段注入、setter方法注入和构造函数注入.
"""

import inspect
from functools import lru_cache
from typing import Any, Callable, Optional
from weakref import WeakKeyDictionary

from loguru import logger

from ..annotations.inject import is_autowired
from ..errors import BeanCreationError, BeanCurrentlyInCreationError
from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import AbstractAnnotationProcessor, ProcessorOrder
from .cache import cache
from .decorators import processor_exception_handler, std_processor


class InjectionCacheManager:
    """注入缓存管理器

    使用弱引用和LRU缓存相结合的策略来管理注入缓存,
    防止内存泄漏并提供高效的缓存访问.
    """

    def __init__(self, max_size: int = 1000, compute_func: Optional[Callable] = None):
        """初始化缓存管理器

        Args:
            max_size: LRU缓存的最大大小
            compute_func: 计算注入信息的函数
        """
        self._max_size = max_size
        # 使用弱引用字典,当类对象被垃圾回收时自动清理缓存
        self._weak_cache: WeakKeyDictionary[type, list[tuple]] = WeakKeyDictionary()
        # 设置计算函数
        self._compute_func = compute_func or self._compute_injection_info
        # 创建LRU缓存函数
        self._lru_get = lru_cache(maxsize=max_size)(self._compute_func)

    def get_injection_info(self, bean_class: type) -> list[tuple]:
        """获取类的注入信息

        Args:
            bean_class: Bean类

        Returns:
            注入信息列表
        """
        # 首先检查弱引用缓存
        if bean_class in self._weak_cache:
            return self._weak_cache[bean_class]

        # 使用LRU缓存计算注入信息
        injection_info = self._lru_get(bean_class)

        # 存储到弱引用缓存中
        self._weak_cache[bean_class] = injection_info

        return injection_info

    def _compute_injection_info(self, bean_class: type) -> list[tuple]:  # noqa: ARG002
        """计算类的注入信息

        这个方法被LRU缓存装饰,用于实际计算注入信息.

        Args:
            bean_class: Bean类

        Returns:
            注入信息列表
        """
        # 这个方法将在AutowiredAnnotationProcessor中被重写
        return []

    def clear_cache(self) -> None:
        """清理所有缓存"""
        self._weak_cache.clear()
        self._lru_get.cache_clear()

    def get_cache_info(self) -> dict:
        """获取缓存统计信息

        Returns:
            缓存统计信息字典
        """
        lru_info = self._lru_get.cache_info()
        return {
            "weak_cache_size": len(self._weak_cache),
            "lru_hits": lru_info.hits,
            "lru_misses": lru_info.misses,
            "lru_current_size": lru_info.currsize,
            "lru_max_size": lru_info.maxsize,
        }


class AutowiredAnnotationProcessor(AbstractAnnotationProcessor):
    """
    @Autowired注解处理器

    负责处理@Autowired注解的依赖注入,支持:
    - 字段注入:直接在字段上使用@Autowired
    - Setter方法注入:在setter方法上使用@Autowired
    - 构造函数注入:在构造函数参数上使用@Autowired

    处理器在Bean初始化前执行,确保所有依赖在Bean使用前完成注入.

    Example:
        # 字段注入
        class UserService:
            @Autowired
            user_repository: UserRepository

        # Setter方法注入
        class UserService:
            @Autowired
            def set_user_repository(self, user_repository: UserRepository):
                self.user_repository = user_repository
    """

    def __init__(self, bean_factory=None, cache_max_size: int = 1000):
        """
        初始化自动装配处理器

        Args:
            bean_factory: Bean工厂实例,用于解析依赖
            cache_max_size: 注入缓存的最大大小,默认1000
        """
        super().__init__()  # 调用抽象基类初始化
        self._bean_factory = bean_factory
        # 使用智能缓存管理器替代普通字典
        self._cache_manager = InjectionCacheManager(max_size=cache_max_size, compute_func=self._compute_injection_info_impl)
        # 获取类型检查缓存
        self._type_cache = cache()

    def set_bean_factory(self, bean_factory) -> None:
        """
        设置Bean工厂

        Args:
            bean_factory: Bean工厂实例
        """
        self._bean_factory = bean_factory

    def _get_annotation_type(self) -> str:
        """获取目标注解类型"""
        return "Autowired"

    def _process_before(self, bean: Any, bean_name: str) -> Any:
        """前置处理:执行依赖注入"""
        # 执行字段注入
        self._inject_fields(bean, bean_name)

        # 执行方法注入
        self._inject_methods(bean, bean_name)

        # 标记为已处理
        self._mark_processed(bean_name)

        logger.debug(f"Completed autowired injection for bean: {bean_name}")
        return bean

    def _process_after(self, bean: Any, bean_name: str) -> Any:
        """后置处理:自动装配处理器不需要后处理"""
        return bean

    def get_order(self) -> int:
        """
        获取处理器执行顺序

        自动装配处理器需要在其他处理器之前执行,
        确保依赖注入在其他处理之前完成.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR

    def _inject_fields(self, bean: Any, bean_name: str) -> None:
        """
        注入@Autowired标记的字段

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        bean_class = bean.__class__

        # 获取缓存的字段信息
        field_info = self._get_autowired_fields(bean_class)

        for field_name, field_type, required, qualifier in field_info:
            try:
                # 检查字段是否已经有值
                if hasattr(bean, field_name) and getattr(bean, field_name) is not None:
                    continue

                # 解析依赖
                dependency = self._resolve_dependency(field_type, field_name, bean_name, required, qualifier)

                if dependency is not None:
                    setattr(bean, field_name, dependency)
                    logger.debug(f"Injected field '{field_name}' for bean '{bean_name}'")
                elif required:
                    raise BeanCreationError(f"Required autowired field '{field_name}' could not be resolved", bean_name)

            except Exception as e:
                if required:
                    raise BeanCreationError(f"Failed to inject autowired field '{field_name}' for bean '{bean_name}'", bean_name, e) from e
                else:
                    logger.warning(f"Optional autowired field '{field_name}' injection failed: {e}")

    def _inject_methods(self, bean: Any, bean_name: str) -> None:
        """
        注入@Autowired标记的方法

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        bean_class = bean.__class__

        # 获取缓存的方法信息
        method_info = self._get_autowired_methods(bean_class)

        for method_name, method, required, qualifier in method_info:
            try:
                # 解析方法参数
                args = self._resolve_method_dependencies(method, bean_name, required, qualifier)

                # 调用方法
                method(bean, *args)
                logger.debug(f"Injected method '{method_name}' for bean '{bean_name}'")

            except Exception as e:
                if required:
                    raise BeanCreationError(f"Failed to inject autowired method '{method_name}' for bean '{bean_name}'", bean_name, e) from e
                else:
                    logger.warning(f"Optional autowired method '{method_name}' injection failed: {e}")

    def _get_autowired_fields(self, bean_class: type) -> list[tuple]:
        """
        获取类中的@Autowired字段信息

        Args:
            bean_class: Bean类

        Returns:
            字段信息列表:[(field_name, field_type, required, qualifier), ...]
        """
        # 使用缓存管理器获取注入信息
        return self._cache_manager.get_injection_info(bean_class)

    def _compute_injection_info_impl(self, bean_class: type) -> list[tuple]:
        """
        实际计算类的注入信息

        这个方法被缓存管理器调用来计算注入信息.

        Args:
            bean_class: Bean类

        Returns:
            字段信息列表:[(field_name, field_type, required, qualifier), ...]
        """
        fields = []

        # 检查类级别的@Autowired字段
        if hasattr(bean_class, "__autowired_fields__"):
            autowired_fields = bean_class.__autowired_fields__
            # 使用类型缓存获取类型提示
            type_hints = self._type_cache.get_type_hints(bean_class)

            for field_name, metadata in autowired_fields.items():
                field_type = type_hints.get(field_name)
                if field_type:
                    # 安全地获取metadata属性
                    required = getattr(metadata, "required", True)
                    qualifier = getattr(metadata, "qualifier", None)
                    fields.append((field_name, field_type, required, qualifier))

        # 检查字段级别的@Autowired注解
        # 使用缓存获取类属性列表
        attributes = self._type_cache.get_class_attributes(bean_class)
        # 使用缓存获取类型提示
        type_hints = self._type_cache.get_type_hints(bean_class)

        for attr_name in attributes:
            try:
                attr = self._type_cache.get_attribute_safely(bean_class, attr_name)
                if attr and is_autowired(attr) and hasattr(attr, "__is_field_injection__"):
                    # 获取字段类型
                    field_type = type_hints.get(attr_name)

                    if field_type:
                        # 获取@Autowired元数据
                        metadata = getattr(attr, "__autowired_metadata__", None)
                        fields.append((attr_name, field_type, metadata.required if metadata else True, metadata.qualifier if metadata else None))
            except Exception:
                continue

        return fields

    def _get_autowired_methods(self, bean_class: type) -> list[tuple]:
        """
        获取类中的@Autowired方法信息

        Args:
            bean_class: Bean类

        Returns:
            方法信息列表:[(method_name, method, required, qualifier), ...]
        """
        methods = []

        # 使用缓存获取类属性列表
        attributes = self._type_cache.get_class_attributes(bean_class)

        for attr_name in attributes:
            try:
                attr = self._type_cache.get_attribute_safely(bean_class, attr_name)

                if attr and self._type_cache.is_method_or_function(attr) and is_autowired(attr) and hasattr(attr, "__is_setter_injection__"):
                    # 获取@Autowired元数据
                    metadata = getattr(attr, "__autowired_metadata__", None)
                    methods.append((attr_name, attr, metadata.required if metadata else True, metadata.qualifier if metadata else None))
            except Exception:
                continue

        return methods

    def _has_autowired_annotations(self, bean_class: type) -> bool:
        """
        检查类是否有@Autowired注解

        Args:
            bean_class: Bean类

        Returns:
            True表示有@Autowired注解
        """
        # 检查类级别的@Autowired字段
        if hasattr(bean_class, "__autowired_fields__") and bean_class.__autowired_fields__:
            return True

        # 检查字段和方法级别的@Autowired注解
        # 使用缓存获取类属性列表
        attributes = self._type_cache.get_class_attributes(bean_class)

        for attr_name in attributes:
            try:
                attr = self._type_cache.get_attribute_safely(bean_class, attr_name)
                if attr and is_autowired(attr):
                    return True
            except Exception:
                continue

        return False

    def _resolve_dependency(self, dependency_type: type, field_name: str, bean_name: str, required: bool, qualifier: Optional[str]) -> Any:
        """
        解析单个依赖

        Args:
            dependency_type: 依赖类型
            field_name: 字段名称
            bean_name: 当前Bean名称
            required: 是否必需
            qualifier: 限定符

        Returns:
            解析的依赖实例
        """
        if self._bean_factory is None:
            if required:
                raise BeanCreationError("Bean factory not available for dependency resolution", bean_name)
            return None

        try:
            # 使用限定符或类型解析依赖
            if qualifier:
                return self._bean_factory.get_bean(qualifier)
            else:
                return self._bean_factory.get_bean_by_type(dependency_type)

        except BeanCurrentlyInCreationError:
            # 处理循环依赖
            if required:
                raise BeanCreationError(f"Circular dependency detected for field '{field_name}' in bean '{bean_name}'", bean_name) from None
            return None
        except Exception as e:
            if required:
                raise BeanCreationError(f"Failed to resolve dependency '{dependency_type.__name__}' for field '{field_name}'", bean_name, e) from e
            return None

    def _resolve_method_dependencies(self, method, bean_name: str, required: bool, qualifier: Optional[str]) -> list[Any]:
        """
        解析方法参数依赖

        Args:
            method: 方法对象
            bean_name: Bean名称
            required: 是否必需
            qualifier: 限定符

        Returns:
            解析的参数列表
        """
        if self._bean_factory is None:
            if required:
                raise BeanCreationError("Bean factory not available for method dependency resolution", bean_name)
            return []

        # 使用缓存获取方法签名
        sig = self._type_cache.get_method_signature(method.__self__.__class__, method.__name__)
        if not sig:
            # 回退到直接获取
            sig = inspect.signature(method)

        args = []

        # 跳过self参数
        parameters = list(sig.parameters.values())[1:]

        for param in parameters:
            param_type = param.annotation
            if param_type == inspect.Parameter.empty:
                if required:
                    raise BeanCreationError(f"Parameter '{param.name}' in method '{method.__name__}' has no type annotation", bean_name)
                args.append(None)
                continue

            try:
                # 解析参数依赖
                dependency = self._bean_factory.get_bean(qualifier) if qualifier else self._bean_factory.get_bean_by_type(param_type)
                args.append(dependency)

            except Exception as e:
                if required:
                    raise BeanCreationError(f"Failed to resolve parameter '{param.name}' for method '{method.__name__}'", bean_name, e) from e
                args.append(None)

        return args

    def clear_injection_cache(self) -> None:
        """清理注入缓存

        清理所有缓存的注入信息,释放内存.
        通常在应用关闭或需要强制刷新缓存时调用.
        """
        self._cache_manager.clear_cache()
        logger.debug("Injection cache cleared")

    def get_cache_statistics(self) -> dict:
        """获取缓存统计信息

        Returns:
            包含缓存统计信息的字典
        """
        return self._cache_manager.get_cache_info()

    def set_cache_max_size(self, max_size: int) -> None:
        """设置缓存最大大小

        Args:
            max_size: 新的缓存最大大小

        Note:
            这会清理现有缓存并创建新的缓存管理器
        """
        if max_size != self._cache_manager._max_size:
            # 创建新的缓存管理器
            self._cache_manager = InjectionCacheManager(max_size=max_size)
            self._cache_manager._compute_injection_info = self._compute_injection_info_impl
            logger.debug(f"Injection cache max size updated to {max_size}")
