#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Actuator 安全中间件 - 端点访问控制和认证

提供端点级别的安全中间件,包括:
- HTTP 基础认证
- Bearer Token 认证
- 端点访问控制
- 安全头部处理
- CORS 配置
"""

import base64
import re
from typing import Any, Dict, Optional, Tuple

from fastapi import HTTPException, Request, Response
from fastapi.security import HTTPBasic, HTTPBasicCredentials, HTTPBearer
from loguru import logger

from .manager import ProductionSecurityManager, SecurityLevel, SecurityToken


class SecurityMiddleware:
    """Actuator 安全中间件"""

    def __init__(self, security_manager: ProductionSecurityManager):
        """初始化安全中间件

        Args:
            security_manager: 安全管理器
        """
        self.security_manager = security_manager
        self.http_basic = HTTPBasic()
        self.http_bearer = HTTPBearer(auto_error=False)

        logger.info("SecurityMiddleware initialized")

    async def authenticate_request(self, request: Request) -> Optional[SecurityToken]:
        """认证请求

        Args:
            request: HTTP 请求

        Returns:
            SecurityToken: 认证成功返回令牌,否则返回 None
        """
        # 获取客户端信息
        ip_address = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "unknown")

        # 尝试 Bearer Token 认证
        token = await self._authenticate_bearer_token(request, ip_address)
        if token:
            return token

        # 尝试 Basic 认证
        token = await self._authenticate_basic_auth(request, ip_address)
        if token:
            return token

        return None

    async def authorize_endpoint_access(self, request: Request, endpoint: str, token: Optional[SecurityToken] = None) -> bool:
        """授权端点访问

        Args:
            request: HTTP 请求
            endpoint: 端点名称
            token: 安全令牌

        Returns:
            bool: 是否有访问权限
        """
        ip_address = self._get_client_ip(request)
        token_id = token.token_id if token else None

        return self.security_manager.authorize_endpoint_access(token_id, endpoint, ip_address)

    def add_security_headers(self, response: Response) -> None:
        """添加安全头部

        Args:
            response: HTTP 响应
        """
        # 基础安全头部
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

        # 如果需要 HTTPS
        if self.security_manager.policy.get("require_https", False):
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        # 内容安全策略
        response.headers["Content-Security-Policy"] = "default-src 'self'; script-src 'none'; object-src 'none'"

        # 权限策略
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

    def check_https_requirement(self, request: Request) -> None:
        """检查 HTTPS 要求

        Args:
            request: HTTP 请求

        Raises:
            HTTPException: 如果需要 HTTPS 但请求不是 HTTPS
        """
        if self.security_manager.policy.get("require_https", False):
            # 检查是否为 HTTPS
            is_https = (
                request.url.scheme == "https" or request.headers.get("x-forwarded-proto") == "https" or request.headers.get("x-forwarded-ssl") == "on"
            )

            if not is_https:
                logger.warning(f"HTTPS required but request is not secure: {request.url}")
                raise HTTPException(status_code=426, detail="HTTPS Required", headers={"Upgrade": "TLS/1.2"})

    def validate_cors(self, request: Request) -> bool:
        """验证 CORS 请求

        Args:
            request: HTTP 请求

        Returns:
            bool: 是否允许 CORS 请求
        """
        if not self.security_manager.policy.get("cors_enabled", False):
            return False

        origin = request.headers.get("origin")
        if not origin:
            return True  # 非 CORS 请求

        # 检查允许的源
        allowed_origins = self.security_manager.config.allowed_origins
        if "*" in allowed_origins:
            return True

        return origin in allowed_origins

    async def _authenticate_bearer_token(self, request: Request, ip_address: str) -> Optional[SecurityToken]:
        """Bearer Token 认证"""
        try:
            authorization = request.headers.get("authorization")
            if not authorization or not authorization.startswith("Bearer "):
                return None

            token_id = authorization[7:]  # 移除 "Bearer " 前缀

            # 检查令牌是否存在且有效
            if token_id in self.security_manager.active_tokens:
                token = self.security_manager.active_tokens[token_id]
                if token.is_valid():
                    return token
                else:
                    # 清理过期令牌
                    del self.security_manager.active_tokens[token_id]

            return None

        except Exception as e:
            logger.warning(f"Bearer token authentication failed: {e}")
            return None

    async def _authenticate_basic_auth(self, request: Request, ip_address: str) -> Optional[SecurityToken]:
        """Basic 认证"""
        try:
            authorization = request.headers.get("authorization")
            if not authorization or not authorization.startswith("Basic "):
                return None

            # 解码 Basic 认证
            encoded_credentials = authorization[6:]  # 移除 "Basic " 前缀
            decoded_credentials = base64.b64decode(encoded_credentials).decode("utf-8")
            username, password = decoded_credentials.split(":", 1)

            # 使用安全管理器进行认证
            token = self.security_manager.authenticate(username, password, ip_address)
            return token

        except Exception as e:
            logger.warning(f"Basic authentication failed: {e}")
            return None

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端 IP 地址"""
        # 检查代理头部
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # 取第一个 IP(客户端真实 IP)
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        # 使用客户端地址
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"


class SecurityExceptionHandler:
    """安全异常处理器"""

    @staticmethod
    def handle_authentication_required() -> HTTPException:
        """处理需要认证的异常"""
        return HTTPException(status_code=401, detail="Authentication required", headers={"WWW-Authenticate": 'Basic realm="Actuator", Bearer'})

    @staticmethod
    def handle_access_denied(endpoint: str) -> HTTPException:
        """处理访问被拒绝的异常"""
        return HTTPException(status_code=403, detail=f"Access denied to endpoint: {endpoint}")

    @staticmethod
    def handle_endpoint_not_allowed(endpoint: str) -> HTTPException:
        """处理端点不被允许的异常"""
        return HTTPException(status_code=404, detail=f"Endpoint not available: {endpoint}")

    @staticmethod
    def handle_rate_limit_exceeded() -> HTTPException:
        """处理速率限制超出的异常"""
        return HTTPException(status_code=429, detail="Too many requests", headers={"Retry-After": "60"})

    @staticmethod
    def handle_user_locked(username: str) -> HTTPException:
        """处理用户被锁定的异常"""
        return HTTPException(status_code=423, detail=f"User account locked: {username}")
