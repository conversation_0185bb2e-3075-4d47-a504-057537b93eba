#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频播放相关数据模型
"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Optional


class AudioItemType(Enum):
    """音频项目类型"""

    FILE = "file"  # 音频文件
    TEXT = "text"  # 文本语音


@dataclass
class AudioItem:
    """音频播放项目"""

    type: AudioItemType
    content: str
    options: Optional[dict[str, Any]] = None

    @classmethod
    def file(cls, file_path: str, **options) -> "AudioItem":
        """创建音频文件项目

        Args:
            file_path: 音频文件路径
            **options: 播放选项

        Returns:
            AudioItem: 音频文件项目
        """
        return cls(AudioItemType.FILE, file_path, options if options else None)

    @classmethod
    def text(cls, text: str, **options) -> "AudioItem":
        """创建文本语音项目

        Args:
            text: 文本内容
            **options: TTS选项

        Returns:
            AudioItem: 文本语音项目
        """
        return cls(AudioItemType.TEXT, text, options if options else None)
