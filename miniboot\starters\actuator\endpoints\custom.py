#!/usr/bin/env python
"""
* @author: cz
* @description: 自定义端点

提供用户自定义监控端点的功能,支持灵活的端点定义和处理逻辑.
"""

import inspect
from abc import ABC, abstractmethod
from typing import Any, Callable, Optional, Union

from .base import BaseEndpoint, EndpointOperation, OperationType


class CustomEndpointHandler(ABC):
    """自定义端点处理器基类"""

    @abstractmethod
    def handle(self, **kwargs) -> Any:
        """处理端点请求"""
        pass

    def get_description(self) -> str:
        """获取处理器描述"""
        return self.__class__.__doc__ or "Custom endpoint handler"


class FunctionEndpointHandler(CustomEndpointHandler):
    """基于函数的端点处理器"""

    def __init__(self, func: Callable, description: str = ""):
        """
        初始化函数处理器

        Args:
            func: 处理函数
            description: 处理器描述
        """
        self.func = func
        self.description = description or func.__doc__ or "Function-based endpoint handler"

    def handle(self, **kwargs) -> Any:
        """处理端点请求"""
        # 检查函数签名,只传递函数需要的参数
        sig = inspect.signature(self.func)
        filtered_kwargs = {}

        for param_name in sig.parameters:
            if param_name in kwargs:
                filtered_kwargs[param_name] = kwargs[param_name]

        return self.func(**filtered_kwargs)

    def get_description(self) -> str:
        """获取处理器描述"""
        return self.description


class LambdaEndpointHandler(CustomEndpointHandler):
    """基于Lambda的端点处理器"""

    def __init__(self, lambda_func: Callable, description: str = "Lambda endpoint handler"):
        """
        初始化Lambda处理器

        Args:
            lambda_func: Lambda函数
            description: 处理器描述
        """
        self.lambda_func = lambda_func
        self.description = description

    def handle(self, **kwargs) -> Any:
        """处理端点请求"""
        try:
            # 尝试传递所有参数
            return self.lambda_func(**kwargs)
        except TypeError:
            # 如果参数不匹配,尝试无参数调用
            try:
                return self.lambda_func()
            except TypeError:
                # 如果还是不行,传递kwargs字典
                return self.lambda_func(kwargs)

    def get_description(self) -> str:
        """获取处理器描述"""
        return self.description


class CustomEndpoint(BaseEndpoint):
    """自定义端点实现"""

    def __init__(self, endpoint_id: str, enabled: bool = True, sensitive: bool = False, description: str = ""):
        """
        初始化自定义端点

        Args:
            endpoint_id: 端点ID
            enabled: 是否启用
            sensitive: 是否敏感
            description: 端点描述
        """
        super().__init__(endpoint_id, enabled, sensitive)
        self.description = description
        self.handlers: dict[OperationType, CustomEndpointHandler] = {}
        self.metadata: dict[str, Any] = {}

    def add_handler(
        self,
        operation_type: OperationType,
        handler: Union[CustomEndpointHandler, Callable],
        method: str = "GET",  # noqa: ARG002
        path: str = "",  # noqa: ARG002
        description: str = "",
    ) -> "CustomEndpoint":
        """
        添加操作处理器

        Args:
            operation_type: 操作类型
            handler: 处理器或函数
            method: HTTP方法
            path: 子路径
            description: 描述

        Returns:
            自身,支持链式调用
        """
        if isinstance(handler, CustomEndpointHandler):
            self.handlers[operation_type] = handler
        elif callable(handler):
            self.handlers[operation_type] = FunctionEndpointHandler(handler, description)
        else:
            raise TypeError("Handler must be a CustomEndpointHandler or callable")

        # 清空操作缓存,强制重新创建
        self.clear_operations_cache()
        return self

    def add_read_handler(self, handler: Union[CustomEndpointHandler, Callable], description: str = "") -> "CustomEndpoint":
        """添加READ操作处理器(快捷方法)"""
        return self.add_handler(OperationType.READ, handler, "GET", "", description)

    def add_write_handler(self, handler: Union[CustomEndpointHandler, Callable], description: str = "") -> "CustomEndpoint":
        """添加WRITE操作处理器(快捷方法)"""
        return self.add_handler(OperationType.WRITE, handler, "POST", "", description)

    def add_delete_handler(self, handler: Union[CustomEndpointHandler, Callable], description: str = "") -> "CustomEndpoint":
        """添加DELETE操作处理器(快捷方法)"""
        return self.add_handler(OperationType.DELETE, handler, "DELETE", "", description)

    def set_metadata(self, key: str, value: Any) -> "CustomEndpoint":
        """设置元数据"""
        self.metadata[key] = value
        return self

    def get_metadata(self, key: str, default: Any = None) -> Any:
        """获取元数据"""
        return self.metadata.get(key, default)

    def _create_operations(self) -> list[EndpointOperation]:
        """创建操作列表"""
        operations = []

        for operation_type in self.handlers:
            method = "GET"
            if operation_type == OperationType.WRITE:
                method = "POST"
            elif operation_type == OperationType.DELETE:
                method = "DELETE"

            operations.append(
                EndpointOperation(
                    operation_type=operation_type, method=method, handler=lambda op_type=operation_type: self._handle_operation(op_type)
                )
            )

        return operations

    def invoke(self, operation_type: OperationType, **kwargs) -> Any:
        """执行端点操作"""
        return self._handle_operation(operation_type, **kwargs)

    def _handle_operation(self, operation_type: OperationType, **kwargs) -> Any:
        """处理操作"""
        if operation_type not in self.handlers:
            return {"error": f"Unsupported operation: {operation_type}", "operation": operation_type.value, "endpoint": self.id}

        handler = self.handlers[operation_type]
        try:
            return handler.handle(**kwargs)
        except Exception as e:
            return {"error": str(e), "operation": operation_type.value, "endpoint": self.id}

    def get_info(self) -> dict[str, Any]:
        """获取端点信息"""
        return {
            "id": self.id,
            "description": self.description,
            "enabled": self.enabled,
            "sensitive": self.sensitive,
            "operations": [op.operation_type.value for op in self.operations()],
            "handlers": {op_type.value: handler.get_description() for op_type, handler in self.handlers.items()},
            "metadata": self.metadata,
        }


class CustomEndpointBuilder:
    """自定义端点构建器"""

    def __init__(self, endpoint_id: str):
        """
        初始化构建器

        Args:
            endpoint_id: 端点ID
        """
        self.endpoint_id = endpoint_id
        self._enabled = True
        self._sensitive = False
        self._description = ""
        self._handlers: list[tuple] = []
        self._metadata: dict[str, Any] = {}

    def enabled(self, enabled: bool = True) -> "CustomEndpointBuilder":
        """设置是否启用"""
        self._enabled = enabled
        return self

    def sensitive(self, sensitive: bool = True) -> "CustomEndpointBuilder":
        """设置是否敏感"""
        self._sensitive = sensitive
        return self

    def description(self, description: str) -> "CustomEndpointBuilder":
        """设置描述"""
        self._description = description
        return self

    def on_read(self, handler: Union[CustomEndpointHandler, Callable], description: str = "") -> "CustomEndpointBuilder":
        """添加READ处理器"""
        self._handlers.append((OperationType.READ, handler, description))
        return self

    def on_write(self, handler: Union[CustomEndpointHandler, Callable], description: str = "") -> "CustomEndpointBuilder":
        """添加WRITE处理器"""
        self._handlers.append((OperationType.WRITE, handler, description))
        return self

    def on_delete(self, handler: Union[CustomEndpointHandler, Callable], description: str = "") -> "CustomEndpointBuilder":
        """添加DELETE处理器"""
        self._handlers.append((OperationType.DELETE, handler, description))
        return self

    def metadata(self, key: str, value: Any) -> "CustomEndpointBuilder":
        """添加元数据"""
        self._metadata[key] = value
        return self

    def build(self) -> CustomEndpoint:
        """构建自定义端点"""
        endpoint = CustomEndpoint(endpoint_id=self.endpoint_id, enabled=self._enabled, sensitive=self._sensitive, description=self._description)

        # 添加处理器
        for operation_type, handler, description in self._handlers:
            endpoint.add_handler(operation_type, handler, description=description)

        # 设置元数据
        for key, value in self._metadata.items():
            endpoint.set_metadata(key, value)

        return endpoint


def endpoint(endpoint_id: str) -> CustomEndpointBuilder:
    """创建自定义端点构建器的快捷函数"""
    return CustomEndpointBuilder(endpoint_id)


# 装饰器支持
class EndpointDecorator:
    """端点装饰器类"""

    def __init__(self, endpoint_id: str, operation_type: OperationType, description: str = ""):
        self.endpoint_id = endpoint_id
        self.operation_type = operation_type
        self.description = description

    def __call__(self, func: Callable) -> Callable:
        """装饰器调用"""
        # 在函数上添加端点元数据
        if not hasattr(func, "_endpoint_metadata"):
            func._endpoint_metadata = {}

        func._endpoint_metadata[self.operation_type] = {
            "endpoint_id": self.endpoint_id,
            "description": self.description or func.__doc__ or "",
            "function": func,
        }

        return func


def read(endpoint_id: str, description: str = ""):
    """READ端点装饰器"""
    return EndpointDecorator(endpoint_id, OperationType.READ, description)


def write(endpoint_id: str, description: str = ""):
    """WRITE端点装饰器"""
    return EndpointDecorator(endpoint_id, OperationType.WRITE, description)


def delete(endpoint_id: str, description: str = ""):
    """DELETE端点装饰器"""
    return EndpointDecorator(endpoint_id, OperationType.DELETE, description)


class EndpointRegistry:
    """端点注册表(扩展版)"""

    @staticmethod
    def create_from_decorated_functions(*objects) -> list[CustomEndpoint]:
        """从装饰的函数创建端点"""
        endpoints_map: dict[str, CustomEndpoint] = {}

        for obj in objects:
            # 获取对象的所有方法
            if inspect.isclass(obj):
                instance = obj()
                methods = [
                    getattr(instance, method_name)
                    for method_name in dir(instance)
                    if callable(getattr(instance, method_name)) and not method_name.startswith("_")
                ]
            else:
                methods = [
                    getattr(obj, method_name) for method_name in dir(obj) if callable(getattr(obj, method_name)) and not method_name.startswith("_")
                ]

            # 检查每个方法是否有端点元数据
            for method in methods:
                if hasattr(method, "_endpoint_metadata"):
                    for operation_type, metadata in method._endpoint_metadata.items():
                        endpoint_id = metadata["endpoint_id"]

                        # 如果端点不存在,创建它
                        if endpoint_id not in endpoints_map:
                            endpoints_map[endpoint_id] = CustomEndpoint(
                                endpoint_id=endpoint_id, description=f"Auto-generated endpoint: {endpoint_id}"
                            )

                        # 添加处理器
                        endpoints_map[endpoint_id].add_handler(operation_type=operation_type, handler=method, description=metadata["description"])

        return list(endpoints_map.values())


# 预定义的常用端点处理器
class StatusEndpointHandler(CustomEndpointHandler):
    """状态端点处理器"""

    def __init__(self, status_provider: Optional[Callable] = None):
        self.status_provider = status_provider

    def handle(self, **kwargs) -> Any:  # noqa: ARG002
        """处理状态请求"""
        if self.status_provider:
            return self.status_provider()

        return {"status": "UP", "timestamp": __import__("datetime").datetime.now().isoformat(), "message": "Service is running"}

    def get_description(self) -> str:
        return "Provides service status information"


class ConfigEndpointHandler(CustomEndpointHandler):
    """配置端点处理器"""

    def __init__(self, config_provider: Optional[Callable] = None):
        self.config_provider = config_provider

    def handle(self, **kwargs) -> Any:  # noqa: ARG002
        """处理配置请求"""
        if self.config_provider:
            return self.config_provider()

        return {"message": "No configuration provider set", "timestamp": __import__("datetime").datetime.now().isoformat()}

    def get_description(self) -> str:
        return "Provides configuration information"


class VersionEndpointHandler(CustomEndpointHandler):
    """版本端点处理器"""

    def __init__(self, version: str = "1.0.0", build_info: Optional[dict] = None):
        self.version = version
        self.build_info = build_info or {}

    def handle(self, **kwargs) -> Any:  # noqa: ARG002
        """处理版本请求"""
        return {"version": self.version, "build": self.build_info, "timestamp": __import__("datetime").datetime.now().isoformat()}

    def get_description(self) -> str:
        return f"Provides version information (v{self.version})"
