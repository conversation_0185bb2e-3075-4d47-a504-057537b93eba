#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: MonitoringAlerts - 监控告警集成系统

提供全面的监控告警功能,支持:
- 阈值监控和告警
- 健康状态变化通知
- 多种告警通道
- 告警规则管理
- 告警历史记录
- 智能告警抑制
"""

import asyncio
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Union

from loguru import logger

from ..performance.metrics import PerformanceMetrics, get_metrics_registry


class AlertSeverity(Enum):
    """告警严重级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态"""

    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ACKNOWLEDGED = "acknowledged"


class AlertChannel(Enum):
    """告警通道"""

    LOG = "log"
    EMAIL = "email"
    WEBHOOK = "webhook"
    SMS = "sms"
    SLACK = "slack"
    CONSOLE = "console"


@dataclass
class AlertRule:
    """告警规则"""

    rule_id: str
    name: str
    description: str
    metric_name: str
    condition: str  # 条件表达式,如 "> 0.8", "< 100", "== 'DOWN'"
    threshold: Union[float, int, str]
    severity: AlertSeverity
    enabled: bool = True
    channels: List[AlertChannel] = field(default_factory=list)
    tags: Dict[str, str] = field(default_factory=dict)

    # 告警抑制配置
    cooldown_seconds: int = 300  # 冷却时间(秒)
    max_alerts_per_hour: int = 10  # 每小时最大告警数

    def evaluate(self, value: Any) -> bool:
        """评估告警条件

        Args:
            value: 指标值

        Returns:
            bool: 是否触发告警
        """
        try:
            if isinstance(value, str):
                if self.condition.startswith("=="):
                    return value == self.threshold
                elif self.condition.startswith("!="):
                    return value != self.threshold
                else:
                    return False

            # 数值比较
            numeric_value = float(value)
            threshold_value = float(self.threshold)

            if self.condition.startswith(">="):
                return numeric_value >= threshold_value
            elif self.condition.startswith("<="):
                return numeric_value <= threshold_value
            elif self.condition.startswith(">"):
                return numeric_value > threshold_value
            elif self.condition.startswith("<"):
                return numeric_value < threshold_value
            elif self.condition.startswith("=="):
                return numeric_value == threshold_value
            elif self.condition.startswith("!="):
                return numeric_value != threshold_value
            else:
                return False

        except (ValueError, TypeError):
            return False


@dataclass
class Alert:
    """告警实例"""

    alert_id: str
    rule_id: str
    rule_name: str
    metric_name: str
    metric_value: Any
    threshold: Union[float, int, str]
    severity: AlertSeverity
    status: AlertStatus
    message: str
    created_at: datetime
    updated_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "alert_id": self.alert_id,
            "rule_id": self.rule_id,
            "rule_name": self.rule_name,
            "metric_name": self.metric_name,
            "metric_value": self.metric_value,
            "threshold": self.threshold,
            "severity": self.severity.value,
            "status": self.status.value,
            "message": self.message,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "acknowledged_at": self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            "acknowledged_by": self.acknowledged_by,
            "tags": self.tags,
            "duration_seconds": ((self.resolved_at or datetime.now()) - self.created_at).total_seconds(),
        }


@dataclass
class AlertStats:
    """告警统计"""

    total_alerts: int = 0
    active_alerts: int = 0
    resolved_alerts: int = 0
    suppressed_alerts: int = 0
    acknowledged_alerts: int = 0
    alerts_by_severity: Dict[AlertSeverity, int] = field(default_factory=lambda: defaultdict(int))
    alerts_by_rule: Dict[str, int] = field(default_factory=lambda: defaultdict(int))

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_alerts": self.total_alerts,
            "active_alerts": self.active_alerts,
            "resolved_alerts": self.resolved_alerts,
            "suppressed_alerts": self.suppressed_alerts,
            "acknowledged_alerts": self.acknowledged_alerts,
            "alerts_by_severity": {k.value: v for k, v in self.alerts_by_severity.items()},
            "alerts_by_rule": dict(self.alerts_by_rule),
        }


class AlertNotifier:
    """告警通知器"""

    def __init__(self):
        """初始化告警通知器"""
        self.handlers: Dict[AlertChannel, Callable] = {}
        self._setup_default_handlers()

    def _setup_default_handlers(self) -> None:
        """设置默认处理器"""
        self.handlers[AlertChannel.LOG] = self._log_handler
        self.handlers[AlertChannel.CONSOLE] = self._console_handler

    def register_handler(self, channel: AlertChannel, handler: Callable) -> None:
        """注册告警处理器

        Args:
            channel: 告警通道
            handler: 处理器函数
        """
        self.handlers[channel] = handler
        logger.debug(f"Registered alert handler for channel: {channel.value}")

    async def send_alert(self, alert: Alert, channels: List[AlertChannel]) -> Dict[AlertChannel, bool]:
        """发送告警

        Args:
            alert: 告警实例
            channels: 告警通道列表

        Returns:
            Dict[AlertChannel, bool]: 各通道发送结果
        """
        results = {}

        for channel in channels:
            if channel in self.handlers:
                try:
                    handler = self.handlers[channel]
                    if asyncio.iscoroutinefunction(handler):
                        await handler(alert)
                    else:
                        handler(alert)
                    results[channel] = True
                except Exception as e:
                    logger.error(f"Failed to send alert via {channel.value}: {e}")
                    results[channel] = False
            else:
                logger.warning(f"No handler registered for channel: {channel.value}")
                results[channel] = False

        return results

    def _log_handler(self, alert: Alert) -> None:
        """日志处理器"""
        level_map = {
            AlertSeverity.INFO: logger.info,
            AlertSeverity.WARNING: logger.warning,
            AlertSeverity.ERROR: logger.error,
            AlertSeverity.CRITICAL: logger.critical,
        }

        log_func = level_map.get(alert.severity, logger.info)
        log_func(f"ALERT [{alert.severity.value.upper()}] {alert.rule_name}: {alert.message}")

    def _console_handler(self, alert: Alert) -> None:
        """控制台处理器"""
        severity_icons = {AlertSeverity.INFO: "ℹ️", AlertSeverity.WARNING: "⚠️", AlertSeverity.ERROR: "❌", AlertSeverity.CRITICAL: "🚨"}

        icon = severity_icons.get(alert.severity, "📢")
        print(f"{icon} [{alert.severity.value.upper()}] {alert.rule_name}: {alert.message}")


class MonitoringAlerts:
    """监控告警系统"""

    def __init__(self, check_interval: float = 30.0, max_alert_history: int = 1000, enable_suppression: bool = True):
        """初始化监控告警系统

        Args:
            check_interval: 检查间隔(秒)
            max_alert_history: 最大告警历史记录数
            enable_suppression: 是否启用告警抑制
        """
        self.check_interval = check_interval
        self.max_alert_history = max_alert_history
        self.enable_suppression = enable_suppression

        # 告警规则
        self.rules: Dict[str, AlertRule] = {}

        # 告警历史
        self.alerts: deque[Alert] = deque(maxlen=max_alert_history)
        self.active_alerts: Dict[str, Alert] = {}

        # 告警抑制
        self.suppression_tracker: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))

        # 通知器
        self.notifier = AlertNotifier()

        # 性能指标注册表
        self.metrics_registry = get_metrics_registry()

        # 运行状态
        self._running = False
        self._monitor_task: Optional[asyncio.Task] = None

        # 线程安全
        self._lock = threading.RLock()

        logger.info("MonitoringAlerts initialized")

    async def start(self) -> None:
        """启动监控告警系统"""
        if self._running:
            logger.warning("MonitoringAlerts already running")
            return

        self._running = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())

        logger.info("MonitoringAlerts started")

    async def stop(self) -> None:
        """停止监控告警系统"""
        if not self._running:
            return

        self._running = False

        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

        logger.info("MonitoringAlerts stopped")

    def add_rule(self, rule: AlertRule) -> None:
        """添加告警规则

        Args:
            rule: 告警规则
        """
        with self._lock:
            self.rules[rule.rule_id] = rule
            logger.debug(f"Added alert rule: {rule.name}")

    def remove_rule(self, rule_id: str) -> bool:
        """移除告警规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            if rule_id in self.rules:
                del self.rules[rule_id]
                logger.debug(f"Removed alert rule: {rule_id}")
                return True
            return False

    def get_rule(self, rule_id: str) -> Optional[AlertRule]:
        """获取告警规则

        Args:
            rule_id: 规则ID

        Returns:
            Optional[AlertRule]: 告警规则
        """
        return self.rules.get(rule_id)

    def get_rules(self) -> List[AlertRule]:
        """获取所有告警规则"""
        return list(self.rules.values())

    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self._running:
            try:
                await self._check_alerts()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(5.0)

    async def _check_alerts(self) -> None:
        """检查告警"""
        current_time = datetime.now()

        # 获取所有性能指标
        all_metrics = self.metrics_registry.get_all_metrics()

        for rule in self.rules.values():
            if not rule.enabled:
                continue

            try:
                # 获取指标值
                metric_value = await self._get_metric_value(rule.metric_name, all_metrics)

                if metric_value is None:
                    continue

                # 评估告警条件
                should_alert = rule.evaluate(metric_value)

                if should_alert:
                    await self._handle_alert_triggered(rule, metric_value, current_time)
                else:
                    await self._handle_alert_resolved(rule, current_time)

            except Exception as e:
                logger.error(f"Error checking rule {rule.rule_id}: {e}")

    async def _get_metric_value(self, metric_name: str, all_metrics: Dict[str, Any]) -> Optional[Any]:
        """获取指标值"""
        # 支持点分隔的指标路径,如 "performance.response_time.average_time"
        parts = metric_name.split(".")

        if len(parts) >= 2:
            metrics_name = parts[0]
            if metrics_name in all_metrics:
                metrics = all_metrics[metrics_name]
                summary = metrics.get_summary()

                # 导航到指定的指标值
                current = summary
                for part in parts[1:]:
                    if isinstance(current, dict) and part in current:
                        current = current[part]
                    else:
                        return None

                return current

        return None

    async def _handle_alert_triggered(self, rule: AlertRule, metric_value: Any, current_time: datetime) -> None:
        """处理告警触发"""
        alert_key = f"{rule.rule_id}_{rule.metric_name}"

        # 检查是否已有活跃告警
        if alert_key in self.active_alerts:
            return

        # 检查告警抑制
        if self.enable_suppression and self._is_suppressed(rule, current_time):
            return

        # 创建告警
        alert = Alert(
            alert_id=f"alert_{int(time.time() * 1000)}_{rule.rule_id}",
            rule_id=rule.rule_id,
            rule_name=rule.name,
            metric_name=rule.metric_name,
            metric_value=metric_value,
            threshold=rule.threshold,
            severity=rule.severity,
            status=AlertStatus.ACTIVE,
            message=f"{rule.description}: {metric_value} {rule.condition} {rule.threshold}",
            created_at=current_time,
            updated_at=current_time,
            tags=rule.tags.copy(),
        )

        # 记录告警
        with self._lock:
            self.alerts.append(alert)
            self.active_alerts[alert_key] = alert

            # 更新抑制跟踪
            if self.enable_suppression:
                self.suppression_tracker[rule.rule_id].append(current_time)

        # 发送通知
        if rule.channels:
            await self.notifier.send_alert(alert, rule.channels)

        logger.info(f"Alert triggered: {alert.rule_name} - {alert.message}")

    async def _handle_alert_resolved(self, rule: AlertRule, current_time: datetime) -> None:
        """处理告警解决"""
        alert_key = f"{rule.rule_id}_{rule.metric_name}"

        if alert_key in self.active_alerts:
            alert = self.active_alerts[alert_key]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = current_time
            alert.updated_at = current_time

            # 从活跃告警中移除
            with self._lock:
                del self.active_alerts[alert_key]

            logger.info(f"Alert resolved: {alert.rule_name}")

    def _is_suppressed(self, rule: AlertRule, current_time: datetime) -> bool:
        """检查告警是否被抑制"""
        rule_alerts = self.suppression_tracker[rule.rule_id]

        if not rule_alerts:
            return False

        # 检查冷却时间
        last_alert = rule_alerts[-1]
        if (current_time - last_alert).total_seconds() < rule.cooldown_seconds:
            return True

        # 检查每小时告警数限制
        hour_ago = current_time - timedelta(hours=1)
        recent_alerts = [alert_time for alert_time in rule_alerts if alert_time > hour_ago]

        if len(recent_alerts) >= rule.max_alerts_per_hour:
            return True

        return False

    def get_alert_stats(self) -> AlertStats:
        """获取告警统计"""
        with self._lock:
            stats = AlertStats()

            for alert in self.alerts:
                stats.total_alerts += 1
                stats.alerts_by_severity[alert.severity] += 1
                stats.alerts_by_rule[alert.rule_id] += 1

                if alert.status == AlertStatus.ACTIVE:
                    stats.active_alerts += 1
                elif alert.status == AlertStatus.RESOLVED:
                    stats.resolved_alerts += 1
                elif alert.status == AlertStatus.SUPPRESSED:
                    stats.suppressed_alerts += 1
                elif alert.status == AlertStatus.ACKNOWLEDGED:
                    stats.acknowledged_alerts += 1

            return stats

    def get_alerts(self, limit: int = 50, severity: Optional[AlertSeverity] = None, status: Optional[AlertStatus] = None) -> List[Dict[str, Any]]:
        """获取告警列表"""
        with self._lock:
            filtered_alerts = []

            for alert in reversed(self.alerts):
                if severity and alert.severity != severity:
                    continue
                if status and alert.status != status:
                    continue

                filtered_alerts.append(alert.to_dict())

                if len(filtered_alerts) >= limit:
                    break

            return filtered_alerts

    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """确认告警

        Args:
            alert_id: 告警ID
            acknowledged_by: 确认人

        Returns:
            bool: 是否成功确认
        """
        with self._lock:
            for alert in self.alerts:
                if alert.alert_id == alert_id and alert.status == AlertStatus.ACTIVE:
                    alert.status = AlertStatus.ACKNOWLEDGED
                    alert.acknowledged_at = datetime.now()
                    alert.acknowledged_by = acknowledged_by
                    alert.updated_at = datetime.now()

                    logger.info(f"Alert acknowledged: {alert.rule_name} by {acknowledged_by}")
                    return True

            return False
