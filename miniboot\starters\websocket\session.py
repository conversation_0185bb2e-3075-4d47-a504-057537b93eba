#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 会话管理
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Any, Optional

from fastapi import WebSocket
from fastapi.websockets import WebSocketState

from .exceptions import WebSocketSessionException


class WebSocketSession:
    """WebSocket 会话类

    封装 WebSocket 连接的会话信息和操作方法,提供统一的会话管理接口.
    """

    def __init__(self, websocket: WebSocket, session_id: Optional[str] = None):
        """初始化 WebSocket 会话

        Args:
            websocket: FastAPI WebSocket 连接对象
            session_id: 会话 ID,如果为 None 则自动生成
        """
        self.websocket = websocket
        self.session_id = session_id or str(uuid.uuid4())
        self.user_id: Optional[str] = None
        self.attributes: dict[str, Any] = {}
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self._closed = False

    def get_id(self) -> str:
        """获取会话 ID

        Returns:
            str: 会话 ID
        """
        return self.session_id

    def get_user_id(self) -> Optional[str]:
        """获取用户 ID

        Returns:
            Optional[str]: 用户 ID,如果未设置返回 None
        """
        return self.user_id

    def set_user_id(self, user_id: str) -> None:
        """设置用户 ID

        Args:
            user_id: 用户 ID
        """
        self.user_id = user_id
        self.last_activity = datetime.now()

    def get_attribute(self, key: str, default: Any = None) -> Any:
        """获取会话属性

        Args:
            key: 属性键
            default: 默认值

        Returns:
            Any: 属性值
        """
        return self.attributes.get(key, default)

    def set_attribute(self, key: str, value: Any) -> None:
        """设置会话属性

        Args:
            key: 属性键
            value: 属性值
        """
        self.attributes[key] = value
        self.last_activity = datetime.now()

    def remove_attribute(self, key: str) -> Any:
        """移除会话属性

        Args:
            key: 属性键

        Returns:
            Any: 被移除的属性值
        """
        self.last_activity = datetime.now()
        return self.attributes.pop(key, None)

    def get_all_attributes(self) -> dict[str, Any]:
        """获取所有会话属性

        Returns:
            Dict[str, Any]: 所有属性的副本
        """
        return self.attributes.copy()

    async def send_text(self, message: str) -> None:
        """发送文本消息

        Args:
            message: 文本消息

        Raises:
            WebSocketSessionException: 发送失败时抛出
        """
        try:
            if not self.is_active():
                raise WebSocketSessionException("Session is not active")

            await self.websocket.send_text(message)
            self.last_activity = datetime.now()
        except Exception as e:
            raise WebSocketSessionException(f"Failed to send text message: {e}", cause=e)

    async def send_bytes(self, data: bytes) -> None:
        """发送二进制消息

        Args:
            data: 二进制数据

        Raises:
            WebSocketSessionException: 发送失败时抛出
        """
        try:
            if not self.is_active():
                raise WebSocketSessionException("Session is not active")

            await self.websocket.send_bytes(data)
            self.last_activity = datetime.now()
        except Exception as e:
            raise WebSocketSessionException(f"Failed to send bytes message: {e}", cause=e)

    async def send_json(self, data: Any) -> None:
        """发送 JSON 消息

        Args:
            data: 要发送的数据

        Raises:
            WebSocketSessionException: 发送失败时抛出
        """
        try:
            if not self.is_active():
                raise WebSocketSessionException("Session is not active")

            await self.websocket.send_json(data)
            self.last_activity = datetime.now()
        except Exception as e:
            raise WebSocketSessionException(f"Failed to send JSON message: {e}", cause=e)

    async def close(self, code: int = 1000, reason: str = "") -> None:
        """关闭连接

        Args:
            code: 关闭代码
            reason: 关闭原因
        """
        try:
            if not self._closed and self.is_active():
                await self.websocket.close(code, reason)
            self._closed = True
        except Exception:
            # 忽略关闭时的异常
            self._closed = True

    def is_active(self) -> bool:
        """检查连接是否活跃

        Returns:
            bool: 连接是否活跃
        """
        return not self._closed and hasattr(self.websocket, "client_state") and self.websocket.client_state == WebSocketState.CONNECTED

    def get_duration(self) -> timedelta:
        """获取会话持续时间

        Returns:
            timedelta: 会话持续时间
        """
        return datetime.now() - self.created_at

    def get_idle_time(self) -> timedelta:
        """获取空闲时间

        Returns:
            timedelta: 空闲时间
        """
        return datetime.now() - self.last_activity

    def __str__(self) -> str:
        """返回会话的字符串表示"""
        return f"WebSocketSession(id={self.session_id}, user_id={self.user_id}, active={self.is_active()})"

    def __repr__(self) -> str:
        """返回会话的详细字符串表示"""
        return (
            f"WebSocketSession(id={self.session_id}, user_id={self.user_id}, "
            f"active={self.is_active()}, created_at={self.created_at}, "
            f"last_activity={self.last_activity})"
        )


class WebSocketSessionManager:
    """WebSocket 会话管理器

    负责管理所有 WebSocket 会话的生命周期,包括创建、存储、查找和清理会话.
    """

    def __init__(self):
        """初始化会话管理器"""
        self._sessions: dict[str, WebSocketSession] = {}
        self._user_sessions: dict[str, set[str]] = {}  # user_id -> session_ids
        self._lock = asyncio.Lock()

    async def create_session(self, websocket: WebSocket, user_id: Optional[str] = None) -> WebSocketSession:
        """创建新会话

        Args:
            websocket: WebSocket 连接
            user_id: 用户 ID

        Returns:
            WebSocketSession: 新创建的会话
        """
        session = WebSocketSession(websocket)
        if user_id:
            session.set_user_id(user_id)

        async with self._lock:
            self._sessions[session.get_id()] = session

            if user_id:
                if user_id not in self._user_sessions:
                    self._user_sessions[user_id] = set()
                self._user_sessions[user_id].add(session.get_id())

        return session

    async def get_session(self, session_id: str) -> Optional[WebSocketSession]:
        """获取会话

        Args:
            session_id: 会话 ID

        Returns:
            Optional[WebSocketSession]: 会话对象,如果不存在返回 None
        """
        return self._sessions.get(session_id)

    async def remove_session(self, session_id: str) -> Optional[WebSocketSession]:
        """移除会话

        Args:
            session_id: 会话 ID

        Returns:
            Optional[WebSocketSession]: 被移除的会话,如果不存在返回 None
        """
        async with self._lock:
            session = self._sessions.pop(session_id, None)

            if session and session.get_user_id():
                user_id = session.get_user_id()
                if user_id in self._user_sessions:
                    self._user_sessions[user_id].discard(session_id)
                    if not self._user_sessions[user_id]:
                        del self._user_sessions[user_id]

        return session

    async def get_user_sessions(self, user_id: str) -> list[WebSocketSession]:
        """获取用户的所有会话

        Args:
            user_id: 用户 ID

        Returns:
            List[WebSocketSession]: 用户的会话列表
        """
        session_ids = self._user_sessions.get(user_id, set())
        sessions = []

        for session_id in session_ids:
            session = self._sessions.get(session_id)
            if session and session.is_active():
                sessions.append(session)

        return sessions

    async def get_all_sessions(self) -> list[WebSocketSession]:
        """获取所有活跃会话

        Returns:
            List[WebSocketSession]: 所有活跃会话列表
        """
        return [session for session in self._sessions.values() if session.is_active()]

    async def get_session_count(self) -> int:
        """获取活跃会话数量

        Returns:
            int: 活跃会话数量
        """
        return len([session for session in self._sessions.values() if session.is_active()])

    async def broadcast(self, message: Any, exclude_sessions: Optional[set[str]] = None) -> int:
        """广播消息给所有活跃会话

        Args:
            message: 要广播的消息
            exclude_sessions: 要排除的会话 ID 集合

        Returns:
            int: 成功发送的会话数量
        """
        exclude_sessions = exclude_sessions or set()
        tasks = []

        for session in self._sessions.values():
            if session.is_active() and session.get_id() not in exclude_sessions:
                tasks.append(session.send_json(message))

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return sum(1 for result in results if not isinstance(result, Exception))

        return 0

    async def send_to_user(self, user_id: str, message: Any) -> int:
        """发送消息给指定用户的所有会话

        Args:
            user_id: 用户 ID
            message: 要发送的消息

        Returns:
            int: 成功发送的会话数量
        """
        sessions = await self.get_user_sessions(user_id)
        tasks = [session.send_json(message) for session in sessions]

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return sum(1 for result in results if not isinstance(result, Exception))

        return 0

    async def cleanup_inactive_sessions(self, max_idle_time: timedelta = timedelta(hours=1)) -> int:
        """清理不活跃的会话

        Args:
            max_idle_time: 最大空闲时间

        Returns:
            int: 清理的会话数量
        """
        inactive_sessions = []

        for session in self._sessions.values():
            if not session.is_active() or session.get_idle_time() > max_idle_time:
                inactive_sessions.append(session.get_id())

        for session_id in inactive_sessions:
            await self.remove_session(session_id)

        return len(inactive_sessions)

    async def close_all_sessions(self, code: int = 1000, reason: str = "Server shutdown") -> None:
        """关闭所有会话

        Args:
            code: 关闭代码
            reason: 关闭原因
        """
        tasks = []

        for session in self._sessions.values():
            if session.is_active():
                tasks.append(session.close(code, reason))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        async with self._lock:
            self._sessions.clear()
            self._user_sessions.clear()
