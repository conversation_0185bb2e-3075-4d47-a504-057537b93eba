#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 指标聚合器 - 高性能异步实现

提供高性能的指标聚合功能,支持多种数据收集器的统一管理和聚合.

核心特性:
- 多收集器统一管理
- 异步并发数据收集
- 指标数据聚合和分析
- 智能缓存和性能优化
- 灵活的聚合策略
"""

import asyncio
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from loguru import logger

from miniboot.utils import timeout

from .health import AsyncHealthCollector
from .metrics import AppMetricsCollector
from .system import SystemMetricsCollector


class MetricsAggregator:
    """指标聚合器

    统一管理和聚合多种类型的指标收集器.
    """

    def __init__(self, timeout: float = 15.0, cache_ttl: float = 60.0):
        """初始化指标聚合器

        Args:
            timeout: 聚合超时时间(秒)
            cache_ttl: 缓存TTL(秒)
        """
        self.timeout = timeout
        self.cache_ttl = cache_ttl
        self._start_time = time.time()

        # 初始化收集器
        self.system_collector = SystemMetricsCollector(timeout=timeout - 5, cache_ttl=cache_ttl)
        self.application_collector = AppMetricsCollector(timeout=timeout - 5, cache_ttl=cache_ttl)
        self.health_collector = AsyncHealthCollector(timeout=timeout - 5)

        # 创建默认健康指标
        self.health_collector.create_default_indicators()

        logger.info("MetricsAggregator initialized with all collectors")

    @timeout(15.0)
    async def collect_all(self, include_system: bool = True, include_application: bool = True, include_health: bool = True) -> Dict[str, Any]:
        """收集所有指标"""
        try:
            start_time = time.time()
            tasks = []
            task_names = []

            # 根据参数决定收集哪些指标
            if include_system:
                tasks.append(self.system_collector.collect_all())
                task_names.append("system")

            if include_application:
                tasks.append(self.application_collector.collect_all())
                task_names.append("application")

            if include_health:
                tasks.append(self.health_collector.collect_health())
                task_names.append("health")

            if not tasks:
                return {"timestamp": datetime.now().isoformat(), "error": "No metrics collectors enabled"}

            # 并发执行所有收集任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            aggregated_metrics = {
                "timestamp": datetime.now().isoformat(),
                "collection_time_ms": round((time.time() - start_time) * 1000, 2),
                "collectors_executed": len(tasks),
            }

            for i, result in enumerate(results):
                collector_name = task_names[i]

                if isinstance(result, Exception):
                    aggregated_metrics[collector_name] = {"error": str(result), "status": "failed"}
                    logger.error(f"Collector {collector_name} failed: {result}")
                else:
                    aggregated_metrics[collector_name] = result
                    aggregated_metrics[collector_name]["status"] = "success"

            # 添加聚合摘要
            aggregated_metrics["summary"] = self._generate_summary(aggregated_metrics)

            return aggregated_metrics

        except Exception as e:
            logger.error(f"Metrics aggregation failed: {e}")
            return {"timestamp": datetime.now().isoformat(), "error": str(e), "status": "failed"}

    async def collect_specific(self, collector_type: str, metric_type: Optional[str] = None) -> Dict[str, Any]:
        """收集特定类型的指标"""
        try:
            start_time = time.time()

            if collector_type == "system":
                if metric_type:
                    result = await self.system_collector.collect_specific(metric_type)
                else:
                    result = await self.system_collector.collect_all()
            elif collector_type == "application":
                if metric_type:
                    result = await self.application_collector.collect_specific(metric_type)
                else:
                    result = await self.application_collector.collect_all()
            elif collector_type == "health":
                result = await self.health_collector.collect_health()
            else:
                raise ValueError(f"Unknown collector type: {collector_type}")

            # 添加聚合信息
            result["aggregator_info"] = {
                "collector_type": collector_type,
                "metric_type": metric_type,
                "collection_time_ms": round((time.time() - start_time) * 1000, 2),
                "aggregator_uptime": time.time() - self._start_time,
            }

            return result

        except Exception as e:
            logger.error(f"Specific metrics collection failed for {collector_type}: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "collector_type": collector_type,
                "metric_type": metric_type,
                "error": str(e),
                "status": "failed",
            }

    def _generate_summary(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """生成聚合摘要"""
        summary = {"total_collectors": 0, "successful_collectors": 0, "failed_collectors": 0, "overall_status": "unknown", "key_metrics": {}}

        # 统计收集器状态
        for key, value in metrics.items():
            if isinstance(value, dict) and "status" in value:
                summary["total_collectors"] += 1
                if value["status"] == "success":
                    summary["successful_collectors"] += 1
                else:
                    summary["failed_collectors"] += 1

        # 确定总体状态
        if summary["failed_collectors"] == 0:
            summary["overall_status"] = "healthy"
        elif summary["successful_collectors"] > 0:
            summary["overall_status"] = "degraded"
        else:
            summary["overall_status"] = "unhealthy"

        # 提取关键指标
        try:
            # 系统关键指标
            if "system" in metrics and "status" in metrics["system"] and metrics["system"]["status"] == "success":
                system_data = metrics["system"]
                if "cpu" in system_data and "error" not in system_data["cpu"]:
                    summary["key_metrics"]["cpu_usage"] = system_data["cpu"].get("usage_percent", 0)
                if "memory" in system_data and "error" not in system_data["memory"]:
                    memory_data = system_data["memory"]["virtual"]
                    summary["key_metrics"]["memory_usage"] = memory_data.get("percent", 0)

            # 应用关键指标
            if "application" in metrics and "status" in metrics["application"] and metrics["application"]["status"] == "success":
                app_data = metrics["application"]
                if "application" in app_data and "error" not in app_data["application"]:
                    app_metrics = app_data["application"]["performance"]
                    summary["key_metrics"]["total_requests"] = app_metrics.get("total_requests", 0)
                    summary["key_metrics"]["error_rate"] = app_metrics.get("error_rate", 0)

            # 健康关键指标
            if "health" in metrics and "status" in metrics["health"] and metrics["health"]["status"] == "success":
                health_data = metrics["health"]
                summary["key_metrics"]["health_status"] = health_data.get("status", "unknown")
                if "summary" in health_data:
                    health_summary = health_data["summary"]
                    summary["key_metrics"]["health_up_count"] = health_summary.get("up_count", 0)
                    summary["key_metrics"]["health_down_count"] = health_summary.get("down_count", 0)

        except Exception as e:
            logger.warning(f"Failed to extract key metrics: {e}")

        return summary

    async def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            health_result = await self.health_collector.collect_health()

            # 添加聚合器信息
            health_result["aggregator_info"] = {"uptime": time.time() - self._start_time, "collectors_available": ["system", "application", "health"]}

            return health_result

        except Exception as e:
            logger.error(f"Health status collection failed: {e}")
            return {"status": "DOWN", "timestamp": datetime.now().isoformat(), "error": str(e)}

    def record_request(self, method: str, status_code: int = 200) -> None:
        """记录请求统计"""
        self.application_collector.record_request(method, status_code)

    def record_task(self, task_name: str, success: bool = True) -> None:
        """记录任务统计"""
        self.application_collector.record_task(task_name, success)

    def record_error(self, error_type: str) -> None:
        """记录错误统计"""
        self.application_collector.record_error(error_type)

    def register_health_indicator(self, indicator) -> None:
        """注册健康指标"""
        self.health_collector.register_indicator(indicator)

    def unregister_health_indicator(self, name: str) -> bool:
        """注销健康指标"""
        return self.health_collector.unregister_indicator(name)

    def get_supported_collectors(self) -> List[str]:
        """获取支持的收集器类型"""
        return ["system", "application", "health"]

    def get_supported_system_metrics(self) -> List[str]:
        """获取支持的系统指标类型"""
        return self.system_collector.get_supported_metrics()

    def get_supported_application_metrics(self) -> List[str]:
        """获取支持的应用指标类型"""
        return self.application_collector.get_supported_metrics()

    def get_health_indicator_names(self) -> List[str]:
        """获取健康指标名称"""
        return self.health_collector.get_indicator_names()

    def reset_application_stats(self) -> None:
        """重置应用统计"""
        self.application_collector.reset_stats()

    def get_aggregator_info(self) -> Dict[str, Any]:
        """获取聚合器信息"""
        return {
            "uptime": time.time() - self._start_time,
            "uptime_formatted": self._format_uptime(time.time() - self._start_time),
            "timeout": self.timeout,
            "cache_ttl": self.cache_ttl,
            "collectors": {
                "system": {"type": "SystemMetricsCollector", "supported_metrics": self.get_supported_system_metrics()},
                "application": {"type": "AppMetricsCollector", "supported_metrics": self.get_supported_application_metrics()},
                "health": {"type": "AsyncHealthCollector", "indicators": self.get_health_indicator_names()},
            },
        }

    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if days > 0:
            return f"{days}d {hours}h {minutes}m {secs}s"
        elif hours > 0:
            return f"{hours}h {minutes}m {secs}s"
        elif minutes > 0:
            return f"{minutes}m {secs}s"
        else:
            return f"{secs}s"
