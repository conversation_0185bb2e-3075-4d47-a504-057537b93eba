#!/usr/bin/env python3
"""
MP3音频播放器单元测试
简单的音频文件播放功能
"""

import platform
import subprocess
import sys
import time
import unittest
from pathlib import Path


class MP3Player:
    """简单的MP3播放器"""

    def __init__(self):
        self.enabled = True

    def play(self, audio_file):
        """播放音频文件"""
        if not self.enabled:
            return False

        audio_path = Path(audio_file)
        if not audio_path.exists():
            print(f"❌ 音频文件不存在: {audio_file}")
            return False

        print(f"🔊 播放音频: {audio_file}")

        try:
            if platform.system().lower() == "windows":
                # Windows: 使用简化的PowerShell播放（适合短音频）
                print("🎵 播放短音频文件...")

                # 简化的PowerShell命令，固定播放时间
                ps_command = f'''
                Add-Type -AssemblyName presentationCore
                $mediaPlayer = New-Object System.Windows.Media.MediaPlayer
                $mediaPlayer.Open([System.Uri]::new((Resolve-Path "{audio_path}").Path))
                $mediaPlayer.Play()
                Start-Sleep -Seconds 2
                $mediaPlayer.Stop()
                $mediaPlayer.Close()
                '''

                # 执行PowerShell命令，短超时时间
                try:
                    subprocess.run(
                        ["powershell", "-Command", ps_command],
                        capture_output=True,
                        text=True,
                        timeout=5,  # 只等待5秒
                    )
                    print(f"✅ 播放完成: {audio_file}")
                    return True
                except subprocess.TimeoutExpired:
                    print(f"⚠️ 播放超时，但可能已播放: {audio_file}")
                    return True  # 即使超时也认为成功

            elif platform.system().lower() == "darwin":
                # macOS使用afplay
                subprocess.run(["afplay", str(audio_path)], check=True)
                print(f"✅ 播放完成: {audio_file}")
                return True

            else:
                # Linux使用aplay
                subprocess.run(["aplay", str(audio_path)], check=True)
                print(f"✅ 播放完成: {audio_file}")
                return True

        except subprocess.CalledProcessError as e:
            print(f"❌ 播放失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 播放异常: {e}")
            return False

    def play_multiple(self, audio_files, interval=0.1):
        """连续播放多个音频文件"""
        if not self.enabled:
            return False

        success_count = 0
        total_count = len(audio_files)

        for i, audio_file in enumerate(audio_files, 1):
            print(f"\n[{i}/{total_count}] 播放: {audio_file}")

            if self.play(audio_file):
                success_count += 1

            # 播放间隔
            if i < total_count and interval > 0:
                print(f"⏳ 等待{interval}秒...")
                time.sleep(interval)

        print(f"\n📊 播放统计: {success_count}/{total_count} 成功")
        return success_count == total_count

    def set_enabled(self, enabled):
        """启用/禁用播放功能"""
        self.enabled = enabled


class TestMP3Player(unittest.TestCase):
    """MP3播放器测试"""

    def setUp(self):
        """测试前准备"""
        self.player = MP3Player()

        # 测试音频文件
        audio_file = "tests/resources/augment_completion.mp3"
        self.test_audio_files = [
            audio_file,
            audio_file,  # 可以重复播放同一个文件
            audio_file,
            audio_file,
        ]

        # 检查哪些文件实际存在
        self.existing_files = [f for f in self.test_audio_files if Path(f).exists()]

        if not self.existing_files:
            print("\n⚠️ 警告: 没有找到测试音频文件")
            print("请在项目根目录放置以下测试文件:")
            for file in self.test_audio_files:
                print(f"  - {file}")

    def test_single_file_playback(self):
        """测试单个文件播放"""
        if not self.existing_files:
            self.skipTest("没有可用的测试音频文件")

        print("\n" + "=" * 60)
        print("🎵 单个文件播放测试")
        print("=" * 60)

        test_file = self.existing_files[0]
        success = self.player.play(test_file)
        self.assertTrue(success, f"播放失败: {test_file}")

        print("\n🎉 单个文件播放测试完成！")

    def test_multiple_files_playback(self):
        """测试多个文件连续播放"""
        if len(self.existing_files) < 2:
            self.skipTest("需要至少2个测试音频文件")

        print("\n" + "=" * 60)
        print("🎵 多文件连续播放测试")
        print("=" * 60)

        success = self.player.play_multiple(self.existing_files, interval=0.2)
        self.assertTrue(success, "多文件播放失败")

        print("\n🎉 多文件连续播放测试完成！")

    def test_nonexistent_file(self):
        """测试播放不存在的文件"""
        print("\n" + "=" * 60)
        print("🎵 不存在文件测试")
        print("=" * 60)

        success = self.player.play("nonexistent.mp3")
        self.assertFalse(success, "应该播放失败")

        print("\n🎉 不存在文件测试完成！")

    def test_enable_disable(self):
        """测试启用/禁用功能"""
        print("\n" + "=" * 60)
        print("🎵 启用/禁用功能测试")
        print("=" * 60)

        # 测试禁用
        self.player.set_enabled(False)
        self.assertFalse(self.player.enabled)

        # 禁用状态下应该返回False
        result = self.player.play("any_file.mp3")
        self.assertFalse(result)

        # 测试重新启用
        self.player.set_enabled(True)
        self.assertTrue(self.player.enabled)

        print("\n🎉 启用/禁用功能测试完成！")

    def test_quick_sequential_playback(self):
        """测试快速连续播放"""
        if len(self.existing_files) < 4:
            self.skipTest("需要至少4个测试音频文件")

        print("\n" + "=" * 60)
        print("🎵 快速连续播放测试")
        print("=" * 60)
        print("请仔细听，应该能听到所有音频文件！")

        # 使用很短的间隔进行快速播放
        success = self.player.play_multiple(self.existing_files[:4], interval=0.1)
        self.assertTrue(success, "快速连续播放失败")

        print("\n🎉 快速连续播放测试完成！")
        print("如果您听到了所有音频，说明播放功能正常！")


if __name__ == "__main__":
    print("MP3音频播放器单元测试")
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print("=" * 60)

    # 检查测试文件
    test_file = "tests/resources/augment_completion.mp3"
    file_exists = Path(test_file).exists()

    if file_exists:
        print(f"✅ 找到测试文件: {test_file}")
    else:
        print(f"❌ 未找到测试文件: {test_file}")
        print("请确保文件在tests/resources/目录中")

    print("=" * 60)

    # 运行测试
    unittest.main(verbosity=2)
