#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 长期运行稳定性测试 - 测试系统在长时间运行下的稳定性和性能

验证Mini-Boot框架在长期运行场景下的内存使用、性能稳定性和资源管理。
"""

import gc
import os
import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from datetime import datetime, timedelta
from unittest.mock import Mock

import psutil

# 核心组件
from miniboot.bean.bean_registry import DefaultBeanDefinitionRegistry
from miniboot.bean.composite_factory import CompositeBeanFactory
from miniboot.bean.composite_proxy import CompositeProxy
from miniboot.bean.deps_graph import DependencyGraph

# 缓存组件
# 事件系统
from miniboot.events.composite_event import CompositeEvent
from miniboot.events.event_types import ApplicationStartedEvent


@dataclass
class StabilityMetrics:
    """稳定性指标"""

    timestamp: datetime
    memory_usage_mb: float
    cpu_usage_percent: float
    active_threads: int
    operations_completed: int
    errors_count: int
    gc_collections: int


class LongRunningStabilityTestCase(unittest.TestCase):
    """长期运行稳定性测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.process = psutil.Process(os.getpid())
        self.metrics_history: list[StabilityMetrics] = []
        self.test_duration_minutes = 5  # 默认5分钟测试
        self.monitoring_interval_seconds = 10  # 每10秒收集一次指标
        self.stop_monitoring = threading.Event()

        # 性能阈值
        self.max_memory_growth_mb = 50  # 最大内存增长50MB
        self.max_cpu_usage_percent = 80  # 最大CPU使用率80%
        self.max_error_rate_percent = 1  # 最大错误率1%

    def collect_metrics(self, operations_completed: int, errors_count: int) -> StabilityMetrics:
        """收集系统指标"""
        # 强制垃圾回收
        gc_before = gc.get_count()
        gc.collect()
        gc_after = gc.get_count()

        return StabilityMetrics(
            timestamp=datetime.now(),
            memory_usage_mb=self.process.memory_info().rss / 1024 / 1024,
            cpu_usage_percent=self.process.cpu_percent(),
            active_threads=threading.active_count(),
            operations_completed=operations_completed,
            errors_count=errors_count,
            gc_collections=sum(gc_after) - sum(gc_before),
        )

    def start_monitoring(self, operations_counter: dict[str, int], errors_counter: dict[str, int]):
        """启动监控线程"""

        def monitor():
            while not self.stop_monitoring.is_set():
                metrics = self.collect_metrics(operations_counter.get("total", 0), errors_counter.get("total", 0))
                self.metrics_history.append(metrics)

                # 等待下次监控
                self.stop_monitoring.wait(self.monitoring_interval_seconds)

        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
        return monitor_thread

    def test_bean_factory_long_running_stability(self):
        """Bean工厂长期运行稳定性测试"""
        print(f"\n=== Bean工厂长期运行稳定性测试 ({self.test_duration_minutes}分钟) ===")

        # 创建工厂和Bean
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = CompositeBeanFactory(registry, dependency_graph)

        # 注册测试Bean
        class StabilityTestBean:
            def __init__(self, name: str):
                self.name = name
                self.call_count = 0
                self._lock = threading.Lock()

            def process_data(self, data: str) -> str:
                with self._lock:
                    self.call_count += 1
                    return f"processed_{data}_{self.call_count}"

        beans = {}
        for i in range(50):  # 创建50个Bean
            bean = StabilityTestBean(f"bean_{i}")
            bean_name = f"bean_{i}"
            beans[bean_name] = bean

            # 注册Bean定义
            from miniboot.bean.bean_definition import BeanDefinition, BeanScope

            bean_definition = BeanDefinition(bean_name=bean_name, bean_class=type(bean), scope=BeanScope.SINGLETON)
            registry.register_bean_definition(bean_name, bean_definition)

            # 使用缓存组件注册Bean实例
            from miniboot.bean.factory_components import BeanCacheComponent

            cache_component = factory.get_component(BeanCacheComponent)
            if cache_component:
                cache_component.put_singleton(bean_name, bean)

        # 计数器
        operations_counter = {"total": 0}
        errors_counter = {"total": 0}

        # 启动监控
        monitor_thread = self.start_monitoring(operations_counter, errors_counter)

        # 工作函数
        def bean_factory_worker():
            """Bean工厂工作函数"""
            end_time = datetime.now() + timedelta(minutes=self.test_duration_minutes)

            while datetime.now() < end_time and not self.stop_monitoring.is_set():
                try:
                    # 随机获取Bean并调用方法
                    bean_name = f"bean_{operations_counter['total'] % 50}"
                    bean = factory.get_bean(bean_name)

                    if bean:
                        bean.process_data(f"data_{operations_counter['total']}")
                        operations_counter["total"] += 1

                        # 模拟一些工作负载
                        if operations_counter["total"] % 100 == 0:
                            time.sleep(0.01)  # 每100次操作休息10ms

                except Exception as e:
                    errors_counter["total"] += 1
                    print(f"Bean工厂操作错误: {e}")

        # 启动多个工作线程
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(bean_factory_worker) for _ in range(5)]

            # 等待测试完成
            for future in futures:
                future.result()

        # 停止监控
        self.stop_monitoring.set()
        monitor_thread.join(timeout=5)

        # 分析结果
        self._analyze_stability_results("Bean工厂", operations_counter, errors_counter)

        # 清理
        factory.shutdown()

    def test_proxy_long_running_stability(self):
        """代理长期运行稳定性测试"""
        print(f"\n=== 代理长期运行稳定性测试 ({self.test_duration_minutes}分钟) ===")

        # 创建测试Bean和代理
        class ProxyTestBean:
            def __init__(self):
                self.call_count = 0
                self._lock = threading.Lock()

            def heavy_computation(self, data: int) -> int:
                with self._lock:
                    self.call_count += 1
                    # 模拟重计算
                    result = sum(range(data % 1000))
                    return result + self.call_count

        bean = ProxyTestBean()
        proxy = CompositeProxy(sync_bean=bean, bean_name="stabilityTestProxy")

        # 重置计数器和监控
        self.metrics_history.clear()
        self.stop_monitoring.clear()
        operations_counter = {"total": 0}
        errors_counter = {"total": 0}

        # 启动监控
        monitor_thread = self.start_monitoring(operations_counter, errors_counter)

        # 工作函数
        def proxy_worker():
            """代理工作函数"""
            end_time = datetime.now() + timedelta(minutes=self.test_duration_minutes)

            while datetime.now() < end_time and not self.stop_monitoring.is_set():
                try:
                    # 调用代理方法
                    proxy.heavy_computation(operations_counter["total"])
                    operations_counter["total"] += 1

                    # 定期检查代理状态
                    if operations_counter["total"] % 500 == 0:
                        stats = proxy.get_stats()
                        print(f"代理统计: {stats}")

                except Exception as e:
                    errors_counter["total"] += 1
                    print(f"代理操作错误: {e}")

        # 启动多个工作线程
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(proxy_worker) for _ in range(3)]

            # 等待测试完成
            for future in futures:
                future.result()

        # 停止监控
        self.stop_monitoring.set()
        monitor_thread.join(timeout=5)

        # 分析结果
        self._analyze_stability_results("代理", operations_counter, errors_counter)

        # 清理
        proxy.shutdown()

    def test_event_system_long_running_stability(self):
        """事件系统长期运行稳定性测试"""
        print(f"\n=== 事件系统长期运行稳定性测试 ({self.test_duration_minutes}分钟) ===")

        # 重置计数器和监控
        self.metrics_history.clear()
        self.stop_monitoring.clear()
        operations_counter = {"total": 0}
        errors_counter = {"total": 0}

        # 启动监控
        monitor_thread = self.start_monitoring(operations_counter, errors_counter)

        # 事件存储（定期清理）
        events = []
        events_lock = threading.Lock()

        # 工作函数
        def event_worker():
            """事件工作函数"""
            end_time = datetime.now() + timedelta(minutes=self.test_duration_minutes)

            while datetime.now() < end_time and not self.stop_monitoring.is_set():
                try:
                    # 创建不同类型的事件
                    if operations_counter["total"] % 3 == 0:
                        event = ApplicationStartedEvent(application=Mock(), startup_time=1.5)
                    else:
                        event = CompositeEvent(
                            event_type=f"TestEvent_{operations_counter['total']}", source=Mock(), data={"index": operations_counter["total"]}
                        )

                    # 操作事件
                    event.set_data("processed_at", datetime.now().isoformat())
                    event.mark_processed()

                    with events_lock:
                        events.append(event)
                        operations_counter["total"] += 1

                        # 定期清理事件以防止内存泄漏
                        if len(events) > 1000:
                            # 清理前500个事件
                            for old_event in events[:500]:
                                old_event.cleanup()
                            events[:500] = []

                except Exception as e:
                    errors_counter["total"] += 1
                    print(f"事件操作错误: {e}")

        # 启动多个工作线程
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(event_worker) for _ in range(4)]

            # 等待测试完成
            for future in futures:
                future.result()

        # 停止监控
        self.stop_monitoring.set()
        monitor_thread.join(timeout=5)

        # 清理剩余事件
        with events_lock:
            for event in events:
                event.cleanup()
            events.clear()

        # 分析结果
        self._analyze_stability_results("事件系统", operations_counter, errors_counter)

    def _analyze_stability_results(self, test_name: str, operations_counter: dict[str, int], errors_counter: dict[str, int]):
        """分析稳定性测试结果"""
        if not self.metrics_history:
            print(f"❌ {test_name}: 没有收集到监控数据")
            return

        # 计算统计数据
        initial_memory = self.metrics_history[0].memory_usage_mb
        final_memory = self.metrics_history[-1].memory_usage_mb
        memory_growth = final_memory - initial_memory

        max_memory = max(m.memory_usage_mb for m in self.metrics_history)
        avg_cpu = sum(m.cpu_usage_percent for m in self.metrics_history) / len(self.metrics_history)
        max_cpu = max(m.cpu_usage_percent for m in self.metrics_history)

        total_operations = operations_counter.get("total", 0)
        total_errors = errors_counter.get("total", 0)
        error_rate = (total_errors / total_operations * 100) if total_operations > 0 else 0

        # 输出结果
        print(f"\n📊 {test_name}稳定性分析结果:")
        print(f"   运行时长: {self.test_duration_minutes}分钟")
        print(f"   总操作数: {total_operations:,}")
        print(f"   总错误数: {total_errors}")
        print(f"   错误率: {error_rate:.2f}%")
        print(f"   内存增长: {memory_growth:.2f}MB (初始: {initial_memory:.2f}MB, 最终: {final_memory:.2f}MB)")
        print(f"   最大内存: {max_memory:.2f}MB")
        print(f"   平均CPU: {avg_cpu:.1f}%")
        print(f"   最大CPU: {max_cpu:.1f}%")
        print(f"   监控数据点: {len(self.metrics_history)}")

        # 验证稳定性指标
        stability_passed = True

        if memory_growth > self.max_memory_growth_mb:
            print(f"⚠️ 内存增长过大: {memory_growth:.2f}MB > {self.max_memory_growth_mb}MB")
            stability_passed = False

        if max_cpu > self.max_cpu_usage_percent:
            print(f"⚠️ CPU使用率过高: {max_cpu:.1f}% > {self.max_cpu_usage_percent}%")
            stability_passed = False

        if error_rate > self.max_error_rate_percent:
            print(f"⚠️ 错误率过高: {error_rate:.2f}% > {self.max_error_rate_percent}%")
            stability_passed = False

        if stability_passed:
            print(f"✅ {test_name}长期运行稳定性测试通过")
        else:
            print(f"❌ {test_name}长期运行稳定性测试失败")

        return stability_passed


if __name__ == "__main__":
    # 设置较短的测试时间用于演示
    import sys

    if "--quick" in sys.argv:
        # 快速测试模式：1分钟
        LongRunningStabilityTestCase.test_duration_minutes = 1
        LongRunningStabilityTestCase.monitoring_interval_seconds = 5
        sys.argv.remove("--quick")

    unittest.main(verbosity=2)
