[run]
source = miniboot
omit =
    */tests/*
    */venv/*
    */__pycache__/*
    */.venv/*
    */site-packages/*
    setup.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    def __str__
    raise AssertionError
    raise NotImplementedError
    if __name__ == .__main__.:
    if TYPE_CHECKING:
    @abstractmethod
    @abc.abstractmethod

[html]
directory = htmlcov
title = Mini-Boot Coverage Report

[xml]
output = coverage.xml
