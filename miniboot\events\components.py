#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 事件系统组件 - 使用组合模式替代复杂继承层次

提供事件系统的各种功能组件,通过组合模式实现复杂功能,
避免深层继承带来的维护困难.
"""

import threading
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Optional

from loguru import logger


class EventComponent(ABC):
    """事件组件基础接口

    所有事件功能组件都应该实现此接口.
    组件负责特定的功能,可以独立测试和维护.
    """

    @abstractmethod
    def initialize(self) -> None:
        """初始化组件"""
        pass

    @abstractmethod
    def shutdown(self) -> None:
        """关闭组件"""
        pass

    @property
    @abstractmethod
    def component_name(self) -> str:
        """组件名称"""
        pass


@dataclass
class EventMetadata:
    """事件元数据

    使用数据类来封装事件的基本信息,
    避免在事件类中混合数据和行为.
    """

    event_id: str
    event_type: str
    source: Any
    timestamp: datetime
    processed: bool = False
    data: dict[str, Any] = field(default_factory=dict)

    def mark_processed(self) -> None:
        """标记事件已处理"""
        self.processed = True

    def is_processed(self) -> bool:
        """检查事件是否已处理"""
        return self.processed

    def get_data(self, key: str, default: Any = None) -> Any:
        """获取事件数据"""
        return self.data.get(key, default)

    def set_data(self, key: str, value: Any) -> None:
        """设置事件数据"""
        self.data[key] = value


class EventIdComponent(EventComponent):
    """事件ID生成组件

    负责生成唯一的事件ID.
    将ID生成逻辑从事件类中分离.
    """

    # 类级别的计数器和锁,确保全局唯一性
    _global_counter = 0
    _global_lock = threading.Lock()

    def __init__(self):
        """初始化ID生成组件"""
        self._prefix = "evt"

        logger.debug("EventIdComponent initialized")

    def initialize(self) -> None:
        """初始化组件"""
        logger.debug("EventIdComponent initialized successfully")

    def shutdown(self) -> None:
        """关闭组件"""
        logger.debug("EventIdComponent shutdown successfully")

    @property
    def component_name(self) -> str:
        """组件名称"""
        return "EventIdComponent"

    def generate_id(self) -> str:
        """生成唯一事件ID

        Returns:
            str: 唯一的事件ID
        """
        with EventIdComponent._global_lock:  # 使用类级别锁确保全局唯一性
            EventIdComponent._global_counter += 1
            timestamp = int(time.time() * 1000000)  # 微秒时间戳,提高精度
            return f"{self._prefix}_{timestamp}_{EventIdComponent._global_counter:08d}"


class EventDataComponent(EventComponent):
    """事件数据管理组件

    负责管理事件的数据存储和访问.
    将数据管理逻辑从事件类中分离.
    """

    def __init__(self):
        """初始化数据管理组件"""
        self._event_data: dict[str, dict[str, Any]] = {}

        logger.debug("EventDataComponent initialized")

    def initialize(self) -> None:
        """初始化组件"""
        logger.debug("EventDataComponent initialized successfully")

    def shutdown(self) -> None:
        """关闭组件"""
        self._event_data.clear()
        logger.debug("EventDataComponent shutdown successfully")

    @property
    def component_name(self) -> str:
        """组件名称"""
        return "EventDataComponent"

    def store_event_data(self, event_id: str, data: dict[str, Any]) -> None:
        """存储事件数据

        Args:
            event_id: 事件ID
            data: 事件数据
        """
        self._event_data[event_id] = data.copy()

    def get_event_data(self, event_id: str) -> dict[str, Any]:
        """获取事件数据

        Args:
            event_id: 事件ID

        Returns:
            Dict[str, Any]: 事件数据
        """
        return self._event_data.get(event_id, {}).copy()

    def remove_event_data(self, event_id: str) -> None:
        """移除事件数据

        Args:
            event_id: 事件ID
        """
        self._event_data.pop(event_id, None)

    def has_event_data(self, event_id: str) -> bool:
        """检查是否存在事件数据

        Args:
            event_id: 事件ID

        Returns:
            bool: 如果存在返回True
        """
        return event_id in self._event_data


class AsyncEventComponent(EventComponent):
    """异步事件处理组件

    负责异步事件的特殊处理逻辑.
    将异步处理从事件类中分离.
    """

    def __init__(self):
        """初始化异步事件组件"""
        self._async_events: dict[str, Any] = {}
        self._processing_events: set[str] = set()

        logger.debug("AsyncEventComponent initialized")

    def initialize(self) -> None:
        """初始化组件"""
        logger.debug("AsyncEventComponent initialized successfully")

    def shutdown(self) -> None:
        """关闭组件"""
        self._async_events.clear()
        self._processing_events.clear()
        logger.debug("AsyncEventComponent shutdown successfully")

    @property
    def component_name(self) -> str:
        """组件名称"""
        return "AsyncEventComponent"

    def register_async_event(self, event_id: str, event_data: Any) -> None:
        """注册异步事件

        Args:
            event_id: 事件ID
            event_data: 事件数据
        """
        self._async_events[event_id] = event_data

    def start_processing(self, event_id: str) -> None:
        """开始处理异步事件

        Args:
            event_id: 事件ID
        """
        self._processing_events.add(event_id)

    def finish_processing(self, event_id: str) -> None:
        """完成异步事件处理

        Args:
            event_id: 事件ID
        """
        self._processing_events.discard(event_id)
        self._async_events.pop(event_id, None)

    def is_processing(self, event_id: str) -> bool:
        """检查事件是否正在处理

        Args:
            event_id: 事件ID

        Returns:
            bool: 如果正在处理返回True
        """
        return event_id in self._processing_events


class BeanEventComponent(EventComponent):
    """Bean事件处理组件

    专门处理Bean生命周期相关的事件.
    将Bean事件逻辑从通用事件类中分离.
    """

    def __init__(self):
        """初始化Bean事件组件"""
        self._bean_events: dict[str, list[str]] = {}  # bean_name -> event_ids
        self._event_beans: dict[str, str] = {}  # event_id -> bean_name

        logger.debug("BeanEventComponent initialized")

    def initialize(self) -> None:
        """初始化组件"""
        logger.debug("BeanEventComponent initialized successfully")

    def shutdown(self) -> None:
        """关闭组件"""
        self._bean_events.clear()
        self._event_beans.clear()
        logger.debug("BeanEventComponent shutdown successfully")

    @property
    def component_name(self) -> str:
        """组件名称"""
        return "BeanEventComponent"

    def register_bean_event(self, event_id: str, bean_name: str) -> None:
        """注册Bean事件

        Args:
            event_id: 事件ID
            bean_name: Bean名称
        """
        if bean_name not in self._bean_events:
            self._bean_events[bean_name] = []

        self._bean_events[bean_name].append(event_id)
        self._event_beans[event_id] = bean_name

    def get_bean_events(self, bean_name: str) -> list[str]:
        """获取Bean的所有事件

        Args:
            bean_name: Bean名称

        Returns:
            List[str]: 事件ID列表
        """
        return self._bean_events.get(bean_name, []).copy()

    def get_event_bean(self, event_id: str) -> Optional[str]:
        """获取事件对应的Bean名称

        Args:
            event_id: 事件ID

        Returns:
            Optional[str]: Bean名称,如果不存在返回None
        """
        return self._event_beans.get(event_id)

    def remove_bean_events(self, bean_name: str) -> None:
        """移除Bean的所有事件

        Args:
            bean_name: Bean名称
        """
        if bean_name in self._bean_events:
            event_ids = self._bean_events.pop(bean_name)
            for event_id in event_ids:
                self._event_beans.pop(event_id, None)
