# 项目结构和组织

## 根目录布局

```
mini-boot/
├── miniboot/           # 核心框架包
├── examples/           # 使用示例和演示
├── tests/             # 测试套件 (单元、集成、性能测试)
├── docs/              # 文档 (中文)
├── scripts/           # 构建和发布自动化
├── resources/         # 配置文件 (YAML)
├── logs/              # 应用日志
└── htmlcov/           # 覆盖率报告
```

## 核心框架结构 (miniboot/)

### 扁平化架构设计

核心 IoC 功能直接放在 `miniboot/` 根目录下，减少嵌套层级，提高可用性。

```
miniboot/
├── __init__.py        # 框架主入口点
├── runner.py          # MiniBootApplication - 应用运行器
├── context.py         # SmartApplicationContext - IoC 容器
└── [其他核心文件]      # 直接的核心功能
```

### 模块化子系统

```
miniboot/
├── annotations/       # 注解系统 (@Component, @Service 等)
├── bean/             # Bean 管理 (工厂、注册表、解析器)
├── processor/        # Bean 处理器 (自动装配、生命周期等)
├── env/              # 环境和配置管理
├── web/              # Web 集成 (FastAPI 适配器)
├── events/           # 事件系统 (发布器、监听器)
├── schedule/         # 任务调度 (cron、异步)
├── actuator/         # 监控 (健康检查、指标、端点)
├── asyncs/           # 异步处理和线程池
├── starters/         # 模块化功能的 Starter 机制
├── errors/           # 异常层次结构和错误处理
├── log/              # 日志配置
├── banner/           # 启动横幅显示
└── utils/            # 工具类和辅助函数
```

## 关键架构模式

### 1. 注解驱动配置

-   通过 `@Component`、`@Service`、`@Repository` 发现组件
-   通过 `@Autowired` 进行依赖注入
-   通过 `@Value` 进行配置绑定
-   通过 `@RestController`、`@GetMapping` 定义 Web 端点

### 2. Bean 生命周期管理

-   `BeanDefinition` - Bean 的元数据
-   `BeanFactory` - 创建和管理 Bean 实例
-   `BeanPostProcessor` - 初始化前后的钩子
-   `DependencyResolver` - 处理注入和循环依赖

### 3. 模块化 Starter 系统

```
starters/
├── mock/             # 测试工具 starter
└── monitor/          # 监控功能 starter
```

每个 starter 遵循以下模式:

```
starter_name/
├── META-INF/         # 自动配置元数据
├── configuration.py  # 自动配置类
├── properties.py     # 配置属性
└── service.py        # 核心服务实现
```

### 4. 基于环境的配置

```
resources/
├── application.yml           # 基础配置
├── application-dev.yml       # 开发环境
├── application-test.yml      # 测试环境
└── application-prod.yml      # 生产环境
```

## 文件组织约定

### 1. 导入结构

```python
# 标准库导入
import os
from typing import Dict, List

# 第三方库导入
import yaml
from fastapi import FastAPI

# 本地导入
from miniboot.core import Container
from miniboot.annotations import Component
```

### 2. 文件头 (必需)

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块功能描述
"""
```

### 3. 模块导出

每个 `__init__.py` 应该清晰地导出公共 API:

```python
from .core import Component, Service
from .scanner import ComponentScanner

__all__ = ["Component", "Service", "ComponentScanner"]
```

## 测试结构

```
tests/
├── unit/             # 按模块的单元测试
│   ├── annotations/
│   ├── bean/
│   └── web/
├── integration/      # 集成测试
├── benchmark/        # 性能测试
└── testutils/        # 测试工具和固件
```

## 文档结构

```
docs/
├── 01.prd.md                    # 产品需求
├── 02.架构设计.md                # 架构设计
├── 03.项目目录.md                # 项目结构
├── coding-standards.md          # 编码标准
└── [编号文档]                    # 功能文档
```

## 关键设计原则

1. **扁平化核心结构** - 核心 IoC 功能在根级别
2. **模块化子系统** - 清晰的关注点分离
3. **约定优于配置** - 合理的默认值和覆盖能力
4. **注解驱动** - 声明式编程模型
5. **可扩展架构** - 通过 starter 和处理器的插件系统
