# Mini-Boot 注解系统设计

## 1. 概述

Mini-Boot 框架的注解系统是一个轻量级的依赖注入和控制反转框架，它通过 Python 装饰器的方式实现了组件的自动装配、依赖注入、条件装配等功能。该系统的设计灵感来源于 Spring 框架，充分利用了 Python 装饰器的强大功能。

注解系统主要由两部分组成：

1. **注解定义 (annotations.py)**：定义了各种装饰器类型及其参数
2. **组件扫描器 (scanner.py)**：负责扫描代码中的装饰器并解析其含义

## 1.1 目录结构

```
miniboot/
├── annotations.py                  # 注解定义模块
├── scanner.py                      # 组件扫描器
└── bean_definition.py             # Bean定义（Scope枚举等）
```

## 2. 核心注解类型

### 2.1 组件定义注解

#### @Component

用于标记一个类为组件，可以被容器管理。支持无参数调用和带参数调用两种方式。

```python
from mini.ioc.bean_definition import Scope

def Component(name: str = None, scope: Scope = Scope.SINGLETON):
    """组件注解

    Args:
        name: Bean名称，默认为类名首字母小写
        scope: Bean作用域，默认为单例
    """
    def decorator(cls: Type) -> Type:
        # 存储组件信息
        if name is not None:
            cls.__component_name__ = name
        cls.__component_scope__ = scope
        cls.__is_component__ = True
        return cls

    # 如果直接传入类，不带参数
    if isinstance(name, type):
        cls = name
        name = None
        return decorator(cls)
    return decorator
```

使用示例：

```python
# 无参数使用
@Component
class UserService:
    pass

# 带参数使用
@Component(name="userService", scope=Scope.SINGLETON)
class UserService:
    pass
```

#### @Service

标记一个类为服务组件，是@Component 的特化形式。

```python
def Service(cls: Type = None, name: str = None):
    """服务注解

    @Service 是 @Component 的特化，用于标记服务类
    """
    return Component(name=name)(cls) if cls is None else Component(cls)
```

#### @Configuration

标记一个类为配置类，通常用于集中管理 Bean 的定义。配置类本身也会被注册为一个 Bean。

```python
def Configuration(cls: Type = None):
    """配置类注解，标记一个类为配置类。
    配置类中可以使用@Bean注解来定义Bean。
    配置类本身也会被注册为一个Bean。
    """
    def decorator(cls: Type) -> Type:
        # 标记为配置类
        setattr(cls, '__configuration__', True)
        # 配置类本身也是一个组件
        setattr(cls, '__is_component__', True)
        setattr(cls, '__component_scope__', Scope.SINGLETON)
        return cls

    if cls is None:
        return decorator
    return decorator(cls)
```

#### @MiniBootApplication

应用入口注解，相当于@Configuration + @ComponentScan + @EnableAutoConfiguration 的组合。

```python
def MiniBootApplication(cls: Type = None, base_packages: List[str] = None):
    """应用入口注解,相当于@Configuration + @ComponentScan + @EnableAutoConfiguration的组合。

    Args:
        cls: 被装饰的类
        base_packages: 要扫描的包路径列表，例如 ["tests.ioc"]

    Returns:
        装饰后的类
    """
    def decorator(cls: Type) -> Type:
        # 标记为配置类
        setattr(cls, '__configuration__', True)
        # 配置类本身也是一个组件
        setattr(cls, '__is_component__', True)
        setattr(cls, '__component_scope__', Scope.SINGLETON)

        # 应用@ComponentScan注解
        if base_packages is None:
            module = cls.__module__
            current_package = module.rsplit('.', 1)[0]
            ComponentScan([current_package])(cls)
        else:
            ComponentScan(base_packages)(cls)

        # 应用@EnableAutoConfiguration注解
        EnableAutoConfiguration(cls)
        return cls

    if cls is None:
        return decorator
    return decorator(cls)
```

### 2.2 依赖注入注解

#### @Autowired

自动装配依赖，可以注入其他 Bean。支持字段注入和 setter 方法注入。

```python
def Autowired(field_or_func=None, *, required: bool = True, name: str = None):
    """自动装配注解

    Args:
        field_or_func: 被装饰的字段或方法
        required: 是否必需，默认为True
        name: 指定Bean名称，默认根据类型推断
    """
    def decorator(field_or_func):
        # 标记为需要自动装配
        setattr(field_or_func, '__autowired__', True)
        setattr(field_or_func, '__autowired_required__', required)
        if name is not None:
            setattr(field_or_func, '__autowired_name__', name)
        return field_or_func

    if field_or_func is None:
        return decorator
    return decorator(field_or_func)
```

使用示例：

```python
@Component
class UserController:
    # 字段注入
    user_service: 'UserService' = Autowired()

    # setter方法注入
    @Autowired(name="userService")
    def set_user_service(self, user_service):
        self.user_service = user_service
```

#### @Bean

在配置类中定义 Bean 的方法注解。

```python
def Bean(func=None, *, name: str = None, scope: Scope = Scope.SINGLETON):
    """Bean定义注解，用于在配置类中定义Bean

    Args:
        func: 被装饰的方法
        name: Bean名称，默认为方法名
        scope: Bean作用域，默认为单例
    """
    def decorator(func):
        # 标记为Bean定义方法
        setattr(func, '__bean__', True)
        setattr(func, '__bean_scope__', scope)
        if name is not None:
            setattr(func, '__bean_name__', name)
        return func

    if func is None:
        return decorator
    return decorator(func)
```

使用示例：

```python
@Configuration
class DatabaseConfig:
    @Bean(name="dataSource")
    def create_data_source(self):
        return DataSource("********************************")

    @Bean(scope=Scope.PROTOTYPE)
    def create_connection(self, data_source: DataSource):
        return data_source.get_connection()
```

### 2.3 条件装配注解

#### @conditional

条件注解的基础类型，可以派生出多种条件装配注解。

```python
@dataclass
class ConditionalMetadata:
    """条件元数据"""
    condition_type: str
    properties: List[str] = field(default_factory=list)
    having_value: Optional[str] = None
    match_if_missing: bool = False
    bean_names: List[str] = field(default_factory=list)

def conditional(condition_type: str, **kwargs) -> callable:
    """条件装饰器"""
    def decorator(cls_or_func):
        metadata = ConditionalMetadata(condition_type=condition_type, **kwargs)
        cls_or_func.__conditional_metadata__ = metadata
        cls_or_func.__is_conditional__ = True
        return cls_or_func
    return decorator
```

#### @conditional_on_property

基于属性条件的装配。

```python
def conditional_on_property(name: str,
                           having_value: Optional[str] = None,
                           match_if_missing: bool = False) -> callable:
    """基于属性的条件装配装饰器"""
    return conditional(
        condition_type="OnProperty",
        properties=[name],
        having_value=having_value,
        match_if_missing=match_if_missing
    )
```

#### @conditional_on_bean

基于 Bean 存在的条件装配。

```python
def conditional_on_bean(*bean_names: str) -> callable:
    """基于Bean存在的条件装配装饰器"""
    return conditional(
        condition_type="OnBean",
        bean_names=list(bean_names)
    )
```

#### @conditional_on_missing_bean

基于 Bean 不存在的条件装配。

```python
def conditional_on_missing_bean(*bean_names: str) -> callable:
    """基于Bean不存在的条件装配装饰器"""
    return conditional(
        condition_type="OnMissingBean",
        bean_names=list(bean_names)
    )
```

### 2.4 配置相关注解

#### @configuration_properties

将配置属性绑定到类。

```python
@dataclass
class ConfigurationPropertiesMetadata:
    """配置属性元数据"""
    prefix: str
    ignore_invalid_fields: bool = False
    ignore_unknown_fields: bool = True

def configuration_properties(prefix: str,
                            ignore_invalid_fields: bool = False,
                            ignore_unknown_fields: bool = True) -> callable:
    """配置属性装饰器"""
    def decorator(cls):
        metadata = ConfigurationPropertiesMetadata(
            prefix=prefix,
            ignore_invalid_fields=ignore_invalid_fields,
            ignore_unknown_fields=ignore_unknown_fields
        )
        cls.__configuration_properties_metadata__ = metadata
        cls.__is_configuration_properties__ = True
        return cls
    return decorator
```

#### @component_scan

声明组件扫描路径。

```python
@dataclass
class ComponentScanMetadata:
    """组件扫描元数据"""
    base_packages: List[str] = field(default_factory=list)
    excludes: List[str] = field(default_factory=list)
    includes: List[str] = field(default_factory=list)
    use_default_filters: bool = True

def component_scan(base_packages: List[str] = None,
                  excludes: List[str] = None,
                  includes: List[str] = None,
                  use_default_filters: bool = True) -> callable:
    """组件扫描装饰器"""
    def decorator(cls):
        metadata = ComponentScanMetadata(
            base_packages=base_packages or [],
            excludes=excludes or [],
            includes=includes or [],
            use_default_filters=use_default_filters
        )
        cls.__component_scan_metadata__ = metadata
        cls.__is_component_scan__ = True
        return cls
    return decorator
```

### 2.5 生命周期注解

#### @post_construct

标记初始化方法。

```python
@dataclass
class PostConstructMetadata:
    """初始化方法元数据"""
    method: str

def post_construct(func) -> callable:
    """初始化方法装饰器"""
    metadata = PostConstructMetadata(method=func.__name__)
    func.__post_construct_metadata__ = metadata
    func.__is_post_construct__ = True
    return func
```

#### @pre_destroy

标记销毁方法。

```python
@dataclass
class PreDestroyMetadata:
    """销毁方法元数据"""
    method: str

def pre_destroy(func) -> callable:
    """销毁方法装饰器"""
    metadata = PreDestroyMetadata(method=func.__name__)
    func.__pre_destroy_metadata__ = metadata
    func.__is_pre_destroy__ = True
    return func
```

### 2.6 异步和调度注解

#### @async_method

标记异步执行的方法。

```python
@dataclass
class AsyncMetadata:
    """异步方法元数据"""
    pool: Optional[str] = None
    method: str = ""

def async_method(pool: Optional[str] = None) -> callable:
    """异步方法装饰器"""
    def decorator(func):
        metadata = AsyncMetadata(pool=pool, method=func.__name__)
        func.__async_metadata__ = metadata
        func.__is_async__ = True
        return func
    return decorator
```

#### @scheduled

标记定时调度的方法。

```python
@dataclass
class ScheduledMetadata:
    """调度方法元数据"""
    cron: Optional[str] = None
    fixed_rate: Optional[str] = None
    fixed_delay: Optional[str] = None
    initial_delay: Optional[str] = None
    zone: Optional[str] = None
    concurrent: bool = False

def scheduled(cron: Optional[str] = None,
              fixed_rate: Optional[str] = None,
              fixed_delay: Optional[str] = None,
              initial_delay: Optional[str] = None,
              zone: Optional[str] = None,
              concurrent: bool = False) -> callable:
    """调度方法装饰器"""
    def decorator(func):
        metadata = ScheduledMetadata(
            cron=cron,
            fixed_rate=fixed_rate,
            fixed_delay=fixed_delay,
            initial_delay=initial_delay,
            zone=zone,
            concurrent=concurrent
        )
        func.__scheduled_metadata__ = metadata
        func.__is_scheduled__ = True
        return func
    return decorator
```

### 2.7 事件相关注解

#### @event_listener

标记事件监听方法。

```python
@dataclass
class EventListenerMetadata:
    """事件监听器元数据"""
    async_exec: bool = False
    order: int = 0
    condition: Optional[str] = None

def event_listener(async_exec: bool = False,
                  order: int = 0,
                  condition: Optional[str] = None) -> callable:
    """事件监听器装饰器"""
    def decorator(func):
        metadata = EventListenerMetadata(
            async_exec=async_exec,
            order=order,
            condition=condition
        )
        func.__event_listener_metadata__ = metadata
        func.__is_event_listener__ = True
        return func
    return decorator
```

## 3. 组件扫描机制

### 3.1 ComponentScanner

`ComponentScanner`是组件扫描器的核心实现，负责扫描代码中的装饰器并解析其含义。

```python
import importlib
import inspect
import pkgutil
from typing import Dict, List, Type, Any, Set

class ComponentScanner:
    """组件扫描器，用于扫描和发现带有注解的组件类"""

    def __init__(self):
        # 存储扫描到的组件类
        self.component_classes: Dict[str, Type] = {}
        # 存储配置类
        self.configuration_classes: Dict[str, Type] = {}
        # 存储Bean定义方法
        self.bean_methods: Dict[str, List[callable]] = {}
        # 存储自动装配字段和方法
        self.autowired_fields: Dict[str, Dict[str, Any]] = {}
        self.autowired_methods: Dict[str, Dict[str, Any]] = {}

    def scan_packages(self, packages: List[str]) -> None:
        """扫描指定包路径下的所有组件

        Args:
            packages: 要扫描的包路径列表，例如 ["tests.ioc", "app.services"]
        """
        for package_name in packages:
            self._scan_package(package_name)

    def _scan_package(self, package_name: str) -> None:
        """扫描单个包"""
        try:
            package = importlib.import_module(package_name)

            # 如果是包，递归扫描子模块
            if hasattr(package, '__path__'):
                for importer, modname, ispkg in pkgutil.iter_modules(package.__path__, package_name + "."):
                    self._scan_module(modname)
            else:
                # 如果是模块，直接扫描
                self._scan_module(package_name)

        except ImportError as e:
            print(f"Failed to import package {package_name}: {e}")

    def _scan_module(self, module_name: str) -> None:
        """扫描单个模块中的所有类"""
        try:
            module = importlib.import_module(module_name)

            # 扫描模块中的所有类
            for name, obj in inspect.getmembers(module, inspect.isclass):
                # 只处理定义在当前模块中的类
                if obj.__module__ == module_name:
                    self._scan_class(obj)

        except ImportError as e:
            print(f"Failed to import module {module_name}: {e}")

    def _scan_class(self, cls: Type) -> None:
        """扫描单个类，检查是否有相关注解"""
        class_name = f"{cls.__module__}.{cls.__name__}"

        # 检查是否是组件类
        if hasattr(cls, '__is_component__'):
            self.component_classes[class_name] = cls
            # 扫描自动装配字段和方法
            self._scan_autowired_members(cls, class_name)

        # 检查是否是配置类
        if hasattr(cls, '__configuration__'):
            self.configuration_classes[class_name] = cls
            # 扫描Bean定义方法
            self._scan_bean_methods(cls, class_name)

    def _scan_autowired_members(self, cls: Type, class_name: str) -> None:
        """扫描类中的自动装配字段和方法"""
        # 扫描字段注解
        annotations = getattr(cls, '__annotations__', {})
        for field_name, field_type in annotations.items():
            field_value = getattr(cls, field_name, None)
            if hasattr(field_value, '__autowired__'):
                if class_name not in self.autowired_fields:
                    self.autowired_fields[class_name] = {}
                self.autowired_fields[class_name][field_name] = {
                    'type': field_type,
                    'required': getattr(field_value, '__autowired_required__', True),
                    'name': getattr(field_value, '__autowired_name__', None)
                }

        # 扫描方法注解
        for method_name, method in inspect.getmembers(cls, inspect.isfunction):
            if hasattr(method, '__autowired__'):
                if class_name not in self.autowired_methods:
                    self.autowired_methods[class_name] = {}
                self.autowired_methods[class_name][method_name] = {
                    'method': method,
                    'required': getattr(method, '__autowired_required__', True),
                    'name': getattr(method, '__autowired_name__', None)
                }

    def _scan_bean_methods(self, cls: Type, class_name: str) -> None:
        """扫描配置类中的Bean定义方法"""
        bean_methods = []
        for method_name, method in inspect.getmembers(cls, inspect.isfunction):
            if hasattr(method, '__bean__'):
                bean_methods.append(method)

        if bean_methods:
            self.bean_methods[class_name] = bean_methods

    def _scan_class_methods(self, cls: Type, class_name: str) -> None:
        """扫描类中的方法"""
        for method_name, method in inspect.getmembers(cls, inspect.isfunction):
            # 扫描自动装配方法
            if hasattr(method, '__is_autowired__'):
                if class_name not in self.autowired_methods:
                    self.autowired_methods[class_name] = {}
                self.autowired_methods[class_name][method_name] = method.__autowired_metadata__

            # 扫描配置值方法
            if hasattr(method, '__is_value__'):
                if class_name not in self.value_methods:
                    self.value_methods[class_name] = {}
                self.value_methods[class_name][method_name] = method.__value_metadata__

            # 扫描调度方法
            if hasattr(method, '__is_scheduled__'):
                if class_name not in self.scheduled_methods:
                    self.scheduled_methods[class_name] = {}
                self.scheduled_methods[class_name][method_name] = method.__scheduled_metadata__

            # 扫描事件监听方法
            if hasattr(method, '__is_event_listener__'):
                if class_name not in self.event_listener_methods:
                    self.event_listener_methods[class_name] = {}
                self.event_listener_methods[class_name][method_name] = method.__event_listener_metadata__

    def _should_exclude_module(self, module_name: str) -> bool:
        """检查模块是否应该被排除"""
        for exclude in self.excludes:
            if exclude in module_name:
                return True

        if self.includes:
            for include in self.includes:
                if include in module_name:
                    return False
            return True

        return False

    def get_component_classes(self) -> Dict[str, Type]:
        """获取所有组件类"""
        result = {}
        for class_name, cls in self.scanned_classes.items():
            if class_name in self.component_metadata:
                result[class_name] = cls
        return result

    def get_configuration_classes(self) -> Dict[str, Type]:
        """获取所有配置类"""
        result = {}
        for class_name, cls in self.scanned_classes.items():
            if class_name in self.configuration_metadata:
                result[class_name] = cls
        return result

    def get_component_metadata(self, class_name: str) -> ComponentMetadata:
        """获取组件元数据"""
        return self.component_metadata.get(class_name)

    def get_autowired_methods(self, class_name: str) -> Dict[str, AutowiredMetadata]:
        """获取自动装配方法"""
        return self.autowired_methods.get(class_name, {})

    def get_scheduled_methods(self, class_name: str) -> Dict[str, ScheduledMetadata]:
        """获取调度方法"""
        return self.scheduled_methods.get(class_name, {})

    def get_event_listener_methods(self, class_name: str) -> Dict[str, EventListenerMetadata]:
        """获取事件监听方法"""
        return self.event_listener_methods.get(class_name, {})
```

## 4. 与其他模块的集成

### 4.1 与 Bean 容器的集成

注解系统与 Bean 容器紧密集成，通过装饰器定义 Bean，并由容器负责 Bean 的生命周期管理。

```python
from miniboot.annotations import component, autowired, post_construct, pre_destroy
from miniboot.bean import ApplicationContext

# 定义组件
@component("userService")
class UserService:
    def __init__(self):
        self.user_repository = None

    @autowired(name="userRepository")
    def set_user_repository(self, repository):
        self.user_repository = repository

    @post_construct
    def init(self):
        print("UserService initialized")

    @pre_destroy
    def cleanup(self):
        print("UserService destroyed")

# 创建应用上下文并扫描组件
context = ApplicationContext()
context.scan("com.example.services")
context.refresh()

# 获取Bean
user_service = context.get_bean("userService")
```

### 4.2 与环境配置的集成

注解系统与环境配置模块集成，支持从配置中读取值并注入到 Bean 中。

```python
from miniboot.annotations import component, value, configuration_properties

@component("cacheService")
class CacheService:
    def __init__(self):
        self.cache_size = None
        self.cache_ttl = None

    @value("${cache.size:100}")
    def set_cache_size(self, size):
        self.cache_size = int(size)

    @value("${cache.ttl:3600}")
    def set_cache_ttl(self, ttl):
        self.cache_ttl = int(ttl)

@configuration_properties(prefix="database")
class DatabaseConfig:
    def __init__(self):
        self.url = ""
        self.username = ""
        self.password = ""
        self.pool_size = 10
```

### 4.3 与异步执行的集成

注解系统与异步执行模块集成，支持异步执行方法。

```python
from miniboot.annotations import component, async_method

@component("emailService")
class EmailService:
    @async_method(pool="email-pool")
    async def send_email(self, to: str, subject: str, body: str):
        # 异步发送邮件
        print(f"Sending email to {to}: {subject}")
        await asyncio.sleep(1)  # 模拟发送过程
        print("Email sent successfully")
```

### 4.4 与调度系统的集成

注解系统与调度系统集成，支持定时执行方法。

```python
from miniboot.annotations import component, scheduled

@component("reportService")
class ReportService:
    @scheduled(cron="0 0 12 * * ?")
    def generate_daily_report(self):
        """每天中午12点生成日报"""
        print("Generating daily report...")

    @scheduled(fixed_rate="5m")
    def health_check(self):
        """每5分钟执行健康检查"""
        print("Performing health check...")

    @scheduled(fixed_delay="10s", initial_delay="30s")
    def cleanup_temp_files(self):
        """清理临时文件，上次执行完成后延迟10秒再执行"""
        print("Cleaning up temporary files...")
```

### 4.5 与事件系统的集成

注解系统与事件系统集成，支持事件监听。

```python
from miniboot.annotations import component, event_listener

@component("userEventListener")
class UserEventListener:
    @event_listener(async_exec=True, condition="event.type == 'REGISTER'")
    async def on_user_register(self, event):
        """处理用户注册事件"""
        print(f"User registered: {event.user_id}")
        # 发送欢迎邮件
        await self.send_welcome_email(event.user_id)

    @event_listener(order=1)
    def on_user_login(self, event):
        """处理用户登录事件"""
        print(f"User logged in: {event.user_id}")
        # 记录登录日志
        self.log_user_activity(event.user_id, "LOGIN")
```

## 4. 实际使用示例

### 4.1 基本组件定义

```python
from miniboot.annotations import Component, Service, Autowired, PostConstruct, PreDestroy

# 基本组件
@Component
class UserRepository:
    def __init__(self):
        self.connection = None

    @PostConstruct
    def init(self):
        print("UserRepository initialized")
        self.connection = "database_connection"

    @PreDestroy
    def cleanup(self):
        print("UserRepository destroyed")
        self.connection = None

    def find_by_id(self, user_id: int):
        return {"id": user_id, "name": f"User{user_id}"}

# 服务组件
@Service("userService")
class UserService:
    def __init__(self):
        self.user_repository = None

    @Autowired
    def set_user_repository(self, user_repository: UserRepository):
        self.user_repository = user_repository

    def get_user(self, user_id: int):
        return self.user_repository.find_by_id(user_id)
```

### 4.2 配置类和 Bean 定义

```python
from miniboot.annotations import Configuration, Bean, ComponentScan, MiniBootApplication

@Configuration
@ComponentScan(["com.example.services", "com.example.repositories"])
class AppConfig:

    @Bean(name="dataSource")
    def create_data_source(self):
        return DataSource("********************************")

    @Bean
    def create_cache_manager(self):
        return CacheManager()

# 或者使用组合注解
@MiniBootApplication(base_packages=["com.example"])
class Application:
    pass
```

### 4.3 条件装配示例

```python
from miniboot.annotations import Component, ConditionalOnProperty, ConditionalOnWeb

@Component
@ConditionalOnProperty(prefix="cache", name="enabled", havingValue="true")
class RedisCacheService:
    def get(self, key: str):
        return f"Redis value for {key}"

@Component
@ConditionalOnWeb
class WebSecurityConfig:
    def configure_security(self):
        print("Configuring web security")
```

### 4.4 异步和调度功能

```python
from miniboot.annotations import Component, Async, Scheduled, EnableAsync, EnableScheduling

@EnableAsync
@EnableScheduling
@MiniBootApplication
class Application:
    pass

@Component
class EmailService:
    @Async
    async def send_email(self, to: str, subject: str, body: str):
        print(f"Sending email to {to}: {subject}")
        # 模拟异步发送
        await asyncio.sleep(1)
        print("Email sent successfully")

@Component
class ReportService:
    @Scheduled(cron="0 0 12 * * ?")  # 每天中午12点
    def generate_daily_report(self):
        print("Generating daily report...")

    @Scheduled(fixed_rate=300)  # 每5分钟
    def health_check(self):
        print("Performing health check...")
```

### 4.5 Web 控制器

```python
from miniboot.annotations import RestController, GetMapping, PostMapping, Autowired

@RestController(prefix="/api/users")
class UserController:
    def __init__(self):
        self.user_service = None

    @Autowired
    def set_user_service(self, user_service: UserService):
        self.user_service = user_service

    @GetMapping("/{user_id}")
    def get_user(self, user_id: int):
        return self.user_service.get_user(user_id)

    @PostMapping("/")
    def create_user(self, user_data: dict):
        return self.user_service.create_user(user_data)
```

### 4.6 配置属性绑定

```python
from miniboot.annotations import ConfigurationProperties, Component

@ConfigurationProperties(prefix="database")
@Component
class DatabaseConfig:
    def __init__(self):
        self.url = ""
        self.username = ""
        self.password = ""
        self.pool_size = 10
        self.timeout = 30

    def get_connection_string(self):
        return f"{self.url}?user={self.username}&timeout={self.timeout}"
```

## 6. 与 Go 版本对比

| 特性         | Go 版本               | Python 版本           |
| ------------ | --------------------- | --------------------- |
| 注解实现方式 | 注释 + 正则表达式解析 | 装饰器 + 元数据       |
| 扫描机制     | AST 解析 Go 源码      | 反射扫描 Python 模块  |
| 类型安全     | 反射类型检查          | 类型注解 + 运行时检查 |
| 依赖注入     | 结构体字段标签        | 方法装饰器            |
| 生命周期管理 | 方法注释标记          | 方法装饰器            |
| 条件装配     | 注释条件表达式        | 装饰器条件参数        |
| 配置绑定     | 结构体标签            | 类装饰器              |
| 异步支持     | 协程池注释            | async/await 装饰器    |
| 调度支持     | cron 表达式注释       | 调度装饰器            |
| 事件监听     | 方法注释              | 事件监听装饰器        |

## 7. 注解扫描技术对比

### 7.1 Go 版本的实现

Go 版本使用注释作为注解载体，通过以下技术实现：

1. **AST 解析**: 使用`go/ast`包解析 Go 源代码
2. **注释提取**: 从 AST 中提取类型、字段、方法的注释
3. **正则表达式**: 通过正则表达式解析注释中的注解信息

### 7.2 Python 版本的实现

Python 版本使用装饰器作为注解载体，通过以下技术实现：

1. **模块导入**: 使用`importlib`动态导入 Python 模块
2. **反射检查**: 使用`inspect`模块检查类和方法
3. **元数据存储**: 将装饰器参数存储为对象属性

### 7.3 技术优势对比

#### Go 版本优势

-   编译时检查，性能更好
-   不影响运行时对象结构
-   支持复杂的条件表达式

#### Python 版本优势

-   语言原生支持，更加自然
-   类型安全，IDE 支持更好
-   运行时可动态修改
-   装饰器语法更加简洁

## 5. 总结

Mini-Boot 框架的注解系统是一个功能丰富、设计灵活的组件，它通过 Python 装饰器的方式简化了依赖注入、条件装配、异步执行等功能的实现。基于现有的实际代码实现，该系统具有以下特点：

### 核心特性

1. **完整的组件生命周期管理**

    - 支持@Component、@Service、@Configuration 等组件定义
    - 提供@PostConstruct、@PreDestroy 生命周期钩子
    - 支持@Bean 方法定义和@Autowired 依赖注入

2. **灵活的条件装配机制**

    - @ConditionalOnProperty 基于配置的条件装配
    - @ConditionalOnWeb、@ConditionalOnScheduling 功能条件装配
    - 支持复杂的条件表达式和环境判断

3. **强大的异步和调度支持**

    - @Async 异步方法执行
    - @Scheduled 定时任务调度，支持 cron 表达式和固定间隔
    - @EnableAsync、@EnableScheduling 功能开关

4. **完整的 Web 支持**

    - @RestController、@GetMapping、@PostMapping 等 Web 注解
    - 支持路径参数、查询参数、请求体等多种参数类型
    - 集成 FastAPI 风格的 API 文档生成

5. **配置属性绑定**
    - @ConfigurationProperties 配置属性自动绑定
    - @Value 单个配置值注入
    - 支持配置验证和类型转换

### 实现优势

1. **Python 原生特性**

    - 充分利用 Python 装饰器语法，代码简洁自然
    - 支持类型注解，提供更好的 IDE 支持和类型检查
    - 运行时反射机制，支持动态组件发现

2. **模块化设计**

    - 注解定义与扫描器分离，职责清晰
    - 支持包级别的组件扫描，灵活配置扫描范围
    - 与 Bean 容器、环境配置等模块深度集成

3. **扩展性强**
    - 支持自定义注解和条件装配
    - 提供丰富的扩展点和钩子函数
    - 易于添加新的功能模块

### 与 Spring 框架对比

| 特性     | Spring Framework                 | Mini-Boot                     |
| -------- | -------------------------------- | ----------------------------- |
| 组件定义 | @Component、@Service             | @Component、@Service          |
| 依赖注入 | @Autowired、@Value               | @Autowired、@Value            |
| 配置类   | @Configuration、@Bean            | @Configuration、@Bean         |
| 条件装配 | @ConditionalOnProperty           | @ConditionalOnProperty        |
| 异步支持 | @Async、@EnableAsync             | @Async、@EnableAsync          |
| 调度支持 | @Scheduled、@EnableScheduling    | @Scheduled、@EnableScheduling |
| Web 支持 | @RestController、@RequestMapping | @RestController、@GetMapping  |
| 配置绑定 | @ConfigurationProperties         | @ConfigurationProperties      |

### 使用场景

1. **企业级应用开发**

    - 支持复杂的依赖注入和组件管理
    - 提供完整的配置管理和环境隔离
    - 支持微服务架构和分布式部署

2. **Web 应用开发**

    - 集成 Web 框架，支持 RESTful API 开发
    - 提供请求处理、参数绑定、响应序列化等功能
    - 支持 API 文档自动生成

3. **异步和定时任务**

    - 支持异步方法执行和协程池管理
    - 提供灵活的定时任务调度机制
    - 支持任务监控和错误处理

4. **配置驱动开发**
    - 支持外部化配置和环境变量
    - 提供配置验证和类型安全
    - 支持配置热更新和动态刷新

通过注解系统，Mini-Boot 框架为 Python 开发提供了一种更加声明式、更加面向对象的编程模式，使得代码更加简洁、清晰和易于维护，同时保持了与 Spring 框架相似的开发体验。

---

_本文档定义了 Mini-Boot 框架的注解系统设计，提供声明式的组件管理功能。_
