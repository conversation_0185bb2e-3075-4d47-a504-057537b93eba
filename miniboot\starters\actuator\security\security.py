#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Actuator 安全模块

提供 Actuator 的安全管理功能,支持基本认证和访问控制.
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Set

from loguru import logger

from ..properties import SecurityProperties


class ActuatorSecurity:
    """Actuator 安全管理器"""

    def __init__(self, config: SecurityProperties):
        """初始化安全管理器

        Args:
            config: 安全配置
        """
        self.config = config
        self.enabled = config.enabled
        self.username = config.username
        self.password = config.password
        self.cors_enabled = config.cors_enabled

        # 安全统计
        self._access_attempts = 0
        self._successful_access = 0
        self._failed_access = 0
        self._start_time = time.time()

        logger.info(f"ActuatorSecurity initialized: enabled={self.enabled}, username={self.username}")

    async def initialize(self) -> None:
        """初始化安全配置"""
        try:
            if self.enabled:
                # 验证基本认证配置
                if not self.username or not self.password:
                    logger.warning("Security enabled but username/password not configured")

                logger.info("✅ Security configuration validated")
            else:
                logger.info("ℹ️ Security is disabled")

        except Exception as e:
            logger.error(f"❌ Security initialization failed: {e}")
            raise

    def check_access(self, endpoint: str, username: Optional[str] = None, password: Optional[str] = None) -> bool:
        """检查端点访问权限

        Args:
            endpoint: 端点名称
            username: 用户名
            password: 密码

        Returns:
            bool: 是否允许访问
        """
        try:
            self._access_attempts += 1

            # 如果安全未启用,允许所有访问
            if not self.enabled:
                self._successful_access += 1
                return True

            # 如果用户没有提供认证信息,拒绝访问
            if not username or not password:
                logger.warning(f"Access denied to {endpoint}: no credentials provided")
                self._failed_access += 1
                return False

            # 检查认证信息
            if username == self.username and password == self.password:
                logger.debug(f"Access granted to {endpoint}: authentication successful")
                self._successful_access += 1
                return True
            else:
                logger.warning(f"Access denied to {endpoint}: authentication failed")
                self._failed_access += 1
                return False

        except Exception as e:
            logger.error(f"Error checking access to {endpoint}: {e}")
            self._failed_access += 1
            return False

    def get_info(self) -> Dict[str, Any]:
        """获取安全信息"""
        uptime = time.time() - self._start_time

        return {
            "enabled": self.enabled,
            "username": self.username if self.enabled else None,
            "cors_enabled": self.cors_enabled,
            "statistics": {
                "total_access_attempts": self._access_attempts,
                "successful_access": self._successful_access,
                "failed_access": self._failed_access,
                "success_rate": (self._successful_access / max(self._access_attempts, 1)) * 100,
                "uptime_seconds": uptime,
            },
        }

    def update_config(self, config: SecurityProperties) -> None:
        """更新安全配置"""
        old_enabled = self.enabled

        self.config = config
        self.enabled = config.enabled
        self.username = config.username
        self.password = config.password
        self.cors_enabled = config.cors_enabled

        if old_enabled != self.enabled:
            logger.info(f"Security status changed: {old_enabled} -> {self.enabled}")

    def reset_statistics(self) -> None:
        """重置安全统计"""
        self._access_attempts = 0
        self._successful_access = 0
        self._failed_access = 0
        self._start_time = time.time()
        logger.info("Security statistics reset")

    def is_authenticated(self, username: Optional[str], password: Optional[str]) -> bool:
        """检查认证信息是否正确

        Args:
            username: 用户名
            password: 密码

        Returns:
            bool: 认证是否成功
        """
        if not self.enabled:
            return True

        return username == self.username and password == self.password and username is not None and password is not None

    def get_cors_headers(self) -> Dict[str, str]:
        """获取CORS头信息"""
        if not self.cors_enabled:
            return {}

        return {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
            "Access-Control-Max-Age": "86400",
        }

    def validate_request(self, request_headers: Dict[str, str]) -> bool:
        """验证请求

        Args:
            request_headers: 请求头

        Returns:
            bool: 请求是否有效
        """
        if not self.enabled:
            return True

        # 检查基本认证头
        auth_header = request_headers.get("Authorization", "")
        if not auth_header.startswith("Basic "):
            return False

        try:
            import base64

            encoded_credentials = auth_header[6:]  # 移除 "Basic " 前缀
            decoded_credentials = base64.b64decode(encoded_credentials).decode("utf-8")
            username, password = decoded_credentials.split(":", 1)
            return self.is_authenticated(username, password)
        except Exception:
            return False

    def __str__(self) -> str:
        return f"ActuatorSecurity(enabled={self.enabled}, username={self.username})"

    def __repr__(self) -> str:
        return self.__str__()
