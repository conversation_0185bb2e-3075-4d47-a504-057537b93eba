#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 类型检查缓存系统 - 高性能类型检查和方法签名缓存
"""

import inspect
import threading
from functools import lru_cache
from typing import Any, Optional, get_type_hints
from weakref import WeakKeyDictionary

from miniboot.utils import SingletonMeta, WeakReferenceCache


class TypeCheckCache(metaclass=SingletonMeta):
    """类型检查缓存系统

    提供高性能的类型检查和方法签名缓存功能,显著减少反射操作的开销.
    使用多层缓存策略:
    1. 弱引用缓存 - 自动清理无用类的缓存
    2. LRU缓存 - 限制内存使用
    3. 线程安全 - 支持并发访问

    使用单例模式确保全局唯一的类型检查缓存.
    """

    def __init__(self, max_size: int = 1000):
        """初始化类型检查缓存

        Args:
            max_size: LRU缓存的最大大小
        """
        # 防止重复初始化(单例模式要求)
        if not hasattr(self, '_initialized'):
            self._max_size = max_size
            self._lock = threading.RLock()

            # 使用统一的缓存基类
            self._type_hints_cache = WeakReferenceCache(max_size)
            self._method_signatures_cache = WeakReferenceCache(max_size)
            self._class_attributes_cache = WeakReferenceCache(max_size)

            self._initialized = True

    @lru_cache(maxsize=1000)
    def _get_type_hints_cached(self, cls: type) -> tuple[tuple[str, str], ...]:
        """获取类型提示(LRU缓存版本)

        Args:
            cls: 类对象

        Returns:
            类型提示元组(用于缓存)
        """
        try:
            type_hints = get_type_hints(cls)
            # 转换为可缓存的格式
            return tuple((name, str(hint)) for name, hint in type_hints.items())
        except Exception:
            # 如果获取类型提示失败,使用__annotations__
            annotations = getattr(cls, "__annotations__", {})
            return tuple((name, str(hint)) for name, hint in annotations.items())

    def get_type_hints(self, cls: type) -> dict[str, type]:
        """获取类的类型提示(使用缓存)

        Args:
            cls: 类对象

        Returns:
            类型提示字典
        """
        with self._lock:
            # 检查弱引用缓存
            if cls in self._type_hints_cache:
                self._stats["type_hints_hits"] += 1
                return self._type_hints_cache[cls]

            # 从LRU缓存获取
            try:
                cached_tuple = self._get_type_hints_cached(cls)
                # 重建类型提示字典
                type_hints = {}
                for name, hint_str in cached_tuple:
                    try:
                        # 尝试从类的模块中解析类型
                        if hasattr(cls, "__module__"):
                            module = inspect.getmodule(cls)
                            if module and hasattr(module, "__dict__"):
                                # 在模块命名空间中查找类型
                                if hint_str in module.__dict__:
                                    type_hints[name] = module.__dict__[hint_str]
                                    continue

                        # 回退到字符串表示
                        type_hints[name] = hint_str
                    except Exception:
                        # 如果解析失败,使用字符串
                        type_hints[name] = hint_str

                # 存储到弱引用缓存
                self._type_hints_cache[cls] = type_hints
                self._stats["type_hints_misses"] += 1

                return type_hints

            except Exception:
                # 如果所有方法都失败,返回空字典
                empty_hints = {}
                self._type_hints_cache[cls] = empty_hints
                self._stats["type_hints_misses"] += 1
                return empty_hints

    @lru_cache(maxsize=1000)
    def _get_method_signature_cached(self, method: Any) -> Optional[str]:
        """获取方法签名(LRU缓存版本)

        Args:
            method: 方法对象

        Returns:
            方法签名的字符串表示
        """
        try:
            sig = inspect.signature(method)
            return str(sig)
        except Exception:
            return None

    def get_method_signature(self, cls: type, method_name: str) -> Optional[inspect.Signature]:
        """获取方法签名(使用缓存)

        Args:
            cls: 类对象
            method_name: 方法名称

        Returns:
            方法签名对象
        """
        with self._lock:
            # 检查弱引用缓存
            if cls in self._method_signatures_cache:
                signatures = self._method_signatures_cache[cls]
                if method_name in signatures:
                    self._stats["signature_hits"] += 1
                    return signatures[method_name]
            else:
                self._method_signatures_cache[cls] = {}

            # 获取方法并解析签名
            try:
                method = getattr(cls, method_name)
                sig_str = self._get_method_signature_cached(method)

                if sig_str:
                    # 重新构建签名对象
                    sig = inspect.signature(method)
                    self._method_signatures_cache[cls][method_name] = sig
                    self._stats["signature_misses"] += 1
                    return sig
                else:
                    self._stats["signature_misses"] += 1
                    return None

            except Exception:
                self._stats["signature_misses"] += 1
                return None

    @lru_cache(maxsize=1000)
    def _get_class_attributes_cached(self, cls: type) -> tuple[str, ...]:
        """获取类属性列表(LRU缓存版本)

        Args:
            cls: 类对象

        Returns:
            属性名称元组
        """
        try:
            # 过滤掉私有属性
            attributes = [attr for attr in dir(cls) if not attr.startswith("_")]
            return tuple(attributes)
        except Exception:
            return ()

    def get_class_attributes(self, cls: type) -> list[str]:
        """获取类的公共属性列表(使用缓存)

        Args:
            cls: 类对象

        Returns:
            属性名称列表
        """
        with self._lock:
            # 检查弱引用缓存
            if cls in self._class_attributes_cache:
                self._stats["attributes_hits"] += 1
                return self._class_attributes_cache[cls]

            # 从LRU缓存获取
            cached_tuple = self._get_class_attributes_cached(cls)
            attributes = list(cached_tuple)

            # 存储到弱引用缓存
            self._class_attributes_cache[cls] = attributes
            self._stats["attributes_misses"] += 1

            return attributes

    def get_attribute_safely(self, cls: type, attr_name: str) -> Any:
        """安全地获取类属性

        Args:
            cls: 类对象
            attr_name: 属性名称

        Returns:
            属性值,如果不存在则返回None
        """
        try:
            return getattr(cls, attr_name)
        except Exception:
            return None

    def is_method_or_function(self, obj: Any) -> bool:
        """检查对象是否是方法或函数(优化版本)

        Args:
            obj: 要检查的对象

        Returns:
            是否是方法或函数
        """
        # 使用更快的类型检查
        return callable(obj) and (inspect.isfunction(obj) or inspect.ismethod(obj))

    def get_method_parameter_types(self, cls: type, method_name: str) -> list[tuple[str, type]]:
        """获取方法参数类型列表

        Args:
            cls: 类对象
            method_name: 方法名称

        Returns:
            参数类型列表:[(param_name, param_type), ...]
        """
        sig = self.get_method_signature(cls, method_name)
        if not sig:
            return []

        param_types = []
        # 跳过self参数
        parameters = list(sig.parameters.values())[1:]

        for param in parameters:
            param_type = param.annotation
            if param_type != inspect.Parameter.empty:
                param_types.append((param.name, param_type))

        return param_types

    def clear_cache(self) -> None:
        """清理所有缓存"""
        with self._lock:
            self._type_hints_cache.clear()
            self._method_signatures_cache.clear()
            self._class_attributes_cache.clear()
            self._get_type_hints_cached.cache_clear()
            self._get_method_signature_cached.cache_clear()
            self._get_class_attributes_cached.cache_clear()
            self._stats = {
                "type_hints_hits": 0,
                "type_hints_misses": 0,
                "signature_hits": 0,
                "signature_misses": 0,
                "attributes_hits": 0,
                "attributes_misses": 0,
            }

    def cleanup(self) -> None:
        """清理资源(单例重置时调用)"""
        self.clear_cache()

    def get_cache_stats(self) -> dict[str, Any]:
        """获取缓存统计信息

        Returns:
            缓存统计信息
        """
        with self._lock:
            total_type_hints = self._stats["type_hints_hits"] + self._stats["type_hints_misses"]
            total_signatures = self._stats["signature_hits"] + self._stats["signature_misses"]
            total_attributes = self._stats["attributes_hits"] + self._stats["attributes_misses"]

            return {
                "type_hints": {
                    "hits": self._stats["type_hints_hits"],
                    "misses": self._stats["type_hints_misses"],
                    "hit_ratio": self._stats["type_hints_hits"] / total_type_hints if total_type_hints > 0 else 0,
                },
                "signatures": {
                    "hits": self._stats["signature_hits"],
                    "misses": self._stats["signature_misses"],
                    "hit_ratio": self._stats["signature_hits"] / total_signatures if total_signatures > 0 else 0,
                },
                "attributes": {
                    "hits": self._stats["attributes_hits"],
                    "misses": self._stats["attributes_misses"],
                    "hit_ratio": self._stats["attributes_hits"] / total_attributes if total_attributes > 0 else 0,
                },
                "cache_sizes": {
                    "type_hints": len(self._type_hints_cache),
                    "signatures": len(self._method_signatures_cache),
                    "attributes": len(self._class_attributes_cache),
                },
                "lru_cache_info": {
                    "type_hints": self._get_type_hints_cached.cache_info(),
                    "signatures": self._get_method_signature_cached.cache_info(),
                    "attributes": self._get_class_attributes_cached.cache_info(),
                },
            }


def cache() -> TypeCheckCache:
    """获取全局类型检查缓存实例

    Returns:
        TypeCheckCache: 单例的类型检查缓存实例
    """
    return TypeCheckCache()


def clear_cache() -> None:
    """清理全局类型检查缓存"""
    type_cache = cache()
    type_cache.clear_cache()


def cache_stats() -> dict[str, Any]:
    """获取类型缓存统计信息

    Returns:
        缓存统计信息
    """
    type_cache = cache()
    return type_cache.get_cache_stats()
