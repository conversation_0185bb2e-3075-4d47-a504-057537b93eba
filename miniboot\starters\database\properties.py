#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Database Starter配置属性
"""

from dataclasses import dataclass

from miniboot.annotations import ConfigurationProperties
from miniboot.autoconfigure.properties import StarterProperties


@ConfigurationProperties(prefix="miniboot.starters.database")
@dataclass
class DatabaseProperties(StarterProperties):
    """Database Starter配置属性

    用于配置数据库连接的基本参数.
    """

    # 基础配置
    enabled: bool = True  # 是否启用数据库功能
    auto_connect: bool = True  # 是否自动连接数据库
    url: str = "sqlite:///app.db"  # 数据库连接URL
    driver: str = "sqlite"  # 数据库驱动类型
    pool_size: int = 5  # 连接池大小
