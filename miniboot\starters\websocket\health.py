#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 健康检查指示器
"""

import asyncio
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Optional

from miniboot.annotations import Component

from .metrics import WebSocketMetrics
from .service import WebSocketService


class HealthStatus(Enum):
    """健康状态枚举"""

    UP = "UP"
    DOWN = "DOWN"
    OUT_OF_SERVICE = "OUT_OF_SERVICE"
    UNKNOWN = "UNKNOWN"


class HealthDetail:
    """健康检查详情"""

    def __init__(self, status: HealthStatus, message: str = "", details: Optional[dict[str, Any]] = None):
        """初始化健康检查详情

        Args:
            status: 健康状态
            message: 状态消息
            details: 详细信息
        """
        self.status = status
        self.message = message
        self.details = details or {}
        self.timestamp = datetime.now()

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式

        Returns:
            Dict[str, Any]: 健康检查详情字典
        """
        return {"status": self.status.value, "message": self.message, "timestamp": self.timestamp.isoformat(), "details": self.details}


@Component
class WebSocketHealthIndicator:
    """WebSocket 健康检查指示器

    负责监控 WebSocket 服务的健康状态,包括连接状态、性能指标、
    错误率等多个维度的健康检查.
    """

    def __init__(self, websocket_service: Optional[WebSocketService] = None, websocket_metrics: Optional[WebSocketMetrics] = None):
        """初始化健康检查指示器

        Args:
            websocket_service: WebSocket 服务实例
            websocket_metrics: WebSocket 指标收集器
        """
        self.websocket_service = websocket_service
        self.websocket_metrics = websocket_metrics

        # 健康检查配置
        self.max_error_rate = 0.1  # 最大错误率 10%
        self.max_response_time_ms = 5000  # 最大响应时间 5 秒
        self.min_success_rate = 0.9  # 最小成功率 90%
        self.max_memory_usage_mb = 1024  # 最大内存使用 1GB

        # 健康检查历史
        self._health_history: list[HealthDetail] = []
        self._last_check_time: Optional[datetime] = None

        # 检查间隔(秒)
        self.check_interval = 30

    async def check_health(self) -> HealthDetail:
        """执行完整的健康检查

        Returns:
            HealthDetail: 健康检查结果
        """
        try:
            self._last_check_time = datetime.now()

            # 执行各项健康检查
            checks = [
                self._check_service_status(),
                self._check_connection_health(),
                self._check_message_health(),
                self._check_performance_health(),
                self._check_error_rate(),
                await self._check_connectivity(),
            ]

            # 收集所有检查结果
            results = []
            for check in checks:
                if asyncio.iscoroutine(check):
                    results.append(await check)
                else:
                    results.append(check)

            # 综合评估健康状态
            overall_status = self._evaluate_overall_health(results)

            # 创建健康检查详情
            health_detail = HealthDetail(
                status=overall_status,
                message=self._get_status_message(overall_status, results),
                details={"checks": [result.to_dict() for result in results], "summary": self._get_health_summary()},
            )

            # 记录健康检查历史
            self._record_health_history(health_detail)

            return health_detail

        except Exception as e:
            return HealthDetail(status=HealthStatus.UNKNOWN, message=f"Health check failed: {e}", details={"error": str(e)})

    def _check_service_status(self) -> HealthDetail:
        """检查服务状态

        Returns:
            HealthDetail: 服务状态检查结果
        """
        if not self.websocket_service:
            return HealthDetail(status=HealthStatus.OUT_OF_SERVICE, message="WebSocket service not available")

        if not self.websocket_service._initialized:
            return HealthDetail(status=HealthStatus.DOWN, message="WebSocket service not initialized")

        return HealthDetail(
            status=HealthStatus.UP,
            message="WebSocket service is running",
            details={
                "initialized": self.websocket_service._initialized,
                "running": self.websocket_service._running,
                "registered_controllers": len(self.websocket_service._controllers),
            },
        )

    def _check_connection_health(self) -> HealthDetail:
        """检查连接健康状态

        Returns:
            HealthDetail: 连接健康检查结果
        """
        if not self.websocket_metrics:
            return HealthDetail(status=HealthStatus.UNKNOWN, message="Metrics not available for connection health check")

        conn_metrics = self.websocket_metrics.get_connection_metrics()

        # 检查连接成功率
        success_rate = conn_metrics.get("success_rate", 0)
        if success_rate < self.min_success_rate:
            return HealthDetail(status=HealthStatus.DOWN, message=f"Connection success rate too low: {success_rate:.2%}", details=conn_metrics)

        # 检查活跃连接数
        active_connections = conn_metrics.get("active_connections", 0)
        peak_connections = conn_metrics.get("peak_connections", 0)

        return HealthDetail(
            status=HealthStatus.UP,
            message=f"Connection health is good (success rate: {success_rate:.2%})",
            details={"active_connections": active_connections, "peak_connections": peak_connections, "success_rate": success_rate},
        )

    def _check_message_health(self) -> HealthDetail:
        """检查消息处理健康状态

        Returns:
            HealthDetail: 消息健康检查结果
        """
        if not self.websocket_metrics:
            return HealthDetail(status=HealthStatus.UNKNOWN, message="Metrics not available for message health check")

        msg_metrics = self.websocket_metrics.get_message_metrics()

        # 检查消息失败率
        failure_rate = msg_metrics.get("message_failure_rate", 0)
        if failure_rate > self.max_error_rate:
            return HealthDetail(status=HealthStatus.DOWN, message=f"Message failure rate too high: {failure_rate:.2%}", details=msg_metrics)

        # 检查消息处理时间
        avg_processing_time = msg_metrics.get("average_processing_time_ms", 0)
        if avg_processing_time > self.max_response_time_ms:
            return HealthDetail(
                status=HealthStatus.DOWN, message=f"Message processing time too high: {avg_processing_time:.2f}ms", details=msg_metrics
            )

        return HealthDetail(
            status=HealthStatus.UP,
            message="Message processing is healthy",
            details={
                "total_messages": msg_metrics.get("total_messages_sent", 0) + msg_metrics.get("total_messages_received", 0),
                "failure_rate": failure_rate,
                "avg_processing_time_ms": avg_processing_time,
            },
        )

    def _check_performance_health(self) -> HealthDetail:
        """检查性能健康状态

        Returns:
            HealthDetail: 性能健康检查结果
        """
        if not self.websocket_metrics:
            return HealthDetail(status=HealthStatus.UNKNOWN, message="Metrics not available for performance health check")

        perf_metrics = self.websocket_metrics.get_performance_metrics()

        # 检查响应时间
        avg_response_time = perf_metrics.get("average_response_time_ms", 0)
        p95_response_time = perf_metrics.get("p95_response_time_ms", 0)

        if p95_response_time > self.max_response_time_ms:
            return HealthDetail(status=HealthStatus.DOWN, message=f"P95 response time too high: {p95_response_time:.2f}ms", details=perf_metrics)

        return HealthDetail(
            status=HealthStatus.UP,
            message="Performance is healthy",
            details={
                "avg_response_time_ms": avg_response_time,
                "p95_response_time_ms": p95_response_time,
                "total_errors": perf_metrics.get("total_errors", 0),
            },
        )

    def _check_error_rate(self) -> HealthDetail:
        """检查错误率

        Returns:
            HealthDetail: 错误率检查结果
        """
        if not self.websocket_metrics:
            return HealthDetail(status=HealthStatus.UNKNOWN, message="Metrics not available for error rate check")

        # 获取最近的错误
        recent_errors = self.websocket_metrics.get_recent_errors(limit=100)

        # 计算最近1小时的错误率
        one_hour_ago = datetime.now() - timedelta(hours=1)
        recent_error_count = 0

        for error in recent_errors:
            try:
                error_time = datetime.fromisoformat(error["timestamp"])
                if error_time > one_hour_ago:
                    recent_error_count += 1
            except (KeyError, ValueError):
                continue

        # 获取总操作数(连接 + 消息)
        conn_metrics = self.websocket_metrics.get_connection_metrics()
        msg_metrics = self.websocket_metrics.get_message_metrics()

        total_operations = (
            conn_metrics.get("connection_attempts", 0) + msg_metrics.get("total_messages_sent", 0) + msg_metrics.get("total_messages_received", 0)
        )

        if total_operations > 0:
            error_rate = recent_error_count / total_operations
            if error_rate > self.max_error_rate:
                return HealthDetail(
                    status=HealthStatus.DOWN,
                    message=f"Error rate too high: {error_rate:.2%}",
                    details={"recent_errors": recent_error_count, "total_operations": total_operations, "error_rate": error_rate},
                )

        return HealthDetail(
            status=HealthStatus.UP,
            message="Error rate is acceptable",
            details={"recent_errors": recent_error_count, "total_operations": total_operations},
        )

    async def _check_connectivity(self) -> HealthDetail:
        """检查连接性(可以扩展为实际的连接测试)

        Returns:
            HealthDetail: 连接性检查结果
        """
        try:
            # 这里可以实现实际的连接测试
            # 例如:创建一个测试连接,发送ping消息等

            # 模拟连接测试
            start_time = time.time()
            await asyncio.sleep(0.01)  # 模拟网络延迟
            response_time = (time.time() - start_time) * 1000

            return HealthDetail(
                status=HealthStatus.UP,
                message="Connectivity test passed",
                details={"test_response_time_ms": round(response_time, 2), "test_timestamp": datetime.now().isoformat()},
            )

        except Exception as e:
            return HealthDetail(status=HealthStatus.DOWN, message=f"Connectivity test failed: {e}", details={"error": str(e)})

    def _evaluate_overall_health(self, results: list[HealthDetail]) -> HealthStatus:
        """评估整体健康状态

        Args:
            results: 各项检查结果

        Returns:
            HealthStatus: 整体健康状态
        """
        if not results:
            return HealthStatus.UNKNOWN

        # 如果有任何检查失败,整体状态为DOWN
        for result in results:
            if result.status == HealthStatus.DOWN:
                return HealthStatus.DOWN

        # 如果有服务不可用,整体状态为OUT_OF_SERVICE
        for result in results:
            if result.status == HealthStatus.OUT_OF_SERVICE:
                return HealthStatus.OUT_OF_SERVICE

        # 如果有未知状态,整体状态为UNKNOWN
        for result in results:
            if result.status == HealthStatus.UNKNOWN:
                return HealthStatus.UNKNOWN

        # 所有检查都通过,整体状态为UP
        return HealthStatus.UP

    def _get_status_message(self, status: HealthStatus, results: list[HealthDetail]) -> str:
        """获取状态消息

        Args:
            status: 整体健康状态
            results: 各项检查结果

        Returns:
            str: 状态消息
        """
        if status == HealthStatus.UP:
            return f"WebSocket service is healthy (passed {len(results)} checks)"
        elif status == HealthStatus.DOWN:
            failed_checks = [r for r in results if r.status == HealthStatus.DOWN]
            return f"WebSocket service is unhealthy ({len(failed_checks)} checks failed)"
        elif status == HealthStatus.OUT_OF_SERVICE:
            return "WebSocket service is out of service"
        else:
            return "WebSocket service health status is unknown"

    def _get_health_summary(self) -> dict[str, Any]:
        """获取健康状态摘要

        Returns:
            Dict[str, Any]: 健康状态摘要
        """
        summary = {
            "last_check_time": self._last_check_time.isoformat() if self._last_check_time else None,
            "check_interval_seconds": self.check_interval,
            "thresholds": {
                "max_error_rate": self.max_error_rate,
                "max_response_time_ms": self.max_response_time_ms,
                "min_success_rate": self.min_success_rate,
            },
        }

        if self.websocket_metrics:
            summary["metrics_available"] = True
            summary["uptime_seconds"] = self.websocket_metrics.get_all_metrics().get("uptime_seconds", 0)
        else:
            summary["metrics_available"] = False

        return summary

    def _record_health_history(self, health_detail: HealthDetail) -> None:
        """记录健康检查历史

        Args:
            health_detail: 健康检查详情
        """
        self._health_history.append(health_detail)

        # 限制历史记录数量
        if len(self._health_history) > 100:
            self._health_history = self._health_history[-50:]

    def get_health_history(self, limit: int = 10) -> list[dict[str, Any]]:
        """获取健康检查历史

        Args:
            limit: 返回的历史记录数量限制

        Returns:
            List[Dict[str, Any]]: 健康检查历史
        """
        return [detail.to_dict() for detail in self._health_history[-limit:]]

    def is_healthy(self) -> bool:
        """检查服务是否健康

        Returns:
            bool: 服务是否健康
        """
        if not self._health_history:
            return False

        latest_health = self._health_history[-1]
        return latest_health.status == HealthStatus.UP
