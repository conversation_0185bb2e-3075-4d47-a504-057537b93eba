# Mini-Boot Actuator Starter 自动配置注册文件
#
# 此文件定义了 Actuator Starter 的自动配置类，
# 框架启动时会自动扫描并加载这些配置。
#
# 使用标准的 Spring Boot 格式：key=value
# 支持续行符 \ 进行多行配置

# Actuator Starter 自动配置
miniboot.autoconfigure.EnableAutoConfiguration=\
miniboot.starters.actuator.autoconfigure.bean.BeanMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.scheduler.SchedulerMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.context.ContextMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.env.EnvMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.web.WebAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.performance.PerformanceMonitorAutoConfiguration,\
miniboot.starters.actuator.configuration.ActuatorStarterAutoConfiguration
