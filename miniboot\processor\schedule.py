#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务处理器 - 实现@Scheduled注解的自动注册

定时任务处理器负责处理@Scheduled注解标记的方法,
实现定时任务的自动注册功能.支持多种调度策略和任务管理.
"""

import inspect
from typing import Any

from loguru import logger

# 定时任务系统模块是必需依赖
from ..annotations.schedule import get_config
from ..errors import ProcessorExecutionError as BeanProcessingError
from ..schedule.scheduler import MiniBootScheduler
from .base import BeanPostProcessor, ProcessorOrder


def is_scheduled(obj: Any) -> bool:
    """检查对象是否有@Scheduled注解"""
    return hasattr(obj, "__scheduled__") and obj.__scheduled__


class ScheduledAnnotationProcessor(BeanPostProcessor):
    """
    @Scheduled注解处理器

    负责处理@Scheduled注解的定时任务自动注册,支持:
    - 任务扫描:扫描Bean中的@Scheduled方法
    - 自动注册:将任务注册到任务调度器
    - 调度策略:支持cron、fixed_rate、fixed_delay等调度策略
    - 任务管理:支持任务生命周期管理和监控
    - 异步支持:支持异步定时任务

    处理器在Bean初始化后执行,确保Bean完全准备就绪后再注册定时任务.

    Example:
        # 定时任务
        @Component
        class TaskService:
            @Scheduled(cron="0 0 12 * * ?")
            def daily_report(self):
                print("生成日报")

            @Scheduled(fixed_rate="5s")
            def health_check(self):
                print("健康检查")
    """

    def __init__(self, task_scheduler=None):
        """
        初始化定时任务处理器

        Args:
            task_scheduler: 任务调度器实例,用于注册任务
        """
        self._task_scheduler = task_scheduler or MiniBootScheduler()
        self._processed_beans: set[str] = set()
        self._scheduled_cache: dict[type, list[tuple]] = {}
        self._registered_tasks: dict[str, list[str]] = {}  # bean_name -> task_ids

    def set_task_scheduler(self, task_scheduler) -> None:
        """
        设置任务调度器

        Args:
            task_scheduler: 任务调度器实例
        """
        self._task_scheduler = task_scheduler

    def _do_post_process_before_initialization(self, bean: Any, _bean_name: str) -> Any:
        """
        在Bean初始化前处理(定时任务处理器不需要前处理)

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            原始Bean实例
        """
        return bean

    def _do_post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        在Bean初始化后处理@Scheduled注解

        扫描Bean类中的@Scheduled注解,注册定时任务.

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例

        Raises:
            BeanProcessingError: 当定时任务注册失败时
        """
        if bean is None or bean_name in self._processed_beans:
            return bean

        try:
            # 检查是否有@Scheduled注解
            if self._has_scheduled_annotations(bean.__class__):
                # 注册定时任务
                self._register_scheduled_tasks(bean, bean_name)

                # 标记为已处理
                self._processed_beans.add(bean_name)

                logger.debug(f"Completed scheduled task registration for bean: {bean_name}")

            return bean

        except Exception as e:
            raise BeanProcessingError(
                f"Failed to process @Scheduled annotations for bean '{bean_name}'",
                bean_name=bean_name,
                processor_name=self.__class__.__name__,
                cause=e,
            ) from e

    def get_order(self) -> int:
        """
        获取处理器执行顺序

        定时任务处理器需要在事件监听处理器之后执行,
        确保Bean完全初始化后再注册定时任务.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.LIFECYCLE_PROCESSOR + 30  # 在生命周期处理器之后执行

    def supports(self, bean: Any, _bean_name: str) -> bool:
        """
        检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示支持处理此Bean
        """
        if bean is None:
            return False

        # 检查Bean类是否有@Scheduled注解
        return self._has_scheduled_annotations(bean.__class__)

    def destroy_bean(self, bean: Any, bean_name: str) -> None:
        """
        销毁Bean时取消注册定时任务

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        if bean is None or bean_name not in self._registered_tasks:
            return

        try:
            # 取消注册所有任务
            task_ids = self._registered_tasks.get(bean_name, [])
            for task_id in task_ids:
                if self._task_scheduler:
                    self._task_scheduler.remove_task(task_id)

            # 清理记录
            if bean_name in self._registered_tasks:
                del self._registered_tasks[bean_name]

            logger.debug(f"Unregistered {len(task_ids)} scheduled tasks for bean: {bean_name}")

        except Exception as e:
            logger.warning(f"Failed to unregister scheduled tasks for bean '{bean_name}': {e}")

    def _register_scheduled_tasks(self, bean: Any, bean_name: str) -> None:
        """
        注册Bean中的定时任务

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        if self._task_scheduler is None:
            logger.warning(f"Task scheduler not available for bean '{bean_name}', skipping scheduled task registration")
            return

        bean_class = bean.__class__

        # 获取缓存的定时任务信息
        scheduled_info = self._get_scheduled_methods(bean_class)

        task_ids = []

        for method_name, method, config in scheduled_info:
            try:
                # 使用任务管理器创建并注册任务
                if hasattr(self._task_scheduler, "task_manager") and self._task_scheduler.task_manager:
                    task_id = self._task_scheduler.task_manager.create_method_task(
                        method=method,
                        instance=bean,
                        config=config,
                        name=f"{bean_name}.{method_name}",
                        description=f"Scheduled task from {bean_class.__name__}.{method_name}",
                    )
                else:
                    # 回退到直接使用调度器
                    task_id = self._task_scheduler.create_managed_lambda_task(
                        func=lambda m=method: m(bean),
                        config=config,
                        name=f"{bean_name}.{method_name}",
                        description=f"Scheduled task from {bean_class.__name__}.{method_name}",
                    )

                task_ids.append(task_id)
                logger.debug(f"Registered scheduled task '{method_name}' for bean '{bean_name}' with task_id '{task_id}'")

            except Exception as e:
                logger.error(f"Failed to register scheduled task '{method_name}' for bean '{bean_name}': {e}")
                # 继续注册其他任务

        # 记录注册的任务ID
        if task_ids:
            self._registered_tasks[bean_name] = task_ids

    def _get_scheduled_methods(self, bean_class: type) -> list[tuple]:
        """
        获取类中的定时任务方法信息

        Args:
            bean_class: Bean类

        Returns:
            定时任务信息列表:[(method_name, method, config), ...]
        """
        if bean_class in self._scheduled_cache:
            return self._scheduled_cache[bean_class]

        scheduled_methods = []

        # 扫描类中的所有方法
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)

                if (inspect.isfunction(attr) or inspect.ismethod(attr)) and is_scheduled(attr):
                    # 获取@Scheduled配置
                    config = get_config(attr)
                    if config:
                        scheduled_methods.append((attr_name, attr, config))

            except Exception:
                continue

        # 缓存结果
        self._scheduled_cache[bean_class] = scheduled_methods
        return scheduled_methods

    def _has_scheduled_annotations(self, bean_class: type) -> bool:
        """
        检查类是否有@Scheduled注解

        Args:
            bean_class: Bean类

        Returns:
            True表示有@Scheduled注解
        """
        # 检查方法级别的@Scheduled注解
        for attr_name in dir(bean_class):
            if attr_name.startswith("_"):
                continue

            try:
                attr = getattr(bean_class, attr_name)
                if (inspect.isfunction(attr) or inspect.ismethod(attr)) and is_scheduled(attr):
                    return True
            except Exception:
                continue

        return False

    def get_processed_beans_count(self) -> int:
        """
        获取已处理的Bean数量

        Returns:
            已处理的Bean数量
        """
        return len(self._processed_beans)

    def get_registered_tasks_count(self) -> int:
        """
        获取已注册的任务数量

        Returns:
            已注册的任务数量
        """
        return sum(len(task_ids) for task_ids in self._registered_tasks.values())

    def start_scheduler(self) -> None:
        """
        启动任务调度器
        """
        if self._task_scheduler and not self._task_scheduler.is_running():
            self._task_scheduler.start()
            logger.info("Task scheduler started")

    def stop_scheduler(self) -> None:
        """
        停止任务调度器
        """
        if self._task_scheduler and self._task_scheduler.is_running():
            self._task_scheduler.stop()
            logger.info("Task scheduler stopped")
