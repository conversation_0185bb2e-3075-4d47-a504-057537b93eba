"""
Load Monitor - 负载监控器

独立的负载监控 Starter 组件，提供系统和应用负载的实时监控功能。
重构自原 miniboot.web.backpressure.monitor，采用 @PostConstruct 生命周期管理。
"""

import asyncio
import threading
import time
from collections import deque
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
from typing import Any, Callable, Dict, List, Optional

import psutil
from loguru import logger

from miniboot.annotations import Component, PostConstruct, PreDestroy

from .properties import LoadMonitorProperties


@dataclass
class SystemMetrics:
    """系统资源指标"""

    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_available: int = 0
    disk_io_read: int = 0
    disk_io_write: int = 0
    network_io_sent: int = 0
    network_io_recv: int = 0
    timestamp: float = field(default_factory=time.time)


@dataclass
class ApplicationMetrics:
    """应用性能指标"""

    request_count: int = 0
    error_count: int = 0
    avg_response_time: float = 0.0
    throughput: float = 0.0
    error_rate: float = 0.0
    active_requests: int = 0
    timestamp: float = field(default_factory=time.time)


class LoadLevel(Enum):
    """负载等级"""

    NORMAL = "normal"       # 正常负载
    WARNING = "warning"     # 警告负载
    DANGER = "danger"       # 危险负载
    CRITICAL = "critical"   # 临界负载


@dataclass
class LoadStatus:
    """负载状态"""

    level: LoadLevel = LoadLevel.NORMAL
    score: float = 0.0  # 负载评分 (0.0-1.0)
    system_metrics: Optional[SystemMetrics] = None
    app_metrics: Optional[ApplicationMetrics] = None
    message: str = ""
    timestamp: float = field(default_factory=time.time)


@Component
class LoadMonitor:
    """负载监控器 - 独立 Starter 版本

    提供系统和应用负载的实时监控功能，支持：
    - 系统资源监控 (CPU、内存)
    - 应用性能监控 (响应时间、吞吐量)
    - 负载状态评估和回调通知
    - 配置驱动的监控策略
    """

    def __init__(self, properties: LoadMonitorProperties):
        """初始化负载监控器

        Args:
            properties: 负载监控配置属性
        """
        self.properties = properties

        # 验证配置
        self.properties.validate()

        # 监控状态
        self._is_running = False
        self._monitor_task: Optional[asyncio.Task] = None

        # 指标存储
        self._current_system_metrics: Optional[SystemMetrics] = None
        self._current_app_metrics: Optional[ApplicationMetrics] = None
        self._current_load_status: LoadStatus = LoadStatus()

        # 历史数据
        self._system_history: deque = deque(maxlen=self.properties.max_history_size)
        self._app_history: deque = deque(maxlen=self.properties.max_history_size)
        self._load_history: deque = deque(maxlen=self.properties.max_history_size)

        # 性能统计
        self._request_times: deque = deque(maxlen=1000)  # 保留最近1000个请求时间
        self._last_request_count = 0
        self._last_error_count = 0
        self._last_throughput_time = time.time()

        # 回调函数
        self._load_change_callbacks: List[Callable[[LoadStatus], None]] = []

        # 线程安全
        self._metrics_lock = Lock()

        logger.debug(f"LoadMonitor initialized with properties: {self.properties}")

    @PostConstruct
    async def start(self) -> None:
        """自动启动负载监控（框架调用）"""
        if not self.properties.enabled:
            logger.info("LoadMonitor disabled by configuration")
            return

        if self._is_running:
            logger.warning("LoadMonitor is already running")
            return

        logger.info(f"🚀 Starting LoadMonitor (interval: {self.properties.monitor_interval}s)")

        self._is_running = True

        # 启动监控循环（使用简化的非阻塞系统调用）
        self._monitor_task = asyncio.create_task(self._monitor_loop())

        logger.info("✅ LoadMonitor started successfully")

    @PreDestroy
    async def stop(self) -> None:
        """自动停止负载监控（框架调用）"""
        if not self._is_running:
            logger.debug("LoadMonitor is not running")
            return

        logger.info("🛑 Stopping LoadMonitor")

        self._is_running = False

        # 取消监控任务
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                logger.debug("LoadMonitor task cancelled successfully")

        logger.info("✅ LoadMonitor stopped successfully")

    async def _monitor_loop(self) -> None:
        """监控主循环"""
        logger.debug("LoadMonitor monitoring loop started")

        while self._is_running:
            try:
                # 收集系统指标
                await self._collect_system_metrics()

                # 更新应用指标
                await self._update_app_metrics()

                # 评估负载状态
                await self._evaluate_load_status()

                # 清理历史数据
                await self._cleanup_history()

                # 获取有效的监控间隔（支持动态调整）
                current_load = self._current_load_status.score
                interval = self.properties.get_effective_monitor_interval(current_load)

                # 等待下一次监控
                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                logger.debug("LoadMonitor loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(1.0)  # 错误时短暂等待

    async def _collect_system_metrics(self) -> None:
        """收集系统资源指标（简化版，模仿 PerformanceMonitor）"""
        try:
            # 使用与 PerformanceMonitor 相同的简单非阻塞调用
            cpu_percent = psutil.cpu_percent()  # 无参数，非阻塞
            memory = psutil.virtual_memory()    # 非阻塞

            # 移除可能阻塞的 I/O 统计调用

            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available=memory.available,
                disk_io_read=0,      # 简化：不收集磁盘I/O
                disk_io_write=0,     # 简化：不收集磁盘I/O
                network_io_sent=0,   # 简化：不收集网络I/O
                network_io_recv=0,   # 简化：不收集网络I/O
                timestamp=time.time(),
            )

            with self._metrics_lock:
                self._current_system_metrics = metrics
                self._system_history.append(metrics)

        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")

    async def _update_app_metrics(self) -> None:
        """更新应用性能指标"""
        try:
            current_time = time.time()

            with self._metrics_lock:
                # 计算响应时间统计
                if self._request_times:
                    response_times = list(self._request_times)
                    avg_response_time = sum(response_times) / len(response_times)
                else:
                    avg_response_time = 0.0

                # 计算吞吐量
                time_diff = current_time - self._last_throughput_time
                if time_diff > 0:
                    throughput = len(self._request_times) / time_diff
                else:
                    throughput = 0.0

                # 计算错误率
                total_requests = len(self._request_times)
                error_rate = (self._last_error_count / total_requests * 100) if total_requests > 0 else 0.0

                metrics = ApplicationMetrics(
                    request_count=total_requests,
                    error_count=self._last_error_count,
                    avg_response_time=avg_response_time,
                    throughput=throughput,
                    error_rate=error_rate,
                    active_requests=0,  # 需要外部更新
                    timestamp=current_time,
                )

                self._current_app_metrics = metrics
                self._app_history.append(metrics)

        except Exception as e:
            logger.error(f"Error updating app metrics: {e}")

    async def _evaluate_load_status(self) -> None:
        """评估负载状态"""
        try:
            if not self._current_system_metrics or not self._current_app_metrics:
                return

            # 计算负载评分
            score = self._calculate_load_score()

            # 确定负载等级
            level = self._determine_load_level(score)

            # 创建负载状态
            new_status = LoadStatus(
                level=level,
                score=score,
                system_metrics=self._current_system_metrics,
                app_metrics=self._current_app_metrics,
                message=self._generate_load_message(level, score),
                timestamp=time.time(),
            )

            # 检查是否需要触发回调
            old_level = self._current_load_status.level
            self._current_load_status = new_status
            self._load_history.append(new_status)

            # 触发负载变化回调
            if old_level != level and self.properties.enable_callbacks:
                await self._trigger_load_change_callbacks(new_status)

        except Exception as e:
            logger.error(f"Error evaluating load status: {e}")

    def _calculate_load_score(self) -> float:
        """计算负载评分 (0.0-1.0)"""
        if not self._current_system_metrics or not self._current_app_metrics:
            return 0.0

        # 系统负载权重
        cpu_score = min(self._current_system_metrics.cpu_percent / 100.0, 1.0)
        memory_score = min(self._current_system_metrics.memory_percent / 100.0, 1.0)

        # 应用负载权重
        response_time_score = min(self._current_app_metrics.avg_response_time / self.properties.response_time_threshold, 1.0)
        throughput_score = max(0.0, 1.0 - (self._current_app_metrics.throughput / self.properties.throughput_threshold))

        # 加权平均
        total_score = (
            cpu_score * 0.3 +
            memory_score * 0.3 +
            response_time_score * 0.2 +
            throughput_score * 0.2
        )

        return min(total_score, 1.0)

    def _determine_load_level(self, score: float) -> LoadLevel:
        """根据评分确定负载等级"""
        if score >= 0.9:
            return LoadLevel.CRITICAL
        elif score >= 0.7:
            return LoadLevel.DANGER
        elif score >= 0.5:
            return LoadLevel.WARNING
        else:
            return LoadLevel.NORMAL

    def _generate_load_message(self, level: LoadLevel, score: float) -> str:
        """生成负载状态消息"""
        if level == LoadLevel.CRITICAL:
            return f"系统负载临界 (评分: {score:.2f})"
        elif level == LoadLevel.DANGER:
            return f"系统负载过高 (评分: {score:.2f})"
        elif level == LoadLevel.WARNING:
            return f"系统负载较高 (评分: {score:.2f})"
        else:
            return f"系统负载正常 (评分: {score:.2f})"

    async def _trigger_load_change_callbacks(self, status: LoadStatus) -> None:
        """触发负载变化回调"""
        if not self._load_change_callbacks:
            return

        logger.debug(f"Triggering {len(self._load_change_callbacks)} load change callbacks")

        for callback in self._load_change_callbacks:
            try:
                # 使用超时执行回调
                await asyncio.wait_for(
                    asyncio.create_task(self._execute_callback(callback, status)),
                    timeout=self.properties.callback_timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"Load change callback timed out: {callback}")
            except Exception as e:
                logger.error(f"Error in load change callback: {e}")

    async def _execute_callback(self, callback: Callable, status: LoadStatus) -> None:
        """执行回调函数"""
        if asyncio.iscoroutinefunction(callback):
            await callback(status)
        else:
            callback(status)

    async def _cleanup_history(self) -> None:
        """清理历史数据"""
        current_time = time.time()

        # 定期清理（根据配置间隔）
        if hasattr(self, '_last_cleanup_time'):
            if current_time - self._last_cleanup_time < self.properties.cleanup_interval:
                return

        self._last_cleanup_time = current_time

        # 清理过期的请求时间记录
        cutoff_time = current_time - 300  # 保留最近5分钟的数据
        while self._request_times and self._request_times[0] < cutoff_time:
            self._request_times.popleft()

        logger.debug("Historical data cleanup completed")

    # ==================== 公共接口 ====================

    def add_load_change_callback(self, callback: Callable[[LoadStatus], None]) -> None:
        """添加负载变化回调函数

        Args:
            callback: 回调函数，接收 LoadStatus 参数
        """
        if callback not in self._load_change_callbacks:
            self._load_change_callbacks.append(callback)
            logger.debug(f"Added load change callback: {callback}")

    def remove_load_change_callback(self, callback: Callable[[LoadStatus], None]) -> None:
        """移除负载变化回调函数

        Args:
            callback: 要移除的回调函数
        """
        if callback in self._load_change_callbacks:
            self._load_change_callbacks.remove(callback)
            logger.debug(f"Removed load change callback: {callback}")

    def record_request(self, response_time: float, is_error: bool = False) -> None:
        """记录请求信息

        Args:
            response_time: 响应时间(毫秒)
            is_error: 是否为错误请求
        """
        current_time = time.time()

        with self._metrics_lock:
            self._request_times.append(current_time)

            if is_error:
                self._last_error_count += 1

    def get_current_load_status(self) -> LoadStatus:
        """获取当前负载状态

        Returns:
            当前负载状态
        """
        return self._current_load_status

    def get_system_metrics(self) -> Optional[SystemMetrics]:
        """获取当前系统指标

        Returns:
            当前系统指标，如果未收集则返回 None
        """
        return self._current_system_metrics

    def get_app_metrics(self) -> Optional[ApplicationMetrics]:
        """获取当前应用指标

        Returns:
            当前应用指标，如果未收集则返回 None
        """
        return self._current_app_metrics

    def is_running(self) -> bool:
        """检查监控器是否正在运行

        Returns:
            是否正在运行
        """
        return self._is_running

    def get_load_history(self, limit: int = 100) -> List[LoadStatus]:
        """获取负载历史记录

        Args:
            limit: 返回记录数量限制

        Returns:
            负载历史记录列表
        """
        with self._metrics_lock:
            history = list(self._load_history)
            return history[-limit:] if limit > 0 else history
