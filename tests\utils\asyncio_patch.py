#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: asyncio 警告抑制工具

用于在测试中抑制 asyncio 相关的警告信息，提供更清洁的测试输出。
"""

import warnings
from functools import wraps
from typing import Any, Callable, TypeVar

F = TypeVar("F", bound=Callable[..., Any])


def suppress_asyncio_warnings(func: F) -> F:
    """
    装饰器：抑制 asyncio 相关的警告

    Args:
        func: 要装饰的函数

    Returns:
        装饰后的函数
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        with warnings.catch_warnings():
            # 抑制 asyncio 相关警告
            warnings.filterwarnings("ignore", category=DeprecationWarning, module="asyncio")
            warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")
            warnings.filterwarnings("ignore", message=".*coroutine.*was never awaited.*")
            warnings.filterwarnings("ignore", message=".*Task was destroyed but it is pending.*")
            warnings.filterwarnings("ignore", message=".*Event loop is closed.*")

            return func(*args, **kwargs)

    return wrapper


def suppress_all_asyncio_warnings():
    """
    全局抑制 asyncio 相关警告
    """
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="asyncio")
    warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")
    warnings.filterwarnings("ignore", message=".*coroutine.*was never awaited.*")
    warnings.filterwarnings("ignore", message=".*Task was destroyed but it is pending.*")
    warnings.filterwarnings("ignore", message=".*Event loop is closed.*")


def reset_asyncio_warnings():
    """
    重置 asyncio 警告过滤器
    """
    warnings.resetwarnings()
