#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Actuator Web 集成条件

实现 Actuator 与 Web 框架集成的条件检查功能，包括：
- FastAPI 可用性检查
- Web 模块启用状态检查
- 综合集成条件判断

设计原则：
- 条件化集成：只有在满足所有条件时才启用 Web 集成
- 优雅降级：当 Web 框架不可用时，Actuator 仍可独立运行
- 配置驱动：通过配置属性控制集成行为
- 环境感知：自动检测运行环境和可用组件

使用示例：
    # 基本使用
    conditions = ActuatorWebConditions()
    if conditions.should_integrate():
        print("Web 集成已启用")

    # 详细检查
    info = conditions.info()
    print(f"FastAPI 可用: {info['fastapi_available']}")
    print(f"Web 模块启用: {info['web_module_enabled']}")
"""

import importlib
import sys
from typing import Any, Dict, Optional

from loguru import logger


class ActuatorWebConditions:
    """Actuator Web 集成条件

    简化的 Actuator Web 集成条件检查，专注于核心条件逻辑：
    1. 检查 FastAPI 框架可用性
    2. 检查 Web 模块启用状态（通过环境接口）
    3. 综合判断是否应该启用集成
    """

    def __init__(self):
        """初始化 Actuator Web 集成条件"""
        self._environment = None
        logger.debug("ActuatorWebConditions initialized")

    def _get_environment(self):
        """获取环境实例（延迟初始化）"""
        if self._environment is None:
            from miniboot.context.environment import Environment
            self._environment = Environment()
        return self._environment

    def fastapi_available(self) -> bool:
        """检查 FastAPI 是否可用

        Returns:
            bool: FastAPI 是否可用
        """
        try:
            from fastapi import FastAPI, Request, Response
            from fastapi.routing import APIRouter
            return True
        except ImportError:
            return False

    def fastapi_version(self) -> Optional[str]:
        """获取 FastAPI 版本信息

        Returns:
            Optional[str]: FastAPI 版本号，如果不可用则返回 None
        """
        try:
            import fastapi
            return fastapi.__version__
        except ImportError:
            return None

    def web_enabled(self) -> bool:
        """检查 Web 模块是否启用

        Returns:
            bool: Web 模块是否启用
        """
        try:
            environment = self._get_environment()
            return environment.get_property("miniboot.starters.web.enabled", True)
        except Exception:
            return True  # 默认启用



    def should_integrate(self) -> bool:
        """综合判断是否应该启用 Web 集成

        简化的集成条件检查：
        1. FastAPI 必须可用
        2. Web 模块必须启用

        Returns:
            bool: 是否应该启用 Web 集成
        """
        return self.fastapi_available() and self.web_enabled()

    def info(self) -> Dict[str, Any]:
        """获取集成信息（用于调试和监控）

        Returns:
            Dict[str, Any]: 集成信息
        """
        fastapi_available = self.fastapi_available()
        web_enabled = self.web_enabled()
        should_integrate = fastapi_available and web_enabled

        info = {
            'fastapi_available': fastapi_available,
            'fastapi_version': self.fastapi_version() if fastapi_available else None,
            'web_module_enabled': web_enabled,
            'should_integrate': should_integrate,
            'timestamp': __import__('time').time()
        }

        # 添加集成原因
        if should_integrate:
            info['reason'] = "All conditions met for Web integration"
        else:
            reasons = []
            if not fastapi_available:
                reasons.append("FastAPI not available")
            if not web_enabled:
                reasons.append("Web module disabled")
            info['reason'] = f"Integration disabled: {', '.join(reasons)}"

        logger.debug(f"Web integration info: {should_integrate}, reason: {info['reason']}")
        return info



    def summary(self) -> Dict[str, Any]:
        """获取集成状态摘要（兼容性方法）

        Returns:
            Dict[str, Any]: 集成状态摘要信息
        """
        # 直接返回集成信息，保持向后兼容
        return self.info()
