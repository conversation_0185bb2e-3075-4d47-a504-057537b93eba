#!/usr/bin/env python
"""
Web框架性能测试用例

测试背压控制和智能调度的性能提升效果。
"""

import asyncio
import statistics
import time
import unittest
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, patch

from miniboot.web.application import WebApplication
from miniboot.web.middleware import MiddlewareRegistry, ResponseMiddleware
from miniboot.web.properties import (AsyncOptimizationConfig,
                                     BackpressureConfig, WebProperties)


class PerformanceMetrics:
    """性能指标收集器"""

    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.response_times = []
        self.error_count = 0
        self.success_count = 0

    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.response_times = []
        self.error_count = 0
        self.success_count = 0

    def record_response_time(self, response_time: float):
        """记录响应时间"""
        self.response_times.append(response_time)

    def record_success(self):
        """记录成功"""
        self.success_count += 1

    def record_error(self):
        """记录错误"""
        self.error_count += 1

    def stop_monitoring(self):
        """停止监控"""
        self.end_time = time.time()

    def get_summary(self):
        """获取性能摘要"""
        total_time = self.end_time - self.start_time if self.end_time else 0
        total_requests = self.success_count + self.error_count

        return {
            "total_time": total_time,
            "total_requests": total_requests,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / max(1, total_requests) * 100,
            "throughput": total_requests / max(0.001, total_time),
            "avg_response_time": statistics.mean(self.response_times) if self.response_times else 0,
            "max_response_time": max(self.response_times) if self.response_times else 0,
            "min_response_time": min(self.response_times) if self.response_times else 0,
        }


class TestWebApplicationPerformance(unittest.TestCase):
    """Web应用性能测试"""

    def test_application_initialization_performance(self):
        """测试应用初始化性能"""
        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 测试多次初始化
        for _ in range(10):
            start_time = time.time()

            properties = WebProperties()
            app = WebApplication(properties=properties)

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)
            metrics.record_success()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证性能指标
        self.assertLess(summary["avg_response_time"], 0.1)  # 平均初始化时间小于100ms
        self.assertEqual(summary["success_rate"], 100.0)

    def test_status_monitoring_performance(self):
        """测试状态监控性能"""
        properties = WebProperties()
        app = WebApplication(properties=properties)

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 大量状态查询
        for _ in range(1000):
            start_time = time.time()

            status = app.get_status()

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)

            if isinstance(status, dict) and len(status) > 0:
                metrics.record_success()
            else:
                metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证性能指标
        self.assertLess(summary["avg_response_time"], 0.001)  # 平均响应时间小于1ms
        self.assertGreater(summary["throughput"], 1000)  # 吞吐量大于1000 req/s
        self.assertEqual(summary["success_rate"], 100.0)

    def test_component_creation_performance(self):
        """测试组件创建性能"""
        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        for _ in range(100):
            start_time = time.time()

            # 创建各种组件
            manager = MiddlewareManager()
            middleware = ResponseMiddleware()
            manager.register(middleware)

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)
            metrics.record_success()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证组件创建性能
        self.assertLess(summary["avg_response_time"], 0.01)  # 平均创建时间小于10ms
        self.assertEqual(summary["success_rate"], 100.0)


class TestMiddlewarePerformance(unittest.TestCase):
    """中间件性能测试"""

    def test_middleware_registration_performance(self):
        """测试中间件注册性能"""
        manager = MiddlewareManager()

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 注册大量中间件
        for i in range(100):
            start_time = time.time()

            middleware = ResponseMiddleware(name=f"middleware_{i}")
            success = manager.register(middleware)

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)

            if success:
                metrics.record_success()
            else:
                metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证注册性能
        self.assertLess(summary["avg_response_time"], 0.001)  # 平均注册时间小于1ms
        self.assertGreater(summary["success_rate"], 95.0)  # 成功率大于95%

    def test_middleware_retrieval_performance(self):
        """测试中间件检索性能"""
        manager = MiddlewareManager()

        # 预先注册一些中间件
        for i in range(10):
            middleware = ResponseMiddleware(name=f"middleware_{i}")
            manager.register(middleware)

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 大量检索操作
        for _ in range(1000):
            start_time = time.time()

            middleware = manager.get_middleware("middleware_5")

            end_time = time.time()
            metrics.record_response_time(end_time - start_time)

            if middleware is not None:
                metrics.record_success()
            else:
                metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证检索性能
        self.assertLess(summary["avg_response_time"], 0.0001)  # 平均检索时间小于0.1ms
        self.assertEqual(summary["success_rate"], 100.0)


class TestConcurrentPerformance(unittest.TestCase):
    """并发性能测试"""

    def test_concurrent_application_creation(self):
        """测试并发应用创建性能"""

        def create_application():
            start_time = time.time()
            properties = WebProperties()
            app = WebApplication(properties=properties)
            status = app.get_status()
            end_time = time.time()
            return end_time - start_time, len(status)

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 并发创建应用
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(create_application) for _ in range(20)]

            for future in futures:
                try:
                    response_time, status_count = future.result(timeout=5.0)
                    metrics.record_response_time(response_time)
                    if status_count > 0:
                        metrics.record_success()
                    else:
                        metrics.record_error()
                except Exception:
                    metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证并发性能
        self.assertGreater(summary["success_rate"], 95.0)
        self.assertLess(summary["avg_response_time"], 1.0)

    def test_concurrent_middleware_operations(self):
        """测试并发中间件操作性能"""
        registry = MiddlewareRegistry()

        def middleware_operations():
            start_time = time.time()

            # 注册中间件
            middleware = ResponseMiddleware(name=f"middleware_{time.time()}")
            registry.register(middleware)

            # 获取中间件列表
            middlewares = registry.list()

            end_time = time.time()
            return end_time - start_time, len(middlewares)

        metrics = PerformanceMetrics()
        metrics.start_monitoring()

        # 并发中间件操作
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(middleware_operations) for _ in range(50)]

            for future in futures:
                try:
                    response_time, middleware_count = future.result(timeout=5.0)
                    metrics.record_response_time(response_time)
                    if middleware_count >= 0:
                        metrics.record_success()
                    else:
                        metrics.record_error()
                except Exception:
                    metrics.record_error()

        metrics.stop_monitoring()
        summary = metrics.get_summary()

        # 验证并发中间件操作性能
        self.assertGreater(summary["success_rate"], 90.0)
        self.assertLess(summary["avg_response_time"], 0.1)


class TestMemoryPerformance(unittest.TestCase):
    """内存性能测试"""

    def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        properties = WebProperties()
        app = WebApplication(properties=properties)

        # 创建大量对象模拟负载
        objects = []
        for i in range(1000):
            # 创建应用状态快照
            status = app.get_status()
            objects.append(status)

            # 每100次检查对象数量
            if i % 100 == 0:
                self.assertLessEqual(len(objects), i + 1)

        # 清理对象
        objects.clear()

        # 验证清理后状态
        self.assertEqual(len(objects), 0)

    def test_component_memory_efficiency(self):
        """测试组件内存效率"""
        # 创建多个中间件管理器
        managers = []
        for i in range(100):
            manager = MiddlewareManager()
            middleware = ResponseMiddleware(name=f"middleware_{i}")
            manager.register(middleware)
            managers.append(manager)

        # 验证管理器数量
        self.assertEqual(len(managers), 100)

        # 验证每个管理器都有中间件
        for manager in managers:
            middlewares = manager.list()
            self.assertGreaterEqual(len(middlewares), 1)

        # 清理
        managers.clear()
        self.assertEqual(len(managers), 0)


def run_tests():
    """运行所有性能测试"""
    test_suite = unittest.TestSuite()

    test_classes = [TestWebApplicationPerformance, TestMiddlewarePerformance, TestConcurrentPerformance, TestMemoryPerformance]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    return result


if __name__ == "__main__":
    print("🚀 运行Web框架性能测试...")
    result = run_tests()

    if result.wasSuccessful():
        print("✅ 性能测试全部通过！")
    else:
        print(f"❌ 性能测试失败: {len(result.failures)} 失败, {len(result.errors)} 错误")
