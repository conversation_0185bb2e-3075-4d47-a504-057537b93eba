#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mini-Boot框架基础异常类和四层分类体系

新的异常层次结构设计：
- MiniBootError: 框架基础异常
- SystemError: 系统级异常 (CRITICAL)
- ApplicationError: 应用级异常 (HIGH)
- BusinessError: 业务级异常 (MEDIUM)
- ValidationError: 验证级异常 (LOW)

每个异常类都包含重试配置属性：
- retryable: 是否可重试
- max_attempts: 最大重试次数
- base_delay: 基础延迟时间(秒)
- strategy: 重试策略("fixed", "exponential", "linear")
"""

import traceback
from datetime import datetime
from typing import Any, Dict, Optional


class MiniBootError(Exception):
    """Mini-Boot框架基础异常类

    所有框架异常的基类，提供统一的异常处理接口和标准化的错误信息格式。

    特性:
    1. 统一的错误信息格式
    2. 错误代码自动生成
    3. 详细的上下文信息
    4. 异常链支持
    5. 时间戳记录
    6. 重试配置属性
    """

    # 默认重试配置
    retryable = False
    max_attempts = 1
    base_delay = 1.0
    strategy = "exponential"

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        context: Optional[Dict[str, Any]] = None,
    ):
        """初始化异常

        Args:
            message: 异常消息
            error_code: 错误代码，如果未提供则自动生成
            details: 错误详细信息
            cause: 引起异常的原因
            context: 错误发生时的上下文信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self._generate_error_code()
        self.details = details or {}
        self.cause = cause
        self.context = context or {}
        self.timestamp = datetime.now()

        # 设置异常链
        if cause:
            self.__cause__ = cause

        # 收集调试信息
        self._collect_debug_info()

    def _generate_error_code(self) -> str:
        """自动生成错误代码

        基于异常类的层次结构生成错误代码：
        格式：MB-[层级]-[类名简写]-[哈希]

        Returns:
            自动生成的错误代码
        """
        class_name = self.__class__.__name__

        # 确定层级代码
        if isinstance(self, SystemError):
            level = "1"
        elif isinstance(self, ApplicationError):
            level = "2"
        elif isinstance(self, BusinessError):
            level = "3"
        elif isinstance(self, ValidationError):
            level = "4"
        else:
            level = "0"  # 基础异常

        # 生成类名简写（取前3个字符）
        if class_name.endswith("Error"):
            short_name = class_name[:-5][:3].upper()
        else:
            short_name = class_name[:3].upper()

        # 生成简单的哈希值（基于类名）
        hash_value = abs(hash(class_name)) % 1000

        return f"MB-{level}-{short_name}-{hash_value:03d}"

    def _collect_debug_info(self):
        """收集调试信息"""
        try:
            # 获取调用栈信息
            stack = traceback.extract_stack()[:-1]  # 排除当前方法
            if stack:
                frame = stack[-1]
                self.context.update({
                    "file": frame.filename,
                    "line": frame.lineno,
                    "function": frame.name,
                })
        except Exception:
            # 如果收集调试信息失败，不影响异常的正常抛出
            pass

    def get_user_message(self) -> str:
        """获取用户友好的错误消息

        Returns:
            用户友好的错误消息
        """
        return f"[{self.error_code}] {self.message}"

    def get_debug_message(self) -> str:
        """获取详细的调试消息

        Returns:
            包含详细信息的调试消息
        """
        parts = [f"[{self.error_code}] {self.message}"]

        if self.details:
            parts.append(f"Details: {self.details}")

        if self.context:
            parts.append(f"Context: {self.context}")

        if self.cause:
            parts.append(f"Caused by: {type(self.cause).__name__}: {self.cause}")

        return " | ".join(parts)

    def to_dict(self) -> Dict[str, Any]:
        """将异常转换为字典格式

        Returns:
            异常的字典表示
        """
        return {
            "error_code": self.error_code,
            "message": self.message,
            "type": self.__class__.__name__,
            "details": self.details,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "retryable": self.retryable,
            "max_attempts": self.max_attempts,
        }

    def __str__(self) -> str:
        """返回异常的字符串表示"""
        return self.get_user_message()

    def __repr__(self) -> str:
        """返回异常的详细表示"""
        return f"{self.__class__.__name__}(message='{self.message}', error_code='{self.error_code}')"


class SystemError(MiniBootError):
    """系统级异常 - 影响系统运行的严重错误

    特征：
    - 严重程度：CRITICAL
    - 通常不可重试
    - 需要立即关注和处理
    - 可能导致系统停止运行

    示例：资源耗尽、安全违规、系统崩溃等
    """
    retryable = False
    max_attempts = 1


class ApplicationError(MiniBootError):
    """应用级异常 - 影响应用功能的错误

    特征：
    - 严重程度：HIGH
    - 部分可重试
    - 影响应用的正常功能
    - 通常需要配置或环境调整

    示例：配置错误、初始化失败、状态错误等
    """
    retryable = True
    max_attempts = 2
    base_delay = 2.0


class BusinessError(MiniBootError):
    """业务级异常 - 业务逻辑相关错误

    特征：
    - 严重程度：MEDIUM
    - 通常可重试
    - 与业务逻辑相关
    - 可能是临时性问题

    示例：Bean创建失败、事件发布失败、任务执行失败等
    """
    retryable = True
    max_attempts = 3
    base_delay = 1.0
    strategy = "exponential"


class ValidationError(MiniBootError):
    """验证级异常 - 数据验证相关错误

    特征：
    - 严重程度：LOW
    - 通常不可重试
    - 数据或参数验证失败
    - 需要修正输入数据

    示例：参数验证失败、类型不匹配、数据格式错误等
    """
    retryable = False
    max_attempts = 1


# 为了向后兼容，保留MiniBootException别名
MiniBootException = MiniBootError
