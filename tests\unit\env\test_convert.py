#!/usr/bin/env python
"""
* @author: cz
* @description: 环境模块类型转换功能测试
"""

import unittest

from miniboot.env.convert import ConversionError, Converter, DefaultConversionService
from miniboot.env.convert.strings import StringToBooleanConverter, StringToFloatConverter, StringToIntConverter, StringToListConverter


class StringConvertersTestCase(unittest.TestCase):
    """字符串转换器测试"""

    def setUp(self):
        self.bool_converter = StringToBooleanConverter()
        self.int_converter = StringToIntConverter()
        self.float_converter = StringToFloatConverter()
        self.list_converter = StringToListConverter()

    def test_string_to_boolean_converter(self):
        """测试字符串到布尔值转换"""
        # 测试 True 值
        for value in ["true", "True", "TRUE", "yes", "YES", "1", "on", "enabled"]:
            self.assertTrue(self.bool_converter.convert(value, bool))

        # 测试 False 值
        for value in ["false", "False", "FALSE", "no", "NO", "0", "off", "disabled"]:
            self.assertFalse(self.bool_converter.convert(value, bool))

        # 测试无效值
        with self.assertRaises(ConversionError):
            self.bool_converter.convert("invalid", bool)

    def test_string_to_int_converter(self):
        """测试字符串到整数转换"""
        # 测试十进制
        self.assertEqual(123, self.int_converter.convert("123", int))
        self.assertEqual(-456, self.int_converter.convert("-456", int))

        # 测试十六进制
        self.assertEqual(16, self.int_converter.convert("0x10", int))
        self.assertEqual(255, self.int_converter.convert("0xFF", int))

        # 测试二进制
        self.assertEqual(5, self.int_converter.convert("0b101", int))

        # 测试八进制
        self.assertEqual(8, self.int_converter.convert("0o10", int))

        # 测试无效值
        with self.assertRaises(ConversionError):
            self.int_converter.convert("invalid", int)

    def test_string_to_float_converter(self):
        """测试字符串到浮点数转换"""
        self.assertEqual(123.45, self.float_converter.convert("123.45", float))
        self.assertEqual(-67.89, self.float_converter.convert("-67.89", float))
        self.assertEqual(1.23e-4, self.float_converter.convert("1.23e-4", float))

        # 测试无效值
        with self.assertRaises(ConversionError):
            self.float_converter.convert("invalid", float)

    def test_string_to_list_converter(self):
        """测试字符串到列表转换"""
        # 测试正常分割
        self.assertEqual(["a", "b", "c"], self.list_converter.convert("a,b,c", list))
        self.assertEqual(["1", "2", "3"], self.list_converter.convert("1, 2, 3", list))

        # 测试空字符串
        self.assertEqual([], self.list_converter.convert("", list))
        self.assertEqual([], self.list_converter.convert("   ", list))

        # 测试单个元素
        self.assertEqual(["single"], self.list_converter.convert("single", list))


class DefaultConversionServiceTestCase(unittest.TestCase):
    """默认转换服务测试"""

    def setUp(self):
        self.service = DefaultConversionService()

    def test_can_convert(self):
        """测试转换能力检查"""
        # 测试相同类型
        self.assertTrue(self.service.can_convert(str, str))

        # 测试支持的转换
        self.assertTrue(self.service.can_convert(str, bool))
        self.assertTrue(self.service.can_convert(str, int))
        self.assertTrue(self.service.can_convert(str, float))
        self.assertTrue(self.service.can_convert(str, list))

        # 测试不支持的转换(使用一个确实无法转换的类型)
        class CustomType:
            pass

        self.assertFalse(self.service.can_convert(CustomType, list))

    def test_convert_same_type(self):
        """测试相同类型转换"""
        value = "test"
        result = self.service.convert(value, str)
        self.assertEqual(value, result)

    def test_convert_none_value(self):
        """测试 None 值转换"""
        result = self.service.convert(None, str)
        self.assertIsNone(result)

    def test_convert_string_to_types(self):
        """测试字符串到各种类型的转换"""
        # 字符串到布尔值
        self.assertTrue(self.service.convert("true", bool))
        self.assertFalse(self.service.convert("false", bool))

        # 字符串到整数
        self.assertEqual(123, self.service.convert("123", int))

        # 字符串到浮点数
        self.assertEqual(123.45, self.service.convert("123.45", float))

        # 字符串到列表
        self.assertEqual(["a", "b"], self.service.convert("a,b", list))

    def test_convert_to_string(self):
        """测试转换到字符串"""
        self.assertEqual("123", self.service.convert(123, str))
        self.assertEqual("True", self.service.convert(True, str))
        self.assertEqual("[1, 2, 3]", self.service.convert([1, 2, 3], str))

    def test_convert_unsupported_type(self):
        """测试不支持的类型转换"""

        class CustomType:
            pass

        with self.assertRaises(ConversionError):
            self.service.convert(CustomType(), list)

    def test_add_custom_converter(self):
        """测试添加自定义转换器"""

        class CustomConverter(Converter[str]):
            def can_convert(self, source_type, target_type):
                return source_type is dict and target_type is str

            def convert(self, source, _target_type):
                return f"dict:{len(source)}"

        # 添加自定义转换器
        custom_converter = CustomConverter()
        self.service.add_converter(custom_converter)

        # 测试自定义转换
        result = self.service.convert({"a": 1, "b": 2}, str)
        self.assertEqual("dict:2", result)


if __name__ == "__main__":
    unittest.main()
