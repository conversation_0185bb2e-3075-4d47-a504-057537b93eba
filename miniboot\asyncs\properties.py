#!/usr/bin/env python
"""
异步配置属性类和配置管理 - Spring Boot风格

包含异步配置属性类和从环境配置创建配置的功能.
重构后采用模块化配置分组，提升可维护性和扩展性。
"""

from dataclasses import dataclass, field
from typing import Any, Dict, Literal, Optional

# ============================================================================
# 配置分组类 - 按功能模块化
# ============================================================================

@dataclass
class ThreadPoolConfig:
    """线程池基础配置"""
    core_size: int = 2  # 减少默认核心线程数以提高启动速度
    max_size: int = 4  # 减少默认最大线程数
    queue_capacity: int = 100
    keep_alive: int = 60
    thread_name_prefix: str = "miniboot-async-"
    allow_core_thread_timeout: bool = False
    rejection_policy: Literal["abort", "caller_runs", "discard", "discard_oldest"] = "caller_runs"


@dataclass
class DynamicAdjustmentConfig:
    """动态调整配置"""
    enabled: bool = True
    adjustment_interval: float = 5.0  # 调整间隔（秒）
    load_threshold_high: float = 0.8  # 高负载阈值
    load_threshold_low: float = 0.3   # 低负载阈值


@dataclass
class LoadBalancingConfig:
    """负载均衡配置"""
    enabled: bool = True
    strategy: Literal["round_robin", "least_connections", "adaptive", "priority_based"] = "adaptive"
    enable_priority_queue: bool = True
    priority_levels: int = 5          # 优先级级别数


@dataclass
class IntelligentSchedulingConfig:
    """智能调度配置"""
    enable_strategy_caching: bool = True
    strategy_cache_size: int = 1000
    enable_predictive_scheduling: bool = True
    enable_batch_processing: bool = True


@dataclass
class OptimizationConfig:
    """优化功能配置（整合自 AsyncOptimizationConfig）"""
    enable_optimization: bool = True
    intelligent_scheduling: bool = True
    performance_profiling: bool = True
    strategy_selection: bool = True


@dataclass
class MonitoringConfig:
    """性能监控配置"""
    enabled: bool = True
    metrics_collection_interval: float = 5.0
    performance_history_size: int = 10000
    enable_detailed_history: bool = False


@dataclass
class AdaptiveLearningConfig:
    """自适应学习配置"""
    enabled: bool = True
    learning_window_size: int = 1000
    learning_rate: float = 0.01
    model_update_interval: int = 100


@dataclass
class PerformanceThresholdsConfig:
    """性能阈值配置"""
    async_benefit_threshold: float = 0.1
    cpu_bound_threshold: float = 0.05
    io_bound_threshold: float = 0.01
    lightweight_threshold: float = 0.001
    thread_pool_threshold: float = 0.1
    process_pool_threshold: float = 1.0


@dataclass
class ThreadPoolAdvancedConfig:
    """线程池高级配置"""
    max_workers: int = 20
    queue_size: int = 1000
    keep_alive_time: int = 60


# ============================================================================
# 重构后的主配置类 - 使用组合模式
# ============================================================================

@dataclass
class AsyncExecutorProperties:
    """重构后的异步执行器配置属性 - 使用组合模式"""

    # 配置分组
    thread_pool: ThreadPoolConfig = field(default_factory=ThreadPoolConfig)
    dynamic_adjustment: DynamicAdjustmentConfig = field(default_factory=DynamicAdjustmentConfig)
    load_balancing: LoadBalancingConfig = field(default_factory=LoadBalancingConfig)
    intelligent_scheduling: IntelligentSchedulingConfig = field(default_factory=IntelligentSchedulingConfig)
    optimization: OptimizationConfig = field(default_factory=OptimizationConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    adaptive_learning: AdaptiveLearningConfig = field(default_factory=AdaptiveLearningConfig)
    performance_thresholds: PerformanceThresholdsConfig = field(default_factory=PerformanceThresholdsConfig)
    thread_pool_advanced: ThreadPoolAdvancedConfig = field(default_factory=ThreadPoolAdvancedConfig)

    def update_from_optimization_config(self, async_optimization_config: Any) -> None:
        """从 AsyncOptimizationConfig 更新配置（重构后使用组合模式）

        Args:
            async_optimization_config: AsyncOptimizationConfig 实例
        """
        if not async_optimization_config:
            return

        # 更新优化功能配置
        self.optimization.enable_optimization = getattr(async_optimization_config, 'enabled', self.optimization.enable_optimization)
        self.optimization.intelligent_scheduling = getattr(async_optimization_config, 'intelligent_scheduling', self.optimization.intelligent_scheduling)
        self.optimization.performance_profiling = getattr(async_optimization_config, 'performance_profiling', self.optimization.performance_profiling)
        self.optimization.strategy_selection = getattr(async_optimization_config, 'classification_enabled', self.optimization.strategy_selection)

        # 更新性能监控配置
        self.monitoring.enabled = getattr(async_optimization_config, 'monitoring_enabled', self.monitoring.enabled)
        self.monitoring.metrics_collection_interval = getattr(async_optimization_config, 'metrics_collection_interval', self.monitoring.metrics_collection_interval)
        self.monitoring.performance_history_size = getattr(async_optimization_config, 'performance_history_size', self.monitoring.performance_history_size)

        # 更新自适应学习配置
        self.adaptive_learning.enabled = getattr(async_optimization_config, 'adaptive_learning', self.adaptive_learning.enabled)
        self.adaptive_learning.learning_window_size = getattr(async_optimization_config, 'learning_window_size', self.adaptive_learning.learning_window_size)
        self.adaptive_learning.learning_rate = getattr(async_optimization_config, 'learning_rate', self.adaptive_learning.learning_rate)
        self.adaptive_learning.model_update_interval = getattr(async_optimization_config, 'model_update_interval', self.adaptive_learning.model_update_interval)

        # 更新性能阈值
        if hasattr(async_optimization_config, 'performance_thresholds'):
            for key, value in async_optimization_config.performance_thresholds.items():
                if hasattr(self.performance_thresholds, key):
                    setattr(self.performance_thresholds, key, value)

        # 更新线程池高级配置
        if hasattr(async_optimization_config, 'thread_pool_config'):
            for key, value in async_optimization_config.thread_pool_config.items():
                if hasattr(self.thread_pool_advanced, key):
                    setattr(self.thread_pool_advanced, key, value)

    def validate(self) -> None:
        """验证所有配置参数（重构后使用组合模式）"""
        # 验证线程池配置
        self._validate_thread_pool_config()

        # 验证动态调整配置
        self._validate_dynamic_adjustment_config()

        # 验证负载均衡配置
        self._validate_load_balancing_config()

        # 验证智能调度配置
        self._validate_intelligent_scheduling_config()

        # 验证监控配置
        self._validate_monitoring_config()

        # 验证自适应学习配置
        self._validate_adaptive_learning_config()

    def _validate_thread_pool_config(self) -> None:
        """验证线程池配置"""
        if self.thread_pool.core_size <= 0:
            raise ValueError("thread_pool.core_size must be positive")

        if self.thread_pool.max_size < self.thread_pool.core_size:
            raise ValueError("thread_pool.max_size must be >= core_size")

        if self.thread_pool.queue_capacity < 0:
            raise ValueError("thread_pool.queue_capacity must be non-negative")

        if self.thread_pool.keep_alive < 0:
            raise ValueError("thread_pool.keep_alive must be non-negative")

    def _validate_dynamic_adjustment_config(self) -> None:
        """验证动态调整配置"""
        if self.dynamic_adjustment.adjustment_interval <= 0:
            raise ValueError("dynamic_adjustment.adjustment_interval must be positive")

        if not 0 < self.dynamic_adjustment.load_threshold_low < self.dynamic_adjustment.load_threshold_high < 1:
            raise ValueError("load thresholds must be: 0 < low < high < 1")

    def _validate_load_balancing_config(self) -> None:
        """验证负载均衡配置"""
        if self.load_balancing.priority_levels <= 0:
            raise ValueError("load_balancing.priority_levels must be positive")

    def _validate_intelligent_scheduling_config(self) -> None:
        """验证智能调度配置"""
        if self.intelligent_scheduling.strategy_cache_size <= 0:
            raise ValueError("intelligent_scheduling.strategy_cache_size must be positive")

    def _validate_monitoring_config(self) -> None:
        """验证监控配置"""
        if self.monitoring.metrics_collection_interval <= 0:
            raise ValueError("monitoring.metrics_collection_interval must be positive")

        if self.monitoring.performance_history_size <= 0:
            raise ValueError("monitoring.performance_history_size must be positive")

    def _validate_adaptive_learning_config(self) -> None:
        """验证自适应学习配置"""
        if self.adaptive_learning.learning_window_size <= 0:
            raise ValueError("adaptive_learning.learning_window_size must be positive")

        if not 0 < self.adaptive_learning.learning_rate <= 1:
            raise ValueError("adaptive_learning.learning_rate must be between 0 and 1")

        if self.adaptive_learning.model_update_interval <= 0:
            raise ValueError("adaptive_learning.model_update_interval must be positive")

    def to_dict(self) -> dict:
        """转换为字典（重构后使用组合模式）"""
        from dataclasses import asdict

        return {
            "thread_pool": asdict(self.thread_pool),
            "dynamic_adjustment": asdict(self.dynamic_adjustment),
            "load_balancing": asdict(self.load_balancing),
            "intelligent_scheduling": asdict(self.intelligent_scheduling),
            "optimization": asdict(self.optimization),
            "monitoring": asdict(self.monitoring),
            "adaptive_learning": asdict(self.adaptive_learning),
            "performance_thresholds": asdict(self.performance_thresholds),
            "thread_pool_advanced": asdict(self.thread_pool_advanced),
        }


@dataclass
class CustomExecutorProperties:
    """自定义执行器配置属性"""

    name: str = ""
    executor: AsyncExecutorProperties = field(default_factory=AsyncExecutorProperties)
    enabled: bool = True

    def validate(self) -> None:
        """验证配置参数"""
        if not self.name:
            raise ValueError("Custom executor name cannot be empty")

        self.executor.validate()


@dataclass
class AsyncProperties:
    """异步配置属性主类"""

    enabled: bool = True
    executor: AsyncExecutorProperties = field(default_factory=AsyncExecutorProperties)
    custom_executors: dict[str, AsyncExecutorProperties] = field(default_factory=dict)
    default_timeout: Optional[float] = None
    proxy_target_class: bool = False
    exception_handler: Optional[str] = None

    def validate(self) -> None:
        """验证配置参数"""
        self.executor.validate()

        for name, executor_props in self.custom_executors.items():
            if not name:
                raise ValueError("Custom executor name cannot be empty")
            executor_props.validate()

        if self.default_timeout is not None and self.default_timeout <= 0:
            raise ValueError("default-timeout must be positive")

    def get_executor_properties(self, name: Optional[str] = None) -> AsyncExecutorProperties:
        """获取执行器配置属性"""
        if name is None or name == "default":
            return self.executor

        if name in self.custom_executors:
            return self.custom_executors[name]

        raise ValueError(f"Unknown executor: {name}")

    def has_custom_executor(self, name: str) -> bool:
        """检查是否有指定的自定义执行器"""
        return name in self.custom_executors

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "enabled": self.enabled,
            "executor": self.executor.to_dict(),
            "custom_executors": {name: props.to_dict() for name, props in self.custom_executors.items()},
            "default_timeout": self.default_timeout,
            "proxy_target_class": self.proxy_target_class,
            "exception_handler": self.exception_handler,
        }

    def get_all_executor_names(self) -> list[str]:
        """获取所有执行器名称"""
        names = ["default"]
        names.extend(self.custom_executors.keys())
        return names

    @classmethod
    def from_environment(cls, environment) -> "AsyncProperties":
        """从环境配置创建异步配置

        使用统一的框架配置加载方式,简洁高效
        """
        try:
            enabled = environment.get_property("miniboot.async.enabled", True)
            if not enabled:
                return cls(enabled=False)

            executor_props = AsyncExecutorProperties(
                core_size=environment.get_property("miniboot.async.executor.core-size", 2),
                max_size=environment.get_property("miniboot.async.executor.max-size", 4),
                queue_capacity=environment.get_property("miniboot.async.executor.queue-capacity", 100),
                keep_alive=environment.get_property("miniboot.async.executor.keep-alive", 60),
                thread_name_prefix=environment.get_property("miniboot.async.executor.thread-name-prefix", "miniboot-async-"),
                allow_core_thread_timeout=environment.get_property("miniboot.async.executor.allow-core-thread-timeout", False),
                rejection_policy=environment.get_property("miniboot.async.executor.rejection-policy", "caller_runs"),
                # 动态调整配置
                dynamic_adjustment_enabled=environment.get_property("miniboot.async.executor.dynamic-adjustment-enabled", True),
                adjustment_interval=environment.get_property("miniboot.async.executor.adjustment-interval", 5.0),
                load_threshold_high=environment.get_property("miniboot.async.executor.load-threshold-high", 0.8),
                load_threshold_low=environment.get_property("miniboot.async.executor.load-threshold-low", 0.3),
                # 负载均衡配置
                load_balancing_enabled=environment.get_property("miniboot.async.executor.load-balancing-enabled", True),
                load_balancing_strategy=environment.get_property("miniboot.async.executor.load-balancing-strategy", "adaptive"),
                enable_priority_queue=environment.get_property("miniboot.async.executor.enable-priority-queue", True),
                priority_levels=environment.get_property("miniboot.async.executor.priority-levels", 5),
                # 智能调度配置
                enable_strategy_caching=environment.get_property("miniboot.async.executor.enable-strategy-caching", True),
                strategy_cache_size=environment.get_property("miniboot.async.executor.strategy-cache-size", 1000),
                enable_predictive_scheduling=environment.get_property("miniboot.async.executor.enable-predictive-scheduling", True),
                enable_batch_processing=environment.get_property("miniboot.async.executor.enable-batch-processing", True),
            )

            custom_executors = {}
            custom_prefix = "miniboot.async.custom-executors"

            # 动态发现自定义执行器配置
            executor_names = cls._discover_custom_executor_names(environment, custom_prefix)

            for name in executor_names:
                prefix = f"{custom_prefix}.{name}"
                custom_executor_props = AsyncExecutorProperties(
                    core_size=environment.get_property(f"{prefix}.core-size", 4),
                    max_size=environment.get_property(f"{prefix}.max-size", 8),
                    queue_capacity=environment.get_property(f"{prefix}.queue-capacity", 50),
                    keep_alive=environment.get_property(f"{prefix}.keep-alive", 60),
                    thread_name_prefix=environment.get_property(f"{prefix}.thread-name-prefix", f"{name}-"),
                    allow_core_thread_timeout=environment.get_property(f"{prefix}.allow-core-thread-timeout", False),
                    rejection_policy=environment.get_property(f"{prefix}.rejection-policy", "caller_runs"),
                    # 动态调整配置
                    dynamic_adjustment_enabled=environment.get_property(f"{prefix}.dynamic-adjustment-enabled", True),
                    adjustment_interval=environment.get_property(f"{prefix}.adjustment-interval", 5.0),
                    load_threshold_high=environment.get_property(f"{prefix}.load-threshold-high", 0.8),
                    load_threshold_low=environment.get_property(f"{prefix}.load-threshold-low", 0.3),
                    # 负载均衡配置
                    load_balancing_enabled=environment.get_property(f"{prefix}.load-balancing-enabled", True),
                    load_balancing_strategy=environment.get_property(f"{prefix}.load-balancing-strategy", "adaptive"),
                    enable_priority_queue=environment.get_property(f"{prefix}.enable-priority-queue", True),
                    priority_levels=environment.get_property(f"{prefix}.priority-levels", 5),
                    # 智能调度配置
                    enable_strategy_caching=environment.get_property(f"{prefix}.enable-strategy-caching", True),
                    strategy_cache_size=environment.get_property(f"{prefix}.strategy-cache-size", 1000),
                    enable_predictive_scheduling=environment.get_property(f"{prefix}.enable-predictive-scheduling", True),
                    enable_batch_processing=environment.get_property(f"{prefix}.enable-batch-processing", True),
                )
                custom_executors[name] = custom_executor_props

            properties = cls(
                enabled=enabled,
                executor=executor_props,
                custom_executors=custom_executors,
                default_timeout=environment.get_property("miniboot.async.default-timeout"),
                proxy_target_class=environment.get_property("miniboot.async.proxy-target-class", False),
                exception_handler=environment.get_property("miniboot.async.exception-handler"),
            )

            properties.validate()
            return properties

        except Exception as e:
            from loguru import logger

            logger.warning(f"Failed to parse async properties from environment: {e}, using defaults")
            return default_properties()

    @staticmethod
    def _discover_custom_executor_names(environment, custom_prefix: str) -> set[str]:
        """动态发现自定义执行器名称

        Args:
            environment: 环境配置对象
            custom_prefix: 自定义执行器配置前缀

        Returns:
            set[str]: 发现的执行器名称集合
        """
        executor_names = set()

        try:
            # 尝试从属性源中获取所有属性名
            property_sources = environment.get_property_sources()

            for source in property_sources:
                if hasattr(source, "get_property_names"):
                    try:
                        property_names = source.get_property_names()
                        for prop_name in property_names:
                            if prop_name.startswith(f"{custom_prefix}."):
                                # 提取执行器名称
                                # 格式: miniboot.async.custom-executors.{name}.{property}
                                parts = prop_name.split(".")
                                if len(parts) >= 5:  # miniboot.async.custom-executors.{name}.{property}
                                    executor_name = parts[3]
                                    executor_names.add(executor_name)
                    except Exception:
                        # 如果某个属性源不支持get_property_names,继续处理其他源
                        continue

            # 如果没有发现任何执行器,尝试一些常见的名称
            if not executor_names:
                common_names = ["email-pool", "file-pool", "task-pool", "io-pool", "cpu-pool"]
                for name in common_names:
                    if environment.contains_property(f"{custom_prefix}.{name}.core-size"):
                        executor_names.add(name)

        except Exception as e:
            from loguru import logger

            logger.debug(f"Failed to discover custom executor names: {e}")
            # 回退到检查常见名称
            common_names = ["email-pool", "file-pool", "task-pool", "io-pool", "cpu-pool"]
            for name in common_names:
                try:
                    if environment.contains_property(f"{custom_prefix}.{name}.core-size"):
                        executor_names.add(name)
                except Exception:
                    continue

        return executor_names

    def is_enabled(self) -> bool:
        """检查异步模块是否启用"""
        return self.enabled
