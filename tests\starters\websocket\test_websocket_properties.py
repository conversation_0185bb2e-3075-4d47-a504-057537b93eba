#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 配置属性测试
"""

import unittest

from miniboot.starters.websocket.properties import (
    AuthConfig,
    CompressionConfig,
    ConnectionLimitConfig,
    JWTConfig,
    SecurityConfig,
    ServerConfig,
    TimeoutConfig,
    WebSocketProperties,
)


class WebSocketPropertiesTestCase(unittest.TestCase):
    """WebSocket 配置属性测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.properties = WebSocketProperties()

    def test_default_values(self):
        """测试默认值"""
        self.assertTrue(self.properties.enabled)
        self.assertEqual(self.properties.path, "/ws")
        self.assertEqual(self.properties.max_message_size, 512 * 1024)

        # 测试服务器配置默认值
        self.assertEqual(self.properties.server.host, "0.0.0.0")
        self.assertEqual(self.properties.server.port, 8080)
        self.assertEqual(self.properties.server.read_timeout, 10)
        self.assertEqual(self.properties.server.write_timeout, 10)
        self.assertEqual(self.properties.server.idle_timeout, 60)

        # 测试安全配置默认值
        self.assertFalse(self.properties.security.auth.enabled)
        self.assertEqual(self.properties.security.auth.token_header, "Authorization")
        self.assertEqual(self.properties.security.auth.token_query_param, "token")
        self.assertEqual(self.properties.security.auth.token_cookie, "auth_token")

        # 测试 JWT 配置默认值
        self.assertFalse(self.properties.security.jwt.enabled)
        self.assertEqual(self.properties.security.jwt.secret_key, "mini-boot-websocket-jwt-secret-key")
        self.assertEqual(self.properties.security.jwt.issuer, "mini-boot-websocket")
        self.assertEqual(self.properties.security.jwt.expiration_time, 3600)

        # 测试压缩配置默认值
        self.assertFalse(self.properties.compression.enabled)
        self.assertEqual(self.properties.compression.level, 6)

        # 测试超时配置默认值
        self.assertFalse(self.properties.timeout.enabled)
        self.assertEqual(self.properties.timeout.duration, 5)
        self.assertEqual(self.properties.timeout.ping_interval, 54)
        self.assertEqual(self.properties.timeout.pong_timeout, 60)

        # 测试连接限制配置默认值
        self.assertFalse(self.properties.connection_limit.enabled)
        self.assertEqual(self.properties.connection_limit.max_connections_per_user, 5)
        self.assertEqual(self.properties.connection_limit.max_total_connections, 1000)

    def test_custom_values(self):
        """测试自定义值"""
        properties = WebSocketProperties(
            enabled=False,
            path="/custom-ws",
            max_message_size=1024 * 1024,
            server=ServerConfig(host="127.0.0.1", port=9090, read_timeout=20, write_timeout=20, idle_timeout=120),
            security=SecurityConfig(
                auth=AuthConfig(enabled=True, token_header="X-Auth-Token", token_query_param="auth_token", token_cookie="session_token"),
                jwt=JWTConfig(enabled=True, secret_key="custom-secret-key", issuer="custom-issuer", expiration_time=7200),
            ),
            compression=CompressionConfig(enabled=True, level=9),
            timeout=TimeoutConfig(enabled=True, duration=10, ping_interval=30, pong_timeout=30),
            connection_limit=ConnectionLimitConfig(enabled=True, max_connections_per_user=10, max_total_connections=2000),
        )

        self.assertFalse(properties.enabled)
        self.assertEqual(properties.path, "/custom-ws")
        self.assertEqual(properties.max_message_size, 1024 * 1024)

        # 验证服务器配置
        self.assertEqual(properties.server.host, "127.0.0.1")
        self.assertEqual(properties.server.port, 9090)
        self.assertEqual(properties.server.read_timeout, 20)
        self.assertEqual(properties.server.write_timeout, 20)
        self.assertEqual(properties.server.idle_timeout, 120)

        # 验证安全配置
        self.assertTrue(properties.security.auth.enabled)
        self.assertEqual(properties.security.auth.token_header, "X-Auth-Token")
        self.assertEqual(properties.security.auth.token_query_param, "auth_token")
        self.assertEqual(properties.security.auth.token_cookie, "session_token")

        # 验证 JWT 配置
        self.assertTrue(properties.security.jwt.enabled)
        self.assertEqual(properties.security.jwt.secret_key, "custom-secret-key")
        self.assertEqual(properties.security.jwt.issuer, "custom-issuer")
        self.assertEqual(properties.security.jwt.expiration_time, 7200)

        # 验证压缩配置
        self.assertTrue(properties.compression.enabled)
        self.assertEqual(properties.compression.level, 9)

        # 验证超时配置
        self.assertTrue(properties.timeout.enabled)
        self.assertEqual(properties.timeout.duration, 10)
        self.assertEqual(properties.timeout.ping_interval, 30)
        self.assertEqual(properties.timeout.pong_timeout, 30)

        # 验证连接限制配置
        self.assertTrue(properties.connection_limit.enabled)
        self.assertEqual(properties.connection_limit.max_connections_per_user, 10)
        self.assertEqual(properties.connection_limit.max_total_connections, 2000)

    def test_validate_success(self):
        """测试配置验证成功"""
        # 默认配置应该通过验证
        try:
            self.properties.validate()
        except Exception as e:
            self.fail(f"Default configuration validation failed: {e}")

        # 自定义有效配置应该通过验证
        properties = WebSocketProperties(
            max_message_size=1024,
            server=ServerConfig(port=8080, read_timeout=5, write_timeout=5, idle_timeout=30),
            compression=CompressionConfig(enabled=True, level=5),
            connection_limit=ConnectionLimitConfig(enabled=True, max_connections_per_user=1, max_total_connections=100),
        )

        try:
            properties.validate()
        except Exception as e:
            self.fail(f"Valid configuration validation failed: {e}")

    def test_validate_invalid_max_message_size(self):
        """测试无效的最大消息大小"""
        self.properties.max_message_size = 0
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("max_message_size must be positive", str(context.exception))

        self.properties.max_message_size = -1
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("max_message_size must be positive", str(context.exception))

    def test_validate_invalid_server_port(self):
        """测试无效的服务器端口"""
        self.properties.server.port = 0
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("server.port must be between 1 and 65535", str(context.exception))

        self.properties.server.port = 65536
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("server.port must be between 1 and 65535", str(context.exception))

    def test_validate_invalid_timeouts(self):
        """测试无效的超时配置"""
        self.properties.server.read_timeout = 0
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("server.read_timeout must be positive", str(context.exception))

        self.properties.server.read_timeout = 10
        self.properties.server.write_timeout = -1
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("server.write_timeout must be positive", str(context.exception))

        self.properties.server.write_timeout = 10
        self.properties.server.idle_timeout = 0
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("server.idle_timeout must be positive", str(context.exception))

    def test_validate_invalid_compression_level(self):
        """测试无效的压缩级别"""
        self.properties.compression.enabled = True
        self.properties.compression.level = 0
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("compression.level must be between 1 and 9", str(context.exception))

        self.properties.compression.level = 10
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("compression.level must be between 1 and 9", str(context.exception))

    def test_validate_invalid_connection_limits(self):
        """测试无效的连接限制"""
        self.properties.connection_limit.enabled = True
        self.properties.connection_limit.max_connections_per_user = 0
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("connection_limit.max_connections_per_user must be positive", str(context.exception))

        self.properties.connection_limit.max_connections_per_user = 5
        self.properties.connection_limit.max_total_connections = -1
        with self.assertRaises(ValueError) as context:
            self.properties.validate()
        self.assertIn("connection_limit.max_total_connections must be positive", str(context.exception))

    def test_dataclass_features(self):
        """测试数据类特性"""
        # 测试相等性
        props1 = WebSocketProperties()
        props2 = WebSocketProperties()
        self.assertEqual(props1, props2)

        # 测试不相等性
        props2.enabled = False
        self.assertNotEqual(props1, props2)

        # 测试字符串表示
        props_str = str(self.properties)
        self.assertIn("WebSocketProperties", props_str)
        self.assertIn("'enabled': True", props_str)


if __name__ == "__main__":
    unittest.main()
