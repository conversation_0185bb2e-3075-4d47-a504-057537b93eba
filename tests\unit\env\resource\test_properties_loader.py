#!/usr/bin/env python
"""
* @author: cz
* @description: Properties 属性源加载器测试
"""

import tempfile
import unittest
from pathlib import Path

from miniboot.env.resource.loader import DefaultResourceLoader
from miniboot.env.resource.properties_loader import PropertiesPropertySourceLoader


class PropertiesPropertySourceLoaderTestCase(unittest.TestCase):
    """Properties 属性源加载器测试"""

    def setUp(self):
        self.loader = PropertiesPropertySourceLoader()
        self.resource_loader = DefaultResourceLoader()
        self.temp_dir = Path(tempfile.mkdtemp())

    def tearDown(self):
        # 清理临时文件
        for file in self.temp_dir.rglob("*"):
            if file.is_file():
                file.unlink()
        self.temp_dir.rmdir()

    def test_get_file_extensions(self):
        """测试获取支持的文件扩展名"""
        extensions = self.loader.get_file_extensions()
        self.assertEqual([".properties"], extensions)

    def test_load_simple_properties(self):
        """测试加载简单Properties文件"""
        properties_content = """
# Application configuration
app.name=test-app
app.version=1.0.0

# Server configuration
server.port=8080
server.debug=true
server.host=localhost
        """

        # 创建临时Properties文件
        props_file = self.temp_dir / "test.properties"
        props_file.write_text(properties_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("test", self.resource_loader, props_file)

        self.assertEqual(1, len(property_sources))

        source = property_sources[0]
        self.assertEqual("test", source.name)

        # 验证属性
        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual("1.0.0", source.get_property("app.version"))
        self.assertEqual("8080", source.get_property("server.port"))
        self.assertEqual("true", source.get_property("server.debug"))
        self.assertEqual("localhost", source.get_property("server.host"))

    def test_load_properties_with_colon_separator(self):
        """测试加载使用冒号分隔符的Properties文件"""
        properties_content = """
app.name:test-app
app.version:1.0.0
server.port:8080
        """

        # 创建临时Properties文件
        props_file = self.temp_dir / "colon_test.properties"
        props_file.write_text(properties_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("colon_test", self.resource_loader, props_file)

        source = property_sources[0]
        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual("1.0.0", source.get_property("app.version"))
        self.assertEqual("8080", source.get_property("server.port"))

    def test_load_properties_with_escaped_characters(self):
        """测试加载包含转义字符的Properties文件"""
        properties_content = r"""
app.name=test\=app
app.description=This is a test\nwith newline
app.path=C\:\\Program Files\\App
server.message=Hello\ World
        """

        # 创建临时Properties文件
        props_file = self.temp_dir / "escaped_test.properties"
        props_file.write_text(properties_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("escaped_test", self.resource_loader, props_file)

        source = property_sources[0]
        self.assertEqual("test=app", source.get_property("app.name"))
        self.assertEqual("This is a test\nwith newline", source.get_property("app.description"))
        self.assertEqual("C:\\Program Files\\App", source.get_property("app.path"))
        self.assertEqual("Hello World", source.get_property("server.message"))

    def test_load_properties_with_comments(self):
        """测试加载包含注释的Properties文件"""
        properties_content = """
# This is a comment
! This is also a comment
app.name=test-app

# Server settings
server.port=8080
# server.debug=false (commented out)
server.host=localhost
        """

        # 创建临时Properties文件
        props_file = self.temp_dir / "comments_test.properties"
        props_file.write_text(properties_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("comments_test", self.resource_loader, props_file)

        source = property_sources[0]
        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual("8080", source.get_property("server.port"))
        self.assertEqual("localhost", source.get_property("server.host"))

        # 注释行不应该被解析
        self.assertIsNone(source.get_property("server.debug"))

    def test_load_empty_properties(self):
        """测试加载空Properties文件"""
        properties_content = """
# Only comments
! And more comments

        """

        # 创建临时Properties文件
        props_file = self.temp_dir / "empty.properties"
        props_file.write_text(properties_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("empty", self.resource_loader, props_file)

        self.assertEqual(1, len(property_sources))
        source = property_sources[0]
        self.assertEqual("empty", source.name)
        self.assertEqual({}, source._properties)

    def test_load_properties_with_spaces(self):
        """测试加载包含空格的Properties文件"""
        properties_content = """
  app.name  =  test-app
  server.port  :  8080
database.url = ********************************
        """

        # 创建临时Properties文件
        props_file = self.temp_dir / "spaces_test.properties"
        props_file.write_text(properties_content, encoding="utf-8")

        # 加载属性源
        property_sources = self.loader.load("spaces_test", self.resource_loader, props_file)

        source = property_sources[0]
        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual("8080", source.get_property("server.port"))
        self.assertEqual("********************************", source.get_property("database.url"))

    def test_unescape_string(self):
        """测试字符串转义处理"""
        test_cases = [
            (r"test\=value", "test=value"),
            (r"test\:value", "test:value"),
            (r"test\nvalue", "test\nvalue"),
            (r"test\\value", "test\\value"),
            (r"test\ value", "test value"),
            (r"C\:\\Program Files", "C:\\Program Files"),
        ]

        for escaped, expected in test_cases:
            with self.subTest(escaped=escaped):
                result = self.loader._unescape_string(escaped)
                self.assertEqual(expected, result)


if __name__ == "__main__":
    unittest.main()
