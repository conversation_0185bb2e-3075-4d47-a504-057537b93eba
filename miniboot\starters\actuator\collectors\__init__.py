#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Mini-Boot Actuator 异步数据收集器模块

提供高性能的异步数据收集器,支持系统指标、应用指标和健康指标的并发收集.

主要组件:
- AsyncSystemMetricsCollector: 异步系统指标收集器
- AsyncApplicationMetricsCollector: 异步应用指标收集器
- AsyncHealthIndicator: 异步健康指标收集器
- MetricsAggregator: 指标聚合器

使用示例:
    # 1. 系统指标收集
    from miniboot.starters.actuator.collectors import SystemMetricsCollector

    collector = SystemMetricsCollector()
    metrics = await collector.collect_all()

    # 2. 特定指标收集
    cpu_metrics = await collector.collect_specific("cpu")
"""

from .aggregator import MetricsAggregator
from .health import (AsyncCustomHealthIndicator, AsyncDatabaseHealthIndicator,
                     AsyncDiskSpaceHealthIndicator, AsyncHealthCollector,
                     AsyncHealthIndicator, AsyncMemoryHealthIndicator,
                     HealthStatus)
from .metrics import AppMetricsCollector
from .system import SystemMetricsCollector

__all__ = [
    # 异步系统指标收集器
    "SystemMetricsCollector",
    # 异步应用指标收集器
    "AppMetricsCollector",
    # 异步健康指标收集器
    "AsyncHealthCollector",
    "AsyncHealthIndicator",
    "AsyncDiskSpaceHealthIndicator",
    "AsyncMemoryHealthIndicator",
    "AsyncDatabaseHealthIndicator",
    "AsyncCustomHealthIndicator",
    "HealthStatus",
    # 指标聚合器
    "MetricsAggregator",
]

# 版本信息
__version__ = "0.0.1"
