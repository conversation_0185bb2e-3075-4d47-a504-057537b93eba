# Mini-Boot 发布配置文件

# 私有仓库配置
repository:
    # 仓库名称
    name: "pypi-hosted"
    # 仓库地址（基于 docs/20.docker-nexus.md 配置）
    url: "http://*************:8081/repository/pypi-hosted/"
    # 用户名
    username: "developer1"
    # 密码
    password: "dev123"

# 发布前检查配置
checks:
    # 是否运行代码质量检查
    quality_check: true
    # 是否运行测试
    run_tests: true
    # 是否检查 Git 状态
    git_status: true

# 构建配置
build:
    # 是否清理 dist 目录
    clean_dist: true
    # 构建前是否更新版本号
    auto_version: true

# 版本管理配置
version:
    # 默认版本递增类型 (major, minor, patch)
    default_bump: "patch"
    # 是否创建 Git 标签
    create_tag: true
    # 标签前缀
    tag_prefix: "v"
    # 发布内容描述（用作标签描述）
    release_notes: "Mini-Boot 框架发布 - 新增功能和改进"

# 通知配置（可选）
notifications:
    # 是否启用通知
    enabled: false
    # Webhook URL
    webhook_url: ""
    # 通知消息模板
    message_template: "Mini-Boot {version} 发布成功！"
