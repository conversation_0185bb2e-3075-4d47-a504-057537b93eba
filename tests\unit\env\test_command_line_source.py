#!/usr/bin/env python
"""
* @author: cz
* @description: 命令行属性源测试
"""

import unittest

from miniboot.env.sources import CommandLinePropertySource


class CommandLinePropertySourceTestCase(unittest.TestCase):
    """命令行属性源测试"""

    def test_parse_java_style_args(self):
        """测试解析 Java 风格参数"""
        args = ["-Dapp.name=test-app", "-Dserver.port=8080", "-Ddebug"]
        source = CommandLinePropertySource(args=args)

        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual(8080, source.get_property("server.port"))
        self.assertTrue(source.get_property("debug"))

    def test_parse_long_options_with_equals(self):
        """测试解析长选项 --key=value 格式"""
        args = ["--app.name=test-app", "--server.port=9090", "--debug=true"]
        source = CommandLinePropertySource(args=args)

        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual(9090, source.get_property("server.port"))
        self.assertTrue(source.get_property("debug"))

    def test_parse_long_options_with_space(self):
        """测试解析长选项 --key value 格式"""
        args = ["--app.name", "test-app", "--server.port", "7070", "--debug"]
        source = CommandLinePropertySource(args=args)

        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual(7070, source.get_property("server.port"))
        self.assertTrue(source.get_property("debug"))

    def test_mixed_argument_formats(self):
        """测试混合参数格式"""
        args = ["-Dapp.name=test-app", "--server.port=8080", "--database.host", "localhost", "--debug"]
        source = CommandLinePropertySource(args=args)

        self.assertEqual("test-app", source.get_property("app.name"))
        self.assertEqual(8080, source.get_property("server.port"))
        self.assertEqual("localhost", source.get_property("database.host"))
        self.assertTrue(source.get_property("debug"))

    def test_value_type_conversion(self):
        """测试值类型转换"""
        args = ["--string.value=hello", "--int.value=123", "--float.value=45.67", "--bool.true=true", "--bool.false=false"]
        source = CommandLinePropertySource(args=args)

        self.assertEqual("hello", source.get_property("string.value"))
        self.assertEqual(123, source.get_property("int.value"))
        self.assertEqual(45.67, source.get_property("float.value"))
        self.assertTrue(source.get_property("bool.true"))
        self.assertFalse(source.get_property("bool.false"))

    def test_contains_property(self):
        """测试属性存在检查"""
        args = ["--existing.key=value"]
        source = CommandLinePropertySource(args=args)

        self.assertTrue(source.contains_property("existing.key"))
        self.assertFalse(source.contains_property("non.existing.key"))

    def test_empty_args(self):
        """测试空参数列表"""
        source = CommandLinePropertySource(args=[])

        self.assertIsNone(source.get_property("any.key"))
        self.assertFalse(source.contains_property("any.key"))
        self.assertEqual({}, source.properties)

    def test_properties_copy(self):
        """测试属性字典副本"""
        args = ["--key1=value1", "--key2=value2"]
        source = CommandLinePropertySource(args=args)

        properties = source.properties
        self.assertEqual("value1", properties["key1"])
        self.assertEqual("value2", properties["key2"])

        # 修改副本不应该影响原始数据
        properties["key3"] = "value3"
        self.assertIsNone(source.get_property("key3"))

    def test_priority(self):
        """测试优先级设置"""
        source = CommandLinePropertySource()
        self.assertEqual(1000, source.priority)  # 默认最高优先级

        custom_source = CommandLinePropertySource(priority=500)
        self.assertEqual(500, custom_source.priority)

    def test_ignore_short_options(self):
        """测试忽略短选项"""
        args = ["-h", "help", "-v", "--real.option=value"]
        source = CommandLinePropertySource(args=args)

        # 短选项应该被忽略
        self.assertIsNone(source.get_property("h"))
        self.assertIsNone(source.get_property("v"))

        # 长选项应该被解析
        self.assertEqual("value", source.get_property("real.option"))

    def test_complex_values(self):
        """测试复杂值处理"""
        args = ["--url=http://localhost:8080/api", "--path=/home/<USER>/app", "--message=Hello, World!", '--json={"key": "value"}']
        source = CommandLinePropertySource(args=args)

        self.assertEqual("http://localhost:8080/api", source.get_property("url"))
        self.assertEqual("/home/<USER>/app", source.get_property("path"))
        self.assertEqual("Hello, World!", source.get_property("message"))
        self.assertEqual('{"key": "value"}', source.get_property("json"))


if __name__ == "__main__":
    unittest.main()
