#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mock Starter自动配置
"""

from miniboot.annotations import Bean, ConditionalOnProperty
from miniboot.autoconfigure.base import StarterAutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.context import ApplicationContext

from .properties import MockProperties
from .service import MockService


@ConditionalOnProperty(prefix="miniboot.starters.mock", name="enabled", having_value="true", match_if_missing=True)
class MockAutoConfiguration(StarterAutoConfiguration):
    """Mock Starter自动配置类

    当mock.enabled=true时自动配置Mock功能.
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="mock-auto-configuration",
            description="Mock功能自动配置",
            priority=200,  # 中等优先级
            auto_configure_after=[],  # 不依赖其他配置
        )

    def get_starter_name(self) -> str:
        """获取Starter名称"""
        return "miniboot-starter-mock"

    def get_starter_version(self) -> str:
        """获取Starter版本"""
        return "1.0.0"

    def get_starter_description(self) -> str:
        """获取Starter描述"""
        return "Mini-Boot Mock功能Starter,提供测试环境的Mock支持"

    def get_configuration_properties_classes(self) -> list[type]:
        """获取配置属性类列表"""
        return [MockProperties]

    @Bean
    def mock_properties(self) -> MockProperties:
        """创建Mock配置属性Bean

        Returns:
            MockProperties: Mock配置属性实例
        """
        return MockProperties()

    @Bean
    def mock_service(self, mock_properties: MockProperties) -> MockService:
        """创建Mock服务Bean

        Args:
            mock_properties: Mock配置属性

        Returns:
            MockService: Mock服务实例
        """
        return MockService(mock_properties)

    @Bean
    @ConditionalOnProperty(prefix="mock", name="auto_mock", having_value="true", match_if_missing=True)
    def mock_interceptor(self, mock_service: MockService) -> "MockInterceptor":
        """创建Mock拦截器Bean

        Args:
            mock_service: Mock服务

        Returns:
            MockInterceptor: Mock拦截器实例
        """
        return MockInterceptor(mock_service)

    def _initialize_starter(self, context: ApplicationContext) -> None:
        """初始化Mock Starter

        Args:
            context: 应用上下文
        """
        try:
            # 获取Mock配置
            mock_properties = context.get_bean("mock_properties")

            # 记录初始化信息
            from loguru import logger

            logger.info("Mock Starter initialized:")
            logger.info(f"  - Data Source: {mock_properties.data_source}")
            logger.info(f"  - Auto Mock: {mock_properties.auto_mock}")
            logger.info(f"  - Cache Enabled: {mock_properties.cache_enabled}")
            logger.info(f"  - Patterns: {len(mock_properties.patterns)}")

            # 如果配置了数据文件,验证文件存在性
            if mock_properties.data_source == "file" and mock_properties.data_file_path:
                import os

                if not os.path.exists(mock_properties.data_file_path):
                    logger.warning(f"Mock data file not found: {mock_properties.data_file_path}")

            # 注册Mock服务到全局(如果需要)
            if hasattr(context, "register_global_service"):
                mock_service = context.get_bean("mock_service")
                context.register_global_service("mock", mock_service)

        except Exception as e:
            from loguru import logger

            logger.error(f"Failed to initialize Mock Starter: {e}")
            raise


class MockInterceptor:
    """Mock拦截器

    用于自动拦截和Mock外部请求.
    """

    def __init__(self, mock_service: MockService):
        self.mock_service = mock_service
        self._enabled = True

        from loguru import logger

        logger.debug("MockInterceptor initialized")

    def intercept(self, url: str, method: str = "GET", **kwargs):
        """拦截请求并返回Mock数据

        Args:
            url: 请求URL
            method: HTTP方法
            **kwargs: 其他请求参数

        Returns:
            Mock响应数据
        """
        if not self._enabled:
            return None

        try:
            return self.mock_service.mock_request(url, method, **kwargs)
        except Exception as e:
            from loguru import logger

            logger.warning(f"Mock interception failed for {url}: {e}")
            return None

    def enable(self) -> None:
        """启用拦截器"""
        self._enabled = True
        from loguru import logger

        logger.debug("MockInterceptor enabled")

    def disable(self) -> None:
        """禁用拦截器"""
        self._enabled = False
        from loguru import logger

        logger.debug("MockInterceptor disabled")

    def is_enabled(self) -> bool:
        """检查拦截器是否启用"""
        return self._enabled
