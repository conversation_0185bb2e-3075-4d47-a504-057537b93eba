#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 中文TTS播放器实现
"""

import time

from ..exceptions import TTSException


class ChineseTTSPlayer:
    """中文TTS播放器

    基于pyttsx3实现的文本转语音播放器,自动选择中文语音.
    """

    def __init__(self, rate: int = 150, volume: float = 1.0, language: str = "zh-CN"):
        self.rate = rate
        self.volume = volume
        self.language = language
        self.enabled = True
        self._pyttsx3 = None

    def _init_pyttsx3(self):
        """延迟初始化pyttsx3"""
        if self._pyttsx3 is None:
            try:
                import pyttsx3

                self._pyttsx3 = pyttsx3
            except ImportError:
                raise TTSException("pyttsx3库未安装,无法使用TTS功能")

    def speak(self, text: str) -> bool:
        """播放文本语音

        Args:
            text: 要播放的文本

        Returns:
            bool: 播放是否成功
        """
        if not self.enabled or not text or not text.strip():
            return False

        try:
            # 初始化pyttsx3
            self._init_pyttsx3()

            # 每次创建新的引擎实例避免冲突
            engine = self._pyttsx3.init()

            # 自动配置中文语音
            self._configure_chinese_voice(engine)

            # 设置语音参数
            engine.setProperty("rate", self.rate)
            engine.setProperty("volume", self.volume)

            # 播放
            engine.say(text)
            engine.runAndWait()

            # 立即清理
            engine.stop()
            del engine

            return True

        except Exception as e:
            raise TTSException(f"TTS播放失败: {e}")

    def speak_multiple(self, texts: list[str], interval: float = 0.2) -> bool:
        """连续播放多段文本语音

        Args:
            texts: 文本列表
            interval: 播放间隔(秒)

        Returns:
            bool: 是否全部播放成功
        """
        if not self.enabled:
            return False

        success_count = 0
        total_count = len(texts)

        for i, text in enumerate(texts, 1):
            if self.speak(text):
                success_count += 1

            # 播放间隔
            if i < total_count and interval > 0:
                time.sleep(interval)

        return success_count == total_count

    def set_properties(self, rate: int = None, volume: float = None) -> None:
        """设置TTS属性

        Args:
            rate: 语速
            volume: 音量
        """
        if rate is not None:
            self.rate = rate
        if volume is not None and 0.0 <= volume <= 1.0:
            self.volume = volume

    def _configure_chinese_voice(self, engine):
        """自动配置中文语音

        Args:
            engine: pyttsx3引擎实例
        """
        try:
            voices = engine.getProperty("voices")

            if not voices:
                print("⚠️ 未找到可用语音,使用默认语音")
                return

            # 优先级顺序:寻找中文语音
            chinese_keywords = ["zh", "chinese", "中文", "mandarin"]

            for voice in voices:
                if voice.languages:
                    for lang in voice.languages:
                        lang_str = str(lang).lower()
                        if any(keyword in lang_str for keyword in chinese_keywords):
                            engine.setProperty("voice", voice.id)
                            print(f"✅ 使用中文语音: {voice.name}")
                            return

            # 如果没找到中文语音,使用默认语音
            print("⚠️ 未找到中文语音,使用默认语音")

        except Exception as e:
            print(f"⚠️ 配置语音时出错: {e},使用默认语音")
