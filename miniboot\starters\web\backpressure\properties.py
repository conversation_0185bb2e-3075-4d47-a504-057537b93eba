"""
Backpressure Properties

背压控制配置属性类，定义所有可配置的背压控制参数。
"""

from dataclasses import dataclass
from typing import Optional, List

from miniboot.annotations import ConfigurationProperties


@ConfigurationProperties(prefix="miniboot.starters.web.backpressure")
@dataclass
class BackpressureProperties:
    """背压控制配置属性
    
    配置前缀: miniboot.starters.web.backpressure
    
    示例配置:
    ```yaml
    miniboot:
      starters:
        web:
          backpressure:
            enabled: true
            circuit-breaker-enabled: true
            rate-limiting-enabled: true
            max-requests-per-second: 1000
    ```
    """
    
    # ==================== 基础配置 ====================
    enabled: bool = True
    """是否启用背压控制"""
    
    monitoring_enabled: bool = True
    """是否启用性能监控"""
    
    # ==================== 熔断器配置 ====================
    circuit_breaker_enabled: bool = True
    """是否启用熔断器"""
    
    failure_threshold: int = 5
    """熔断器失败阈值（连续失败次数）"""
    
    success_threshold: int = 3
    """熔断器恢复阈值（连续成功次数）"""
    
    timeout_threshold: float = 5.0
    """请求超时阈值(秒)"""
    
    circuit_breaker_timeout: float = 60.0
    """熔断器超时时间(秒)，超时后尝试恢复"""
    
    # ==================== 限流配置 ====================
    rate_limiting_enabled: bool = True
    """是否启用限流"""
    
    max_requests_per_second: int = 1000
    """每秒最大请求数"""
    
    burst_capacity: int = 100
    """突发容量（令牌桶大小）"""
    
    rate_limit_window: float = 1.0
    """限流时间窗口(秒)"""
    
    # ==================== 负载检测配置 ====================
    load_detection_enabled: bool = True
    """是否启用负载检测"""
    
    cpu_threshold: float = 80.0
    """CPU 使用率阈值(%)"""
    
    memory_threshold: float = 85.0
    """内存使用率阈值(%)"""
    
    response_time_threshold: float = 2.0
    """响应时间阈值(秒)"""
    
    error_rate_threshold: float = 5.0
    """错误率阈值(%)"""
    
    # ==================== 自适应控制配置 ====================
    adaptive_control_enabled: bool = True
    """是否启用自适应控制"""
    
    load_factor_adjustment: float = 0.1
    """负载因子调整步长"""
    
    min_rate_limit: int = 10
    """最小限流值"""
    
    max_rate_limit: int = 10000
    """最大限流值"""
    
    # ==================== 监控配置 ====================
    metrics_collection_interval: float = 5.0
    """指标收集间隔(秒)"""
    
    metrics_history_size: int = 1000
    """保留的指标历史记录数量"""
    
    performance_window_size: int = 100
    """性能统计窗口大小（请求数量）"""
    
    # ==================== 恢复机制配置 ====================
    auto_recovery_enabled: bool = True
    """是否启用自动恢复"""
    
    recovery_check_interval: float = 30.0
    """恢复检查间隔(秒)"""
    
    health_check_timeout: float = 5.0
    """健康检查超时时间(秒)"""
    
    recovery_success_threshold: int = 5
    """恢复成功阈值（连续成功次数）"""
    
    # ==================== 降级策略配置 ====================
    degradation_enabled: bool = True
    """是否启用服务降级"""
    
    degradation_response_time: float = 0.1
    """降级响应时间(秒)"""
    
    degradation_success_rate: float = 99.0
    """降级模式成功率(%)"""
    
    # ==================== 队列管理配置 ====================
    queue_management_enabled: bool = True
    """是否启用队列管理"""
    
    max_queue_size: int = 1000
    """最大队列大小"""
    
    queue_timeout: float = 30.0
    """队列超时时间(秒)"""
    
    priority_queue_enabled: bool = False
    """是否启用优先级队列"""
    
    # ==================== 高级配置 ====================
    distributed_mode: bool = False
    """是否启用分布式模式"""
    
    cluster_nodes: List[str] = None
    """集群节点列表"""
    
    coordination_interval: float = 10.0
    """集群协调间隔(秒)"""
    
    enable_detailed_logging: bool = False
    """是否启用详细日志"""
    
    def __post_init__(self):
        """初始化后处理"""
        if self.cluster_nodes is None:
            self.cluster_nodes = []
    
    def validate(self) -> None:
        """验证配置参数的合理性"""
        if self.failure_threshold <= 0:
            raise ValueError("failure_threshold must be positive")
            
        if self.success_threshold <= 0:
            raise ValueError("success_threshold must be positive")
            
        if self.timeout_threshold <= 0:
            raise ValueError("timeout_threshold must be positive")
            
        if self.circuit_breaker_timeout <= 0:
            raise ValueError("circuit_breaker_timeout must be positive")
            
        if self.max_requests_per_second <= 0:
            raise ValueError("max_requests_per_second must be positive")
            
        if self.burst_capacity <= 0:
            raise ValueError("burst_capacity must be positive")
            
        if self.cpu_threshold < 0 or self.cpu_threshold > 100:
            raise ValueError("cpu_threshold must be between 0 and 100")
            
        if self.memory_threshold < 0 or self.memory_threshold > 100:
            raise ValueError("memory_threshold must be between 0 and 100")
            
        if self.response_time_threshold <= 0:
            raise ValueError("response_time_threshold must be positive")
            
        if self.error_rate_threshold < 0 or self.error_rate_threshold > 100:
            raise ValueError("error_rate_threshold must be between 0 and 100")
            
        if self.load_factor_adjustment <= 0 or self.load_factor_adjustment >= 1:
            raise ValueError("load_factor_adjustment must be between 0 and 1")
            
        if self.min_rate_limit <= 0:
            raise ValueError("min_rate_limit must be positive")
            
        if self.max_rate_limit <= self.min_rate_limit:
            raise ValueError("max_rate_limit must be greater than min_rate_limit")
            
        if self.metrics_collection_interval <= 0:
            raise ValueError("metrics_collection_interval must be positive")
            
        if self.performance_window_size <= 0:
            raise ValueError("performance_window_size must be positive")
    
    def get_effective_rate_limit(self, current_load: Optional[float] = None) -> int:
        """获取有效的限流值
        
        Args:
            current_load: 当前系统负载 (0.0-1.0)
            
        Returns:
            有效的限流值
        """
        if not self.adaptive_control_enabled or current_load is None:
            return self.max_requests_per_second
            
        # 根据负载动态调整限流值：负载越高，限流越严格
        load_factor = 1.0 - current_load
        adjusted_rate = int(self.max_requests_per_second * load_factor)
        
        # 确保在合理范围内
        return max(self.min_rate_limit, min(self.max_rate_limit, adjusted_rate))
    
    def should_trigger_circuit_breaker(self, error_rate: float, avg_response_time: float) -> bool:
        """判断是否应该触发熔断器
        
        Args:
            error_rate: 当前错误率 (0.0-100.0)
            avg_response_time: 平均响应时间(秒)
            
        Returns:
            是否应该触发熔断器
        """
        if not self.circuit_breaker_enabled:
            return False
            
        return (
            error_rate > self.error_rate_threshold or
            avg_response_time > self.response_time_threshold
        )
    
    def should_enable_degradation(self, system_load: float, response_time: float) -> bool:
        """判断是否应该启用降级模式
        
        Args:
            system_load: 系统负载 (0.0-1.0)
            response_time: 当前响应时间(秒)
            
        Returns:
            是否应该启用降级模式
        """
        if not self.degradation_enabled:
            return False
            
        cpu_overload = system_load > (self.cpu_threshold / 100.0)
        response_slow = response_time > self.response_time_threshold
        
        return cpu_overload or response_slow
    
    def get_degradation_config(self) -> dict:
        """获取降级配置
        
        Returns:
            降级配置字典
        """
        return {
            "response_time": self.degradation_response_time,
            "success_rate": self.degradation_success_rate,
            "enabled": self.degradation_enabled
        }