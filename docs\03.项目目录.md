# Mini-Boot 项目目录结构

## 1. 项目概述

Mini-Boot 是一个轻量级的 Python Web 框架，参考 Spring Boot 的设计理念。本文档详细说明了项目的目录结构和各个模块的功能职责。

## 2. 根目录结构

```
mini-boot/
├── miniboot/                          # 核心框架包
├── examples/                          # 示例项目
├── tests/                             # 测试代码
├── docs/                              # 项目文档
├── pyproject.toml                     # 项目配置
├── README.md                          # 项目说明
└── LICENSE                            # 开源许可证
```

## 3. 核心框架包 (miniboot/)

### 3.1 整体架构

```mermaid
graph TD
    A[miniboot/] --> B[核心IoC文件]
    A --> C[annotations/]
    A --> D[bean/]
    A --> E[processor/]
    A --> F[errors/]
    A --> G[async/]
    A --> H[actuator/]
    A --> I[env/]
    A --> J[web/]
    A --> K[events/]
    A --> L[schedule/]
    A --> M[starters/]
    A --> N[utils/]

    B --> O[__init__.py]
    B --> P[context.py]
    B --> Q[lifecycle.py]
    B --> R[runner.py]
    B --> S[logger.py]

    C --> T[__init__.py]
    C --> U[core.py]
    C --> V[scanner.py]
    C --> W[processor.py]

    D --> X[__init__.py]
    D --> Y[definition.py]
    D --> Z[factory.py]
    D --> AA[registry.py]
    D --> BB[resolver.py]

    E --> CC[__init__.py]
    E --> DD[autowired.py]
    E --> EE[configuration.py]
    E --> FF[event.py]
    E --> GG[lifecycle.py]
    E --> HH[schedule.py]
    E --> II[value.py]
    E --> JJ[web.py]

    F --> KK[__init__.py]
    F --> LL[core.py]
    F --> MM[bean.py]
    F --> NN[web.py]
    F --> OO[config.py]
    F --> PP[handler.py]

    G --> QQ[__init__.py]
    G --> RR[executor.py]
    G --> SS[pool.py]
    G --> TT[context.py]
    G --> UU[decorators.py]

    H --> VV[__init__.py]
    H --> WW[health.py]
    H --> XX[metrics.py]
    H --> YY[endpoints.py]
    H --> ZZ[collectors.py]
```

### 3.2 核心 IoC 文件（根目录）

```
miniboot/
├── __init__.py                        # 框架主入口，导出核心API
├── context.py                         # ApplicationContext - IoC容器核心
├── lifecycle.py                       # LifecycleManager - Bean生命周期管理
├── processor.py                       # BeanPostProcessor - Bean后处理器
├── conditional.py                     # 条件装配实现
├── runner.py                          # MiniBootApplication - 应用运行器
├── exceptions.py                      # 核心异常定义
├── banner.py                          # 启动横幅显示
└── logger.py                          # Loguru日志配置
```

#### 核心文件功能说明

| 文件名           | 功能说明                                                            |
| ---------------- | ------------------------------------------------------------------- |
| `__init__.py`    | **框架主入口** - 导出所有核心 API，用户主要导入点                   |
| `context.py`     | **ApplicationContext** - IoC 容器的核心实现，统一管理 Bean 生命周期 |
| `lifecycle.py`   | **LifecycleManager** - 管理 Bean 的完整生命周期                     |
| `processor.py`   | **BeanPostProcessor** - Bean 后处理器接口和实现                     |
| `runner.py`      | **MiniBootApplication** - 应用启动和运行的入口类                    |
| `conditional.py` | **条件装配** - @ConditionalOnClass 等条件注解实现                   |
| `logger.py`      | **日志配置** - 集成 loguru，提供统一的日志配置接口                  |

### 3.3 注解系统模块 (annotations/)

```
annotations/
├── __init__.py                        # 注解模块导出
├── core.py                           # 核心注解(@Component、@Service、@Repository等)
├── scanner.py                        # ComponentScanner - 组件扫描器
└── processor.py                      # AnnotationProcessor - 注解处理器
```

**注解模块功能说明**：

| 文件名         | 功能说明                                                            |
| -------------- | ------------------------------------------------------------------- |
| `__init__.py`  | **注解模块导出** - 导出所有核心注解和扫描器                         |
| `core.py`      | **核心注解定义** - @Component、@Service、@Repository、@Autowired 等 |
| `scanner.py`   | **ComponentScanner** - 扫描包路径，发现带注解的组件类               |
| `processor.py` | **AnnotationProcessor** - 注解的解析和处理逻辑                      |

**功能职责**：

-   **core.py**: 定义框架的所有核心注解，包括组件注解、依赖注入注解等
-   **scanner.py**: 实现包路径扫描，自动发现标记了注解的类
-   **processor.py**: 处理注解的元数据，解析注解属性和配置

### 3.4 Bean 管理模块 (bean/)

```
bean/
├── __init__.py                        # Bean模块导出
├── definition.py                      # BeanDefinition - Bean定义
├── factory.py                         # BeanFactory - Bean工厂
├── registry.py                        # BeanRegistry - Bean注册表
└── resolver.py                        # DependencyResolver - 依赖解析器
```

**Bean 模块功能说明**：

| 文件名          | 功能说明                                                      |
| --------------- | ------------------------------------------------------------- |
| `__init__.py`   | **Bean 模块导出** - 导出 Bean 相关的核心类和接口              |
| `definition.py` | **BeanDefinition** - Bean 定义类，描述 Bean 的元数据信息      |
| `factory.py`    | **BeanFactory** - Bean 实例的创建和管理工厂                   |
| `registry.py`   | **BeanRegistry** - Bean 定义和实例的注册表                    |
| `resolver.py`   | **DependencyResolver** - 依赖解析器，处理 Bean 之间的依赖关系 |

**功能职责**：

-   **definition.py**: 定义 Bean 的元数据结构，包括类型、作用域、依赖关系等
-   **factory.py**: 负责 Bean 实例的创建、初始化和销毁
-   **registry.py**: 管理 Bean 定义的注册和 Bean 实例的缓存
-   **resolver.py**: 解析和注入 Bean 之间的依赖关系，处理循环依赖

### 3.5 Bean 处理器模块 (processor/)

```
processor/
├── __init__.py                        # 处理器模块导出
├── autowired.py                       # AutowiredProcessor - 自动装配处理器
├── configuration.py                   # ConfigurationProcessor - 配置类处理器
├── event.py                          # EventProcessor - 事件处理器
├── lifecycle.py                      # LifecycleProcessor - 生命周期处理器
├── schedule.py                       # ScheduleProcessor - 调度处理器
├── value.py                          # ValueProcessor - 值注入处理器
└── web.py                            # WebProcessor - Web注解处理器
```

**处理器模块功能说明**：

| 文件名             | 功能说明                                                        |
| ------------------ | --------------------------------------------------------------- |
| `__init__.py`      | **处理器模块导出** - 导出所有 Bean 处理器接口和实现             |
| `autowired.py`     | **AutowiredProcessor** - 处理@Autowired 注解的依赖注入          |
| `configuration.py` | **ConfigurationProcessor** - 处理@Configuration 和@Bean 注解    |
| `event.py`         | **EventProcessor** - 处理@EventListener 注解的事件监听器        |
| `lifecycle.py`     | **LifecycleProcessor** - Bean 生命周期的前置和后置处理          |
| `schedule.py`      | **ScheduleProcessor** - 处理@Scheduled 注解的定时任务           |
| `value.py`         | **ValueProcessor** - 处理@Value 注解的配置值注入                |
| `web.py`           | **WebProcessor** - 处理@Controller、@RequestMapping 等 Web 注解 |

**功能职责**：

-   **autowired.py**: 在 Bean 初始化时处理依赖注入，解析@Autowired 注解
-   **configuration.py**: 处理配置类，扫描@Bean 方法并注册 Bean 定义
-   **event.py**: 注册事件监听器，处理事件订阅关系
-   **lifecycle.py**: 提供 Bean 初始化前置和后置处理钩子，类似 Spring 的 BeanPostProcessor
-   **schedule.py**: 注册定时任务，处理调度配置
-   **value.py**: 注入配置属性值，支持 SpEL 表达式和默认值
-   **web.py**: 注册 Web 控制器和路由映射

### 3.6 错误处理模块 (errors/)

```
errors/
├── __init__.py                        # 错误模块导出
├── core.py                           # 核心异常定义
├── bean.py                           # Bean相关异常
├── web.py                            # Web相关异常
├── config.py                         # 配置相关异常
└── handler.py                        # 全局异常处理器
```

**错误模块功能说明**：

| 文件名        | 功能说明                                           |
| ------------- | -------------------------------------------------- |
| `__init__.py` | **错误模块导出** - 导出所有异常类和处理器          |
| `core.py`     | **核心异常定义** - 框架核心异常类，如 IoC 容器异常 |
| `bean.py`     | **Bean 相关异常** - Bean 创建、注入、生命周期异常  |
| `web.py`      | **Web 相关异常** - HTTP 请求、路由、控制器异常     |
| `config.py`   | **配置相关异常** - 配置加载、解析、验证异常        |
| `handler.py`  | **全局异常处理器** - 统一异常处理和错误响应        |

**功能职责**：

-   **core.py**: 定义框架核心异常，如 ApplicationContextException、BeanCreationException
-   **bean.py**: 定义 Bean 相关异常，如 BeanNotFoundException、CircularDependencyException
-   **web.py**: 定义 Web 相关异常，如 ControllerNotFoundException、RequestMappingException
-   **config.py**: 定义配置相关异常，如 ConfigurationException、PropertyNotFoundException
-   **handler.py**: 提供全局异常处理机制，统一错误响应格式

### 3.8 异步处理模块 (async/)

```
async/
├── __init__.py                        # 异步模块导出
├── executor.py                        # AsyncExecutor - 异步执行器
├── pool.py                           # ThreadPool - 线程池管理
├── context.py                        # AsyncContext - 异步上下文
└── decorators.py                     # 异步装饰器(@Async等)
```

**异步模块功能说明**：

| 文件名          | 功能说明                                                      |
| --------------- | ------------------------------------------------------------- |
| `__init__.py`   | **异步模块导出** - 导出异步相关的核心类和装饰器               |
| `executor.py`   | **AsyncExecutor** - 异步任务执行器，管理异步任务的执行        |
| `pool.py`       | **ThreadPool** - 线程池管理器，提供线程池的创建和管理         |
| `context.py`    | **AsyncContext** - 异步上下文管理，处理异步环境下的 Bean 管理 |
| `decorators.py` | **异步装饰器** - @Async、@Background 等异步相关注解           |

**功能职责**：

-   **executor.py**: 提供异步任务的执行和调度，支持 asyncio 和线程池两种模式
-   **pool.py**: 管理线程池的创建、配置和生命周期
-   **context.py**: 在异步环境下管理 Bean 的作用域和生命周期
-   **decorators.py**: 提供异步方法的注解支持，简化异步编程

### 3.9 系统监控模块 (actuator/)

```
actuator/
├── __init__.py                        # 监控模块导出
├── health.py                         # HealthIndicator - 健康检查
├── metrics.py                        # MetricsCollector - 指标收集
├── endpoints.py                      # MonitorEndpoints - 监控端点
└── collectors.py                     # DataCollectors - 数据收集器
```

**监控模块功能说明**：

| 文件名          | 功能说明                                                     |
| --------------- | ------------------------------------------------------------ |
| `__init__.py`   | **监控模块导出** - 导出监控相关的核心类和接口                |
| `health.py`     | **HealthIndicator** - 健康检查指示器，检查应用和依赖服务状态 |
| `metrics.py`    | **MetricsCollector** - 指标收集器，收集应用性能和业务指标    |
| `endpoints.py`  | **MonitorEndpoints** - 监控端点，暴露健康检查和指标查询接口  |
| `collectors.py` | **DataCollectors** - 数据收集器，收集系统资源和运行时数据    |

**功能职责**：

-   **health.py**: 提供应用健康状态检查，包括数据库、缓存、外部服务等依赖检查
-   **metrics.py**: 收集和管理应用指标，如请求数量、响应时间、错误率等
-   **endpoints.py**: 暴露 `/health`、`/metrics`、`/info` 等监控端点
-   **collectors.py**: 收集系统级数据，如 CPU、内存、磁盘使用率等

## 4. 功能模块

### 4.1 环境和配置管理模块 (env/)

```
env/
├── __init__.py                        # 环境模块导出
├── auto_configuration.py             # 自动配置引擎
├── property_loader.py                 # 属性加载器
├── environment.py                     # 环境管理器
├── conditional_evaluator.py          # 条件评估器
├── yaml_loader.py                    # YAML配置加载器
├── json_loader.py                    # JSON配置加载器
├── properties.py                     # 配置属性管理
└── profiles.py                       # 环境配置文件管理
```

**功能职责**：

-   **environment.py**: 管理不同环境（dev、test、prod）的配置
-   **property_loader.py**: 从多种源加载配置属性
-   **auto_configuration.py**: 处理自动配置类和条件装配
-   **conditional_evaluator.py**: 评估@ConditionalOnClass 等条件注解
-   **profiles.py**: 管理 application-dev.yml 等环境特定配置

### 4.2 Web 集成模块 (web/)

```
web/
├── __init__.py                        # Web模块导出
├── application.py                     # Web应用
├── auto_configuration.py             # Web自动配置
├── annotations.py                     # Web注解(@Controller、@RequestMapping等)
├── routing.py                        # 路由系统
├── middleware.py                     # 中间件管理
├── exception_handler.py              # 异常处理器
└── adapters/                         # Web框架适配器
    ├── __init__.py
    ├── fastapi_adapter.py            # FastAPI适配器
    └── flask_adapter.py              # Flask适配器
```

**功能职责**：

-   **application.py**: Web 应用的启动和管理
-   **routing.py**: 基于注解的路由自动注册
-   **middleware.py**: 中间件的注册和管理
-   **adapters/**: 不同 Web 框架的适配器实现

### 4.3 事件系统模块 (events/)

```
events/
├── __init__.py                        # 事件模块导出
├── event_bus.py                      # 事件总线
├── event_publisher.py                # 事件发布器
├── event_listener.py                 # 事件监听器
├── annotations.py                     # 事件注解(@EventListener等)
├── async_publisher.py                # 异步事件发布器
└── auto_configuration.py             # 事件自动配置
```

**功能职责**：

-   **event_bus.py**: 事件的分发和路由
-   **event_publisher.py**: 事件的发布接口
-   **async_publisher.py**: 异步事件处理

### 4.4 任务调度模块 (schedule/)

```
schedule/
├── __init__.py                        # 调度模块导出
├── scheduler.py                      # 调度器
├── annotations.py                     # 调度注解(@Scheduled等)
├── auto_configuration.py             # 调度自动配置
├── tasks.py                          # 任务定义
├── cron.py                           # Cron表达式解析
└── async_scheduler.py                # 异步调度器
```

**功能职责**：

-   **scheduler.py**: 定时任务的调度管理
-   **tasks.py**: 任务的定义和执行
-   **cron.py**: Cron 表达式的解析和计算

### 4.5 Starter 机制模块 (starters/)

```mermaid
graph TD
    A[starters/] --> B[mock/]
    A --> C[monitor/]
    A --> D[__init__.py]

    B --> E[META-INF/]
    B --> F[__init__.py]
    B --> G[configuration.py]
    B --> H[properties.py]
    B --> I[service.py]

    C --> J[META-INF/]
    C --> K[__init__.py]
    C --> L[collectors.py]
    C --> M[configuration.py]
    C --> N[metrics.py]
    C --> O[properties.py]
    C --> P[service.py]
```

```
starters/
├── __init__.py                        # Starter模块导出
├── mock/                              # Mock功能Starter
│   ├── META-INF/                      # 元信息目录
│   ├── __init__.py                    # Mock Starter导出
│   ├── configuration.py               # Mock自动配置类
│   ├── properties.py                  # Mock配置属性
│   └── service.py                     # Mock服务实现
└── monitor/                           # 监控功能Starter
    ├── META-INF/                      # 元信息目录
    ├── __init__.py                    # Monitor Starter导出
    ├── collectors.py                  # 指标收集器
    ├── configuration.py               # 监控自动配置类
    ├── metrics.py                     # 指标定义
    ├── properties.py                  # 监控配置属性
    └── service.py                     # 监控服务实现
```

#### Starter 功能说明

**mock/ - Mock 功能 Starter**：

-   测试环境的 Mock 功能支持
-   自动 Mock 外部依赖服务
-   可配置的 Mock 数据生成

**monitor/ - 监控功能 Starter**：

-   应用性能监控（APM）
-   健康检查端点
-   自定义指标收集
-   监控数据导出

#### 标准 Starter 结构

```
starter_name/
├── META-INF/                          # 元信息目录
│   └── mini.factories                 # 自动配置类声明
├── __init__.py                        # Starter导出
├── configuration.py                   # 自动配置类
├── properties.py                      # 配置属性类
└── service.py                         # 核心服务实现
```

### 4.6 工具类模块 (utils/)

```
utils/
├── __init__.py                        # 工具模块导出
├── reflection.py                     # 反射工具
├── class_scanner.py                  # 类扫描工具
├── type_utils.py                     # 类型工具
├── string_utils.py                   # 字符串工具
└── validation.py                     # 验证工具
```

**功能职责**：

-   **reflection.py**: Python 反射和内省功能
-   **class_scanner.py**: 包和类的扫描工具
-   **type_utils.py**: 类型检查和转换工具

## 5. 示例项目 (examples/)

```
examples/
├── hello_world/                      # Hello World示例
│   ├── main.py
│   ├── application.yml
│   └── README.md
├── rest_api/                         # REST API示例
│   ├── main.py
│   ├── controllers/
│   ├── services/
│   ├── models/
│   └── README.md
├── full_featured/                    # 完整功能示例
│   ├── main.py
│   ├── config/
│   ├── web/
│   ├── services/
│   ├── events/
│   ├── tasks/
│   └── README.md
└── microservice/                     # 微服务示例
    ├── user_service/
    ├── order_service/
    └── gateway/
```

## 6. 测试代码 (tests/)

```
tests/
├── unit/                             # 单元测试
│   ├── core/                         # 核心模块测试
│   ├── annotations/                  # 注解系统测试
│   ├── env/                          # 配置管理测试
│   ├── web/                          # Web功能测试
│   └── utils/                        # 工具类测试
├── integration/                      # 集成测试
│   ├── container/                    # 容器集成测试
│   ├── web/                          # Web集成测试
│   └── full_app/                     # 完整应用测试
├── performance/                      # 性能测试
│   ├── startup_time.py               # 启动时间测试
│   ├── memory_usage.py               # 内存使用测试
│   └── throughput.py                 # 吞吐量测试
└── fixtures/                         # 测试数据和工具
    ├── test_apps/                    # 测试应用
    └── mock_data/                    # 模拟数据
```

## 7. 项目文档 (docs/)

```
docs/
├── prd.md                            # 产品需求文档
├── 架构设计.md                        # 架构设计文档
├── 项目目录.md                        # 项目目录结构文档
├── api/                              # API文档
│   ├── core.md                       # 核心API文档
│   ├── annotations.md                # 注解API文档
│   ├── env.md                        # 配置API文档
│   └── web.md                        # Web API文档
├── guides/                           # 使用指南
│   ├── quick_start.md                # 快速开始
│   ├── configuration.md              # 配置指南
│   ├── web_development.md            # Web开发指南
│   └── best_practices.md             # 最佳实践
└── design/                           # 设计文档
    ├── technical_design.md           # 技术设计
    ├── api_design.md                 # API设计
    └── performance_design.md         # 性能设计
```

## 8. 设计原则

### 8.1 扁平化结构

-   核心 IoC 功能直接在 `miniboot` 根目录
-   减少嵌套层级，更易理解和使用
-   符合 Python 包的常见组织方式

### 8.2 模块化设计

-   **根目录** - 核心 IoC 容器功能
-   **annotations** - 注解系统和组件扫描
-   **bean** - Bean 定义、创建和管理
-   **processor** - Bean 处理器和生命周期管理
-   **errors** - 错误处理和异常管理
-   **async** - 异步处理和线程池管理
-   **actuator** - 系统监控和健康检查
-   **env** - 环境和配置管理
-   **web** - Web 功能
-   **events** - 事件系统
-   **schedule** - 任务调度
-   **starters** - 功能启动器
-   **utils** - 工具类

### 8.3 职责单一

-   每个组件只负责一个核心功能
-   通过接口和抽象类定义清晰的边界
-   避免组件间的直接耦合

### 8.4 扩展性强

-   通过 BeanPostProcessor 提供扩展点
-   支持自定义自动配置
-   事件驱动的松耦合架构
-   插件化的功能模块

### 8.5 配置驱动

-   约定优于配置的设计理念
-   支持外部化配置管理
-   条件装配机制
-   自动配置和属性绑定

## 9. 使用示例

### 9.1 简洁的导入方式

```python
# 核心功能导入
from miniboot import MiniBootApplication
from miniboot.annotations import Component, Service, Autowired
from miniboot.bean import BeanFactory, BeanDefinition
from miniboot.processor import BeanPostProcessor, LifecycleProcessor
from miniboot.errors import MiniBootException, BeanCreationException
from miniboot.async import Async, AsyncExecutor
from miniboot.actuator import HealthIndicator, MetricsCollector
from miniboot.web import RestController, GetMapping
from miniboot.events import EventListener
from miniboot.log import Logger
```

### 9.2 用户应用代码示例

```python
# 用户应用 - main.py
from miniboot import MiniBootApplication
from miniboot.annotations import Service, Autowired, Component
from miniboot.processor import BeanPostProcessor
from miniboot.errors import BeanCreationException, WebException
from miniboot.async import Async
from miniboot.actuator import HealthIndicator
from miniboot.web import RestController, GetMapping, ExceptionHandler

@RestController
class HelloController:
    @Autowired
    def __init__(self, hello_service: HelloService):
        self.hello_service = hello_service

    @GetMapping("/hello")
    async def hello(self):
        try:
            message = await self.hello_service.get_message_async()
            return {"message": message}
        except Exception as e:
            raise WebException(f"Failed to get message: {str(e)}")

@Service
class HelloService:
    def get_message(self):
        return "Hello from Mini-Boot!"

    @Async
    async def get_message_async(self):
        # 模拟异步操作
        import asyncio
        await asyncio.sleep(0.1)
        return "Hello from Mini-Boot Async!"

@Component
class DatabaseHealthIndicator(HealthIndicator):
    def check_health(self):
        # 检查数据库连接状态
        try:
            # 模拟数据库检查
            return {"status": "UP", "database": "connected"}
        except Exception as e:
            return {"status": "DOWN", "error": str(e)}

@Component
class CustomBeanPostProcessor(BeanPostProcessor):
    def post_process_before_initialization(self, bean, bean_name: str):
        """Bean初始化前的处理"""
        try:
            print(f"Before initialization: {bean_name}")
            return bean
        except Exception as e:
            raise BeanCreationException(f"Failed to process bean {bean_name}: {str(e)}")

    def post_process_after_initialization(self, bean, bean_name: str):
        """Bean初始化后的处理"""
        print(f"After initialization: {bean_name}")
        # 可以返回代理对象或增强的Bean
        return bean

@Component
class GlobalExceptionHandler:
    @ExceptionHandler(WebException)
    def handle_web_exception(self, e: WebException):
        return {"error": "Web Error", "message": str(e), "code": 500}

    @ExceptionHandler(BeanCreationException)
    def handle_bean_exception(self, e: BeanCreationException):
        return {"error": "Bean Creation Error", "message": str(e), "code": 500}

if __name__ == "__main__":
    MiniBootApplication.run()
```

## 10. Starter 机制优势

### 10.1 模块化设计

-   每个 Starter 独立封装一个功能领域
-   可选择性启用，按需加载
-   清晰的功能边界

### 10.2 自动配置

-   通过 META-INF 声明自动配置类
-   框架自动发现和加载
-   零配置启用功能

### 10.3 配置驱动

-   通过 properties.py 定义配置选项
-   支持外部配置文件覆盖
-   提供合理的默认值

### 10.4 扩展性强

-   标准化的 Starter 结构
-   易于添加新的功能模块
-   支持第三方 Starter 开发

## 11. 完整的模块架构

现在 Mini-Boot 框架具备了完整的模块化架构：

```
miniboot/
├── 根目录 - 容器协调和应用启动
├── annotations/ - 注解系统和组件扫描
├── bean/ - Bean定义、创建和管理
├── processor/ - Bean处理器和生命周期管理
├── errors/ - 错误处理和异常管理
├── async/ - 异步处理和线程池管理
├── actuator/ - 系统监控和健康检查
├── env/ - 环境和配置管理
├── web/ - Web功能和路由
├── events/ - 事件系统
├── schedule/ - 任务调度
├── starters/ - 功能启动器
└── utils/ - 工具类
```

## 12. Actuator 监控功能特性

### 12.1 健康检查端点

```
GET /actuator/health
{
  "status": "UP",
  "components": {
    "database": {"status": "UP", "details": {...}},
    "redis": {"status": "UP", "details": {...}},
    "diskSpace": {"status": "UP", "details": {...}}
  }
}
```

### 12.2 应用指标端点

```
GET /actuator/metrics
{
  "names": [
    "http.server.requests",
    "jvm.memory.used",
    "system.cpu.usage",
    "application.ready.time"
  ]
}
```

### 12.3 应用信息端点

```
GET /actuator/info
{
  "app": {
    "name": "mini-boot-app",
    "version": "1.0.0",
    "description": "Mini-Boot Application"
  },
  "build": {
    "time": "2024-01-01T00:00:00Z",
    "version": "1.0.0"
  }
}
```

### 12.4 自定义监控指标

```python
from miniboot.actuator import MetricsCollector

@Service
class BusinessMetrics:
    @Autowired
    def __init__(self, metrics: MetricsCollector):
        self.metrics = metrics

    def record_user_action(self, action: str):
        self.metrics.counter("user.actions", tags={"action": action}).increment()

    def record_response_time(self, duration: float):
        self.metrics.timer("response.time").record(duration)
```

## 13. Processor 处理器机制详解

### 13.1 BeanPostProcessor 接口

```python
from abc import ABC, abstractmethod

class BeanPostProcessor(ABC):
    """Bean后处理器接口，类似Spring Boot的BeanPostProcessor"""

    def post_process_before_initialization(self, bean, bean_name: str):
        """Bean初始化前的处理钩子"""
        return bean

    def post_process_after_initialization(self, bean, bean_name: str):
        """Bean初始化后的处理钩子"""
        return bean
```

### 13.2 内置处理器示例

#### **AutowiredProcessor - 依赖注入处理器**

```python
@Component
class AutowiredProcessor(BeanPostProcessor):
    def post_process_before_initialization(self, bean, bean_name: str):
        # 处理@Autowired注解的字段和方法
        self._inject_dependencies(bean)
        return bean
```

#### **ValueProcessor - 配置值注入处理器**

```python
@Component
class ValueProcessor(BeanPostProcessor):
    def post_process_before_initialization(self, bean, bean_name: str):
        # 处理@Value注解的配置值注入
        self._inject_config_values(bean)
        return bean
```

#### **WebProcessor - Web 注解处理器**

```python
@Component
class WebProcessor(BeanPostProcessor):
    def post_process_after_initialization(self, bean, bean_name: str):
        # 注册@Controller和@RequestMapping
        if hasattr(bean, '__class__'):
            self._register_web_endpoints(bean)
        return bean
```

### 13.3 自定义处理器示例

#### **缓存代理处理器**

```python
@Component
class CacheProxyProcessor(BeanPostProcessor):
    def post_process_after_initialization(self, bean, bean_name: str):
        # 为标记了@Cacheable的方法创建缓存代理
        if self._has_cache_annotations(bean):
            return self._create_cache_proxy(bean)
        return bean
```

#### **性能监控处理器**

```python
@Component
class PerformanceMonitorProcessor(BeanPostProcessor):
    def post_process_after_initialization(self, bean, bean_name: str):
        # 为Service类添加性能监控
        if hasattr(bean.__class__, '__annotations__'):
            if any(annotation.__name__ == 'Service'
                   for annotation in bean.__class__.__annotations__.values()):
                return self._create_performance_proxy(bean)
        return bean
```

### 13.4 处理器执行顺序

```python
# 处理器执行流程
1. Bean实例创建
2. 执行所有BeanPostProcessor.post_process_before_initialization()
3. Bean初始化（调用@PostConstruct方法）
4. 执行所有BeanPostProcessor.post_process_after_initialization()
5. Bean准备就绪，可以被注入到其他Bean中
```

## 14. Errors 错误处理机制详解

### 14.1 异常层次结构

```python
# 异常继承层次
MiniBootException (基础异常)
├── ApplicationContextException (应用上下文异常)
├── BeanException (Bean相关异常)
│   ├── BeanCreationException (Bean创建异常)
│   ├── BeanNotFoundException (Bean未找到异常)
│   ├── CircularDependencyException (循环依赖异常)
│   └── BeanDefinitionException (Bean定义异常)
├── WebException (Web相关异常)
│   ├── ControllerNotFoundException (控制器未找到异常)
│   ├── RequestMappingException (请求映射异常)
│   └── HttpException (HTTP异常)
└── ConfigurationException (配置相关异常)
    ├── PropertyNotFoundException (属性未找到异常)
    ├── ConfigurationLoadException (配置加载异常)
    └── ValidationException (验证异常)
```

### 14.2 核心异常定义

#### **core.py - 核心异常**

```python
class MiniBootException(Exception):
    """Mini-Boot框架基础异常"""
    def __init__(self, message: str, cause: Exception = None):
        super().__init__(message)
        self.cause = cause

class ApplicationContextException(MiniBootException):
    """应用上下文异常"""
    pass
```

#### **bean.py - Bean 相关异常**

```python
class BeanException(MiniBootException):
    """Bean相关异常基类"""
    pass

class BeanCreationException(BeanException):
    """Bean创建异常"""
    def __init__(self, bean_name: str, message: str, cause: Exception = None):
        super().__init__(f"Failed to create bean '{bean_name}': {message}", cause)
        self.bean_name = bean_name

class CircularDependencyException(BeanException):
    """循环依赖异常"""
    def __init__(self, dependency_chain: list):
        chain_str = " -> ".join(dependency_chain)
        super().__init__(f"Circular dependency detected: {chain_str}")
        self.dependency_chain = dependency_chain
```

### 14.3 全局异常处理器

```python
@Component
class GlobalExceptionHandler:
    """全局异常处理器"""

    @ExceptionHandler(BeanCreationException)
    def handle_bean_creation_error(self, e: BeanCreationException):
        return {
            "error": "Bean Creation Failed",
            "bean_name": e.bean_name,
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }

    @ExceptionHandler(WebException)
    def handle_web_error(self, e: WebException):
        return {
            "error": "Web Request Failed",
            "message": str(e),
            "status_code": getattr(e, 'status_code', 500)
        }

    @ExceptionHandler(Exception)
    def handle_general_error(self, e: Exception):
        return {
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "type": type(e).__name__
        }
```

### 14.4 错误处理最佳实践

#### **异常传播链**

```python
try:
    # 业务逻辑
    result = some_operation()
except DatabaseException as e:
    # 转换为框架异常
    raise BeanCreationException("userService", "Database connection failed", e)
```

#### **错误日志记录**

```python
@Component
class ErrorLogger:
    def log_error(self, exception: Exception, context: dict = None):
        logger.error(
            f"Exception occurred: {type(exception).__name__}",
            extra={
                "exception": str(exception),
                "context": context or {},
                "stack_trace": traceback.format_exc()
            }
        )
```

---

_本文档详细描述了 Mini-Boot 框架的完整目录结构和设计理念，包括企业级的监控、运维能力、强大的 Bean 处理器机制和完善的错误处理体系，为项目开发提供了清晰的指导。_
