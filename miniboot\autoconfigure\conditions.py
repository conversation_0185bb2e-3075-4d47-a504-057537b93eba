#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 条件注解和评估器模块

包含：
- ConfigurationCondition: 条件接口和实现类
- ConditionEvaluator: 条件评估器
- 条件装饰器函数
"""

import importlib
from abc import ABC, abstractmethod
from typing import Any, Union

from loguru import logger

from miniboot.context import ApplicationContext
from miniboot.errors.domains.autoconfigure import \
    ConditionEvaluationError as ConditionEvaluationException


def _add_condition_to_target(target, condition):
    """为目标对象添加条件，统一装饰器逻辑

    Args:
        target: 目标类或函数
        condition: 条件对象

    Returns:
        添加条件后的目标对象
    """
    if not hasattr(target, "_autoconfigure_conditions"):
        target._autoconfigure_conditions = []
    target._autoconfigure_conditions.append(condition)
    return target


class ConfigurationCondition(ABC):
    """配置条件接口

    所有条件注解的基础接口,定义条件评估的标准方法.
    """

    @abstractmethod
    def matches(self, context: ApplicationContext) -> bool:
        """判断条件是否匹配

        Args:
            context: 应用上下文

        Returns:
            bool: 条件是否匹配
        """
        pass

    def get_condition_message(self) -> str:
        """获取条件描述信息

        Returns:
            str: 条件描述
        """
        return f"{self.__class__.__name__} condition"


class ConditionalOnProperty(ConfigurationCondition):
    """基于属性的条件判断

    检查指定的配置属性是否存在且匹配期望值.
    """

    def __init__(self, prefix: str = None, name: str = None, having_value: Any = True, match_if_missing: bool = False):
        """初始化属性条件

        Args:
            prefix: 属性前缀
            name: 属性名称
            having_value: 期望的属性值
            match_if_missing: 属性不存在时是否匹配
        """
        self.prefix = prefix
        self.name = name
        self.having_value = having_value
        self.match_if_missing = match_if_missing

        # 构建完整的属性键
        if prefix and name:
            self.property_key = f"{prefix}.{name}"
        elif name:
            self.property_key = name
        else:
            raise ValueError("Either 'name' or both 'prefix' and 'name' must be provided")

    def matches(self, context: ApplicationContext) -> bool:
        """判断属性条件是否匹配"""
        try:
            env = context.get_environment()
            property_value = env.get_property(self.property_key)

            # 属性不存在的情况
            if property_value is None:
                return self.match_if_missing

            # 比较属性值
            return self._compare_values(property_value, self.having_value)

        except Exception as e:
            logger.warning(f"Error evaluating property condition {self.property_key}: {e}")
            return self.match_if_missing

    def _compare_values(self, actual: Any, expected: Any) -> bool:
        """比较实际值和期望值"""
        # 字符串比较(忽略大小写)
        if isinstance(actual, str) and isinstance(expected, str):
            return actual.lower() == expected.lower()

        # 布尔值比较
        if isinstance(expected, bool):
            if isinstance(actual, str):
                return actual.lower() in ("true", "1", "yes", "on") if expected else actual.lower() in ("false", "0", "no", "off")
            return bool(actual) == expected

        # 直接比较
        return actual == expected

    def get_condition_message(self) -> str:
        return f"@ConditionalOnProperty(name={self.property_key}, havingValue={self.having_value})"


class ConditionalOnClass(ConfigurationCondition):
    """基于类存在的条件判断

    检查指定的类是否在类路径中可用.
    """

    def __init__(self, class_name: str):
        """初始化类条件

        Args:
            class_name: 要检查的类名(完整路径)
        """
        self.class_name = class_name

    def matches(self, _context: ApplicationContext) -> bool:
        """判断类是否存在"""
        try:
            # 尝试导入类
            module_name, class_name = self.class_name.rsplit(".", 1)
            module = importlib.import_module(module_name)
            getattr(module, class_name)
            return True
        except (ImportError, AttributeError, ValueError):
            return False

    def get_condition_message(self) -> str:
        return f"@ConditionalOnClass({self.class_name})"


class ConditionalOnBean(ConfigurationCondition):
    """基于Bean存在的条件判断

    检查指定的Bean是否在应用上下文中存在.
    """

    def __init__(self, bean_type: Union[type, str] = None, name: str = None):
        """初始化Bean条件

        Args:
            bean_type: Bean类型或类型名称
            name: Bean名称
        """
        if bean_type is None and name is None:
            raise ValueError("Either 'bean_type' or 'name' must be provided")

        self.bean_type = bean_type
        self.name = name

    def matches(self, context: ApplicationContext) -> bool:
        """判断Bean是否存在"""
        try:
            # 按名称查找
            if self.name:
                return context.contains_bean(self.name)

            # 按类型查找
            if self.bean_type:
                if isinstance(self.bean_type, str):
                    # 字符串类型名称
                    return context.contains_bean(self.bean_type)
                else:
                    # 实际类型
                    try:
                        context.get_bean(self.bean_type)
                        return True
                    except Exception:
                        return False

            return False
        except Exception as e:
            logger.warning(f"Error evaluating bean condition: {e}")
            return False

    def get_condition_message(self) -> str:
        if self.name:
            return f"@ConditionalOnBean(name={self.name})"
        else:
            bean_type_name = self.bean_type.__name__ if hasattr(self.bean_type, "__name__") else str(self.bean_type)
            return f"@ConditionalOnBean(type={bean_type_name})"


class ConditionalOnMissingBean(ConfigurationCondition):
    """基于Bean不存在的条件判断

    检查指定的Bean是否在应用上下文中不存在.
    """

    def __init__(self, bean_type: Union[type, str] = None, name: str = None):
        """初始化Bean缺失条件

        Args:
            bean_type: Bean类型或类型名称
            name: Bean名称
        """
        self.bean_condition = ConditionalOnBean(bean_type, name)

    def matches(self, context: ApplicationContext) -> bool:
        """判断Bean是否不存在"""
        return not self.bean_condition.matches(context)

    def get_condition_message(self) -> str:
        return self.bean_condition.get_condition_message().replace("@ConditionalOnBean", "@ConditionalOnMissingBean")


class ConditionalOnResource(ConfigurationCondition):
    """基于资源存在的条件判断

    检查指定的资源文件是否存在.
    """

    def __init__(self, resource_path: str):
        """初始化资源条件

        Args:
            resource_path: 资源文件路径
        """
        self.resource_path = resource_path

    def matches(self, _context: ApplicationContext) -> bool:
        """判断资源是否存在"""
        try:
            from pathlib import Path

            # 检查绝对路径
            resource_path = Path(self.resource_path)
            if resource_path.is_absolute():
                return resource_path.exists()

            # 检查相对路径(相对于当前工作目录)
            return Path(self.resource_path).exists()

        except Exception as e:
            logger.warning(f"Error checking resource {self.resource_path}: {e}")
            return False

    def get_condition_message(self) -> str:
        return f"@ConditionalOnResource({self.resource_path})"


class ConditionEvaluator:
    """条件评估器

    统一的条件评估入口,负责评估各种类型的条件.
    """

    def __init__(self):
        """初始化条件评估器"""
        self._condition_cache = {}

    def evaluate_conditions(self, conditions: list[ConfigurationCondition], context: ApplicationContext) -> bool:
        """评估条件列表

        Args:
            conditions: 条件列表
            context: 应用上下文

        Returns:
            bool: 所有条件是否都满足
        """
        if not conditions:
            return True

        for condition in conditions:
            try:
                if not self._evaluate_single_condition(condition, context):
                    logger.debug(f"Condition failed: {condition.get_condition_message()}")
                    return False
            except Exception as e:
                logger.error(f"Error evaluating condition {condition.get_condition_message()}: {e}")
                raise ConditionEvaluationException(condition.get_condition_message(), str(e)) from e

        return True

    def _evaluate_single_condition(self, condition: ConfigurationCondition, context: ApplicationContext) -> bool:
        """评估单个条件"""
        # 简单的缓存机制(基于条件消息)
        cache_key = condition.get_condition_message()
        if cache_key in self._condition_cache:
            return self._condition_cache[cache_key]

        result = condition.matches(context)
        self._condition_cache[cache_key] = result

        logger.debug(f"Condition evaluation: {cache_key} -> {result}")
        return result

    def clear_cache(self) -> None:
        """清除条件评估缓存"""
        self._condition_cache.clear()


# ==================== 条件装饰器函数 ====================

def conditional_on_property(prefix: str = None, name: str = None, having_value: Any = True, match_if_missing: bool = False):
    """基于属性的条件装饰器

    Args:
        prefix: 属性前缀
        name: 属性名称
        having_value: 期望的属性值
        match_if_missing: 属性不存在时是否匹配
    """

    def decorator(cls_or_func):
        condition = ConditionalOnProperty(prefix, name, having_value, match_if_missing)
        return _add_condition_to_target(cls_or_func, condition)

    return decorator


def conditional_on_class(class_name: str):
    """基于类存在的条件装饰器

    Args:
        class_name: 要检查的类名(完整路径)
    """

    def decorator(cls_or_func):
        condition = ConditionalOnClass(class_name)
        return _add_condition_to_target(cls_or_func, condition)

    return decorator


def conditional_on_bean(bean_type: Union[type, str] = None, name: str = None):
    """基于Bean存在的条件装饰器

    Args:
        bean_type: Bean类型或类型名称
        name: Bean名称
    """

    def decorator(cls_or_func):
        condition = ConditionalOnBean(bean_type, name)
        return _add_condition_to_target(cls_or_func, condition)

    return decorator


def conditional_on_missing_bean(bean_type: Union[type, str] = None, name: str = None):
    """基于Bean不存在的条件装饰器

    Args:
        bean_type: Bean类型或类型名称
        name: Bean名称
    """

    def decorator(cls_or_func):
        condition = ConditionalOnMissingBean(bean_type, name)
        return _add_condition_to_target(cls_or_func, condition)

    return decorator


def conditional_on_resource(resource_path: str):
    """基于资源存在的条件装饰器

    Args:
        resource_path: 资源文件路径
    """

    def decorator(cls_or_func):
        condition = ConditionalOnResource(resource_path)
        return _add_condition_to_target(cls_or_func, condition)

    return decorator


# ==================== 装饰器别名 ====================
# 为了与Spring Boot保持一致,提供大写的装饰器别名

ConditionalOnProperty = conditional_on_property
ConditionalOnClass = conditional_on_class
ConditionalOnBean = conditional_on_bean
ConditionalOnMissingBean = conditional_on_missing_bean
ConditionalOnResource = conditional_on_resource


# ==================== 工具函数 ====================

def get_conditions(cls) -> list:
    """从类中获取条件列表

    Args:
        cls: 要检查的类

    Returns:
        list: 条件列表
    """
    conditions = []

    # 获取类级别的条件
    if hasattr(cls, "_autoconfigure_conditions"):
        conditions.extend(cls._autoconfigure_conditions)

    # 获取方法级别的条件(如果需要的话)
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if hasattr(attr, "_autoconfigure_conditions"):
            conditions.extend(attr._autoconfigure_conditions)

    return conditions


def has_conditions(cls_or_func) -> bool:
    """检查类或函数是否有条件注解

    Args:
        cls_or_func: 要检查的类或函数

    Returns:
        bool: 是否有条件注解
    """
    return hasattr(cls_or_func, "_autoconfigure_conditions") and len(cls_or_func._autoconfigure_conditions) > 0
