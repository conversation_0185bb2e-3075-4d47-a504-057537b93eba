#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 运行时环境检测模块

提供智能的运行时环境检测功能,能够自动识别当前运行环境的各种特征,
包括异步支持、框架环境、线程状态等,为应用上下文提供最优的执行策略建议.
"""

import asyncio
import inspect
import sys
import threading
from typing import Any

from loguru import logger

from .async_detection_strategy import AsyncDetectionChain


class RuntimeDetector:
    """运行时环境检测器

    智能检测当前运行时环境的各种特征,包括异步支持、框架环境、线程状态等.
    检测因素包括:事件循环状态、调用栈分析、线程环境、框架支持等.
    """

    def __init__(self):
        """初始化运行时环境检测器"""
        self._detection_cache = {}
        self._cache_lock = threading.Lock()
        self._async_detection_chain = AsyncDetectionChain()

        logger.debug("RuntimeDetector initialized")

    def is_async_environment(self, use_cache: bool = True) -> bool:
        """检测是否为异步环境

        Args:
            use_cache: 是否使用缓存结果

        Returns:
            bool: True表示异步环境,False表示同步环境
        """
        if use_cache:
            cache_key = self._get_cache_key()
            with self._cache_lock:
                if cache_key in self._detection_cache:
                    result = self._detection_cache[cache_key]
                    logger.debug(f"Using cached async detection result: {result}")
                    return result

        # 执行检测
        result = self._perform_detection()

        if use_cache:
            with self._cache_lock:
                self._detection_cache[cache_key] = result

        logger.debug(f"Async environment detection result: {result}")
        return result

    def _perform_detection(self) -> bool:
        """执行异步环境检测

        Returns:
            bool: 检测结果
        """
        # 使用策略模式的异步检测链
        return self._async_detection_chain.detect_async_environment()

    def _detect_event_loop(self) -> float:
        """检测事件循环状态

        Returns:
            float: 异步倾向度 (0.0-1.0)
        """
        try:
            # 尝试获取当前运行的事件循环
            loop = asyncio.get_running_loop()
            if loop and loop.is_running():
                logger.debug("Found running event loop")
                return 0.8  # 高异步倾向
            else:
                logger.debug("Event loop exists but not running")
                return 0.3  # 中等异步倾向
        except RuntimeError:
            # 没有运行的事件循环
            try:
                # 检查是否可以获取事件循环
                loop = asyncio.get_event_loop()
                if loop:
                    logger.debug("Event loop available but not running")
                    return 0.2  # 低异步倾向
            except RuntimeError:
                logger.debug("No event loop available")
                return 0.0  # 无异步倾向

        return 0.0

    def _detect_async_in_stack(self) -> float:
        """检测调用栈中的异步函数

        Returns:
            float: 异步倾向度 (0.0-1.0)
        """
        try:
            # 获取当前调用栈
            frame = inspect.currentframe()
            async_count = 0
            total_count = 0

            while frame and total_count < 20:  # 限制检查深度
                try:
                    code = frame.f_code

                    # 检查函数是否为协程
                    if code.co_flags & inspect.CO_ITERABLE_COROUTINE:
                        async_count += 1
                        logger.debug(f"Found async function in stack: {code.co_name}")

                    total_count += 1
                    frame = frame.f_back

                except Exception:
                    break

            if total_count == 0:
                return 0.0

            ratio = async_count / total_count
            logger.debug(f"Async functions in stack: {async_count}/{total_count} = {ratio:.2f}")

            # 转换为异步倾向度
            if ratio > 0.5:
                return 0.9
            elif ratio > 0.2:
                return 0.6
            elif ratio > 0:
                return 0.3
            else:
                return 0.0

        except Exception as e:
            logger.debug(f"Error detecting async in stack: {e}")
            return 0.0

    def _detect_async_frameworks(self) -> float:
        """检测异步框架环境

        Returns:
            float: 异步倾向度 (0.0-1.0)
        """
        async_frameworks = ["fastapi", "aiohttp", "tornado", "sanic", "quart", "starlette", "uvicorn", "hypercorn"]

        detected_frameworks = []

        for framework in async_frameworks:
            if framework in sys.modules:
                detected_frameworks.append(framework)
                logger.debug(f"Detected async framework: {framework}")

        if detected_frameworks:
            # 根据检测到的框架数量计算倾向度
            ratio = min(len(detected_frameworks) / 3, 1.0)  # 最多3个框架达到满分
            return 0.5 + ratio * 0.4  # 0.5-0.9范围

        return 0.0

    def _detect_thread_environment(self) -> float:
        """检测线程环境

        Returns:
            float: 异步倾向度 (0.0-1.0)
        """
        try:
            current_thread = threading.current_thread()
            thread_name = current_thread.name.lower()

            # 检查线程名称中的异步相关关键词
            async_keywords = ["async", "event", "loop", "coroutine", "await"]

            for keyword in async_keywords:
                if keyword in thread_name:
                    logger.debug(f"Found async keyword '{keyword}' in thread name: {thread_name}")
                    return 0.4

            # 检查是否为主线程
            if current_thread is threading.main_thread():
                logger.debug("Running in main thread")
                return 0.2  # 主线程中等异步倾向
            else:
                logger.debug(f"Running in worker thread: {thread_name}")
                return 0.1  # 工作线程低异步倾向

        except Exception as e:
            logger.debug(f"Error detecting thread environment: {e}")
            return 0.0

    def _detect_python_async_support(self) -> float:
        """检测Python版本和异步特性支持

        Returns:
            float: 异步倾向度 (0.0-1.0)
        """
        try:
            version_info = sys.version_info

            # Python 3.7+ 有更好的异步支持
            if version_info >= (3, 9):
                logger.debug("Python 3.9+ detected - excellent async support")
                return 0.3
            elif version_info >= (3, 7):
                logger.debug("Python 3.7+ detected - good async support")
                return 0.2
            elif version_info >= (3, 5):
                logger.debug("Python 3.5+ detected - basic async support")
                return 0.1
            else:
                logger.debug("Python < 3.5 detected - limited async support")
                return 0.0

        except Exception as e:
            logger.debug(f"Error detecting Python async support: {e}")
            return 0.0

    def _evaluate_factors(self, factors: list[tuple[str, float]]) -> bool:
        """综合评估检测因素

        Args:
            factors: 检测因素列表,每个元素为(名称, 权重)

        Returns:
            bool: 最终评估结果
        """
        # 定义权重
        weights = {
            "event_loop": 0.4,  # 事件循环状态最重要
            "async_stack": 0.3,  # 调用栈异步函数次重要
            "async_framework": 0.2,  # 异步框架检测
            "thread_env": 0.05,  # 线程环境
            "python_support": 0.05,  # Python版本支持
        }

        total_score = 0.0
        total_weight = 0.0

        for factor_name, factor_value in factors:
            weight = weights.get(factor_name, 0.0)
            total_score += factor_value * weight
            total_weight += weight

            logger.debug(f"Factor '{factor_name}': value={factor_value:.2f}, weight={weight:.2f}")

        if total_weight == 0:
            return False

        final_score = total_score / total_weight
        logger.debug(f"Final score: {final_score:.2f}")

        # 阈值判断:大于0.5认为是异步环境
        return final_score > 0.5

    def _get_cache_key(self) -> str:
        """生成缓存键

        Returns:
            str: 缓存键
        """
        try:
            thread_id = threading.get_ident()

            # 尝试获取事件循环ID
            loop_id = "no_loop"
            try:
                loop = asyncio.get_running_loop()
                loop_id = id(loop)
            except RuntimeError:
                pass

            return f"thread_{thread_id}_loop_{loop_id}"

        except Exception:
            return "default"

    def clear_cache(self) -> None:
        """清除检测缓存"""
        with self._cache_lock:
            self._detection_cache.clear()
            logger.debug("Runtime detection cache cleared")

    def get_detection_info(self) -> dict[str, Any]:
        """获取详细的检测信息

        Returns:
            dict: 检测信息详情
        """
        info = {
            "is_async_environment": self.is_async_environment(use_cache=False),
            "event_loop_factor": self._detect_event_loop(),
            "async_stack_factor": self._detect_async_in_stack(),
            "framework_factor": self._detect_async_frameworks(),
            "thread_factor": self._detect_thread_environment(),
            "python_support_factor": self._detect_python_async_support(),
            "cache_size": len(self._detection_cache),
        }

        return info


# 向后兼容性别名
AsyncEnvironmentDetector = RuntimeDetector
