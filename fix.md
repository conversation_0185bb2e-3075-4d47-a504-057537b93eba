# Mini-Boot Spring Boot 风格模块整合改进方案 - 详细任务清单

## 📋 项目概述

将 Mini-Boot 框架重构为 Spring Boot 风格的模块整合架构，实现统一的 Bean 驱动模块管理机制。通过引入 AutoConfiguration 机制和统一的 Lifecycle 接口，解决当前模块管理分散、接口不一致的问题。

## 🎯 核心目标

- **统一模块管理**：所有模块通过 Bean 方式集成到 ApplicationContext
- **配置驱动**：基于 Properties + @Bean 的配置驱动模式
- **异步生命周期统一**：统一的 AsyncLifecycle 接口管理模块生命周期
- **并发启动优化**：支持阶段性并发启动，显著提升性能
- **架构一致性**：与 Spring Boot 架构高度一致，降低学习成本

## 🚀 **重大架构升级**

### **异步 Lifecycle 整合**

本次改进将原有的 `Lifecycle` 和 `SmartLifecycle` 接口整合为统一的 `AsyncLifecycle` 接口：

**🎯 主要优势：**

- **性能提升 50%+**：并发启动减少总启动时间
- **架构简化**：消除双重生命周期管理体系
- **功能增强**：支持超时控制、优雅停止、错误恢复
- **现代化设计**：原生异步编程，符合 Python 最佳实践

**🔄 迁移策略：**

- **渐进式迁移**：通过适配器保持向后兼容
- **新模块优先**：新开发的模块直接使用 AsyncLifecycle
- **逐步替换**：现有模块逐步迁移到异步实现

---

## 🏗️ 详细架构设计

### 1. 整体架构对比

#### 1.1 当前架构问题

**🚨 现有架构的核心问题：**

```python
# 当前 DefaultApplicationContext 的问题架构
class DefaultApplicationContext(ApplicationContext):
    def _init_components(self) -> None:
        # ❌ 问题1：直接实例化，不通过 Bean 机制
        self._event_publisher = ApplicationEventPublisher()
        self._component_scanner = ComponentScanner()
        self._processor_registry = BeanPostProcessorRegistry()

    async def _execute_startup_sequence(self) -> None:
        # ❌ 问题2：分散的模块初始化逻辑
        await self._initialize_environment()        # Environment 模块
        self._create_smart_bean_factory()          # Bean 模块 - 同步
        await self._initialize_event_publisher()   # Events 模块 - 异步
        await self._start_scheduler()              # Schedule 模块 - 异步
        await self._start_web_server()             # Web 模块 - 异步
```

**问题分析：**

- **双重管理体系**：既有 ApplicationContext 直接管理，又有 Bean 工厂管理
- **接口不统一**：各模块的启动、停止、状态检查方法不一致
- **依赖关系混乱**：模块间通过直接引用而非依赖注入
- **配置驱动不彻底**：部分模块支持配置驱动，部分手动创建

#### 1.2 目标架构设计

**✅ Spring Boot 风格的统一架构：**

```python
# 目标架构：一切皆 Bean
class DefaultApplicationContext(ApplicationContext):
    def __init__(self, config_path: Optional[str] = None):
        # ✅ 只初始化最核心的组件
        self._environment: Environment = None
        self._bean_factory: BeanFactory = None
        # ✅ 其他所有组件都通过 Bean 方式管理

    async def start(self) -> None:
        """统一的启动流程"""
        # 1. 初始化环境
        await self._initialize_environment()

        # 2. 创建 Bean 工厂
        self._create_bean_factory()

        # 3. 注册框架自动配置
        self._register_framework_auto_configurations()

        # 4. 扫描用户 Bean
        await self._scan_user_components()

        # 5. 创建所有 Bean（包括模块 Bean）
        await self._instantiate_all_beans()

        # 6. 启动生命周期 Bean
        await self._start_lifecycle_beans()
```

### 2. 核心设计原则

#### 2.1 一切皆 Bean 原则

**🎯 目标：所有模块功能都通过 Bean 的方式集成**

```mermaid
graph TD
    A[ApplicationContext] --> B[BeanFactory]
    B --> C[WebApplication Bean]
    B --> D[EventPublisher Bean]
    B --> E[Scheduler Bean]
    B --> F[ActuatorContext Bean]

    G[WebAutoConfiguration] --> C
    H[EventsAutoConfiguration] --> D
    I[ScheduleAutoConfiguration] --> E
    J[ActuatorAutoConfiguration] --> F

    K[WebProperties Bean] --> C
    L[EventProperties Bean] --> D
    M[SchedulerProperties Bean] --> E
    N[ActuatorProperties Bean] --> F
```

#### 2.2 配置驱动原则

**🔧 每个模块都有对应的配置类和自动配置类：**

```python
# 配置属性类 - 使用现有模式
@dataclass
class WebProperties:
    """Web 模块配置属性"""
    # 基础配置
    enabled: bool = True
    host: str = "0.0.0.0"
    port: int = 8080
    auto_start: bool = True

# 自动配置类
@Configuration
@ConditionalOnClass("fastapi.FastAPI")
@ConditionalOnProperty("miniboot.web.enabled", having_value="true", match_if_missing=True)
class WebAutoConfiguration:
    """Web 模块自动配置"""

    @Bean
    def web_properties(self) -> WebProperties:
        """创建 Web 配置属性 Bean"""
        return WebProperties()

    @Bean
    def web_application(self, web_properties: WebProperties) -> WebApplication:
        """创建 Web 应用 Bean"""
        return WebApplication(web_properties)
```

#### 2.3 生命周期统一原则

**🔄 所有模块实现统一的 Lifecycle 接口：**

```python
# 重构后的统一生命周期接口 (miniboot/bean/base.py)
class Lifecycle(ABC):
    """统一的生命周期接口

    整合了原有的 Lifecycle 和 SmartLifecycle 功能，
    所有方法都采用异步设计。
    """

    @abstractmethod
    async def start(self) -> None:
        """异步启动方法"""
        pass

    @abstractmethod
    async def stop(self, callback: Optional[Callable] = None) -> None:
        """异步停止方法，支持回调"""
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查是否正在运行"""
        pass

    def is_auto_startup(self) -> bool:
        """是否自动启动"""
        return True

    def get_phase(self) -> int:
        """获取启动阶段优先级（数值越小优先级越高）"""
        return 0

    async def graceful_stop(self, timeout: float = 30.0) -> bool:
        """优雅停止，支持超时控制"""
        try:
            await asyncio.wait_for(self.stop(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            return False
```

### 3. 改进方案：Spring Boot 风格的模块整合

#### 3.1 核心改进策略

**🎯 目标：一切模块功能都通过 Bean 的方式集成**

| 方面             | Spring Boot              | Mini-Boot 改进后            | 当前 Mini-Boot |
| ---------------- | ------------------------ | --------------------------- | -------------- |
| **模块集成方式** | Bean + AutoConfiguration | ✅ Bean + AutoConfiguration | ❌ 直接实例化  |
| **依赖管理**     | Bean 依赖注入            | ✅ Bean 依赖注入            | ❌ 手动管理    |
| **生命周期管理** | Lifecycle 接口           | ✅ Lifecycle 接口           | ❌ 分散管理    |
| **配置驱动**     | @ConfigurationProperties | ✅ Properties + @Bean       | ⚠️ 部分支持    |
| **条件化配置**   | @Conditional 注解        | ✅ @Conditional 注解        | ❌ 手动判断    |
| **架构一致性**   | 高度一致                 | ✅ 高度一致                 | ❌ 不一致      |

#### 3.2 具体实现方案

**步骤 1：定义统一接口**

```python
# miniboot/bean/base.py - Lifecycle 接口已存在
class Lifecycle(ABC):
    """生命周期接口 - 已存在于 Bean 模块"""

    @abstractmethod
    def start(self) -> None:
        """启动方法"""
        pass

    @abstractmethod
    def stop(self) -> None:
        """停止方法"""
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查是否正在运行"""
        pass

# miniboot/autoconfigure/properties.py - 使用现有的 StarterProperties
class StarterProperties(ABC):
    """Starter配置属性基类 - 已存在"""
    enabled: bool = True  # 是否启用Starter

    def is_enabled(self) -> bool:
        return self.enabled
```

**步骤 2：创建自动配置类**

```python
# miniboot/autoconfigure/web_auto_configuration.py
@Configuration
@ConditionalOnClass("fastapi.FastAPI")
class WebAutoConfiguration:
    @Bean
    def web_application(self, web_properties: WebProperties) -> WebApplication:
        return WebApplication(web_properties)
```

**步骤 3：重构 ApplicationContext**

```python
class DefaultApplicationContext(ApplicationContext):
    def _register_framework_auto_configurations(self) -> None:
        """注册框架内置的自动配置类"""
        auto_configurations = [
            WebAutoConfiguration,
            EventsAutoConfiguration,
            AsyncsAutoConfiguration,
            ScheduleAutoConfiguration,
        ]
        for config_class in auto_configurations:
            if self._evaluate_configuration_conditions(config_class):
                self._register_configuration_class(config_class)
```

**步骤 4：实现生命周期管理**

```python
async def _start_lifecycle_beans(self) -> None:
    """启动所有生命周期 Bean"""
    lifecycle_beans = self._bean_factory.get_beans_of_type(Lifecycle)

    # 按阶段排序启动
    sorted_beans = sorted(lifecycle_beans.items(),
                         key=lambda x: x[1].get_phase())

    for bean_name, bean in sorted_beans:
        if hasattr(bean, 'start'):
            await self._safe_start_bean(bean, bean_name)
```

### 4. 技术架构图

#### 4.1 模块整合架构图

```mermaid
graph TB
    subgraph "ApplicationContext Layer"
        AC[DefaultApplicationContext]
        BF[BeanFactory]
        AC --> BF
    end

    subgraph "AutoConfiguration Layer"
        WAC[WebAutoConfiguration]
        EAC[EventsAutoConfiguration]
        AAC[AsyncsAutoConfiguration]
        SAC[ScheduleAutoConfiguration]
    end

    subgraph "Properties Layer"
        WP[WebProperties]
        EP[EventProperties]
        AP[AsyncProperties]
        SP[SchedulerProperties]
    end

    subgraph "Module Layer"
        WA[WebApplication]
        EPub[EventPublisher]
        AE[AsyncExecutor]
        Sch[MiniBootScheduler]
    end

    subgraph "Lifecycle Management"
        LC[Lifecycle Interface]
        LP[LifecycleProcessor]
    end

    AC --> WAC
    AC --> EAC
    AC --> AAC
    AC --> SAC

    WAC --> WP
    WAC --> WA
    EAC --> EP
    EAC --> EPub
    AAC --> AP
    AAC --> AE
    SAC --> SP
    SAC --> Sch

    WA -.-> LC
    EPub -.-> LC
    AE -.-> LC
    Sch -.-> LC

    AC --> LP
    LP --> LC
```

#### 4.2 Bean 创建和依赖注入流程

```mermaid
sequenceDiagram
    participant AC as ApplicationContext
    participant BF as BeanFactory
    participant WAC as WebAutoConfiguration
    participant WA as WebApplication
    participant WP as WebProperties

    AC->>AC: 1. 初始化环境
    AC->>BF: 2. 创建 BeanFactory
    AC->>AC: 3. 注册 AutoConfiguration
    AC->>WAC: 4. 评估条件注解
    WAC-->>AC: 5. 条件满足
    AC->>BF: 6. 注册 Bean 定义
    AC->>BF: 7. 创建所有 Bean
    BF->>WP: 8. 创建 WebProperties Bean
    BF->>WA: 9. 创建 WebApplication Bean (注入 WebProperties)
    AC->>WA: 10. 启动 Lifecycle Bean
    WA-->>AC: 11. 启动完成
```

### 5. 设计模式和最佳实践

#### 5.1 工厂模式 + 依赖注入

**🏭 AutoConfiguration 作为 Bean 工厂：**

```python
@Configuration
class WebAutoConfiguration:
    """Web 模块的 Bean 工厂"""

    @Bean
    @ConditionalOnMissingBean
    def web_properties(self) -> WebProperties:
        """工厂方法：创建配置属性"""
        properties = WebProperties()
        # 从环境中绑定配置值
        self._bind_properties(properties)
        return properties

    @Bean
    @ConditionalOnBean(WebProperties)
    def web_application(self, web_properties: WebProperties) -> WebApplication:
        """工厂方法：创建 Web 应用（依赖注入配置）"""
        return WebApplication(web_properties)

    @Bean
    @ConditionalOnBean(WebApplication)
    def controller_registry(self, web_app: WebApplication) -> ControllerRegistry:
        """工厂方法：创建控制器注册表（依赖注入 Web 应用）"""
        return ControllerRegistry(web_app)
```

#### 5.2 策略模式 + 条件化配置

**🎯 基于条件的策略选择：**

```python
# 不同环境使用不同的实现策略
@Configuration
class EventsAutoConfiguration:

    @Bean
    @ConditionalOnProperty("miniboot.events.type", having_value="async")
    def async_event_publisher(self) -> EventPublisher:
        """异步事件发布器策略"""
        return AsyncEventPublisher()

    @Bean
    @ConditionalOnProperty("miniboot.events.type", having_value="sync")
    def sync_event_publisher(self) -> EventPublisher:
        """同步事件发布器策略"""
        return SyncEventPublisher()

    @Bean
    @ConditionalOnMissingBean(EventPublisher)
    def default_event_publisher(self) -> EventPublisher:
        """默认事件发布器策略"""
        return DefaultEventPublisher()
```

#### 5.3 观察者模式 + 生命周期管理

**👀 生命周期事件的观察者模式：**

```python
class LifecycleProcessor:
    """生命周期处理器 - 观察者模式"""

    def __init__(self):
        self._lifecycle_listeners: List[LifecycleListener] = []

    def add_lifecycle_listener(self, listener: LifecycleListener) -> None:
        """添加生命周期监听器"""
        self._lifecycle_listeners.append(listener)

    async def start_beans(self, beans: Dict[str, Lifecycle]) -> None:
        """启动所有生命周期 Bean"""
        # 按阶段排序
        sorted_beans = sorted(beans.items(), key=lambda x: x[1].get_phase())

        for bean_name, bean in sorted_beans:
            # 通知监听器：Bean 即将启动
            await self._notify_listeners('before_start', bean_name, bean)

            try:
                await bean.start()
                # 通知监听器：Bean 启动成功
                await self._notify_listeners('after_start', bean_name, bean)
            except Exception as e:
                # 通知监听器：Bean 启动失败
                await self._notify_listeners('start_failed', bean_name, bean, e)
                raise
```

### 6. 关键技术决策说明

#### 6.1 为什么选择 Bean 驱动架构？

**✅ 优势：**

1. **统一管理**：所有组件都在同一个容器中管理，便于监控和调试
2. **依赖注入**：自动解决组件间的依赖关系，降低耦合度
3. **配置驱动**：通过配置控制组件的创建和行为，提高灵活性
4. **生命周期统一**：统一的启动、停止流程，便于资源管理
5. **可测试性**：易于进行单元测试和集成测试

**❌ 劣势和缓解方案：**

1. **复杂性增加** → 通过清晰的文档和示例降低学习成本
2. **性能开销** → 通过延迟初始化和缓存优化性能
3. **调试难度** → 提供详细的日志和诊断工具

#### 6.2 为什么选择 AutoConfiguration 模式？

**🔧 技术原因：**

1. **模块化**：每个模块的配置逻辑独立，便于维护
2. **条件化**：支持根据环境和配置动态启用/禁用模块
3. **扩展性**：用户可以轻松添加自定义的自动配置
4. **Spring Boot 兼容**：与 Spring Boot 的设计理念一致

#### 6.3 为什么选择 Lifecycle 接口？

**🔄 设计考虑：**

1. **标准化**：提供统一的生命周期管理接口
2. **可控性**：支持精确控制组件的启动顺序
3. **优雅关闭**：支持资源的优雅释放和清理
4. **监控友好**：便于监控组件的运行状态

### 7. 实施风险评估和缓解策略

#### 7.1 技术风险

| 风险项         | 风险等级 | 影响             | 缓解策略                                                       |
| -------------- | -------- | ---------------- | -------------------------------------------------------------- |
| **循环依赖**   | 🔴 高    | Bean 创建失败    | 1. 设计时避免循环依赖<br>2. 使用 @Lazy 注解<br>3. 重构依赖关系 |
| **性能退化**   | 🟡 中    | 启动时间增加     | 1. 性能基准测试<br>2. 延迟初始化<br>3. 并行创建 Bean           |
| **配置复杂**   | 🟡 中    | 学习成本增加     | 1. 详细文档<br>2. 示例代码<br>3. 迁移指南                      |
| **兼容性问题** | 🟢 低    | 现有代码需要修改 | 1. 渐进式迁移<br>2. 兼容性适配器<br>3. 充分测试                |

#### 7.2 实施风险

| 风险项           | 风险等级 | 影响         | 缓解策略                                          |
| ---------------- | -------- | ------------ | ------------------------------------------------- |
| **开发时间超期** | 🟡 中    | 项目延期     | 1. 分阶段实施<br>2. 并行开发<br>3. 预留缓冲时间   |
| **团队学习成本** | 🟡 中    | 开发效率下降 | 1. 技术培训<br>2. 结对编程<br>3. 代码审查         |
| **测试覆盖不足** | 🔴 高    | 质量问题     | 1. TDD 开发<br>2. 自动化测试<br>3. 代码覆盖率监控 |

### 8. 成功标准和验收条件

#### 8.1 功能性标准

- [ ] **模块创建统一**：所有模块都通过 AutoConfiguration 创建
- [ ] **依赖注入正常**：模块间依赖关系自动解决
- [ ] **生命周期管理**：所有模块支持统一的启动/停止
- [ ] **配置驱动**：模块行为可通过配置文件控制
- [ ] **条件化配置**：支持根据条件启用/禁用模块

#### 8.2 非功能性标准

- [ ] **性能要求**：启动时间不超过原有架构的 120%
- [ ] **内存使用**：内存占用不超过原有架构的 110%
- [ ] **代码质量**：测试覆盖率 ≥ 90%，无 ruff 警告
- [ ] **文档完整**：架构文档、API 文档、迁移指南齐全
- [ ] **可维护性**：代码结构清晰，模块职责明确

## 📅 总体时间规划

- **总工期**：4-5 周
- **并行开发**：部分任务可并行执行
- **风险缓冲**：预留 20% 时间用于问题解决和优化

---

## 🚀 阶段 1：基础接口和生命周期定义

**目标**：为后续模块重构奠定统一的基础架构
**预计时间**：1 周
**优先级**：🔥 最高

### 1.1 创建统一的 Lifecycle 接口

**文件**：`miniboot/bean/base.py` (重构)
**工作量**：6 小时
**依赖**：无

**实现步骤**：

1. 重构 `Lifecycle` 接口，整合原有 `SmartLifecycle` 功能
2. 所有方法统一采用异步设计，支持并发启动/停止
3. 添加优雅停止、超时控制等高级功能
4. 移除旧的同步接口，全面异步化

**统一接口设计**：

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 统一的生命周期接口
"""

from abc import ABC, abstractmethod
from typing import Optional, Callable
import asyncio

class Lifecycle(ABC):
    """统一的生命周期接口

    整合了原有的 Lifecycle 和 SmartLifecycle 功能，
    所有方法都采用异步设计。
    """

    @abstractmethod
    async def start(self) -> None:
        """异步启动方法"""
        pass

    @abstractmethod
    async def stop(self, callback: Optional[Callable] = None) -> None:
        """异步停止方法，支持回调"""
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查是否正在运行"""
        pass

    def is_auto_startup(self) -> bool:
        """是否自动启动"""
        return True

    def get_phase(self) -> int:
        """获取启动阶段（数值越小优先级越高）"""
        return 0

    async def graceful_stop(self, timeout: float = 30.0) -> bool:
        """优雅停止，支持超时控制"""
        try:
            await asyncio.wait_for(self.stop(), timeout=timeout)
            return True
        except asyncio.TimeoutError:
            return False
```

**🎯 设计优势**：

- **性能提升**：支持并发启动/停止，减少总启动时间
- **架构简化**：统一接口，消除双重生命周期管理体系
- **功能增强**：支持超时控制、优雅停止、错误恢复
- **设计简洁**：无历史包袱，直接采用最优设计

### 1.2 创建生命周期管理器

**文件**：`miniboot/bean/lifecycle_manager.py` (新建)
**工作量**：8 小时
**依赖**：1.1

**实现步骤**：

1. 创建 `LifecycleManager` 类，统一管理所有生命周期 Bean
2. 实现并发启动/停止机制，支持阶段性管理
3. 添加错误处理、超时控制、状态监控功能
4. 集成到 Bean 工厂和应用上下文

**核心功能实现**：

```python
class LifecycleManager:
    """生命周期管理器

    统一管理所有生命周期Bean，支持：
    - 阶段性并发启动/停止
    - 超时控制和错误恢复
    - 状态监控和统计
    """

    def __init__(self):
        self._lifecycle_beans: Dict[str, Lifecycle] = {}
        self._running = False
        self._lock = asyncio.Lock()
        self._stats = {
            'total_beans': 0,
            'started_beans': 0,
            'failed_beans': 0,
            'startup_time': 0.0
        }

    async def start_lifecycle_beans(self) -> None:
        """启动所有生命周期Bean"""
        start_time = time.time()

        # 按阶段分组
        phase_groups = self._group_beans_by_phase()

        # 按阶段顺序启动
        for phase in sorted(phase_groups.keys()):
            await self._start_phase_beans(phase_groups[phase])

        self._stats['startup_time'] = time.time() - start_time
        self._running = True

    async def _start_phase_beans(self, beans: List[Tuple[str, Lifecycle]]) -> None:
        """并发启动同阶段的Bean"""
        tasks = []
        for bean_name, bean in beans:
            if bean.is_auto_startup() and not bean.is_running():
                task = asyncio.create_task(
                    self._safe_start_bean(bean, bean_name),
                    name=f"start-{bean_name}"
                )
                tasks.append(task)

        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            self._process_start_results(results)

    async def stop_lifecycle_beans(self, timeout: float = 30.0) -> None:
        """停止所有生命周期Bean"""
        # 按阶段逆序分组
        phase_groups = self._group_beans_by_phase()

        # 按阶段逆序停止
        for phase in sorted(phase_groups.keys(), reverse=True):
            await self._stop_phase_beans(phase_groups[phase], timeout)

        self._running = False
```

**集成到应用上下文**：

```python
# 在 DefaultApplicationContext 中集成
class DefaultApplicationContext(ApplicationContext):
    def __init__(self):
        super().__init__()
        self._lifecycle_manager = LifecycleManager()

    async def start(self) -> None:
        # ... 其他启动逻辑
        await self._lifecycle_manager.start_lifecycle_beans()

    async def stop(self) -> None:
        await self._lifecycle_manager.stop_lifecycle_beans()
        # ... 其他停止逻辑

    async def stop(self) -> None:
        await self._async_lifecycle_manager.stop_lifecycle_beans()
        # ... 其他停止逻辑
```

### 1.3 使用现有配置属性基类

**文件**：`miniboot/autoconfigure/properties.py` (已存在)
**工作量**：0 小时 (无需新建)
**依赖**：无

**现状分析**：

1. ✅ `StarterProperties` 基类已存在，提供通用配置功能
2. ✅ 核心模块（Web、Events）使用独立配置类模式
3. ✅ Starter 模块统一继承 `StarterProperties`
4. ✅ 配置属性管理机制已经成熟完善

**设计决策**：无需创建新的 `ModuleProperties` 基类，使用现有的配置属性模式。

### 1.4 完善条件注解系统

**文件**：`miniboot/annotations/conditional.py`
**工作量**：6 小时
**依赖**：现有注解系统

**实现步骤**：

1. 增强 `@ConditionalOnProperty` 支持复杂表达式
2. 添加 `@ConditionalOnBean` 和 `@ConditionalOnMissingBean`
3. 实现 `@ConditionalOnClass` 和 `@ConditionalOnMissingClass`
4. 优化条件评估器性能

---

## 🏗️ 阶段 2：模块自动配置类创建

**目标**：为各核心模块创建 Spring Boot 风格的自动配置类
**预计时间**：1.5 周
**优先级**：🔥 最高
**依赖**：阶段 1 完成

### 2.1 创建 Web 模块自动配置

**文件**：`miniboot/autoconfigure/web_auto_configuration.py`
**工作量**：8 小时
**依赖**：1.1, 1.2

**实现步骤**：

1. 创建 `WebAutoConfiguration` 类
2. 定义 `@Bean` 方法创建 WebApplication、ControllerRegistry 等
3. 添加条件注解 `@ConditionalOnClass("fastapi.FastAPI")`
4. 实现配置属性绑定

**预期结果**：

```python
@Configuration
@ConditionalOnClass("fastapi.FastAPI")
@ConditionalOnProperty("miniboot.web.enabled", having_value="true", match_if_missing=True)
class WebAutoConfiguration:

    @Bean
    def web_properties(self) -> WebProperties:
        return WebProperties()

    @Bean
    def web_application(self, web_properties: WebProperties) -> WebApplication:
        return WebApplication(web_properties)
```

### 2.2 创建 Events 模块自动配置

**文件**：`miniboot/autoconfigure/events_auto_configuration.py`
**工作量**：6 小时
**依赖**：1.1, 1.2

**实现步骤**：

1. 创建 `EventsAutoConfiguration` 类
2. 定义 EventPublisher 的 Bean 创建方法
3. 支持事件配置属性绑定
4. 添加条件化配置支持

### 2.3 创建 Asyncs 模块自动配置

**文件**：`miniboot/autoconfigure/asyncs_auto_configuration.py`
**工作量**：6 小时
**依赖**：1.1, 1.2

**实现步骤**：

1. 创建 `AsyncsAutoConfiguration` 类
2. 定义 AsyncExecutor 和 ThreadPoolManager 的 Bean 创建
3. 支持异步执行器配置属性
4. 实现条件化异步功能启用

### 2.4 创建 Schedule 模块自动配置

**文件**：`miniboot/autoconfigure/schedule_auto_configuration.py`
**工作量**：6 小时
**依赖**：1.1, 1.2

**实现步骤**：

1. 创建 `ScheduleAutoConfiguration` 类
2. 定义 MiniBootScheduler 和 TaskManager 的 Bean 创建
3. 支持调度器配置属性
4. 实现条件化启用机制

### 2.5 创建自动配置注册机制

**文件**：`miniboot/autoconfigure/__init__.py`
**工作量**：4 小时
**依赖**：2.1-2.4

**实现步骤**：

1. 创建自动配置类注册表
2. 实现自动发现机制
3. 支持配置类的优先级排序
4. 提供配置类的条件评估

---

## 🔄 阶段 3：ApplicationContext 重构

**目标**：重构 DefaultApplicationContext，实现 Bean 驱动的模块集成
**预计时间**：1 周
**优先级**：🔥 最高
**依赖**：阶段 2 完成

### 3.1 重构 ApplicationContext 初始化

**文件**：`miniboot/context/application.py`
**工作量**：10 小时
**依赖**：2.5

**实现步骤**：

1. 移除 `_init_components()` 中的直接模块创建
2. 重构 `_execute_startup_sequence()` 方法
3. 实现基于 Bean 的模块管理流程
4. 保留核心的环境和 Bean 工厂初始化

**关键修改**：

```python
def _init_components(self) -> None:
    """初始化核心组件 - 重构版"""
    # ✅ 只初始化最核心的组件
    self._environment: Environment = None
    self._bean_factory: BeanFactory = None
    # ❌ 移除直接创建的模块组件
    # self._event_publisher = ApplicationEventPublisher()  # 删除
    # self._component_scanner = ComponentScanner()         # 删除
```

### 3.2 实现自动配置加载机制

**文件**：`miniboot/context/application.py`
**工作量**：8 小时
**依赖**：3.1

**实现步骤**：

1. 在启动流程中添加自动配置加载步骤
2. 实现条件评估和配置类注册
3. 支持配置类的依赖排序
4. 添加配置加载的日志和监控

### 3.3 实现统一的模块访问接口

**文件**：`miniboot/context/application.py`
**工作量**：6 小时
**依赖**：3.2

**实现步骤**：

1. 添加 `get_web_application()` 等模块访问方法
2. 通过 Bean 工厂获取模块实例
3. 提供可选的模块获取（支持模块未启用的情况）
4. 保持与现有 API 的兼容性

### 3.4 删除 ModuleInitializer

**文件**：`miniboot/context/module_initializer.py`
**工作量**：4 小时
**依赖**：3.3

**实现步骤**：

1. 分析 ModuleInitializer 的现有功能
2. 将功能迁移到对应的 AutoConfiguration 类
3. 删除 ModuleInitializer 相关代码
4. 更新相关的导入和引用

---

## 🔄 阶段 4：模块生命周期改造

**目标**：改造各模块实现统一的 Lifecycle 接口
**预计时间**：1 周
**优先级**：🔥 高
**依赖**：阶段 3 完成

### 4.1 改造 WebApplication 实现 Lifecycle

**文件**：`miniboot/web/application.py`
**工作量**：4 小时
**依赖**：1.1

**实现步骤**：

1. 让 WebApplication 继承 Lifecycle 接口
2. 重构现有的 start/stop 方法符合接口规范
3. 实现 `get_phase()` 方法定义启动优先级
4. 确保异步方法的正确实现

### 4.2 改造 MiniBootScheduler 实现 Lifecycle

**文件**：`miniboot/schedule/scheduler.py`
**工作量**：4 小时
**依赖**：1.1

**实现步骤**：

1. 让 MiniBootScheduler 继承 Lifecycle 接口
2. 适配现有的启动停止逻辑
3. 实现状态管理方法
4. 添加优雅关闭支持

### 4.3 改造 ActuatorContext 实现 Lifecycle

**文件**：`miniboot/starters/actuator/context.py`
**工作量**：4 小时
**依赖**：1.1

**实现步骤**：

1. 让 ActuatorContext 继承 Lifecycle 接口
2. 实现监控模块的生命周期管理
3. 支持端点的动态启用/禁用
4. 添加健康检查集成

### 4.4 改造 EventPublisher 实现 Lifecycle

**文件**：`miniboot/events/publisher.py`
**工作量**：3 小时
**依赖**：1.1

**实现步骤**：

1. 让 EventPublisher 继承 Lifecycle 接口
2. 实现事件发布器的生命周期管理
3. 支持优雅的事件处理关闭
4. 添加事件队列的清理机制

### 4.5 实现统一生命周期管理

**文件**：`miniboot/context/application.py`
**工作量**：6 小时
**依赖**：4.1-4.4

**实现步骤**：

1. 实现 `LifecycleProcessor` 类
2. 在 ApplicationContext 中集成生命周期管理
3. 支持按阶段（phase）排序启动
4. 实现优雅关闭和错误处理

---

## ✅ 阶段 5：测试和优化

**目标**：全面测试重构后的架构，确保稳定性和性能
**预计时间**：1 周
**优先级**：🔥 高
**依赖**：阶段 4 完成

### 5.1 单元测试编写

**文件**：`tests/test_autoconfigure/`, `tests/test_lifecycle/`
**工作量**：12 小时
**依赖**：所有前置阶段

**实现步骤**：

1. 为所有 AutoConfiguration 类编写单元测试
2. 测试 Lifecycle 接口的实现
3. 测试条件注解的评估逻辑
4. 确保测试覆盖率达到 90% 以上

### 5.2 集成测试编写

**文件**：`tests/integration/test_module_integration.py`
**工作量**：8 小时
**依赖**：5.1

**实现步骤**：

1. 测试完整的应用启动流程
2. 验证模块间的依赖关系
3. 测试配置驱动的模块启用/禁用
4. 验证生命周期管理的正确性

### 5.3 性能基准测试

**文件**：`tests/performance/test_startup_performance.py`
**工作量**：6 小时
**依赖**：5.2

**实现步骤**：

1. 对比重构前后的启动时间
2. 测试内存使用情况
3. 验证模块创建的性能影响
4. 确保性能没有显著退化

### 5.4 文档更新

**文件**：`docs/architecture/module-integration.md`
**工作量**：6 小时
**依赖**：5.3

**实现步骤**：

1. 更新架构设计文档
2. 编写模块整合机制说明
3. 提供配置参考和最佳实践
4. 更新 API 文档

### 5.5 示例代码更新

**文件**：`examples/`
**工作量**：4 小时
**依赖**：5.4

**实现步骤**：

1. 更新现有示例代码
2. 添加新的配置示例
3. 展示模块的条件化配置
4. 提供迁移指南

---

## 📊 任务依赖关系图

```
阶段1 (基础接口)
    ↓
阶段2 (自动配置类)
    ↓
阶段3 (ApplicationContext重构)
    ↓
阶段4 (生命周期改造)
    ↓
阶段5 (测试优化)
```

## ⚠️ 风险评估与缓解

### 高风险项

- **ApplicationContext 重构**：影响面大，需要充分测试
- **模块依赖关系**：可能出现循环依赖问题

### 缓解策略

- 分阶段实施，每阶段充分验证
- 保持详细的测试覆盖
- 及时进行代码审查

## 🎯 成功标准

1. **功能完整性**：所有现有功能正常工作
2. **架构一致性**：模块管理方式统一
3. **性能稳定**：启动时间和内存使用无显著退化
4. **代码质量**：符合项目编码规范，测试覆盖率 > 90%
5. **文档完善**：架构变更有完整文档说明

---

## 📝 实施注意事项

### 代码规范要求

- **文件头格式**：所有新文件必须包含标准的文件头（shebang + encoding + @author: cz）
- **类型注解**：所有函数和方法必须有完整的类型注解
- **文档字符串**：所有公共类和方法必须有详细的文档字符串
- **中文注释**：使用中文注释时必须使用英文标点符号
- **代码质量**：所有代码必须通过 ruff 检查，无警告和错误

### 测试要求

- **单元测试**：每个新增类都必须有对应的单元测试
- **集成测试**：关键流程必须有集成测试覆盖
- **性能测试**：重构后的性能不能低于重构前
- **测试覆盖率**：新增代码的测试覆盖率必须达到 90% 以上

### 提交规范

- **提交粒度**：每个子任务对应一个或多个提交
- **提交信息**：使用规范的提交信息格式
- **代码审查**：所有代码变更都需要经过代码审查
- **文档同步**：代码变更必须同步更新相关文档

## 🔧 技术实施细节

### AutoConfiguration 实现模式

**📋 标准模板和开发指导：**

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块自动配置类 - 负责创建模块相关的所有 Bean
"""

from typing import Optional
from miniboot.annotations import Configuration, Bean, ConditionalOnClass, ConditionalOnProperty, ConditionalOnMissingBean
from miniboot.web.properties import WebProperties  # 使用具体的配置属性类
from miniboot.bean.base import Lifecycle  # 使用现有的 Lifecycle 接口

@Configuration
@ConditionalOnClass("target.module.Class")  # 检查目标类是否存在
@ConditionalOnProperty("miniboot.module.enabled", having_value="true", match_if_missing=True)
class ModuleAutoConfiguration:
    """模块自动配置类

    负责创建模块相关的所有 Bean，支持条件化配置.

    设计原则：
    1. 每个模块一个自动配置类
    2. 配置属性和实例分离创建
    3. 支持条件化配置
    4. 遵循依赖注入原则
    """

    @Bean  # 默认 scope=Scope.SINGLETON，单例模式
    def web_properties(self) -> WebProperties:
        """创建 Web 配置属性 Bean（单例）

        Returns:
            WebProperties: Web 配置属性实例（全局唯一）
        """
        properties = WebProperties()
        # 从环境中绑定配置值
        self._bind_properties_from_environment(properties)
        return properties

    @Bean  # 默认单例，Web应用在整个容器中只有一个实例
    @ConditionalOnMissingBean  # 避免重复创建
    def web_application(self, properties: WebProperties) -> 'WebApplication':
        """创建 Web 应用实例 Bean（单例）

        Args:
            properties: 自动注入的配置属性

        Returns:
            WebApplication: Web 应用实例（全局唯一）

        Note:
            Web应用作为单例是正确的设计，因为：
            1. 整个应用只需要一个Web服务器实例
            2. 生命周期状态（_running）应该是全局的
            3. 资源管理（端口占用）需要全局控制
        """
        return WebApplication(properties)

    @Bean
    @ConditionalOnBean("web_application")  # 依赖主实例
    def controller_registry(self, web_application: 'WebApplication') -> 'ControllerRegistry':
        """创建控制器注册表 Bean（可选）

        Args:
            web_application: 自动注入的 Web 应用实例

        Returns:
            ControllerRegistry: 控制器注册表实例
        """
        return ControllerRegistry(web_application)

    def _bind_properties_from_environment(self, properties: WebProperties) -> None:
        """从环境中绑定配置属性

        Args:
            properties: 要绑定的配置属性对象
        """
        # 实现配置绑定逻辑
        pass
```

**🎯 开发要点：**

1. **文件命名规范**：`{module}_auto_configuration.py`
2. **类命名规范**：`{Module}AutoConfiguration`
3. **Bean 命名规范**：使用 camelCase，如 `webApplication`、`eventPublisher`
4. **条件注解使用**：合理使用条件注解避免不必要的 Bean 创建
5. **依赖注入**：充分利用方法参数的自动注入功能

**⚠️ Bean 作用域重要说明：**

- **默认单例**：`@Bean` 注解默认创建单例 Bean（`scope=Scope.SINGLETON`）
- **框架模块适用**：Web、Events、Asyncs、Schedule 等核心模块应该是单例
- **状态管理**：单例 Bean 的状态在整个应用中共享，需要考虑线程安全
- **资源控制**：单例模式确保资源（如端口、线程池）的全局唯一性
- **原型模式**：如需每次创建新实例，使用 `@Bean(scope=Scope.PROTOTYPE)`

### Lifecycle 实现模式

**📋 标准模板和开发指导：**

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块类 - 实现统一的生命周期接口
"""

import asyncio
from typing import Optional, Callable
from loguru import logger
from miniboot.bean.base import Lifecycle  # 使用重构后的接口
from miniboot.web.properties import WebProperties

class WebApplication(Lifecycle):
    """模块类实现 Lifecycle 接口

    所有需要生命周期管理的模块都应该实现此接口.

    生命周期阶段：
    - Phase 0: 基础设施模块（Environment、BeanFactory）
    - Phase 100: 核心业务模块（EventPublisher、AsyncExecutor）
    - Phase 150: 调度模块（Scheduler）
    - Phase 200: Web 模块（WebApplication）
    """

    def __init__(self, properties: WebProperties):
        """初始化模块

        Args:
            properties: 模块配置属性
        """
        self.properties = properties
        self._running = False
        self._starting = False
        self._stopping = False
        self._start_time: Optional[float] = None

    async def start(self) -> None:
        """异步启动模块 - 实现 Lifecycle 接口

        实现幂等性：多次调用不会产生副作用
        """
        if self._running or self._starting:
            logger.debug(f"{self.__class__.__name__} already running or starting")
            return

        if not self.properties.auto_start:
            logger.info(f"{self.__class__.__name__} auto-start disabled")
            return

        self._starting = True
        start_time = asyncio.get_event_loop().time()

        try:
            logger.info(f"🚀 Starting {self.__class__.__name__}...")

            # 执行具体的异步启动逻辑
            await self._do_start()

            self._running = True
            self._start_time = start_time
            elapsed = asyncio.get_event_loop().time() - start_time

            logger.info(f"✅ {self.__class__.__name__} started in {elapsed:.3f}s")

        except Exception as e:
            logger.error(f"❌ Failed to start {self.__class__.__name__}: {e}")
            raise
        finally:
            self._starting = False

    async def stop(self, callback: Optional[Callable] = None) -> None:
        """异步停止模块 - 实现 Lifecycle 接口

        实现优雅关闭：确保资源正确释放
        """
        if not self._running or self._stopping:
            logger.debug(f"{self.__class__.__name__} not running or already stopping")
            return

        self._stopping = True
        stop_time = asyncio.get_event_loop().time()

        try:
            logger.info(f"🛑 Stopping {self.__class__.__name__}...")

            # 执行具体的异步停止逻辑
            await self._do_stop()

            self._running = False
            elapsed = asyncio.get_event_loop().time() - stop_time

            logger.info(f"✅ {self.__class__.__name__} stopped in {elapsed:.3f}s")

            # 执行回调
            if callback:
                callback()

        except Exception as e:
            logger.error(f"❌ Failed to stop {self.__class__.__name__}: {e}")
            raise
        finally:
            self._stopping = False

    def is_running(self) -> bool:
        """检查是否运行中

        Returns:
            bool: True 如果模块正在运行
        """
        return self._running

    def get_phase(self) -> int:
        """获取启动阶段优先级

        数值越小优先级越高，用于控制模块启动顺序.

        Returns:
            int: 启动阶段优先级
        """
        return 100  # 默认优先级，子类应该重写

    async def _do_start(self) -> None:
        """执行具体的异步启动逻辑

        子类必须重写此方法来定义具体的启动行为.
        """
        # 默认实现：什么都不做
        pass

    async def _do_stop(self) -> None:
        """执行具体的异步停止逻辑

        子类必须重写此方法来定义具体的停止行为.
        """
        # 默认实现：什么都不做
        pass

    def get_phase(self) -> int:
        """获取启动阶段优先级

        数值越小优先级越高，用于控制模块启动顺序.

        Returns:
            int: 启动阶段优先级
        """
        return 200  # Web 模块默认优先级

    def is_auto_startup(self) -> bool:
        """是否自动启动

        Returns:
            bool: 如果配置了自动启动则返回 True
        """
        return self.properties.auto_start

    def get_status(self) -> dict:
        """获取模块状态信息

        Returns:
            dict: 模块状态信息
        """
        return {
            "name": self.__class__.__name__,
            "running": self._running,
            "starting": self._starting,
            "stopping": self._stopping,
            "start_time": self._start_time,
            "phase": self.get_phase(),
            "auto_start": self.properties.auto_start,
            "enabled": self.properties.enabled
        }
```

**🎯 开发要点：**

1. **状态管理**：使用 `_running`、`_starting`、`_stopping` 标志避免并发问题
2. **幂等性**：确保 `start()` 和 `stop()` 方法可以安全地多次调用
3. **错误处理**：在启动/停止过程中正确处理异常
4. **日志记录**：提供详细的启动/停止日志，包含耗时信息
5. **优先级设置**：合理设置 `get_phase()` 返回值控制启动顺序

### Bean 访问模式

**📋 ApplicationContext 中的统一模块访问：**

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: ApplicationContext 模块访问接口实现
"""

from typing import Optional, Dict, Any, Type, TypeVar
from loguru import logger
from miniboot.bean.exceptions import BeanNotFoundError
from miniboot.bean.base import Lifecycle

T = TypeVar('T')

class DefaultApplicationContext(ApplicationContext):
    """重构后的应用上下文 - 统一的模块访问接口"""

    def get_web_application(self) -> Optional['WebApplication']:
        """获取 Web 应用模块

        Returns:
            Optional[WebApplication]: Web 应用实例，如果未启用则返回 None
        """
        return self._get_module_bean("webApplication", "Web application")

    def get_event_publisher(self) -> Optional['EventPublisher']:
        """获取事件发布器

        Returns:
            Optional[EventPublisher]: 事件发布器实例，如果未启用则返回 None
        """
        return self._get_module_bean("eventPublisher", "Event publisher")

    def get_async_executor(self) -> Optional['AsyncExecutor']:
        """获取异步执行器

        Returns:
            Optional[AsyncExecutor]: 异步执行器实例，如果未启用则返回 None
        """
        return self._get_module_bean("asyncExecutor", "Async executor")

    def get_scheduler(self) -> Optional['MiniBootScheduler']:
        """获取调度器

        Returns:
            Optional[MiniBootScheduler]: 调度器实例，如果未启用则返回 None
        """
        return self._get_module_bean("scheduler", "Scheduler")

    def get_module_beans(self) -> Dict[str, Any]:
        """获取所有模块 Bean

        Returns:
            Dict[str, Any]: 模块名称到实例的映射
        """
        module_beans = {}
        module_names = ["webApplication", "eventPublisher", "asyncExecutor", "scheduler"]

        for name in module_names:
            bean = self._get_module_bean(name, name, log_not_found=False)
            if bean is not None:
                module_beans[name] = bean

        return module_beans

    def get_lifecycle_beans(self) -> Dict[str, Lifecycle]:
        """获取所有生命周期 Bean

        Returns:
            Dict[str, Lifecycle]: 实现了 Lifecycle 接口的 Bean
        """
        try:
            return self._bean_factory.get_beans_of_type(Lifecycle)
        except Exception as e:
            logger.warning(f"Failed to get lifecycle beans: {e}")
            return {}

    async def start_all_lifecycle_beans(self) -> None:
        """启动所有生命周期 Bean"""
        if hasattr(self, '_lifecycle_manager'):
            await self._lifecycle_manager.start_lifecycle_beans()

    async def stop_all_lifecycle_beans(self, timeout: float = 30.0) -> None:
        """停止所有生命周期 Bean"""
        if hasattr(self, '_lifecycle_manager'):
            await self._lifecycle_manager.stop_lifecycle_beans(timeout)

    def _get_module_bean(self, bean_name: str, display_name: str, log_not_found: bool = True) -> Optional[Any]:
        """获取模块 Bean 的通用方法

        Args:
            bean_name: Bean 名称
            display_name: 显示名称（用于日志）
            log_not_found: 是否记录未找到的日志

        Returns:
            Optional[Any]: Bean 实例，如果未找到则返回 None
        """
        try:
            bean = self._bean_factory.get_bean(bean_name)
            logger.debug(f"Retrieved {display_name}: {bean}")
            return bean
        except BeanNotFoundError:
            if log_not_found:
                logger.debug(f"{display_name} not available (module may be disabled)")
            return None
        except Exception as e:
            logger.error(f"Failed to get {display_name}: {e}")
            return None

    def is_module_enabled(self, module_name: str) -> bool:
        """检查模块是否启用

        Args:
            module_name: 模块名称

        Returns:
            bool: True 如果模块已启用并创建了对应的 Bean
        """
        return self._get_module_bean(module_name, module_name, log_not_found=False) is not None

    def get_module_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模块的状态信息

        Returns:
            Dict[str, Dict[str, Any]]: 模块状态信息
        """
        status = {}
        lifecycle_beans = self.get_lifecycle_beans()

        for bean_name, bean in lifecycle_beans.items():
            if hasattr(bean, 'get_status'):
                status[bean_name] = bean.get_status()
            else:
                status[bean_name] = {
                    "name": bean.__class__.__name__,
                    "running": getattr(bean, 'is_running', lambda: False)(),
                    "type": type(bean).__name__
                }

        return status
```

**🎯 开发要点：**

1. **统一访问接口**：所有模块都通过 `get_xxx()` 方法访问
2. **可选性处理**：模块可能未启用，返回 `Optional` 类型
3. **错误处理**：优雅处理 Bean 不存在的情况
4. **日志记录**：提供适当的调试信息
5. **状态监控**：支持获取模块运行状态

### 开发流程指导

#### 步骤 1：创建模块配置属性类

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 模块配置属性
"""

from dataclasses import dataclass

@dataclass
class WebProperties:
    """Web 模块配置属性"""

    # 基础配置
    enabled: bool = True
    auto_start: bool = True

    # Web 特定配置
    host: str = "0.0.0.0"
    port: int = 8080
    debug: bool = False
    cors_enabled: bool = True

    def validate(self) -> None:
        """验证配置参数"""
        super().validate()
        if not (1 <= self.port <= 65535):
            raise ValueError(f"Invalid port: {self.port}")
```

#### 步骤 2：改造现有模块类实现 Lifecycle

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 应用模块 - 实现 Lifecycle 接口
"""

from miniboot.bean.base import Lifecycle  # 使用重构后的接口
from miniboot.web.properties import WebProperties

class WebApplication(Lifecycle):
    """Web 应用模块 - 原生异步实现"""

    def __init__(self, properties: WebProperties):
        self.properties = properties
        self._running = False
        self.app = None
        self.server = None

    def get_phase(self) -> int:
        """Web 模块在较晚阶段启动"""
        return 200

    def is_auto_startup(self) -> bool:
        """是否自动启动"""
        return self.properties.auto_start

    async def start(self) -> None:
        """异步启动 Web 服务器"""
        if self._running:
            return

        await self._do_start()
        self._running = True

    async def stop(self, callback: Optional[Callable] = None) -> None:
        """异步停止 Web 服务器"""
        if not self._running:
            return

        await self._do_stop()
        self._running = False

        if callback:
            callback()

    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self._running

    async def _do_start(self) -> None:
        """异步启动逻辑"""
        from fastapi import FastAPI
        import uvicorn

        self.app = FastAPI(debug=self.properties.debug)
        # 配置路由、中间件等
        await self._configure_application()

        # 启动服务器
        config = uvicorn.Config(
            self.app,
            host=self.properties.host,
            port=self.properties.port,
            log_level="info"
        )
        self.server = uvicorn.Server(config)
        await self.server.serve()

    async def _do_stop(self) -> None:
        """异步停止逻辑"""
        if self.server:
            self.server.should_exit = True
            await self.server.shutdown()

    async def _configure_application(self) -> None:
        """配置 FastAPI 应用"""
        # 配置路由、中间件等
        pass
```

#### 步骤 3：创建自动配置类

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 模块自动配置
"""

from miniboot.annotations import Configuration, Bean, ConditionalOnClass, ConditionalOnProperty
from miniboot.web.properties import WebProperties
from miniboot.web.application import WebApplication

@Configuration
@ConditionalOnClass("fastapi.FastAPI")
@ConditionalOnProperty("miniboot.web.enabled", having_value="true", match_if_missing=True)
class WebAutoConfiguration:
    """Web 模块自动配置"""

    @Bean
    def web_properties(self) -> WebProperties:
        """创建 Web 配置属性 Bean"""
        return WebProperties()

    @Bean
    def web_application(self, web_properties: WebProperties) -> WebApplication:
        """创建 Web 应用 Bean"""
        return WebApplication(web_properties)
```

#### 步骤 4：注册自动配置类

```python
# miniboot/autoconfigure/__init__.py
"""自动配置模块"""

from .web_auto_configuration import WebAutoConfiguration
from .events_auto_configuration import EventsAutoConfiguration
from .asyncs_auto_configuration import AsyncsAutoConfiguration
from .schedule_auto_configuration import ScheduleAutoConfiguration

# 框架核心自动配置类注册表
AUTO_CONFIGURATIONS = [
    WebAutoConfiguration,
    EventsAutoConfiguration,
    AsyncsAutoConfiguration,
    ScheduleAutoConfiguration,
]

__all__ = [
    "AUTO_CONFIGURATIONS",
    "WebAutoConfiguration",
    "EventsAutoConfiguration",
    "AsyncsAutoConfiguration",
    "ScheduleAutoConfiguration",
]
```

### 测试指导

#### 单元测试示例

```python
#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 自动配置测试
"""

import pytest
from miniboot.autoconfigure.web_auto_configuration import WebAutoConfiguration
from miniboot.web.properties import WebProperties
from miniboot.web.application import WebApplication

class TestWebAutoConfiguration:
    """Web 自动配置测试"""

    def test_web_properties_creation(self):
        """测试 Web 配置属性创建"""
        config = WebAutoConfiguration()
        properties = config.web_properties()

        assert isinstance(properties, WebProperties)
        assert properties.enabled is True
        assert properties.port == 8080

    def test_web_application_creation(self):
        """测试 Web 应用创建"""
        config = WebAutoConfiguration()
        properties = WebProperties()
        app = config.web_application(properties)

        assert isinstance(app, WebApplication)
        assert app.properties is properties

    @pytest.mark.asyncio
    async def test_lifecycle_management(self):
        """测试异步生命周期管理"""
        properties = WebProperties()
        properties.port = 8081  # 使用不同端口避免冲突

        app = WebApplication(properties)

        # 测试启动 - 原生异步调用
        assert not app.is_running()
        await app.start()
        assert app.is_running()

        # 测试停止
        await app.stop()
        assert not app.is_running()

    @pytest.mark.asyncio
    async def test_graceful_stop(self):
        """测试优雅停止功能"""
        properties = WebProperties()
        properties.port = 8082

        app = WebApplication(properties)
        await app.start()

        # 测试优雅停止
        success = await app.graceful_stop(timeout=5.0)
        assert success
        assert not app.is_running()
```

## 📋 检查清单

### 阶段完成检查

- [ ] 所有代码通过 ruff 检查
- [ ] 所有测试用例通过
- [ ] 文档已更新
- [ ] 性能测试通过
- [ ] 代码审查完成

### 最终验收标准

- [ ] 应用启动流程正常
- [ ] 所有模块功能正常
- [ ] 配置驱动机制工作正常
- [ ] 生命周期管理正确
- [ ] 性能无显著退化
- [ ] 测试覆盖率达标
- [ ] 文档完整准确

## 🚀 后续优化方向

1. **插件机制**：基于新的架构实现插件系统
2. **自动配置增强**：支持更复杂的条件化配置
3. **监控集成**：增强模块的监控和诊断能力
4. **性能优化**：进一步优化启动性能和内存使用
5. **云原生支持**：增强对容器化和微服务的支持

---

_本任务清单基于 Mini-Boot 项目的深度分析制定，旨在将框架架构提升到 Spring Boot 的专业水准。通过系统性的重构，Mini-Boot 将具备更好的可扩展性、可维护性和专业性。_
