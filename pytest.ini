[tool:pytest]
# pytest configuration for Mini-Boot Web module testing

# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出选项
addopts =
    -v
    --tb=short
    --strict-markers
    --strict-config
    --durations=10
    --color=yes
    --cov=miniboot
    --cov-report=html
    --cov-report=term-missing

# asyncio 模式
asyncio_mode = auto

# 标记定义
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    web: marks tests as web module tests
    performance: marks tests as performance tests
    smoke: marks tests as smoke tests

# 最小版本要求
minversion = 6.0

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore:websockets.legacy is deprecated:DeprecationWarning
    ignore:websockets.server.WebSocketServerProtocol is deprecated:DeprecationWarning

# 并行测试（如果安装了pytest-xdist）
# addopts = -n auto

# 超时设置（如果安装了pytest-timeout）
# timeout = 300
