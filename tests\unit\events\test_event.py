#!/usr/bin/env python
"""
* @author: cz
* @description: 事件基类系统单元测试
"""

import threading
import unittest
from datetime import datetime
from unittest.mock import Mock, patch

from miniboot.events.base import (ApplicationEvent, ApplicationFailedEvent,
                                  ApplicationReadyEvent,
                                  ApplicationStartedEvent,
                                  ApplicationStoppedEvent, BeanCreatedEvent,
                                  BeanDestroyedEvent, BeanInitializedEvent,
                                  Event, EventIdGenerator)


class ConcreteEvent(Event):
    """具体事件类，用于测试抽象基类"""

    def __init__(self, source=None, _custom_data=None):
        super().__init__(source)

    def get_event_name(self) -> str:
        """获取事件名称"""
        return "ConcreteEvent"


class TestEventIdGenerator(unittest.TestCase):
    """事件ID生成器测试"""

    def setUp(self):
        """设置测试"""
        self.generator = EventIdGenerator()

    def test_id_generation_basic(self):
        """测试基本ID生成"""
        id1 = self.generator.generate_id()
        id2 = self.generator.generate_id()

        # ID应该不同
        self.assertNotEqual(id1, id2)

        # ID应该是字符串
        self.assertIsInstance(id1, str)
        self.assertIsInstance(id2, str)

        # ID长度应该一致（时间戳14位 + 序列号6位）
        self.assertEqual(len(id1), 20)
        self.assertEqual(len(id2), 20)

    def test_id_sequential_increment(self):
        """测试ID序列递增"""
        ids = []
        for _ in range(10):
            ids.append(self.generator.generate_id())

        # 所有ID应该不同
        self.assertEqual(len(set(ids)), 10)

        # 在同一秒内，序列号应该递增
        for i in range(1, len(ids)):
            # 提取序列号部分（最后6位）
            seq1 = int(ids[i - 1][-6:])
            seq2 = int(ids[i][-6:])

            # 如果时间戳相同，序列号应该递增
            if ids[i - 1][:14] == ids[i][:14]:
                self.assertEqual(seq2, seq1 + 1)

    def test_id_format(self):
        """测试ID格式"""
        event_id = self.generator.generate_id()

        # ID应该是20位数字字符串
        self.assertTrue(event_id.isdigit())
        self.assertEqual(len(event_id), 20)

        # 前14位应该是时间戳格式
        timestamp_part = event_id[:14]
        self.assertTrue(timestamp_part.isdigit())

        # 后6位应该是序列号
        sequence_part = event_id[14:]
        self.assertTrue(sequence_part.isdigit())
        self.assertEqual(len(sequence_part), 6)

    def test_thread_safety(self):
        """测试线程安全性"""
        generated_ids = []
        threads = []
        lock = threading.Lock()

        def generate_ids():
            """线程函数：生成ID"""
            local_ids = []
            for _ in range(100):
                local_ids.append(self.generator.generate_id())

            with lock:
                generated_ids.extend(local_ids)

        # 创建10个线程，每个生成100个ID
        for _ in range(10):
            thread = threading.Thread(target=generate_ids)
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证所有ID都是唯一的
        self.assertEqual(len(generated_ids), 1000)
        self.assertEqual(len(set(generated_ids)), 1000)

    def test_reset_functionality(self):
        """测试重置功能"""
        # 生成一些ID
        id1 = self.generator.generate_id()
        id2 = self.generator.generate_id()

        # 重置生成器
        self.generator.reset()

        # 重置后生成的ID应该重新开始
        id3 = self.generator.generate_id()

        # 验证重置后的行为
        self.assertNotEqual(id1, id3)
        self.assertNotEqual(id2, id3)

    @patch("time.time")
    def test_timestamp_change_resets_sequence(self, mock_time):
        """测试时间戳变化时序列号重置"""
        # 模拟第一个时间戳
        mock_time.return_value = 1640995200  # 2022-01-01 00:00:00

        id1 = self.generator.generate_id()
        id2 = self.generator.generate_id()

        # 验证序列号递增
        self.assertEqual(id1[-6:], "000001")
        self.assertEqual(id2[-6:], "000002")

        # 模拟时间戳变化
        mock_time.return_value = 1640995201  # 2022-01-01 00:00:01

        id3 = self.generator.generate_id()

        # 新时间戳下序列号应该重置
        self.assertEqual(id3[-6:], "000001")

        # 时间戳部分应该不同
        self.assertNotEqual(id1[:14], id3[:14])

    def test_generator_singleton(self):
        """测试生成器单例"""
        generator1 = EventIdGenerator()
        generator2 = EventIdGenerator()

        # 应该是同一个实例
        self.assertIs(generator1, generator2)

        # 生成的ID应该是连续的
        id1 = generator1.generate_id()
        id2 = generator2.generate_id()

        # 如果在同一秒内，序列号应该连续
        if id1[:6] == id2[:6]:  # 比较时间戳前缀
            seq1 = int(id1[-6:])
            seq2 = int(id2[-6:])
            self.assertEqual(seq2, seq1 + 1)


class TestEvent(unittest.TestCase):
    """Event基类测试"""

    def test_event_initialization(self):
        """测试事件初始化"""
        source = Mock()
        event = ConcreteEvent(source)

        # 验证基本属性
        self.assertIsNotNone(event.event_id)
        self.assertEqual(event.source, source)
        self.assertIsInstance(event.timestamp, datetime)
        self.assertFalse(event.processed)
        self.assertEqual(event.event_type, "ConcreteEvent")

    def test_event_without_source(self):
        """测试无源事件初始化"""
        event = ConcreteEvent()

        self.assertIsNotNone(event.event_id)
        self.assertIsNone(event.source)
        self.assertIsInstance(event.timestamp, datetime)
        self.assertFalse(event.processed)

    def test_event_id_uniqueness(self):
        """测试事件ID唯一性"""
        event1 = ConcreteEvent()
        event2 = ConcreteEvent()

        self.assertNotEqual(event1.event_id, event2.event_id)
        self.assertTrue(len(event1.event_id) > 0)
        self.assertTrue(len(event2.event_id) > 0)

        # 验证ID格式（应该是20位数字）
        self.assertTrue(event1.event_id.isdigit())
        self.assertTrue(event2.event_id.isdigit())
        self.assertEqual(len(event1.event_id), 20)
        self.assertEqual(len(event2.event_id), 20)

    def test_event_properties(self):
        """测试事件属性访问"""
        source = "test_source"
        event = ConcreteEvent(source)

        # 测试属性访问
        self.assertEqual(event.get_source(), source)
        self.assertIsInstance(event.get_timestamp(), datetime)
        self.assertFalse(event.is_processed())

    def test_event_processing_state(self):
        """测试事件处理状态管理"""
        event = ConcreteEvent()

        # 初始状态
        self.assertFalse(event.is_processed())
        self.assertFalse(event.processed)

        # 标记为已处理
        event.mark_processed()
        self.assertTrue(event.is_processed())
        self.assertTrue(event.processed)

    def test_event_string_representation(self):
        """测试事件字符串表示"""
        source = "test_source"
        event = ConcreteEvent(source)

        str_repr = str(event)
        self.assertIn("ConcreteEvent", str_repr)
        self.assertIn(event.event_id[:8], str_repr)
        self.assertIn("test_source", str_repr)
        self.assertIn("processed=False", str_repr)

        repr_str = repr(event)
        self.assertIn("ConcreteEvent", repr_str)
        self.assertIn(event.event_id, repr_str)


class TestApplicationEvent(unittest.TestCase):
    """ApplicationEvent应用事件测试"""

    def test_application_event_initialization(self):
        """测试应用事件初始化"""
        source = Mock()
        data = {"key1": "value1", "key2": 42}
        event = ApplicationEvent(source, data)

        # 验证基本属性
        self.assertEqual(event.source, source)
        self.assertEqual(event.data, data)
        self.assertIsNot(event.data, data)  # 应该是副本

    def test_application_event_without_data(self):
        """测试无数据的应用事件"""
        event = ApplicationEvent()

        self.assertIsNone(event.source)
        self.assertEqual(event.data, {})
        self.assertIsInstance(event.data, dict)

    def test_data_operations(self):
        """测试数据操作方法"""
        event = ApplicationEvent()

        # 测试设置和获取数据
        event.set_data("name", "Alice")
        event.set_data("age", 30)

        self.assertEqual(event.get_data("name"), "Alice")
        self.assertEqual(event.get_data("age"), 30)
        self.assertEqual(event.get_data("nonexistent"), None)
        self.assertEqual(event.get_data("nonexistent", "default"), "default")

    def test_data_existence_check(self):
        """测试数据存在性检查"""
        event = ApplicationEvent()

        self.assertFalse(event.has_data("name"))

        event.set_data("name", "Bob")
        self.assertTrue(event.has_data("name"))

    def test_data_removal(self):
        """测试数据移除"""
        event = ApplicationEvent()
        event.set_data("temp", "value")

        # 移除存在的数据
        removed_value = event.remove_data("temp")
        self.assertEqual(removed_value, "value")
        self.assertFalse(event.has_data("temp"))

        # 移除不存在的数据
        removed_value = event.remove_data("nonexistent")
        self.assertIsNone(removed_value)

    def test_data_clear(self):
        """测试数据清空"""
        event = ApplicationEvent()
        event.set_data("key1", "value1")
        event.set_data("key2", "value2")

        self.assertEqual(len(event.data), 2)

        event.clear_data()
        self.assertEqual(len(event.data), 0)
        self.assertEqual(event.data, {})

    def test_data_update(self):
        """测试批量数据更新"""
        event = ApplicationEvent()
        event.set_data("existing", "old_value")

        update_data = {"existing": "new_value", "new_key": "new_value"}

        event.update_data(update_data)

        self.assertEqual(event.get_data("existing"), "new_value")
        self.assertEqual(event.get_data("new_key"), "new_value")

    def test_application_event_string_representation(self):
        """测试应用事件字符串表示"""
        event = ApplicationEvent()
        event.set_data("key1", "value1")
        event.set_data("key2", "value2")

        str_repr = str(event)
        self.assertIn("ApplicationEvent", str_repr)
        self.assertIn("data_keys=['key1', 'key2']", str_repr)


class TestBuiltinEvents(unittest.TestCase):
    """内置事件类型测试"""

    def test_application_started_event(self):
        """测试应用启动事件"""
        app = Mock()
        startup_time = 2.5

        event = ApplicationStartedEvent(app, startup_time)

        self.assertEqual(event.source, app)
        self.assertEqual(event.get_data("startup_time"), startup_time)
        self.assertEqual(event.event_type, "ApplicationStartedEvent")

    def test_application_started_event_without_time(self):
        """测试无启动时间的应用启动事件"""
        app = Mock()
        event = ApplicationStartedEvent(app)

        self.assertEqual(event.source, app)
        self.assertIsNone(event.get_data("startup_time"))

    def test_application_stopped_event(self):
        """测试应用停止事件"""
        app = Mock()
        reason = "User requested shutdown"

        event = ApplicationStoppedEvent(app, reason)

        self.assertEqual(event.source, app)
        self.assertEqual(event.get_data("shutdown_reason"), reason)
        self.assertEqual(event.event_type, "ApplicationStoppedEvent")

    def test_application_ready_event(self):
        """测试应用就绪事件"""
        app = Mock()
        event = ApplicationReadyEvent(app)

        self.assertEqual(event.source, app)
        self.assertEqual(event.event_type, "ApplicationReadyEvent")

    def test_application_failed_event(self):
        """测试应用失败事件"""
        app = Mock()
        exception = ValueError("Test error")

        event = ApplicationFailedEvent(app, exception)

        self.assertEqual(event.source, app)
        self.assertEqual(event.get_data("exception_type"), "ValueError")
        self.assertEqual(event.get_data("exception_message"), "Test error")
        self.assertEqual(event.get_data("exception"), exception)

    def test_bean_created_event(self):
        """测试Bean创建事件"""
        bean_instance = Mock()
        bean_name = "testBean"
        bean_type = Mock

        event = BeanCreatedEvent(bean_instance, bean_name, bean_type)

        self.assertEqual(event.source, bean_instance)
        self.assertEqual(event.get_data("bean_name"), bean_name)
        self.assertEqual(event.get_data("bean_type"), "Mock")
        self.assertEqual(event.get_data("bean_class"), bean_type)

    def test_bean_destroyed_event(self):
        """测试Bean销毁事件"""
        bean_instance = Mock()
        bean_name = "testBean"
        bean_type = Mock

        event = BeanDestroyedEvent(bean_instance, bean_name, bean_type)

        self.assertEqual(event.source, bean_instance)
        self.assertEqual(event.get_data("bean_name"), bean_name)
        self.assertEqual(event.get_data("bean_type"), "Mock")
        self.assertEqual(event.get_data("bean_class"), bean_type)

    def test_bean_initialized_event(self):
        """测试Bean初始化完成事件"""
        bean_instance = Mock()
        bean_name = "testBean"
        bean_type = Mock

        event = BeanInitializedEvent(bean_instance, bean_name, bean_type)

        self.assertEqual(event.source, bean_instance)
        self.assertEqual(event.get_data("bean_name"), bean_name)
        self.assertEqual(event.get_data("bean_type"), "Mock")
        self.assertEqual(event.get_data("bean_class"), bean_type)


if __name__ == "__main__":
    unittest.main()
