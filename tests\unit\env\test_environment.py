#!/usr/bin/env python
"""
* @author: cz
* @description: Environment类单元测试
"""

import unittest

from miniboot.env.environment import StandardEnvironment
from miniboot.env.sources import MapPropertySource, MutablePropertySources


class StandardEnvironmentTestCase(unittest.TestCase):
    """StandardEnvironment测试用例"""

    def setUp(self):
        """测试前准备"""
        self.environment = StandardEnvironment()

    def test_initialization(self):
        """测试环境初始化"""
        self.assertIsInstance(self.environment.get_property_sources(), MutablePropertySources)
        # 注意：实际的环境可能已经有默认的配置文件设置
        active_profiles = self.environment.get_active_profiles()
        self.assertIsInstance(active_profiles, list)
        default_profiles = self.environment.get_default_profiles()
        self.assertIsInstance(default_profiles, list)

    def test_get_property_from_sources(self):
        """测试从属性源获取属性"""
        # 添加测试属性源
        source = MapPropertySource("test", {"app.name": "test-app", "app.version": "1.0.0", "app.port": "8080"})
        self.environment.get_property_sources().add_last(source)

        self.assertEqual(self.environment.get_property("app.name"), "test-app")
        self.assertEqual(self.environment.get_property("app.version"), "1.0.0")
        self.assertEqual(self.environment.get_property("app.port"), "8080")

    def test_get_property_with_default(self):
        """测试获取属性时使用默认值"""
        self.assertEqual(self.environment.get_property("non.existent", "default"), "default")
        self.assertIsNone(self.environment.get_property("non.existent"))

    def test_get_property_as_with_default(self):
        """测试获取属性并转换类型，使用默认值"""
        result = self.environment.get_property_as("non.existent", int, 9000)
        self.assertEqual(result, 9000)

    def test_contains_property(self):
        """测试检查属性是否存在"""
        source = MapPropertySource("test", {"app.name": "test-app"})
        self.environment.get_property_sources().add_last(source)

        self.assertTrue(self.environment.contains_property("app.name"))
        self.assertFalse(self.environment.contains_property("non.existent"))

    def test_resolve_placeholders_with_default(self):
        """测试带默认值的占位符解析"""
        result = self.environment.resolve_placeholders("${non.existent:default-value}")
        self.assertEqual(result, "default-value")

    def test_accepts_profiles_basic(self):
        """测试基本配置文件匹配"""
        # 获取当前活跃的配置文件
        active_profiles = self.environment.get_active_profiles()
        if active_profiles:
            # 如果有活跃配置文件，测试第一个
            self.assertTrue(self.environment.accepts_profiles(active_profiles[0]))
        else:
            # 如果没有活跃配置文件，测试默认配置文件
            default_profiles = self.environment.get_default_profiles()
            if default_profiles:
                self.assertTrue(self.environment.accepts_profiles(default_profiles[0]))

    def test_property_sources_access(self):
        """测试属性源访问"""
        # 添加多个属性源
        source1 = MapPropertySource("source1", {"app.name": "app1"})
        source2 = MapPropertySource("source2", {"app.name": "app2"})

        self.environment.get_property_sources().add_last(source1)
        self.environment.get_property_sources().add_first(source2)

        # source2 应该有更高优先级
        self.assertEqual(self.environment.get_property("app.name"), "app2")


if __name__ == "__main__":
    unittest.main()
