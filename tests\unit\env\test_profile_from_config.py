#!/usr/bin/env python
"""
* @author: cz
* @description: 从配置文件中读取 Profile 并自动加载对应配置的测试
"""

import tempfile
import unittest
from pathlib import Path

from miniboot.env.environment import StandardEnvironment


class ProfileFromConfigTestCase(unittest.TestCase):
    """从配置文件中读取 Profile 并自动加载对应配置的测试"""

    def setUp(self):
        self.temp_dir = Path(tempfile.mkdtemp())
        self.resources_dir = self.temp_dir / "resources"
        self.resources_dir.mkdir(exist_ok=True)

        # 保存原始工作目录
        import os

        self.original_cwd = Path.cwd()
        os.chdir(self.temp_dir)

    def tearDown(self):
        # 恢复原始工作目录
        import os

        os.chdir(self.original_cwd)

        # 清理临时文件
        for file in self.temp_dir.rglob("*"):
            if file.is_file():
                file.unlink()
        for dir in reversed(list(self.temp_dir.rglob("*"))):
            if dir.is_dir():
                dir.rmdir()
        self.temp_dir.rmdir()

    def test_profile_from_application_yml(self):
        """测试从 application.yml 中读取 profile 配置"""
        # 创建 application.yml,其中配置了 profile
        default_config = """
miniboot:
  profiles:
    active: test

app:
  name: default-app
  environment: default
  debug: false
"""
        (self.resources_dir / "application.yml").write_text(default_config, encoding="utf-8")

        # 创建 application-test.yml
        test_config = """
app:
  environment: testing
  debug: true

server:
  port: 8082

test:
  enabled: true
"""
        (self.resources_dir / "application-test.yml").write_text(test_config, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证 Profile 被正确激活
        self.assertIn("test", env.get_active_profiles())

        # 验证默认配置被加载
        self.assertEqual("default-app", env.get_property("app.name"))

        # 验证 test profile 配置覆盖了默认配置
        self.assertEqual("testing", env.get_property("app.environment"))  # 来自 test 配置
        self.assertEqual(True, env.get_property("app.debug"))  # 来自 test 配置
        self.assertEqual(8082, env.get_property("server.port"))  # 来自 test 配置
        self.assertEqual(True, env.get_property("test.enabled"))  # 来自 test 配置

    def test_multiple_profiles_from_config(self):
        """测试从配置文件中读取多个 profile"""
        # 创建 application.yml,配置多个 profile
        default_config = """
miniboot:
  profiles:
    active: dev,test

app:
  name: multi-profile-app
  version: 1.0.0
"""
        (self.resources_dir / "application.yml").write_text(default_config, encoding="utf-8")

        # 创建 application-dev.yml
        dev_config = """
app:
  environment: development

database:
  host: dev-db.example.com
  port: 5432
"""
        (self.resources_dir / "application-dev.yml").write_text(dev_config, encoding="utf-8")

        # 创建 application-test.yml
        test_config = """
app:
  debug: true

test:
  enabled: true
  timeout: 30
"""
        (self.resources_dir / "application-test.yml").write_text(test_config, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证多个 Profile 被正确激活
        active_profiles = env.get_active_profiles()
        self.assertIn("dev", active_profiles)
        self.assertIn("test", active_profiles)

        # 验证来自不同 profile 的配置都被加载
        self.assertEqual("multi-profile-app", env.get_property("app.name"))  # 默认配置
        self.assertEqual("1.0.0", env.get_property("app.version"))  # 默认配置
        self.assertEqual("development", env.get_property("app.environment"))  # dev 配置
        self.assertEqual(True, env.get_property("app.debug"))  # test 配置
        self.assertEqual("dev-db.example.com", env.get_property("database.host"))  # dev 配置
        self.assertEqual(5432, env.get_property("database.port"))  # dev 配置
        self.assertEqual(True, env.get_property("test.enabled"))  # test 配置
        self.assertEqual(30, env.get_property("test.timeout"))  # test 配置

    def test_profile_priority_over_default(self):
        """测试 profile 配置优先级高于默认配置"""
        # 创建 application.yml
        default_config = """
miniboot:
  profiles:
    active: prod

app:
  name: default-app
  environment: default
  debug: true
  port: 8080

database:
  host: localhost
  port: 3306
  username: root
"""
        (self.resources_dir / "application.yml").write_text(default_config, encoding="utf-8")

        # 创建 application-prod.yml,覆盖部分默认配置
        prod_config = """
app:
  environment: production
  debug: false
  port: 80

database:
  host: prod-db.example.com
  port: 5432
  username: prod-user
"""
        (self.resources_dir / "application-prod.yml").write_text(prod_config, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证 Profile 被正确激活
        self.assertIn("prod", env.get_active_profiles())

        # 验证 prod 配置覆盖了默认配置
        self.assertEqual("default-app", env.get_property("app.name"))  # 未被覆盖,使用默认值
        self.assertEqual("production", env.get_property("app.environment"))  # 被 prod 覆盖
        self.assertEqual(False, env.get_property("app.debug"))  # 被 prod 覆盖
        self.assertEqual(80, env.get_property("app.port"))  # 被 prod 覆盖
        self.assertEqual("prod-db.example.com", env.get_property("database.host"))  # 被 prod 覆盖
        self.assertEqual(5432, env.get_property("database.port"))  # 被 prod 覆盖
        self.assertEqual("prod-user", env.get_property("database.username"))  # 被 prod 覆盖

    def test_env_var_profile_overrides_config_profile(self):
        """测试环境变量中的 profile 优先级高于配置文件中的 profile"""
        # 创建 application.yml,配置 profile 为 test
        default_config = """
miniboot:
  profiles:
    active: test

app:
  name: config-profile-app
"""
        (self.resources_dir / "application.yml").write_text(default_config, encoding="utf-8")

        # 创建 application-test.yml
        test_config = """
app:
  environment: testing
"""
        (self.resources_dir / "application-test.yml").write_text(test_config, encoding="utf-8")

        # 创建 application-dev.yml
        dev_config = """
app:
  environment: development
"""
        (self.resources_dir / "application-dev.yml").write_text(dev_config, encoding="utf-8")

        # 通过环境变量设置不同的 profile
        import os

        os.environ["MINIBOOT_PROFILES_ACTIVE"] = "dev"

        try:
            # 创建环境实例
            env = StandardEnvironment()

            # 验证环境变量中的 profile 优先级更高
            active_profiles = env.get_active_profiles()
            self.assertIn("dev", active_profiles)
            # test profile 可能也会被激活,因为配置文件中也设置了

            # 验证使用了 dev 配置
            self.assertEqual("config-profile-app", env.get_property("app.name"))
            # 环境变量的 profile 应该有更高优先级
            if "dev" in active_profiles and "test" not in active_profiles:
                self.assertEqual("development", env.get_property("app.environment"))

        finally:
            # 清理环境变量
            if "MINIBOOT_PROFILES_ACTIVE" in os.environ:
                del os.environ["MINIBOOT_PROFILES_ACTIVE"]

    def test_no_profile_in_config(self):
        """测试配置文件中没有 profile 配置的情况"""
        # 创建 application.yml,不包含 profile 配置
        default_config = """
app:
  name: no-profile-app
  environment: default
  debug: false
"""
        (self.resources_dir / "application.yml").write_text(default_config, encoding="utf-8")

        # 创建 application-dev.yml(不会被加载)
        dev_config = """
app:
  environment: development
  debug: true
"""
        (self.resources_dir / "application-dev.yml").write_text(dev_config, encoding="utf-8")

        # 创建环境实例
        env = StandardEnvironment()

        # 验证没有激活额外的 profile
        active_profiles = env.get_active_profiles()
        self.assertEqual(0, len(active_profiles))  # 应该为空

        # 验证只加载了默认配置
        self.assertEqual("no-profile-app", env.get_property("app.name"))
        self.assertEqual("default", env.get_property("app.environment"))
        self.assertEqual(False, env.get_property("app.debug"))

    def test_profile_specific_config_not_found(self):
        """测试 profile 特定配置文件不存在的情况"""
        # 创建 application.yml,配置一个不存在的 profile
        default_config = """
miniboot:
  profiles:
    active: nonexistent

app:
  name: missing-profile-app
  environment: default
"""
        (self.resources_dir / "application.yml").write_text(default_config, encoding="utf-8")

        # 不创建 application-nonexistent.yml

        # 创建环境实例
        env = StandardEnvironment()

        # 验证 profile 被激活但配置文件不存在不会报错
        self.assertIn("nonexistent", env.get_active_profiles())

        # 验证只使用了默认配置
        self.assertEqual("missing-profile-app", env.get_property("app.name"))
        self.assertEqual("default", env.get_property("app.environment"))


if __name__ == "__main__":
    unittest.main()
