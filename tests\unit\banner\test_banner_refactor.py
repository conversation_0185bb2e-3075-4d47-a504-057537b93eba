#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Banner模块重构后的功能测试
"""

import unittest
from io import StringIO
from unittest.mock import Mock

from miniboot.banner import Banner, BannerConfig, BannerMode
from miniboot.banner.resource import DefaultBannerResource, BannerResourceLoader
from miniboot.banner.printer import ConsoleBannerPrinter, FileBannerPrinter, LogBannerPrinter


class TestBannerRefactor(unittest.TestCase):
    """Banner模块重构功能测试"""
    
    def test_banner_simplified_methods(self):
        """测试Banner类简化后的方法"""
        banner = Banner("Test App", "1.0.0")
        
        # 测试from_env方法
        mock_env = Mock()
        mock_env.get_property.return_value = True
        
        result = banner.from_env(mock_env)
        self.assertIs(result, banner)  # 支持链式调用
        
        # 测试resource方法
        resource = DefaultBannerResource()
        result = banner.resource(resource)
        self.assertIs(result, banner)  # 支持链式调用
        
        # 测试load方法
        result = banner.load("banner.txt")
        self.assertIs(result, banner)  # 支持链式调用
    
    def test_banner_config_simplified_methods(self):
        """测试BannerConfig类简化后的方法"""
        config = BannerConfig(enabled=True, mode=BannerMode.CONSOLE)
        
        # 测试is_enabled方法
        self.assertTrue(config.is_enabled())
        
        # 测试console_enabled方法
        self.assertTrue(config.console_enabled())
        
        # 测试log_enabled方法
        config.mode = BannerMode.LOG
        self.assertTrue(config.log_enabled())
        
        # 测试from_env方法
        mock_env = Mock()
        mock_env.get_property.return_value = True
        
        config = BannerConfig.from_env(mock_env)
        self.assertIsInstance(config, BannerConfig)
    
    def test_banner_resource_simplified_methods(self):
        """测试BannerResource类简化后的方法"""
        resource = DefaultBannerResource("Test App", "1.0.0")
        
        # 测试content方法
        content = resource.content()
        self.assertIsInstance(content, str)
        self.assertIn("Test App", content)
        
        # 测试variables方法
        variables = resource.variables()
        self.assertIsInstance(variables, dict)
        self.assertIn("application.name", variables)
        self.assertEqual(variables["application.name"], "Test App")
    
    def test_banner_resource_loader_simplified_methods(self):
        """测试BannerResourceLoader类简化后的方法"""
        loader = BannerResourceLoader()
        
        # 测试locations方法
        locations = loader.locations()
        self.assertIsInstance(locations, list)
        self.assertIn("banner.txt", locations)
        
        # 测试find方法
        resource = loader.find("Test App", "1.0.0")
        self.assertIsNotNone(resource)
        
        # 测试load方法
        resource = loader.load("banner.txt", "UTF-8", "Test App", "1.0.0")
        self.assertIsNotNone(resource)
    
    def test_banner_printer_simplified_methods(self):
        """测试BannerPrinter类简化后的方法"""
        output = StringIO()
        printer = ConsoleBannerPrinter(output)
        
        resource = DefaultBannerResource("Test App", "1.0.0")
        config = BannerConfig(enabled=True, mode=BannerMode.CONSOLE)
        
        # 测试print方法
        printer.print(resource, config)
        
        output_content = output.getvalue()
        self.assertIn("Test App", output_content)
    
    def test_file_banner_printer_simplified_methods(self):
        """测试FileBannerPrinter类简化后的方法"""
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            temp_file = f.name
        
        try:
            printer = FileBannerPrinter(temp_file)
            resource = DefaultBannerResource("Test App", "1.0.0")
            config = BannerConfig(enabled=True, mode=BannerMode.CONSOLE)
            
            # 测试print方法
            printer.print(resource, config)
            
            # 验证文件内容
            with open(temp_file, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn("Test App", content)
        
        finally:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    def test_log_banner_printer_simplified_methods(self):
        """测试LogBannerPrinter类简化后的方法"""
        mock_logger = Mock()
        printer = LogBannerPrinter(mock_logger)
        
        resource = DefaultBannerResource("Test App", "1.0.0")
        config = BannerConfig(enabled=True, mode=BannerMode.LOG)
        
        # 测试print方法
        printer.print(resource, config)
        
        # 验证logger被调用
        self.assertTrue(mock_logger.info.called)
    
    def test_banner_chain_calls(self):
        """测试Banner链式调用"""
        output = StringIO()
        
        # 创建配置
        config = BannerConfig(
            enabled=True,
            mode=BannerMode.CONSOLE,
            show_version=True
        )
        
        # 测试完整的链式调用
        banner = Banner("Chain Test", "2.0.0")
        banner.configure(config).load("banner.txt").show(ConsoleBannerPrinter(output))
        
        output_content = output.getvalue()
        self.assertIn("Chain Test", output_content)
    
    def test_static_method_simplified(self):
        """测试静态方法简化"""
        mock_env = Mock()
        mock_env.get_property.return_value = True
        
        # 测试show_from_env方法
        output = StringIO()
        
        # 由于show_from_env会直接输出到stdout，我们需要重定向
        import sys
        old_stdout = sys.stdout
        sys.stdout = output
        
        try:
            Banner.show_from_env(mock_env, "Static Test", "3.0.0")
            output_content = output.getvalue()
            # 由于环境配置可能禁用banner，我们只检查方法是否正常执行
            self.assertIsInstance(output_content, str)
        finally:
            sys.stdout = old_stdout
    
    def test_error_handling_after_refactor(self):
        """测试重构后的错误处理"""
        banner = Banner("Error Test", "1.0.0")
        
        # 测试无效配置
        config = BannerConfig(enabled=False)
        banner.configure(config)
        
        # 应该不会抛出异常，只是不显示
        banner.show()
        
        # 测试无效资源路径
        banner.load("nonexistent.txt")
        
        # 应该回退到默认资源，不抛出异常
        banner.show()
    
    def test_performance_after_refactor(self):
        """测试重构后的性能"""
        import time
        
        banner = Banner("Performance Test", "1.0.0")
        config = BannerConfig(enabled=True, mode=BannerMode.CONSOLE)
        
        start_time = time.time()
        
        # 执行多次操作
        for _ in range(100):
            banner.configure(config).load("banner.txt")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 应该在合理时间内完成
        self.assertLess(execution_time, 1.0)  # 100次操作应该在1秒内完成


if __name__ == '__main__':
    unittest.main()
