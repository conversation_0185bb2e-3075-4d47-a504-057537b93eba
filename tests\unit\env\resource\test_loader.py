#!/usr/bin/env python
"""
* @author: cz
* @description: 资源加载器测试
"""

import tempfile
import unittest
from pathlib import Path

from miniboot.env.resource.loader import DefaultResourceLoader, ResourceNotFoundError


class DefaultResourceLoaderTestCase(unittest.TestCase):
    """默认资源加载器测试"""

    def setUp(self):
        self.loader = DefaultResourceLoader()
        self.temp_dir = Path(tempfile.mkdtemp())

    def tearDown(self):
        # 清理临时文件
        for file in self.temp_dir.rglob("*"):
            if file.is_file():
                file.unlink()
        self.temp_dir.rmdir()

    def test_exists_file_exists(self):
        """测试检查存在的文件"""
        # 创建临时文件
        test_file = self.temp_dir / "test.txt"
        test_file.write_text("test content", encoding="utf-8")

        self.assertTrue(self.loader.exists(test_file))
        self.assertTrue(self.loader.exists(str(test_file)))

    def test_exists_file_not_exists(self):
        """测试检查不存在的文件"""
        non_existent_file = self.temp_dir / "non_existent.txt"
        self.assertFalse(self.loader.exists(non_existent_file))

    def test_exists_directory(self):
        """测试检查目录(应该返回False)"""
        self.assertFalse(self.loader.exists(self.temp_dir))

    def test_load_existing_file(self):
        """测试加载存在的文件"""
        # 创建临时文件
        test_content = "Hello, World!\nThis is a test file."
        test_file = self.temp_dir / "test.txt"
        test_file.write_text(test_content, encoding="utf-8")

        # 加载文件
        loaded_content = self.loader.load(test_file)
        self.assertEqual(test_content, loaded_content)

    def test_load_non_existent_file(self):
        """测试加载不存在的文件"""
        non_existent_file = self.temp_dir / "non_existent.txt"

        with self.assertRaises(ResourceNotFoundError) as context:
            self.loader.load(non_existent_file)

        self.assertEqual(non_existent_file, context.exception.location)

    def test_load_utf8_file(self):
        """测试加载UTF-8编码的文件"""
        # 创建包含中文的文件
        test_content = "你好,世界!\nThis is a UTF-8 test file."
        test_file = self.temp_dir / "utf8_test.txt"
        test_file.write_text(test_content, encoding="utf-8")

        # 加载文件
        loaded_content = self.loader.load(test_file)
        self.assertEqual(test_content, loaded_content)

    def test_load_empty_file(self):
        """测试加载空文件"""
        # 创建空文件
        test_file = self.temp_dir / "empty.txt"
        test_file.touch()

        # 加载文件
        loaded_content = self.loader.load(test_file)
        self.assertEqual("", loaded_content)

    def test_load_with_path_object(self):
        """测试使用Path对象加载文件"""
        test_content = "Path object test"
        test_file = self.temp_dir / "path_test.txt"
        test_file.write_text(test_content, encoding="utf-8")

        # 使用Path对象加载
        loaded_content = self.loader.load(test_file)
        self.assertEqual(test_content, loaded_content)

    def test_load_with_string_path(self):
        """测试使用字符串路径加载文件"""
        test_content = "String path test"
        test_file = self.temp_dir / "string_test.txt"
        test_file.write_text(test_content, encoding="utf-8")

        # 使用字符串路径加载
        loaded_content = self.loader.load(str(test_file))
        self.assertEqual(test_content, loaded_content)

    def test_load_binary_file(self):
        """测试加载二进制文件"""
        # 创建二进制文件
        binary_file = self.temp_dir / "binary.bin"
        binary_data = b"\x00\x01\x02\x03\xff"
        binary_file.write_bytes(binary_data)

        # 加载应该抛出ResourceLoadError
        from miniboot.env.resource.loader import ResourceLoadError

        with self.assertRaises(ResourceLoadError):
            self.loader.load(binary_file)

    def test_load_large_file(self):
        """测试加载大文件"""
        large_file = self.temp_dir / "large.txt"
        # 创建一个较大的文件（100KB）
        large_content = "x" * (100 * 1024)
        large_file.write_text(large_content, encoding="utf-8")

        content = self.loader.load(large_file)
        self.assertEqual(len(content), 100 * 1024)
        self.assertTrue(content.startswith("xxx"))

    def test_load_file_with_special_encoding(self):
        """测试加载特殊编码的文件"""
        special_file = self.temp_dir / "special.txt"
        # 写入包含特殊字符的内容
        special_content = "测试中文内容 🚀 Special chars: àáâãäå"
        special_file.write_text(special_content, encoding="utf-8")

        content = self.loader.load(special_file)
        self.assertEqual(content, special_content)

    def test_error_handling_coverage(self):
        """测试错误处理覆盖率"""
        # 这个测试主要是为了覆盖率，测试基本的错误处理逻辑

        # 创建一个测试文件
        test_file = self.temp_dir / "test.txt"
        test_file.write_text("test content", encoding="utf-8")

        # 测试exists方法的基本功能
        self.assertTrue(self.loader.exists(test_file))
        self.assertFalse(self.loader.exists("non_existent_file"))

        # 测试load方法的基本错误处理
        from miniboot.env.resource.loader import ResourceNotFoundError

        with self.assertRaises(ResourceNotFoundError):
            self.loader.load("definitely_non_existent_file_12345")


if __name__ == "__main__":
    unittest.main()
