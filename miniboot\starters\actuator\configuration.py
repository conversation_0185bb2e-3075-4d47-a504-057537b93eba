#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Actuator Starter 主自动配置类

负责 Actuator Starter 的核心配置和组件装配。
基于条件注解实现智能化的组件加载，符合新的架构设计。

主要功能：
- 注册核心 Bean：ActuatorContext、EndpointRegistry
- 条件化配置：基于属性配置启用/禁用功能
- 集成管理：协调各个模块的自动配置
"""

from typing import Optional

from loguru import logger

from miniboot.annotations import (Autowired, Bean, ConditionalOnProperty,
                                  Configuration)
from miniboot.annotations.conditional import AutoConfigureOrder
from miniboot.annotations.config import ConfigurationProperties
from miniboot.autoconfigure.base import (AutoConfiguration,
                                         StarterAutoConfiguration)
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
# 导入监控接口
from miniboot.monitoring.interfaces import EndpointProvider, MonitoringContext

from .properties import ActuatorProperties


@AutoConfigureOrder(25)  # 更高优先级，确保在WebAutoConfiguration(100)之前
@ConditionalOnProperty(name="miniboot.starters.actuator.enabled", match_if_missing=True)
class ActuatorStarterAutoConfiguration(StarterAutoConfiguration):
    """Actuator Starter 主自动配置类

    负责配置 Actuator Starter 的核心组件，包括：
    - ActuatorContext：纯监控上下文
    - EndpointRegistry：端点注册表
    - ActuatorProperties：配置属性

    采用条件化配置，支持灵活的功能启用/禁用。
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="actuator-starter-auto-configuration",
            description="Actuator Starter 主自动配置",
            priority=25,  # 更高优先级，确保在WebAutoConfiguration之前
            auto_configure_after=[],  # 不依赖其他配置
        )

    def get_starter_name(self) -> str:
        """获取Starter名称"""
        return "miniboot-starter-actuator"

    def get_starter_version(self) -> str:
        """获取Starter版本"""
        return "1.0.0"

    def get_starter_description(self) -> str:
        """获取Starter描述"""
        return "Mini-Boot Actuator Starter for monitoring and management"

    def get_configuration_properties_classes(self) -> list[type]:
        """获取配置属性类列表"""
        return [ActuatorProperties]



    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.context.enabled", match_if_missing=True)
    def actuator_context(self, actuator_properties: ActuatorProperties) -> "ActuatorContext":
        """创建 Actuator 上下文 Bean - 具体实现类型

        Args:
            actuator_properties: Actuator 配置属性

        Returns:
            ActuatorContext: Actuator 上下文实现
        """
        from miniboot.starters.actuator.context import ActuatorContext

        # 创建 ActuatorContext 实例
        context = ActuatorContext(properties=actuator_properties, auto_load_config=False)

        logger.info(f"Created ActuatorContext: {context.__class__.__name__} "
                   f"with {len(context.get_endpoints())} endpoints")
        return context

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.context.enabled", match_if_missing=True)
    def monitoring_context(self, actuator_context: "ActuatorContext") -> MonitoringContext:
        """创建监控上下文 Bean - 返回接口类型

        Args:
            actuator_context: Actuator 上下文实例

        Returns:
            MonitoringContext: 监控上下文接口实现
        """
        # 直接返回 ActuatorContext 实例，因为它实现了 MonitoringContext 接口
        logger.debug("Created MonitoringContext interface from ActuatorContext")
        return actuator_context

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.endpoints.enabled", match_if_missing=True)
    def endpoint_registry(self) -> "EndpointRegistry":
        """创建端点注册表 Bean

        Returns:
            EndpointRegistry: 端点注册表实例
        """
        from miniboot.starters.actuator.endpoints import EndpointRegistry

        registry = EndpointRegistry()
        logger.debug("Created EndpointRegistry bean")
        return registry

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.metrics.enabled", match_if_missing=True)
    def metrics_registry(self) -> "MetricsRegistry":
        """创建指标注册中心 Bean

        Returns:
            MetricsRegistry: 指标注册中心实例
        """
        from .endpoints.metrics import MetricsRegistry

        registry = MetricsRegistry()
        logger.debug("Created MetricsRegistry bean")
        return registry

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.health.enabled", match_if_missing=True)
    def health_endpoint_provider(self) -> EndpointProvider:
        """创建健康检查端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: 健康检查端点提供者接口实现
        """
        from .endpoints.health import HealthEndpoint

        endpoint = HealthEndpoint()
        logger.debug("Created HealthEndpoint as EndpointProvider")
        return endpoint

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.info.enabled", match_if_missing=True)
    def info_endpoint_provider(self) -> EndpointProvider:
        """创建信息端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: 信息端点提供者接口实现
        """
        from .endpoints.info import InfoEndpoint

        endpoint = InfoEndpoint()
        logger.debug("Created InfoEndpoint as EndpointProvider")
        return endpoint

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.metrics.enabled", match_if_missing=True)
    def metrics_endpoint_provider(self) -> EndpointProvider:
        """创建指标端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: 指标端点提供者实例
        """
        from .endpoints.metrics import MetricsEndpoint
        endpoint = MetricsEndpoint()
        logger.debug("Created MetricsEndpoint as EndpointProvider")
        return endpoint

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.threaddump.enabled", match_if_missing=True)
    def threaddump_endpoint_provider(self) -> EndpointProvider:
        """创建线程转储端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: 线程转储端点提供者实例
        """
        from .endpoints.threaddump import ThreadDumpEndpoint
        endpoint = ThreadDumpEndpoint()
        logger.debug("Created ThreadDumpEndpoint as EndpointProvider")
        return endpoint

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.env.enabled", match_if_missing=True)
    def env_endpoint_provider(self) -> EndpointProvider:
        """创建环境信息端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: 环境信息端点提供者实例
        """
        from .endpoints.env import AsyncEnvEndpoint
        endpoint = AsyncEnvEndpoint()
        logger.debug("Created AsyncEnvEndpoint as EndpointProvider")
        return endpoint

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.beans.enabled", match_if_missing=True)
    def beans_endpoint_provider(self) -> EndpointProvider:
        """创建Bean信息端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: Bean信息端点提供者实例
        """
        from .endpoints.beans import BeansEndpoint
        endpoint = BeansEndpoint()
        logger.debug("Created BeansEndpoint as EndpointProvider")
        return endpoint
