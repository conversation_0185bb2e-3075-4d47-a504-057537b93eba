#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置优先级综合测试用例
"""

import os
import tempfile
import unittest
from pathlib import Path

from miniboot.env import ConfigurationLoader, MutablePropertySources


class ConfigurationPriorityComprehensiveTestCase(unittest.TestCase):
    """配置优先级综合测试"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)

        # 保存原始环境变量
        self.original_config_path = os.getenv("MINIBOOT_CONFIG_PATH")
        self.original_miniboot_home = os.getenv("MINIBOOT_HOME")

        # 创建完整的测试配置文件结构
        self._create_comprehensive_config_structure()

    def tearDown(self):
        """清理测试环境"""
        # 恢复原始环境变量
        if self.original_config_path is not None:
            os.environ["MINIBOOT_CONFIG_PATH"] = self.original_config_path
        else:
            os.environ.pop("MINIBOOT_CONFIG_PATH", None)

        if self.original_miniboot_home is not None:
            os.environ["MINIBOOT_HOME"] = self.original_miniboot_home
        else:
            os.environ.pop("MINIBOOT_HOME", None)

        # 清理临时文件
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def _create_comprehensive_config_structure(self):
        """创建全面的配置文件结构"""
        # 用户配置目录
        user_dirs = [
            self.temp_path / "resources",
            self.temp_path / "config",
            self.temp_path / "src" / "main" / "resources",
            self.temp_path / "conf",
        ]

        # 框架配置目录
        framework_dirs = [
            self.temp_path / "miniboot" / "resources",
            self.temp_path / "miniboot" / "config",
        ]

        # 环境变量配置目录
        env_dirs = [
            self.temp_path / "env_config",
            self.temp_path / "miniboot_home" / "config",
        ]

        # 创建所有目录
        for directory in user_dirs + framework_dirs + env_dirs:
            directory.mkdir(parents=True, exist_ok=True)

        # 配置文件内容定义
        configs = [
            # 框架默认配置
            (
                framework_dirs[0] / "application.yml",
                """
miniboot:
  application:
    name: framework-app
    version: 1.0.0
    description: Framework default config
  server:
    port: 8000
    host: 0.0.0.0
    timeout: 30
  database:
    driver: com.mysql.cj.jdbc.Driver
    pool:
      min-size: 5
      max-size: 20
  logging:
    level: INFO
    pattern: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
""",
            ),
            # 框架Profile配置
            (
                framework_dirs[0] / "application-dev.yml",
                """
miniboot:
  application:
    description: Framework dev config
  server:
    timeout: 60
  database:
    pool:
      max-size: 10
  logging:
    level: DEBUG
""",
            ),
            # 用户默认配置（resources目录）
            (
                user_dirs[0] / "application.yml",
                """
miniboot:
  application:
    name: user-resources-app
    version: 2.0.0
  server:
    port: 8080
    host: localhost
  database:
    url: **********************************
    username: user
    password: password
""",
            ),
            # 用户默认配置（config目录）
            (
                user_dirs[1] / "application.yml",
                """
miniboot:
  application:
    name: user-config-app
  server:
    port: 8090
  database:
    url: ************************************
""",
            ),
            # 用户Profile配置
            (
                user_dirs[0] / "application-dev.yml",
                """
miniboot:
  application:
    name: user-dev-app
  server:
    port: 8081
  database:
    url: *********************************
    username: dev_user
  logging:
    level: TRACE
""",
            ),
            # Maven标准目录配置
            (
                user_dirs[2] / "application.yml",
                """
miniboot:
  application:
    name: maven-app
  server:
    port: 8070
""",
            ),
            # 环境变量配置
            (
                env_dirs[0] / "application.yml",
                """
miniboot:
  application:
    name: env-config-app
  server:
    port: 9000
""",
            ),
            # MINIBOOT_HOME配置
            (
                env_dirs[1] / "application.yml",
                """
miniboot:
  application:
    name: miniboot-home-app
  server:
    port: 9100
""",
            ),
        ]

        # 写入所有配置文件
        for config_path, content in configs:
            config_path.write_text(content.strip())

    def test_framework_vs_user_priority(self):
        """测试框架配置与用户配置的优先级"""
        search_paths = [str(self.temp_path / "resources"), str(self.temp_path / "miniboot" / "resources")]

        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        loader.load_configuration(property_sources, set())

        # 验证用户配置优先于框架配置
        app_name = property_sources.get_property("miniboot.application.name")
        self.assertEqual(app_name, "user-resources-app")

        # 验证用户配置覆盖框架配置
        port = property_sources.get_property("miniboot.server.port")
        self.assertEqual(port, 8080)

        # 验证框架配置的默认值被保留
        timeout = property_sources.get_property("miniboot.server.timeout")
        self.assertEqual(timeout, 30)

    def test_profile_specific_priority(self):
        """测试Profile特定配置的优先级"""
        search_paths = [str(self.temp_path / "resources"), str(self.temp_path / "miniboot" / "resources")]

        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        loader.load_configuration(property_sources, {"dev"})

        # 验证用户Profile配置具有最高优先级
        app_name = property_sources.get_property("miniboot.application.name")
        self.assertEqual(app_name, "user-dev-app")

        # 验证Profile配置覆盖默认配置
        port = property_sources.get_property("miniboot.server.port")
        self.assertEqual(port, 8081)

        # 验证日志级别被Profile配置覆盖
        log_level = property_sources.get_property("miniboot.logging.level")
        self.assertEqual(log_level, "TRACE")

    def test_multiple_user_config_directories(self):
        """测试多个用户配置目录的优先级"""
        search_paths = [
            str(self.temp_path / "config"),  # 高优先级
            str(self.temp_path / "resources"),  # 中优先级
            str(self.temp_path / "src" / "main" / "resources"),  # 低优先级
        ]

        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        loader.load_configuration(property_sources, set())

        # 验证config目录配置优先级最高
        app_name = property_sources.get_property("miniboot.application.name")
        self.assertEqual(app_name, "user-config-app")

        # 验证端口配置
        port = property_sources.get_property("miniboot.server.port")
        self.assertEqual(port, 8090)

    def test_environment_variable_priority(self):
        """测试环境变量配置路径的优先级"""
        # 设置环境变量
        env_config_path = str(self.temp_path / "env_config")
        miniboot_home = str(self.temp_path / "miniboot_home")

        os.environ["MINIBOOT_CONFIG_PATH"] = env_config_path
        os.environ["MINIBOOT_HOME"] = miniboot_home

        # 使用默认搜索路径（会读取环境变量）
        loader = ConfigurationLoader()
        property_sources = MutablePropertySources()

        # 切换到临时目录
        original_cwd = os.getcwd()
        try:
            os.chdir(self.temp_dir)
            loader.load_configuration(property_sources, set())

            # 验证环境变量路径具有最高优先级
            app_name = property_sources.get_property("miniboot.application.name")
            # MINIBOOT_HOME 路径优先级高于 MINIBOOT_CONFIG_PATH
            self.assertEqual(app_name, "miniboot-home-app")

            port = property_sources.get_property("miniboot.server.port")
            self.assertEqual(port, 9100)

        finally:
            os.chdir(original_cwd)

    def test_configuration_merging_behavior(self):
        """测试配置合并行为"""
        search_paths = [str(self.temp_path / "resources"), str(self.temp_path / "miniboot" / "resources")]

        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        loader.load_configuration(property_sources, {"dev"})

        # 验证配置正确合并
        # 应用名称来自用户Profile配置
        self.assertEqual(property_sources.get_property("miniboot.application.name"), "user-dev-app")

        # 版本信息来自用户默认配置
        self.assertEqual(property_sources.get_property("miniboot.application.version"), "2.0.0")

        # 描述信息来自框架Profile配置（优先级高于框架默认配置）
        self.assertEqual(property_sources.get_property("miniboot.application.description"), "Framework dev config")

        # 数据库驱动来自框架配置
        self.assertEqual(property_sources.get_property("miniboot.database.driver"), "com.mysql.cj.jdbc.Driver")

        # 数据库URL来自用户Profile配置
        self.assertEqual(property_sources.get_property("miniboot.database.url"), "*********************************")

    def test_priority_values_correctness(self):
        """测试优先级数值的正确性"""
        search_paths = [str(self.temp_path / "resources"), str(self.temp_path / "miniboot" / "resources")]

        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        loader.load_configuration(property_sources, {"dev"})

        # 获取所有配置源
        sources = property_sources._property_sources

        # 验证优先级排序（从高到低）
        priorities = [source.priority for source in sources]
        self.assertEqual(priorities, sorted(priorities, reverse=True))

        # 验证具体优先级值范围
        priority_ranges = {
            "commandLineArgs": (10000, 10000),  # 系统级
            "application-dev.yml": (6000, 8000),  # Profile配置（框架和用户）
            "application.yml": (5000, 7000),  # 默认配置（框架和用户）
        }

        for source in sources:
            if source.name in priority_ranges:
                min_priority, max_priority = priority_ranges[source.name]
                self.assertGreaterEqual(source.priority, min_priority)
                self.assertLessEqual(source.priority, max_priority)

    def test_load_statistics_accuracy(self):
        """测试加载统计信息的准确性"""
        search_paths = [str(self.temp_path / "resources"), str(self.temp_path / "miniboot" / "resources")]

        loader = ConfigurationLoader(search_paths)
        property_sources = MutablePropertySources()

        result = loader.load_configuration(property_sources, {"dev"})

        # 验证加载统计
        self.assertGreater(result.load_time_ms, 0)
        self.assertGreater(len(result.loaded_files), 0)
        self.assertEqual(len(result.failed_files), 0)
        self.assertGreater(result.total_sources, 0)

        # 验证层级统计
        self.assertIn("user", result.layer_statistics)
        self.assertIn("framework", result.layer_statistics)
        self.assertIn("system", result.layer_statistics)

        # 验证用户配置源数量大于等于框架配置源数量
        self.assertGreaterEqual(result.layer_statistics["user"], result.layer_statistics["framework"])


if __name__ == "__main__":
    unittest.main()
