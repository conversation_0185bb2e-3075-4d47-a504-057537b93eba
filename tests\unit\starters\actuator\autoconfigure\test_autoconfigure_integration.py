#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Autoconfigure integration tests - basic integration testing for all autoconfigure modules
"""

import unittest
from unittest.mock import MagicMock, patch

from miniboot.starters.actuator.autoconfigure.bean import \
    BeanMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.context import \
    ContextMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.env import \
    EnvMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.scheduler import \
    SchedulerMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.web import WebAutoConfiguration


class AutoConfigureIntegrationTestCase(unittest.TestCase):
    """Autoconfigure modules integration test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.bean_config = BeanMetricsAutoConfiguration()
        self.context_config = ContextMetricsAutoConfiguration()
        self.env_config = EnvMetricsAutoConfiguration()
        self.scheduler_config = SchedulerMetricsAutoConfiguration()
        self.web_config = WebAutoConfiguration()

    def test_all_configurations_initialization(self) -> None:
        """Test all auto-configurations can be initialized"""
        self.assertIsInstance(self.bean_config, BeanMetricsAutoConfiguration)
        self.assertIsInstance(self.context_config, ContextMetricsAutoConfiguration)
        self.assertIsInstance(self.env_config, EnvMetricsAutoConfiguration)
        self.assertIsInstance(self.scheduler_config, SchedulerMetricsAutoConfiguration)
        self.assertIsInstance(self.web_config, WebAutoConfiguration)

    def test_all_configurations_metadata(self) -> None:
        """Test all auto-configurations provide metadata"""
        configs = [
            self.bean_config,
            self.context_config,
            self.env_config,
            self.scheduler_config,
            self.web_config
        ]

        for config in configs:
            metadata = config.get_metadata()
            self.assertIsNotNone(metadata.name)
            self.assertIsNotNone(metadata.description)
            self.assertIsInstance(metadata.priority, int)
            self.assertIsInstance(metadata.auto_configure_after, list)

    def test_configuration_priorities(self) -> None:
        """Test configuration priorities are properly set"""
        bean_metadata = self.bean_config.get_metadata()
        context_metadata = self.context_config.get_metadata()
        env_metadata = self.env_config.get_metadata()
        scheduler_metadata = self.scheduler_config.get_metadata()
        web_metadata = self.web_config.get_metadata()

        # All metrics configurations should have same priority (200)
        self.assertEqual(bean_metadata.priority, 200)
        self.assertEqual(context_metadata.priority, 200)
        self.assertEqual(env_metadata.priority, 200)
        self.assertEqual(scheduler_metadata.priority, 200)

        # Web configuration should have higher priority (300)
        self.assertEqual(web_metadata.priority, 300)

    def test_configuration_dependencies(self) -> None:
        """Test configuration dependencies are properly set"""
        configs = [
            self.bean_config,
            self.context_config,
            self.env_config,
            self.scheduler_config,
            self.web_config
        ]

        for config in configs:
            metadata = config.get_metadata()
            # All should depend on actuator-starter-auto-configuration
            self.assertIn("actuator-starter-auto-configuration", metadata.auto_configure_after)

    def test_bean_creation_integration(self) -> None:
        """Test Bean creation across all configurations"""
        # Bean metrics configuration
        bean_collector = self.bean_config.bean_metrics_collector()
        self.assertIsNotNone(bean_collector)

        # Context metrics configuration
        context_collector = self.context_config.context_metrics_collector()
        self.assertIsNotNone(context_collector)

        # Environment metrics configuration
        env_collector = self.env_config.env_metrics_collector()
        self.assertIsNotNone(env_collector)

        # Scheduler metrics configuration
        scheduler_collector = self.scheduler_config.scheduler_metrics_collector()
        self.assertIsNotNone(scheduler_collector)

        # Web configuration
        web_checker = self.web_config.web_integration_checker()
        self.assertIsNotNone(web_checker)

        # Note: actuator_route_registrar requires dependencies, so we skip it in basic test

    def test_collector_names_uniqueness(self) -> None:
        """Test all collectors have unique names"""
        bean_collector = self.bean_config.bean_metrics_collector()
        context_collector = self.context_config.context_metrics_collector()
        env_collector = self.env_config.env_metrics_collector()
        scheduler_collector = self.scheduler_config.scheduler_metrics_collector()

        collector_names = [
            bean_collector.get_collector_name(),
            context_collector.get_collector_name(),
            env_collector.get_collector_name(),
            scheduler_collector.get_collector_name()
        ]

        # All names should be unique
        self.assertEqual(len(collector_names), len(set(collector_names)))

    def test_all_collectors_available(self) -> None:
        """Test all collectors report as available"""
        bean_collector = self.bean_config.bean_metrics_collector()
        context_collector = self.context_config.context_metrics_collector()
        env_collector = self.env_config.env_metrics_collector()
        scheduler_collector = self.scheduler_config.scheduler_metrics_collector()

        collectors = [bean_collector, context_collector, env_collector, scheduler_collector]

        for collector in collectors:
            self.assertTrue(collector.is_available())

    def test_all_collectors_provide_metrics(self) -> None:
        """Test all collectors can provide supported metrics list"""
        bean_collector = self.bean_config.bean_metrics_collector()
        context_collector = self.context_config.context_metrics_collector()
        env_collector = self.env_config.env_metrics_collector()
        scheduler_collector = self.scheduler_config.scheduler_metrics_collector()

        collectors = [bean_collector, context_collector, env_collector, scheduler_collector]

        for collector in collectors:
            supported_metrics = collector.get_supported_metrics()
            self.assertIsInstance(supported_metrics, list)
            self.assertGreater(len(supported_metrics), 0)

    def test_all_collectors_can_collect_metrics(self) -> None:
        """Test all collectors can collect metrics without errors"""
        bean_collector = self.bean_config.bean_metrics_collector()
        context_collector = self.context_config.context_metrics_collector()
        env_collector = self.env_config.env_metrics_collector()
        scheduler_collector = self.scheduler_config.scheduler_metrics_collector()

        collectors = [bean_collector, context_collector, env_collector, scheduler_collector]

        for collector in collectors:
            # Should not raise exception
            metrics_data = collector.collect_metrics()
            self.assertIsInstance(metrics_data, list)


if __name__ == "__main__":
    unittest.main()
