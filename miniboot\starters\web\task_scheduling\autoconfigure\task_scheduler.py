"""
Task Scheduler Auto Configuration

任务调度自动配置类，提供 TaskScheduler 的自动配置和 Bean 注册。
"""

from typing import Optional

from miniboot.annotations import Bean
from miniboot.autoconfigure import (AutoConfiguration,
                                    AutoConfigurationMetadata,
                                    ConditionalOnProperty)

from ..scheduler import TaskScheduler
from ..properties import TaskSchedulerProperties


@ConditionalOnProperty(name="miniboot.starters.web.task-scheduler.enabled", match_if_missing=True)
class TaskSchedulerAutoConfiguration(AutoConfiguration):
    """任务调度自动配置
    
    当满足以下条件时自动配置 TaskScheduler：
    1. 配置属性 miniboot.starters.web.task-scheduler.enabled 为 true（默认为 true）
    
    提供的 Bean：
    - task_scheduler_properties: TaskSchedulerProperties 配置属性
    - task_scheduler: TaskScheduler 任务调度器实例
    """
    
    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取自动配置元数据
        
        Returns:
            自动配置元数据
        """
        return AutoConfigurationMetadata(
            name="task-scheduler-auto-configuration",
            description="Web 任务调度自动配置，提供智能任务调度和管理功能",
            priority=450,  # 中等优先级，在 LoadMonitor 之后
        )
    
    @Bean
    def task_scheduler_properties(self) -> TaskSchedulerProperties:
        """创建任务调度配置属性 Bean
        
        Returns:
            TaskSchedulerProperties 实例
        """
        properties = TaskSchedulerProperties()
        
        # 验证配置参数
        properties.validate()
        
        return properties
    
    @Bean
    def task_scheduler(self, properties: TaskSchedulerProperties) -> TaskScheduler:
        """创建任务调度器 Bean
        
        Args:
            properties: 任务调度配置属性（自动注入）
            
        Returns:
            TaskScheduler 实例
        """
        # 创建 TaskScheduler 实例
        scheduler = TaskScheduler(properties)
        
        # 容器会自动调用 @PostConstruct 方法初始化调度器
        
        return scheduler