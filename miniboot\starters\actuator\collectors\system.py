#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 异步系统指标收集器 - 高性能异步实现

提供高性能的异步系统指标收集,支持并发执行 I/O 操作和智能缓存.

核心特性:
- 异步系统指标收集
- 并发执行 I/O 操作
- CPU、内存、磁盘、网络指标
- 智能缓存和性能优化
- 跨平台支持
"""

import asyncio
import os
import platform
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

import psutil
from loguru import logger

from miniboot.utils import timeout


class SystemMetricsCollector:
    """异步系统指标收集器

    提供高性能的异步系统指标收集,支持并发执行和智能缓存.
    """

    def __init__(self, timeout: float = 5.0, cache_ttl: float = 30.0):
        """初始化异步系统指标收集器

        Args:
            timeout: 收集超时时间(秒)
            cache_ttl: 缓存TTL(秒)
        """
        self.timeout = timeout
        self.cache_ttl = cache_ttl
        self._cache = {}
        self._cache_timestamps = {}
        self._start_time = time.time()

    @timeout(5.0)
    async def collect_all(self) -> Dict[str, Any]:
        """收集所有系统指标"""
        try:
            loop = asyncio.get_event_loop()

            # 并发收集不同类型的系统指标
            cpu_task = loop.run_in_executor(None, self._collect_cpu_metrics)
            memory_task = loop.run_in_executor(None, self._collect_memory_metrics)
            disk_task = loop.run_in_executor(None, self._collect_disk_metrics)
            network_task = loop.run_in_executor(None, self._collect_network_metrics)
            process_task = loop.run_in_executor(None, self._collect_process_metrics)
            system_task = loop.run_in_executor(None, self._collect_system_info)

            # 等待所有任务完成
            cpu_metrics, memory_metrics, disk_metrics, network_metrics, process_metrics, system_info = await asyncio.gather(
                cpu_task, memory_task, disk_task, network_task, process_task, system_task, return_exceptions=True
            )

            return {
                "timestamp": datetime.now().isoformat(),
                "cpu": cpu_metrics if not isinstance(cpu_metrics, Exception) else {"error": str(cpu_metrics)},
                "memory": memory_metrics if not isinstance(memory_metrics, Exception) else {"error": str(memory_metrics)},
                "disk": disk_metrics if not isinstance(disk_metrics, Exception) else {"error": str(disk_metrics)},
                "network": network_metrics if not isinstance(network_metrics, Exception) else {"error": str(network_metrics)},
                "process": process_metrics if not isinstance(process_metrics, Exception) else {"error": str(process_metrics)},
                "system": system_info if not isinstance(system_info, Exception) else {"error": str(system_info)},
            }

        except Exception as e:
            logger.error(f"System metrics collection failed: {e}")
            return {"timestamp": datetime.now().isoformat(), "error": str(e)}

    def _collect_cpu_metrics(self) -> Dict[str, Any]:
        """收集CPU指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            cpu_count = psutil.cpu_count()
            cpu_count_logical = psutil.cpu_count(logical=True)

            # CPU频率
            cpu_freq = psutil.cpu_freq()

            # CPU时间
            cpu_times = psutil.cpu_times()

            # 负载平均值(仅Unix系统)
            load_avg = None
            if hasattr(os, "getloadavg"):
                try:
                    load_avg = os.getloadavg()
                except OSError:
                    pass

            return {
                "usage_percent": cpu_percent,
                "count_physical": cpu_count,
                "count_logical": cpu_count_logical,
                "frequency": {
                    "current": cpu_freq.current if cpu_freq else None,
                    "min": cpu_freq.min if cpu_freq else None,
                    "max": cpu_freq.max if cpu_freq else None,
                },
                "times": {
                    "user": cpu_times.user,
                    "system": cpu_times.system,
                    "idle": cpu_times.idle,
                    "iowait": getattr(cpu_times, "iowait", None),
                    "irq": getattr(cpu_times, "irq", None),
                    "softirq": getattr(cpu_times, "softirq", None),
                },
                "load_average": {
                    "1min": load_avg[0] if load_avg else None,
                    "5min": load_avg[1] if load_avg else None,
                    "15min": load_avg[2] if load_avg else None,
                }
                if load_avg
                else None,
            }

        except Exception as e:
            return {"error": f"Failed to collect CPU metrics: {e}"}

    def _collect_memory_metrics(self) -> Dict[str, Any]:
        """收集内存指标"""
        try:
            # 虚拟内存
            virtual_memory = psutil.virtual_memory()

            # 交换内存
            swap_memory = psutil.swap_memory()

            return {
                "virtual": {
                    "total": virtual_memory.total,
                    "available": virtual_memory.available,
                    "used": virtual_memory.used,
                    "free": virtual_memory.free,
                    "percent": virtual_memory.percent,
                    "active": getattr(virtual_memory, "active", None),
                    "inactive": getattr(virtual_memory, "inactive", None),
                    "buffers": getattr(virtual_memory, "buffers", None),
                    "cached": getattr(virtual_memory, "cached", None),
                },
                "swap": {
                    "total": swap_memory.total,
                    "used": swap_memory.used,
                    "free": swap_memory.free,
                    "percent": swap_memory.percent,
                    "sin": swap_memory.sin,
                    "sout": swap_memory.sout,
                },
            }

        except Exception as e:
            return {"error": f"Failed to collect memory metrics: {e}"}

    def _collect_disk_metrics(self) -> Dict[str, Any]:
        """收集磁盘指标"""
        try:
            # 磁盘分区
            partitions = psutil.disk_partitions()
            disk_info = []

            for partition in partitions:
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info.append(
                        {
                            "device": partition.device,
                            "mountpoint": partition.mountpoint,
                            "fstype": partition.fstype,
                            "total": usage.total,
                            "used": usage.used,
                            "free": usage.free,
                            "percent": (usage.used / usage.total * 100) if usage.total > 0 else 0,
                        }
                    )
                except PermissionError:
                    # 跳过无权限访问的分区
                    continue

            # 磁盘I/O统计
            disk_io = psutil.disk_io_counters()

            return {
                "partitions": disk_info,
                "io": {
                    "read_count": disk_io.read_count if disk_io else None,
                    "write_count": disk_io.write_count if disk_io else None,
                    "read_bytes": disk_io.read_bytes if disk_io else None,
                    "write_bytes": disk_io.write_bytes if disk_io else None,
                    "read_time": disk_io.read_time if disk_io else None,
                    "write_time": disk_io.write_time if disk_io else None,
                }
                if disk_io
                else None,
            }

        except Exception as e:
            return {"error": f"Failed to collect disk metrics: {e}"}

    def _collect_network_metrics(self) -> Dict[str, Any]:
        """收集网络指标"""
        try:
            # 网络I/O统计
            net_io = psutil.net_io_counters()

            # 网络连接
            connections = psutil.net_connections()
            connection_stats = {
                "total": len(connections),
                "established": len([c for c in connections if c.status == "ESTABLISHED"]),
                "listen": len([c for c in connections if c.status == "LISTEN"]),
                "time_wait": len([c for c in connections if c.status == "TIME_WAIT"]),
            }

            # 网络接口
            net_if_addrs = psutil.net_if_addrs()
            net_if_stats = psutil.net_if_stats()

            interfaces = {}
            for interface, addrs in net_if_addrs.items():
                stats = net_if_stats.get(interface)
                interfaces[interface] = {
                    "addresses": [{"family": addr.family.name, "address": addr.address, "netmask": addr.netmask} for addr in addrs],
                    "is_up": stats.isup if stats else None,
                    "duplex": stats.duplex.name if stats and hasattr(stats.duplex, "name") else None,
                    "speed": stats.speed if stats else None,
                    "mtu": stats.mtu if stats else None,
                }

            return {
                "io": {
                    "bytes_sent": net_io.bytes_sent if net_io else None,
                    "bytes_recv": net_io.bytes_recv if net_io else None,
                    "packets_sent": net_io.packets_sent if net_io else None,
                    "packets_recv": net_io.packets_recv if net_io else None,
                    "errin": net_io.errin if net_io else None,
                    "errout": net_io.errout if net_io else None,
                    "dropin": net_io.dropin if net_io else None,
                    "dropout": net_io.dropout if net_io else None,
                }
                if net_io
                else None,
                "connections": connection_stats,
                "interfaces": interfaces,
            }

        except Exception as e:
            return {"error": f"Failed to collect network metrics: {e}"}

    def _collect_process_metrics(self) -> Dict[str, Any]:
        """收集进程指标"""
        try:
            # 当前进程
            current_process = psutil.Process()

            # 进程统计
            processes = list(psutil.process_iter(["pid", "name", "status", "cpu_percent", "memory_percent"]))

            process_stats = {
                "total": len(processes),
                "running": len([p for p in processes if p.info["status"] == "running"]),
                "sleeping": len([p for p in processes if p.info["status"] == "sleeping"]),
                "zombie": len([p for p in processes if p.info["status"] == "zombie"]),
            }

            # 当前进程详细信息
            current_info = {
                "pid": current_process.pid,
                "name": current_process.name(),
                "status": current_process.status(),
                "cpu_percent": current_process.cpu_percent(),
                "memory_percent": current_process.memory_percent(),
                "memory_info": {"rss": current_process.memory_info().rss, "vms": current_process.memory_info().vms},
                "num_threads": current_process.num_threads(),
                "num_fds": current_process.num_fds() if hasattr(current_process, "num_fds") else None,
                "create_time": current_process.create_time(),
            }

            return {"statistics": process_stats, "current_process": current_info}

        except Exception as e:
            return {"error": f"Failed to collect process metrics: {e}"}

    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        try:
            # 系统信息
            uname = platform.uname()

            # 启动时间
            boot_time = psutil.boot_time()
            uptime = time.time() - boot_time

            return {
                "platform": {
                    "system": uname.system,
                    "node": uname.node,
                    "release": uname.release,
                    "version": uname.version,
                    "machine": uname.machine,
                    "processor": uname.processor,
                },
                "python": {
                    "version": platform.python_version(),
                    "implementation": platform.python_implementation(),
                    "compiler": platform.python_compiler(),
                },
                "uptime": {"boot_time": boot_time, "uptime_seconds": uptime, "uptime_formatted": self._format_uptime(uptime)},
                "collection_time": datetime.now().isoformat(),
                "collector_uptime": time.time() - self._start_time,
            }

        except Exception as e:
            return {"error": f"Failed to collect system info: {e}"}

    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if days > 0:
            return f"{days}d {hours}h {minutes}m {secs}s"
        elif hours > 0:
            return f"{hours}h {minutes}m {secs}s"
        elif minutes > 0:
            return f"{minutes}m {secs}s"
        else:
            return f"{secs}s"

    async def collect_specific(self, metric_type: str) -> Dict[str, Any]:
        """异步收集特定类型的指标"""
        try:
            loop = asyncio.get_event_loop()

            if metric_type == "cpu":
                result = await loop.run_in_executor(None, self._collect_cpu_metrics)
            elif metric_type == "memory":
                result = await loop.run_in_executor(None, self._collect_memory_metrics)
            elif metric_type == "disk":
                result = await loop.run_in_executor(None, self._collect_disk_metrics)
            elif metric_type == "network":
                result = await loop.run_in_executor(None, self._collect_network_metrics)
            elif metric_type == "process":
                result = await loop.run_in_executor(None, self._collect_process_metrics)
            elif metric_type == "system":
                result = await loop.run_in_executor(None, self._collect_system_info)
            else:
                raise ValueError(f"Unknown metric type: {metric_type}")

            return {"timestamp": datetime.now().isoformat(), "metric_type": metric_type, "data": result}

        except Exception as e:
            logger.error(f"Specific metric collection failed for {metric_type}: {e}")
            return {"timestamp": datetime.now().isoformat(), "metric_type": metric_type, "error": str(e)}

    def get_supported_metrics(self) -> List[str]:
        """获取支持的指标类型"""
        return ["cpu", "memory", "disk", "network", "process", "system"]
