#!/usr/bin/env python
"""
* @author: cz
* @description: Mini-Boot 日志模块

Mini-Boot框架的日志系统模块,基于loguru实现的简洁日志系统.

主要组件:
- Logger: 日志管理器类,负责配置和管理日志系统
- configure_logger: 便捷配置函数

使用示例:
    # 1. 使用Logger类(推荐)
    from miniboot.log import Logger
    from miniboot.env import StandardEnvironment

    env = StandardEnvironment()
    logger_manager = Logger()
    logger_manager.configure(env)

    # 2. 使用便捷函数
    from miniboot.log import configure_logger
    configure_logger(env)

    # 3. 在任何模块中使用日志
    from loguru import logger
    logger.info("这是一条日志")
"""

from .logger import Logger
from .properties import (ConsoleConfig, FileConfig, FormatConfig,
                         LoggingProperties, RotationConfig)

__all__ = [
    "Logger",
    "LoggingProperties",
    "ConsoleConfig",
    "FileConfig",
    "FormatConfig",
    "RotationConfig",
]
