"""
Load Monitor Properties

负载监控配置属性类，定义所有可配置的监控参数。
"""

from dataclasses import dataclass
from typing import Optional

from miniboot.annotations import ConfigurationProperties


@ConfigurationProperties(prefix="miniboot.starters.web.load-monitoring")
@dataclass
class LoadMonitorProperties:
    """负载监控配置属性

    配置前缀: miniboot.starters.web.load-monitoring

    示例配置:
    ```yaml
    miniboot:
      starters:
        web:
          load-monitoring:
            enabled: true
            monitor-interval: 3.0
            cpu-threshold: 80.0
            memory-threshold: 85.0
    ```
    """

    # ==================== 基础配置 ====================
    enabled: bool = True
    """是否启用负载监控"""

    monitor_interval: float = 3.0
    """监控间隔(秒)，建议不低于1秒以避免性能影响"""

    # ==================== 阈值配置 ====================
    cpu_threshold: float = 80.0
    """CPU 使用率阈值(%)，超过此值触发高负载警告"""

    memory_threshold: float = 85.0
    """内存使用率阈值(%)，超过此值触发高负载警告"""

    response_time_threshold: float = 1000.0
    """响应时间阈值(毫秒)，超过此值认为响应过慢"""

    throughput_threshold: float = 10.0
    """吞吐量阈值(请求/秒)，低于此值认为吞吐量过低"""

    # ==================== 历史数据配置 ====================
    max_history_size: int = 1000
    """最大历史记录数量，用于趋势分析"""

    cleanup_interval: float = 300.0
    """历史数据清理间隔(秒)，默认5分钟"""

    # ==================== 回调配置 ====================
    enable_callbacks: bool = True
    """是否启用负载变化回调通知"""

    callback_timeout: float = 5.0
    """回调执行超时时间(秒)"""

    # ==================== 高级配置 ====================
    enable_detailed_metrics: bool = False
    """是否启用详细指标收集（可能影响性能）"""

    load_calculation_window: int = 10
    """负载计算窗口大小（最近N次测量的平均值）"""

    auto_adjust_interval: bool = False
    """是否根据系统负载自动调整监控间隔"""

    min_monitor_interval: float = 1.0
    """自动调整时的最小监控间隔(秒)"""

    max_monitor_interval: float = 10.0
    """自动调整时的最大监控间隔(秒)"""

    def validate(self) -> None:
        """验证配置参数的合理性"""
        if self.monitor_interval <= 0:
            raise ValueError("monitor_interval must be positive")

        if self.cpu_threshold <= 0 or self.cpu_threshold > 100:
            raise ValueError("cpu_threshold must be between 0 and 100")

        if self.memory_threshold <= 0 or self.memory_threshold > 100:
            raise ValueError("memory_threshold must be between 0 and 100")

        if self.response_time_threshold <= 0:
            raise ValueError("response_time_threshold must be positive")

        if self.max_history_size <= 0:
            raise ValueError("max_history_size must be positive")

        if self.auto_adjust_interval:
            if self.min_monitor_interval >= self.max_monitor_interval:
                raise ValueError("min_monitor_interval must be less than max_monitor_interval")

    def get_effective_monitor_interval(self, current_load: Optional[float] = None) -> float:
        """获取有效的监控间隔

        Args:
            current_load: 当前负载水平 (0.0-1.0)

        Returns:
            有效的监控间隔
        """
        if not self.auto_adjust_interval or current_load is None:
            return self.monitor_interval

        # 根据负载动态调整间隔：负载越高，监控越频繁
        adjusted_interval = self.max_monitor_interval - (current_load * (self.max_monitor_interval - self.min_monitor_interval))
        return max(self.min_monitor_interval, min(self.max_monitor_interval, adjusted_interval))
