#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: <PERSON>ck Starter配置属性
"""

from dataclasses import dataclass

from miniboot.annotations import ConfigurationProperties
from miniboot.autoconfigure.properties import StarterProperties


@ConfigurationProperties(prefix="miniboot.starters.mock")
@dataclass
class MockProperties(StarterProperties):
    """Mock Starter配置属性

    用于配置Mock功能的各种参数.
    """

    # 基础配置
    enabled: bool = True  # 是否启用Mock功能
    auto_mock: bool = True  # 是否自动Mock外部依赖
    data_source: str = "random"  # 数据源类型:random, file, database
    prefix: str = "[MOCK]"  # Mock日志前缀

    # 行为配置
    strict_mode: bool = False  # 是否启用严格模式
    response_delay: int = 0  # 响应延迟(毫秒)
    failure_rate: float = 0.0  # 失败率(0.0-1.0)

    # 缓存配置
    cache_enabled: bool = True  # 是否启用缓存
    cache_size: int = 1000  # 缓存大小
    cache_ttl: int = 3600  # 缓存TTL(秒)

    # 数据配置
    data_file_path: str = ""  # 数据文件路径
    patterns: list = None  # Mock模式列表

    def __post_init__(self):
        """初始化后处理"""
        if self.patterns is None:
            self.patterns = []

    def validate(self) -> None:
        """验证配置参数"""
        if not (0.0 <= self.failure_rate <= 1.0):
            raise ValueError("failure_rate must be between 0.0 and 1.0")

        if self.cache_size < 0:
            raise ValueError("cache_size must be non-negative")

        if self.cache_ttl < 0:
            raise ValueError("cache_ttl must be non-negative")

        if self.response_delay < 0:
            raise ValueError("response_delay must be non-negative")
