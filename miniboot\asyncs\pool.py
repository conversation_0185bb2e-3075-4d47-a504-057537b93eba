#!/usr/bin/env python
"""
* @author: cz
* @description: 线程池管理系统

提供可配置的线程池管理器,支持动态调整、监控和资源管理.
"""

import contextlib
import os
import threading
import time
from concurrent.futures import Future, ThreadPoolExecutor
from dataclasses import dataclass, field
from enum import Enum
from queue import Queue
from typing import Any, Callable, Optional

from ..utils.singleton import SingletonMeta
from .properties import ThreadPoolConfig


class PoolStrategy(Enum):
    """线程池策略"""

    FIXED = "fixed"  # 固定大小线程池
    CACHED = "cached"  # 缓存线程池
    DYNAMIC = "dynamic"  # 动态调整线程池
    SCHEDULED = "scheduled"  # 定时任务线程池


class PoolState(Enum):
    """线程池状态"""

    CREATED = "created"  # 已创建
    RUNNING = "running"  # 运行中
    PAUSED = "paused"  # 已暂停
    SHUTDOWN = "shutdown"  # 已关闭
    TERMINATED = "terminated"  # 已终止




@dataclass
class ThreadPoolMetrics:
    """线程池指标"""

    pool_name: str = ""
    state: PoolState = PoolState.CREATED
    core_pool_size: int = 0
    maximum_pool_size: int = 0
    current_pool_size: int = 0
    active_count: int = 0
    largest_pool_size: int = 0
    task_count: int = 0
    completed_task_count: int = 0
    queue_size: int = 0
    queue_remaining_capacity: int = 0
    rejected_execution_count: int = 0
    average_execution_time: float = 0.0
    peak_execution_time: float = 0.0
    total_execution_time: float = 0.0
    created_time: float = field(default_factory=time.time)
    last_activity_time: float = field(default_factory=time.time)

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return {
            "pool_name": self.pool_name,
            "state": self.state.value,
            "core_pool_size": self.core_pool_size,
            "maximum_pool_size": self.maximum_pool_size,
            "current_pool_size": self.current_pool_size,
            "active_count": self.active_count,
            "largest_pool_size": self.largest_pool_size,
            "task_count": self.task_count,
            "completed_task_count": self.completed_task_count,
            "queue_size": self.queue_size,
            "queue_remaining_capacity": self.queue_remaining_capacity,
            "rejected_execution_count": self.rejected_execution_count,
            "average_execution_time": self.average_execution_time,
            "peak_execution_time": self.peak_execution_time,
            "total_execution_time": self.total_execution_time,
            "uptime": time.time() - self.created_time,
            "idle_time": time.time() - self.last_activity_time,
        }


class TaskWrapper:
    """任务包装器"""

    def __init__(self, task_id: str, func: Callable, args: tuple, kwargs: dict):
        self.task_id = task_id
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.submit_time = time.time()
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.result: Any = None
        self.exception: Optional[Exception] = None

    def execute(self) -> Any:
        """执行任务"""
        self.start_time = time.time()
        try:
            self.result = self.func(*self.args, **self.kwargs)
            return self.result
        except Exception as e:
            self.exception = e
            raise
        finally:
            self.end_time = time.time()

    @property
    def execution_time(self) -> float:
        """执行时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0.0

    @property
    def wait_time(self) -> float:
        """等待时间"""
        if self.start_time:
            return self.start_time - self.submit_time
        return time.time() - self.submit_time


class ManagedThreadPool:
    """托管线程池"""

    def __init__(self, config: ThreadPoolConfig, name: str = "default"):
        """
        初始化托管线程池

        Args:
            config: 线程池配置 (来自 properties.py)
            name: 线程池名称
        """
        self.config = config
        self.name = name

        self.metrics = ThreadPoolMetrics(pool_name=name)
        self._executor: Optional[ThreadPoolExecutor] = None
        self._lock = threading.RLock()
        self._task_queue: Queue = Queue(maxsize=config.queue_capacity)
        self._active_tasks: dict[str, TaskWrapper] = {}
        self._completed_tasks: list[TaskWrapper] = []
        self._task_counter = 0
        self._shutdown_event = threading.Event()
        self._monitor_thread: Optional[threading.Thread] = None

        # 延迟初始化线程池,只在需要时创建
        # self._initialize_pool()

        # 默认启用监控
        self._start_monitoring()

    def _initialize_pool(self):
        """初始化线程池"""
        with self._lock:
            # 使用固定大小线程池策略
            self._executor = ThreadPoolExecutor(
                max_workers=self.config.max_size,
                thread_name_prefix=self.config.thread_name_prefix
            )

            self.metrics.state = PoolState.RUNNING
            self.metrics.core_pool_size = self.config.core_size
            self.metrics.maximum_pool_size = self.config.max_size

    def _start_monitoring(self):
        """启动监控线程"""

        def monitor():
            while not self._shutdown_event.is_set():
                try:
                    self._update_metrics()
                    time.sleep(1)  # 每秒更新一次指标
                except Exception:
                    pass  # 忽略监控错误

        self._monitor_thread = threading.Thread(target=monitor, name=f"{self.name}-monitor", daemon=True)
        self._monitor_thread.start()

    def _update_metrics(self):
        """更新指标"""
        with self._lock:
            if self._executor:
                # 更新基本指标
                self.metrics.current_pool_size = len(self._executor._threads)
                self.metrics.active_count = len(self._active_tasks)
                self.metrics.queue_size = self._task_queue.qsize()
                self.metrics.queue_remaining_capacity = self.config.queue_capacity - self.metrics.queue_size

                # 更新执行时间统计
                if self._completed_tasks:
                    execution_times = [task.execution_time for task in self._completed_tasks]
                    self.metrics.total_execution_time = sum(execution_times)
                    self.metrics.average_execution_time = self.metrics.total_execution_time / len(self._completed_tasks)
                    self.metrics.peak_execution_time = max(execution_times)

                # 更新活动时间
                if self._active_tasks or not self._task_queue.empty():
                    self.metrics.last_activity_time = time.time()

    def submit(self, func: Callable, *args, **kwargs) -> Future:
        """提交任务"""
        # 检查线程池是否已关闭
        if self.metrics.state in (PoolState.SHUTDOWN, PoolState.TERMINATED):
            raise RuntimeError(f"Thread pool {self.name} is shutdown")

        # 延迟初始化线程池
        if self._executor is None:
            self._initialize_pool()

        if self.metrics.state != PoolState.RUNNING:
            raise RuntimeError(f"Thread pool {self.name} is not running")

        with self._lock:
            # 生成任务ID
            self._task_counter += 1
            task_id = f"{self.name}-task-{self._task_counter}"

            # 创建任务包装器
            wrapper = TaskWrapper(task_id, func, args, kwargs)

            # 包装执行函数
            def wrapped_execute():
                try:
                    self._active_tasks[task_id] = wrapper
                    result = wrapper.execute()
                    return result
                finally:
                    # 无论成功还是失败,都增加完成计数
                    self.metrics.completed_task_count += 1
                    # 清理活跃任务
                    self._active_tasks.pop(task_id, None)
                    # 保存已完成任务(限制数量)
                    self._completed_tasks.append(wrapper)
                    if len(self._completed_tasks) > 1000:  # 默认最大历史记录数
                        self._completed_tasks = self._completed_tasks[-500:]  # 保留最近500个

            # 提交到线程池
            future = self._executor.submit(wrapped_execute)
            self.metrics.task_count += 1

            return future

    def shutdown(self, wait: bool = True, timeout: Optional[float] = None):
        """关闭线程池 - 简化版本"""
        with self._lock:
            if self.metrics.state == PoolState.SHUTDOWN:
                return

            self.metrics.state = PoolState.SHUTDOWN

            try:
                # 设置关闭事件
                self._shutdown_event.set()

                # 取消队列中的任务
                cancelled_count = self._cancel_pending_tasks()

                # 关闭执行器
                if self._executor:
                    self._executor.shutdown(wait=wait)

                # 等待监控线程结束
                if self._monitor_thread and self._monitor_thread.is_alive():
                    self._monitor_thread.join(timeout=timeout or 5.0)

                # 清理活动任务
                self._active_tasks.clear()

                self.metrics.state = PoolState.TERMINATED

                # 简单的日志记录
                if cancelled_count > 0:
                    print(f"Pool '{self.name}' shutdown: cancelled {cancelled_count} pending tasks")

            except KeyboardInterrupt:
                # 强制设置为已终止状态
                self.metrics.state = PoolState.TERMINATED
                raise
            except Exception as e:
                # 清理失败,但仍然标记为已终止
                self.metrics.state = PoolState.TERMINATED
                print(f"Pool '{self.name}' shutdown failed: {e}")
                raise

    def _cancel_pending_tasks(self) -> int:
        """取消队列中的待处理任务"""
        cancelled_count = 0
        while not self._task_queue.empty():
            try:
                task = self._task_queue.get_nowait()
                if hasattr(task, 'cancel'):
                    task.cancel()
                cancelled_count += 1
            except:
                break
        return cancelled_count

    def get_metrics(self) -> ThreadPoolMetrics:
        """获取指标"""
        self._update_metrics()
        return self.metrics

    def is_shutdown(self) -> bool:
        """是否已关闭"""
        return self.metrics.state in (PoolState.SHUTDOWN, PoolState.TERMINATED)

    def __enter__(self):
        # 在进入上下文时初始化线程池
        if self._executor is None:
            self._initialize_pool()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


class ThreadPoolManager(metaclass=SingletonMeta):
    """线程池管理器

    使用标准单例模式的线程池管理器,提供统一的线程池创建、管理和监控功能.
    """

    def __init__(self):
        if hasattr(self, "_initialized"):
            return

        self._pools: dict[str, ManagedThreadPool] = {}
        self._default_config = ThreadPoolConfig()
        self._lock = threading.RLock()
        self._shutdown_hooks: list[Callable] = []
        self._global_metrics = {"total_pools": 0, "active_pools": 0, "total_tasks": 0, "completed_tasks": 0, "failed_tasks": 0}
        self._initialized = True

        # 注册关闭钩子
        import atexit

        atexit.register(self.shutdown_all)

    def create_pool(self, name: str, config: Optional[ThreadPoolConfig] = None) -> ManagedThreadPool:
        """创建线程池"""
        with self._lock:
            if name in self._pools:
                raise ValueError(f"Thread pool '{name}' already exists")

            if config is None:
                config = ThreadPoolConfig()

            pool = ManagedThreadPool(config, name=name)
            self._pools[name] = pool
            self._global_metrics["total_pools"] += 1
            self._global_metrics["active_pools"] += 1

            return pool

    def get_pool(self, name: str) -> Optional[ManagedThreadPool]:
        """获取线程池"""
        return self._pools.get(name)

    def ensure_pool(self, name: str, config: Optional[ThreadPoolConfig] = None) -> ManagedThreadPool:
        """获取或创建线程池"""
        pool = self.get_pool(name)
        if pool is None:
            pool = self.create_pool(name, config)
        return pool



    def remove_pool(self, name: str, shutdown: bool = True) -> bool:
        """移除线程池"""
        with self._lock:
            pool = self._pools.pop(name, None)
            if pool:
                if shutdown and not pool.is_shutdown():
                    pool.shutdown()
                self._global_metrics["active_pools"] -= 1
                return True
            return False

    def pools(self) -> list[str]:
        """列出所有线程池名称"""
        return list(self._pools.keys())

    def list_pools(self) -> list[str]:
        """列出所有线程池名称 (别名方法，用于测试兼容性)"""
        return self.pools()

    def metrics(self) -> dict[str, Any]:
        """获取所有指标"""
        with self._lock:
            pool_metrics = {}
            total_tasks = 0
            completed_tasks = 0

            for name, pool in self._pools.items():
                metrics = pool.get_metrics()
                pool_metrics[name] = metrics.to_dict()
                total_tasks += metrics.task_count
                completed_tasks += metrics.completed_task_count

            self._global_metrics.update({"total_tasks": total_tasks, "completed_tasks": completed_tasks})

            return {"global_metrics": self._global_metrics.copy(), "pool_metrics": pool_metrics}

    def submit(self, pool_name: str, func: Callable, *args, **kwargs) -> Future:
        """向指定线程池提交任务"""
        pool = self.get_pool(pool_name)
        if pool is None:
            raise ValueError(f"Thread pool '{pool_name}' not found")
        return pool.submit(func, *args, **kwargs)

    def submit_default(self, func: Callable, *args, **kwargs) -> Future:
        """向默认线程池提交任务"""
        default_pool = self.ensure_pool("default", self._default_config)
        return default_pool.submit(func, *args, **kwargs)

    def submit_to_default(self, func: Callable, *args, **kwargs) -> Future:
        """向默认线程池提交任务 (别名方法，用于测试兼容性)"""
        return self.submit_default(func, *args, **kwargs)



    def shutdown_pool(self, name: str, wait: bool = True, timeout: Optional[float] = None):
        """关闭指定线程池"""
        with self._lock:
            pool = self.get_pool(name)
            if pool:
                pool.shutdown(wait=wait, timeout=timeout)
                # 从字典中移除已关闭的线程池
                self._pools.pop(name, None)
                self._global_metrics["active_pools"] -= 1

    def shutdown_all(self, wait: bool = True, timeout: Optional[float] = None):
        """关闭所有线程池 - 简化版本"""
        with self._lock:
            try:
                # 直接关闭所有线程池
                for pool_name, pool in list(self._pools.items()):
                    if not pool.is_shutdown():
                        pool.shutdown(wait=wait, timeout=timeout)

                # 清空线程池字典
                self._pools.clear()
                self._global_metrics["active_pools"] = 0

                # 执行关闭钩子
                for hook in self._shutdown_hooks:
                    with contextlib.suppress(Exception):
                        hook()

            except KeyboardInterrupt:
                # 强制清理状态
                self._pools.clear()
                self._global_metrics["active_pools"] = 0
                raise

    def add_shutdown_hook(self, hook: Callable):
        """添加关闭钩子"""
        self._shutdown_hooks.append(hook)



    def verify_resource_cleanup(self) -> dict[str, Any]:
        """验证资源清理状态"""
        verification_result = {
            "pools_count": len(self._pools),
            "active_pools": self._global_metrics["active_pools"],
            "pools_status": {},
            "resource_leaks": [],
        }

        # 检查每个线程池的状态
        for pool_name, pool in self._pools.items():
            pool_status = {
                "state": pool.metrics.state.value,
                "is_shutdown": pool.is_shutdown(),
                "active_tasks": len(pool._active_tasks),
                "queue_size": pool._task_queue.qsize() if hasattr(pool._task_queue, "qsize") else 0,
                "monitor_thread_alive": pool._monitor_thread.is_alive() if pool._monitor_thread else False,
            }
            verification_result["pools_status"][pool_name] = pool_status

            # 检查潜在的资源泄漏
            if not pool.is_shutdown():
                verification_result["resource_leaks"].append(f"Pool '{pool_name}' not shutdown")
            if pool._monitor_thread and pool._monitor_thread.is_alive():
                verification_result["resource_leaks"].append(f"Pool '{pool_name}' monitor thread still alive")
            if pool._active_tasks:
                verification_result["resource_leaks"].append(f"Pool '{pool_name}' has {len(pool._active_tasks)} active tasks")

        return verification_result

    def set_default_config(self, config: ThreadPoolConfig):
        """设置默认配置"""
        self._default_config = config

    def health_check(self) -> dict[str, Any]:
        """健康检查"""
        with self._lock:
            healthy_pools = 0
            unhealthy_pools = 0

            for pool in self._pools.values():
                # CREATED 和 RUNNING 状态都视为健康
                if pool.metrics.state in (PoolState.CREATED, PoolState.RUNNING):
                    healthy_pools += 1
                else:
                    unhealthy_pools += 1

            return {
                "status": "healthy" if unhealthy_pools == 0 else "degraded",
                "total_pools": len(self._pools),
                "healthy_pools": healthy_pools,
                "unhealthy_pools": unhealthy_pools,
                "global_metrics": self._global_metrics.copy(),
            }

    def cleanup(self) -> None:
        """清理资源

        SingletonMeta 在重置实例时会自动调用此方法.
        确保所有线程池被正确关闭,资源被释放.
        """
        try:
            self.shutdown_all()
        except Exception as e:
            # 记录清理错误但不抛出异常
            from loguru import logger

            logger.warning(f"Error during ThreadPoolManager cleanup: {e}")
