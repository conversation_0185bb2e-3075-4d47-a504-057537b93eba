#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置优先级机制集成测试
"""

import os
import tempfile
import unittest
from pathlib import Path

from miniboot.env import ConfigurationLoader, ConfigurationPriority, MutablePropertySources, StandardEnvironment, get_priority_manager


class ConfigurationPriorityIntegrationTestCase(unittest.TestCase):
    """配置优先级机制集成测试"""

    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)

        # 创建测试配置文件
        self._create_test_config_files()

        # 设置环境变量
        os.environ["MINIBOOT_APPLICATION_NAME"] = "env-test-app"
        os.environ["MINIBOOT_SERVER_PORT"] = "9999"

    def tearDown(self):
        """清理测试环境"""
        # 清理环境变量
        os.environ.pop("MINIBOOT_APPLICATION_NAME", None)
        os.environ.pop("MINIBOOT_SERVER_PORT", None)

        # 清理临时文件
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def _create_test_config_files(self):
        """创建测试配置文件"""
        # 用户默认配置
        user_config = self.temp_path / "application.yml"
        user_config.write_text("""
miniboot:
  application:
    name: user-app
    version: 1.0.0
  server:
    port: 8080
    host: localhost
  database:
    url: user-database-url
""")

        # 用户Profile配置
        user_profile_config = self.temp_path / "application-dev.yml"
        user_profile_config.write_text("""
miniboot:
  application:
    name: user-dev-app
  server:
    port: 8081
  database:
    url: dev-database-url
    pool:
      max-size: 20
""")

        # 框架默认配置（模拟）
        framework_dir = self.temp_path / "miniboot" / "resources"
        framework_dir.mkdir(parents=True, exist_ok=True)

        framework_config = framework_dir / "application.yml"
        framework_config.write_text("""
miniboot:
  application:
    name: framework-app
    description: Framework default application
  server:
    port: 8000
    host: 0.0.0.0
    timeout: 30
  database:
    url: framework-database-url
    driver: sqlite
    pool:
      min-size: 5
      max-size: 10
""")

    def test_configuration_priority_order(self):
        """测试配置优先级顺序"""
        # 创建配置加载器，指定搜索路径
        search_paths = [str(self.temp_path), str(self.temp_path / "miniboot" / "resources")]
        loader = ConfigurationLoader(search_paths)

        # 创建属性源管理器
        property_sources = MutablePropertySources()

        # 加载配置（模拟dev profile）
        loader.load_configuration(property_sources, {"dev"})

        # 验证优先级顺序
        sources = property_sources._property_sources
        priorities = [source.priority for source in sources]

        # 验证优先级是降序排列
        priority_manager = get_priority_manager()
        self.assertTrue(priority_manager.validate_priority_order(priorities))

        # 验证命令行参数具有最高优先级（如果存在）
        cmd_source = next((s for s in sources if s.name == "commandLine"), None)
        if cmd_source:
            self.assertEqual(cmd_source.priority, ConfigurationPriority.COMMAND_LINE)

        # 验证至少有一些配置源被加载
        self.assertGreater(len(sources), 0)

    def test_user_configuration_overrides_framework(self):
        """测试用户配置覆盖框架配置"""
        # 创建新的属性源管理器（不包含系统环境变量）
        from miniboot.env.sources import MapPropertySource, MutablePropertySources

        property_sources = MutablePropertySources()

        # 添加框架配置（低优先级）
        framework_config = {
            "miniboot.application.name": "framework-app",
            "miniboot.server.port": "8000",
            "miniboot.server.timeout": "30",
        }
        framework_source = MapPropertySource("framework", framework_config, ConfigurationPriority.FRAMEWORK_DEFAULT)
        property_sources.add_last(framework_source)

        # 添加用户配置（高优先级）
        user_config = {
            "miniboot.application.name": "user-app",
            "miniboot.server.port": "8080",
        }
        user_source = MapPropertySource("user", user_config, ConfigurationPriority.USER_DEFAULT)
        property_sources.add_first(user_source)

        # 验证用户配置覆盖框架配置
        self.assertEqual(property_sources.get_property("miniboot.application.name"), "user-app")
        self.assertEqual(property_sources.get_property("miniboot.server.port"), "8080")

        # 验证框架配置中用户未设置的属性仍然有效
        self.assertEqual(property_sources.get_property("miniboot.server.timeout"), "30")

    def test_environment_variables_highest_priority(self):
        """测试环境变量具有最高优先级"""
        # 创建标准环境
        env = StandardEnvironment()

        # 添加用户配置
        from miniboot.env.sources import MapPropertySource

        user_config = {
            "miniboot.application.name": "user-app",
            "miniboot.server.port": "8080",
        }
        user_source = MapPropertySource("user", user_config, ConfigurationPriority.USER_DEFAULT)
        env._property_sources.add_last(user_source)

        # 环境变量应该覆盖用户配置
        self.assertEqual(env.get_property("miniboot.application.name"), "env-test-app")
        self.assertEqual(env.get_property("miniboot.server.port"), "9999")

    def test_profile_specific_configuration_priority(self):
        """测试Profile特定配置优先级"""
        # 创建新的属性源管理器（不包含系统环境变量）
        from miniboot.env.sources import MapPropertySource, MutablePropertySources

        property_sources = MutablePropertySources()

        # 添加默认配置
        default_config = {
            "miniboot.application.name": "default-app",
            "miniboot.server.port": "8080",
        }
        default_source = MapPropertySource("default", default_config, ConfigurationPriority.USER_DEFAULT)
        property_sources.add_last(default_source)

        # 添加Profile特定配置
        profile_config = {
            "miniboot.application.name": "dev-app",
        }
        profile_source = MapPropertySource("profile-dev", profile_config, ConfigurationPriority.USER_PROFILE_SPECIFIC)
        property_sources.add_first(profile_source)

        # 验证Profile配置覆盖默认配置
        self.assertEqual(property_sources.get_property("miniboot.application.name"), "dev-app")
        # 验证默认配置中Profile未设置的属性仍然有效
        self.assertEqual(property_sources.get_property("miniboot.server.port"), "8080")

    def test_configuration_search_path_priority(self):
        """测试配置文件搜索路径优先级"""
        priority_manager = get_priority_manager()
        search_path = priority_manager.get_search_path()

        # 验证用户路径优先于框架路径
        user_paths = search_path.get_user_paths()
        framework_paths = search_path.get_framework_paths()
        all_paths = search_path.get_all_paths()

        # 用户路径应该在框架路径之前
        user_start = min(all_paths.index(path) for path in user_paths if path in all_paths)
        framework_start = min(all_paths.index(path) for path in framework_paths if path in all_paths)

        self.assertLess(user_start, framework_start)

    def test_merge_strategy_application(self):
        """测试合并策略应用"""
        priority_manager = get_priority_manager()

        # 测试默认覆盖策略
        strategy = priority_manager.get_merge_strategy("miniboot.application.name")
        self.assertEqual(strategy, "override")

        # 测试特定属性的追加策略
        strategy = priority_manager.get_merge_strategy("miniboot.web.cors.allowed-origins")
        self.assertEqual(strategy, "append")

        # 测试嵌套对象的合并策略
        strategy = priority_manager.get_merge_strategy("miniboot.logging.loggers")
        self.assertEqual(strategy, "merge")


if __name__ == "__main__":
    unittest.main()
