#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 缓存管理器单元测试 - 基于实际API
"""

import contextlib
import threading
import time
import unittest

from miniboot.bean.cache import (
    CacheManager,
    BeanAccessPattern,
    MultiLevelCacheManager
)


class TestService:
    """测试用的服务类"""
    def __init__(self, name: str = "test"):
        self.name = name


class CacheManagerTestCase(unittest.TestCase):
    """CacheManager测试 - 基于实际API"""

    def setUp(self):
        """测试前准备"""
        self.cache_manager = CacheManager()

    def tearDown(self):
        """测试后清理"""
        self.cache_manager.clear()
        self.cache_manager = None

    def test_cache_manager_initialization(self):
        """测试缓存管理器初始化"""
        cache = CacheManager()

        # 检查内部组件已初始化
        self.assertIsNotNone(cache._advanced_cache)
        self.assertIsNotNone(cache._access_analyzer)

        # 检查统计信息可以获取
        stats = cache.get_cache_stats()
        self.assertIsInstance(stats, dict)

    def test_put_bean(self):
        """测试存储Bean"""
        service = TestService("test")

        self.cache_manager.put_bean("testBean", service)

        # 验证Bean已存储（通过获取来验证）
        retrieved = self.cache_manager.get_bean("testBean")
        self.assertIs(retrieved, service)

    def test_get_bean_hit(self):
        """测试获取Bean - 缓存命中"""
        service = TestService("test")
        self.cache_manager.put_bean("testBean", service)

        retrieved = self.cache_manager.get_bean("testBean")

        self.assertIs(retrieved, service)

    def test_get_bean_miss(self):
        """测试获取Bean - 缓存未命中"""
        retrieved = self.cache_manager.get_bean("nonExistentBean")

        self.assertIsNone(retrieved)

    def test_remove_bean(self):
        """测试移除Bean"""
        service = TestService("test")
        self.cache_manager.put_bean("testBean", service)

        # 确认Bean存在
        self.assertIsNotNone(self.cache_manager.get_bean("testBean"))

        # 移除Bean
        self.cache_manager.remove_bean("testBean")

        # 确认Bean已移除
        self.assertIsNone(self.cache_manager.get_bean("testBean"))

    def test_clear_cache(self):
        """测试清空缓存"""
        # 存储一些Bean
        service1 = TestService("test1")
        service2 = TestService("test2")

        self.cache_manager.put_bean("bean1", service1)
        self.cache_manager.put_bean("bean2", service2)

        # 确认Bean存在
        self.assertIsNotNone(self.cache_manager.get_bean("bean1"))
        self.assertIsNotNone(self.cache_manager.get_bean("bean2"))

        # 清空缓存
        self.cache_manager.clear()

        # 确认Bean已清空
        self.assertIsNone(self.cache_manager.get_bean("bean1"))
        self.assertIsNone(self.cache_manager.get_bean("bean2"))

    def test_get_cache_stats(self):
        """测试获取缓存统计信息"""
        stats = self.cache_manager.get_cache_stats()

        self.assertIsInstance(stats, dict)
        # 统计信息应该包含一些基本字段
        # 具体字段取决于实现，这里只验证返回类型

    def test_get_bean_info(self):
        """测试获取Bean信息"""
        service = TestService("test")
        self.cache_manager.put_bean("testBean", service)

        # 获取Bean信息
        info = self.cache_manager.get_bean_info("testBean")

        # 可能返回None或dict，取决于实现
        if info is not None:
            self.assertIsInstance(info, dict)

    def test_rebalance_cache(self):
        """测试重新平衡缓存"""
        # 存储一些Bean
        for i in range(5):
            service = TestService(f"test{i}")
            self.cache_manager.put_bean(f"bean{i}", service)

        # 重新平衡缓存（不应该抛出异常）
        try:
            self.cache_manager.rebalance_cache()
        except Exception as e:
            self.fail(f"rebalance_cache() raised {e} unexpectedly!")

    def test_background_tasks(self):
        """测试后台任务管理 - 同步环境版本"""
        # 在同步环境中，后台任务应该能够优雅地处理或提供替代方案
        try:
            # 尝试启动后台任务
            self.cache_manager.start_background_tasks()
            # 如果成功启动，则停止
            self.cache_manager.stop_background_tasks()
        except RuntimeError as e:
            # 如果是异步相关错误，验证错误消息是合理的
            if "no running event loop" in str(e) or "async" in str(e).lower():
                # 这是预期的行为 - 在同步环境中无法启动异步后台任务
                # 验证缓存管理器仍然可以正常工作
                self.assertIsNotNone(self.cache_manager)
                # 测试基本缓存功能仍然可用
                self.cache_manager.put_bean("test_key", "test_value")
                self.assertEqual(self.cache_manager.get_bean("test_key"), "test_value")
            else:
                raise
        except Exception as e:
            self.fail(f"Unexpected error in background tasks: {e}")

    def test_export_cache_report(self):
        """测试导出缓存报告"""
        # 存储一些Bean
        service = TestService("test")
        self.cache_manager.put_bean("testBean", service)

        # 导出报告
        report = self.cache_manager.export_cache_report()

        self.assertIsInstance(report, dict)

    def test_multiple_beans_storage_and_retrieval(self):
        """测试多个Bean的存储和获取"""
        services = {}

        # 存储多个Bean
        for i in range(10):
            service = TestService(f"test{i}")
            bean_name = f"bean{i}"
            services[bean_name] = service
            self.cache_manager.put_bean(bean_name, service)

        # 验证所有Bean都能正确获取
        for bean_name, expected_service in services.items():
            retrieved = self.cache_manager.get_bean(bean_name)
            self.assertIs(retrieved, expected_service)

    def test_bean_overwrite(self):
        """测试Bean覆盖"""
        service1 = TestService("test1")
        service2 = TestService("test2")

        # 存储第一个Bean
        self.cache_manager.put_bean("testBean", service1)
        retrieved1 = self.cache_manager.get_bean("testBean")
        self.assertIs(retrieved1, service1)

        # 覆盖Bean
        self.cache_manager.put_bean("testBean", service2)
        retrieved2 = self.cache_manager.get_bean("testBean")
        self.assertIs(retrieved2, service2)

    def test_thread_safety_basic(self):
        """测试基本线程安全性"""
        results = []
        errors = []

        def cache_operations(thread_id):
            try:
                # 存储Bean
                service = TestService(f"test{thread_id}")
                bean_name = f"bean{thread_id}"
                self.cache_manager.put_bean(bean_name, service)

                # 获取Bean
                retrieved = self.cache_manager.get_bean(bean_name)
                if retrieved is service:
                    results.append(thread_id)
                else:
                    errors.append(f"Thread {thread_id}: Bean mismatch")

            except Exception as e:
                errors.append(f"Thread {thread_id}: {e}")

        # 创建多个线程
        threads = [
            threading.Thread(target=cache_operations, args=(i,))
            for i in range(5)
        ]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # 检查结果
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(results), 5)

    def test_cache_performance_basic(self):
        """测试缓存基本性能"""
        service = TestService("test")

        # 测试存储性能
        start_time = time.time()
        for i in range(100):
            self.cache_manager.put_bean(f"bean{i}", service)
        put_time = time.time() - start_time

        # 测试获取性能
        start_time = time.time()
        for i in range(100):
            self.cache_manager.get_bean(f"bean{i}")
        get_time = time.time() - start_time

        # 性能检查（应该在合理时间内完成）
        self.assertLess(put_time, 1.0)  # 存储100个Bean应该在1秒内完成
        self.assertLess(get_time, 1.0)  # 获取100个Bean应该在1秒内完成

    def test_cache_with_none_values(self):
        """测试缓存None值的处理"""
        # 尝试存储None值
        self.cache_manager.put_bean("nullBean", None)

        # 获取None值
        retrieved = self.cache_manager.get_bean("nullBean")
        self.assertIsNone(retrieved)

    def test_cache_with_different_types(self):
        """测试缓存不同类型的对象"""
        # 存储不同类型的对象
        objects = {
            "string": "test_string",
            "number": 42,
            "list": [1, 2, 3],
            "dict": {"key": "value"},
            "service": TestService("test")
        }

        for name, obj in objects.items():
            self.cache_manager.put_bean(name, obj)

        # 验证所有对象都能正确获取
        for name, expected_obj in objects.items():
            retrieved = self.cache_manager.get_bean(name)
            self.assertEqual(retrieved, expected_obj)


# ==================== Bean访问模式分析测试 ====================

class BeanAccessPatternTestCase(unittest.TestCase):
    """Bean访问模式分析测试"""

    def setUp(self):
        self.pattern = BeanAccessPattern("testBean")

    def test_pattern_initialization(self):
        """测试访问模式初始化"""
        self.assertEqual(self.pattern.name, "testBean")
        self.assertEqual(self.pattern.access_count, 0)
        self.assertIsInstance(self.pattern.last_access_time, float)
        self.assertIsInstance(self.pattern.first_access_time, float)
        self.assertEqual(len(self.pattern.access_intervals), 0)

    def test_record_access(self):
        """测试记录访问"""
        initial_count = self.pattern.access_count
        initial_time = self.pattern.last_access_time

        # 记录第一次访问
        self.pattern.record_access()

        self.assertEqual(self.pattern.access_count, initial_count + 1)
        self.assertGreaterEqual(self.pattern.last_access_time, initial_time)

        # 记录第二次访问
        time.sleep(0.01)  # 确保时间间隔
        self.pattern.record_access()

        self.assertEqual(self.pattern.access_count, initial_count + 2)
        self.assertEqual(len(self.pattern.access_intervals), 1)
        self.assertGreater(self.pattern.access_intervals[0], 0)

    def test_access_frequency_calculation(self):
        """测试访问频率计算"""
        # 初始状态频率为0
        self.assertEqual(self.pattern.get_access_frequency(), 0.0)

        # 记录访问
        self.pattern.record_access()
        time.sleep(0.1)
        self.pattern.record_access()

        frequency = self.pattern.get_access_frequency()
        self.assertGreater(frequency, 0)
        self.assertLess(frequency, 100)  # 合理范围

    def test_average_interval_calculation(self):
        """测试平均访问间隔计算"""
        # 初始状态为无穷大
        self.assertEqual(self.pattern.get_average_interval(), float("inf"))

        # 记录多次访问
        self.pattern.record_access()
        time.sleep(0.05)
        self.pattern.record_access()
        time.sleep(0.05)
        self.pattern.record_access()

        avg_interval = self.pattern.get_average_interval()
        self.assertGreater(avg_interval, 0)
        self.assertLess(avg_interval, 1)  # 应该在合理范围内

    def test_hot_bean_detection(self):
        """测试热点Bean检测"""
        # 模拟高频访问
        for _ in range(10):
            self.pattern.record_access()
            time.sleep(0.01)  # 短间隔

        # 检查是否被识别为热点
        # 注意：由于时间间隔很短，可能不会被识别为热点
        # 这里主要测试方法不抛出异常
        is_hot = self.pattern.is_hot()
        self.assertIsInstance(is_hot, bool)

    def test_warm_bean_detection(self):
        """测试温Bean检测"""
        # 模拟中等频率访问
        for _ in range(5):
            self.pattern.record_access()
            time.sleep(0.02)

        is_warm = self.pattern.is_warm()
        self.assertIsInstance(is_warm, bool)

    def test_cold_bean_detection(self):
        """测试冷Bean检测"""
        # 模拟低频访问
        self.pattern.record_access()
        time.sleep(0.1)
        self.pattern.record_access()

        is_cold = self.pattern.is_cold()
        self.assertIsInstance(is_cold, bool)

    def test_access_intervals_limit(self):
        """测试访问间隔数量限制"""
        # 记录超过20次访问
        for _ in range(25):
            self.pattern.record_access()
            time.sleep(0.001)

        # 验证只保留最近20次间隔
        self.assertLessEqual(len(self.pattern.access_intervals), 20)


# ==================== 多级缓存管理器测试 ====================

class MultiLevelCacheManagerTestCase(unittest.TestCase):
    """多级缓存管理器测试"""

    def setUp(self):
        self.cache_manager = MultiLevelCacheManager()

    def tearDown(self):
        with contextlib.suppress(Exception):
            self.cache_manager.clear()

    def test_cache_manager_initialization(self):
        """测试缓存管理器初始化"""
        self.assertIsNotNone(self.cache_manager._l1_cache)
        self.assertIsNotNone(self.cache_manager._l2_cache)
        self.assertIsNotNone(self.cache_manager._l3_cache)
        self.assertIsNotNone(self.cache_manager._access_patterns)

    def test_put_and_get_bean(self):
        """测试Bean存储和获取"""
        service = TestService("test1")

        # 存储Bean
        self.cache_manager.put_bean("testBean", service)

        # 获取Bean
        retrieved = self.cache_manager.get_bean("testBean")
        self.assertIs(retrieved, service)

    def test_bean_not_found(self):
        """测试获取不存在的Bean"""
        result = self.cache_manager.get_bean("nonExistentBean")
        self.assertIsNone(result)

    def test_cache_level_promotion(self):
        """测试缓存级别提升"""
        service = TestService("hotBean")

        # 存储Bean（自动选择级别）
        self.cache_manager.put_bean("hotBean", service)

        # 多次访问触发提升
        for _ in range(10):
            retrieved = self.cache_manager.get_bean("hotBean")
            self.assertIs(retrieved, service)
            time.sleep(0.001)

        # 验证访问模式被记录
        self.assertIn("hotBean", self.cache_manager._access_patterns)
        pattern = self.cache_manager._access_patterns["hotBean"]
        self.assertGreater(pattern.access_count, 5)

    def test_cache_statistics(self):
        """测试缓存统计"""
        # 添加一些Bean
        for i in range(5):
            service = TestService(f"service_{i}")
            self.cache_manager.put_bean(f"bean_{i}", service)

        # 获取统计信息
        stats = self.cache_manager.get_cache_stats()

        self.assertIsInstance(stats, dict)
        self.assertIn("cache_levels", stats)
        self.assertIn("level_distribution", stats)

        # 验证缓存级别信息
        cache_levels = stats["cache_levels"]
        self.assertIn("total_beans", cache_levels)
        self.assertGreater(cache_levels["total_beans"], 0)

    def test_clear_specific_level(self):
        """测试清理特定级别缓存"""
        # 添加Bean到不同级别
        service1 = TestService("service1")
        service2 = TestService("service2")

        self.cache_manager.put_bean("bean1", service1)
        self.cache_manager.put_bean("bean2", service2)

        # 清理所有缓存（简化测试）
        self.cache_manager.clear()

        # 验证所有Bean都被清理
        self.assertIsNone(self.cache_manager.get_bean("bean1"))
        self.assertIsNone(self.cache_manager.get_bean("bean2"))

    def test_clear_all_levels(self):
        """测试清理所有级别缓存"""
        # 添加Bean
        for i in range(3):
            service = TestService(f"service_{i}")
            self.cache_manager.put_bean(f"bean_{i}", service)

        # 清理所有缓存
        self.cache_manager.clear()

        # 验证所有Bean都被清理
        for i in range(3):
            self.assertIsNone(self.cache_manager.get_bean(f"bean_{i}"))

    def test_cache_size_limits(self):
        """测试缓存大小限制"""
        # 添加大量Bean测试容量限制
        services = []
        for i in range(150):  # 超过默认L1容量
            service = TestService(f"service_{i}")
            services.append(service)
            self.cache_manager.put_bean(f"bean_{i}", service)

        # 验证缓存管理器仍然正常工作
        stats = self.cache_manager.get_cache_stats()
        self.assertIsInstance(stats, dict)
        self.assertGreater(stats["cache_levels"]["total_beans"], 0)


if __name__ == '__main__':
    unittest.main()
