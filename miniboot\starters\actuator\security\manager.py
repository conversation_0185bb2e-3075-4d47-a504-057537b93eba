#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Actuator 安全管理器 - 生产环境安全配置和最佳实践

提供生产环境级别的安全配置管理,包括:
- 敏感端点保护
- 访问控制和认证
- 安全配置最佳实践
- 生产环境安全策略
- 安全审计和监控
"""

import hashlib
import hmac
import secrets
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

from loguru import logger

from ..properties import SecurityProperties


class SecurityLevel(Enum):
    """安全级别枚举"""

    DEVELOPMENT = "development"  # 开发环境 - 最低安全级别
    TESTING = "testing"  # 测试环境 - 中等安全级别
    STAGING = "staging"  # 预发布环境 - 高安全级别
    PRODUCTION = "production"  # 生产环境 - 最高安全级别


class EndpointSensitivity(Enum):
    """端点敏感性级别"""

    PUBLIC = "public"  # 公开端点,无需认证
    INTERNAL = "internal"  # 内部端点,需要基础认证
    SENSITIVE = "sensitive"  # 敏感端点,需要强认证
    CRITICAL = "critical"  # 关键端点,需要最高级别认证


class SecurityPolicy:
    """安全策略配置"""

    # 端点敏感性映射
    ENDPOINT_SENSITIVITY = {
        "health": EndpointSensitivity.PUBLIC,
        "info": EndpointSensitivity.INTERNAL,
        "metrics": EndpointSensitivity.SENSITIVE,
        "env": EndpointSensitivity.CRITICAL,
        "loggers": EndpointSensitivity.CRITICAL,
        "threaddump": EndpointSensitivity.CRITICAL,
        "beans": EndpointSensitivity.SENSITIVE,
        "configprops": EndpointSensitivity.CRITICAL,
        "mappings": EndpointSensitivity.SENSITIVE,
        "scheduledtasks": EndpointSensitivity.SENSITIVE,
        "shutdown": EndpointSensitivity.CRITICAL,
    }

    # 安全级别对应的策略
    SECURITY_POLICIES = {
        SecurityLevel.DEVELOPMENT: {
            "require_auth": False,
            "allowed_endpoints": ["health", "info", "metrics", "env", "loggers", "threaddump"],
            "session_timeout": 3600,  # 1小时
            "max_failed_attempts": 10,
            "lockout_duration": 300,  # 5分钟
            "require_https": False,
            "cors_enabled": True,
        },
        SecurityLevel.TESTING: {
            "require_auth": True,
            "allowed_endpoints": ["health", "info", "metrics"],
            "session_timeout": 1800,  # 30分钟
            "max_failed_attempts": 5,
            "lockout_duration": 600,  # 10分钟
            "require_https": False,
            "cors_enabled": True,
        },
        SecurityLevel.STAGING: {
            "require_auth": True,
            "allowed_endpoints": ["health", "info", "metrics"],
            "session_timeout": 900,  # 15分钟
            "max_failed_attempts": 3,
            "lockout_duration": 1800,  # 30分钟
            "require_https": True,
            "cors_enabled": False,
        },
        SecurityLevel.PRODUCTION: {
            "require_auth": True,
            "allowed_endpoints": ["health"],  # 生产环境只允许健康检查
            "session_timeout": 300,  # 5分钟
            "max_failed_attempts": 3,
            "lockout_duration": 3600,  # 1小时
            "require_https": True,
            "cors_enabled": False,
        },
    }


class SecurityToken:
    """安全令牌"""

    def __init__(self, username: str, expires_at: datetime, permissions: Set[str]):
        """初始化安全令牌

        Args:
            username: 用户名
            expires_at: 过期时间
            permissions: 权限集合
        """
        self.username = username
        self.expires_at = expires_at
        self.permissions = permissions
        self.created_at = datetime.now()
        self.token_id = secrets.token_urlsafe(32)

    def is_valid(self) -> bool:
        """检查令牌是否有效"""
        return datetime.now() < self.expires_at

    def has_permission(self, endpoint: str) -> bool:
        """检查是否有访问指定端点的权限"""
        return endpoint in self.permissions or "*" in self.permissions

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "token_id": self.token_id,
            "username": self.username,
            "created_at": self.created_at.isoformat(),
            "expires_at": self.expires_at.isoformat(),
            "permissions": list(self.permissions),
            "valid": self.is_valid(),
        }


class SecurityAuditLog:
    """安全审计日志"""

    def __init__(self):
        """初始化审计日志"""
        self.logs: List[Dict[str, Any]] = []
        self.max_logs = 1000  # 最大日志条数

    def log_access_attempt(
        self, username: Optional[str], endpoint: str, success: bool, ip_address: str = "unknown", user_agent: str = "unknown"
    ) -> None:
        """记录访问尝试"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": "access_attempt",
            "username": username,
            "endpoint": endpoint,
            "success": success,
            "ip_address": ip_address,
            "user_agent": user_agent,
        }

        self._add_log(log_entry)

        if success:
            logger.info(f"Access granted: {username} -> {endpoint} from {ip_address}")
        else:
            logger.warning(f"Access denied: {username} -> {endpoint} from {ip_address}")

    def log_authentication(self, username: str, success: bool, ip_address: str = "unknown") -> None:
        """记录认证尝试"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": "authentication",
            "username": username,
            "success": success,
            "ip_address": ip_address,
        }

        self._add_log(log_entry)

        if success:
            logger.info(f"Authentication successful: {username} from {ip_address}")
        else:
            logger.warning(f"Authentication failed: {username} from {ip_address}")

    def log_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """记录安全事件"""
        log_entry = {"timestamp": datetime.now().isoformat(), "event_type": event_type, **details}

        self._add_log(log_entry)
        logger.warning(f"Security event: {event_type} - {details}")

    def _add_log(self, log_entry: Dict[str, Any]) -> None:
        """添加日志条目"""
        self.logs.append(log_entry)

        # 保持日志数量在限制内
        if len(self.logs) > self.max_logs:
            self.logs = self.logs[-self.max_logs :]

    def get_recent_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取最近的日志"""
        return self.logs[-limit:]

    def get_failures(self, username: str, since: datetime) -> int:
        """获取指定时间以来的失败尝试次数"""
        count = 0
        for log in self.logs:
            if log.get("username") == username and log.get("success") is False and datetime.fromisoformat(log["timestamp"]) > since:
                count += 1
        return count


class ProductionSecurityManager:
    """生产环境安全管理器"""

    def __init__(self, security_config: SecurityProperties, security_level: SecurityLevel = SecurityLevel.PRODUCTION):
        """初始化生产环境安全管理器

        Args:
            security_config: 安全配置
            security_level: 安全级别
        """
        self.config = security_config
        self.security_level = security_level
        self.policy = SecurityPolicy.SECURITY_POLICIES[security_level]

        # 安全状态
        self.active_tokens: Dict[str, SecurityToken] = {}
        self.locked_users: Dict[str, datetime] = {}
        self.audit_log = SecurityAuditLog()

        # 安全统计
        self._total_requests = 0
        self._successful_auths = 0
        self._failed_auths = 0
        self._blocked_requests = 0

        logger.info(f"ProductionSecurityManager initialized with {security_level.value} level")

    def authenticate(self, username: str, password: str, ip_address: str = "unknown") -> Optional[SecurityToken]:
        """认证用户并生成令牌"""
        self._total_requests += 1

        # 检查用户是否被锁定
        if self._is_user_locked(username):
            self.audit_log.log_authentication(username, False, ip_address)
            self._blocked_requests += 1
            return None

        # 验证凭据
        if self._verify_credentials(username, password):
            # 认证成功,生成令牌
            token = self._create_token(username)
            self.active_tokens[token.token_id] = token

            self.audit_log.log_authentication(username, True, ip_address)
            self._successful_auths += 1

            logger.info(f"User {username} authenticated successfully")
            return token
        else:
            # 认证失败,记录失败尝试
            self._record_failed_attempt(username)
            self.audit_log.log_authentication(username, False, ip_address)
            self._failed_auths += 1

            logger.warning(f"Authentication failed for user {username}")
            return None

    def authorize_endpoint_access(self, token_id: Optional[str], endpoint: str, ip_address: str = "unknown") -> bool:
        """授权端点访问"""
        # 检查端点是否在允许列表中
        if endpoint not in self.policy["allowed_endpoints"]:
            self.audit_log.log_access_attempt(None, endpoint, False, ip_address)
            return False

        # 如果不需要认证,直接允许访问
        if not self.policy["require_auth"]:
            self.audit_log.log_access_attempt(None, endpoint, True, ip_address)
            return True

        # 检查令牌
        if not token_id or token_id not in self.active_tokens:
            self.audit_log.log_access_attempt(None, endpoint, False, ip_address)
            return False

        token = self.active_tokens[token_id]

        # 检查令牌是否有效
        if not token.is_valid():
            del self.active_tokens[token_id]
            self.audit_log.log_access_attempt(token.username, endpoint, False, ip_address)
            return False

        # 检查权限
        if not token.has_permission(endpoint):
            self.audit_log.log_access_attempt(token.username, endpoint, False, ip_address)
            return False

        self.audit_log.log_access_attempt(token.username, endpoint, True, ip_address)
        return True

    def _verify_credentials(self, username: str, password: str) -> bool:
        """验证用户凭据"""
        # 使用安全的密码比较
        expected_username = self.config.username
        expected_password = self.config.password

        # 防止时序攻击
        username_match = hmac.compare_digest(username, expected_username)
        password_match = hmac.compare_digest(password, expected_password)

        return username_match and password_match

    def _create_token(self, username: str) -> SecurityToken:
        """创建安全令牌"""
        expires_at = datetime.now() + timedelta(seconds=self.policy["session_timeout"])

        # 根据安全级别分配权限
        if self.security_level == SecurityLevel.PRODUCTION:
            permissions = {"health"}  # 生产环境只允许健康检查
        elif self.security_level == SecurityLevel.STAGING:
            permissions = {"health", "info", "metrics"}
        else:
            permissions = set(self.policy["allowed_endpoints"])

        return SecurityToken(username, expires_at, permissions)

    def _is_user_locked(self, username: str) -> bool:
        """检查用户是否被锁定"""
        if username not in self.locked_users:
            return False

        unlock_time = self.locked_users[username]
        if datetime.now() > unlock_time:
            del self.locked_users[username]
            return False

        return True

    def _record_failed_attempt(self, username: str) -> None:
        """记录失败尝试"""
        since = datetime.now() - timedelta(seconds=self.policy["lockout_duration"])
        failed_count = self.audit_log.get_failures(username, since)

        if failed_count >= self.policy["max_failed_attempts"]:
            # 锁定用户
            lockout_until = datetime.now() + timedelta(seconds=self.policy["lockout_duration"])
            self.locked_users[username] = lockout_until

            self.audit_log.log_security_event(
                "user_locked", {"username": username, "failed_attempts": failed_count, "lockout_until": lockout_until.isoformat()}
            )

    def get_security_status(self) -> Dict[str, Any]:
        """获取安全状态"""
        return {
            "security_level": self.security_level.value,
            "policy": self.policy,
            "active_tokens": len(self.active_tokens),
            "locked_users": len(self.locked_users),
            "statistics": {
                "total_requests": self._total_requests,
                "successful_auths": self._successful_auths,
                "failed_auths": self._failed_auths,
                "blocked_requests": self._blocked_requests,
                "success_rate": (self._successful_auths / max(self._total_requests, 1)) * 100,
            },
            "recent_logs": self.audit_log.get_recent_logs(10),
        }

    def cleanup_expired_tokens(self) -> int:
        """清理过期令牌"""
        expired_tokens = [token_id for token_id, token in self.active_tokens.items() if not token.is_valid()]

        for token_id in expired_tokens:
            del self.active_tokens[token_id]

        return len(expired_tokens)
