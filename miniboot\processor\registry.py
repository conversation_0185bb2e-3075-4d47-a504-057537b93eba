#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean后处理器注册表 - 管理和执行Bean后处理器
"""

import threading
from collections import defaultdict
from typing import Any

from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import BeanPostProcessor


class BeanPostProcessorRegistry:
    """
    Bean后处理器注册表

    负责管理所有的Bean后处理器,包括注册、排序和执行.
    注册表是线程安全的,支持并发访问.

    处理器按照order值排序执行,相同order值的处理器执行顺序不确定.

    Example:
        registry = BeanPostProcessorRegistry()
        registry.register_processor(MyProcessor())

        # 在Bean生命周期中应用处理器
        bean = registry.apply_before_initialization(bean, "myBean")
        bean = registry.apply_after_initialization(bean, "myBean")
    """

    def __init__(self):
        """初始化处理器注册表"""
        self._processors: list[BeanPostProcessor] = []
        self._processor_types: set[type[BeanPostProcessor]] = set()
        self._lock = threading.RLock()
        self._sorted = True  # 标记处理器列表是否已排序

    def register(self, processor: BeanPostProcessor) -> None:
        """
        注册Bean后处理器

        Args:
            processor: 要注册的处理器实例

        Raises:
            ValueError: 如果处理器为None或已经注册了相同类型的处理器
        """
        if processor is None:
            raise ValueError("处理器不能为None")

        processor_type = type(processor)

        with self._lock:
            # 检查是否已经注册了相同类型的处理器
            if processor_type in self._processor_types:
                raise ValueError(f"处理器类型 {processor_type.__name__} 已经注册")

            self._processors.append(processor)
            self._processor_types.add(processor_type)
            self._sorted = False  # 标记需要重新排序

    def register_processor(self, processor: BeanPostProcessor) -> None:
        """注册Bean后处理器 - 兼容性方法

        这是register方法的别名，用于向后兼容。

        Args:
            processor: 要注册的处理器实例
        """
        self.register(processor)

    def unregister(self, processor_type: type[BeanPostProcessor]) -> bool:
        """
        注销指定类型的处理器

        Args:
            processor_type: 要注销的处理器类型

        Returns:
            True如果成功注销,False如果处理器不存在
        """
        with self._lock:
            for i, processor in enumerate(self._processors):
                if type(processor) is processor_type:
                    del self._processors[i]
                    self._processor_types.discard(processor_type)
                    return True
            return False

    def processors(self) -> list[BeanPostProcessor]:
        """
        获取所有已注册的处理器(按执行顺序排序)

        Returns:
            处理器列表的副本
        """
        with self._lock:
            self._ensure_sorted()
            return self._processors.copy()

    def count(self) -> int:
        """
        获取已注册的处理器数量

        Returns:
            处理器数量
        """
        with self._lock:
            return len(self._processors)

    def has(self, processor_type: type[BeanPostProcessor]) -> bool:
        """
        检查是否已注册指定类型的处理器

        Args:
            processor_type: 处理器类型

        Returns:
            True如果已注册,False否则
        """
        with self._lock:
            return processor_type in self._processor_types

    def clear(self) -> None:
        """清空所有已注册的处理器"""
        with self._lock:
            self._processors.clear()
            self._processor_types.clear()
            self._sorted = True

    def before(self, bean: Any, bean_name: str) -> Any:
        """
        应用所有支持的初始化前处理器

        按照order值从小到大的顺序依次调用每个处理器的
        pre_process方法.

        Args:
            bean: 要处理的Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例

        Raises:
            BeanProcessingException: 当处理过程中发生错误时
        """
        if bean is None:
            return bean

        with self._lock:
            self._ensure_sorted()
            current_bean = bean

            for processor in self._processors:
                if not processor.supports(current_bean, bean_name):
                    continue

                try:
                    result = processor.pre_process(current_bean, bean_name)
                    if result is not None:
                        current_bean = result
                except Exception as e:
                    raise BeanProcessingError("初始化前处理失败", bean_name=bean_name, processor_name=processor.__class__.__name__, cause=e) from e

            return current_bean

    def after(self, bean: Any, bean_name: str) -> Any:
        """
        应用所有支持的初始化后处理器

        按照order值从小到大的顺序依次调用每个处理器的
        post_process方法.

        Args:
            bean: 要处理的Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例

        Raises:
            BeanProcessingException: 当处理过程中发生错误时
        """
        if bean is None:
            return bean

        with self._lock:
            self._ensure_sorted()
            current_bean = bean

            for processor in self._processors:
                if not processor.supports(current_bean, bean_name):
                    continue

                try:
                    result = processor.post_process(current_bean, bean_name)
                    if result is not None:
                        current_bean = result
                except Exception as e:
                    raise BeanProcessingError("初始化后处理失败", bean_name=bean_name, processor_name=processor.__class__.__name__, cause=e) from e

            return current_bean

    def by_order(self) -> dict[int, list[BeanPostProcessor]]:
        """
        按执行顺序分组获取处理器

        Returns:
            字典,键为order值,值为该order值的处理器列表
        """
        with self._lock:
            self._ensure_sorted()
            result = defaultdict(list)
            for processor in self._processors:
                result[processor.get_order()].append(processor)
            return dict(result)

    def _ensure_sorted(self) -> None:
        """确保处理器列表按order值排序"""
        if not self._sorted:
            self._processors.sort(key=lambda p: p.get_order())
            self._sorted = True

    def __len__(self) -> int:
        """返回已注册的处理器数量"""
        return self.count()

    def __contains__(self, processor_type: type[BeanPostProcessor]) -> bool:
        """检查是否包含指定类型的处理器"""
        return self.has(processor_type)

    def __str__(self) -> str:
        """返回注册表的字符串表示"""
        with self._lock:
            processor_names = [p.__class__.__name__ for p in self._processors]
            return f"BeanPostProcessorRegistry({len(self._processors)} processors: {processor_names})"

    def __repr__(self) -> str:
        """返回注册表的详细字符串表示"""
        return self.__str__()
