#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务模块与bean模块集成测试
"""

import threading
import time
import unittest

from miniboot.schedule import EnableScheduling, MiniBootScheduler, Scheduled


class MockBean:
    """模拟Bean对象"""

    def __init__(self, name: str):
        self.name = name
        self.initialized = False
        self.destroyed = False
        self.call_count = 0

    def init(self):
        """Bean初始化"""
        self.initialized = True

    def destroy(self):
        """Bean销毁"""
        self.destroyed = True

    def get_status(self):
        """获取Bean状态"""
        return {"name": self.name, "initialized": self.initialized, "destroyed": self.destroyed, "call_count": self.call_count}


class MockBeanFactory:
    """模拟Bean工厂"""

    def __init__(self):
        self.beans = {}
        self.creation_order = []

    def create_bean(self, name: str, bean_class: type, *args, **kwargs):
        """创建Bean"""
        bean = bean_class(*args, **kwargs)
        self.beans[name] = bean
        self.creation_order.append(name)
        return bean

    def get_bean(self, name: str):
        """获取Bean"""
        return self.beans.get(name)

    def destroy_bean(self, name: str):
        """销毁Bean"""
        if name in self.beans:
            bean = self.beans[name]
            if hasattr(bean, "destroy"):
                bean.destroy()
            del self.beans[name]

    def get_all_beans(self):
        """获取所有Bean"""
        return self.beans.copy()


@EnableScheduling
class ScheduledBeanService(MockBean):
    """带调度功能的Bean服务"""

    def __init__(self, name: str):
        super().__init__(name)
        self.execution_results = []
        self.lock = threading.Lock()

    @Scheduled(fixed_rate="1s")
    def scheduled_task(self):
        """调度任务"""
        if not self.initialized:
            raise RuntimeError("Bean not initialized")

        with self.lock:
            self.call_count += 1
            result = f"{self.name}_task_{self.call_count}"
            self.execution_results.append(result)
            return result

    @Scheduled(fixed_delay="2s", initial_delay="0.5s")
    def delayed_task(self):
        """延迟任务"""
        if not self.initialized:
            raise RuntimeError("Bean not initialized")

        with self.lock:
            self.call_count += 1
            result = f"{self.name}_delayed_{self.call_count}"
            self.execution_results.append(result)
            return result

    def get_results(self):
        """获取执行结果"""
        with self.lock:
            return self.execution_results.copy()

    def reset_results(self):
        """重置结果"""
        with self.lock:
            self.execution_results.clear()


class DependentScheduledService(MockBean):
    """依赖其他Bean的调度服务"""

    def __init__(self, name: str, dependency: MockBean):
        super().__init__(name)
        self.dependency = dependency
        self.execution_results = []
        self.lock = threading.Lock()

    @Scheduled(fixed_rate="1.5s")
    def dependent_task(self):
        """依赖任务"""
        if not self.initialized:
            raise RuntimeError("Bean not initialized")

        if not self.dependency.initialized:
            raise RuntimeError("Dependency not initialized")

        with self.lock:
            self.call_count += 1
            result = f"{self.name}_depends_on_{self.dependency.name}_{self.call_count}"
            self.execution_results.append(result)
            return result

    def get_results(self):
        """获取执行结果"""
        with self.lock:
            return self.execution_results.copy()


class TestBeanIntegration(unittest.TestCase):
    """测试定时任务与Bean模块的集成"""

    def setUp(self):
        """设置测试环境"""
        self.bean_factory = MockBeanFactory()
        self.scheduler = MiniBootScheduler(max_workers=3, use_asyncio=False)

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

        # 清理所有Bean
        for name in list(self.bean_factory.beans.keys()):
            self.bean_factory.destroy_bean(name)

    def test_bean_lifecycle_with_scheduling(self):
        """测试Bean生命周期与调度的集成"""
        # 创建Bean
        bean = self.bean_factory.create_bean("scheduledService", ScheduledBeanService, "testService")

        # 验证Bean创建但未初始化
        self.assertFalse(bean.initialized)
        self.assertFalse(bean.destroyed)

        # 初始化Bean
        bean.init()
        self.assertTrue(bean.initialized)

        # 注册调度任务
        if self.scheduler.task_manager:
            task_id = self.scheduler.task_manager.create_from_scheduled_method(bean.scheduled_task, bean)
            self.assertIsNotNone(task_id)

            # 启动调度器
            self.scheduler.start()
            time.sleep(2.5)  # 运行2.5秒
            self.scheduler.shutdown()

            # 验证任务执行
            results = bean.get_results()
            self.assertGreater(len(results), 0)
            self.assertTrue(all(r.startswith("testService_task_") for r in results))

        # 销毁Bean
        self.bean_factory.destroy_bean("scheduledService")
        self.assertTrue(bean.destroyed)

    def test_bean_dependency_injection_with_scheduling(self):
        """测试Bean依赖注入与调度的集成"""
        # 创建依赖Bean
        dependency = self.bean_factory.create_bean("dependency", MockBean, "dependencyBean")
        dependency.init()

        # 创建依赖调度服务
        dependent_service = self.bean_factory.create_bean("dependentService", DependentScheduledService, "dependentService", dependency)
        dependent_service.init()

        # 验证依赖关系
        self.assertEqual(dependent_service.dependency, dependency)
        self.assertTrue(dependent_service.dependency.initialized)

        # 注册调度任务
        if self.scheduler.task_manager:
            _task_id = self.scheduler.task_manager.create_from_scheduled_method(dependent_service.dependent_task, dependent_service)

            # 启动调度器
            self.scheduler.start()
            time.sleep(3)  # 运行3秒
            self.scheduler.shutdown()

            # 验证任务执行
            results = dependent_service.get_results()
            self.assertGreater(len(results), 0)
            self.assertTrue(all("depends_on_dependencyBean" in r for r in results))

    def test_multiple_bean_scheduling(self):
        """测试多个Bean的调度"""
        # 创建多个Bean
        beans = []
        for i in range(3):
            bean = self.bean_factory.create_bean(f"service_{i}", ScheduledBeanService, f"service_{i}")
            bean.init()
            beans.append(bean)

        # 为所有Bean注册调度任务
        task_ids = []
        if self.scheduler.task_manager:
            for bean in beans:
                task_id = self.scheduler.task_manager.create_from_scheduled_method(bean.scheduled_task, bean)
                task_ids.append(task_id)

            # 启动调度器
            self.scheduler.start()
            time.sleep(2)  # 运行2秒
            self.scheduler.shutdown()

            # 验证所有Bean的任务都执行了
            for bean in beans:
                results = bean.get_results()
                self.assertGreater(len(results), 0)
                self.assertTrue(all(r.startswith(f"{bean.name}_task_") for r in results))

    def test_bean_initialization_order_with_scheduling(self):
        """测试Bean初始化顺序与调度的关系"""
        # 创建Bean但不立即初始化
        bean = self.bean_factory.create_bean("uninitializedService", ScheduledBeanService, "uninitializedService")

        # 注册调度任务（Bean未初始化）
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(bean.scheduled_task, bean)

            # 启动调度器（Bean仍未初始化）
            self.scheduler.start()
            time.sleep(1)

            # 此时任务应该失败（因为Bean未初始化）
            results = bean.get_results()
            self.assertEqual(len(results), 0)  # 应该没有成功执行

            # 现在初始化Bean
            bean.init()
            time.sleep(1.5)  # 继续运行
            self.scheduler.shutdown()

            # 现在应该有成功执行的任务
            results = bean.get_results()
            self.assertGreater(len(results), 0)

    def test_bean_destruction_with_active_scheduling(self):
        """测试Bean销毁时的活跃调度处理"""
        # 创建并初始化Bean
        bean = self.bean_factory.create_bean("toBeDestroyed", ScheduledBeanService, "toBeDestroyed")
        bean.init()

        # 注册调度任务
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(bean.scheduled_task, bean)

            # 启动调度器
            self.scheduler.start()
            time.sleep(1)  # 让任务执行一段时间

            # 获取初始执行结果
            initial_results = bean.get_results()
            initial_count = len(initial_results)
            self.assertGreater(initial_count, 0)

            # 销毁Bean
            self.bean_factory.destroy_bean("toBeDestroyed")
            self.assertTrue(bean.destroyed)

            # 继续运行调度器
            time.sleep(1)
            self.scheduler.shutdown()

            # 验证Bean销毁后任务不再执行（或执行失败）
            final_results = bean.get_results()
            # 结果数量应该不会显著增加
            self.assertLessEqual(len(final_results) - initial_count, 1)

    def test_bean_scope_with_scheduling(self):
        """测试Bean作用域与调度的集成"""
        # 模拟单例Bean
        singleton_bean = self.bean_factory.create_bean("singletonService", ScheduledBeanService, "singletonService")
        singleton_bean.init()

        # 验证单例Bean的调度
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(singleton_bean.scheduled_task, singleton_bean)

            self.scheduler.task_manager.create_from_scheduled_method(singleton_bean.delayed_task, singleton_bean)

            # 启动调度器
            self.scheduler.start()
            time.sleep(2.5)
            self.scheduler.shutdown()

            # 验证单例Bean的多个任务都执行了
            results = singleton_bean.get_results()
            self.assertGreater(len(results), 0)

            # 应该有两种类型的任务结果
            task_results = [r for r in results if "_task_" in r]
            delayed_results = [r for r in results if "_delayed_" in r]

            self.assertGreater(len(task_results), 0)
            self.assertGreater(len(delayed_results), 0)

    def test_bean_factory_integration(self):
        """测试Bean工厂与调度器的集成"""
        # 创建多个不同类型的Bean
        beans_config = [
            ("service1", ScheduledBeanService, "service1"),
            ("service2", ScheduledBeanService, "service2"),
        ]

        created_beans = []
        for name, bean_class, *args in beans_config:
            bean = self.bean_factory.create_bean(name, bean_class, *args)
            bean.init()
            created_beans.append(bean)

        # 批量注册调度任务
        task_ids = []
        if self.scheduler.task_manager:
            for bean in created_beans:
                task_id = self.scheduler.task_manager.create_from_scheduled_method(bean.scheduled_task, bean)
                task_ids.append(task_id)

            # 验证所有任务都注册成功
            self.assertEqual(len(task_ids), len(created_beans))

            # 启动调度器并验证执行
            self.scheduler.start()
            time.sleep(2)
            self.scheduler.shutdown()

            # 验证所有Bean的任务都执行了
            for bean in created_beans:
                results = bean.get_results()
                self.assertGreater(len(results), 0)

        # 验证Bean工厂状态
        all_beans = self.bean_factory.get_all_beans()
        self.assertEqual(len(all_beans), len(created_beans))

        for bean in created_beans:
            self.assertTrue(bean.initialized)
            self.assertFalse(bean.destroyed)


class TestBeanLifecycleCallbacks(unittest.TestCase):
    """测试Bean生命周期回调与调度的集成"""

    def setUp(self):
        """设置测试环境"""
        self.scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)
        self.lifecycle_events = []

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

    def test_post_construct_scheduling(self):
        """测试Bean构造后的调度启动"""

        @EnableScheduling
        class PostConstructService:
            def __init__(self, test_case):
                self.test_case = test_case
                self.constructed = True
                self.post_construct_called = False
                self.execution_count = 0

            def post_construct(self):
                """模拟@PostConstruct回调"""
                self.post_construct_called = True
                self.test_case.lifecycle_events.append("post_construct")

            @Scheduled(fixed_rate="1s")
            def scheduled_task(self):
                if not self.post_construct_called:
                    raise RuntimeError("PostConstruct not called")

                self.execution_count += 1
                self.test_case.lifecycle_events.append(f"task_execution_{self.execution_count}")
                return f"task_{self.execution_count}"

        # 创建服务并调用post_construct
        service = PostConstructService(self)
        service.post_construct()

        # 注册调度任务
        if self.scheduler.task_manager:
            self.scheduler.task_manager.create_from_scheduled_method(service.scheduled_task, service)

            # 启动调度器
            self.scheduler.start()
            time.sleep(2)
            self.scheduler.shutdown()

            # 验证生命周期事件顺序
            self.assertIn("post_construct", self.lifecycle_events)
            task_events = [e for e in self.lifecycle_events if e.startswith("task_execution_")]
            self.assertGreater(len(task_events), 0)

            # post_construct应该在任务执行之前
            post_construct_index = self.lifecycle_events.index("post_construct")
            first_task_index = self.lifecycle_events.index(task_events[0])
            self.assertLess(post_construct_index, first_task_index)


if __name__ == "__main__":
    unittest.main()
