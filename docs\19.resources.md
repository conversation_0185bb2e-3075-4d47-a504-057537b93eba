# 资源目录结构设计

## 概述

参考 Spring Boot 的资源管理模式，为 Mini-Boot 框架设计标准的资源目录结构，用于存放配置文件、静态资源、模板文件等。

## Spring Boot 资源目录参考

### Spring Boot 标准结构
```
src/main/resources/
├── application.yml              # 主配置文件
├── application-dev.yml          # 开发环境配置
├── application-prod.yml         # 生产环境配置
├── application-test.yml         # 测试环境配置
├── static/                      # 静态资源
│   ├── css/
│   ├── js/
│   ├── images/
│   └── favicon.ico
├── templates/                   # 模板文件
│   ├── index.html
│   └── error/
├── META-INF/                    # 元信息
│   └── spring.factories
├── banner.txt                   # 启动横幅
├── logback-spring.xml          # 日志配置
└── i18n/                       # 国际化资源
    ├── messages.properties
    ├── messages_en.properties
    └── messages_zh.properties
```

## Mini-Boot 资源目录设计

### 目录结构
```
resources/
├── config/                      # 配置文件目录
│   ├── application.yml          # 主配置文件
│   ├── application-dev.yml      # 开发环境配置
│   ├── application-prod.yml     # 生产环境配置
│   ├── application-test.yml     # 测试环境配置
│   └── logging.yml              # 日志配置
├── static/                      # 静态资源目录
│   ├── css/                     # 样式文件
│   ├── js/                      # JavaScript 文件
│   ├── images/                  # 图片资源
│   ├── fonts/                   # 字体文件
│   └── favicon.ico              # 网站图标
├── templates/                   # 模板文件目录
│   ├── index.html               # 首页模板
│   ├── error/                   # 错误页面模板
│   │   ├── 404.html
│   │   ├── 500.html
│   │   └── default.html
│   └── layouts/                 # 布局模板
│       ├── base.html
│       └── admin.html
├── i18n/                        # 国际化资源
│   ├── messages.properties      # 默认语言
│   ├── messages_en.properties   # 英文
│   ├── messages_zh.properties   # 中文
│   └── messages_zh_CN.properties # 简体中文
├── data/                        # 数据文件
│   ├── sql/                     # SQL 脚本
│   │   ├── schema.sql           # 数据库结构
│   │   └── data.sql             # 初始数据
│   └── fixtures/                # 测试数据
├── certs/                       # 证书文件
│   ├── keystore.p12
│   └── truststore.jks
├── banner.txt                   # 启动横幅文件
└── META-INF/                    # 元信息目录
    ├── miniboot.factories       # 自动配置工厂
    └── services/                # SPI 服务定义
```

## 配置文件规范

### 主配置文件 (application.yml)
```yaml
# Mini-Boot 应用配置
miniboot:
  application:
    name: mini-boot-app
    version: 1.0.0
  
  # 服务器配置
  server:
    port: 8080
    host: 0.0.0.0
    
  # 数据库配置
  datasource:
    url: sqlite:///data/app.db
    driver: sqlite3
    
  # 日志配置
  logging:
    level: INFO
    file: logs/app.log
    
  # 静态资源配置
  web:
    static-path: /static
    static-locations: resources/static
    template-path: resources/templates
```

### 环境特定配置
```yaml
# application-dev.yml (开发环境)
miniboot:
  logging:
    level: DEBUG
  datasource:
    url: sqlite:///data/dev.db
    
# application-prod.yml (生产环境)  
miniboot:
  logging:
    level: WARN
    file: /var/log/miniboot/app.log
  server:
    port: 80
```

## 资源加载机制

### 配置文件加载顺序
1. `application.yml` (默认配置)
2. `application-{profile}.yml` (环境特定配置)
3. 环境变量覆盖
4. 命令行参数覆盖

### 静态资源访问
- URL 路径：`/static/**`
- 文件系统路径：`resources/static/`
- 支持缓存控制和版本管理

### 模板文件解析
- 支持 Jinja2 模板引擎
- 自动加载 `resources/templates/` 目录
- 支持模板继承和包含

## 国际化支持

### 消息文件格式
```properties
# messages.properties
welcome.message=Welcome to Mini-Boot
error.not_found=Page not found
user.login=Login

# messages_zh.properties  
welcome.message=欢迎使用 Mini-Boot
error.not_found=页面未找到
user.login=登录
```

### 使用方式
```python
from miniboot.i18n import get_message

# 获取当前语言的消息
message = get_message('welcome.message')

# 指定语言
message = get_message('welcome.message', locale='zh')
```

## 实现要点

### 1. 资源发现机制
- 自动扫描 `resources/` 目录
- 支持 JAR 包内资源访问
- 提供资源路径解析工具

### 2. 配置文件解析
- 支持 YAML 和 Properties 格式
- 环境变量替换
- 配置验证和类型转换

### 3. 静态资源服务
- 集成 Web 框架静态文件服务
- 支持 MIME 类型检测
- 缓存控制和压缩

### 4. 模板引擎集成
- 自动配置模板引擎
- 模板缓存管理
- 错误页面处理

## 开发工具支持

### 热重载
- 开发模式下自动重载配置文件
- 静态资源变更检测
- 模板文件实时更新

### 资源打包
- 生产环境资源优化
- 静态资源压缩和合并
- 版本号管理

## 安全考虑

### 路径安全
- 防止目录遍历攻击
- 限制可访问的文件类型
- 敏感文件保护

### 配置安全
- 敏感信息加密存储
- 环境变量注入
- 配置文件权限控制
