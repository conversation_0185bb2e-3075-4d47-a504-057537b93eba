# Mini-Boot 异步功能设计

## 1. 概述

Mini-Boot 框架的异步功能充分利用了 Python 原生的 async/await 特性，提供了类似于 Spring 框架中 `@Async` 注解的异步方法执行能力。该功能允许开发者将方法标记为异步执行，从而在不阻塞调用方的情况下在独立的协程中执行这些方法。

与 Go 版本不同，Python 版本的异步功能更加简洁和自然，因为 Python 语言本身就内置了强大的异步编程支持。

## 1.1 目录结构

```
miniboot/async/
├── __init__.py                     # 异步模块导出
├── annotations.py                  # 异步注解定义
├── executor.py                     # 异步执行器
├── pool.py                         # 协程池管理
├── future.py                       # Future实现
├── properties.py                   # 异步配置属性
├── interceptor.py                  # 异步方法拦截器
└── monitor.py                      # 异步任务监控
```

## 2. 核心组件

### 2.1 异步注解

```python
from typing import Optional, Callable, Any
import asyncio
import functools

def Async(pool: Optional[str] = None, timeout: Optional[float] = None):
    """异步方法装饰器

    Args:
        pool: 指定协程池名称，默认使用全局池
        timeout: 超时时间（秒），默认无超时
    """
    def decorator(func: Callable) -> Callable:
        # 标记为异步方法
        func.__async__ = True
        func.__async_pool__ = pool
        func.__async_timeout__ = timeout

        if asyncio.iscoroutinefunction(func):
            # 如果已经是协程函数，直接返回
            return func
        else:
            # 如果是同步函数，包装为异步函数
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, func, *args, **kwargs)

            async_wrapper.__async__ = True
            async_wrapper.__async_pool__ = pool
            async_wrapper.__async_timeout__ = timeout
            return async_wrapper

    return decorator

def EnableAsync(cls=None):
    """启用异步功能的类装饰器"""
    def decorator(cls):
        cls.__enable_async__ = True
        return cls

    if cls is None:
        return decorator
    return decorator(cls)
```

### 2.2 异步执行器

```python
import asyncio
import concurrent.futures
from typing import Dict, Optional, Any, Callable
from dataclasses import dataclass
import threading
import time

@dataclass
class AsyncProperties:
    """异步配置属性"""
    enabled: bool = True
    core_size: int = 10
    max_size: int = 20
    queue_size: int = 100
    keep_alive: float = 60.0  # 秒
    wait_for_tasks: bool = True
    timeout: Optional[float] = None

    # 监控配置
    monitor_enabled: bool = False
    monitor_interval: float = 5.0

class AsyncExecutor:
    """异步执行器"""

    def __init__(self, properties: AsyncProperties = None):
        self.properties = properties or AsyncProperties()
        self.thread_pool = None
        self.named_pools: Dict[str, concurrent.futures.ThreadPoolExecutor] = {}
        self.monitor = None
        self._lock = threading.Lock()

        if self.properties.enabled:
            self._initialize()

    def _initialize(self):
        """初始化异步执行器"""
        # 创建默认线程池
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.properties.max_size,
            thread_name_prefix="async-executor"
        )

        # 启动监控
        if self.properties.monitor_enabled:
            self._start_monitor()

    def get_pool(self, pool_name: Optional[str] = None) -> concurrent.futures.ThreadPoolExecutor:
        """获取指定的线程池"""
        if pool_name is None:
            return self.thread_pool

        with self._lock:
            if pool_name not in self.named_pools:
                self.named_pools[pool_name] = concurrent.futures.ThreadPoolExecutor(
                    max_workers=self.properties.core_size,
                    thread_name_prefix=f"async-{pool_name}"
                )
            return self.named_pools[pool_name]

    async def execute_async(self, func: Callable, *args, pool_name: Optional[str] = None,
                           timeout: Optional[float] = None, **kwargs) -> Any:
        """执行异步任务"""
        if not self.properties.enabled:
            # 如果异步功能未启用，直接执行
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)

        pool = self.get_pool(pool_name)
        loop = asyncio.get_event_loop()

        # 设置超时
        task_timeout = timeout or self.properties.timeout

        try:
            if asyncio.iscoroutinefunction(func):
                # 协程函数直接执行
                if task_timeout:
                    return await asyncio.wait_for(func(*args, **kwargs), timeout=task_timeout)
                else:
                    return await func(*args, **kwargs)
            else:
                # 同步函数在线程池中执行
                future = loop.run_in_executor(pool, func, *args, **kwargs)
                if task_timeout:
                    return await asyncio.wait_for(future, timeout=task_timeout)
                else:
                    return await future
        except asyncio.TimeoutError:
            raise TimeoutError(f"Async task timed out after {task_timeout} seconds")

    def submit_task(self, func: Callable, *args, pool_name: Optional[str] = None, **kwargs) -> 'AsyncFuture':
        """提交异步任务，返回Future对象"""
        pool = self.get_pool(pool_name)

        if asyncio.iscoroutinefunction(func):
            # 协程函数需要在事件循环中执行
            async def wrapper():
                return await func(*args, **kwargs)

            # 创建任务
            try:
                loop = asyncio.get_event_loop()
                task = loop.create_task(wrapper())
                return AsyncFuture(task)
            except RuntimeError:
                # 如果没有运行的事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                task = loop.create_task(wrapper())
                return AsyncFuture(task)
        else:
            # 同步函数在线程池中执行
            future = pool.submit(func, *args, **kwargs)
            return AsyncFuture(future)

    def shutdown(self, wait: bool = True):
        """关闭异步执行器"""
        if self.thread_pool:
            self.thread_pool.shutdown(wait=wait)

        for pool in self.named_pools.values():
            pool.shutdown(wait=wait)

        if self.monitor:
            self.monitor.stop()

    def _start_monitor(self):
        """启动监控"""
        from .monitor import AsyncMonitor
        self.monitor = AsyncMonitor(self, self.properties.monitor_interval)
        self.monitor.start()
```

### 2.3 Future 实现

```python
import asyncio
import concurrent.futures
from typing import Any, Optional, Union
import time

class AsyncFuture:
    """异步Future实现，兼容asyncio.Task和concurrent.futures.Future"""

    def __init__(self, future: Union[asyncio.Task, concurrent.futures.Future]):
        self.future = future
        self._start_time = time.time()

    async def get(self) -> Any:
        """获取结果，会阻塞直到任务完成"""
        if isinstance(self.future, asyncio.Task):
            return await self.future
        else:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self.future.result)

    async def get_with_timeout(self, timeout: float) -> Any:
        """带超时的获取结果"""
        if isinstance(self.future, asyncio.Task):
            return await asyncio.wait_for(self.future, timeout=timeout)
        else:
            loop = asyncio.get_event_loop()
            return await asyncio.wait_for(
                loop.run_in_executor(None, self.future.result, timeout),
                timeout=timeout
            )

    def is_done(self) -> bool:
        """检查任务是否完成"""
        return self.future.done()

    def cancel(self) -> bool:
        """取消任务"""
        return self.future.cancel()

    def cancelled(self) -> bool:
        """检查任务是否被取消"""
        return self.future.cancelled()

    def exception(self) -> Optional[Exception]:
        """获取异常"""
        if not self.is_done():
            return None

        try:
            if isinstance(self.future, asyncio.Task):
                return self.future.exception()
            else:
                return self.future.exception()
        except:
            return None

    def elapsed_time(self) -> float:
        """获取已执行时间"""
        return time.time() - self._start_time
```

### 2.4 异步方法拦截器

```python
import inspect
from typing import Any, Callable, Dict
from miniboot.bean import BeanPostProcessor

class AsyncMethodInterceptor(BeanPostProcessor):
    """异步方法拦截器"""

    def __init__(self, executor: AsyncExecutor):
        self.executor = executor
        self.async_methods: Dict[str, Dict[str, Any]] = {}

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化后处理异步方法"""
        bean_class = bean.__class__

        # 检查类是否启用了异步功能
        if not hasattr(bean_class, '__enable_async__'):
            return bean

        # 扫描异步方法
        async_methods = {}
        for method_name, method in inspect.getmembers(bean_class, inspect.isfunction):
            if hasattr(method, '__async__'):
                async_methods[method_name] = {
                    'pool': getattr(method, '__async_pool__', None),
                    'timeout': getattr(method, '__async_timeout__', None)
                }

        if async_methods:
            self.async_methods[bean_name] = async_methods
            # 创建代理对象
            return self._create_proxy(bean, async_methods)

        return bean

    def _create_proxy(self, target: Any, async_methods: Dict[str, Any]) -> Any:
        """创建异步代理对象"""
        class AsyncProxy:
            def __init__(self, target_obj, interceptor):
                self._target = target_obj
                self._interceptor = interceptor
                self._async_methods = async_methods

            def __getattr__(self, name):
                attr = getattr(self._target, name)

                if name in self._async_methods and callable(attr):
                    # 异步方法包装
                    async def async_wrapper(*args, **kwargs):
                        method_config = self._async_methods[name]
                        return await self._interceptor.executor.execute_async(
                            attr,
                            *args,
                            pool_name=method_config['pool'],
                            timeout=method_config['timeout'],
                            **kwargs
                        )
                    return async_wrapper
                else:
                    return attr

        return AsyncProxy(target, self)
```

### 2.5 异步任务监控

```python
import threading
import time
from typing import Dict, List
from dataclasses import dataclass, field
from datetime import datetime

@dataclass
class TaskMetrics:
    """任务指标"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    cancelled_tasks: int = 0
    active_tasks: int = 0
    average_execution_time: float = 0.0
    max_execution_time: float = 0.0
    min_execution_time: float = float('inf')

@dataclass
class PoolMetrics:
    """线程池指标"""
    pool_name: str
    core_size: int
    max_size: int
    active_threads: int
    queue_size: int
    completed_task_count: int

class AsyncMonitor:
    """异步任务监控器"""

    def __init__(self, executor: AsyncExecutor, interval: float = 5.0):
        self.executor = executor
        self.interval = interval
        self.running = False
        self.thread = None
        self.metrics = TaskMetrics()
        self.history: List[TaskMetrics] = []
        self.max_history_size = 100
        self._lock = threading.Lock()

    def start(self):
        """启动监控"""
        if self.running:
            return

        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()

    def stop(self):
        """停止监控"""
        self.running = False
        if self.thread:
            self.thread.join()

    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                self._collect_metrics()
                time.sleep(self.interval)
            except Exception as e:
                print(f"Monitor error: {e}")

    def _collect_metrics(self):
        """收集指标"""
        with self._lock:
            # 收集线程池指标
            if self.executor.thread_pool:
                # 这里可以收集线程池的实际指标
                # Python的ThreadPoolExecutor没有直接暴露这些指标
                # 可以通过扩展或使用第三方库来实现
                pass

            # 保存历史记录
            if len(self.history) >= self.max_history_size:
                self.history.pop(0)

            self.history.append(TaskMetrics(
                total_tasks=self.metrics.total_tasks,
                completed_tasks=self.metrics.completed_tasks,
                failed_tasks=self.metrics.failed_tasks,
                cancelled_tasks=self.metrics.cancelled_tasks,
                active_tasks=self.metrics.active_tasks,
                average_execution_time=self.metrics.average_execution_time,
                max_execution_time=self.metrics.max_execution_time,
                min_execution_time=self.metrics.min_execution_time
            ))

    def get_metrics(self) -> TaskMetrics:
        """获取当前指标"""
        with self._lock:
            return self.metrics

    def get_history(self) -> List[TaskMetrics]:
        """获取历史指标"""
        with self._lock:
            return self.history.copy()

    def get_pool_metrics(self) -> List[PoolMetrics]:
        """获取线程池指标"""
        metrics = []

        # 默认线程池
        if self.executor.thread_pool:
            metrics.append(PoolMetrics(
                pool_name="default",
                core_size=self.executor.properties.core_size,
                max_size=self.executor.properties.max_size,
                active_threads=0,  # 需要扩展ThreadPoolExecutor来获取
                queue_size=0,      # 需要扩展ThreadPoolExecutor来获取
                completed_task_count=0  # 需要扩展ThreadPoolExecutor来获取
            ))

        # 命名线程池
        for name, pool in self.executor.named_pools.items():
            metrics.append(PoolMetrics(
                pool_name=name,
                core_size=self.executor.properties.core_size,
                max_size=self.executor.properties.max_size,
                active_threads=0,
                queue_size=0,
                completed_task_count=0
            ))

        return metrics
```

## 3. 配置属性

```python
from dataclasses import dataclass
from typing import Optional

@dataclass
class AsyncConfiguration:
    """异步功能配置"""
    # 基本配置
    enabled: bool = True
    core_size: int = 10
    max_size: int = 20
    queue_size: int = 100
    keep_alive: float = 60.0
    wait_for_tasks: bool = True
    default_timeout: Optional[float] = None

    # 监控配置
    monitor_enabled: bool = False
    monitor_interval: float = 5.0
    monitor_history_size: int = 100

    # 线程池配置
    thread_name_prefix: str = "async-executor"

    @classmethod
    def from_environment(cls, environment) -> 'AsyncConfiguration':
        """从环境配置创建异步配置"""
        return cls(
            enabled=environment.get_property("mini.async.enabled", True),
            core_size=environment.get_property("mini.async.core-size", 10),
            max_size=environment.get_property("mini.async.max-size", 20),
            queue_size=environment.get_property("mini.async.queue-size", 100),
            keep_alive=environment.get_property("mini.async.keep-alive", 60.0),
            wait_for_tasks=environment.get_property("mini.async.wait-for-tasks", True),
            default_timeout=environment.get_property("mini.async.timeout", None),
            monitor_enabled=environment.get_property("mini.async.monitor.enabled", False),
            monitor_interval=environment.get_property("mini.async.monitor.interval", 5.0),
            monitor_history_size=environment.get_property("mini.async.monitor.history-size", 100),
            thread_name_prefix=environment.get_property("mini.async.thread-name-prefix", "async-executor")
        )
```

## 4. 使用示例

### 4.1 基本异步方法

```python
from miniboot.annotations import Component, Autowired
from miniboot.async import Async, EnableAsync
import asyncio

@Component
@EnableAsync
class EmailService:
    """邮件服务"""

    @Async()
    async def send_email(self, to: str, subject: str, content: str) -> bool:
        """异步发送邮件"""
        print(f"Sending email to {to}: {subject}")
        # 模拟异步发送过程
        await asyncio.sleep(2)
        print(f"Email sent to {to}")
        return True

    @Async(pool="email-pool", timeout=10.0)
    def send_bulk_emails(self, recipients: list, subject: str, content: str) -> int:
        """批量发送邮件（同步方法转异步）"""
        count = 0
        for recipient in recipients:
            # 模拟发送过程
            print(f"Sending email to {recipient}")
            count += 1
        return count

@Component
class UserService:
    """用户服务"""

    def __init__(self):
        self.email_service = None

    @Autowired
    def set_email_service(self, email_service: EmailService):
        self.email_service = email_service

    async def register_user(self, username: str, email: str) -> bool:
        """用户注册"""
        # 保存用户信息
        print(f"Saving user: {username}")

        # 异步发送欢迎邮件
        await self.email_service.send_email(
            email,
            "Welcome!",
            f"Welcome {username}!"
        )

        return True
```

### 4.2 使用 Future 获取结果

```python
import asyncio
from miniboot.async import AsyncExecutor, AsyncFuture

async def example_with_future():
    """使用Future的示例"""
    executor = AsyncExecutor()

    # 提交异步任务
    future = executor.submit_task(some_long_running_task, "parameter")

    # 检查任务状态
    print(f"Task done: {future.is_done()}")

    # 获取结果（会等待任务完成）
    try:
        result = await future.get_with_timeout(30.0)
        print(f"Task result: {result}")
    except asyncio.TimeoutError:
        print("Task timed out")
        future.cancel()

def some_long_running_task(param: str) -> str:
    """长时间运行的任务"""
    import time
    time.sleep(5)  # 模拟耗时操作
    return f"Processed: {param}"
```

### 4.3 配置异步功能

```yaml
# application.yml
mini:
    async:
        enabled: true
        core-size: 10
        max-size: 20
        queue-size: 100
        keep-alive: 60.0
        wait-for-tasks: true
        timeout: 30.0
        monitor:
            enabled: true
            interval: 5.0
            history-size: 100
```

```python
# 在应用启动时配置异步功能
from miniboot.annotations import MiniBootApplication, Configuration, Bean
from miniboot.async import AsyncExecutor, AsyncConfiguration
from miniboot.env import Environment

@Configuration
class AsyncConfig:

    @Bean
    def async_executor(self, environment: Environment) -> AsyncExecutor:
        """创建异步执行器"""
        config = AsyncConfiguration.from_environment(environment)
        return AsyncExecutor(config)

@MiniBootApplication
class Application:
    pass
```

### 4.4 自定义线程池

```python
from miniboot.async import Async

@Component
@EnableAsync
class FileService:
    """文件服务"""

    @Async(pool="file-io")
    async def read_large_file(self, file_path: str) -> str:
        """读取大文件"""
        # 在专用的文件IO线程池中执行
        with open(file_path, 'r') as f:
            return f.read()

    @Async(pool="image-processing")
    def process_image(self, image_path: str) -> str:
        """处理图片"""
        # 在专用的图片处理线程池中执行
        # 这里是CPU密集型任务，适合在线程池中执行
        import time
        time.sleep(3)  # 模拟图片处理
        return f"Processed: {image_path}"
```

### 4.5 异步事件处理

```python
from miniboot.annotations import Component, EventListener
from miniboot.async import Async

@Component
@EnableAsync
class UserEventHandler:
    """用户事件处理器"""

    @EventListener
    @Async()
    async def handle_user_registered(self, event):
        """处理用户注册事件"""
        user_id = event.user_id

        # 异步发送欢迎邮件
        await self.send_welcome_email(user_id)

        # 异步初始化用户数据
        await self.initialize_user_data(user_id)

    async def send_welcome_email(self, user_id: str):
        """发送欢迎邮件"""
        await asyncio.sleep(1)  # 模拟发送邮件
        print(f"Welcome email sent to user {user_id}")

    async def initialize_user_data(self, user_id: str):
        """初始化用户数据"""
        await asyncio.sleep(2)  # 模拟数据初始化
        print(f"User data initialized for {user_id}")
```

## 5. 与 Go 版本对比

| 特性       | Go 版本            | Python 版本                                    |
| ---------- | ------------------ | ---------------------------------------------- |
| 并发模型   | 基于 goroutine     | 基于 asyncio 协程 + 线程池                     |
| 语言支持   | 需要自定义实现     | 原生 async/await 支持                          |
| 性能特点   | 轻量级协程，高并发 | 协程+线程池混合，适合 IO 密集型                |
| 配置方式   | YAML 配置文件      | YAML 配置文件 + dataclass                      |
| 返回值处理 | 自定义 Future 接口 | 兼容 asyncio.Task 和 concurrent.futures.Future |
| 异常处理   | error 返回值       | Python 异常机制                                |
| 监控能力   | 内置协程池监控     | 线程池和任务监控                               |
| 代理机制   | 反射动态代理       | Python 动态属性代理                            |
| 资源消耗   | 极低（goroutine）  | 中等（线程池）                                 |
| 适用场景   | 高并发网络服务     | IO 密集型应用                                  |

## 6. 性能优化建议

### 6.1 选择合适的执行模式

```python
# 对于IO密集型任务，使用原生协程
@Async()
async def io_intensive_task():
    async with aiohttp.ClientSession() as session:
        async with session.get('http://api.example.com') as response:
            return await response.text()

# 对于CPU密集型任务，使用线程池
@Async(pool="cpu-intensive")
def cpu_intensive_task():
    # CPU密集型计算
    result = 0
    for i in range(1000000):
        result += i * i
    return result
```

### 6.2 合理配置线程池

```python
# 根据任务类型配置不同的线程池
@Configuration
class AsyncPoolConfig:

    @Bean
    def io_pool(self) -> AsyncExecutor:
        """IO密集型任务池"""
        config = AsyncConfiguration(
            core_size=20,
            max_size=50,
            queue_size=200
        )
        return AsyncExecutor(config)

    @Bean
    def cpu_pool(self) -> AsyncExecutor:
        """CPU密集型任务池"""
        import os
        cpu_count = os.cpu_count()
        config = AsyncConfiguration(
            core_size=cpu_count,
            max_size=cpu_count * 2,
            queue_size=cpu_count * 4
        )
        return AsyncExecutor(config)
```

## 7. 总结

Mini-Boot 框架的异步功能充分利用了 Python 原生的 async/await 特性，提供了一个强大而灵活的异步执行框架。相比 Go 版本，Python 版本具有以下特点和优势：

### 核心特性

1. **原生异步支持**

    - 充分利用 Python 的 async/await 语法
    - 无缝集成 asyncio 生态系统
    - 支持协程和线程池混合执行模式

2. **灵活的执行策略**

    - 自动识别协程函数和同步函数
    - 协程函数直接在事件循环中执行
    - 同步函数在线程池中执行，避免阻塞事件循环

3. **完整的生命周期管理**

    - 支持任务提交、执行、监控和取消
    - 提供 Future 接口获取异步执行结果
    - 支持超时控制和异常处理

4. **丰富的配置选项**
    - 支持多个命名线程池
    - 灵活的线程池大小和队列配置
    - 完整的监控和指标收集

### 设计优势

1. **Python 语言特性**

    - 利用装饰器提供简洁的 API
    - 使用 dataclass 管理配置
    - 充分利用 Python 的动态特性

2. **性能优化**

    - 协程用于 IO 密集型任务，避免线程切换开销
    - 线程池用于 CPU 密集型任务，充分利用多核 CPU
    - 智能的任务分发策略

3. **易于使用**

    - 简单的@Async 装饰器
    - 自动的 Bean 代理和方法拦截
    - 与 Mini-Boot 框架深度集成

4. **生产就绪**
    - 完善的错误处理和异常传播
    - 详细的任务监控和指标收集
    - 优雅的关闭和资源清理

### 适用场景

1. **IO 密集型应用**

    - 网络请求和 API 调用
    - 文件读写操作
    - 数据库访问

2. **事件驱动架构**

    - 异步事件处理
    - 消息队列处理
    - 实时数据处理

3. **微服务架构**

    - 服务间异步通信
    - 后台任务处理
    - 定时任务执行

4. **Web 应用**
    - 异步请求处理
    - 后台数据同步
    - 用户通知发送

### 与 Spring @Async 对比

| 特性     | Spring @Async                 | Mini-Boot @Async |
| -------- | ----------------------------- | ---------------- |
| 语言支持 | Java                          | Python           |
| 并发模型 | 线程池                        | 协程 + 线程池    |
| 配置方式 | Java 配置/XML                 | YAML + dataclass |
| 返回值   | Future/CompletableFuture      | AsyncFuture      |
| 异常处理 | AsyncUncaughtExceptionHandler | Python 异常机制  |
| 性能特点 | 线程开销较大                  | 协程轻量级       |
| 资源消耗 | 中等到高                      | 低到中等         |

### 最佳实践

1. **合理选择执行模式**

    - IO 密集型任务使用 async/await 协程
    - CPU 密集型任务使用线程池执行
    - 混合任务根据主要特征选择

2. **配置优化**

    - 根据应用特点配置线程池大小
    - 为不同类型任务创建专用线程池
    - 启用监控收集性能指标

3. **错误处理**

    - 合理设置超时时间
    - 实现适当的异常处理逻辑
    - 使用 Future 检查任务状态

4. **资源管理**
    - 及时关闭不需要的任务
    - 合理配置线程池保活时间
    - 在应用关闭时优雅停止异步执行器

通过异步功能，Mini-Boot 框架为 Python 应用提供了强大的并发处理能力，特别适合构建高性能的 IO 密集型应用和事件驱动的微服务架构。

---

_本文档定义了 Mini-Boot 框架的异步功能设计，提供基于 Python 原生 async/await 的异步执行能力。_
