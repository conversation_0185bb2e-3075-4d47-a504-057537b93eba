#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Web 应用核心模块 - 提供统一的 WebApplication 核心类,基于 FastAPI 实现配置驱动的 Web 应用功能

Web 应用核心模块

提供统一的 WebApplication 核心类,基于 FastAPI 实现配置驱动的 Web 应用功能.

主要功能:
- WebApplication - 统一Web应用核心类（默认非阻塞启动）
- 非阻塞服务器启动机制（默认行为）
- 配置驱动的组件初始化
- FastAPI集成和配置
- 控制器注册管理
- 异常处理器管理
- 中间件管理
"""

import asyncio
import time
from enum import Enum
from typing import Any, Dict, Optional

# FastAPI和uvicorn是Web模块的必需依赖
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger

from .exceptions import GlobalExceptionHandler
from .middleware import MiddlewareRegistry, ResponseMiddleware
from .params import ParameterBinder
from .properties import WebProperties
from .response import BusinessError, ValidationError
from .router import ControllerRegistry


class WebApplicationState(Enum):
    """Web应用状态枚举"""

    UNINITIALIZED = "uninitialized"  # 初始状态,未初始化
    INITIALIZING = "initializing"  # 正在初始化组件
    INITIALIZED = "initialized"  # 初始化完成,但服务器未启动
    STARTING = "starting"  # 正在启动服务器
    RUNNING = "running"  # 服务器正在运行
    STOPPING = "stopping"  # 正在停止服务器
    STOPPED = "stopped"  # 服务器已停止


class WebApplication:
    """统一的Web应用核心类

    基于FastAPI实现的配置驱动Web应用,支持:
    - 默认非阻塞服务器启动
    - 控制器注册、中间件管理、异常处理等功能
    - 配置驱动的组件初始化
    - 灵活的启动方式选择
    """

    def __init__(self, properties: Optional[WebProperties] = None) -> None:
        """初始化统一Web应用

        Args:
            properties: Web配置属性,如果为None则使用默认配置
        """
        self.properties = properties or WebProperties()

        # 启动时间记录
        self._init_start_time = time.time()

        # FastAPI应用
        self.fastapi_app: Optional[FastAPI] = None

        # 控制器注册表
        self.controller_registry: Optional[ControllerRegistry] = None

        # 中间件注册表
        self.middleware_registry: Optional[MiddlewareRegistry] = None

        # 参数绑定器
        self.parameter_binder: Optional[ParameterBinder] = None

        # 全局异常处理器
        self.exception_handler: Optional[GlobalExceptionHandler] = None

        # 响应处理中间件
        self.response_middleware: Optional[ResponseMiddleware] = None

        # 服务器实例和状态
        self._server: Optional[uvicorn.Server] = None
        self._server_task: Optional[asyncio.Task[None]] = None
        self._state = WebApplicationState.UNINITIALIZED

        # lifespan 状态标志
        self._lifespan_startup_complete = False
        self._lifespan_shutdown_started = False
        self._lifespan_shutdown_complete = False

    def _create_components(self) -> None:
        """创建所有组件（不配置）"""
        # 创建注册表
        self.controller_registry = ControllerRegistry()
        self.middleware_registry = MiddlewareRegistry()

        # 创建处理器
        self.parameter_binder = ParameterBinder()
        self.exception_handler = GlobalExceptionHandler(include_traceback=getattr(self.properties, "debug", False), log_exceptions=True)
        self.response_middleware = ResponseMiddleware(auto_wrap_response=True, add_request_id=True, add_response_time=True)

        # 注册默认中间件到注册表
        self._register_default_middlewares()

        logger.debug("✅ Core components created")

    def _register_default_middlewares(self) -> None:
        """注册默认中间件到注册表"""
        try:
            from .middleware import CompressionMiddleware, CorsMiddleware, LoggingMiddleware

            # 批量注册默认中间件（统一管理）
            middlewares = [
                LoggingMiddleware(),
                CorsMiddleware(),
                CompressionMiddleware(),
                self.response_middleware,  # 统一管理，避免重复处理
            ]

            if self.middleware_registry is not None:
                for middleware in middlewares:
                    self.middleware_registry.register(middleware)

            logger.debug("✅ Default middlewares registered to registry")

        except Exception as e:
            logger.warning(f"Failed to register default middlewares: {e}")

    def _create_fastapi_app(self) -> FastAPI:
        """创建 FastAPI 应用"""
        from contextlib import asynccontextmanager

        @asynccontextmanager
        async def lifespan(app: FastAPI):
            """自定义 lifespan 事件处理器，完全避免 CancelledError"""
            # 启动阶段
            logger.debug("🚀 FastAPI application starting...")

            # 设置启动完成标志
            self._lifespan_startup_complete = True

            try:
                yield
            except asyncio.CancelledError:
                # 在 yield 阶段被取消，这是正常的关闭流程
                logger.debug("🔄 FastAPI lifespan cancelled during operation (normal shutdown)")
            except Exception as e:
                logger.error(f"❌ Error during FastAPI lifespan: {e}")
            finally:
                # 关闭阶段 - 完全避免任何可能导致 CancelledError 的操作
                logger.debug("🛑 FastAPI application shutting down...")

                # 设置关闭开始标志
                self._lifespan_shutdown_started = True

                # 不使用 asyncio.sleep，避免被取消
                # 直接设置关闭完成标志
                self._lifespan_shutdown_complete = True
                logger.debug("✅ FastAPI application shutdown completed")

        # 安全访问配置属性
        docs_enabled = getattr(self.properties.docs, "enabled", True) if self.properties.docs else True

        app = FastAPI(
            title=self.properties.title,
            description=self.properties.description,
            version=self.properties.version,
            docs_url="/docs" if docs_enabled else None,
            redoc_url="/redoc" if docs_enabled else None,
            lifespan=lifespan,
        )

        logger.debug("FastAPI application created with custom lifespan handler")
        return app

    async def _configure_components(self) -> None:
        """配置所有组件到 FastAPI 应用（统一处理）"""
        if self.fastapi_app is None or self.middleware_registry is None:
            raise RuntimeError("FastAPI app or middleware registry not initialized")

        # 2. 配置中间件（统一处理，避免重复）
        self.middleware_registry.configure(self.fastapi_app, self.properties)
        logger.debug("✅ Middlewares configured")

        # 3. 配置异常处理器
        self._setup_exception_handlers()
        logger.debug("✅ Exception handlers configured")

    def _setup_exception_handlers(self) -> None:
        """配置异常处理器"""
        if self.fastapi_app is None or self.exception_handler is None:
            raise RuntimeError("FastAPI app or exception handler not initialized")

        @self.fastapi_app.exception_handler(Exception)
        async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
            """全局异常处理器"""
            try:
                if self.exception_handler:
                    return await self.exception_handler.handle_exception(request, exc)
                else:
                    # 如果没有异常处理器，创建默认响应
                    from .response import ApiResponse

                    response = ApiResponse.internal_error(message=str(exc))
                    return JSONResponse(status_code=500, content=response.to_dict())
            except Exception as e:
                logger.error(f"Exception handler failed: {e}")
                # 创建回退响应
                from .response import ApiResponse

                api_response = ApiResponse.internal_error(message="系统异常", request_id=getattr(request.state, "request_id", None))
                return JSONResponse(status_code=500, content=api_response.to_dict())

        # 注册特定异常处理器
        @self.fastapi_app.exception_handler(BusinessError)
        async def business_error_handler(request: Request, exc: BusinessError) -> JSONResponse:
            """业务异常处理器"""
            request_id = getattr(request.state, "request_id", None)
            api_response = exc.to_response(request_id)
            return JSONResponse(status_code=api_response.code, content=api_response.to_dict())

        @self.fastapi_app.exception_handler(ValidationError)
        async def validation_error_handler(request: Request, exc: ValidationError) -> JSONResponse:
            """校验异常处理器"""
            request_id = getattr(request.state, "request_id", None)
            api_response = exc.to_response(request_id)
            return JSONResponse(status_code=api_response.code, content=api_response.to_dict())

    def _setup_static_files(self) -> None:
        """设置静态文件服务"""
        if not self.properties.static or not getattr(self.properties.static, "enabled", False):
            return

        static_dir = getattr(self.properties.static, "directory", "static")
        mount_path = getattr(self.properties.static, "mount_path", "/static")

        # 检查静态文件目录是否存在
        from pathlib import Path

        static_path = Path(static_dir)
        check_dir = getattr(self.properties.static, "check_dir", True)
        if check_dir and not static_path.exists():
            if static_path.is_absolute():
                # 绝对路径,尝试创建目录
                try:
                    static_path.mkdir(parents=True, exist_ok=True)
                    logger.info(f"Created static directory: {static_dir}")
                except OSError as e:
                    logger.warning(f"Failed to create static directory {static_dir}: {e}")
                    return
            else:
                # 相对路径,记录警告但不创建
                logger.warning(f"Static directory does not exist: {static_dir}")
                return

        try:
            # 挂载静态文件服务
            if self.fastapi_app is not None:
                html_enabled = getattr(self.properties.static, "html", False)
                self.fastapi_app.mount(mount_path, StaticFiles(directory=static_dir, html=html_enabled), name="static")
                logger.info(f"Mounted static files: {mount_path} -> {static_dir}")
        except Exception as e:
            logger.error(f"Failed to mount static files: {e}")

    def _print_routes(self) -> None:
        """打印已注册的路由信息"""
        if not self.fastapi_app:
            return

        # 调试信息：打印FastAPI实例ID和路由详细信息
        logger.debug(f"_print_routes FastAPI app ID: {id(self.fastapi_app)} (type: {type(self.fastapi_app).__name__})")
        logger.debug(f"FastAPI app routes count: {len(self.fastapi_app.routes)}")
        for i, route in enumerate(self.fastapi_app.routes):
            logger.debug(
                f"Route {i}: {type(route).__name__} - path: {getattr(route, 'path', 'N/A')}, "
                f"methods: {getattr(route, 'methods', 'N/A')}, name: {getattr(route, 'name', 'N/A')}"
            )

        routes = []
        for route in self.fastapi_app.routes:
            if hasattr(route, "path") and hasattr(route, "methods"):
                # HTTP路由
                methods = sorted(list(getattr(route, "methods", [])))
                routes.append({"methods": methods, "path": getattr(route, "path", ""), "name": getattr(route, "name", ""), "type": "HTTP"})
            elif hasattr(route, "path"):
                # 其他路由（如静态文件、WebSocket等）
                route_type = type(route).__name__
                routes.append({"methods": [], "path": getattr(route, "path", ""), "name": getattr(route, "name", ""), "type": route_type})

        if routes:
            logger.info("📋 Registered Routes:")
            logger.info("=" * 60)

            # 按路径排序
            routes.sort(key=lambda x: x["path"])

            for route in routes:
                if route["methods"]:
                    methods_str = ", ".join(route["methods"])
                    logger.info(f"  [{methods_str:12}] {route['path']}")
                else:
                    logger.info(f"  [{route['type']:12}] {route['path']}")

            logger.info("=" * 60)
            logger.info(f"📊 Total: {len(routes)} routes registered")
        else:
            logger.warning("⚠️  No routes registered")

    async def start(self, host: Optional[str] = None, port: Optional[int] = None) -> bool:
        """启动Web应用（默认非阻塞）

        Args:
            host: 主机地址，覆盖配置中的host
            port: 端口号，覆盖配置中的port

        Returns:
            bool: 启动是否成功
        """
        # 覆盖配置
        if host is not None:
            self.properties.host = host
        if port is not None:
            self.properties.port = port

        if not self.properties.enabled:
            logger.debug("Web application is disabled")
            return False

        if self._state in (WebApplicationState.STARTING, WebApplicationState.RUNNING):
            logger.warning("Web server is already starting or running")
            return False

        # 确保应用已初始化
        await self._initialized()

        # 非阻塞启动服务器
        return self._start_server_nonblocking()

    async def _initialized(self) -> None:
        """确保应用已初始化（内部方法）"""
        if self._state != WebApplicationState.UNINITIALIZED:
            return

        logger.info("🚀 Initializing web application")

        try:
            # 1. 创建核心组件（一次性）
            self._create_components()

            # 2. 创建 FastAPI 应用（如果还没有创建）
            if self.fastapi_app is None:
                self.fastapi_app = self._create_fastapi_app()
                logger.debug(f"FastAPI app created during initialization: {id(self.fastapi_app)}")
            else:
                logger.debug(f"Using existing FastAPI app: {id(self.fastapi_app)}")

            # 3. 配置组件到应用（一次性）
            await self._configure_components()

            # 4. 设置静态文件
            self._setup_static_files()

            # 记录初始化时间
            init_time = time.time() - self._init_start_time
            self._state = WebApplicationState.INITIALIZED

            logger.info(f"🎉 Web application initialized successfully in {init_time:.3f}s")

        except Exception as e:
            logger.error(f"❌ Failed to initialize web application: {e}")
            raise

    def _start_server_nonblocking(self) -> bool:
        """非阻塞启动服务器"""
        try:
            # 检查是否有运行中的事件循环
            loop = asyncio.get_running_loop()

            # 在现有事件循环中创建启动任务
            loop.create_task(self._start_server_async())
            logger.debug("✅ Web server startup task created in existing event loop")
            return True

        except RuntimeError:
            # 没有运行中的事件循环
            logger.error("❌ No running event loop found. Non-blocking startup requires an active event loop.")
            logger.info("💡 Suggestion: Call start() from within an async function")
            return False

    async def _start_server_async(self) -> None:
        """异步启动服务器（内部方法）"""
        self._state = WebApplicationState.STARTING

        try:
            # 创建服务器配置
            if self.fastapi_app is None:
                raise RuntimeError("FastAPI app not initialized")

            config = uvicorn.Config(
                app=self.fastapi_app,
                host=str(self.properties.host),
                port=int(self.properties.port),
                log_level=str(self.properties.log_level).lower(),
                access_log=bool(self.properties.logging.enabled if self.properties.logging else True),
            )

            self._server = uvicorn.Server(config)

            # 启动服务器
            logger.info(f"🚀 Starting web server on {self.properties.host}:{self.properties.port}")

            # 创建独立的服务器任务，完全避免 CancelledError
            async def _run_server() -> None:
                """独立的服务器运行任务，优雅处理取消"""
                try:
                    if self._server is not None:
                        await self._server.serve()
                except asyncio.CancelledError:
                    # 完全忽略取消错误，不重新抛出
                    logger.debug("🔄 Server task cancelled (handled gracefully)")
                    return  # 正常返回，不抛出异常
                except Exception as e:
                    logger.error(f"❌ Server task error: {e}")
                    raise

            # 启动服务器任务
            self._server_task = asyncio.create_task(_run_server())

            # 等待服务器启动完成
            await self._wait_for_server_startup()

            self._state = WebApplicationState.RUNNING
            logger.info(f"🎉 Web server running on {self.properties.host}:{self.properties.port}")

            # 打印已注册的路由信息
            self._print_routes()

            # 等待服务器任务完成
            if self._server_task is not None:
                await self._server_task

        except Exception as e:
            logger.error(f"❌ Failed to start web server: {e}")
            self._state = WebApplicationState.INITIALIZED
            raise
        finally:
            # 只设置状态，不打印日志（日志在stop方法中打印）
            self._state = WebApplicationState.STOPPED

    async def _wait_for_server_startup(self, timeout: float = 10.0) -> None:
        """等待服务器启动完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 检查 lifespan 启动是否完成
            if self._lifespan_startup_complete:
                logger.debug("✅ Server startup completed (lifespan ready)")
                return

            # 检查服务器是否已经启动
            if self._server and hasattr(self._server, "started") and self._server.started:
                logger.debug("✅ Server startup completed (server ready)")
                return

            # 短暂等待
            await asyncio.sleep(0.1)

        # 超时警告，但不抛出异常
        logger.warning(f"⚠️ Server startup timeout ({timeout}s), continuing anyway...")

    async def _wait_for_lifespan_shutdown_start(self, timeout: float = 2.0) -> None:
        """等待 lifespan 关闭开始"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self._lifespan_shutdown_started:
                logger.debug("✅ Lifespan shutdown started")
                return
            await asyncio.sleep(0.1)

        logger.debug("⚠️ Lifespan shutdown start timeout, continuing...")

    async def stop(self, force: bool = False, timeout: float = 10.0) -> None:
        """停止Web服务器并清理所有资源

        Args:
            force: 是否强制停止（跳过优雅关闭）
            timeout: 停止超时时间（秒）
        """
        if self._state not in (WebApplicationState.RUNNING, WebApplicationState.STARTING):
            logger.debug(f"Web server is not running (current state: {self._state.value})")
            return

        # 立即打印停止日志，确保在应用上下文停止之前
        logger.info("🛑 Web server stopped")
        self._state = WebApplicationState.STOPPING

        try:
            if force:
                # 强制停止模式
                await self._force_stop()
            else:
                # 优雅停止模式
                await self._graceful_stop(timeout)

            # 执行完整的资源清理
            await self._cleanup()

            self._state = WebApplicationState.STOPPED
            logger.debug("✅ Web server cleanup completed")

        except Exception as e:
            logger.error(f"❌ Error stopping server: {e}")
            # 即使出错也要尝试清理和标记为已停止
            try:
                await self._cleanup()
            except Exception as cleanup_error:
                logger.error(f"❌ Error during emergency cleanup: {cleanup_error}")
            finally:
                self._state = WebApplicationState.STOPPED

    async def _graceful_stop(self, timeout: float) -> None:
        """优雅停止服务器"""
        try:
            # 设置退出标志
            if self._server:
                self._server.should_exit = True
                logger.debug("🔄 Server exit flag set")

            # 等待 lifespan 关闭开始
            await self._wait_for_lifespan_shutdown_start(timeout=3.0)

            # 等待服务器任务完成（带更长的超时时间）
            if self._server_task and not self._server_task.done():
                try:
                    # 给 uvicorn 更多时间完成 lifespan 事件处理
                    extended_timeout = max(timeout, 15.0)  # 至少 15 秒
                    await asyncio.wait_for(self._server_task, timeout=extended_timeout)
                    logger.debug("✅ Server task completed gracefully")
                except asyncio.TimeoutError:
                    logger.warning(f"⚠️ Graceful shutdown timeout ({extended_timeout}s)")
                    # 不要强制停止，让任务自然完成
                    logger.info("🔄 Allowing server task to complete naturally...")

                    # 再等待一段时间，但不强制取消
                    try:
                        await asyncio.wait_for(self._server_task, timeout=5.0)
                        logger.debug("✅ Server task completed after extended wait")
                    except asyncio.TimeoutError:
                        logger.warning("⚠️ Server task still running, but proceeding with shutdown")

        except Exception as e:
            logger.error(f"❌ Error during graceful stop: {e}")
            # 不要回退到强制停止，让服务器自然关闭

    async def _force_stop(self) -> None:
        """温和停止服务器（避免强制取消）"""
        try:
            # 设置退出标志，但不强制取消任务
            if self._server:
                self._server.should_exit = True
                if hasattr(self._server, "force_exit"):
                    self._server.force_exit = True

            # 给服务器更多时间自然关闭，避免强制取消
            if self._server_task and not self._server_task.done():
                try:
                    # 等待更长时间让 uvicorn 完成 lifespan 处理
                    await asyncio.wait_for(self._server_task, timeout=10.0)
                    logger.debug("✅ Server stopped naturally")
                except asyncio.TimeoutError:
                    # 即使超时也不强制取消，让任务自然完成
                    logger.warning("⚠️ Server task timeout, but allowing natural completion")
                    logger.info("🔄 Server will complete shutdown in background")

            logger.debug("✅ Server stop initiated (may complete in background)")

        except Exception as e:
            logger.error(f"❌ Error during server stop: {e}")
            # 继续执行，不抛出异常

    async def register_controller(self, controller_instance: Any, controller_name: str) -> bool:
        """注册控制器到Web应用

        Args:
            controller_instance: 控制器实例
            controller_name: 控制器名称

        Returns:
            bool: 注册是否成功

        Raises:
            RuntimeError: 如果应用未初始化
        """
        # 检查应用状态，未初始化则报错
        if not self.is_initialized():
            raise RuntimeError(
                "Cannot register controller: WebApplication is not initialized. Please call 'await app.start()' or 'await app._initialized()' first."
            )

        if not self.fastapi_app:
            raise RuntimeError(
                "Cannot register controller: FastAPI application is not available. Please ensure the WebApplication is properly initialized."
            )

        if self.controller_registry is None:
            raise RuntimeError("Controller registry not initialized")

        # 委托给控制器注册表，传入FastAPI应用实例
        return await self.controller_registry.register(controller_instance, controller_name, self.fastapi_app)

    async def get_status(self) -> Dict[str, Any]:
        """异步获取智能组件状态

        Returns:
            智能组件状态信息
        """
        status = {
            "application_state": self._state.value,
            "is_running": self.is_running(),
            "is_initialized": self.is_initialized(),
            "components_enabled": True,
            "controller_registry": {"type": "traditional", "status": "unknown"},
            "middleware_registry": {"type": "traditional", "status": "unknown"},
            "parameter_binder": {"type": "traditional", "status": "unknown"},
            "exception_handler": {"type": "traditional", "status": "unknown"},
            "response_middleware": {"type": "traditional", "status": "unknown"},
        }

        try:
            # 检查控制器注册器
            if self.controller_registry:
                controllers = await self.controller_registry.get_controllers()
                status["controller_registry"] = {
                    "type": "async",
                    "status": "active",
                    "controllers_count": str(len(controllers)),
                }

            # 检查中间件注册表
            if self.middleware_registry:
                middlewares_count = 0
                if hasattr(self.middleware_registry, "get_all_middlewares"):
                    middlewares = self.middleware_registry.get_all_middlewares()
                    middlewares_count = len(middlewares) if middlewares else 0

                status["middleware_registry"] = {
                    "type": "traditional",
                    "status": "active",
                    "middlewares_count": str(middlewares_count),
                }

            # 检查参数绑定器
            if self.parameter_binder:
                statistics = {}
                performance = {}
                if hasattr(self.parameter_binder, "get_statistics"):
                    statistics = getattr(self.parameter_binder, "get_statistics", dict)()
                if hasattr(self.parameter_binder, "get_parameter_performance"):
                    performance = getattr(self.parameter_binder, "get_parameter_performance", dict)()

                status["parameter_binder"] = {
                    "type": "standard",
                    "status": "active",
                    "statistics": str(statistics),
                    "performance": str(performance),
                }
            else:
                status["parameter_binder"] = {"type": "traditional", "status": "disabled"}

            # 检查全局异常处理器
            if self.exception_handler:
                status["exception_handler"] = {
                    "type": "smart",
                    "status": "active",
                    "include_traceback": str(self.exception_handler.include_traceback),
                    "log_exceptions": str(self.exception_handler.log_exceptions),
                    "handlers_count": str(len(self.exception_handler._handlers)),
                }
            else:
                status["exception_handler"] = {"type": "traditional", "status": "disabled"}

            # 检查响应处理中间件
            if self.response_middleware:
                cors_enabled = getattr(self.response_middleware, "cors_enabled", False)
                status["response_middleware"] = {
                    "type": "smart",
                    "status": "active",
                    "auto_wrap_response": str(self.response_middleware.auto_wrap_response),
                    "add_request_id": str(self.response_middleware.add_request_id),
                    "add_response_time": str(self.response_middleware.add_response_time),
                    "cors_enabled": str(cors_enabled),
                }
            else:
                status["response_middleware"] = {"type": "traditional", "status": "disabled"}

        except Exception as e:
            status["error"] = str(e)

        return status

    async def _cleanup(self) -> None:
        """完整的资源清理 - 分阶段执行"""
        cleanup_errors = []

        # 定义清理阶段
        cleanup_stages = [
            ("Server Resources", self._cleanup_server_resources),
            ("Component Resources", self._cleanup_component_resources),
            ("FastAPI Resources", self._cleanup_fastapi_resources),
            ("Async Tasks", self._cleanup_async_tasks),
            ("System Resources", self._cleanup_system_resources),
        ]

        logger.info("🧹 Starting comprehensive resource cleanup...")

        # 分阶段执行清理
        for stage_name, cleanup_func in cleanup_stages:
            try:
                logger.debug(f"🔄 Cleaning up {stage_name}...")
                await cleanup_func()
                logger.debug(f"✅ {stage_name} cleaned up successfully")
            except Exception as e:
                error_msg = f"{stage_name} cleanup failed: {e}"
                cleanup_errors.append(error_msg)
                logger.error(f"❌ {error_msg}")
                # 继续下一阶段，不中断整个清理过程

        # 检查资源泄漏
        leaks = self._check_resource_leaks()
        if leaks:
            logger.warning(f"⚠️ Potential resource leaks detected: {leaks}")

        # 报告清理结果
        if cleanup_errors:
            logger.warning(f"⚠️ Cleanup completed with {len(cleanup_errors)} errors")
        else:
            logger.info("✅ All resources cleaned up successfully")

    async def _cleanup_server_resources(self) -> None:
        """清理服务器相关资源"""
        try:
            # 清理服务器实例
            if self._server:
                # 确保服务器已停止
                if hasattr(self._server, "should_exit"):
                    self._server.should_exit = True

                # 等待服务器完全关闭
                if hasattr(self._server, "force_exit"):
                    self._server.force_exit = True

                self._server = None
                logger.debug("Server instance cleaned up")

            # 清理服务器任务
            if self._server_task:
                if not self._server_task.done():
                    self._server_task.cancel()
                    try:
                        await asyncio.wait_for(self._server_task, timeout=3.0)
                    except (asyncio.TimeoutError, asyncio.CancelledError):
                        logger.debug("Server task cleanup timeout/cancelled")

                self._server_task = None
                logger.debug("Server task cleaned up")

        except Exception as e:
            logger.error(f"Error cleaning up server resources: {e}")
            raise

    async def _cleanup_component_resources(self) -> None:
        """清理组件资源"""
        try:
            # 清理控制器注册表
            if self.controller_registry:
                if hasattr(self.controller_registry, "cleanup"):
                    self.controller_registry.cleanup()
                self.controller_registry = None
                logger.debug("Controller registry cleaned up")

            # 清理中间件注册表
            if self.middleware_registry:
                if hasattr(self.middleware_registry, "cleanup"):
                    self.middleware_registry.cleanup()
                self.middleware_registry = None
                logger.debug("Middleware registry cleaned up")

            # 清理参数绑定器
            if self.parameter_binder:
                if hasattr(self.parameter_binder, "cleanup"):
                    self.parameter_binder.cleanup()
                self.parameter_binder = None
                logger.debug("Parameter binder cleaned up")

            # 清理异常处理器
            if self.exception_handler:
                if hasattr(self.exception_handler, "cleanup"):
                    self.exception_handler.cleanup()
                self.exception_handler = None
                logger.debug("Exception handler cleaned up")

            # 清理响应中间件
            if self.response_middleware:
                if hasattr(self.response_middleware, "cleanup"):
                    self.response_middleware.cleanup()
                self.response_middleware = None
                logger.debug("Response middleware cleaned up")

        except Exception as e:
            logger.error(f"Error cleaning up component resources: {e}")
            raise

    async def _cleanup_fastapi_resources(self) -> None:
        """清理FastAPI应用资源"""
        try:
            if self.fastapi_app:
                # 清理路由缓存
                if hasattr(self, "_cached_routes"):
                    delattr(self, "_cached_routes")
                    logger.debug("Route cache cleared")

                # 清理FastAPI应用
                self.fastapi_app = None
                logger.debug("FastAPI application cleaned up")

        except Exception as e:
            logger.error(f"Error cleaning up FastAPI resources: {e}")
            raise

    async def _cleanup_async_tasks(self) -> None:
        """清理异步任务"""
        try:
            # 获取当前事件循环中的所有任务
            current_task = asyncio.current_task()
            all_tasks = [task for task in asyncio.all_tasks() if task != current_task]

            if all_tasks:
                logger.debug(f"Cancelling {len(all_tasks)} background tasks...")

                # 取消所有任务
                for task in all_tasks:
                    if not task.done():
                        task.cancel()

                # 等待任务完成或超时
                try:
                    await asyncio.wait_for(asyncio.gather(*all_tasks, return_exceptions=True), timeout=3.0)
                    logger.debug("Background tasks cleaned up")
                except asyncio.TimeoutError:
                    logger.warning("Some background tasks cleanup timeout")

        except Exception as e:
            logger.debug(f"Error cleaning up async tasks: {e}")
            # 不抛出异常，因为这不是致命错误

    async def _cleanup_system_resources(self) -> None:
        """清理系统资源"""
        try:
            # 强制垃圾回收
            import gc

            collected = gc.collect()
            if collected > 0:
                logger.debug(f"Garbage collected {collected} objects")

            # 等待一小段时间确保所有资源都已释放
            await asyncio.sleep(0.1)

            logger.debug("System resources cleaned up")

        except Exception as e:
            logger.debug(f"Error cleaning up system resources: {e}")
            # 不抛出异常，因为这不是致命错误

    def _check_resource_leaks(self) -> list[str]:
        """检测潜在的资源泄漏"""
        leaks = []

        try:
            # 检查服务器资源
            if self._server is not None:
                leaks.append("Server instance not cleaned")
            if self._server_task is not None:
                leaks.append("Server task not cleaned")

            # 检查组件资源
            if self.controller_registry is not None:
                leaks.append("Controller registry not cleaned")
            if self.middleware_registry is not None:
                leaks.append("Middleware registry not cleaned")
            if self.parameter_binder is not None:
                leaks.append("Parameter binder not cleaned")
            if self.exception_handler is not None:
                leaks.append("Exception handler not cleaned")
            if self.response_middleware is not None:
                leaks.append("Response middleware not cleaned")

            # 检查FastAPI资源
            if self.fastapi_app is not None:
                leaks.append("FastAPI application not cleaned")

        except Exception as e:
            logger.debug(f"Error checking resource leaks: {e}")
            leaks.append(f"Leak detection error: {e}")

        return leaks

    def is_running(self) -> bool:
        """检查服务器是否正在运行"""
        return self._state == WebApplicationState.RUNNING

    def is_initialized(self) -> bool:
        """检查应用是否已初始化"""
        return self._state in (
            WebApplicationState.INITIALIZED,
            WebApplicationState.STARTING,
            WebApplicationState.RUNNING,
            WebApplicationState.STOPPING,
            WebApplicationState.STOPPED,
        )

    def get_state(self) -> WebApplicationState:
        """获取当前应用状态"""
        return self._state

    def get_app(self) -> FastAPI:
        """获取FastAPI应用实例（延迟初始化）

        Returns:
            FastAPI应用实例

        Raises:
            RuntimeError: 如果应用未初始化
        """
        # 如果 FastAPI 应用还没有创建，进行最小化初始化
        if self.fastapi_app is None:
            logger.debug("FastAPI app not created yet, performing minimal initialization...")

            # 只创建 FastAPI 应用，不进行完整初始化
            self.fastapi_app = self._create_fastapi_app()
            logger.debug(f"FastAPI app created via minimal initialization: {id(self.fastapi_app)}")

        logger.debug(f"Returning FastAPI app: {id(self.fastapi_app)}")
        return self.fastapi_app
