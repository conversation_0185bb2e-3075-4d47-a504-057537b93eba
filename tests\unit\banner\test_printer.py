#!/usr/bin/env python
"""
* @author: cz
* @description: 横幅打印器单元测试
"""

import tempfile
import unittest
from io import StringIO
from pathlib import Path
from unittest.mock import Mock, patch

from miniboot.banner.printer import (ConsoleBannerPrinter, FileBannerPrinter,
                                     LogBannerPrinter)
from miniboot.banner.properties import BannerConfig, BannerMode
from miniboot.banner.resource import DefaultBannerResource


class ConsoleBannerPrinterTestCase(unittest.TestCase):
    """控制台横幅打印器测试用例"""

    def setUp(self):
        """测试前准备"""
        self.output = StringIO()
        self.printer = ConsoleBannerPrinter(self.output)
        self.resource = DefaultBannerResource("Test App", "1.0.0")

    def test_print_banner_console_mode(self):
        """测试控制台模式打印"""
        config = BannerConfig(mode=BannerMode.CONSOLE)

        self.printer.print_banner(self.resource, config)

        output = self.output.getvalue()
        self.assertIn("Mini-Boot", output)
        self.assertIn("Test App", output)

    def test_print_banner_off_mode(self):
        """测试关闭模式不打印"""
        config = BannerConfig(mode=BannerMode.OFF)

        self.printer.print_banner(self.resource, config)

        output = self.output.getvalue()
        self.assertEqual(output, "")

    def test_print_banner_log_mode(self):
        """测试日志模式不打印到控制台"""
        config = BannerConfig(mode=BannerMode.LOG)

        self.printer.print_banner(self.resource, config)

        output = self.output.getvalue()
        self.assertEqual(output, "")

    def test_process_content(self):
        """测试处理横幅内容"""
        config = BannerConfig()

        content = self.printer._process_content(self.resource, config)

        # 变量应该被替换
        self.assertNotIn("${", content)
        self.assertIn("0.0.4", content)  # miniboot.version
        self.assertIn("Test App", content)  # application.name

    def test_adjust_width(self):
        """测试调整宽度"""
        long_content = "This is a very long line that exceeds the width limit"

        adjusted = self.printer._adjust_width(long_content, 20)

        lines = adjusted.split("\n")
        for line in lines:
            self.assertLessEqual(len(line), 20)





    def test_print_info(self):
        """测试打印附加信息"""
        config = BannerConfig(show_version=True, show_environment=True, show_startup_time=True)

        self.printer._print_info(self.resource, config)

        output = self.output.getvalue()
        self.assertIn("Application Version", output)
        self.assertIn("Python Version", output)
        self.assertIn("Operating System", output)
        self.assertIn("Started at", output)

    def test_print_info_selective(self):
        """测试选择性打印附加信息"""
        config = BannerConfig(show_version=True, show_environment=False, show_startup_time=False)

        self.printer._print_info(self.resource, config)

        output = self.output.getvalue()
        self.assertIn("Application Version", output)
        self.assertNotIn("Operating System", output)
        self.assertNotIn("Started at", output)

    def test_custom_properties(self):
        """测试自定义属性"""
        config = BannerConfig(properties={"custom.key": "custom.value"})

        # 创建包含自定义变量的资源
        class CustomResource:
            def get_content(self):
                return "Custom: ${custom.key}"

            def get_variables(self):
                return {"custom.key": "default.value"}

        custom_resource = CustomResource()
        content = self.printer._process_content(custom_resource, config)

        # 配置中的属性应该覆盖资源中的变量
        self.assertIn("custom.value", content)


class FileBannerPrinterTestCase(unittest.TestCase):
    """文件横幅打印器测试用例"""

    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_file = Path(self.temp_dir) / "banner_output.txt"
        self.printer = FileBannerPrinter(str(self.test_file))
        self.resource = DefaultBannerResource("File App", "2.0.0")

    def tearDown(self):
        """测试后清理"""
        import shutil

        if Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)

    def test_print_banner_to_file(self):
        """测试打印横幅到文件"""
        config = BannerConfig(mode=BannerMode.CONSOLE)

        self.printer.print_banner(self.resource, config)

        # 检查文件是否创建并包含内容
        self.assertTrue(self.test_file.exists())
        content = self.test_file.read_text(encoding="utf-8")
        self.assertIn("Mini-Boot", content)
        self.assertIn("File App", content)

    @patch("pathlib.Path.write_text", side_effect=OSError("Permission denied"))
    @patch("sys.stderr", new_callable=StringIO)
    def test_print_banner_file_error(self, mock_stderr, _mock_write_text):
        """测试文件写入错误处理"""
        config = BannerConfig(mode=BannerMode.CONSOLE)

        # 应该不抛出异常，而是输出错误信息
        self.printer.print_banner(self.resource, config)

        error_output = mock_stderr.getvalue()
        self.assertIn("Failed to write banner", error_output)


class LogBannerPrinterTestCase(unittest.TestCase):
    """日志横幅打印器测试用例"""

    def setUp(self):
        """测试前准备"""
        self.mock_logger = Mock()
        self.printer = LogBannerPrinter(self.mock_logger)
        self.resource = DefaultBannerResource("Log App", "3.0.0")

    def test_print_banner_to_log(self):
        """测试打印横幅到日志"""
        config = BannerConfig(enabled=True, mode=BannerMode.LOG)

        self.printer.print_banner(self.resource, config)

        # 检查logger.info是否被调用
        self.mock_logger.info.assert_called()

        # 检查调用的内容
        call_args = [call[0][0] for call in self.mock_logger.info.call_args_list]
        banner_content = "\n".join(call_args)
        self.assertIn("_____", banner_content)

    def test_print_banner_console_mode(self):
        """测试控制台模式不输出到日志"""
        config = BannerConfig(mode=BannerMode.CONSOLE)

        self.printer.print_banner(self.resource, config)

        # 日志模式下不应该调用logger
        self.mock_logger.info.assert_not_called()

    def test_print_banner_no_logger(self):
        """测试没有logger时的处理"""
        printer = LogBannerPrinter(None)
        config = BannerConfig(mode=BannerMode.LOG)

        # 应该不抛出异常，并且不会有输出
        try:
            printer.print_banner(self.resource, config)
            # 如果没有异常，测试通过
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"不应该抛出异常: {e}")

    def test_skip_empty_lines(self):
        """测试跳过空行"""
        config = BannerConfig(mode=BannerMode.LOG)

        self.printer.print_banner(self.resource, config)

        # 检查没有空行被记录
        call_args = [call[0][0] for call in self.mock_logger.info.call_args_list]
        for arg in call_args:
            self.assertTrue(arg.strip())  # 所有参数都应该有内容


class BannerPrinterEdgeCasesTestCase(unittest.TestCase):
    """Banner打印器边界条件测试"""

    def test_console_printer_with_none_output(self):
        """测试控制台打印器使用None输出流"""
        printer = ConsoleBannerPrinter(None)
        resource = DefaultBannerResource("Test", "1.0.0")
        config = BannerConfig()

        # 应该不抛出异常
        try:
            printer.print_banner(resource, config)
        except Exception as e:
            self.fail(f"不应该抛出异常: {e}")

    def test_file_printer_with_invalid_path(self):
        """测试文件打印器使用无效路径"""
        printer = FileBannerPrinter("/invalid/path/banner.txt")
        resource = DefaultBannerResource("Test", "1.0.0")
        config = BannerConfig()

        # 应该不抛出异常，而是静默失败
        try:
            printer.print_banner(resource, config)
        except Exception as e:
            self.fail(f"不应该抛出异常: {e}")

    def test_console_printer_width_adjustment(self):
        """测试控制台打印器宽度调整"""
        output = StringIO()
        printer = ConsoleBannerPrinter(output)

        long_content = "This is a very long line that should be adjusted to fit within the specified width limit"
        adjusted = printer._adjust_width(long_content, 20)

        lines = adjusted.split("\n")
        for line in lines:
            self.assertLessEqual(len(line), 20)



    def test_log_printer_with_mock_logger(self):
        """测试日志打印器使用模拟logger"""
        mock_logger = Mock()
        printer = LogBannerPrinter(mock_logger)
        resource = DefaultBannerResource("Test", "1.0.0")
        config = BannerConfig(enabled=True, mode=BannerMode.LOG)

        printer.print_banner(resource, config)

        # 验证logger被调用
        mock_logger.info.assert_called()

    def test_console_printer_process_content(self):
        """测试控制台打印器处理Banner内容"""
        output = StringIO()
        printer = ConsoleBannerPrinter(output)

        # 创建一个模拟资源
        resource = DefaultBannerResource("TestApp", "1.0.0")
        config = BannerConfig()

        processed = printer._process_content(resource, config)
        self.assertIn("TestApp", processed)
        self.assertIn("1.0.0", processed)



    def test_file_printer_create_directory(self):
        """测试文件打印器创建目录"""
        import tempfile

        temp_dir = tempfile.mkdtemp()
        from pathlib import Path

        file_path = str(Path(temp_dir) / "subdir" / "banner.txt")

        try:
            printer = FileBannerPrinter(file_path)
            resource = DefaultBannerResource("Test", "1.0.0")
            config = BannerConfig()

            printer.print_banner(resource, config)

            # 由于目录不存在，文件创建会失败，但不应该抛出异常
            # 这测试了错误处理逻辑
            pass
        finally:
            # 清理
            import shutil

            temp_path = Path(temp_dir)
            if temp_path.exists():
                shutil.rmtree(temp_dir)


if __name__ == "__main__":
    unittest.main()
