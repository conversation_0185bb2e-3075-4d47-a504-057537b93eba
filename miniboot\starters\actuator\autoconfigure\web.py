#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Actuator Web 自动配置类

实现 Actuator 与 Web 框架的自动集成配置，包括：
- Web 集成条件检查
- 动态路由注册器配置
- FastAPI 集成支持
- 条件化 Web 功能启用

配置条件：
- FastAPI 类可用：@ConditionalOnClass(name="fastapi.FastAPI")
- Actuator Web 启用：@ConditionalOnProperty(name="miniboot.starters.actuator.web.enabled", match_if_missing=True)

使用示例：
    # application.yml
    miniboot:
        starters:
            actuator:
                web:
                    enabled: true  # 启用 Actuator Web 集成
                base_path: "/actuator"
                endpoints:
                    health: true
                    info: true
                    metrics: true
"""

from typing import Optional

from loguru import logger

from miniboot.annotations import (Bean, ConditionalOnBean, ConditionalOnClass,
                                  ConditionalOnProperty, Configuration,
                                  PostConstruct)
from miniboot.annotations.conditional import (AutoConfigureAfter,
                                              AutoConfigureOrder)
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.bean.base import ApplicationContextAware

from ..properties import ActuatorProperties


@AutoConfigureOrder(100)  # 高优先级，确保在Web模块初始化后执行
@AutoConfigureAfter("web-auto-configuration")  # 明确依赖Web自动配置
@ConditionalOnClass(name="fastapi.FastAPI")
@ConditionalOnProperty(name="miniboot.starters.actuator.web.enabled", match_if_missing=True)
@ConditionalOnProperty(name="miniboot.web.enabled", match_if_missing=True)
class WebAutoConfiguration(AutoConfiguration, ApplicationContextAware):
    """Actuator Web 自动配置类

    负责配置 Actuator 与 Web 框架的集成组件，包括：
    - WebIntegrationChecker：Web 集成条件检查器
    - ActuatorRouteRegistrar：动态路由注册器

    采用条件化配置，确保只在满足条件时才启用 Web 集成：
    1. FastAPI 框架可用
    2. Actuator Web 集成已启用
    """

    def __init__(self):
        """初始化 Web 自动配置"""
        super().__init__()
        self._application_context = None
        self._route_registrar = None

    def set_application_context(self, application_context: "ApplicationContext") -> None:
        """设置应用上下文

        Args:
            application_context: 应用上下文实例
        """
        self._application_context = application_context
        logger.debug("Application context set for WebAutoConfiguration")

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="actuator-web-auto-configuration",
            description="Actuator Web 集成自动配置",
            priority=500,  # 更低优先级，确保在Web模块初始化之后执行
            auto_configure_after=["actuator-starter-auto-configuration", "web-auto-configuration"],
        )

    @Bean
    def actuator_web_conditions(self) -> "ActuatorWebConditions":
        """创建 Actuator Web 集成条件 Bean

        Returns:
            ActuatorWebConditions: Actuator Web 集成条件实例
        """
        from ..web.conditions import ActuatorWebConditions

        logger.debug("Creating ActuatorWebConditions bean")
        conditions = ActuatorWebConditions()

        # 记录集成状态
        info = conditions.info()
        logger.info(f"Web integration status: FastAPI={info['fastapi_available']}, "
                   f"Web module={info['web_module_enabled']}, "
                   f"Should integrate={info['should_integrate']}")

        return conditions

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.web.routes.enabled", match_if_missing=True)
    def actuator_route_registrar(
        self,
        actuator_properties: ActuatorProperties,
        actuator_web_conditions: "ActuatorWebConditions"
    ) -> Optional["ActuatorRouteRegistrar"]:
        """创建 Actuator 路由注册器 Bean

        Args:
            actuator_properties: Actuator 配置属性
            actuator_web_conditions: Actuator Web 集成条件

        Returns:
            ActuatorRouteRegistrar: 动态路由注册器实例，如果条件不满足则返回 None
        """
        from ..web.routes import ActuatorRouteRegistrar

        # 检查是否应该启用 Web 集成
        if not actuator_web_conditions.should_integrate():
            logger.info("Web integration conditions not met, skipping route registrar creation")
            return None

        logger.debug("Creating ActuatorRouteRegistrar bean")

        # 获取 FastAPI 应用实例（如果可用）
        app = self._get_fastapi_app()
        if not app:
            logger.debug("FastAPI app not available yet, route registrar will be created without app (will be set later via delayed initialization)")

        # 创建 ActuatorContext 实例
        actuator_context = self._get_actuator_context(actuator_properties)

        # 获取基础路径配置
        base_path = actuator_properties.web.base_path

        registrar = ActuatorRouteRegistrar(
            app=app,
            actuator_context=actuator_context,
            base_path=base_path
        )

        # 存储注册器实例以便后续设置 FastAPI 应用
        self._route_registrar = registrar

        # 立即尝试设置 FastAPI 应用
        self._try_setup_fastapi_app()

        logger.info(f"Created ActuatorRouteRegistrar with base_path='{base_path}'")
        return registrar

    def _get_fastapi_app(self) -> Optional["FastAPI"]:
        """获取 FastAPI 应用实例

        Returns:
            FastAPI: FastAPI 应用实例，如果不可用则返回 None
        """
        try:
            if not self._application_context:
                logger.debug("Application context not available")
                return None

            # 首先尝试获取 webApp Bean（FastAPI 实例）
            try:
                web_app = self._application_context.get_bean("webApp")
                if web_app:
                    logger.debug("Found FastAPI app instance from webApp Bean")
                    return web_app
            except Exception:
                logger.debug("webApp Bean not found")

            # 然后尝试获取 WebApplication Bean
            try:
                web_application = self._application_context.get_bean("webApplication")
                if web_application:
                    logger.debug("Found WebApplication Bean")
                    # 获取 FastAPI 应用实例
                    if hasattr(web_application, 'fastapi_app') and web_application.fastapi_app:
                        logger.debug("Found FastAPI app instance from WebApplication.fastapi_app")
                        return web_application.fastapi_app
                    elif hasattr(web_application, 'get_app') and callable(web_application.get_app):
                        logger.debug("Getting FastAPI app via WebApplication.get_app()")
                        return web_application.get_app()
                    else:
                        logger.debug("WebApplication found but no FastAPI app available")
                        return None
                else:
                    logger.debug("WebApplication Bean not found")
                    return None
            except Exception as e:
                logger.debug(f"Failed to get WebApplication Bean: {e}")
                return None

        except Exception as e:
            logger.warning(f"Failed to get FastAPI app: {e}")
            return None

    def _get_actuator_context(self, actuator_properties: ActuatorProperties) -> "ActuatorContext":
        """获取或创建 ActuatorContext 实例

        Args:
            actuator_properties: Actuator 配置属性

        Returns:
            ActuatorContext: Actuator 上下文实例
        """
        try:
            # 首先尝试从应用上下文获取现有的 ActuatorContext Bean
            if self._application_context:
                try:
                    actuator_context = self._application_context.get_bean("actuator_context")
                    logger.debug("Retrieved existing ActuatorContext Bean for route registrar")
                    return actuator_context
                except Exception as e:
                    logger.debug(f"Failed to get existing ActuatorContext Bean: {e}")

            # 如果无法获取现有Bean，则创建新实例（兜底方案）
            from miniboot.starters.actuator.context import ActuatorContext
            actuator_context = ActuatorContext(properties=actuator_properties, auto_load_config=False)
            logger.warning("Created new ActuatorContext for route registrar (should use existing Bean)")
            return actuator_context

        except Exception as e:
            logger.error(f"Failed to get ActuatorContext: {e}")
            raise

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.web.middleware.enabled", match_if_missing=False)
    def actuator_web_middleware(self) -> Optional["ActuatorWebMiddleware"]:
        """创建 Actuator Web 中间件 Bean（可选）

        Returns:
            ActuatorWebMiddleware: Web 中间件实例，如果未启用则返回 None
        """
        # 这是一个可选的中间件组件，可以在后续任务中实现
        logger.debug("ActuatorWebMiddleware not implemented yet")
        return None

    def _try_setup_fastapi_app(self) -> None:
        """尝试设置 FastAPI 应用

        这个方法会在创建 ActuatorRouteRegistrar 后立即调用，
        以及在 @PostConstruct 阶段再次调用。
        """
        if not self._route_registrar:
            logger.debug("No route registrar available, skipping FastAPI app setup")
            return

        # 尝试获取 FastAPI 应用实例
        app = self._get_fastapi_app()
        if app:
            logger.info("Setting FastAPI app for ActuatorRouteRegistrar")
            self._route_registrar.set_app(app)

            # 尝试注册路由
            try:
                success = self._route_registrar.register_routes()
                if success:
                    logger.info("🚀 Actuator routes registered successfully")
                else:
                    logger.warning("⚠️ Failed to register Actuator routes")
            except Exception as e:
                logger.error(f"❌ Error registering Actuator routes: {e}")
        else:
            logger.debug("FastAPI app not available yet, will retry later")

    @PostConstruct
    def setup_fastapi_app(self) -> None:
        """在所有 Bean 初始化完成后设置 FastAPI 应用

        这个方法会在 Web 模块初始化完成后被调用，
        此时 webApplication Bean 应该已经可用。
        """
        logger.debug("PostConstruct: Attempting to setup FastAPI app")
        self._try_setup_fastapi_app()
