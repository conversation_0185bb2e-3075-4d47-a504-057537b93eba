#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 处理器专用装饰器 - 统一异常处理和性能监控

提供处理器模块专用的装饰器，整合常用的异常处理、性能监控和重试机制，
减少重复代码，统一处理流程。
"""

import functools
from typing import Any, Callable, Optional

from ..errors import ProcessorExecutionError as BeanProcessingError


def processor_exception_handler(
    _slow_threshold: float = 1.0,
    _max_attempts: int = 3,
    _base_delay: float = 0.1,
    retry_exceptions: tuple = (BeanProcessingError,),
):
    """
    处理器专用异常处理装饰器

    整合了常用的异常处理、性能监控和重试机制，为处理器提供统一的装饰器。

    Args:
        slow_threshold: 性能监控的慢操作阈值（秒）
        max_attempts: 最大重试次数
        base_delay: 重试基础延迟（秒）
        retry_strategy: 重试策略
        retry_exceptions: 需要重试的异常类型

    Returns:
        装饰后的函数

    Example:
        @processor_exception_handler(slow_threshold=2.0, max_attempts=5)
        def process_bean(self, bean, bean_name):
            # 处理逻辑
            return bean
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if isinstance(e, retry_exceptions):
                    raise BeanProcessingError(f"Processor execution failed: {str(e)}") from e
                raise

        return wrapper

    return decorator


def lightweight_processor_handler(_slow_threshold: float = 2.0):
    """
    轻量级处理器装饰器

    只包含异常处理和性能监控，不包含重试机制。
    适用于不需要重试的处理器方法。

    Args:
        slow_threshold: 性能监控的慢操作阈值（秒）

    Returns:
        装饰后的函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                raise BeanProcessingError(f"Processor execution failed: {str(e)}") from e

        return wrapper

    return decorator


def processor_method_handler(
    _processor_name: Optional[str] = None,
    _phase: Optional[str] = None,
    _slow_threshold: float = 1.0,
):
    """
    处理器方法专用装饰器

    为处理器的具体方法（如 post_process_before_initialization）提供
    专门的异常处理和性能监控。

    Args:
        processor_name: 处理器名称（用于日志和错误信息）
        phase: 处理阶段（before/after）
        slow_threshold: 性能监控的慢操作阈值（秒）

    Returns:
        装饰后的函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, bean: Any, bean_name: str) -> Any:
            # 获取处理器名称
            actual_processor_name = _processor_name or self.__class__.__name__

            try:
                # 执行处理器方法
                result = func(self, bean, bean_name)

                return result if result is not None else bean

            except Exception as e:
                # 创建处理器专用的异常
                error_msg = f"处理器 {actual_processor_name} 在 {_phase or 'unknown'} 阶段执行失败"
                processing_error = BeanProcessingError(error_msg)

                # 重新抛出异常
                raise processing_error from e

        return wrapper

    return decorator


def cache_aware_processor(cache_key_func: Optional[Callable] = None):
    """
    缓存感知的处理器装饰器

    为处理器方法添加缓存支持，避免重复处理相同的Bean。

    Args:
        cache_key_func: 缓存键生成函数，默认使用bean_name

    Returns:
        装饰后的函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, bean: Any, bean_name: str) -> Any:
            # 生成缓存键
            cache_key = cache_key_func(bean, bean_name) if cache_key_func else f"{self.__class__.__name__}:{bean_name}"

            # 检查是否已处理
            if hasattr(self, "_processed") and cache_key in self._processed:
                return bean

            # 执行处理
            result = func(self, bean, bean_name)

            # 标记为已处理
            if not hasattr(self, "_processed"):
                self._processed = set()
            self._processed.add(cache_key)

            return result

        return wrapper

    return decorator


def supports_check_processor(supports_func: Optional[Callable] = None):
    """
    支持检查的处理器装饰器

    在处理前检查处理器是否支持当前Bean，如果不支持则跳过处理。

    Args:
        supports_func: 支持检查函数，默认调用处理器的supports方法

    Returns:
        装饰后的函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, bean: Any, bean_name: str) -> Any:
            # 执行支持检查
            if supports_func:
                if not supports_func(self, bean, bean_name):
                    return bean
            elif hasattr(self, "supports") and not self.supports(bean, bean_name):
                return bean

            # 执行处理
            return func(self, bean, bean_name)

        return wrapper

    return decorator


# 便捷的组合装饰器
def std_processor(
    slow_threshold: float = 1.0,
    _max_attempts: int = 3,
    phase: Optional[str] = None,
):
    """
    标准处理器方法装饰器

    组合了支持检查、缓存感知、异常处理和性能监控的完整装饰器。
    适用于大多数处理器方法。

    Args:
        slow_threshold: 性能监控的慢操作阈值（秒）
        max_attempts: 最大重试次数
        phase: 处理阶段（before/after）

    Returns:
        装饰后的函数
    """

    def decorator(func: Callable) -> Callable:
        # 应用装饰器链
        return supports_check_processor()(cache_aware_processor()(processor_method_handler(phase=phase, slow_threshold=slow_threshold)(func)))

    return decorator
