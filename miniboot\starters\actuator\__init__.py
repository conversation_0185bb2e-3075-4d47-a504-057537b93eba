"""
Mini-Boot Actuator Starter

提供应用监控、健康检查、指标收集等运维功能的可选 Starter 模块。
支持条件化 Web 集成，无强制依赖。

主要功能：
- 健康检查端点
- 应用信息端点
- 指标收集和暴露
- 自定义指标系统
- 性能监控
- 缓存监控
- 四层架构：Endpoints -> Collectors -> Monitoring -> Performance

使用方式：
1. 在 application.yml 中启用：
   starters:
     actuator:
       enabled: true

2. 业务代码中使用自定义指标：
   from miniboot.starters.actuator.metrics import metrics_registry, Counted, Timed

   @Service
   class MyService:
       @Counted(name="my.operations.total")
       @Timed(name="my.operation.duration")
       def do_something(self):
           pass

3. 访问监控端点：
   - GET /actuator/health - 健康检查
   - GET /actuator/info - 应用信息
   - GET /actuator/metrics - 指标数据
"""

# 缓存模块
from .cache import (AdvancedEndpointCache, CacheableEndpoint, CacheEndpoint,
                    CacheManager, CacheStrategy, adaptive_cache,
                    advanced_cache, cache_evict, cache_put, conditional_cache,
                    fifo_cache, lfu_cache, lru_cache, ttl_cache)
# 数据收集器模块
from .collectors import (AppMetricsCollector, AsyncCustomHealthIndicator,
                         AsyncDatabaseHealthIndicator,
                         AsyncDiskSpaceHealthIndicator, AsyncHealthCollector,
                         AsyncHealthIndicator, AsyncMemoryHealthIndicator,
                         HealthStatus, MetricsAggregator,
                         SystemMetricsCollector)
from .configuration import ActuatorStarterAutoConfiguration
# 端点模块
from .endpoints import (BaseMetrics, BeansEndpoint, ConfigEndpointHandler,
                        Counter, CustomEndpoint, CustomEndpointBuilder,
                        CustomEndpointHandler, CustomEndpointRegistry,
                        FunctionEndpointHandler, Gauge, HealthEndpoint,
                        HealthIndicator, HealthStatus, InfoContributor,
                        InfoEndpoint, LambdaEndpointHandler, MetricsCollector,
                        MetricsConfig, MetricsEndpoint, MetricsRegistry,
                        StatusEndpointHandler, TaskMetrics, Timer,
                        VersionEndpointHandler, create_beans_endpoint, delete,
                        endpoint, read, write)
# 监控模块
# 监控模块
# 监控模块
# 监控模块
# 监控模块
from .monitoring import (Alert, AlertChannel, AlertNotifier, AlertRule,
                         AlertSeverity, AlertStats, AlertStatus,
                         HealthChangeEvent, HealthMonitor, HealthSnapshot,
                         MonitoringAlerts)
# 性能模块
# 性能模块
# 性能模块
# 性能模块
from .performance import (ConcurrencyMetrics, ErrorMetrics, MetricType,
                          MetricValue, PerformanceAnalyzer,
                          PerformanceCollector, PerformanceContext,
                          PerformanceMetrics, PerformanceMetricsRegistry,
                          PerformanceMonitor, ResponseTimeMetrics,
                          ThroughputMetrics, conditional, count, get_metrics,
                          get_metrics_registry, monitor, perf, time)
# 配置属性
from .properties import (ActuatorConfigUtils, ActuatorProperties,
                         CoreModulesProperties, CustomMetricsProperties,
                         EndpointsProperties, ExportProperties,
                         JsonExportProperties, MetricsProperties,
                         NamingProperties, PerformanceProperties,
                         PrometheusExportProperties, SecurityProperties,
                         WebProperties)

# 导出主要组件
__all__ = [
    # 核心组件
    'ActuatorStarterAutoConfiguration',
    'ActuatorProperties',

    # 配置属性类
    'MetricsProperties',
    'CoreModulesProperties',
    'CustomMetricsProperties',
    'NamingProperties',
    'ExportProperties',
    'PrometheusExportProperties',
    'JsonExportProperties',
    'PerformanceProperties',
    'WebProperties',
    'EndpointsProperties',
    'SecurityProperties',

    # 工具类
    'ActuatorConfigUtils',

    # 端点类
    'BeansEndpoint',
    'HealthEndpoint',
    'InfoEndpoint',
    'MetricsEndpoint',
    'CustomEndpoint',
    'CustomEndpointBuilder',

    # 健康检查
    'HealthIndicator',
    'HealthStatus',

    # 信息贡献者
    'InfoContributor',

    # 指标系统
    'MetricsRegistry',
    'MetricsCollector',
    'MetricsConfig',
    'Counter',
    'Gauge',
    'Timer',
    'BaseMetrics',
    'TaskMetrics',

    # 端点处理器
    'CustomEndpointHandler',
    'CustomEndpointRegistry',
    'ConfigEndpointHandler',
    'FunctionEndpointHandler',
    'LambdaEndpointHandler',
    'StatusEndpointHandler',
    'VersionEndpointHandler',

    # 装饰器和工具函数
    'endpoint',
    'read',
    'write',
    'delete',
    'create_beans_endpoint',

    # 数据收集器
    'SystemMetricsCollector',
    'AppMetricsCollector',
    'MetricsAggregator',
    'AsyncHealthCollector',
    'AsyncHealthIndicator',
    'AsyncDiskSpaceHealthIndicator',
    'AsyncMemoryHealthIndicator',
    'AsyncDatabaseHealthIndicator',
    'AsyncCustomHealthIndicator',
    'HealthStatus',

    # 监控模块
    'MonitoringAlerts',
    'AlertRule',
    'AlertSeverity',
    'AlertStatus',
    'AlertChannel',
    'AlertNotifier',
    'AlertStats',
    'Alert',
    'HealthMonitor',
    'HealthChangeEvent',
    'HealthSnapshot',

    # 缓存模块
    'AdvancedEndpointCache',
    'CacheEndpoint',
    'CacheManager',
    'CacheStrategy',
    'CacheableEndpoint',
    'adaptive_cache',
    'advanced_cache',
    'cache_evict',
    'cache_put',
    'conditional_cache',
    'create_advanced_cache',
    'fifo_cache',
    'get_cache_manager',
    'get_endpoint_cache',
    'lfu_cache',
    'lru_cache',
    'ttl_cache',

    # 性能模块
    'ConcurrencyMetrics',
    'ErrorMetrics',
    'MetricType',
    'MetricValue',
    'PerformanceAnalyzer',
    'PerformanceCollector',
    'PerformanceContext',
    'PerformanceMetrics',
    'PerformanceMetricsRegistry',
    'PerformanceMonitor',
    'ResponseTimeMetrics',
    'ThroughputMetrics',
    'conditional',
    'count',
    'get_metrics',
    'get_metrics_registry',
    'monitor',
    'perf',
    'time',

    # 安全模块
    'ActuatorSecurity',
    'ProductionSecurityManager',
    'SecurityExceptionHandler',
    'SecurityIntegration',
    'SecurityLevel',
    'SecurityMiddleware',
    'SecurityPolicy',
    'SecurityToken',
]

# 安全模块 (延迟导入避免循环依赖)
from .security import (ActuatorSecurity, ProductionSecurityManager,
                       SecurityExceptionHandler, SecurityIntegration,
                       SecurityLevel, SecurityMiddleware, SecurityPolicy,
                       SecurityToken)

# 版本信息
__version__ = '1.0.0'
__author__ = 'Mini-Boot Team'
__description__ = 'Mini-Boot Actuator Starter - 应用监控和管理功能'
