#!/usr/bin/env python3
"""
Mini-Boot 版本管理工具

用于管理项目版本号,支持语义化版本控制
"""

import argparse
import re
import sys
from pathlib import Path


class VersionManager:
    """版本管理器"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.pyproject_path = project_root / "pyproject.toml"
        self.init_path = project_root / "miniboot" / "__init__.py"

    def get_current_version(self) -> str:
        """从 pyproject.toml 获取当前版本号"""
        try:
            with self.pyproject_path.open(encoding="utf-8") as f:
                content = f.read()

            # 查找版本号
            match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)
            if match:
                return match.group(1)
            raise ValueError("未找到版本号")

        except (FileNotFoundError, OSError, ValueError) as e:
            print(f"❌ 读取版本号失败: {e}")
            sys.exit(1)

    def parse_version(self, version: str) -> tuple[int, int, int]:
        """解析版本号为 (major, minor, patch)"""
        match = re.match(r"^(\d+)\.(\d+)\.(\d+)$", version)
        if not match:
            raise ValueError(f"无效的版本号格式: {version}")
        return int(match.group(1)), int(match.group(2)), int(match.group(3))

    def bump_version(self, version_type: str) -> str:
        """递增版本号"""
        current = self.get_current_version()
        major, minor, patch = self.parse_version(current)

        if version_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif version_type == "minor":
            minor += 1
            patch = 0
        elif version_type == "patch":
            patch += 1
        else:
            raise ValueError(f"无效的版本类型: {version_type}")

        new_version = f"{major}.{minor}.{patch}"
        print(f"版本号从 {current} 更新到 {new_version}")
        return new_version

    def update_pyproject_toml(self, new_version: str) -> None:
        """更新 pyproject.toml 中的版本号"""
        try:
            with self.pyproject_path.open(encoding="utf-8") as f:
                content = f.read()

            # 替换版本号
            new_content = re.sub(r'version\s*=\s*["\'][^"\']+["\']', f'version = "{new_version}"', content)

            with self.pyproject_path.open("w", encoding="utf-8") as f:
                f.write(new_content)

            print("✅ 已更新 pyproject.toml")

        except (FileNotFoundError, OSError) as e:
            print(f"❌ 更新 pyproject.toml 失败: {e}")
            sys.exit(1)

    def update_init_py(self, new_version: str) -> None:
        """更新 miniboot/__init__.py 中的版本号"""
        try:
            if self.init_path.exists():
                with self.init_path.open(encoding="utf-8") as f:
                    content = f.read()

                # 替换或添加版本号
                if "__version__" in content:
                    new_content = re.sub(
                        r'__version__\s*=\s*["\'][^"\']+["\']',
                        f'__version__ = "{new_version}"',
                        content,
                    )
                else:
                    # 如果没有版本号,添加到文件末尾
                    new_content = content.rstrip() + f'\n\n__version__ = "{new_version}"\n'

                with self.init_path.open("w", encoding="utf-8") as f:
                    f.write(new_content)

                print("✅ 已更新 miniboot/__init__.py")
            else:
                print("⚠️  miniboot/__init__.py 不存在,跳过更新")

        except (FileNotFoundError, OSError) as e:
            print(f"❌ 更新 miniboot/__init__.py 失败: {e}")
            sys.exit(1)

    def update_version(self, version_type: str) -> str:
        """更新所有文件中的版本号"""
        new_version = self.bump_version(version_type)

        self.update_pyproject_toml(new_version)
        self.update_init_py(new_version)

        print(f"🎉 版本号已更新为 {new_version}")
        return new_version

    def set_version(self, version: str) -> None:
        """设置指定的版本号"""
        # 验证版本号格式
        self.parse_version(version)

        self.update_pyproject_toml(version)
        self.update_init_py(version)

        print(f"🎉 版本号已设置为 {version}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Mini-Boot 版本管理工具")
    parser.add_argument("--show", action="store_true", help="显示当前版本号")
    parser.add_argument("--bump", choices=["major", "minor", "patch"], help="递增版本号")
    parser.add_argument("--set", help="设置指定版本号")

    args = parser.parse_args()

    project_root = Path(__file__).parent.parent
    version_manager = VersionManager(project_root)

    try:
        if args.show:
            current = version_manager.get_current_version()
            print(f"当前版本: {current}")
        elif args.bump:
            version_manager.update_version(args.bump)
        elif args.set:
            version_manager.set_version(args.set)
        else:
            parser.print_help()

    except (ValueError, OSError) as e:
        print(f"❌ 操作失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
