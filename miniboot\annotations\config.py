#!/usr/bin/env python
"""
* @author: cz
* @description: 配置相关注解实现

实现Mini-Boot框架的配置注解,包括@Value、@ConfigurationProperties、@ComponentScan等.
这些注解用于配置值注入、属性绑定和组件扫描等功能.
"""

import inspect
from typing import Any, Callable, Optional, Union

from .metadata import ComponentScanMetadata, ConfigurationPropertiesMetadata, EnableAutoConfigurationMetadata, ImportMetadata, ValueMetadata


def Value(  # noqa: N802
    value: str, *, required: bool = True, default_value: Optional[Any] = None
) -> Callable[[Union[Callable, type]], Union[Callable, type]]:
    """配置值注入注解装饰器

    用于注入单个配置值,支持占位符和默认值.
    支持字段注入和setter方法注入.

    Args:
        value: 配置键或表达式,支持占位符格式 ${key:default}
        required: 是否必需,默认为True
        default_value: 默认值,当配置不存在且required=False时使用

    Returns:
        装饰器函数

    Examples:
        # 简单配置值注入
        class AppConfig:
            @Value("${app.name}")
            app_name: str

        # 带默认值的配置注入
        class DatabaseConfig:
            @Value("${database.port:3306}")
            port: int

        # setter方法注入
        class ServiceConfig:
            @Value("${service.timeout:30}")
            def set_timeout(self, timeout: int):
                self.timeout = timeout

        # 非必需配置
        class OptionalConfig:
            @Value("${optional.feature}", required=False, default_value=False)
            feature_enabled: bool
    """

    def decorator(target: Union[Callable, type]) -> Union[Callable, type]:
        # 创建配置值元数据
        metadata = ValueMetadata(value=value, required=required, default_value=default_value)

        # 判断装饰目标类型
        if inspect.isfunction(target) or inspect.ismethod(target):
            # 方法装饰
            target.__value_metadata__ = metadata
            target.__is_value_injection__ = True
            target.__value_expression__ = value
            target.__value_required__ = required
            target.__value_default__ = default_value

            # 标记为setter方法注入
            target.__is_setter_value_injection__ = True

        else:
            # 字段装饰
            target.__value_metadata__ = metadata
            target.__is_value_injection__ = True
            target.__value_expression__ = value
            target.__value_required__ = required
            target.__value_default__ = default_value

            # 标记为字段注入
            target.__is_field_value_injection__ = True

        return target

    return decorator


def ConfigurationProperties(  # noqa: N802
    prefix: str = "", *, ignore_unknown_fields: bool = True, ignore_invalid_fields: bool = False, validate: bool = True
) -> Callable[[type], type]:
    """配置属性批量绑定注解装饰器

    用于将配置文件中的属性批量绑定到类的字段上.
    支持嵌套对象绑定和类型转换.

    Args:
        prefix: 配置前缀,如 "database" 对应 database.* 配置项
        ignore_unknown_fields: 是否忽略未知字段,默认为True
        ignore_invalid_fields: 是否忽略无效字段,默认为False
        validate: 是否进行配置验证,默认为True

    Returns:
        装饰器函数

    Examples:
        # 数据库配置绑定
        @ConfigurationProperties(prefix="database")
        class DatabaseConfig:
            host: str = "localhost"
            port: int = 3306
            username: str = ""
            password: str = ""

        # 嵌套配置绑定
        @ConfigurationProperties(prefix="app")
        class AppConfig:
            name: str = "mini-boot-app"
            version: str = "1.0.0"
            server: ServerConfig = None

        # 忽略未知字段
        @ConfigurationProperties(prefix="service", ignore_unknown_fields=False)
        class ServiceConfig:
            timeout: int = 30
            retries: int = 3
    """

    def decorator(cls: type) -> type:
        # 创建配置属性元数据
        metadata = ConfigurationPropertiesMetadata(
            prefix=prefix, ignore_unknown_fields=ignore_unknown_fields, ignore_invalid_fields=ignore_invalid_fields, validate=validate
        )

        # 存储元数据
        cls.__configuration_properties_metadata__ = metadata
        cls.__is_configuration_properties__ = True
        cls.__config_prefix__ = prefix
        cls.__config_ignore_unknown__ = ignore_unknown_fields
        cls.__config_ignore_invalid__ = ignore_invalid_fields
        cls.__config_validate__ = validate

        return cls

    return decorator


def ComponentScan(  # noqa: N802
    base_packages: Optional[Union[str, list[str]]] = None,
    *,
    include_filters: Optional[list[str]] = None,
    exclude_filters: Optional[list[str]] = None,
    lazy_init: bool = False,
) -> Callable[[type], type]:
    """组件扫描注解装饰器

    声明组件扫描的包路径和过滤规则.
    通常与@Configuration或@MiniBootApplication一起使用.

    Args:
        base_packages: 基础扫描包路径,可以是字符串或字符串列表
        include_filters: 包含过滤器列表,支持通配符
        exclude_filters: 排除过滤器列表,支持通配符
        lazy_init: 是否延迟初始化扫描到的组件

    Returns:
        装饰器函数

    Examples:
        # 扫描指定包
        @Configuration
        @ComponentScan("com.example.service")
        class AppConfig:
            pass

        # 扫描多个包
        @Configuration
        @ComponentScan(["com.example.service", "com.example.repository"])
        class AppConfig:
            pass

        # 带过滤器的扫描
        @Configuration
        @ComponentScan(
            base_packages="com.example",
            include_filters=["*Service", "*Repository"],
            exclude_filters=["*Test*"]
        )
        class AppConfig:
            pass
    """

    def decorator(cls: type) -> type:
        # 处理base_packages参数
        if base_packages is None:
            # 如果没有指定,使用当前类所在的包
            module_name = cls.__module__
            packages = [module_name.rsplit(".", 1)[0]] if "." in module_name else [module_name]
        elif isinstance(base_packages, str):
            packages = [base_packages]
        else:
            packages = list(base_packages)

        # 创建组件扫描元数据
        metadata = ComponentScanMetadata(
            base_packages=packages, include_filters=include_filters or [], exclude_filters=exclude_filters or [], lazy_init=lazy_init
        )

        # 存储元数据
        cls.__component_scan_metadata__ = metadata
        cls.__is_component_scan__ = True
        cls.__scan_packages__ = packages
        cls.__scan_include_filters__ = include_filters or []
        cls.__scan_exclude_filters__ = exclude_filters or []
        cls.__scan_lazy_init__ = lazy_init

        return cls

    return decorator


def EnableAutoConfiguration(  # noqa: N802
    cls: Optional[type] = None, *, exclude: Optional[list[str]] = None
) -> Union[Callable[[type], type], type]:
    """启用自动配置注解装饰器

    启用Mini-Boot的自动配置机制,自动配置常用的Bean和服务.
    通常与@MiniBootApplication一起使用.

    Args:
        exclude: 排除的自动配置类列表

    Returns:
        装饰器函数

    Examples:
        # 启用所有自动配置
        @Configuration
        @EnableAutoConfiguration
        class AppConfig:
            pass

        # 排除特定自动配置
        @Configuration
        @EnableAutoConfiguration(exclude=["DatabaseAutoConfiguration"])
        class AppConfig:
            pass
    """

    def decorator(cls: type) -> type:
        # 创建自动配置元数据
        metadata = EnableAutoConfigurationMetadata(exclude=exclude or [])

        # 存储元数据
        cls.__enable_auto_configuration_metadata__ = metadata
        cls.__is_enable_auto_configuration__ = True
        cls.__auto_config_exclude__ = exclude or []

        return cls

    # 如果直接传入类,不带参数(@EnableAutoConfiguration)
    if cls is not None:
        return decorator(cls)

    # 带参数调用(@EnableAutoConfiguration(...))
    return decorator


def Import(  # noqa: N802
    *import_classes: type,
) -> Callable[[type], type]:
    """导入配置类注解装饰器

    导入其他配置类,将它们的Bean定义合并到当前配置中.

    Args:
        *import_classes: 要导入的配置类列表

    Returns:
        装饰器函数

    Examples:
        # 导入单个配置类
        @Configuration
        @Import(DatabaseConfig)
        class AppConfig:
            pass

        # 导入多个配置类
        @Configuration
        @Import(DatabaseConfig, CacheConfig, SecurityConfig)
        class AppConfig:
            pass
    """

    def decorator(cls: type) -> type:
        # 创建导入元数据
        metadata = ImportMetadata(import_classes=list(import_classes))

        # 存储元数据
        cls.__import_metadata__ = metadata
        cls.__is_import__ = True
        cls.__import_classes__ = list(import_classes)

        return cls

    return decorator


# 便捷函数:检查对象是否有特定注解
def is_value_injection(obj: Any) -> bool:
    """检查对象是否有@Value注解"""
    return hasattr(obj, "__is_value_injection__") and obj.__is_value_injection__


def is_configuration_properties(obj: Any) -> bool:
    """检查对象是否有@ConfigurationProperties注解"""
    return hasattr(obj, "__is_configuration_properties__") and obj.__is_configuration_properties__


def is_component_scan(obj: Any) -> bool:
    """检查对象是否有@ComponentScan注解"""
    return hasattr(obj, "__is_component_scan__") and obj.__is_component_scan__


def is_enable_auto_configuration(obj: Any) -> bool:
    """检查对象是否有@EnableAutoConfiguration注解"""
    return hasattr(obj, "__is_enable_auto_configuration__") and obj.__is_enable_auto_configuration__


def is_import(obj: Any) -> bool:
    """检查对象是否有@Import注解"""
    return hasattr(obj, "__is_import__") and obj.__is_import__


def get_value_metadata(obj: Any) -> Optional[ValueMetadata]:
    """获取对象的@Value元数据"""
    return getattr(obj, "__value_metadata__", None)


def get_configuration_properties_metadata(obj: Any) -> Optional[ConfigurationPropertiesMetadata]:
    """获取对象的@ConfigurationProperties元数据"""
    return getattr(obj, "__configuration_properties_metadata__", None)


def get_component_scan_metadata(obj: Any) -> Optional[ComponentScanMetadata]:
    """获取对象的@ComponentScan元数据"""
    return getattr(obj, "__component_scan_metadata__", None)


def get_value_expression(obj: Any) -> Optional[str]:
    """获取对象的@Value表达式"""
    return getattr(obj, "__value_expression__", None)


def get_config_prefix(obj: Any) -> Optional[str]:
    """获取对象的配置前缀"""
    return getattr(obj, "__config_prefix__", None)


def get_scan_packages(obj: Any) -> list[str]:
    """获取对象的扫描包列表"""
    return getattr(obj, "__scan_packages__", [])


def get_import_classes(obj: Any) -> list[type]:
    """获取对象的导入类列表"""
    return getattr(obj, "__import_classes__", [])
