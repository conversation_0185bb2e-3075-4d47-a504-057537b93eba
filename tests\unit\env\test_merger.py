#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置合并器测试
"""

import unittest
from unittest.mock import patch

from miniboot.env.merger import ConfigurationMerger, get_configuration_merger, merge_configurations, merge_property_sources
from miniboot.env.priority import ConfigurationMergeStrategy


class ConfigurationMergerTestCase(unittest.TestCase):
    """配置合并器测试"""

    def setUp(self):
        """设置测试环境"""
        self.merger = ConfigurationMerger()

    def test_merge_configurations_override(self):
        """测试覆盖策略合并"""
        high_priority = {"miniboot": {"application": {"name": "test-app"}}}

        low_priority = {"miniboot": {"application": {"name": "default-app", "version": "1.0.0"}}}

        result = self.merger.merge_configurations(high_priority, low_priority)

        # 高优先级的name应该覆盖低优先级的
        self.assertEqual(result["miniboot"]["application"]["name"], "test-app")
        # 低优先级的version应该保留
        self.assertEqual(result["miniboot"]["application"]["version"], "1.0.0")

    def test_merge_configurations_empty(self):
        """测试空配置合并"""
        high_priority = {"key": "value"}
        low_priority = {}

        result = self.merger.merge_configurations(high_priority, low_priority)
        self.assertEqual(result, {"key": "value"})

        result = self.merger.merge_configurations({}, low_priority)
        self.assertEqual(result, {})

        result = self.merger.merge_configurations(None, {"key": "value"})
        self.assertEqual(result, {"key": "value"})

    def test_merge_with_append_strategy_direct(self):
        """测试追加策略合并（直接测试）"""
        # 直接测试追加功能
        target = ["http://localhost:3000"]
        source = ["http://localhost:8080", "http://localhost:9000"]

        result = self.merger._append_values(target, source)

        # 应该包含所有origins
        self.assertIn("http://localhost:3000", result)
        self.assertIn("http://localhost:8080", result)
        self.assertIn("http://localhost:9000", result)
        # 验证总数
        self.assertEqual(len(result), 3)

    @patch("miniboot.env.merger.get_priority_manager")
    def test_merge_with_merge_strategy(self, mock_get_manager):
        """测试合并策略"""
        # 模拟优先级管理器返回合并策略
        mock_manager = mock_get_manager.return_value

        def mock_get_merge_strategy(key):
            # 对于database相关的键使用合并策略
            if key.startswith("database"):
                return ConfigurationMergeStrategy.MERGE
            return ConfigurationMergeStrategy.OVERRIDE

        mock_manager.get_merge_strategy.side_effect = mock_get_merge_strategy

        high_priority = {"database": {"host": "prod-host", "pool": {"max-size": 20}}}

        low_priority = {"database": {"host": "dev-host", "port": 5432, "pool": {"max-size": 10, "min-size": 5}}}

        result = self.merger.merge_configurations(high_priority, low_priority)

        # host应该被覆盖（高优先级）
        self.assertEqual(result["database"]["host"], "prod-host")
        # port应该保留（低优先级中存在，高优先级中不存在）
        self.assertEqual(result["database"]["port"], 5432)
        # pool.max-size应该被覆盖（高优先级）
        self.assertEqual(result["database"]["pool"]["max-size"], 20)
        # pool.min-size应该保留（低优先级中存在，高优先级中不存在）
        self.assertEqual(result["database"]["pool"]["min-size"], 5)

    def test_append_values(self):
        """测试追加值功能"""
        # 列表追加列表
        result = self.merger._append_values(["a", "b"], ["c", "d"])
        self.assertEqual(result, ["a", "b", "c", "d"])

        # 列表追加单值
        result = self.merger._append_values(["a", "b"], "c")
        self.assertEqual(result, ["a", "b", "c"])

        # 单值追加列表
        result = self.merger._append_values("a", ["b", "c"])
        self.assertEqual(result, ["a", "b", "c"])

        # 防重复
        result = self.merger._append_values(["a", "b"], ["b", "c"])
        self.assertEqual(result, ["a", "b", "c"])

    def test_prepend_values(self):
        """测试前置插入值功能"""
        # 列表前置列表
        result = self.merger._prepend_values(["c", "d"], ["a", "b"])
        self.assertEqual(result, ["a", "b", "c", "d"])

        # 列表前置单值
        result = self.merger._prepend_values(["b", "c"], "a")
        self.assertEqual(result, ["a", "b", "c"])

        # 单值前置列表
        result = self.merger._prepend_values("c", ["a", "b"])
        self.assertEqual(result, ["a", "b", "c"])

    def test_merge_property_sources(self):
        """测试合并多个属性源"""
        sources = [
            # 高优先级
            {"miniboot": {"application": {"name": "prod-app"}}},
            # 中优先级
            {"miniboot": {"application": {"name": "dev-app", "version": "1.0.0"}, "server": {"port": 8080}}},
            # 低优先级
            {
                "miniboot": {
                    "application": {"name": "default-app", "version": "0.1.0", "description": "Default application"},
                    "server": {"port": 8000, "host": "localhost"},
                }
            },
        ]

        result = self.merger.merge_property_sources(sources)

        # 验证合并结果
        self.assertEqual(result["miniboot"]["application"]["name"], "prod-app")
        self.assertEqual(result["miniboot"]["application"]["version"], "1.0.0")
        self.assertEqual(result["miniboot"]["application"]["description"], "Default application")
        self.assertEqual(result["miniboot"]["server"]["port"], 8080)
        self.assertEqual(result["miniboot"]["server"]["host"], "localhost")

    def test_validate_merge_result(self):
        """测试合并结果验证"""
        # 有效配置
        valid_config = {"miniboot": {"application": {"name": "test-app"}}}
        self.assertTrue(self.merger.validate_merge_result(valid_config))

        # 无效配置 - 不是字典
        self.assertFalse(self.merger.validate_merge_result("invalid"))

        # 无效配置 - 缺少必需键
        invalid_config = {"other": {"key": "value"}}
        self.assertFalse(self.merger.validate_merge_result(invalid_config))

    def test_deep_copy_behavior(self):
        """测试深拷贝行为"""
        original = {"nested": {"list": [1, 2, 3], "dict": {"key": "value"}}}

        result = self.merger.merge_configurations(original, {})

        # 修改原始对象不应影响结果
        original["nested"]["list"].append(4)
        original["nested"]["dict"]["key"] = "modified"

        self.assertEqual(result["nested"]["list"], [1, 2, 3])
        self.assertEqual(result["nested"]["dict"]["key"], "value")


class GlobalMergerTestCase(unittest.TestCase):
    """全局合并器测试"""

    def test_get_configuration_merger(self):
        """测试获取全局配置合并器"""
        merger1 = get_configuration_merger()
        merger2 = get_configuration_merger()

        # 应该返回同一个实例
        self.assertIs(merger1, merger2)
        self.assertIsInstance(merger1, ConfigurationMerger)

    def test_convenience_functions(self):
        """测试便捷函数"""
        high_priority = {"key1": "value1"}
        low_priority = {"key2": "value2"}

        # 测试merge_configurations函数
        result = merge_configurations(high_priority, low_priority)
        self.assertEqual(result["key1"], "value1")
        self.assertEqual(result["key2"], "value2")

        # 测试merge_property_sources函数
        sources = [high_priority, low_priority]
        result = merge_property_sources(sources)
        self.assertEqual(result["key1"], "value1")
        self.assertEqual(result["key2"], "value2")


if __name__ == "__main__":
    unittest.main()
