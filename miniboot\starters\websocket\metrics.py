#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 指标收集器
"""

from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime
from threading import Lock
from typing import Any, Optional

from miniboot.annotations import Component


@dataclass
class ConnectionMetrics:
    """连接指标数据类"""

    total_connections: int = 0
    active_connections: int = 0
    failed_connections: int = 0
    peak_connections: int = 0
    connection_attempts: int = 0
    successful_connections: int = 0
    disconnections: int = 0
    connection_durations: list[float] = field(default_factory=list)

    @property
    def success_rate(self) -> float:
        """连接成功率"""
        if self.connection_attempts == 0:
            return 0.0
        return self.successful_connections / self.connection_attempts

    @property
    def average_duration(self) -> float:
        """平均连接持续时间(秒)"""
        if not self.connection_durations:
            return 0.0
        return sum(self.connection_durations) / len(self.connection_durations)


@dataclass
class MessageMetrics:
    """消息指标数据类"""

    total_messages_sent: int = 0
    total_messages_received: int = 0
    total_bytes_sent: int = 0
    total_bytes_received: int = 0
    messages_by_type: dict[str, int] = field(default_factory=lambda: defaultdict(int))
    failed_messages: int = 0
    message_processing_times: list[float] = field(default_factory=list)

    @property
    def message_failure_rate(self) -> float:
        """消息失败率"""
        total_messages = self.total_messages_sent + self.total_messages_received
        if total_messages == 0:
            return 0.0
        return self.failed_messages / total_messages

    @property
    def average_processing_time(self) -> float:
        """平均消息处理时间(毫秒)"""
        if not self.message_processing_times:
            return 0.0
        return sum(self.message_processing_times) / len(self.message_processing_times)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""

    cpu_usage_samples: deque = field(default_factory=lambda: deque(maxlen=100))
    memory_usage_samples: deque = field(default_factory=lambda: deque(maxlen=100))
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    error_counts: dict[str, int] = field(default_factory=lambda: defaultdict(int))

    @property
    def average_response_time(self) -> float:
        """平均响应时间(毫秒)"""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)

    @property
    def p95_response_time(self) -> float:
        """95分位响应时间(毫秒)"""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.95)
        return sorted_times[index] if index < len(sorted_times) else sorted_times[-1]


@Component
class WebSocketMetrics:
    """WebSocket 指标收集器

    负责收集和统计 WebSocket 连接、消息传输、性能等各种指标数据.
    提供实时监控和历史数据分析功能.
    """

    def __init__(self):
        """初始化指标收集器"""
        self._lock = Lock()
        self._start_time = datetime.now()

        # 核心指标
        self.connection_metrics = ConnectionMetrics()
        self.message_metrics = MessageMetrics()
        self.performance_metrics = PerformanceMetrics()

        # 用户会话指标
        self._user_sessions: dict[str, set[str]] = defaultdict(set)  # user_id -> session_ids
        self._session_start_times: dict[str, datetime] = {}
        self._session_message_counts: dict[str, int] = defaultdict(int)

        # 时间窗口指标(最近1小时)
        self._hourly_metrics = deque(maxlen=60)  # 每分钟一个数据点
        self._last_hourly_update = datetime.now()

        # 错误统计
        self._error_history: deque = deque(maxlen=1000)

    def record_connection_attempt(self, session_id: str, user_id: Optional[str] = None) -> None:
        """记录连接尝试

        Args:
            session_id: 会话 ID
            user_id: 用户 ID(可选)
        """
        with self._lock:
            self.connection_metrics.connection_attempts += 1
            self._session_start_times[session_id] = datetime.now()

            if user_id:
                self._user_sessions[user_id].add(session_id)

    def record_connection_success(self, session_id: str) -> None:
        """记录连接成功

        Args:
            session_id: 会话 ID
        """
        with self._lock:
            self.connection_metrics.successful_connections += 1
            self.connection_metrics.active_connections += 1
            self.connection_metrics.total_connections += 1

            # 更新峰值连接数
            if self.connection_metrics.active_connections > self.connection_metrics.peak_connections:
                self.connection_metrics.peak_connections = self.connection_metrics.active_connections

    def record_connection_failure(self, session_id: str, error: Exception) -> None:
        """记录连接失败

        Args:
            session_id: 会话 ID
            error: 失败原因
        """
        with self._lock:
            self.connection_metrics.failed_connections += 1

            # 记录错误
            self._record_error("connection_failure", str(error))

            # 清理会话记录
            self._cleanup_session(session_id)

    def record_disconnection(self, session_id: str, user_id: Optional[str] = None) -> None:
        """记录连接断开

        Args:
            session_id: 会话 ID
            user_id: 用户 ID(可选)
        """
        with self._lock:
            self.connection_metrics.active_connections -= 1
            self.connection_metrics.disconnections += 1

            # 计算连接持续时间
            if session_id in self._session_start_times:
                start_time = self._session_start_times[session_id]
                duration = (datetime.now() - start_time).total_seconds()
                self.connection_metrics.connection_durations.append(duration)

                # 限制历史数据大小
                if len(self.connection_metrics.connection_durations) > 1000:
                    self.connection_metrics.connection_durations = self.connection_metrics.connection_durations[-500:]

            # 清理用户会话记录
            if user_id and user_id in self._user_sessions:
                self._user_sessions[user_id].discard(session_id)
                if not self._user_sessions[user_id]:
                    del self._user_sessions[user_id]

            # 清理会话记录
            self._cleanup_session(session_id)

    def record_message_sent(self, session_id: str, message_type: str, size_bytes: int, processing_time_ms: float = 0.0) -> None:
        """记录发送消息

        Args:
            session_id: 会话 ID
            message_type: 消息类型
            size_bytes: 消息大小(字节)
            processing_time_ms: 处理时间(毫秒)
        """
        with self._lock:
            self.message_metrics.total_messages_sent += 1
            self.message_metrics.total_bytes_sent += size_bytes
            self.message_metrics.messages_by_type[message_type] += 1

            if processing_time_ms > 0:
                self.message_metrics.message_processing_times.append(processing_time_ms)

                # 限制历史数据大小
                if len(self.message_metrics.message_processing_times) > 1000:
                    self.message_metrics.message_processing_times = self.message_metrics.message_processing_times[-500:]

            # 更新会话消息计数
            self._session_message_counts[session_id] += 1

    def record_message_received(self, session_id: str, message_type: str, size_bytes: int, processing_time_ms: float = 0.0) -> None:
        """记录接收消息

        Args:
            session_id: 会话 ID
            message_type: 消息类型
            size_bytes: 消息大小(字节)
            processing_time_ms: 处理时间(毫秒)
        """
        with self._lock:
            self.message_metrics.total_messages_received += 1
            self.message_metrics.total_bytes_received += size_bytes
            self.message_metrics.messages_by_type[f"received_{message_type}"] += 1

            if processing_time_ms > 0:
                self.message_metrics.message_processing_times.append(processing_time_ms)

                # 限制历史数据大小
                if len(self.message_metrics.message_processing_times) > 1000:
                    self.message_metrics.message_processing_times = self.message_metrics.message_processing_times[-500:]

    def record_message_failure(self, session_id: str, message_type: str, error: Exception) -> None:
        """记录消息失败

        Args:
            session_id: 会话 ID
            message_type: 消息类型
            error: 失败原因
        """
        with self._lock:
            self.message_metrics.failed_messages += 1
            self._record_error(f"message_failure_{message_type}", str(error))

    def record_response_time(self, response_time_ms: float) -> None:
        """记录响应时间

        Args:
            response_time_ms: 响应时间(毫秒)
        """
        with self._lock:
            self.performance_metrics.response_times.append(response_time_ms)

    def record_error(self, error_type: str, error_message: str) -> None:
        """记录错误

        Args:
            error_type: 错误类型
            error_message: 错误消息
        """
        with self._lock:
            self._record_error(error_type, error_message)

    def _record_error(self, error_type: str, error_message: str) -> None:
        """内部错误记录方法

        Args:
            error_type: 错误类型
            error_message: 错误消息
        """
        self.performance_metrics.error_counts[error_type] += 1
        self._error_history.append({"type": error_type, "message": error_message, "timestamp": datetime.now().isoformat()})

    def _cleanup_session(self, session_id: str) -> None:
        """清理会话相关数据

        Args:
            session_id: 会话 ID
        """
        self._session_start_times.pop(session_id, None)
        self._session_message_counts.pop(session_id, None)

    def get_connection_metrics(self) -> dict[str, Any]:
        """获取连接指标

        Returns:
            Dict[str, Any]: 连接指标数据
        """
        with self._lock:
            return {
                "total_connections": self.connection_metrics.total_connections,
                "active_connections": self.connection_metrics.active_connections,
                "failed_connections": self.connection_metrics.failed_connections,
                "peak_connections": self.connection_metrics.peak_connections,
                "connection_attempts": self.connection_metrics.connection_attempts,
                "successful_connections": self.connection_metrics.successful_connections,
                "disconnections": self.connection_metrics.disconnections,
                "success_rate": round(self.connection_metrics.success_rate, 4),
                "average_duration_seconds": round(self.connection_metrics.average_duration, 2),
                "unique_users": len(self._user_sessions),
            }

    def get_message_metrics(self) -> dict[str, Any]:
        """获取消息指标

        Returns:
            Dict[str, Any]: 消息指标数据
        """
        with self._lock:
            return {
                "total_messages_sent": self.message_metrics.total_messages_sent,
                "total_messages_received": self.message_metrics.total_messages_received,
                "total_bytes_sent": self.message_metrics.total_bytes_sent,
                "total_bytes_received": self.message_metrics.total_bytes_received,
                "messages_by_type": dict(self.message_metrics.messages_by_type),
                "failed_messages": self.message_metrics.failed_messages,
                "message_failure_rate": round(self.message_metrics.message_failure_rate, 4),
                "average_processing_time_ms": round(self.message_metrics.average_processing_time, 2),
            }

    def get_performance_metrics(self) -> dict[str, Any]:
        """获取性能指标

        Returns:
            Dict[str, Any]: 性能指标数据
        """
        with self._lock:
            return {
                "average_response_time_ms": round(self.performance_metrics.average_response_time, 2),
                "p95_response_time_ms": round(self.performance_metrics.p95_response_time, 2),
                "error_counts": dict(self.performance_metrics.error_counts),
                "total_errors": sum(self.performance_metrics.error_counts.values()),
            }

    def get_all_metrics(self) -> dict[str, Any]:
        """获取所有指标数据

        Returns:
            Dict[str, Any]: 完整的指标数据
        """
        uptime = datetime.now() - self._start_time

        return {
            "timestamp": datetime.now().isoformat(),
            "uptime_seconds": int(uptime.total_seconds()),
            "connection_metrics": self.get_connection_metrics(),
            "message_metrics": self.get_message_metrics(),
            "performance_metrics": self.get_performance_metrics(),
            "session_metrics": self._get_session_metrics(),
        }

    def _get_session_metrics(self) -> dict[str, Any]:
        """获取会话指标

        Returns:
            Dict[str, Any]: 会话指标数据
        """
        with self._lock:
            active_sessions = len(self._session_start_times)
            total_messages_per_session = 0

            if active_sessions > 0:
                total_messages_per_session = sum(self._session_message_counts.values()) / active_sessions

            return {
                "active_sessions": active_sessions,
                "average_messages_per_session": round(total_messages_per_session, 2),
                "sessions_by_user": {user_id: len(sessions) for user_id, sessions in self._user_sessions.items()},
            }

    def get_recent_errors(self, limit: int = 10) -> list[dict[str, Any]]:
        """获取最近的错误记录

        Args:
            limit: 返回的错误数量限制

        Returns:
            List[Dict[str, Any]]: 最近的错误记录
        """
        with self._lock:
            return list(self._error_history)[-limit:]

    def reset_metrics(self) -> None:
        """重置所有指标数据"""
        with self._lock:
            self._start_time = datetime.now()
            self.connection_metrics = ConnectionMetrics()
            self.message_metrics = MessageMetrics()
            self.performance_metrics = PerformanceMetrics()
            self._user_sessions.clear()
            self._session_start_times.clear()
            self._session_message_counts.clear()
            self._hourly_metrics.clear()
            self._error_history.clear()

    def export_metrics(self, format_type: str = "json") -> str:
        """导出指标数据

        Args:
            format_type: 导出格式(json, prometheus)

        Returns:
            str: 格式化的指标数据
        """
        metrics = self.get_all_metrics()

        if format_type.lower() == "json":
            import json

            return json.dumps(metrics, indent=2, ensure_ascii=False)

        elif format_type.lower() == "prometheus":
            return self._export_prometheus_format(metrics)

        else:
            raise ValueError(f"Unsupported format type: {format_type}")

    def _export_prometheus_format(self, metrics: dict[str, Any]) -> str:
        """导出 Prometheus 格式的指标

        Args:
            metrics: 指标数据

        Returns:
            str: Prometheus 格式的指标数据
        """
        lines = []

        # 连接指标
        conn = metrics["connection_metrics"]
        lines.extend(
            [
                "# HELP websocket_connections_total Total number of WebSocket connections",
                "# TYPE websocket_connections_total counter",
                f"websocket_connections_total {conn['total_connections']}",
                "",
                "# HELP websocket_connections_active Current active WebSocket connections",
                "# TYPE websocket_connections_active gauge",
                f"websocket_connections_active {conn['active_connections']}",
                "",
                "# HELP websocket_connection_success_rate Connection success rate",
                "# TYPE websocket_connection_success_rate gauge",
                f"websocket_connection_success_rate {conn['success_rate']}",
                "",
            ]
        )

        # 消息指标
        msg = metrics["message_metrics"]
        lines.extend(
            [
                "# HELP websocket_messages_sent_total Total messages sent",
                "# TYPE websocket_messages_sent_total counter",
                f"websocket_messages_sent_total {msg['total_messages_sent']}",
                "",
                "# HELP websocket_messages_received_total Total messages received",
                "# TYPE websocket_messages_received_total counter",
                f"websocket_messages_received_total {msg['total_messages_received']}",
                "",
                "# HELP websocket_bytes_sent_total Total bytes sent",
                "# TYPE websocket_bytes_sent_total counter",
                f"websocket_bytes_sent_total {msg['total_bytes_sent']}",
                "",
            ]
        )

        # 性能指标
        perf = metrics["performance_metrics"]
        lines.extend(
            [
                "# HELP websocket_response_time_ms Average response time in milliseconds",
                "# TYPE websocket_response_time_ms gauge",
                f"websocket_response_time_ms {perf['average_response_time_ms']}",
                "",
                "# HELP websocket_errors_total Total number of errors",
                "# TYPE websocket_errors_total counter",
                f"websocket_errors_total {perf['total_errors']}",
                "",
            ]
        )

        return "\n".join(lines)
