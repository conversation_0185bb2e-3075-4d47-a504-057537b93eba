# Mini-Boot 框架

Mini-Boot 是一个轻量级的 Python Web 框架，参考 Spring Boot 的设计理念。它为 Python 应用程序提供 IoC 容器、依赖注入、自动配置和企业级功能。

## 核心特性

-   **IoC 容器** - 依赖注入和组件管理
-   **注解驱动** - 基于注解的组件扫描和配置
-   **Web 集成** - FastAPI 集成和自动路由
-   **自动配置** - 智能的自动配置机制
-   **监控端点** - 健康检查和系统监控
-   **事件系统** - 发布订阅事件机制
-   **任务调度** - 定时任务和异步处理
-   **Starter 机制** - 模块化的功能扩展

## 目标使用场景

-   需要依赖注入的企业级 Web 应用
-   具有监控和健康检查需求的微服务
-   需要 Spring Boot 式开发体验的 Python 应用
-   需要模块化架构和自动配置的项目

## 核心设计原则

-   约定优于配置
-   基于注解的编程模型
-   模块化和可扩展的架构
-   企业级监控和可观测性
