"""
Mini-Boot Web Starters

Web 相关的 Starter 组件集合，提供模块化的 Web 功能扩展。

Available Starters:
- load_monitoring: 负载监控 Starter ✅
- task_scheduling: 任务调度 Starter ✅
- backpressure: 背压控制 Starter ✅
"""

__version__ = "1.0.0"
__author__ = "Mini-Boot Team"

# 导入 Backpressure Starter 组件
from .backpressure import BackpressureController, BackpressureProperties
from .backpressure.autoconfigure import BackpressureAutoConfiguration
# 导入 LoadMonitor Starter 组件
from .load_monitoring import LoadMonitor, LoadMonitorProperties
from .load_monitoring.autoconfigure import LoadMonitorAutoConfiguration
# 导入 TaskScheduler Starter 组件
from .task_scheduling import TaskScheduler, TaskSchedulerProperties
from .task_scheduling.autoconfigure import TaskSchedulerAutoConfiguration

# 导出主要组件
__all__ = [
    # LoadMonitor Starter
    "LoadMonitor",
    "LoadMonitorProperties",
    "LoadMonitorAutoConfiguration",
    # TaskScheduler Starter
    "TaskScheduler",
    "TaskSchedulerProperties",
    "TaskSchedulerAutoConfiguration",
    # Backpressure Starter
    "BackpressureController",
    "BackpressureProperties",
    "BackpressureAutoConfiguration",
]
