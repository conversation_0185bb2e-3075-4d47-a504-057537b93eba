#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: miniboot
Email: <EMAIL>
Description: Web autoconfigure simple unit tests - basic testing for Web module auto-configuration
"""

import unittest
from unittest.mock import MagicMock, patch

from miniboot.starters.actuator.autoconfigure.web import WebAutoConfiguration


class WebAutoConfigurationTestCase(unittest.TestCase):
    """Web auto-configuration unit test suite"""

    def setUp(self) -> None:
        """Set up test environment"""
        self.config = WebAutoConfiguration()

    def test_configuration_initialization(self) -> None:
        """Test auto-configuration initialization"""
        self.assertIsInstance(self.config, WebAutoConfiguration)

    def test_get_metadata(self) -> None:
        """Test getting configuration metadata"""
        metadata = self.config.get_metadata()

        self.assertEqual(metadata.name, "actuator-web-auto-configuration")
        self.assertIn("Actuator Web 集成自动配置", metadata.description)
        self.assertEqual(metadata.priority, 300)
        self.assertIn("actuator-starter-auto-configuration", metadata.auto_configure_after)




if __name__ == "__main__":
    unittest.main()
