"""
Backpressure Controller - 智能背压控制器

独立的背压控制 Starter 组件，提供智能负载管理和流量控制功能。
重构自原 miniboot.web.backpressure.controller，采用 @PostConstruct 生命周期管理。
"""

import asyncio
import time
from collections import deque
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
from typing import Any, Callable, Dict, List, Optional

from loguru import logger

from miniboot.annotations import Component, PostConstruct, PreDestroy
from .properties import BackpressureProperties


class ProtectionAction(Enum):
    """保护动作类型"""
    
    NONE = "none"
    THROTTLE = "throttle"  # 限流
    REJECT = "reject"  # 拒绝请求
    DEGRADE = "degrade"  # 降级服务
    CIRCUIT_BREAK = "circuit_break"  # 熔断


class CircuitBreakerState(Enum):
    """熔断器状态"""
    
    CLOSED = "closed"  # 关闭状态，正常处理请求
    OPEN = "open"  # 开启状态，拒绝所有请求
    HALF_OPEN = "half_open"  # 半开状态，允许少量请求测试


class SystemState(Enum):
    """系统状态"""
    
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    OVERLOADED = "overloaded"
    CRITICAL = "critical"


@dataclass
class ProtectionDecision:
    """保护决策"""
    
    action: ProtectionAction
    reason: str
    component: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)


@dataclass
class RequestMetrics:
    """请求指标"""
    
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rejected_requests: int = 0
    avg_response_time: float = 0.0
    error_rate: float = 0.0
    throughput: float = 0.0
    last_updated: float = field(default_factory=time.time)


@dataclass
class SystemMetrics:
    """系统指标"""
    
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    active_connections: int = 0
    queue_size: int = 0
    load_average: float = 0.0
    timestamp: float = field(default_factory=time.time)


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, properties: BackpressureProperties):
        self.properties = properties
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0.0
        self.last_success_time = 0.0
        self._lock = Lock()
    
    def can_execute(self) -> bool:
        """判断是否可以执行请求"""
        with self._lock:
            if self.state == CircuitBreakerState.CLOSED:
                return True
            elif self.state == CircuitBreakerState.OPEN:
                # 检查是否可以转为半开状态
                if time.time() - self.last_failure_time > self.properties.circuit_breaker_timeout:
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.success_count = 0
                    logger.info("Circuit breaker transitioning to HALF_OPEN")
                    return True
                return False
            else:  # HALF_OPEN
                return True
    
    def record_success(self) -> None:
        """记录成功请求"""
        with self._lock:
            self.last_success_time = time.time()
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.properties.success_threshold:
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
                    logger.info("Circuit breaker transitioning to CLOSED")
            elif self.state == CircuitBreakerState.CLOSED:
                self.failure_count = 0
    
    def record_failure(self) -> None:
        """记录失败请求"""
        with self._lock:
            self.last_failure_time = time.time()
            
            if self.state == CircuitBreakerState.CLOSED:
                self.failure_count += 1
                if self.failure_count >= self.properties.failure_threshold:
                    self.state = CircuitBreakerState.OPEN
                    logger.warning("Circuit breaker transitioning to OPEN")
            elif self.state == CircuitBreakerState.HALF_OPEN:
                self.state = CircuitBreakerState.OPEN
                self.failure_count = 0
                logger.warning("Circuit breaker transitioning back to OPEN")


class RateLimiter:
    """限流器实现（令牌桶算法）"""
    
    def __init__(self, properties: BackpressureProperties):
        self.properties = properties
        self.tokens = float(properties.burst_capacity)
        self.last_refill = time.time()
        self._lock = Lock()
    
    def can_proceed(self, tokens_requested: int = 1) -> bool:
        """判断是否可以继续处理请求"""
        if not self.properties.rate_limiting_enabled:
            return True
            
        with self._lock:
            now = time.time()
            
            # 计算需要添加的令牌数
            time_passed = now - self.last_refill
            tokens_to_add = time_passed * self.properties.max_requests_per_second
            
            # 更新令牌数量
            self.tokens = min(
                self.properties.burst_capacity,
                self.tokens + tokens_to_add
            )
            self.last_refill = now
            
            # 检查是否有足够的令牌
            if self.tokens >= tokens_requested:
                self.tokens -= tokens_requested
                return True
            
            return False
    
    def get_current_rate(self) -> float:
        """获取当前限流率"""
        with self._lock:
            return self.properties.max_requests_per_second * (self.tokens / self.properties.burst_capacity)


@Component
class BackpressureController:
    """智能背压控制器 - 独立 Starter 版本
    
    提供智能负载管理和流量控制功能，支持：
    - 智能背压检测：基于系统负载和响应时间的背压检测
    - 流量控制：自适应限流和熔断机制
    - 负载均衡：请求分发和负载平衡
    - 性能监控：实时性能指标收集和分析
    - 自动恢复：故障自动检测和恢复机制
    """
    
    def __init__(self, properties: BackpressureProperties):
        """初始化背压控制器
        
        Args:
            properties: 背压控制配置属性
        """
        self.properties = properties
        
        # 验证配置
        self.properties.validate()
        
        # 控制器状态
        self._system_state = SystemState.HEALTHY
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # 核心组件
        self._circuit_breaker = CircuitBreaker(properties)
        self._rate_limiter = RateLimiter(properties)
        
        # 指标收集
        self._request_metrics = RequestMetrics()
        self._system_metrics = SystemMetrics()
        self._response_times = deque(maxlen=self.properties.performance_window_size)
        self._recent_decisions = deque(maxlen=100)
        
        # 请求队列
        self._request_queue = deque(maxlen=self.properties.max_queue_size)
        
        # 线程安全
        self._lock = Lock()
        
        logger.debug(f"BackpressureController initialized with properties: {self.properties}")
    
    @PostConstruct
    async def initialize(self) -> None:
        """自动初始化背压控制器（框架调用）"""
        if not self.properties.enabled:
            logger.info("BackpressureController disabled by configuration")
            return
            
        logger.info(f"🚀 Initializing BackpressureController (rate_limit: {self.properties.max_requests_per_second}/s)")
        
        try:
            # 启动监控任务
            if self.properties.monitoring_enabled:
                self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info("✅ BackpressureController initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize BackpressureController: {e}")
            raise
    
    @PreDestroy
    async def cleanup(self) -> None:
        """自动清理背压控制器（框架调用）"""
        logger.info("🛑 Stopping BackpressureController")
        
        try:
            # 取消监控任务
            if self._monitoring_task:
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    logger.debug("BackpressureController monitoring loop cancelled successfully")
            
            logger.info("✅ BackpressureController stopped successfully")
            
        except Exception as e:
            logger.error(f"Error during BackpressureController cleanup: {e}")
    
    async def _monitoring_loop(self) -> None:
        """监控主循环"""
        logger.debug("BackpressureController monitoring loop started")
        
        while True:
            try:
                # 更新系统指标
                await self._update_system_metrics()
                
                # 更新请求指标
                await self._update_request_metrics()
                
                # 评估系统状态
                await self._evaluate_system_state()
                
                # 自动恢复检查
                if self.properties.auto_recovery_enabled:
                    await self._check_auto_recovery()
                
                # 等待下一次监控
                await asyncio.sleep(self.properties.metrics_collection_interval)
                
            except asyncio.CancelledError:
                logger.debug("BackpressureController monitoring loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(1.0)  # 错误时短暂等待
    
    async def _update_system_metrics(self) -> None:
        """更新系统指标"""
        try:
            # 简化的系统指标收集（避免阻塞）
            import psutil
            
            # 使用非阻塞调用
            cpu_usage = psutil.cpu_percent(interval=None)
            memory_info = psutil.virtual_memory()
            
            with self._lock:
                self._system_metrics.cpu_usage = cpu_usage
                self._system_metrics.memory_usage = memory_info.percent
                self._system_metrics.queue_size = len(self._request_queue)
                self._system_metrics.timestamp = time.time()
                
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    async def _update_request_metrics(self) -> None:
        """更新请求指标"""
        try:
            with self._lock:
                # 计算错误率
                total = self._request_metrics.total_requests
                if total > 0:
                    self._request_metrics.error_rate = (
                        self._request_metrics.failed_requests / total * 100
                    )
                
                # 计算平均响应时间
                if self._response_times:
                    self._request_metrics.avg_response_time = (
                        sum(self._response_times) / len(self._response_times)
                    )
                
                # 计算吞吐量
                current_time = time.time()
                time_diff = current_time - self._request_metrics.last_updated
                if time_diff > 0:
                    self._request_metrics.throughput = (
                        self._request_metrics.successful_requests / time_diff
                    )
                
                self._request_metrics.last_updated = current_time
                
        except Exception as e:
            logger.error(f"Error updating request metrics: {e}")
    
    async def _evaluate_system_state(self) -> None:
        """评估系统状态"""
        try:
            cpu_overload = self._system_metrics.cpu_usage > self.properties.cpu_threshold
            memory_overload = self._system_metrics.memory_usage > self.properties.memory_threshold
            response_slow = self._request_metrics.avg_response_time > self.properties.response_time_threshold
            error_high = self._request_metrics.error_rate > self.properties.error_rate_threshold
            
            # 确定系统状态
            if error_high or (cpu_overload and memory_overload):
                new_state = SystemState.CRITICAL
            elif cpu_overload or memory_overload or response_slow:
                new_state = SystemState.OVERLOADED
            elif self._request_metrics.error_rate > (self.properties.error_rate_threshold * 0.5):
                new_state = SystemState.DEGRADED
            else:
                new_state = SystemState.HEALTHY
            
            # 状态变化时记录日志
            if new_state != self._system_state:
                logger.info(f"System state changed: {self._system_state.value} -> {new_state.value}")
                self._system_state = new_state
                
        except Exception as e:
            logger.error(f"Error evaluating system state: {e}")
    
    async def _check_auto_recovery(self) -> None:
        """检查自动恢复"""
        try:
            if self._circuit_breaker.state == CircuitBreakerState.OPEN:
                # 检查是否可以尝试恢复
                if (time.time() - self._circuit_breaker.last_failure_time > 
                    self.properties.recovery_check_interval):
                    
                    # 执行健康检查
                    if await self._health_check():
                        logger.info("Health check passed, attempting circuit breaker recovery")
                        self._circuit_breaker.state = CircuitBreakerState.HALF_OPEN
                        self._circuit_breaker.success_count = 0
                        
        except Exception as e:
            logger.error(f"Error in auto recovery check: {e}")
    
    async def _health_check(self) -> bool:
        """执行健康检查"""
        try:
            # 简单的健康检查：检查系统负载是否正常
            cpu_ok = self._system_metrics.cpu_usage < (self.properties.cpu_threshold * 0.8)
            memory_ok = self._system_metrics.memory_usage < (self.properties.memory_threshold * 0.8)
            
            return cpu_ok and memory_ok
            
        except Exception as e:
            logger.error(f"Error in health check: {e}")
            return False
    
    # ==================== 公共接口 ====================
    
    async def should_allow_request(self, request_metadata: Optional[Dict[str, Any]] = None) -> ProtectionDecision:
        """判断是否应该允许请求
        
        Args:
            request_metadata: 请求元数据
            
        Returns:
            保护决策
        """
        try:
            # 检查熔断器
            if self.properties.circuit_breaker_enabled and not self._circuit_breaker.can_execute():
                return ProtectionDecision(
                    action=ProtectionAction.CIRCUIT_BREAK,
                    reason="Circuit breaker is open",
                    component="circuit_breaker",
                    severity="HIGH",
                    metadata={"state": self._circuit_breaker.state.value}
                )
            
            # 检查限流
            if not self._rate_limiter.can_proceed():
                with self._lock:
                    self._request_metrics.rejected_requests += 1
                
                return ProtectionDecision(
                    action=ProtectionAction.REJECT,
                    reason="Rate limit exceeded",
                    component="rate_limiter",
                    severity="MEDIUM",
                    metadata={"current_rate": self._rate_limiter.get_current_rate()}
                )
            
            # 检查系统负载
            if self._system_state == SystemState.CRITICAL:
                return ProtectionDecision(
                    action=ProtectionAction.REJECT,
                    reason="System in critical state",
                    component="load_monitor",
                    severity="CRITICAL",
                    metadata={"system_state": self._system_state.value}
                )
            elif self._system_state == SystemState.OVERLOADED:
                # 检查是否需要降级
                if self.properties.should_enable_degradation(
                    self._system_metrics.cpu_usage / 100.0,
                    self._request_metrics.avg_response_time
                ):
                    return ProtectionDecision(
                        action=ProtectionAction.DEGRADE,
                        reason="System overloaded, enabling degradation",
                        component="degradation_manager",
                        severity="MEDIUM",
                        metadata=self.properties.get_degradation_config()
                    )
                else:
                    return ProtectionDecision(
                        action=ProtectionAction.THROTTLE,
                        reason="System overloaded, throttling requests",
                        component="load_monitor",
                        severity="MEDIUM",
                        metadata={"system_state": self._system_state.value}
                    )
            
            # 允许请求
            return ProtectionDecision(
                action=ProtectionAction.NONE,
                reason="Request allowed",
                component="controller",
                severity="LOW",
                metadata={"system_state": self._system_state.value}
            )
            
        except Exception as e:
            logger.error(f"Error in should_allow_request: {e}")
            # 出错时采用保守策略
            return ProtectionDecision(
                action=ProtectionAction.REJECT,
                reason=f"Internal error: {e}",
                component="controller",
                severity="HIGH",
                metadata={}
            )
    
    async def record_request_result(
        self,
        success: bool,
        response_time: Optional[float] = None,
        error: Optional[Exception] = None
    ) -> None:
        """记录请求结果
        
        Args:
            success: 请求是否成功
            response_time: 响应时间(秒)
            error: 错误信息（如果有）
        """
        try:
            with self._lock:
                self._request_metrics.total_requests += 1
                
                if success:
                    self._request_metrics.successful_requests += 1
                    self._circuit_breaker.record_success()
                else:
                    self._request_metrics.failed_requests += 1
                    self._circuit_breaker.record_failure()
                
                # 记录响应时间
                if response_time is not None:
                    self._response_times.append(response_time)
            
            # 记录决策（用于分析）
            decision = ProtectionDecision(
                action=ProtectionAction.NONE,
                reason="Request completed",
                component="controller",
                severity="LOW",
                metadata={
                    "success": success,
                    "response_time": response_time,
                    "error": str(error) if error else None
                }
            )
            
            with self._lock:
                self._recent_decisions.append(decision)
                
        except Exception as e:
            logger.error(f"Error recording request result: {e}")
    
    def get_system_metrics(self) -> SystemMetrics:
        """获取系统指标
        
        Returns:
            系统指标
        """
        with self._lock:
            return SystemMetrics(
                cpu_usage=self._system_metrics.cpu_usage,
                memory_usage=self._system_metrics.memory_usage,
                active_connections=self._system_metrics.active_connections,
                queue_size=self._system_metrics.queue_size,
                load_average=self._system_metrics.load_average,
                timestamp=self._system_metrics.timestamp
            )
    
    def get_request_metrics(self) -> RequestMetrics:
        """获取请求指标
        
        Returns:
            请求指标
        """
        with self._lock:
            return RequestMetrics(
                total_requests=self._request_metrics.total_requests,
                successful_requests=self._request_metrics.successful_requests,
                failed_requests=self._request_metrics.failed_requests,
                rejected_requests=self._request_metrics.rejected_requests,
                avg_response_time=self._request_metrics.avg_response_time,
                error_rate=self._request_metrics.error_rate,
                throughput=self._request_metrics.throughput,
                last_updated=self._request_metrics.last_updated
            )
    
    def get_circuit_breaker_state(self) -> CircuitBreakerState:
        """获取熔断器状态
        
        Returns:
            熔断器状态
        """
        return self._circuit_breaker.state
    
    def get_system_state(self) -> SystemState:
        """获取系统状态
        
        Returns:
            系统状态
        """
        return self._system_state
    
    def get_recent_decisions(self, limit: int = 10) -> List[ProtectionDecision]:
        """获取最近的保护决策
        
        Args:
            limit: 返回的决策数量限制
            
        Returns:
            最近的保护决策列表
        """
        with self._lock:
            return list(self._recent_decisions)[-limit:]
    
    async def force_circuit_breaker_open(self) -> None:
        """强制打开熔断器"""
        self._circuit_breaker.state = CircuitBreakerState.OPEN
        self._circuit_breaker.last_failure_time = time.time()
        logger.warning("Circuit breaker forced to OPEN state")
    
    async def force_circuit_breaker_close(self) -> None:
        """强制关闭熔断器"""
        self._circuit_breaker.state = CircuitBreakerState.CLOSED
        self._circuit_breaker.failure_count = 0
        logger.info("Circuit breaker forced to CLOSED state")
    
    async def adjust_rate_limit(self, new_rate: int) -> None:
        """动态调整限流值
        
        Args:
            new_rate: 新的限流值
        """
        if new_rate < self.properties.min_rate_limit:
            new_rate = self.properties.min_rate_limit
        elif new_rate > self.properties.max_rate_limit:
            new_rate = self.properties.max_rate_limit
            
        self.properties.max_requests_per_second = new_rate
        logger.info(f"Rate limit adjusted to {new_rate} requests/second")