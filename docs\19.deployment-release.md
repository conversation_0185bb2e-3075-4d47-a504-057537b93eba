# Mini-Boot 部署发布指南

## 1. 概述

本文档指导开发者如何将开发完成的Mini-Boot框架发布到私有PyPI仓库。通过本指南，开发者可以完成版本管理、构建打包、质量检查和发布部署的完整流程。

## 2. 发布前准备

### 2.1 环境检查

```bash
# 确保开发环境正常
uv --version
python --version

# 确保在项目根目录
pwd  # 应该显示 .../mini-boot

# 确保虚拟环境激活
uv sync
```

### 2.2 代码质量检查

```bash
# 运行完整测试套件
uv run pytest

# 检查代码覆盖率
uv run pytest --cov=miniboot --cov-report=term-missing

# 代码格式检查
uv run ruff check

# 自动格式化
uv run ruff format

# 类型检查
uv run mypy miniboot
```

### 2.3 版本管理

#### 更新版本号

编辑 `miniboot/__init__.py`:
```python
"""Mini-Boot框架"""

__version__ = "1.0.1"  # 更新版本号
__author__ = "Mini-Boot Team"
__email__ = "<EMAIL>"

from .context import DefaultApplicationContext
from .annotations import Component, Service, Repository, Controller

__all__ = [
    "DefaultApplicationContext",
    "Component", 
    "Service", 
    "Repository", 
    "Controller"
]
```

编辑 `pyproject.toml`:
```toml
[project]
name = "miniboot"
version = "1.0.1"  # 更新版本号
# ... 其他配置
```

## 3. 私有仓库配置

### 3.1 局域网私有PyPI仓库

#### 搭建简单私库

```bash
# 使用pypiserver搭建
pip install pypiserver
mkdir packages
pypi-server -p 8080 ./packages/ --host 0.0.0.0

# 使用devpi搭建（功能更完整）
pip install devpi-server devpi-web
devpi-server --start --host=0.0.0.0 --port=8080
```

#### 配置环境变量

```bash
# 设置私有仓库URL（局域网HTTP协议）
export UV_PUBLISH_URL=http://*************:8080/simple/
export UV_INDEX_URL=http://*************:8080/simple/
export UV_EXTRA_INDEX_URL=https://pypi.org/simple/

# 设置认证token（如果需要）
export UV_PUBLISH_TOKEN=your_private_pypi_token
```

### 3.2 配置文件方式

#### pyproject.toml配置

```toml
[[tool.uv.index]]
name = "private"
url = "http://*************:8080/simple/"
default = true

[[tool.uv.index]]
name = "public"
url = "https://pypi.org/simple/"
```

#### pip配置文件

创建 `~/.pip/pip.conf` (Linux/Mac) 或 `%APPDATA%\pip\pip.ini` (Windows):
```ini
[global]
index-url = http://*************:8080/simple/
extra-index-url = https://pypi.org/simple/
trusted-host = *************
```

## 4. 构建和发布

### 4.1 手动发布流程

```bash
# 1. 最终测试
uv run pytest
uv run ruff check
uv run mypy miniboot

# 2. 构建包
uv build

# 3. 检查构建产物
ls -la dist/
# 应该看到 .whl 和 .tar.gz 文件

# 4. 发布到私有仓库
uv publish --publish-url http://*************:8080/

# 5. 创建Git标签
git add .
git commit -m "Release version 1.0.1"
git tag v1.0.1
git push origin main
git push origin v1.0.1
```

### 4.2 自动化发布脚本

创建 `scripts/release.sh`:
```bash
#!/bin/bash
# scripts/release.sh

set -e

VERSION=$1
REPOSITORY=${2:-"private"}  # 默认发布到私有仓库

if [ -z "$VERSION" ]; then
    echo "Usage: $0 <version> [repository]"
    echo "repository: private (default) | public | test"
    exit 1
fi

echo "发布版本: $VERSION 到 $REPOSITORY 仓库"

# 更新版本号
sed -i "s/version = \".*\"/version = \"$VERSION\"/" pyproject.toml
sed -i "s/__version__ = \".*\"/__version__ = \"$VERSION\"/" miniboot/__init__.py

# 运行测试
echo "运行测试..."
uv run pytest

# 代码检查
echo "代码检查..."
uv run ruff check
uv run ruff format --check

# 类型检查
echo "类型检查..."
uv run mypy miniboot

# 构建包
echo "构建包..."
uv build

# 根据仓库类型发布
echo "发布到 $REPOSITORY 仓库..."
case $REPOSITORY in
    "private")
        # 发布到私有仓库
        uv publish --publish-url ${PRIVATE_PYPI_URL:-"http://*************:8080/simple/"}
        ;;
    "public")
        # 发布到公共PyPI
        uv publish --publish-url https://upload.pypi.org/legacy/
        ;;
    "test")
        # 发布到Test PyPI
        uv publish --publish-url https://test.pypi.org/legacy/
        ;;
    *)
        echo "未知仓库类型: $REPOSITORY"
        exit 1
        ;;
esac

# Git标签
git add .
git commit -m "Release version $VERSION"
git tag "v$VERSION"
git push origin main
git push origin "v$VERSION"

echo "版本 $VERSION 发布到 $REPOSITORY 仓库成功！"
```

### 4.3 使用发布脚本

```bash
# 给脚本执行权限
chmod +x scripts/release.sh

# 发布到私有仓库（默认）
./scripts/release.sh 1.0.1

# 发布到私有仓库（显式指定）
./scripts/release.sh 1.0.1 private

# 发布到公共PyPI
./scripts/release.sh 1.0.1 public

# 设置自定义私有仓库URL
export PRIVATE_PYPI_URL=http://*************:8080/simple/
./scripts/release.sh 1.0.1 private
```

## 5. 验证发布

### 5.1 检查私有仓库

```bash
# 访问私有仓库Web界面
curl http://*************:8080/simple/miniboot/

# 或在浏览器中访问
# http://*************:8080/
```

### 5.2 测试安装

```bash
# 在新环境中测试安装
mkdir test-install
cd test-install

# 从私有仓库安装
uv init test-project
cd test-project
uv add miniboot --index-url http://*************:8080/simple/

# 测试导入
uv run python -c "import miniboot; print(miniboot.__version__)"
```

## 6. 用户安装指南

### 6.1 从私有仓库安装

```bash
# 使用uv安装
uv add miniboot --index-url http://*************:8080/simple/

# 安装Web功能
uv add "miniboot[web]" --index-url http://*************:8080/simple/

# 安装所有功能
uv add "miniboot[all]" --index-url http://*************:8080/simple/

# 使用pip安装
pip install miniboot -i http://*************:8080/simple/
pip install "miniboot[all]" -i http://*************:8080/simple/
```

### 6.2 配置用户环境

用户可以配置环境变量以简化安装：
```bash
# 配置默认私有仓库
export UV_INDEX_URL=http://*************:8080/simple/
export UV_EXTRA_INDEX_URL=https://pypi.org/simple/

# 之后可以直接安装
uv add miniboot
uv add "miniboot[web]"
```

## 7. 版本管理策略

### 7.1 语义化版本控制

- **主版本号**: 不兼容的API修改 (1.0.0 -> 2.0.0)
- **次版本号**: 向后兼容的功能性新增 (1.0.0 -> 1.1.0)
- **修订号**: 向后兼容的问题修正 (1.0.0 -> 1.0.1)

### 7.2 发布周期

- **主版本**: 每年1-2次
- **次版本**: 每季度1次
- **修订版本**: 按需发布

### 7.3 分支策略

- **main**: 稳定版本分支
- **develop**: 开发版本分支
- **feature/***: 功能开发分支
- **hotfix/***: 热修复分支

## 8. 常见问题

### 8.1 发布失败

```bash
# 检查网络连接
ping *************

# 检查私有仓库状态
curl http://*************:8080/

# 清理构建缓存
rm -rf dist/ build/
uv build
```

### 8.2 版本冲突

```bash
# 检查已发布版本
curl http://*************:8080/simple/miniboot/

# 更新版本号后重新发布
./scripts/release.sh 1.0.2
```

### 8.3 权限问题

```bash
# 检查token配置
echo $UV_PUBLISH_TOKEN

# 重新设置token
export UV_PUBLISH_TOKEN=new_token
```

---

*本文档指导开发者将Mini-Boot框架发布到私有PyPI仓库。*
