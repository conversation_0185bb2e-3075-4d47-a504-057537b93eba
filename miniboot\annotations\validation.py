#!/usr/bin/env python
"""
* @author: cz
* @description: 配置验证注解实现

实现配置属性验证注解,用于验证配置值的有效性.
支持常见的验证规则,如非空、范围、格式等.
"""

import re
from typing import Any, Callable, Union


def NotNull(  # noqa: N802
    message: str = "Value cannot be null",
) -> Callable[[Any], Any]:
    """非空验证注解装饰器

    验证配置值不能为None或空字符串.

    Args:
        message: 验证失败时的错误消息

    Returns:
        装饰器函数

    Examples:
        @ConfigurationProperties(prefix="database")
        class DatabaseConfig:
            @NotNull("Database host cannot be null")
            host: str = None
    """

    def decorator(target: Any) -> Any:
        target.__validation_not_null__ = True
        target.__validation_not_null_message__ = message
        return target

    return decorator


def NotEmpty(  # noqa: N802
    message: str = "Value cannot be empty",
) -> Callable[[Any], Any]:
    """非空验证注解装饰器

    验证配置值不能为None、空字符串或空集合.

    Args:
        message: 验证失败时的错误消息

    Returns:
        装饰器函数

    Examples:
        @ConfigurationProperties(prefix="app")
        class AppConfig:
            @NotEmpty("Application name cannot be empty")
            name: str = ""
    """

    def decorator(target: Any) -> Any:
        target.__validation_not_empty__ = True
        target.__validation_not_empty_message__ = message
        return target

    return decorator


def Min(  # noqa: N802
    value: Union[int, float], message: str = None
) -> Callable[[Any], Any]:
    """最小值验证注解装饰器

    验证数值配置不能小于指定值.

    Args:
        value: 最小值
        message: 验证失败时的错误消息

    Returns:
        装饰器函数

    Examples:
        @ConfigurationProperties(prefix="server")
        class ServerConfig:
            @Min(1, "Port must be greater than 0")
            port: int = 8080
    """

    def decorator(target: Any) -> Any:
        target.__validation_min__ = value
        target.__validation_min_message__ = message or f"Value must be at least {value}"
        return target

    return decorator


def Max(  # noqa: N802
    value: Union[int, float], message: str = None
) -> Callable[[Any], Any]:
    """最大值验证注解装饰器

    验证数值配置不能大于指定值.

    Args:
        value: 最大值
        message: 验证失败时的错误消息

    Returns:
        装饰器函数

    Examples:
        @ConfigurationProperties(prefix="server")
        class ServerConfig:
            @Max(65535, "Port must be less than 65536")
            port: int = 8080
    """

    def decorator(target: Any) -> Any:
        target.__validation_max__ = value
        target.__validation_max_message__ = message or f"Value must be at most {value}"
        return target

    return decorator


def Size(  # noqa: N802
    min_size: int = 0, max_size: int = None, message: str = None
) -> Callable[[Any], Any]:
    """大小验证注解装饰器

    验证字符串或集合的大小在指定范围内.

    Args:
        min_size: 最小大小
        max_size: 最大大小
        message: 验证失败时的错误消息

    Returns:
        装饰器函数

    Examples:
        @ConfigurationProperties(prefix="user")
        class UserConfig:
            @Size(min_size=3, max_size=20, "Username length must be between 3 and 20")
            username: str = ""
    """

    def decorator(target: Any) -> Any:
        target.__validation_size_min__ = min_size
        target.__validation_size_max__ = max_size
        target.__validation_size_message__ = message or f"Size must be between {min_size} and {max_size or 'unlimited'}"
        return target

    return decorator


def Pattern(  # noqa: N802
    regex: str, message: str = None
) -> Callable[[Any], Any]:
    r"""正则表达式验证注解装饰器

    验证字符串配置匹配指定的正则表达式.

    Args:
        regex: 正则表达式模式
        message: 验证失败时的错误消息

    Returns:
        装饰器函数

    Examples:
        @ConfigurationProperties(prefix="user")
        class UserConfig:
            @Pattern(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', "Invalid email format")
            email: str = ""
    """

    def decorator(target: Any) -> Any:
        target.__validation_pattern__ = regex
        target.__validation_pattern_message__ = message or f"Value must match pattern: {regex}"
        return target

    return decorator


def Valid(  # noqa: N802
    message: str = "Nested object validation failed",
) -> Callable[[Any], Any]:
    """嵌套对象验证注解装饰器

    标记嵌套对象需要进行验证.

    Args:
        message: 验证失败时的错误消息

    Returns:
        装饰器函数

    Examples:
        @ConfigurationProperties(prefix="app")
        class AppConfig:
            @Valid("Database configuration is invalid")
            database: DatabaseConfig = None
    """

    def decorator(target: Any) -> Any:
        target.__validation_valid__ = True
        target.__validation_valid_message__ = message
        return target

    return decorator


class ValidationError(Exception):
    """配置验证错误"""

    def __init__(self, field_name: str, message: str, value: Any = None):
        self.field_name = field_name
        self.message = message
        self.value = value
        super().__init__(f"Validation failed for field '{field_name}': {message}")


class ConfigurationValidator:
    """配置验证器

    负责验证配置属性的有效性,支持各种验证规则.
    """

    def validate_field(self, field_name: str, field_value: Any, field_type: type) -> None:
        """验证单个字段

        Args:
            field_name: 字段名称
            field_value: 字段值
            field_type: 字段类型

        Raises:
            ValidationError: 验证失败时抛出
        """
        # 获取字段的验证注解
        annotations = getattr(field_type, "__annotations__", {})
        field_info = annotations.get(field_name)

        if field_info is None:
            return

        # 执行各种验证
        self._validate_not_null(field_name, field_value, field_info)
        self._validate_not_empty(field_name, field_value, field_info)
        self._validate_min_max(field_name, field_value, field_info)
        self._validate_size(field_name, field_value, field_info)
        self._validate_pattern(field_name, field_value, field_info)
        self._validate_nested(field_name, field_value, field_info)

    def _validate_not_null(self, field_name: str, value: Any, field_info: Any) -> None:
        """验证非空"""
        if hasattr(field_info, "__validation_not_null__") and field_info.__validation_not_null__ and value is None:
            message = getattr(field_info, "__validation_not_null_message__", "Value cannot be null")
            raise ValidationError(field_name, message, value)

    def _validate_not_empty(self, field_name: str, value: Any, field_info: Any) -> None:
        """验证非空"""
        if (
            hasattr(field_info, "__validation_not_empty__")
            and field_info.__validation_not_empty__
            and (value is None or (hasattr(value, "__len__") and len(value) == 0))
        ):
            message = getattr(field_info, "__validation_not_empty_message__", "Value cannot be empty")
            raise ValidationError(field_name, message, value)

    def _validate_min_max(self, field_name: str, value: Any, field_info: Any) -> None:
        """验证最小最大值"""
        if value is None:
            return

        if hasattr(field_info, "__validation_min__"):
            min_val = field_info.__validation_min__
            if isinstance(value, (int, float)) and value < min_val:
                message = getattr(field_info, "__validation_min_message__", f"Value must be at least {min_val}")
                raise ValidationError(field_name, message, value)

        if hasattr(field_info, "__validation_max__"):
            max_val = field_info.__validation_max__
            if isinstance(value, (int, float)) and value > max_val:
                message = getattr(field_info, "__validation_max_message__", f"Value must be at most {max_val}")
                raise ValidationError(field_name, message, value)

    def _validate_size(self, field_name: str, value: Any, field_info: Any) -> None:
        """验证大小"""
        if value is None or not hasattr(value, "__len__"):
            return

        if hasattr(field_info, "__validation_size_min__"):
            min_size = field_info.__validation_size_min__
            max_size = getattr(field_info, "__validation_size_max__", None)

            size = len(value)
            if size < min_size or (max_size is not None and size > max_size):
                message = getattr(field_info, "__validation_size_message__", f"Size must be between {min_size} and {max_size or 'unlimited'}")
                raise ValidationError(field_name, message, value)

    def _validate_pattern(self, field_name: str, value: Any, field_info: Any) -> None:
        """验证正则表达式"""
        if value is None or not isinstance(value, str):
            return

        if hasattr(field_info, "__validation_pattern__"):
            pattern = field_info.__validation_pattern__
            if not re.match(pattern, value):
                message = getattr(field_info, "__validation_pattern_message__", f"Value must match pattern: {pattern}")
                raise ValidationError(field_name, message, value)

    def _validate_nested(self, _field_name: str, value: Any, field_info: Any) -> None:
        """验证嵌套对象"""
        if value is None:
            return

        if hasattr(field_info, "__validation_valid__") and field_info.__validation_valid__ and hasattr(value, "__class__"):
            # 递归验证嵌套对象
            self.validate_object(value)

    def validate_object(self, obj: Any) -> None:
        """验证整个对象

        Args:
            obj: 要验证的对象

        Raises:
            ValidationError: 验证失败时抛出
        """
        if obj is None:
            return

        # 获取对象的字段信息
        obj_class = obj.__class__
        annotations = getattr(obj_class, "__annotations__", {})

        for field_name, field_type in annotations.items():
            field_value = getattr(obj, field_name, None)
            self.validate_field(field_name, field_value, field_type)
